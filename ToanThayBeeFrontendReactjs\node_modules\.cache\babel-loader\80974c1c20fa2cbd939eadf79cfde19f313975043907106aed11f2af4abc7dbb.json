{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\table\\\\TableAdmin.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TheadAdmin = _ref => {\n  let {\n    children,\n    className = \"bg-[#F6FAFD] sticky top-0 z-10\"\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"thead\", {\n    className: className,\n    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n      className: \"border border-[#E7E7ED]\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = TheadAdmin;\nexport const TableAdmin = _ref2 => {\n  let {\n    children,\n    className = \"w-full border-collapse border border-[#E7E7ED] text-sm\"\n  } = _ref2;\n  return /*#__PURE__*/_jsxDEV(\"table\", {\n    className: className,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 9\n  }, this);\n};\n_c2 = TableAdmin;\nexport const ThAdmin = _ref3 => {\n  let {\n    children,\n    className = \"p-3 text-center\"\n  } = _ref3;\n  return /*#__PURE__*/_jsxDEV(\"th\", {\n    className: className,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 9\n  }, this);\n};\n_c3 = ThAdmin;\nexport const TdAdmin = _ref4 => {\n  let {\n    children,\n    className = \"p-3 text-center\"\n  } = _ref4;\n  return /*#__PURE__*/_jsxDEV(\"td\", {\n    className: className,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 9\n  }, this);\n};\n_c4 = TdAdmin;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"TheadAdmin\");\n$RefreshReg$(_c2, \"TableAdmin\");\n$RefreshReg$(_c3, \"ThAdmin\");\n$RefreshReg$(_c4, \"TdAdmin\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "className", "_jsxDEV", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "TableAdmin", "_ref2", "_c2", "<PERSON>h<PERSON><PERSON><PERSON>", "_ref3", "_c3", "TdAdmin", "_ref4", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/table/TableAdmin.jsx"], "sourcesContent": ["export const TheadAdmin = ({ children, className = \"bg-[#F6FAFD] sticky top-0 z-10\" }) => {\r\n    return (\r\n        <thead className={className}>\r\n            <tr className=\"border border-[#E7E7ED]\">\r\n                {children}\r\n            </tr>\r\n        </thead>\r\n    )\r\n}\r\n\r\n\r\nexport const TableAdmin = ({ children, className = \"w-full border-collapse border border-[#E7E7ED] text-sm\" }) => {\r\n    return (\r\n        <table className={className}>\r\n            {children}\r\n        </table>\r\n    )\r\n}\r\n\r\nexport const ThAdmin = ({ children, className = \"p-3 text-center\" }) => {\r\n    return (\r\n        <th className={className}>\r\n            {children}\r\n        </th>\r\n    )\r\n}\r\n\r\nexport const TdAdmin = ({ children, className = \"p-3 text-center\" }) => {\r\n    return (\r\n        <td className={className}>\r\n            {children}\r\n        </td>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAO,MAAMA,UAAU,GAAGC,IAAA,IAAgE;EAAA,IAA/D;IAAEC,QAAQ;IAAEC,SAAS,GAAG;EAAiC,CAAC,GAAAF,IAAA;EACjF,oBACIG,OAAA;IAAOD,SAAS,EAAEA,SAAU;IAAAD,QAAA,eACxBE,OAAA;MAAID,SAAS,EAAC,yBAAyB;MAAAD,QAAA,EAClCA;IAAQ;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB,CAAC;AAAAC,EAAA,GARYT,UAAU;AAWvB,OAAO,MAAMU,UAAU,GAAGC,KAAA,IAAwF;EAAA,IAAvF;IAAET,QAAQ;IAAEC,SAAS,GAAG;EAAyD,CAAC,GAAAQ,KAAA;EACzG,oBACIP,OAAA;IAAOD,SAAS,EAAEA,SAAU;IAAAD,QAAA,EACvBA;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEhB,CAAC;AAAAI,GAAA,GANYF,UAAU;AAQvB,OAAO,MAAMG,OAAO,GAAGC,KAAA,IAAiD;EAAA,IAAhD;IAAEZ,QAAQ;IAAEC,SAAS,GAAG;EAAkB,CAAC,GAAAW,KAAA;EAC/D,oBACIV,OAAA;IAAID,SAAS,EAAEA,SAAU;IAAAD,QAAA,EACpBA;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEb,CAAC;AAAAO,GAAA,GANYF,OAAO;AAQpB,OAAO,MAAMG,OAAO,GAAGC,KAAA,IAAiD;EAAA,IAAhD;IAAEf,QAAQ;IAAEC,SAAS,GAAG;EAAkB,CAAC,GAAAc,KAAA;EAC/D,oBACIb,OAAA;IAAID,SAAS,EAAEA,SAAU;IAAAD,QAAA,EACpBA;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEb,CAAC;AAAAU,GAAA,GANYF,OAAO;AAAA,IAAAP,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}