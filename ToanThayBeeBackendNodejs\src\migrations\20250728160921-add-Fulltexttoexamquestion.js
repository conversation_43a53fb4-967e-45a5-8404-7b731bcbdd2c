'use strict';

export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.addIndex('question', ['content'], {
      type: 'FULLTEXT',
      name: 'question_content_fulltext'
    });
    await queryInterface.addIndex('exam', ['name'], {
      type: 'FULLTEXT',
      name: 'exam_name_fulltext'
    });

  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('question', 'question_content_fulltext');
    await queryInterface.removeIndex('exam', 'exam_name_fulltext');
  },
};
