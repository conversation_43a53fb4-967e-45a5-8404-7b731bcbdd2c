import React, { useState } from 'react';
import { X } from 'lucide-react';
import LessonSearchInput from './LessonSearchInput';

/**
 * Component for selecting multiple lessons
 * 
 * @param {Object} props
 * @param {Array} props.selectedLessons - Array of selected class objects
 * @param {function} props.onChange - Callback when selected lessons change
 * @param {string} props.className - Additional CSS lessons
 */
const MultiLessonSelector = ({
    selectedLessons = [],
    onChange,
    className = ''
}) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedLessonId, setSelectedLessonId] = useState('');

    // Handle lesson selection
    const handleSelectLesson = (lessonItem) => {
        // Check if lesson is already selected
        if (!selectedLessons.some(l => l.id === lessonItem.id)) {
            const updatedLessons = [...selectedLessons, lessonItem];
            onChange(updatedLessons);
        }
        // console.log('Selected lesson:', lessonItem);
        setSearchTerm('');
        setSelectedLessonId('');
    };

    // Handle removing a lesson
    const handleRemoveLesson = (lessonId) => {
        const updatedLessons = selectedLessons.filter(l => l.id !== lessonId);
        onChange(updatedLessons);
    };

    // Handle clearing the search
    const handleClearSearch = () => {
        setSearchTerm('');
        setSelectedLessonId('');
    };

    return (
        <div className={`${className}`}>
            <LessonSearchInput
                value={searchTerm}
                selectedLessonId={selectedLessonId}
                onChange={setSearchTerm}
                onSelect={handleSelectLesson}
                onClear={handleClearSearch}
                placeholder="Tìm kiếm buổi học..."
                className="mb-2"
            />

            {selectedLessons.length > 0 && (
                <div className="mt-2">
                    <div className="text-sm font-medium text-gray-700 mb-1">Các buổi đã chọn:</div>
                    <div className="flex flex-wrap gap-2">
                        {selectedLessons.map((lessonItem) => (
                            <div
                                key={lessonItem.id}
                                className="flex items-center bg-sky-100 text-sky-800 px-2 py-1 rounded-md text-sm"
                            >
                                <span>{lessonItem.name} - {lessonItem.class.name}</span>
                                <button
                                    onClick={() => handleRemoveLesson(lessonItem.id)}
                                    className="ml-1 text-sky-600 hover:text-sky-800"
                                >
                                    <X size={14} />
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default MultiLessonSelector;
