{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\QuestionView.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { FileText } from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { reorderStatements, reorderQuestions } from \"src/features/questionsExam/questionsExamSlice\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport SortableQuestionItem from \"../PageAIexam/SortableQuestionItem\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuestionContent = _ref => {\n  _s();\n  var _question$ExamQuestio, _codes$chapter, _codes$chapter$find;\n  let {\n    question,\n    index\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\\n            \".concat(selectedIndex === ((_question$ExamQuestio = question.ExamQuestions) === null || _question$ExamQuestio === void 0 ? void 0 : _question$ExamQuestio.order) ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\"),\n    onClick: () => {\n      var _question$ExamQuestio2;\n      return dispatch(setSelectedIndex((_question$ExamQuestio2 = question.ExamQuestions) === null || _question$ExamQuestio2 === void 0 ? void 0 : _question$ExamQuestio2.order));\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      div: true,\n      className: \"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-gray-700\",\n        children: [\"C\\xE2u \", index + 1]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\u0110\\u1ED9 kh\\xF3: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: question.difficulty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Ch\\u01B0\\u01A1ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: ((_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === question.chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || \"Chưa xác định\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-base text-gray-800 leading-relaxed\",\n        children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          className: \"text-gray-800\",\n          text: question.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 17\n      }, this), question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center w-full h-[10rem] mt-1\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: question.imageUrl,\n          alt: \"question\",\n          className: \"object-contain w-full h-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"text-xs font-bold\",\n        children: \"L\\u1EDDi gi\\u1EA3i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: question.solution,\n        className: \" w-full\",\n        style: {\n          fontSize: '12px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 17\n    }, this)]\n  }, question.id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionContent, \"XZZ2Q+l9zMAy4Vp/xsF8U7mo49M=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = QuestionContent;\nconst QuestionViewHeader = _ref2 => {\n  let {\n    title,\n    count,\n    noQuestionText\n  } = _ref2;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" p-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-1 mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-3 h-3 text-gray-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-xs font-semibold text-gray-900\",\n        children: [title, \" (\", count, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this), count === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-4 text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-6 h-6 mx-auto mb-1 opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs\",\n        children: noQuestionText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 9\n  }, this);\n};\n_c2 = QuestionViewHeader;\nconst QuestionTNView = () => {\n  _s2();\n  const {\n    questionsExam,\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const [questionsTN, setQuestionsTN] = useState([]);\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const dispatch = useDispatch();\n  useEffect(() => {\n    setQuestionsTN(questionsExam.filter(q => q.typeOfQuestion === 'TN'));\n  }, [questionsExam]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\",\n      count: questionsTN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: questionsTN.map((question, index) => /*#__PURE__*/_jsxDEV(QuestionContent, {\n        question: question,\n        index: index\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s2(QuestionTNView, \"1+RuVH3kC0NCu1cnn0vlYg/Ibwk=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c3 = QuestionTNView;\nconst QuestionDSView = () => {\n  _s3();\n  const {\n    questionsExam,\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const [questionsDS, setQuestionsDS] = useState([]);\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const dispatch = useDispatch();\n  useEffect(() => {\n    setQuestionsDS(questionsExam.filter(q => q.typeOfQuestion === 'DS'));\n  }, [questionsExam]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi \\u0111\\xFAng sai\",\n      count: questionsDS.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi \\u0111\\xFAng sai\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: questionsDS.map((question, index) => {\n        var _question$ExamQuestio3;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-1 cursor-pointer \".concat(selectedIndex === ((_question$ExamQuestio3 = question.ExamQuestions) === null || _question$ExamQuestio3 === void 0 ? void 0 : _question$ExamQuestio3.order) ? 'border border-blue-500 rounded-lg bg-blue-50' : '', \" \"),\n          onClick: () => {\n            var _question$ExamQuestio4;\n            return dispatch(setSelectedIndex((_question$ExamQuestio4 = question.ExamQuestions) === null || _question$ExamQuestio4 === void 0 ? void 0 : _question$ExamQuestio4.order));\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"text-xs font-bold\",\n            children: [\"C\\xE2u h\\u1ECFi \", index + 1, /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-500 font-normal\",\n              children: [\" (\", question.class || \"Chưa chọn\", \" - \", question.chapter || \"Chưa chọn\", \" - \", question.difficulty || \"Chưa chọn\", \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n            text: question.content,\n            className: \"text-xs break-words w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col gap-2 mt-2\",\n            children: question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs font-bold whitespace-nowrap\",\n                children: prefixDS[index]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                text: statement.content,\n                className: \"text-xs break-words w-full \".concat(statement.isCorrect ? 'text-green-500' : 'text-red-500')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 37\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-xs font-bold\",\n              children: \"L\\u1EDDi gi\\u1EA3i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n              content: question.solution,\n              className: \" w-full\",\n              style: {\n                fontSize: '0.75rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 29\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 21\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s3(QuestionDSView, \"ZDc0MkYy/C9SINXWupXUjKn7BIA=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c4 = QuestionDSView;\nconst QuestionTLNView = () => {\n  _s4();\n  const {\n    questionsExam,\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  const [questionsTLN, setQuestionsTLN] = useState([]);\n  useEffect(() => {\n    setQuestionsTLN(questionsExam.filter(q => q.typeOfQuestion === 'TLN'));\n  }, [questionsExam]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\",\n      count: questionsTLN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: questionsTLN.map((question, index) => {\n        var _question$ExamQuestio5;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-1 cursor-pointer \".concat(selectedIndex === ((_question$ExamQuestio5 = question.ExamQuestions) === null || _question$ExamQuestio5 === void 0 ? void 0 : _question$ExamQuestio5.order) ? 'border border-blue-500 rounded-lg bg-blue-50' : '', \" \"),\n          onClick: () => {\n            var _question$ExamQuestio6;\n            return dispatch(setSelectedIndex((_question$ExamQuestio6 = question.ExamQuestions) === null || _question$ExamQuestio6 === void 0 ? void 0 : _question$ExamQuestio6.order));\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"text-xs font-bold\",\n            children: [\"C\\xE2u h\\u1ECFi \", index + 1, /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-500 font-normal\",\n              children: [\" (\", question.class || \"Chưa chọn\", \" - \", question.chapter || \"Chưa chọn\", \" - \", question.difficulty || \"Chưa chọn\", \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n            text: question.content,\n            className: \"text-xs break-words w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs font-bold mt-2\",\n            children: \"\\u0110\\xE1p \\xE1n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n            text: question.correctAnswer,\n            className: \"text-xs break-words w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 25\n          }, this), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-xs font-bold\",\n              children: \"L\\u1EDDi gi\\u1EA3i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n              content: question.solution,\n              className: \" w-full\",\n              style: {\n                fontSize: '0.75rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 29\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 21\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s4(QuestionTLNView, \"lMktx9ypr+KT1Lv7d9GhrS8Xzgo=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c5 = QuestionTLNView;\nconst QuestionView = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionTNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"border-gray-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(QuestionDSView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"border-gray-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(QuestionTLNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_c6 = QuestionView;\nexport default QuestionView;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"QuestionContent\");\n$RefreshReg$(_c2, \"QuestionViewHeader\");\n$RefreshReg$(_c3, \"QuestionTNView\");\n$RefreshReg$(_c4, \"QuestionDSView\");\n$RefreshReg$(_c5, \"QuestionTLNView\");\n$RefreshReg$(_c6, \"QuestionView\");", "map": {"version": 3, "names": ["FileText", "useEffect", "useState", "useSelector", "useDispatch", "setSelectedIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "reorderStatements", "reorderQuestions", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "SortableQuestionItem", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuestionContent", "_ref", "_s", "_question$ExamQuestio", "_codes$chapter", "_codes$chapter$find", "question", "index", "dispatch", "selectedIndex", "state", "questionsExam", "codes", "className", "concat", "ExamQuestions", "order", "onClick", "_question$ExamQuestio2", "children", "div", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "difficulty", "find", "c", "code", "chapter", "description", "text", "content", "imageUrl", "src", "alt", "solution", "style", "fontSize", "id", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "title", "count", "noQuestionText", "_c2", "QuestionTNView", "_s2", "questionsTN", "setQuestionsTN", "prefixTN", "filter", "q", "typeOfQuestion", "length", "map", "_c3", "QuestionDSView", "_s3", "questionsDS", "setQuestionsDS", "prefixDS", "_question$ExamQuestio3", "_question$ExamQuestio4", "class", "statements", "statement", "isCorrect", "_c4", "QuestionTLNView", "_s4", "questionsTLN", "setQuestionsTLN", "_question$ExamQuestio5", "_question$ExamQuestio6", "<PERSON><PERSON><PERSON><PERSON>", "_c5", "Question<PERSON>iew", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/QuestionView.jsx"], "sourcesContent": ["import { FileText } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\nimport { reorderStatements, reorderQuestions } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport {\r\n    DndContext,\r\n    closestCenter,\r\n    KeyboardSensor,\r\n    PointerSensor,\r\n    useSensor,\r\n    useSensors,\r\n} from '@dnd-kit/core';\r\nimport {\r\n    SortableContext,\r\n    sortableKeyboardCoordinates,\r\n    verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport SortableQuestionItem from \"../PageAIexam/SortableQuestionItem\";\r\n\r\nconst QuestionContent = ({ question, index }) => {\r\n    const dispatch = useDispatch();\r\n    const { selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    return (\r\n        <div\r\n            key={question.id}\r\n            className={`\r\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\r\n            ${selectedIndex === question.ExamQuestions?.order ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\"}`}\r\n            onClick={() => dispatch(setSelectedIndex(question.ExamQuestions?.order))}\r\n        >\r\n            {/* Thông tin câu hỏi */}\r\n            <div div className=\"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\" >\r\n                <span className=\"font-medium text-gray-700\">Câu {index + 1}</span>\r\n                <span>Độ khó: <span className=\"text-gray-700\">{question.difficulty}</span></span>\r\n                <span>\r\n                    Chương:{\" \"}\r\n                    <span className=\"text-gray-700\">\r\n                        {codes[\"chapter\"]?.find(c => c.code === question.chapter)?.description || \"Chưa xác định\"}\r\n                    </span>\r\n                </span>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col gap-2\">\r\n                <div className=\"text-base text-gray-800 leading-relaxed\">\r\n                    <LatexRenderer className=\"text-gray-800\" text={question.content} />\r\n                </div>\r\n                {question.imageUrl && (\r\n                    <div className=\"flex flex-col items-center justify-center w-full h-[10rem] mt-1\">\r\n                        <img\r\n                            src={question.imageUrl}\r\n                            alt=\"question\"\r\n                            className=\"object-contain w-full h-full\"\r\n                        />\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {question.solution && (\r\n                <div className=\"mt-2\">\r\n                    <h6 className=\"text-xs font-bold\">Lời giải</h6>\r\n                    <MarkdownPreviewWithMath content={question.solution} className=\" w-full\" style={{ fontSize: '12px' }} />\r\n                </div>\r\n            )}\r\n\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nconst QuestionViewHeader = ({ title, count, noQuestionText }) => {\r\n    return (\r\n        <div className=\" p-3 \">\r\n            <div className=\"flex items-center gap-1 mb-2\">\r\n                <FileText className=\"w-3 h-3 text-gray-600\" />\r\n                <h4 className=\"text-xs font-semibold text-gray-900\">{title} ({count})</h4>\r\n            </div>\r\n            {count === 0 && (\r\n                <div className=\"text-center py-4 text-gray-500\">\r\n                    <FileText className=\"w-6 h-6 mx-auto mb-1 opacity-50\" />\r\n                    <p className=\"text-xs\">{noQuestionText}</p>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst QuestionTNView = () => {\r\n    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const [questionsTN, setQuestionsTN] = useState([]);\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        setQuestionsTN(questionsExam.filter(q => q.typeOfQuestion === 'TN'));\r\n    }, [questionsExam]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trắc nghiệm\" count={questionsTN.length} noQuestionText=\"Chưa có câu hỏi trắc nghiệm\" />\r\n            <div className=\"p-3\">\r\n                {questionsTN.map((question, index) => (\r\n                    <QuestionContent key={index} question={question} index={index} />\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionDSView = () => {\r\n    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const [questionsDS, setQuestionsDS] = useState([]);\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        setQuestionsDS(questionsExam.filter(q => q.typeOfQuestion === 'DS'));\r\n    }, [questionsExam]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi đúng sai\" count={questionsDS.length} noQuestionText=\"Chưa có câu hỏi đúng sai\" />\r\n            <div className=\"p-3\">\r\n                {questionsDS.map((question, index) => (\r\n                    <div\r\n                        key={index} className={`mb-4 p-1 cursor-pointer ${selectedIndex === question.ExamQuestions?.order ? 'border border-blue-500 rounded-lg bg-blue-50' : ''} `}\r\n                        onClick={() => dispatch(setSelectedIndex(question.ExamQuestions?.order))}\r\n                    >\r\n                        <h5 className=\"text-xs font-bold\">Câu hỏi {index + 1}\r\n                            <span className=\"text-gray-500 font-normal\"> ({question.class || \"Chưa chọn\"} - {question.chapter || \"Chưa chọn\"} - {question.difficulty || \"Chưa chọn\"})</span>\r\n                        </h5>\r\n                        <LatexRenderer text={question.content} className=\"text-xs break-words w-full\" />\r\n                        <div className=\"flex flex-col gap-2 mt-2\">\r\n                            {question.statements.map((statement, index) => (\r\n                                <div key={index} className=\"flex items-center gap-1\">\r\n                                    <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                        {prefixDS[index]}\r\n                                    </p>\r\n                                    <LatexRenderer text={statement.content} className={`text-xs break-words w-full ${statement.isCorrect ? 'text-green-500' : 'text-red-500'}`} />\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        {question.solution && (\r\n                            <div className=\"mt-2\">\r\n                                <h6 className=\"text-xs font-bold\">Lời giải</h6>\r\n                                <MarkdownPreviewWithMath content={question.solution} className=\" w-full\" style={{ fontSize: '0.75rem' }} />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionTLNView = () => {\r\n    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n    const [questionsTLN, setQuestionsTLN] = useState([]);\r\n\r\n    useEffect(() => {\r\n        setQuestionsTLN(questionsExam.filter(q => q.typeOfQuestion === 'TLN'));\r\n    }, [questionsExam]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trả lời ngắn\" count={questionsTLN.length} noQuestionText=\"Chưa có câu hỏi trả lời ngắn\" />\r\n            <div className=\"p-3\">\r\n                {questionsTLN.map((question, index) => (\r\n                    <div\r\n                        key={index} className={`mb-4 p-1 cursor-pointer ${selectedIndex === question.ExamQuestions?.order ? 'border border-blue-500 rounded-lg bg-blue-50' : ''} `}\r\n                        onClick={() => dispatch(setSelectedIndex(question.ExamQuestions?.order))}\r\n                    >\r\n                        <h5 className=\"text-xs font-bold\">Câu hỏi {index + 1}\r\n                            <span className=\"text-gray-500 font-normal\"> ({question.class || \"Chưa chọn\"} - {question.chapter || \"Chưa chọn\"} - {question.difficulty || \"Chưa chọn\"})</span>\r\n                        </h5>\r\n                        <LatexRenderer text={question.content} className=\"text-xs break-words w-full\" />\r\n                        <p className=\"text-xs font-bold mt-2\">Đáp án:</p>\r\n                        <LatexRenderer text={question.correctAnswer} className=\"text-xs break-words w-full\" />\r\n                        {question.solution && (\r\n                            <div className=\"mt-2\">\r\n                                <h6 className=\"text-xs font-bold\">Lời giải</h6>\r\n                                <MarkdownPreviewWithMath content={question.solution} className=\" w-full\" style={{ fontSize: '0.75rem' }} />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionView = () => {\r\n\r\n    return (\r\n        <>\r\n            <QuestionTNView />\r\n            <hr className=\"border-gray-200\" />\r\n            <QuestionDSView />\r\n            <hr className=\"border-gray-200\" />\r\n            <QuestionTLNView />\r\n        </>\r\n    )\r\n}\r\n\r\nexport default QuestionView;"], "mappings": ";;;;;AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAC9D,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,+CAA+C;AACnG,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAC1B,OAAOC,oBAAoB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtE,MAAMC,eAAe,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,cAAA,EAAAC,mBAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAN,IAAA;EACxC,MAAMO,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B;EAAc,CAAC,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACrE,MAAM;IAAEC;EAAM,CAAC,GAAGjC,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,oBACIf,OAAA;IAEIgB,SAAS,oGAAAC,MAAA,CAEPL,aAAa,OAAAN,qBAAA,GAAKG,QAAQ,CAACS,aAAa,cAAAZ,qBAAA,uBAAtBA,qBAAA,CAAwBa,KAAK,IAAG,4BAA4B,GAAG,2CAA2C,CAAG;IACjIC,OAAO,EAAEA,CAAA;MAAA,IAAAC,sBAAA;MAAA,OAAMV,QAAQ,CAAC3B,gBAAgB,EAAAqC,sBAAA,GAACZ,QAAQ,CAACS,aAAa,cAAAG,sBAAA,uBAAtBA,sBAAA,CAAwBF,KAAK,CAAC,CAAC;IAAA,CAAC;IAAAG,QAAA,gBAGzEtB,OAAA;MAAKuB,GAAG;MAACP,SAAS,EAAC,mEAAmE;MAAAM,QAAA,gBAClFtB,OAAA;QAAMgB,SAAS,EAAC,2BAA2B;QAAAM,QAAA,GAAC,SAAI,EAACZ,KAAK,GAAG,CAAC;MAAA;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClE3B,OAAA;QAAAsB,QAAA,GAAM,uBAAQ,eAAAtB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAM,QAAA,EAAEb,QAAQ,CAACmB;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjF3B,OAAA;QAAAsB,QAAA,GAAM,mBACK,EAAC,GAAG,eACXtB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAM,QAAA,EAC1B,EAAAf,cAAA,GAAAQ,KAAK,CAAC,SAAS,CAAC,cAAAR,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKtB,QAAQ,CAACuB,OAAO,CAAC,cAAAxB,mBAAA,uBAAxDA,mBAAA,CAA0DyB,WAAW,KAAI;QAAe;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN3B,OAAA;MAAKgB,SAAS,EAAC,qBAAqB;MAAAM,QAAA,gBAChCtB,OAAA;QAAKgB,SAAS,EAAC,yCAAyC;QAAAM,QAAA,eACpDtB,OAAA,CAACf,aAAa;UAAC+B,SAAS,EAAC,eAAe;UAACkB,IAAI,EAAEzB,QAAQ,CAAC0B;QAAQ;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,EACLlB,QAAQ,CAAC2B,QAAQ,iBACdpC,OAAA;QAAKgB,SAAS,EAAC,iEAAiE;QAAAM,QAAA,eAC5EtB,OAAA;UACIqC,GAAG,EAAE5B,QAAQ,CAAC2B,QAAS;UACvBE,GAAG,EAAC,UAAU;UACdtB,SAAS,EAAC;QAA8B;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAELlB,QAAQ,CAAC8B,QAAQ,iBACdvC,OAAA;MAAKgB,SAAS,EAAC,MAAM;MAAAM,QAAA,gBACjBtB,OAAA;QAAIgB,SAAS,EAAC,mBAAmB;QAAAM,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/C3B,OAAA,CAACd,uBAAuB;QAACiD,OAAO,EAAE1B,QAAQ,CAAC8B,QAAS;QAACvB,SAAS,EAAC,SAAS;QAACwB,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CACR;EAAA,GAtCIlB,QAAQ,CAACiC,EAAE;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAyCf,CAAC;AAEd,CAAC;AAAAtB,EAAA,CAlDKF,eAAe;EAAA,QACApB,WAAW,EACFD,WAAW,EACnBA,WAAW;AAAA;AAAA6D,EAAA,GAH3BxC,eAAe;AAoDrB,MAAMyC,kBAAkB,GAAGC,KAAA,IAAsC;EAAA,IAArC;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAe,CAAC,GAAAH,KAAA;EACxD,oBACI7C,OAAA;IAAKgB,SAAS,EAAC,OAAO;IAAAM,QAAA,gBAClBtB,OAAA;MAAKgB,SAAS,EAAC,8BAA8B;MAAAM,QAAA,gBACzCtB,OAAA,CAACrB,QAAQ;QAACqC,SAAS,EAAC;MAAuB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9C3B,OAAA;QAAIgB,SAAS,EAAC,qCAAqC;QAAAM,QAAA,GAAEwB,KAAK,EAAC,IAAE,EAACC,KAAK,EAAC,GAAC;MAAA;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,EACLoB,KAAK,KAAK,CAAC,iBACR/C,OAAA;MAAKgB,SAAS,EAAC,gCAAgC;MAAAM,QAAA,gBAC3CtB,OAAA,CAACrB,QAAQ;QAACqC,SAAS,EAAC;MAAiC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxD3B,OAAA;QAAGgB,SAAS,EAAC,SAAS;QAAAM,QAAA,EAAE0B;MAAc;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAsB,GAAA,GAfKL,kBAAkB;AAiBxB,MAAMM,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAErC,aAAa;IAAEF;EAAc,CAAC,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACpF,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMyE,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM3C,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAE9BH,SAAS,CAAC,MAAM;IACZyE,cAAc,CAACvC,aAAa,CAACyC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,IAAI,CAAC,CAAC;EACxE,CAAC,EAAE,CAAC3C,aAAa,CAAC,CAAC;EAEnB,oBACId,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACItB,OAAA,CAAC4C,kBAAkB;MAACE,KAAK,EAAC,uCAAqB;MAACC,KAAK,EAAEK,WAAW,CAACM,MAAO;MAACV,cAAc,EAAC;IAA6B;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1H3B,OAAA;MAAKgB,SAAS,EAAC,KAAK;MAAAM,QAAA,EACf8B,WAAW,CAACO,GAAG,CAAC,CAAClD,QAAQ,EAAEC,KAAK,kBAC7BV,OAAA,CAACG,eAAe;QAAaM,QAAQ,EAAEA,QAAS;QAACC,KAAK,EAAEA;MAAM,GAAxCA,KAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqC,CACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAwB,GAAA,CApBKD,cAAc;EAAA,QACyBpE,WAAW,EAGnCC,WAAW;AAAA;AAAA6E,GAAA,GAJ1BV,cAAc;AAsBpB,MAAMW,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAEhD,aAAa;IAAEF;EAAc,CAAC,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACpF,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMoF,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMtD,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAE9BH,SAAS,CAAC,MAAM;IACZoF,cAAc,CAAClD,aAAa,CAACyC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,IAAI,CAAC,CAAC;EACxE,CAAC,EAAE,CAAC3C,aAAa,CAAC,CAAC;EAEnB,oBACId,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACItB,OAAA,CAAC4C,kBAAkB;MAACE,KAAK,EAAC,kCAAkB;MAACC,KAAK,EAAEgB,WAAW,CAACL,MAAO;MAACV,cAAc,EAAC;IAA0B;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpH3B,OAAA;MAAKgB,SAAS,EAAC,KAAK;MAAAM,QAAA,EACfyC,WAAW,CAACJ,GAAG,CAAC,CAAClD,QAAQ,EAAEC,KAAK;QAAA,IAAAwD,sBAAA;QAAA,oBAC7BlE,OAAA;UACgBgB,SAAS,6BAAAC,MAAA,CAA6BL,aAAa,OAAAsD,sBAAA,GAAKzD,QAAQ,CAACS,aAAa,cAAAgD,sBAAA,uBAAtBA,sBAAA,CAAwB/C,KAAK,IAAG,8CAA8C,GAAG,EAAE,MAAI;UAC3JC,OAAO,EAAEA,CAAA;YAAA,IAAA+C,sBAAA;YAAA,OAAMxD,QAAQ,CAAC3B,gBAAgB,EAAAmF,sBAAA,GAAC1D,QAAQ,CAACS,aAAa,cAAAiD,sBAAA,uBAAtBA,sBAAA,CAAwBhD,KAAK,CAAC,CAAC;UAAA,CAAC;UAAAG,QAAA,gBAEzEtB,OAAA;YAAIgB,SAAS,EAAC,mBAAmB;YAAAM,QAAA,GAAC,kBAAQ,EAACZ,KAAK,GAAG,CAAC,eAChDV,OAAA;cAAMgB,SAAS,EAAC,2BAA2B;cAAAM,QAAA,GAAC,IAAE,EAACb,QAAQ,CAAC2D,KAAK,IAAI,WAAW,EAAC,KAAG,EAAC3D,QAAQ,CAACuB,OAAO,IAAI,WAAW,EAAC,KAAG,EAACvB,QAAQ,CAACmB,UAAU,IAAI,WAAW,EAAC,GAAC;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK,CAAC,eACL3B,OAAA,CAACf,aAAa;YAACiD,IAAI,EAAEzB,QAAQ,CAAC0B,OAAQ;YAACnB,SAAS,EAAC;UAA4B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChF3B,OAAA;YAAKgB,SAAS,EAAC,0BAA0B;YAAAM,QAAA,EACpCb,QAAQ,CAAC4D,UAAU,CAACV,GAAG,CAAC,CAACW,SAAS,EAAE5D,KAAK,kBACtCV,OAAA;cAAiBgB,SAAS,EAAC,yBAAyB;cAAAM,QAAA,gBAChDtB,OAAA;gBAAGgB,SAAS,EAAC,qCAAqC;gBAAAM,QAAA,EAC7C2C,QAAQ,CAACvD,KAAK;cAAC;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACJ3B,OAAA,CAACf,aAAa;gBAACiD,IAAI,EAAEoC,SAAS,CAACnC,OAAQ;gBAACnB,SAAS,gCAAAC,MAAA,CAAgCqD,SAAS,CAACC,SAAS,GAAG,gBAAgB,GAAG,cAAc;cAAG;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,GAJxIjB,KAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EACLlB,QAAQ,CAAC8B,QAAQ,iBACdvC,OAAA;YAAKgB,SAAS,EAAC,MAAM;YAAAM,QAAA,gBACjBtB,OAAA;cAAIgB,SAAS,EAAC,mBAAmB;cAAAM,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C3B,OAAA,CAACd,uBAAuB;cAACiD,OAAO,EAAE1B,QAAQ,CAAC8B,QAAS;cAACvB,SAAS,EAAC,SAAS;cAACwB,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAU;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CACR;QAAA,GAtBIjB,KAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBT,CAAC;MAAA,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAmC,GAAA,CA5CKD,cAAc;EAAA,QACyB/E,WAAW,EAGnCC,WAAW;AAAA;AAAAyF,GAAA,GAJ1BX,cAAc;AA8CpB,MAAMY,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAM;IAAE5D,aAAa;IAAEF;EAAc,CAAC,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACpF,MAAMH,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAEpDD,SAAS,CAAC,MAAM;IACZgG,eAAe,CAAC9D,aAAa,CAACyC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,KAAK,CAAC,CAAC;EAC1E,CAAC,EAAE,CAAC3C,aAAa,CAAC,CAAC;EAEnB,oBACId,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACItB,OAAA,CAAC4C,kBAAkB;MAACE,KAAK,EAAC,6CAAsB;MAACC,KAAK,EAAE4B,YAAY,CAACjB,MAAO;MAACV,cAAc,EAAC;IAA8B;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7H3B,OAAA;MAAKgB,SAAS,EAAC,KAAK;MAAAM,QAAA,EACfqD,YAAY,CAAChB,GAAG,CAAC,CAAClD,QAAQ,EAAEC,KAAK;QAAA,IAAAmE,sBAAA;QAAA,oBAC9B7E,OAAA;UACgBgB,SAAS,6BAAAC,MAAA,CAA6BL,aAAa,OAAAiE,sBAAA,GAAKpE,QAAQ,CAACS,aAAa,cAAA2D,sBAAA,uBAAtBA,sBAAA,CAAwB1D,KAAK,IAAG,8CAA8C,GAAG,EAAE,MAAI;UAC3JC,OAAO,EAAEA,CAAA;YAAA,IAAA0D,sBAAA;YAAA,OAAMnE,QAAQ,CAAC3B,gBAAgB,EAAA8F,sBAAA,GAACrE,QAAQ,CAACS,aAAa,cAAA4D,sBAAA,uBAAtBA,sBAAA,CAAwB3D,KAAK,CAAC,CAAC;UAAA,CAAC;UAAAG,QAAA,gBAEzEtB,OAAA;YAAIgB,SAAS,EAAC,mBAAmB;YAAAM,QAAA,GAAC,kBAAQ,EAACZ,KAAK,GAAG,CAAC,eAChDV,OAAA;cAAMgB,SAAS,EAAC,2BAA2B;cAAAM,QAAA,GAAC,IAAE,EAACb,QAAQ,CAAC2D,KAAK,IAAI,WAAW,EAAC,KAAG,EAAC3D,QAAQ,CAACuB,OAAO,IAAI,WAAW,EAAC,KAAG,EAACvB,QAAQ,CAACmB,UAAU,IAAI,WAAW,EAAC,GAAC;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK,CAAC,eACL3B,OAAA,CAACf,aAAa;YAACiD,IAAI,EAAEzB,QAAQ,CAAC0B,OAAQ;YAACnB,SAAS,EAAC;UAA4B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChF3B,OAAA;YAAGgB,SAAS,EAAC,wBAAwB;YAAAM,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjD3B,OAAA,CAACf,aAAa;YAACiD,IAAI,EAAEzB,QAAQ,CAACsE,aAAc;YAAC/D,SAAS,EAAC;UAA4B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACrFlB,QAAQ,CAAC8B,QAAQ,iBACdvC,OAAA;YAAKgB,SAAS,EAAC,MAAM;YAAAM,QAAA,gBACjBtB,OAAA;cAAIgB,SAAS,EAAC,mBAAmB;cAAAM,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C3B,OAAA,CAACd,uBAAuB;cAACiD,OAAO,EAAE1B,QAAQ,CAAC8B,QAAS;cAACvB,SAAS,EAAC,SAAS;cAACwB,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAU;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CACR;QAAA,GAdIjB,KAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeT,CAAC;MAAA,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAA+C,GAAA,CAnCKD,eAAe;EAAA,QACwB3F,WAAW,EACnCC,WAAW;AAAA;AAAAiG,GAAA,GAF1BP,eAAe;AAqCrB,MAAMQ,YAAY,GAAGA,CAAA,KAAM;EAEvB,oBACIjF,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACItB,OAAA,CAACkD,cAAc;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClB3B,OAAA;MAAIgB,SAAS,EAAC;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClC3B,OAAA,CAAC6D,cAAc;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClB3B,OAAA;MAAIgB,SAAS,EAAC;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClC3B,OAAA,CAACyE,eAAe;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACrB,CAAC;AAEX,CAAC;AAAAuD,GAAA,GAXKD,YAAY;AAalB,eAAeA,YAAY;AAAC,IAAAtC,EAAA,EAAAM,GAAA,EAAAW,GAAA,EAAAY,GAAA,EAAAQ,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAxC,EAAA;AAAAwC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}