import cron from 'node-cron';
import db from '../models/index.js';
import { Op } from 'sequelize';

const { User } = db;

/**
 * Cron job để deactivate users vào tháng 7 hàng năm
 * Chạy vào ngày 1 tháng 7 lúc 00:00 (midnight) hàng năm
 * Cron pattern: '0 0 1 7 *' = phút 0, giờ 0, ngày 1, tháng 7, mọi năm
 */
export const scheduleUserDeactivation = () => {
    // Cron job chạy vào 00:00 ngày 1 tháng 7 hàng năm
    cron.schedule('0 0 1 7 *', async () => {
        try {
            console.log('🔄 Starting annual user deactivation process...');
            
            const currentYear = new Date().getFullYear();
            console.log(`📅 Current year: ${currentYear}`);
            
            // Tìm và deactivate users có graduationYear = currentYear hoặc null
            const result = await User.update(
                { 
                    isActive: false,
                    updatedAt: new Date()
                },
                {
                    where: {
                        [Op.and]: [
                            {
                                [Op.or]: [
                                    { graduationYear: currentYear },
                                    { graduationYear: null }
                                ]
                            },
                            { isActive: true } // Chỉ update những user đang active
                        ]
                    }
                }
            );
            
            const deactivatedCount = result[0]; // Số lượng records được update
            
            console.log(`✅ Annual user deactivation completed successfully!`);
            console.log(`📊 Deactivated ${deactivatedCount} users with graduationYear = ${currentYear} or null`);
            
            // Log chi tiết để audit
            const logData = {
                timestamp: new Date().toISOString(),
                action: 'ANNUAL_USER_DEACTIVATION',
                year: currentYear,
                deactivatedCount: deactivatedCount,
                criteria: `graduationYear = ${currentYear} OR graduationYear IS NULL`
            };
            
            console.log('📋 Audit log:', JSON.stringify(logData, null, 2));
            
        } catch (error) {
            console.error('❌ Error during annual user deactivation:', error);
            
            // Log error để debugging
            const errorLog = {
                timestamp: new Date().toISOString(),
                action: 'ANNUAL_USER_DEACTIVATION_ERROR',
                error: error.message,
                stack: error.stack
            };
            
            console.error('🚨 Error log:', JSON.stringify(errorLog, null, 2));
        }
    }, {
        scheduled: true,
        timezone: "Asia/Ho_Chi_Minh" // Timezone Việt Nam
    });
    
    console.log('⏰ Annual user deactivation cron job scheduled for July 1st at 00:00 (Vietnam time)');
};

/**
 * Cron job test - chạy mỗi phút để test (chỉ dùng khi development)
 * Uncomment để test functionality
 */
export const scheduleUserDeactivationTest = () => {
    // CẢNH BÁO: Chỉ dùng để test, không enable trong production
    // cron.schedule('* * * * *', async () => {
    //     try {
    //         console.log('🧪 [TEST] Running user deactivation test...');
            
    //         const currentYear = new Date().getFullYear();
            
    //         // Chỉ log, không thực sự update trong test
    //         const usersToDeactivate = await User.findAll({
    //             where: {
    //                 [Op.and]: [
    //                     {
    //                         [Op.or]: [
    //                             { graduationYear: currentYear },
    //                             { graduationYear: null }
    //                         ]
    //                     },
    //                     { isActive: true }
    //                 ]
    //             },
    //             attributes: ['id', 'username', 'graduationYear', 'isActive']
    //         });
            
    //         console.log(`🧪 [TEST] Found ${usersToDeactivate.length} users that would be deactivated:`);
    //         usersToDeactivate.forEach(user => {
    //             console.log(`  - User ID: ${user.id}, Username: ${user.username}, GraduationYear: ${user.graduationYear}`);
    //         });
            
    //     } catch (error) {
    //         console.error('🧪 [TEST] Error during test:', error);
    //     }
    // }, {
    //     scheduled: true,
    //     timezone: "Asia/Ho_Chi_Minh"
    // });
    
    // console.log('🧪 [TEST] User deactivation test cron job scheduled (every minute)');
};

/**
 * Manual function để trigger deactivation (dùng cho testing hoặc manual run)
 */
export const manualUserDeactivation = async () => {
    try {
        console.log('🔧 Manual user deactivation triggered...');
        
        const currentYear = new Date().getFullYear();
        console.log(`📅 Current year: ${currentYear}`);
        
        // Tìm users sẽ bị deactivate trước khi update
        const usersToDeactivate = await User.findAll({
            where: {
                [Op.and]: [
                    {
                        [Op.or]: [
                            { graduationYear: currentYear },
                            { graduationYear: null }
                        ]
                    },
                    { isActive: true }
                ]
            },
            attributes: ['id', 'username', 'graduationYear', 'isActive']
        });
        
        console.log(`📋 Found ${usersToDeactivate.length} users to deactivate:`);
        usersToDeactivate.forEach(user => {
            console.log(`  - User ID: ${user.id}, Username: ${user.username}, GraduationYear: ${user.graduationYear}`);
        });
        
        // Thực hiện update
        const result = await User.update(
            { 
                isActive: false,
                updatedAt: new Date()
            },
            {
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                { graduationYear: currentYear },
                                { graduationYear: null }
                            ]
                        },
                        { isActive: true }
                    ]
                }
            }
        );
        
        const deactivatedCount = result[0];
        
        console.log(`✅ Manual user deactivation completed!`);
        console.log(`📊 Deactivated ${deactivatedCount} users`);
        
        return {
            success: true,
            deactivatedCount: deactivatedCount,
            year: currentYear,
            usersDeactivated: usersToDeactivate
        };
        
    } catch (error) {
        console.error('❌ Error during manual user deactivation:', error);
        throw error;
    }
};

/**
 * Function để lấy thống kê users sẽ bị deactivate
 */
export const getUserDeactivationStats = async () => {
    try {
        const currentYear = new Date().getFullYear();
        
        const stats = await User.findAndCountAll({
            where: {
                [Op.and]: [
                    {
                        [Op.or]: [
                            { graduationYear: currentYear },
                            { graduationYear: null }
                        ]
                    },
                    { isActive: true }
                ]
            },
            attributes: ['id', 'username', 'graduationYear', 'isActive', 'createdAt']
        });
        
        return {
            currentYear: currentYear,
            totalUsersToDeactivate: stats.count,
            users: stats.rows,
            criteria: `graduationYear = ${currentYear} OR graduationYear IS NULL`
        };
        
    } catch (error) {
        console.error('❌ Error getting user deactivation stats:', error);
        throw error;
    }
};
