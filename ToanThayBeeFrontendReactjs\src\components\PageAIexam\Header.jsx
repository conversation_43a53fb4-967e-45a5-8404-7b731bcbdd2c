import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import Button from "src/components/PageAIexam/Button";
import { useSelector, useDispatch } from 'react-redux';
import { useState, useEffect } from 'react';
import { setViewEdit } from 'src/features/examAI/examAISlice';

const arrayOfObjectEqualUnordered = (arr1, arr2) => {
    if (arr1.length !== arr2.length) return false;
    const sortedArr1 = arr1.map(obj => JSON.stringify(obj)).sort();
    const sortedArr2 = arr2.map(obj => JSON.stringify(obj)).sort();
    return sortedArr1.every((value, index) => value === sortedArr2[index]);
};

const Header = ({ title = "Đề thi AI" }) => {
    const navigate = useNavigate();
    const [isChanged, setIsChanged] = useState(false);
    const { questions, questionsEdited, viewEdit } = useSelector((state) => state.examAI);
    const dispatch = useDispatch();

    useEffect(() => {
        setIsChanged(!arrayOfObjectEqualUnordered(questions, questionsEdited));
    }, [questions, questionsEdited]);

    return (
        <div className="fixed z-50 top-0 w-full bg-white h-16 px-4 flex items-center justify-between border-b border-gray-300">
            <div className="flex items-center gap-4">
                <Button onClick={() => navigate(-1)} type="none">
                    <ChevronLeft className="w-5 h-5 text-black" />
                </Button>
                <p className="font-bold">Quản lý</p>
                <p className="text-gray-500">{title}</p>
            </div>

            <div className="flex items-center gap-4">
                <button
                    // onClick={}
                    className={`px-2 py-1 text-sm rounded-md transition-colors duration-300
                ${isChanged ? 'bg-emerald-500 hover:bg-emerald-700' : 'bg-gray-300 cursor-not-allowed'}`}
                    disabled={!isChanged}
                >
                    <span className="text-white">Lưu thay đổi</span>
                </button>
            </div>

            {/* 🟦 Nút giữa cạnh dưới header */}
            <div className="absolute left-1/2 -translate-x-1/2 bottom-[-1.25rem] z-50">
                <button
                    onClick={() => dispatch(setViewEdit('exam'))}
                    className={`px-4 py-1 ${viewEdit === 'exam' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200'} text-sm border border-gray-300`}
                >
                    Đề thi
                </button>
                <button
                    onClick={() => dispatch(setViewEdit('question'))}
                    className={`px-4 py-1 ${viewEdit === 'question' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200'} text-sm  border border-gray-300`}
                >
                    Câu hỏi
                </button>
            </div>
        </div>

    );
};
export default Header;