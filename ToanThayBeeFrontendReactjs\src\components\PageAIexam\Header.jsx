import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import Button from "src/components/PageAIexam/Button";
import { useSelector, useDispatch } from 'react-redux';
import { useState, useEffect } from 'react';
import { setViewEdit, setIsChange, saveExam, commitExam } from 'src/features/examAI/examAISlice';
import ConfirmModal from "src/components/modal/ConfirmModal";
import { setErrorMessage } from 'src/features/state/stateApiSlice';

const arrayOfObjectEqualUnordered = (arr1, arr2) => {
    if (arr1.length !== arr2.length) return false;
    const sortedArr1 = arr1.map(obj => JSON.stringify(obj)).sort();
    const sortedArr2 = arr2.map(obj => JSON.stringify(obj)).sort();
    return sortedArr1.every((value, index) => value === sortedArr2[index]);
};

const objectEqual = (obj1, obj2) => {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
};

const validateExamData = (examData, dispatch) => {
    if (examData.name.trim() === "") {
        dispatch(setErrorMessage("Tên đề thi không được để trống!"));
        return false;
    }
    if (examData.class === null) {
        dispatch(setErrorMessage("Lớp đề thi không được để trống!"));
        return false;
    }
    if (examData.typeOfExam === null) {
        dispatch(setErrorMessage("Loại đề thi không được để trống!"));
        return false;
    }
    return true;
}

const validateQuestionsData = (questions, dispatch) => {
    for (const question of questions) {
        if (question.content.trim() === "") {
            dispatch(setErrorMessage("Nội dung câu hỏi không được để trống!"));
            return false;
        }
        if (question.typeOfQuestion === null) {
            dispatch(setErrorMessage("Loại câu hỏi không được để trống!"));
            return false;
        }
        if (question.class === null || question.class === "") {
            dispatch(setErrorMessage("Lớp câu hỏi không được để trống!"));
            return false;
        }
        if (question.typeOfQuestion === "TLN" && (question.correctAnswer === null || question.correctAnswer === "")) {
            dispatch(setErrorMessage("Đáp án câu hỏi TLN không được để trống!"));
            return false;
        }
        if (question.typeOfQuestion === "TLN" && !question.correctAnswer.trim().replace(",", ".").match(/^-?\d+(\.\d+)?$/)) {
            dispatch(setErrorMessage("Đáp án câu hỏi TLN phải là một số!"));
            return false;
        }
    }
    return true;
}

const Header = ({ title = "Đề thi AI", tabNavigate = true }) => {
    const navigate = useNavigate();
    const { questions, questionsEdited, viewEdit, exam, editedExam, isChange, loadingSave, loadingCommit } = useSelector((state) => state.examAI);
    const dispatch = useDispatch();
    const { closeSidebar } = useSelector((state) => state.sidebar);
    const [modalOpen, setModalOpen] = useState(false);
    const [confirm, setConfirm] = useState(false);

    useEffect(() => {
        if (isChange === true) return
        dispatch(setIsChange(!arrayOfObjectEqualUnordered(questions, questionsEdited)));
    }, [questions, questionsEdited]);

    useEffect(() => {
        if (isChange === true) return
        dispatch(setIsChange(!objectEqual(exam, editedExam)));
    }, [exam, editedExam]);

    const handleCommitExam = () => {
        if (!exam?.id) return;
        if (!validateExamData(exam, dispatch)) return;
        if (!validateQuestionsData(questions, dispatch)) return;
        setModalOpen(true);
    }

    // useEffect(() => {
    //     console.log(modalOpen)
    // }, [modalOpen]);

    useEffect(() => {
        if (confirm) {
            dispatch(commitExam(exam.id));
            setConfirm(false);
        }
    }, [confirm]);

    return (
        <div className={`fixed z-50 top-0  bg-white h-16 px-4 flex items-center justify-between border-b border-gray-300 ${closeSidebar ? "w-[calc(100vw_-_104px)]" : "w-[calc(100vw_-16rem)]"}`}>
            <ConfirmModal
                isOpen={modalOpen}
                onClose={() => setModalOpen(false)}
                onConfirm={() => setConfirm(true)}
                title="Xác nhận cập nhật"
                message={`${isChange ? "Bạn có sự thay đổi, s" : "S"}au khi cập nhật đề thi thật bản nháp này sẽ mất bạn chắc chắn muốn cập nhật không?`}
            />
            <div className="flex items-center gap-4">
                <Button onClick={() => navigate(-1)} type="none">
                    <ChevronLeft className="w-5 h-5 text-black" />
                </Button>
                <p className="font-bold">Quản lý</p>
                <p className="text-gray-500">{title}</p>
            </div>
            {tabNavigate && (
                <div className="flex items-center gap-4">
                    {/* <button
                        onClick={() => dispatch(saveExam({ examData: editedExam, questions: questionsEdited, id: exam.id }))}
                        className={`px-2 py-1 text-sm rounded-md transition-colors duration-300
                ${isChange ? 'bg-emerald-500 hover:bg-emerald-700' : 'bg-gray-300 cursor-not-allowed'}`}
                        disabled={!isChange}
                    >
                        <span className="text-white">Lưu thay đổi</span>
                    </button> */}

                    <Button
                        onClick={handleCommitExam}
                        type="commit"
                        disabled={loadingCommit}
                        loading={loadingCommit}

                    >
                        Cập nhật
                    </Button>
                    <Button
                        onClick={() => dispatch(saveExam({ examData: editedExam, questions: questionsEdited, id: exam.id }))}
                        type={!isChange ? "disabled" : "save"}
                        disabled={!isChange || loadingSave}
                        loading={loadingSave}
                    >
                        Lưu thay đổi
                    </Button>
                </div>
            )}

            {/* 🟦 Nút giữa cạnh dưới header */}
            {tabNavigate && (
                <div className="absolute left-1/2 -translate-x-1/2 bottom-[-1.25rem] z-50">
                    <button
                        onClick={() => dispatch(setViewEdit('exam'))}
                        className={`px-4 py-1 ${viewEdit === 'exam' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200'} text-sm border border-gray-300`}
                    >
                        Đề thi
                    </button>
                    <button
                        onClick={() => dispatch(setViewEdit('question'))}
                        className={`px-4 py-1 ${viewEdit === 'question' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200'} text-sm  border border-gray-300`}
                    >
                        Câu hỏi
                    </button>
                </div>
            )}
        </div>

    );
};
export default Header;