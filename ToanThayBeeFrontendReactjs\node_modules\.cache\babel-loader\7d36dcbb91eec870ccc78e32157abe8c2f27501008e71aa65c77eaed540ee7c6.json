{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\SortableStatementsContainer.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/questionsExam/questionsExamSlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableStatementsContainer = _ref => {\n  _s();\n  var _question$statements, _question$statements2;\n  let {\n    question\n  } = _ref;\n  const dispatch = useDispatch();\n\n  // Debug: Log question structure\n  console.log('Question structure:', {\n    id: question.id,\n    type: question.typeOfQuestion,\n    hasStatements: !!question.statements,\n    statementsLength: (_question$statements = question.statements) === null || _question$statements === void 0 ? void 0 : _question$statements.length,\n    statementsKeys: question.statements ? Object.keys(question.statements[0] || {}) : [],\n    firstStatement: (_question$statements2 = question.statements) === null || _question$statements2 === void 0 ? void 0 : _question$statements2[0]\n  });\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n  const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    console.log('Statement drag end:', {\n      activeId: active.id,\n      overId: over === null || over === void 0 ? void 0 : over.id\n    });\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      // Sử dụng index trực tiếp từ id (vì id có format \"statementId-index\" hoặc chỉ là index)\n      const oldIndex = parseInt(active.id.toString().split('-').pop()) || question.statements.findIndex((item, idx) => \"\".concat(item.id || idx) === active.id);\n      const newIndex = parseInt(over.id.toString().split('-').pop()) || question.statements.findIndex((item, idx) => \"\".concat(item.id || idx) === over.id);\n      console.log('Statement indices:', {\n        activeId: active.id,\n        overId: over.id,\n        oldIndex,\n        newIndex,\n        questionId: question.id,\n        statementsLength: question.statements.length\n      });\n      if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {\n        dispatch(reorderStatements({\n          questionId: question.id,\n          oldIndex,\n          newIndex\n        }));\n      }\n    }\n  };\n  const getPrefix = index => {\n    if (question.typeOfQuestion === \"TN\") {\n      return prefixTN[index] || \"\".concat(String.fromCharCode(65 + index), \".\");\n    } else if (question.typeOfQuestion === \"DS\") {\n      return prefixDS[index] || \"\".concat(String.fromCharCode(97 + index), \")\");\n    }\n    return \"\".concat(index + 1, \".\");\n  };\n  if (question.typeOfQuestion === \"TLN\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-800 mt-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"\\u0110\\xE1p \\xE1n: \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: question.correctAnswer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this);\n  }\n  if (!question.statements || question.statements.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2\",\n    children: /*#__PURE__*/_jsxDEV(DndContext, {\n      sensors: sensors,\n      collisionDetection: closestCenter,\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(SortableContext, {\n        items: question.statements.map((item, idx) => \"\".concat(item.id || idx)),\n        strategy: verticalListSortingStrategy,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1\",\n          children: question.statements.map((item, idx) => /*#__PURE__*/_jsxDEV(SortableStatementItem, {\n            statement: item,\n            index: idx,\n            prefix: getPrefix(idx),\n            isCorrect: item.isCorrect,\n            questionType: question.typeOfQuestion\n          }, \"\".concat(item.id || idx), false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableStatementsContainer, \"fKWeCsq/OsPww/eQUkThDyU1Mpg=\", false, function () {\n  return [useDispatch, useSensors, useSensor, useSensor];\n});\n_c = SortableStatementsContainer;\nexport default SortableStatementsContainer;\nvar _c;\n$RefreshReg$(_c, \"SortableStatementsContainer\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "reorderStatements", "SortableStatementItem", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SortableStatementsContainer", "_ref", "_s", "_question$statements", "_question$statements2", "question", "dispatch", "console", "log", "id", "type", "typeOfQuestion", "hasStatements", "statements", "<PERSON><PERSON><PERSON><PERSON>", "length", "statementsKeys", "Object", "keys", "firstStatement", "sensors", "activationConstraint", "distance", "coordinateGetter", "prefixTN", "prefixDS", "handleDragEnd", "event", "active", "over", "activeId", "overId", "oldIndex", "parseInt", "toString", "split", "pop", "findIndex", "item", "idx", "concat", "newIndex", "questionId", "getPrefix", "index", "String", "fromCharCode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "collisionDetection", "onDragEnd", "items", "map", "strategy", "statement", "prefix", "isCorrect", "questionType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/SortableStatementsContainer.jsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/questionsExam/questionsExamSlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport {\n    DndContext,\n    closestCenter,\n    KeyboardSensor,\n    PointerSensor,\n    useSensor,\n    useSensors,\n} from '@dnd-kit/core';\nimport {\n    SortableContext,\n    sortableKeyboardCoordinates,\n    verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport LatexRenderer from \"../latex/RenderLatex\";\n\nconst SortableStatementsContainer = ({ question }) => {\n    const dispatch = useDispatch();\n\n    // Debug: Log question structure\n    console.log('Question structure:', {\n        id: question.id,\n        type: question.typeOfQuestion,\n        hasStatements: !!question.statements,\n        statementsLength: question.statements?.length,\n        statementsKeys: question.statements ? Object.keys(question.statements[0] || {}) : [],\n        firstStatement: question.statements?.[0]\n    });\n    const sensors = useSensors(\n        useSensor(PointerSensor, {\n            activationConstraint: {\n                distance: 8,\n            },\n        }),\n        useSensor(KeyboardSensor, {\n            coordinateGetter: sortableKeyboardCoordinates,\n        })\n    );\n\n    const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n    const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n\n    const handleDragEnd = (event) => {\n        const { active, over } = event;\n        console.log('Statement drag end:', { activeId: active.id, overId: over?.id });\n\n        if (active.id !== over?.id) {\n            // Sử dụng index trực tiếp từ id (vì id có format \"statementId-index\" hoặc chỉ là index)\n            const oldIndex = parseInt(active.id.toString().split('-').pop()) ||\n                            question.statements.findIndex((item, idx) => `${item.id || idx}` === active.id);\n            const newIndex = parseInt(over.id.toString().split('-').pop()) ||\n                           question.statements.findIndex((item, idx) => `${item.id || idx}` === over.id);\n\n            console.log('Statement indices:', {\n                activeId: active.id,\n                overId: over.id,\n                oldIndex,\n                newIndex,\n                questionId: question.id,\n                statementsLength: question.statements.length\n            });\n\n            if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {\n                dispatch(reorderStatements({\n                    questionId: question.id,\n                    oldIndex,\n                    newIndex\n                }));\n            }\n        }\n    };\n\n    const getPrefix = (index) => {\n        if (question.typeOfQuestion === \"TN\") {\n            return prefixTN[index] || `${String.fromCharCode(65 + index)}.`;\n        } else if (question.typeOfQuestion === \"DS\") {\n            return prefixDS[index] || `${String.fromCharCode(97 + index)})`;\n        }\n        return `${index + 1}.`;\n    };\n\n    if (question.typeOfQuestion === \"TLN\") {\n        return (\n            <div className=\"text-gray-800 mt-2\">\n                <span className=\"font-semibold\">Đáp án: </span>\n                <span>{question.correctAnswer}</span>\n            </div>\n        );\n    }\n\n    if (!question.statements || question.statements.length === 0) {\n        return null;\n    }\n\n    return (\n        <div className=\"mt-2\">\n            <DndContext\n                sensors={sensors}\n                collisionDetection={closestCenter}\n                onDragEnd={handleDragEnd}\n            >\n                <SortableContext\n                    items={question.statements.map((item, idx) => `${item.id || idx}`)}\n                    strategy={verticalListSortingStrategy}\n                >\n                    <div className=\"space-y-1\">\n                        {question.statements.map((item, idx) => (\n                            <SortableStatementItem\n                                key={`${item.id || idx}`}\n                                statement={item}\n                                index={idx}\n                                prefix={getPrefix(idx)}\n                                isCorrect={item.isCorrect}\n                                questionType={question.typeOfQuestion}\n                            />\n                        ))}\n                    </div>\n                </SortableContext>\n            </DndContext>\n        </div>\n    );\n};\n\nexport default SortableStatementsContainer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAC1B,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,2BAA2B,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAJ,IAAA;EAC7C,MAAMK,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACAuB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;IAC/BC,EAAE,EAAEJ,QAAQ,CAACI,EAAE;IACfC,IAAI,EAAEL,QAAQ,CAACM,cAAc;IAC7BC,aAAa,EAAE,CAAC,CAACP,QAAQ,CAACQ,UAAU;IACpCC,gBAAgB,GAAAX,oBAAA,GAAEE,QAAQ,CAACQ,UAAU,cAAAV,oBAAA,uBAAnBA,oBAAA,CAAqBY,MAAM;IAC7CC,cAAc,EAAEX,QAAQ,CAACQ,UAAU,GAAGI,MAAM,CAACC,IAAI,CAACb,QAAQ,CAACQ,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;IACpFM,cAAc,GAAAf,qBAAA,GAAEC,QAAQ,CAACQ,UAAU,cAAAT,qBAAA,uBAAnBA,qBAAA,CAAsB,CAAC;EAC3C,CAAC,CAAC;EACF,MAAMgB,OAAO,GAAG3B,UAAU,CACtBD,SAAS,CAACD,aAAa,EAAE;IACrB8B,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACF9B,SAAS,CAACF,cAAc,EAAE;IACtBiC,gBAAgB,EAAE5B;EACtB,CAAC,CACL,CAAC;EAED,MAAM6B,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAC9BpB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MAAEsB,QAAQ,EAAEF,MAAM,CAACnB,EAAE;MAAEsB,MAAM,EAAEF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEpB;IAAG,CAAC,CAAC;IAE7E,IAAImB,MAAM,CAACnB,EAAE,MAAKoB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEpB,EAAE,GAAE;MACxB;MACA,MAAMuB,QAAQ,GAAGC,QAAQ,CAACL,MAAM,CAACnB,EAAE,CAACyB,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,IAChD/B,QAAQ,CAACQ,UAAU,CAACwB,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK,GAAAC,MAAA,CAAGF,IAAI,CAAC7B,EAAE,IAAI8B,GAAG,MAAOX,MAAM,CAACnB,EAAE,CAAC;MAC/F,MAAMgC,QAAQ,GAAGR,QAAQ,CAACJ,IAAI,CAACpB,EAAE,CAACyB,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,IAC/C/B,QAAQ,CAACQ,UAAU,CAACwB,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK,GAAAC,MAAA,CAAGF,IAAI,CAAC7B,EAAE,IAAI8B,GAAG,MAAOV,IAAI,CAACpB,EAAE,CAAC;MAE5FF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;QAC9BsB,QAAQ,EAAEF,MAAM,CAACnB,EAAE;QACnBsB,MAAM,EAAEF,IAAI,CAACpB,EAAE;QACfuB,QAAQ;QACRS,QAAQ;QACRC,UAAU,EAAErC,QAAQ,CAACI,EAAE;QACvBK,gBAAgB,EAAET,QAAQ,CAACQ,UAAU,CAACE;MAC1C,CAAC,CAAC;MAEF,IAAIiB,QAAQ,KAAK,CAAC,CAAC,IAAIS,QAAQ,KAAK,CAAC,CAAC,IAAIT,QAAQ,KAAKS,QAAQ,EAAE;QAC7DnC,QAAQ,CAACpB,iBAAiB,CAAC;UACvBwD,UAAU,EAAErC,QAAQ,CAACI,EAAE;UACvBuB,QAAQ;UACRS;QACJ,CAAC,CAAC,CAAC;MACP;IACJ;EACJ,CAAC;EAED,MAAME,SAAS,GAAIC,KAAK,IAAK;IACzB,IAAIvC,QAAQ,CAACM,cAAc,KAAK,IAAI,EAAE;MAClC,OAAOa,QAAQ,CAACoB,KAAK,CAAC,OAAAJ,MAAA,CAAOK,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGF,KAAK,CAAC,MAAG;IACnE,CAAC,MAAM,IAAIvC,QAAQ,CAACM,cAAc,KAAK,IAAI,EAAE;MACzC,OAAOc,QAAQ,CAACmB,KAAK,CAAC,OAAAJ,MAAA,CAAOK,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGF,KAAK,CAAC,MAAG;IACnE;IACA,UAAAJ,MAAA,CAAUI,KAAK,GAAG,CAAC;EACvB,CAAC;EAED,IAAIvC,QAAQ,CAACM,cAAc,KAAK,KAAK,EAAE;IACnC,oBACIZ,OAAA;MAAKgD,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBAC/BjD,OAAA;QAAMgD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CrD,OAAA;QAAAiD,QAAA,EAAO3C,QAAQ,CAACgD;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEd;EAEA,IAAI,CAAC/C,QAAQ,CAACQ,UAAU,IAAIR,QAAQ,CAACQ,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;IAC1D,OAAO,IAAI;EACf;EAEA,oBACIhB,OAAA;IAAKgD,SAAS,EAAC,MAAM;IAAAC,QAAA,eACjBjD,OAAA,CAACX,UAAU;MACPgC,OAAO,EAAEA,OAAQ;MACjBkC,kBAAkB,EAAEjE,aAAc;MAClCkE,SAAS,EAAE7B,aAAc;MAAAsB,QAAA,eAEzBjD,OAAA,CAACL,eAAe;QACZ8D,KAAK,EAAEnD,QAAQ,CAACQ,UAAU,CAAC4C,GAAG,CAAC,CAACnB,IAAI,EAAEC,GAAG,QAAAC,MAAA,CAAQF,IAAI,CAAC7B,EAAE,IAAI8B,GAAG,CAAE,CAAE;QACnEmB,QAAQ,EAAE9D,2BAA4B;QAAAoD,QAAA,eAEtCjD,OAAA;UAAKgD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACrB3C,QAAQ,CAACQ,UAAU,CAAC4C,GAAG,CAAC,CAACnB,IAAI,EAAEC,GAAG,kBAC/BxC,OAAA,CAACZ,qBAAqB;YAElBwE,SAAS,EAAErB,IAAK;YAChBM,KAAK,EAAEL,GAAI;YACXqB,MAAM,EAAEjB,SAAS,CAACJ,GAAG,CAAE;YACvBsB,SAAS,EAAEvB,IAAI,CAACuB,SAAU;YAC1BC,YAAY,EAAEzD,QAAQ,CAACM;UAAe,MAAA6B,MAAA,CAL9BF,IAAI,CAAC7B,EAAE,IAAI8B,GAAG;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMzB,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEd,CAAC;AAAClD,EAAA,CAzGIF,2BAA2B;EAAA,QACZhB,WAAW,EAWZS,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAAuE,EAAA,GAlBX/D,2BAA2B;AA2GjC,eAAeA,2BAA2B;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}