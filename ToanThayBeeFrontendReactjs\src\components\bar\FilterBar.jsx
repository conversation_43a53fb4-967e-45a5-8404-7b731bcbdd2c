import { useSelector, useDispatch } from "react-redux";
import { useState, useEffect } from "react";
import { setGraduationYearFilter, setClassFilter } from "../../features/user/userSlice";
import { X } from "lucide-react";

const FilterBar = () => {
    const dispatch = useDispatch();
    const { graduationYearFilter, classFilter } = useSelector((state) => state.users);
    const [isOpen, setIsOpen] = useState(false);

    // Local state for temporary filter values
    const [tempGraduationYear, setTempGraduationYear] = useState(graduationYearFilter);
    const [tempClassFilter, setTempClassFilter] = useState(classFilter);

    // Sync temp values when filter values change from outside (e.g., clear filters)
    useEffect(() => {
        setTempGraduationYear(graduationYearFilter);
        setTempClassFilter(classFilter);
    }, [graduationYearFilter, classFilter]);

    // Generate graduation year options (current year to current year + 6)
    const currentYear = new Date().getFullYear();
    const graduationYearOptions = [];
    for (let year = currentYear; year <= currentYear + 6; year++) {
        graduationYearOptions.push(year);
    }

    const classOptions = ['10', '11', '12'];

    // Handle temporary filter changes (not applied yet)
    const handleTempGraduationYearChange = (year) => {
        setTempGraduationYear(year === '' ? null : parseInt(year));
    };

    const handleTempClassChange = (classValue) => {
        setTempClassFilter(classValue === '' ? null : classValue);
    };

    // Apply filters when user clicks "Áp dụng"
    const applyFilters = () => {
        // console.log('Applying filters:', tempGraduationYear, tempClassFilter);
        dispatch(setGraduationYearFilter(tempGraduationYear));
        dispatch(setClassFilter(tempClassFilter));
        setIsOpen(false);
    };

    // Clear all filters
    const clearFilters = () => {
        setTempGraduationYear(null);
        setTempClassFilter(null);
        dispatch(setGraduationYearFilter(null));
        dispatch(setClassFilter(null));
    };

    // Reset temp values when opening dropdown
    const handleOpenDropdown = () => {
        setTempGraduationYear(graduationYearFilter);
        setTempClassFilter(classFilter);
        setIsOpen(true);
    };

    // Handle individual filter removal from active tags
    const handleRemoveGraduationYear = () => {
        dispatch(setGraduationYearFilter(null));
    };

    const handleRemoveClass = () => {
        dispatch(setClassFilter(null));
    };

    const hasActiveFilters = graduationYearFilter !== null || classFilter !== null;

    const iconFilter = (
        <div data-svg-wrapper className="relative">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M4.5 7H19.5M7 12H17M10 17H14" stroke="#202325" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
        </div>
    );

    return (
        <div className="relative">
            {/* Filter Toggle Button */}
            <button
                onClick={handleOpenDropdown}
                className={`flex items-center gap-2 px-3 py-2 border rounded-lg text-sm font-medium transition-colors ${
                    hasActiveFilters
                        ? 'bg-sky-50 border-sky-300 text-sky-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
            >
                {iconFilter}
                Lọc
                {hasActiveFilters && (
                    <span className="bg-sky-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {(graduationYearFilter ? 1 : 0) + (classFilter ? 1 : 0)}
                    </span>
                )}
            </button>

            {/* Filter Dropdown */}
            {isOpen && (
                <div className="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="p-4">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-sm font-semibold text-gray-900">Bộ lọc</h3>
                            <button
                                onClick={() => setIsOpen(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <X size={16} />
                            </button>
                        </div>

                        <div className="space-y-4">
                            {/* Graduation Year Filter */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Năm tốt nghiệp
                                </label>
                                <select
                                    value={tempGraduationYear || ''}
                                    onChange={(e) => handleTempGraduationYearChange(e.target.value)}
                                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-sky-500 focus:border-sky-500"
                                >
                                    <option value="">Tất cả năm</option>
                                    {graduationYearOptions.map((year) => (
                                        <option key={year} value={year}>
                                            {year}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Class Filter */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Khối lớp
                                </label>
                                <select
                                    value={tempClassFilter || ''}
                                    onChange={(e) => handleTempClassChange(e.target.value)}
                                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-sky-500 focus:border-sky-500"
                                >
                                    <option value="">Tất cả khối</option>
                                    {classOptions.map((classValue) => (
                                        <option key={classValue} value={classValue}>
                                            Khối {classValue}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        {/* Filter Actions */}
                        <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                            <button
                                onClick={clearFilters}
                                disabled={!hasActiveFilters}
                                className="text-sm text-gray-500 hover:text-gray-700 disabled:text-gray-300 disabled:cursor-not-allowed"
                            >
                                Xóa bộ lọc
                            </button>
                            <button
                                onClick={applyFilters}
                                className="px-4 py-2 bg-sky-600 text-white text-sm font-medium rounded-lg hover:bg-sky-700 transition-colors"
                            >
                                Áp dụng
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Active Filters Display */}
            {hasActiveFilters && (
                <div className="absolute top-full left-0 mt-1 flex flex-wrap gap-2">
                    {graduationYearFilter && (
                        <div className="flex items-center gap-1 px-2 py-1 bg-sky-100 text-sky-800 text-xs rounded-full">
                            <span>Năm: {graduationYearFilter}</span>
                            <button
                                onClick={handleRemoveGraduationYear}
                                className="text-sky-600 hover:text-sky-800"
                            >
                                <X size={12} />
                            </button>
                        </div>
                    )}
                    {classFilter && (
                        <div className="flex items-center gap-1 px-2 py-1 bg-sky-100 text-sky-800 text-xs rounded-full">
                            <span>Khối: {classFilter}</span>
                            <button
                                onClick={handleRemoveClass}
                                className="text-sky-600 hover:text-sky-800"
                            >
                                <X size={12} />
                            </button>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default FilterBar;
