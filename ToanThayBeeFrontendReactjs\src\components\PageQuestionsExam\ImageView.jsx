import { useSelector, useDispatch } from "react-redux";
import { setShowAddImagesModal } from "src/features/addExam/addExamSlice";
import { ImagePlus } from "lucide-react";

const ImageView = () => {
    const { images } = useSelector((state) => state.images);
    const { folder } = useSelector((state) => state.addExam);
    const dispatch = useDispatch();

    return (
        <div className="text-xs grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-3">
            <button
                onClick={() => dispatch(setShowAddImagesModal(true))}
                className="border-dashed border-2 hover:bg-gray-100 border-gray-300 hover:shadow-lg rounded-lg p-8 flex flex-col items-center justify-center gap-2"
            >
                Thêm ảnh
            </button>
            {images?.[folder]?.map((image, index) => (
                <div key={index} className="mb-6 group relative w-full">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hình ảnh {index + 1}:
                    </label>

                    <div
                        className="relative w-full h-48 rounded-lg overflow-hidden border transition-all duration-300 
                   hover:border-sky-400 hover:shadow-lg group cursor-move"
                        draggable
                        onDragStart={(e) => {
                            e.dataTransfer.setData("text/plain", image);
                        }}
                    >
                        <img
                            src={image}
                            alt={`image-${index}`}
                            className="w-full h-full object-contain transition-all duration-300 group-hover:brightness-75"
                        />

                        {/* Icon hiện khi hover */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                            <ImagePlus className="w-8 h-8 text-white bg-sky-500 p-1 rounded-full shadow-lg" />
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
}

export default ImageView;
