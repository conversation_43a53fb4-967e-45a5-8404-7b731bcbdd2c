{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\image\\\\ImageDropZone.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Trash2, Plus } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDropZone = _ref => {\n  _s();\n  let {\n    imageUrl,\n    onImageDrop,\n    onImageRemove\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\");\n    if (draggedImage && onImageDrop) {\n      onImageDrop(draggedImage);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!e.currentTarget.contains(e.relatedTarget)) {\n      setIsDraggingOver(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative rounded-lg w-full p-4 transition-all duration-200 min-h-[60px] flex items-center justify-center\\n                    \".concat(isDraggingOver ? \"border-2 border-dashed border-blue-400 bg-blue-50\" : \"border border-gray-200 hover:border-blue-300 hover:bg-blue-25\"),\n    onDragOver: handleDragOver,\n    onDragEnter: handleDragEnter,\n    onDragLeave: handleDragLeave,\n    onDrop: handleDrop,\n    children: imageUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative group w-fit bg-gray-50 rounded-lg p-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: \"Attached image\",\n        className: \"rounded-md max-h-48 max-w-full object-contain\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n        onClick: onImageRemove,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 text-xs text-gray-500 text-center\",\n        children: \"\\u1EA2nh \\u0111\\xE3 th\\xEAm \\u2022 Click v\\xE0o icon \\u0111\\u1EC3 x\\xF3a\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center justify-center gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Plus, {\n        className: \"w-5 h-5 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: \"Th\\xEAm \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 9\n  }, this);\n};\n_s(ImageDropZone, \"VKW1ZLDpPe+zo2/qAoOWVX5YkVs=\");\n_c = ImageDropZone;\nexport default ImageDropZone;\nvar _c;\n$RefreshReg$(_c, \"ImageDropZone\");", "map": {"version": 3, "names": ["React", "useState", "Trash2", "Plus", "jsxDEV", "_jsxDEV", "ImageDropZone", "_ref", "_s", "imageUrl", "onImageDrop", "onImageRemove", "isDraggingOver", "setIsDraggingOver", "handleDrop", "e", "preventDefault", "stopPropagation", "draggedImage", "dataTransfer", "getData", "handleDragOver", "types", "includes", "handleDragEnter", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "className", "concat", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/image/ImageDropZone.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Trash2, Plus } from \"lucide-react\";\r\n\r\nconst ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\");\r\n        if (draggedImage && onImageDrop) {\r\n            onImageDrop(draggedImage);\r\n        }\r\n    };\r\n\r\n    const handleDragOver = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (e.dataTransfer.types.includes('text/plain')) {\r\n            setIsDraggingOver(true);\r\n        }\r\n    };\r\n\r\n    const handleDragEnter = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (e.dataTransfer.types.includes('text/plain')) {\r\n            setIsDraggingOver(true);\r\n        }\r\n    };\r\n\r\n    const handleDragLeave = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (!e.currentTarget.contains(e.relatedTarget)) {\r\n            setIsDraggingOver(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div\r\n            className={`relative rounded-lg w-full p-4 transition-all duration-200 min-h-[60px] flex items-center justify-center\r\n                    ${isDraggingOver\r\n                    ? \"border-2 border-dashed border-blue-400 bg-blue-50\"\r\n                    : \"border border-gray-200 hover:border-blue-300 hover:bg-blue-25\"\r\n                }`}\r\n            onDragOver={handleDragOver}\r\n            onDragEnter={handleDragEnter}\r\n            onDragLeave={handleDragLeave}\r\n            onDrop={handleDrop}\r\n        >\r\n            {imageUrl ? (\r\n                <div className=\"relative group w-fit bg-gray-50 rounded-lg p-2\">\r\n                    <img\r\n                        src={imageUrl}\r\n                        alt=\"Attached image\"\r\n                        className=\"rounded-md max-h-48 max-w-full object-contain\"\r\n                    />\r\n                    <button\r\n                        className=\"absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\r\n                        onClick={onImageRemove}\r\n                    >\r\n                        <div className=\"bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg\">\r\n                            <Trash2 className=\"w-4 h-4\" />\r\n                        </div>\r\n                    </button>\r\n                    <div className=\"mt-2 text-xs text-gray-500 text-center\">\r\n                        Ảnh đã thêm • Click vào icon để xóa\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <div className=\"flex flex-col items-center justify-center gap-2\">\r\n                    <Plus className=\"w-5 h-5 text-gray-400\" />\r\n                    <p className=\"text-xs text-gray-500\">Thêm ảnh</p>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default ImageDropZone;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,aAAa,GAAGC,IAAA,IAA8C;EAAAC,EAAA;EAAA,IAA7C;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAAJ,IAAA;EAC3D,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMa,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBJ,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMK,YAAY,GAAGH,CAAC,CAACI,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,IAAIF,YAAY,IAAIR,WAAW,EAAE;MAC7BA,WAAW,CAACQ,YAAY,CAAC;IAC7B;EACJ,CAAC;EAED,MAAMG,cAAc,GAAIN,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIF,CAAC,CAACI,YAAY,CAACG,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC7CV,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMW,eAAe,GAAIT,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIF,CAAC,CAACI,YAAY,CAACG,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC7CV,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMY,eAAe,GAAIV,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAI,CAACF,CAAC,CAACW,aAAa,CAACC,QAAQ,CAACZ,CAAC,CAACa,aAAa,CAAC,EAAE;MAC5Cf,iBAAiB,CAAC,KAAK,CAAC;IAC5B;EACJ,CAAC;EAED,oBACIR,OAAA;IACIwB,SAAS,mIAAAC,MAAA,CACClB,cAAc,GACd,mDAAmD,GACnD,+DAA+D,CAClE;IACPmB,UAAU,EAAEV,cAAe;IAC3BW,WAAW,EAAER,eAAgB;IAC7BS,WAAW,EAAER,eAAgB;IAC7BS,MAAM,EAAEpB,UAAW;IAAAqB,QAAA,EAElB1B,QAAQ,gBACLJ,OAAA;MAAKwB,SAAS,EAAC,gDAAgD;MAAAM,QAAA,gBAC3D9B,OAAA;QACI+B,GAAG,EAAE3B,QAAS;QACd4B,GAAG,EAAC,gBAAgB;QACpBR,SAAS,EAAC;MAA+C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACFpC,OAAA;QACIwB,SAAS,EAAC,4FAA4F;QACtGa,OAAO,EAAE/B,aAAc;QAAAwB,QAAA,eAEvB9B,OAAA;UAAKwB,SAAS,EAAC,qEAAqE;UAAAM,QAAA,eAChF9B,OAAA,CAACH,MAAM;YAAC2B,SAAS,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACTpC,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAM,QAAA,EAAC;MAExD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAENpC,OAAA;MAAKwB,SAAS,EAAC,iDAAiD;MAAAM,QAAA,gBAC5D9B,OAAA,CAACF,IAAI;QAAC0B,SAAS,EAAC;MAAuB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CpC,OAAA;QAAGwB,SAAS,EAAC,uBAAuB;QAAAM,QAAA,EAAC;MAAQ;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAjC,EAAA,CA7EKF,aAAa;AAAAqC,EAAA,GAAbrC,aAAa;AA+EnB,eAAeA,aAAa;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}