import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import LoadingSpinner from "../loading/LoadingSpinner";
import { fetchAllCodes, setSortOrder } from "../../features/code/codeSlice";
import ChangeDescriptionCode from "../../components/modal/ChangeDescriptionCode";
import { TotalComponent } from "./TotalComponent";
import LoadingData from "../loading/LoadingData";

const CodeTable = ({ codes }) => {
    const dispatch = useDispatch();
    const { loading } = useSelector(state => state.states);
    const { search } = useSelector(state => state.codes);
    const { page, pageSize, total, sortOrder } = useSelector(state => state.codes.pagination);
    const [id, setId] = useState(null);
    const [isEdit, setIsEdit] = useState(false);
    const [code, setCode] = useState(null);
    const optionTypeCode = [
        { code: "chapter", description: "Ch<PERSON>ơng" },
        { code: "exam type", description: "Loại đề" },
        { code: "year", description: "Năm" },
        { code: "grade", description: "Khối (lớp)" },
        { code: "difficulty", description: "Độ khó" },
        { code: "question type", description: "Loại câu hỏi" },
        { code: "highSchool", description: "Trường" },
        { code: "chapter", description: "Chương" },
        { code: "difficulty", description: "Độ khó" },

    ]
    const typeExists = (type) => optionTypeCode.some(option => option.code === type);

    const handleClickedRow = (code) => {
        // if (typeExists(code.type)) {

        setCode(code);
        setIsEdit(true);
        // }
    }


    return (
        <LoadingData
            loading={loading}
            isNoData={codes.length > 0 ? false : true}
            loadText="Đang tải danh sách mã"
            noDataText="Không có mã nào."
        >
            <div className="flex flex-col gap-4 h-full min-h-0 text-sm">
                {(isEdit && code) && <ChangeDescriptionCode code={code} onClose={() => setIsEdit(false)} />}
                <TotalComponent
                    total={total}
                    page={page}
                    pageSize={pageSize}
                    setSortOrder={() => dispatch(setSortOrder())}
                />
                <div className="flex-grow h-[70vh] overflow-y-auto hide-scrollbar">
                    <table className="w-full border-collapse border border-[#E7E7ED]">
                        <thead className="bg-[#F6FAFD] sticky top-0 z-10">
                            <tr className="border border-[#E7E7ED]">
                                <th className="py-3 w-16">STT</th>
                                <th className="py-3 w-64">Mã</th>
                                <th className="py-3 w-40">Loại</th>
                                <th className="py-3 w-64">Mô tả</th>
                                <th className="py-3 w-30">Thời gian tạo</th>
                                <th className="py-3 w-30">Thời gian cập nhật</th>
                            </tr>
                        </thead>
                        <tbody>
                            {codes.map((code, index) => (
                                <tr key={code.id} className={`border border-[#E7E7ED] cursor-pointer hover:bg-gray-50`}
                                    onClick={() => handleClickedRow(code)}>
                                    <td className="py-3 text-center">{index + 1}</td>
                                    <td className="py-3 text-center">{code.code}</td>
                                    <td className="py-3 text-center">{code.type}</td>
                                    <td className="py-3 text-center">{code.description}</td>
                                    <td className="py-3 text-center">{new Date(code.createdAt).toLocaleDateString()}</td>
                                    <td className="py-3 text-center">{new Date(code.updatedAt).toLocaleDateString()}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </LoadingData>

    );

}


export default CodeTable