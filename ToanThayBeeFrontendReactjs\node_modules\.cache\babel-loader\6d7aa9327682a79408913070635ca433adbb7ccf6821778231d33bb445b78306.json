{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LeftContent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 border-r bg-white min-h-screen border-gray-300 overflow-y-auto p-4\",\n    children: \"Left Content\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = LeftContent;\nexport default LeftContent;\nvar _c;\n$RefreshReg$(_c, \"LeftContent\");", "map": {"version": 3, "names": ["LeftContent", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["export const LeftContent = () => {\r\n    return (\r\n        <div className=\"flex-1 border-r bg-white min-h-screen border-gray-300 overflow-y-auto p-4\">\r\n            Left Content\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default LeftContent;"], "mappings": ";;AAAA,OAAO,MAAMA,WAAW,GAAGA,CAAA,KAAM;EAC7B,oBACIC,OAAA;IAAKC,SAAS,EAAC,2EAA2E;IAAAC,QAAA,EAAC;EAE3F;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAEd,CAAC;AAAAC,EAAA,GANYR,WAAW;AAQxB,eAAeA,WAAW;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}