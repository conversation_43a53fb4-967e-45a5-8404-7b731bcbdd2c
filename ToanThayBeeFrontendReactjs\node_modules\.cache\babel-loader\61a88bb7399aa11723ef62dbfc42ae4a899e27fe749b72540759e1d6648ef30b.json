{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\QuestionView.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { FileText } from \"lucide-react\";\nimport { useEffect, useState, useMemo } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { reorderStatements, reorderQuestions } from \"src/features/questionsExam/questionsExamSlice\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport SortableQuestionItem from \"./SortableQuestionItem\";\nimport QuestionContent from \"./QuestionContent\";\nimport NavigateBar from \"../PageAddExam/NavigateBar\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuestionViewHeader = _ref => {\n  let {\n    title,\n    count,\n    noQuestionText\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" p-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-1 mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-3 h-3 text-gray-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-xs font-semibold text-gray-900\",\n        children: [title, \" (\", count, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this), count === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-4 text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-6 h-6 mx-auto mb-1 opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs\",\n        children: noQuestionText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 9\n  }, this);\n};\n_c = QuestionViewHeader;\nconst QuestionTNView = () => {\n  _s();\n  const {\n    questionsExam\n  } = useSelector(state => state.questionsExam);\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const dispatch = useDispatch();\n  const questionsTN = useMemo(() => questionsExam.filter(q => q.typeOfQuestion === 'TN'), [questionsExam]);\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\n      const oldIndex = questionsExam.findIndex(q => q.id === active.id);\n      const newIndex = questionsExam.findIndex(q => q.id === over.id);\n\n      // Dispatch Redux action\n      dispatch(reorderQuestions({\n        oldIndex,\n        newIndex\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\",\n      count: questionsTN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col gap-4\",\n      children: /*#__PURE__*/_jsxDEV(DndContext, {\n        sensors: sensors,\n        collisionDetection: closestCenter,\n        onDragEnd: handleDragEnd,\n        children: /*#__PURE__*/_jsxDEV(SortableContext, {\n          items: questionsTN.map(q => q.id),\n          strategy: verticalListSortingStrategy,\n          children: questionsTN.map((q, index) => /*#__PURE__*/_jsxDEV(SortableQuestionItem, {\n            question: q,\n            index: index\n          }, q.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(QuestionTNView, \"CX74QX37K7kghu7w8/JTRJ0o2LY=\", false, function () {\n  return [useSelector, useDispatch, useSensors, useSensor, useSensor];\n});\n_c2 = QuestionTNView;\nconst QuestionDSView = () => {\n  _s2();\n  const {\n    questionsExam\n  } = useSelector(state => state.questionsExam);\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const dispatch = useDispatch();\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const questionsDS = useMemo(() => questionsExam.filter(q => q.typeOfQuestion === 'DS'), [questionsExam]);\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\n      const oldIndex = questionsExam.findIndex(q => q.id === active.id);\n      const newIndex = questionsExam.findIndex(q => q.id === over.id);\n\n      // Dispatch Redux action\n      dispatch(reorderQuestions({\n        oldIndex,\n        newIndex\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi \\u0111\\xFAng sai\",\n      count: questionsDS.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi \\u0111\\xFAng sai\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col gap-4\",\n      children: /*#__PURE__*/_jsxDEV(DndContext, {\n        sensors: sensors,\n        collisionDetection: closestCenter,\n        onDragEnd: handleDragEnd,\n        children: /*#__PURE__*/_jsxDEV(SortableContext, {\n          items: questionsDS.map(q => q.id),\n          strategy: verticalListSortingStrategy,\n          children: questionsDS.map((q, index) => /*#__PURE__*/_jsxDEV(SortableQuestionItem, {\n            question: q,\n            index: index\n          }, q.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s2(QuestionDSView, \"k8qh+nVIMCXvW4Y1bQdKMIm4OwU=\", false, function () {\n  return [useSelector, useDispatch, useSensors, useSensor, useSensor];\n});\n_c3 = QuestionDSView;\nconst QuestionTLNView = () => {\n  _s3();\n  const {\n    questionsExam\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const questionsTLN = useMemo(() => questionsExam.filter(q => q.typeOfQuestion === 'TLN'), [questionsExam]);\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\n      const oldIndex = questionsExam.findIndex(q => q.id === active.id);\n      const newIndex = questionsExam.findIndex(q => q.id === over.id);\n\n      // Dispatch Redux action\n      dispatch(reorderQuestions({\n        oldIndex,\n        newIndex\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\",\n      count: questionsTLN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col gap-4\",\n      children: /*#__PURE__*/_jsxDEV(DndContext, {\n        sensors: sensors,\n        collisionDetection: closestCenter,\n        onDragEnd: handleDragEnd,\n        children: /*#__PURE__*/_jsxDEV(SortableContext, {\n          items: questionsTLN.map(q => q.id),\n          strategy: verticalListSortingStrategy,\n          children: questionsTLN.map((q, index) => /*#__PURE__*/_jsxDEV(SortableQuestionItem, {\n            question: q,\n            index: index\n          }, q.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s3(QuestionTLNView, \"4OILeMyDn9d7Ip/HDSemeQ3QjAM=\", false, function () {\n  return [useSelector, useDispatch, useSensors, useSensor, useSensor];\n});\n_c4 = QuestionTLNView;\nconst QuestionView = () => {\n  _s4();\n  const [view, setView] = useState('TN');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(NavigateBar, {\n      list: [{\n        id: 1,\n        name: 'Trắc nghiệm',\n        value: 'TN'\n      }, {\n        id: 2,\n        name: 'Đúng sai',\n        value: 'DS'\n      }, {\n        id: 3,\n        name: 'Trả lời ngắn',\n        value: 'TLN'\n      }],\n      active: view,\n      setActive: setView\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 13\n    }, this), view === 'TN' && /*#__PURE__*/_jsxDEV(QuestionTNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 31\n    }, this), view === 'DS' && /*#__PURE__*/_jsxDEV(QuestionDSView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 31\n    }, this), view === 'TLN' && /*#__PURE__*/_jsxDEV(QuestionTLNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 32\n    }, this)]\n  }, void 0, true);\n};\n_s4(QuestionView, \"zwJDvP/d1ZsVzYJ3/nmE43DyZ8s=\");\n_c5 = QuestionView;\nexport default QuestionView;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"QuestionViewHeader\");\n$RefreshReg$(_c2, \"QuestionTNView\");\n$RefreshReg$(_c3, \"QuestionDSView\");\n$RefreshReg$(_c4, \"QuestionTLNView\");\n$RefreshReg$(_c5, \"QuestionView\");", "map": {"version": 3, "names": ["FileText", "useEffect", "useState", "useMemo", "useSelector", "useDispatch", "setSelectedIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "reorderStatements", "reorderQuestions", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "SortableQuestionItem", "QuestionContent", "NavigateBar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "title", "count", "noQuestionText", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "QuestionTNView", "_s", "questionsExam", "state", "prefixTN", "dispatch", "questionsTN", "filter", "q", "typeOfQuestion", "sensors", "activationConstraint", "distance", "coordinateGetter", "handleDragEnd", "event", "active", "over", "id", "oldIndex", "findIndex", "newIndex", "length", "collisionDetection", "onDragEnd", "items", "map", "strategy", "index", "question", "_c2", "QuestionDSView", "_s2", "prefixDS", "questionsDS", "_c3", "QuestionTLNView", "_s3", "questionsTLN", "_c4", "Question<PERSON>iew", "_s4", "view", "<PERSON><PERSON><PERSON><PERSON>", "list", "name", "value", "setActive", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/QuestionView.jsx"], "sourcesContent": ["import { FileText } from \"lucide-react\";\r\nimport { useEffect, useState, useMemo } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\nimport { reorderStatements, reorderQuestions } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport {\r\n    DndContext,\r\n    closestCenter,\r\n    KeyboardSensor,\r\n    PointerSensor,\r\n    useSensor,\r\n    useSensors,\r\n} from '@dnd-kit/core';\r\nimport {\r\n    SortableContext,\r\n    sortableKeyboardCoordinates,\r\n    verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport SortableQuestionItem from \"./SortableQuestionItem\";\r\nimport QuestionContent from \"./QuestionContent\";\r\nimport NavigateBar from \"../PageAddExam/NavigateBar\";\r\n\r\nconst QuestionViewHeader = ({ title, count, noQuestionText }) => {\r\n    return (\r\n        <div className=\" p-3 \">\r\n            <div className=\"flex items-center gap-1 mb-2\">\r\n                <FileText className=\"w-3 h-3 text-gray-600\" />\r\n                <h4 className=\"text-xs font-semibold text-gray-900\">{title} ({count})</h4>\r\n            </div>\r\n            {count === 0 && (\r\n                <div className=\"text-center py-4 text-gray-500\">\r\n                    <FileText className=\"w-6 h-6 mx-auto mb-1 opacity-50\" />\r\n                    <p className=\"text-xs\">{noQuestionText}</p>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst QuestionTNView = () => {\r\n    const { questionsExam } = useSelector((state) => state.questionsExam);\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const dispatch = useDispatch();\r\n\r\n    const questionsTN = useMemo(\r\n        () => questionsExam.filter(q => q.typeOfQuestion === 'TN'),\r\n        [questionsExam]\r\n    );\r\n\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor, {\r\n            activationConstraint: {\r\n                distance: 8,\r\n            },\r\n        }),\r\n        useSensor(KeyboardSensor, {\r\n            coordinateGetter: sortableKeyboardCoordinates,\r\n        })\r\n    );\r\n\r\n    const handleDragEnd = (event) => {\r\n        const { active, over } = event;\r\n\r\n        if (active.id !== over?.id) {\r\n            // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\r\n            const oldIndex = questionsExam.findIndex(q => q.id === active.id);\r\n            const newIndex = questionsExam.findIndex(q => q.id === over.id);\r\n\r\n            // Dispatch Redux action\r\n            dispatch(reorderQuestions({\r\n                oldIndex,\r\n                newIndex\r\n            }));\r\n        }\r\n    };\r\n\r\n\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trắc nghiệm\" count={questionsTN.length} noQuestionText=\"Chưa có câu hỏi trắc nghiệm\" />\r\n            <div className=\"p-3 flex flex-col gap-4\">\r\n                <DndContext\r\n                    sensors={sensors}\r\n                    collisionDetection={closestCenter}\r\n                    onDragEnd={handleDragEnd}\r\n                >\r\n                    <SortableContext\r\n                        items={questionsTN.map(q => q.id)}\r\n                        strategy={verticalListSortingStrategy}\r\n                    >\r\n                        {questionsTN.map((q, index) => (\r\n                            <SortableQuestionItem\r\n                                key={q.id}\r\n                                question={q}\r\n                                index={index}\r\n                            />\r\n                        ))}\r\n                    </SortableContext>\r\n                </DndContext>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionDSView = () => {\r\n    const { questionsExam } = useSelector((state) => state.questionsExam);\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const dispatch = useDispatch();\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor, {\r\n            activationConstraint: {\r\n                distance: 8,\r\n            },\r\n        }),\r\n        useSensor(KeyboardSensor, {\r\n            coordinateGetter: sortableKeyboardCoordinates,\r\n        })\r\n    );\r\n\r\n    const questionsDS = useMemo(\r\n        () => questionsExam.filter(q => q.typeOfQuestion === 'DS'),\r\n        [questionsExam]\r\n    );\r\n\r\n    const handleDragEnd = (event) => {\r\n        const { active, over } = event;\r\n\r\n        if (active.id !== over?.id) {\r\n            // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\r\n            const oldIndex = questionsExam.findIndex(q => q.id === active.id);\r\n            const newIndex = questionsExam.findIndex(q => q.id === over.id);\r\n\r\n            // Dispatch Redux action\r\n            dispatch(reorderQuestions({\r\n                oldIndex,\r\n                newIndex\r\n            }));\r\n        }\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi đúng sai\" count={questionsDS.length} noQuestionText=\"Chưa có câu hỏi đúng sai\" />\r\n            <div className=\"p-3 flex flex-col gap-4\">\r\n                <DndContext\r\n                    sensors={sensors}\r\n                    collisionDetection={closestCenter}\r\n                    onDragEnd={handleDragEnd}\r\n                >\r\n                    <SortableContext\r\n                        items={questionsDS.map(q => q.id)}\r\n                        strategy={verticalListSortingStrategy}\r\n                    >\r\n                        {questionsDS.map((q, index) => (\r\n                            <SortableQuestionItem\r\n                                key={q.id}\r\n                                question={q}\r\n                                index={index}\r\n                            />\r\n                        ))}\r\n                    </SortableContext>\r\n                </DndContext>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionTLNView = () => {\r\n    const { questionsExam } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor, {\r\n            activationConstraint: {\r\n                distance: 8,\r\n            },\r\n        }),\r\n        useSensor(KeyboardSensor, {\r\n            coordinateGetter: sortableKeyboardCoordinates,\r\n        })\r\n    );\r\n\r\n    const questionsTLN = useMemo(\r\n        () => questionsExam.filter(q => q.typeOfQuestion === 'TLN'),\r\n        [questionsExam]\r\n    );\r\n\r\n    const handleDragEnd = (event) => {\r\n        const { active, over } = event;\r\n\r\n        if (active.id !== over?.id) {\r\n            // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\r\n            const oldIndex = questionsExam.findIndex(q => q.id === active.id);\r\n            const newIndex = questionsExam.findIndex(q => q.id === over.id);\r\n\r\n            // Dispatch Redux action\r\n            dispatch(reorderQuestions({\r\n                oldIndex,\r\n                newIndex\r\n            }));\r\n        }\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trả lời ngắn\" count={questionsTLN.length} noQuestionText=\"Chưa có câu hỏi trả lời ngắn\" />\r\n            <div className=\"p-3 flex flex-col gap-4\">\r\n                <DndContext\r\n                    sensors={sensors}\r\n                    collisionDetection={closestCenter}\r\n                    onDragEnd={handleDragEnd}\r\n                >\r\n                    <SortableContext\r\n                        items={questionsTLN.map(q => q.id)}\r\n                        strategy={verticalListSortingStrategy}\r\n                    >\r\n                        {questionsTLN.map((q, index) => (\r\n                            <SortableQuestionItem\r\n                                key={q.id}\r\n                                question={q}\r\n                                index={index}\r\n                            />\r\n                        ))}\r\n                    </SortableContext>\r\n                </DndContext>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionView = () => {\r\n    const [view, setView] = useState('TN');\r\n    return (\r\n        <>\r\n            <NavigateBar\r\n                list={[{\r\n                    id: 1,\r\n                    name: 'Trắc nghiệm',\r\n                    value: 'TN'\r\n                },\r\n                {\r\n                    id: 2,\r\n                    name: 'Đúng sai',\r\n                    value: 'DS'\r\n                },\r\n                {\r\n                    id: 3,\r\n                    name: 'Trả lời ngắn',\r\n                    value: 'TLN'\r\n                }\r\n                ]}\r\n                active={view}\r\n                setActive={setView}\r\n            />\r\n            {view === 'TN' && <QuestionTNView />}\r\n            {view === 'DS' && <QuestionDSView />}\r\n            {view === 'TLN' && <QuestionTLNView />}\r\n        </>\r\n    )\r\n}\r\n\r\nexport default QuestionView;"], "mappings": ";;;;;AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AACpD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAC9D,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,+CAA+C;AACnG,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAC1B,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,kBAAkB,GAAGC,IAAA,IAAsC;EAAA,IAArC;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAe,CAAC,GAAAH,IAAA;EACxD,oBACIJ,OAAA;IAAKQ,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAClBT,OAAA;MAAKQ,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBACzCT,OAAA,CAACxB,QAAQ;QAACgC,SAAS,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Cb,OAAA;QAAIQ,SAAS,EAAC,qCAAqC;QAAAC,QAAA,GAAEJ,KAAK,EAAC,IAAE,EAACC,KAAK,EAAC,GAAC;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,EACLP,KAAK,KAAK,CAAC,iBACRN,OAAA;MAAKQ,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC3CT,OAAA,CAACxB,QAAQ;QAACgC,SAAS,EAAC;MAAiC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDb,OAAA;QAAGQ,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAEF;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAC,EAAA,GAfKX,kBAAkB;AAiBxB,MAAMY,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAc,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACD,aAAa,CAAC;EACrE,MAAME,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAE9B,MAAMwC,WAAW,GAAG1C,OAAO,CACvB,MAAMsC,aAAa,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,IAAI,CAAC,EAC1D,CAACP,aAAa,CAClB,CAAC;EAED,MAAMQ,OAAO,GAAGjC,UAAU,CACtBD,SAAS,CAACD,aAAa,EAAE;IACrBoC,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACFpC,SAAS,CAACF,cAAc,EAAE;IACtBuC,gBAAgB,EAAElC;EACtB,CAAC,CACL,CAAC;EAED,MAAMmC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB;MACA,MAAMC,QAAQ,GAAGjB,aAAa,CAACkB,SAAS,CAACZ,CAAC,IAAIA,CAAC,CAACU,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACjE,MAAMG,QAAQ,GAAGnB,aAAa,CAACkB,SAAS,CAACZ,CAAC,IAAIA,CAAC,CAACU,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;;MAE/D;MACAb,QAAQ,CAAClC,gBAAgB,CAAC;QACtBgD,QAAQ;QACRE;MACJ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAID,oBACIpC,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACIT,OAAA,CAACG,kBAAkB;MAACE,KAAK,EAAC,uCAAqB;MAACC,KAAK,EAAEe,WAAW,CAACgB,MAAO;MAAC9B,cAAc,EAAC;IAA6B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1Hb,OAAA;MAAKQ,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACpCT,OAAA,CAACb,UAAU;QACPsC,OAAO,EAAEA,OAAQ;QACjBa,kBAAkB,EAAElD,aAAc;QAClCmD,SAAS,EAAEV,aAAc;QAAApB,QAAA,eAEzBT,OAAA,CAACP,eAAe;UACZ+C,KAAK,EAAEnB,WAAW,CAACoB,GAAG,CAAClB,CAAC,IAAIA,CAAC,CAACU,EAAE,CAAE;UAClCS,QAAQ,EAAE/C,2BAA4B;UAAAc,QAAA,EAErCY,WAAW,CAACoB,GAAG,CAAC,CAAClB,CAAC,EAAEoB,KAAK,kBACtB3C,OAAA,CAACJ,oBAAoB;YAEjBgD,QAAQ,EAAErB,CAAE;YACZoB,KAAK,EAAEA;UAAM,GAFRpB,CAAC,CAACU,EAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAG,EAAA,CAhEKD,cAAc;EAAA,QACUnC,WAAW,EAEpBC,WAAW,EAOZW,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAAsD,GAAA,GAhBX9B,cAAc;AAkEpB,MAAM+B,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAE9B;EAAc,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACD,aAAa,CAAC;EACrE,MAAM+B,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM5B,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAM4C,OAAO,GAAGjC,UAAU,CACtBD,SAAS,CAACD,aAAa,EAAE;IACrBoC,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACFpC,SAAS,CAACF,cAAc,EAAE;IACtBuC,gBAAgB,EAAElC;EACtB,CAAC,CACL,CAAC;EAED,MAAMuD,WAAW,GAAGtE,OAAO,CACvB,MAAMsC,aAAa,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,IAAI,CAAC,EAC1D,CAACP,aAAa,CAClB,CAAC;EAED,MAAMY,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB;MACA,MAAMC,QAAQ,GAAGjB,aAAa,CAACkB,SAAS,CAACZ,CAAC,IAAIA,CAAC,CAACU,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACjE,MAAMG,QAAQ,GAAGnB,aAAa,CAACkB,SAAS,CAACZ,CAAC,IAAIA,CAAC,CAACU,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;;MAE/D;MACAb,QAAQ,CAAClC,gBAAgB,CAAC;QACtBgD,QAAQ;QACRE;MACJ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAED,oBACIpC,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACIT,OAAA,CAACG,kBAAkB;MAACE,KAAK,EAAC,kCAAkB;MAACC,KAAK,EAAE2C,WAAW,CAACZ,MAAO;MAAC9B,cAAc,EAAC;IAA0B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpHb,OAAA;MAAKQ,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACpCT,OAAA,CAACb,UAAU;QACPsC,OAAO,EAAEA,OAAQ;QACjBa,kBAAkB,EAAElD,aAAc;QAClCmD,SAAS,EAAEV,aAAc;QAAApB,QAAA,eAEzBT,OAAA,CAACP,eAAe;UACZ+C,KAAK,EAAES,WAAW,CAACR,GAAG,CAAClB,CAAC,IAAIA,CAAC,CAACU,EAAE,CAAE;UAClCS,QAAQ,EAAE/C,2BAA4B;UAAAc,QAAA,EAErCwC,WAAW,CAACR,GAAG,CAAC,CAAClB,CAAC,EAAEoB,KAAK,kBACtB3C,OAAA,CAACJ,oBAAoB;YAEjBgD,QAAQ,EAAErB,CAAE;YACZoB,KAAK,EAAEA;UAAM,GAFRpB,CAAC,CAACU,EAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAkC,GAAA,CA7DKD,cAAc;EAAA,QACUlE,WAAW,EAEpBC,WAAW,EACZW,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAA2D,GAAA,GAVXJ,cAAc;AA+DpB,MAAMK,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAM;IAAEnC;EAAc,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACD,aAAa,CAAC;EACrE,MAAMG,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAM4C,OAAO,GAAGjC,UAAU,CACtBD,SAAS,CAACD,aAAa,EAAE;IACrBoC,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACFpC,SAAS,CAACF,cAAc,EAAE;IACtBuC,gBAAgB,EAAElC;EACtB,CAAC,CACL,CAAC;EAED,MAAM2D,YAAY,GAAG1E,OAAO,CACxB,MAAMsC,aAAa,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,KAAK,CAAC,EAC3D,CAACP,aAAa,CAClB,CAAC;EAED,MAAMY,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB;MACA,MAAMC,QAAQ,GAAGjB,aAAa,CAACkB,SAAS,CAACZ,CAAC,IAAIA,CAAC,CAACU,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACjE,MAAMG,QAAQ,GAAGnB,aAAa,CAACkB,SAAS,CAACZ,CAAC,IAAIA,CAAC,CAACU,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;;MAE/D;MACAb,QAAQ,CAAClC,gBAAgB,CAAC;QACtBgD,QAAQ;QACRE;MACJ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAED,oBACIpC,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACIT,OAAA,CAACG,kBAAkB;MAACE,KAAK,EAAC,6CAAsB;MAACC,KAAK,EAAE+C,YAAY,CAAChB,MAAO;MAAC9B,cAAc,EAAC;IAA8B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7Hb,OAAA;MAAKQ,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACpCT,OAAA,CAACb,UAAU;QACPsC,OAAO,EAAEA,OAAQ;QACjBa,kBAAkB,EAAElD,aAAc;QAClCmD,SAAS,EAAEV,aAAc;QAAApB,QAAA,eAEzBT,OAAA,CAACP,eAAe;UACZ+C,KAAK,EAAEa,YAAY,CAACZ,GAAG,CAAClB,CAAC,IAAIA,CAAC,CAACU,EAAE,CAAE;UACnCS,QAAQ,EAAE/C,2BAA4B;UAAAc,QAAA,EAErC4C,YAAY,CAACZ,GAAG,CAAC,CAAClB,CAAC,EAAEoB,KAAK,kBACvB3C,OAAA,CAACJ,oBAAoB;YAEjBgD,QAAQ,EAAErB,CAAE;YACZoB,KAAK,EAAEA;UAAM,GAFRpB,CAAC,CAACU,EAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAuC,GAAA,CA5DKD,eAAe;EAAA,QACSvE,WAAW,EACpBC,WAAW,EACZW,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAA+D,GAAA,GATXH,eAAe;AA8DrB,MAAMI,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EACtC,oBACIsB,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACIT,OAAA,CAACF,WAAW;MACR6D,IAAI,EAAE,CAAC;QACH1B,EAAE,EAAE,CAAC;QACL2B,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE;MACX,CAAC,EACD;QACI5B,EAAE,EAAE,CAAC;QACL2B,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE;MACX,CAAC,EACD;QACI5B,EAAE,EAAE,CAAC;QACL2B,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE;MACX,CAAC,CACC;MACF9B,MAAM,EAAE0B,IAAK;MACbK,SAAS,EAAEJ;IAAQ;MAAAhD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,EACD4C,IAAI,KAAK,IAAI,iBAAIzD,OAAA,CAACe,cAAc;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACnC4C,IAAI,KAAK,IAAI,iBAAIzD,OAAA,CAAC8C,cAAc;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACnC4C,IAAI,KAAK,KAAK,iBAAIzD,OAAA,CAACmD,eAAe;MAAAzC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACxC,CAAC;AAEX,CAAC;AAAA2C,GAAA,CA7BKD,YAAY;AAAAQ,GAAA,GAAZR,YAAY;AA+BlB,eAAeA,YAAY;AAAC,IAAAzC,EAAA,EAAA+B,GAAA,EAAAK,GAAA,EAAAI,GAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAlD,EAAA;AAAAkD,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}