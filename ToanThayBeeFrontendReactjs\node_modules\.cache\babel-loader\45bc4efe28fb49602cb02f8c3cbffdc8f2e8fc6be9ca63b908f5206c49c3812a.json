{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\ImageDropZone.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDropZone = _ref => {\n  _s();\n  let {\n    imageUrl,\n    onImageDrop,\n    onImageRemove,\n    content\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\n    if (draggedImage && onImageDrop) {\n      onImageDrop(draggedImage);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-2 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative flex items-center justify-start rounded-md p-4 cursor-pointer transition-all\\n                    \".concat(isDraggingOver ? \"border border-dashed border-sky-500 bg-sky-50\" : \"hover:border hover:border-dashed hover:border-sky-300 hover:bg-sky-25\"),\n      onDragOver: e => {\n        e.preventDefault();\n        e.stopPropagation();\n      },\n      onDragEnter: e => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.dataTransfer.types.includes('text/plain')) {\n          setIsDraggingOver(true);\n        }\n      },\n      onDragLeave: e => {\n        e.preventDefault();\n        e.stopPropagation();\n        // Chỉ set false khi rời khỏi container chính\n        if (!e.currentTarget.contains(e.relatedTarget)) {\n          setIsDraggingOver(false);\n        }\n      },\n      onDrop: e => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDraggingOver(false);\n        handleDrop(e);\n      },\n      children: isDraggingOver ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sky-600 text-sm font-medium\",\n          children: \"Th\\u1EA3 \\u1EA3nh v\\xE0o \\u0111\\xE2y\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        text: content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this), imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-fit group/image\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: \"dropped\",\n        className: \"rounded-md max-h-40 object-contain w-full border transition-all group-hover/image:brightness-75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20 rounded-md\",\n        onClick: e => {\n          e.stopPropagation();\n          onImageRemove === null || onImageRemove === void 0 ? void 0 : onImageRemove();\n        },\n        title: \"X\\xF3a \\u1EA3nh\",\n        children: /*#__PURE__*/_jsxDEV(Trash2, {\n          className: \"w-6 h-6 text-white bg-red-500 p-1 rounded-full group-hover/image:opacity-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_s(ImageDropZone, \"VKW1ZLDpPe+zo2/qAoOWVX5YkVs=\");\n_c = ImageDropZone;\nexport default ImageDropZone;\nvar _c;\n$RefreshReg$(_c, \"ImageDropZone\");", "map": {"version": 3, "names": ["React", "useState", "Trash2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ImageDropZone", "_ref", "_s", "imageUrl", "onImageDrop", "onImageRemove", "content", "isDraggingOver", "setIsDraggingOver", "handleDrop", "e", "preventDefault", "draggedImage", "dataTransfer", "getData", "className", "children", "concat", "onDragOver", "stopPropagation", "onDragEnter", "types", "includes", "onDragLeave", "currentTarget", "contains", "relatedTarget", "onDrop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "src", "alt", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/ImageDropZone.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\r\nimport Latex<PERSON>enderer from \"../latex/RenderLatex\";\r\nconst ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove, content }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\r\n        if (draggedImage && onImageDrop) {\r\n            onImageDrop(draggedImage);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-2 w-full\">\r\n            <div\r\n                className={`relative flex items-center justify-start rounded-md p-4 cursor-pointer transition-all\r\n                    ${isDraggingOver ? \"border border-dashed border-sky-500 bg-sky-50\" : \"hover:border hover:border-dashed hover:border-sky-300 hover:bg-sky-25\"}`}\r\n                onDragOver={(e) => {\r\n                    e.preventDefault();\r\n                    e.stopPropagation();\r\n                }}\r\n                onDragEnter={(e) => {\r\n                    e.preventDefault();\r\n                    e.stopPropagation();\r\n                    if (e.dataTransfer.types.includes('text/plain')) {\r\n                        setIsDraggingOver(true);\r\n                    }\r\n                }}\r\n                onDragLeave={(e) => {\r\n                    e.preventDefault();\r\n                    e.stopPropagation();\r\n                    // Chỉ set false khi rời khỏi container chính\r\n                    if (!e.currentTarget.contains(e.relatedTarget)) {\r\n                        setIsDraggingOver(false);\r\n                    }\r\n                }}\r\n                onDrop={(e) => {\r\n                    e.preventDefault();\r\n                    e.stopPropagation();\r\n                    setIsDraggingOver(false);\r\n                    handleDrop(e);\r\n                }}\r\n            >\r\n                {isDraggingOver ? (\r\n                    <div className=\"w-full text-center\">\r\n                        <p className=\"text-sky-600 text-sm font-medium\">Thả ảnh vào đây</p>\r\n                    </div>\r\n                ) : (\r\n                    <LatexRenderer text={content} />\r\n                )}\r\n            </div>\r\n            {imageUrl && (\r\n                <div className=\"relative w-fit group/image\">\r\n                    <img\r\n                        src={imageUrl}\r\n                        alt=\"dropped\"\r\n                        className=\"rounded-md max-h-40 object-contain w-full border transition-all group-hover/image:brightness-75\"\r\n                    />\r\n                    <button\r\n                        className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20 rounded-md\"\r\n                        onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            onImageRemove?.();\r\n                        }}\r\n                        title=\"Xóa ảnh\"\r\n                    >\r\n                        <Trash2 className=\"w-6 h-6 text-white bg-red-500 p-1 rounded-full group-hover/image:opacity-100\" />\r\n                    </button>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ImageDropZone;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,cAAc,CAAC,CAAC;AACvC,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACjD,MAAMC,aAAa,GAAGC,IAAA,IAAuD;EAAAC,EAAA;EAAA,IAAtD;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,aAAa;IAAEC;EAAQ,CAAC,GAAAL,IAAA;EACpE,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMc,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMI,YAAY,GAAGF,CAAC,CAACG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3D,IAAIF,YAAY,IAAIR,WAAW,EAAE;MAC7BA,WAAW,CAACQ,YAAY,CAAC;IAC7B;EACJ,CAAC;EAED,oBACIb,OAAA;IAAKgB,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACvCjB,OAAA;MACIgB,SAAS,gHAAAE,MAAA,CACHV,cAAc,GAAG,+CAA+C,GAAG,uEAAuE,CAAG;MACnJW,UAAU,EAAGR,CAAC,IAAK;QACfA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBD,CAAC,CAACS,eAAe,CAAC,CAAC;MACvB,CAAE;MACFC,WAAW,EAAGV,CAAC,IAAK;QAChBA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBD,CAAC,CAACS,eAAe,CAAC,CAAC;QACnB,IAAIT,CAAC,CAACG,YAAY,CAACQ,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;UAC7Cd,iBAAiB,CAAC,IAAI,CAAC;QAC3B;MACJ,CAAE;MACFe,WAAW,EAAGb,CAAC,IAAK;QAChBA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBD,CAAC,CAACS,eAAe,CAAC,CAAC;QACnB;QACA,IAAI,CAACT,CAAC,CAACc,aAAa,CAACC,QAAQ,CAACf,CAAC,CAACgB,aAAa,CAAC,EAAE;UAC5ClB,iBAAiB,CAAC,KAAK,CAAC;QAC5B;MACJ,CAAE;MACFmB,MAAM,EAAGjB,CAAC,IAAK;QACXA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBD,CAAC,CAACS,eAAe,CAAC,CAAC;QACnBX,iBAAiB,CAAC,KAAK,CAAC;QACxBC,UAAU,CAACC,CAAC,CAAC;MACjB,CAAE;MAAAM,QAAA,EAEDT,cAAc,gBACXR,OAAA;QAAKgB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAC/BjB,OAAA;UAAGgB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,gBAENhC,OAAA,CAACF,aAAa;QAACmC,IAAI,EAAE1B;MAAQ;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAClC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EACL5B,QAAQ,iBACLJ,OAAA;MAAKgB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACvCjB,OAAA;QACIkC,GAAG,EAAE9B,QAAS;QACd+B,GAAG,EAAC,SAAS;QACbnB,SAAS,EAAC;MAAiG;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9G,CAAC,eACFhC,OAAA;QACIgB,SAAS,EAAC,6JAA6J;QACvKoB,OAAO,EAAGzB,CAAC,IAAK;UACZA,CAAC,CAACS,eAAe,CAAC,CAAC;UACnBd,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAG,CAAC;QACrB,CAAE;QACF+B,KAAK,EAAC,iBAAS;QAAApB,QAAA,eAEfjB,OAAA,CAACH,MAAM;UAACmB,SAAS,EAAC;QAA8E;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC7B,EAAA,CAzEIF,aAAa;AAAAqC,EAAA,GAAbrC,aAAa;AA2EnB,eAAeA,aAAa;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}