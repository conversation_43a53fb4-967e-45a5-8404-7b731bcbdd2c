{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\QuestionContent.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionContent = _ref => {\n  _s();\n  var _question$ExamQuestio, _codes$chapter, _codes$chapter$find;\n  let {\n    question,\n    index\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\\n            flex flex-col gap-3 p-3 rounded-lg border transition cursor-pointer\\n            \".concat(selectedIndex === ((_question$ExamQuestio = question.ExamQuestions) === null || _question$ExamQuestio === void 0 ? void 0 : _question$ExamQuestio.order) ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\"),\n    onClick: () => {\n      var _question$ExamQuestio2;\n      return dispatch(setSelectedIndex((_question$ExamQuestio2 = question.ExamQuestions) === null || _question$ExamQuestio2 === void 0 ? void 0 : _question$ExamQuestio2.order));\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      div: true,\n      className: \"flex flex-wrap items-center text-xs text-gray-500 gap-x-4 gap-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-gray-700\",\n        children: [\"C\\xE2u \", index + 1]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\u0110\\u1ED9 kh\\xF3: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: question.difficulty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Ch\\u01B0\\u01A1ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: ((_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === question.chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || \"Chưa xác định\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-base text-gray-800 leading-relaxed\",\n        children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          className: \"text-gray-800 text-xs\",\n          text: question.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 17\n      }, this), question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center w-full h-[10rem] mt-1\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: question.imageUrl,\n          alt: \"question\",\n          className: \"object-contain w-full h-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"text-xs font-bold\",\n        children: \"L\\u1EDDi gi\\u1EA3i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: question.solution,\n        className: \" w-full\",\n        style: {\n          fontSize: '12px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 17\n    }, this)]\n  }, question.id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionContent, \"XZZ2Q+l9zMAy4Vp/xsF8U7mo49M=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = QuestionContent;\nexport default QuestionContent;\nvar _c;\n$RefreshReg$(_c, \"QuestionContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "setSelectedIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "jsxDEV", "_jsxDEV", "QuestionContent", "_ref", "_s", "_question$ExamQuestio", "_codes$chapter", "_codes$chapter$find", "question", "index", "dispatch", "selectedIndex", "state", "questionsExam", "codes", "className", "concat", "ExamQuestions", "order", "onClick", "_question$ExamQuestio2", "children", "div", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "difficulty", "find", "c", "code", "chapter", "description", "text", "content", "imageUrl", "src", "alt", "solution", "style", "fontSize", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/QuestionContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\n\r\nconst QuestionContent = ({ question, index }) => {\r\n    const dispatch = useDispatch();\r\n    const { selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    return (\r\n        <div\r\n            key={question.id}\r\n            className={`\r\n            flex flex-col gap-3 p-3 rounded-lg border transition cursor-pointer\r\n            ${selectedIndex === question.ExamQuestions?.order ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\"}`}\r\n            onClick={() => dispatch(setSelectedIndex(question.ExamQuestions?.order))}\r\n        >\r\n            {/* Thông tin câu hỏi */}\r\n            <div div className=\"flex flex-wrap items-center text-xs text-gray-500 gap-x-4 gap-y-1\" >\r\n                <span className=\"font-medium text-gray-700\">Câu {index + 1}</span>\r\n                <span>Độ khó: <span className=\"text-gray-700\">{question.difficulty}</span></span>\r\n                <span>\r\n                    Chương:{\" \"}\r\n                    <span className=\"text-gray-700\">\r\n                        {codes[\"chapter\"]?.find(c => c.code === question.chapter)?.description || \"Chưa xác định\"}\r\n                    </span>\r\n                </span>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col gap-2\">\r\n                <div className=\"text-base text-gray-800 leading-relaxed\">\r\n                    <LatexRenderer className=\"text-gray-800 text-xs\" text={question.content} />\r\n                </div>\r\n                {question.imageUrl && (\r\n                    <div className=\"flex flex-col items-center justify-center w-full h-[10rem] mt-1\">\r\n                        <img\r\n                            src={question.imageUrl}\r\n                            alt=\"question\"\r\n                            className=\"object-contain w-full h-full\"\r\n                        />\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {question.solution && (\r\n                <div className=\"mt-2\">\r\n                    <h6 className=\"text-xs font-bold\">Lời giải</h6>\r\n                    <MarkdownPreviewWithMath content={question.solution} className=\" w-full\" style={{ fontSize: '12px' }} />\r\n                </div>\r\n            )}\r\n\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default QuestionContent;"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,eAAe,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,cAAA,EAAAC,mBAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAN,IAAA;EACxC,MAAMO,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAc,CAAC,GAAGhB,WAAW,CAAEiB,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACrE,MAAM;IAAEC;EAAM,CAAC,GAAGnB,WAAW,CAAEiB,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,oBACIb,OAAA;IAEIc,SAAS,oGAAAC,MAAA,CAEPL,aAAa,OAAAN,qBAAA,GAAKG,QAAQ,CAACS,aAAa,cAAAZ,qBAAA,uBAAtBA,qBAAA,CAAwBa,KAAK,IAAG,4BAA4B,GAAG,2CAA2C,CAAG;IACjIC,OAAO,EAAEA,CAAA;MAAA,IAAAC,sBAAA;MAAA,OAAMV,QAAQ,CAACb,gBAAgB,EAAAuB,sBAAA,GAACZ,QAAQ,CAACS,aAAa,cAAAG,sBAAA,uBAAtBA,sBAAA,CAAwBF,KAAK,CAAC,CAAC;IAAA,CAAC;IAAAG,QAAA,gBAGzEpB,OAAA;MAAKqB,GAAG;MAACP,SAAS,EAAC,mEAAmE;MAAAM,QAAA,gBAClFpB,OAAA;QAAMc,SAAS,EAAC,2BAA2B;QAAAM,QAAA,GAAC,SAAI,EAACZ,KAAK,GAAG,CAAC;MAAA;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEzB,OAAA;QAAAoB,QAAA,GAAM,uBAAQ,eAAApB,OAAA;UAAMc,SAAS,EAAC,eAAe;UAAAM,QAAA,EAAEb,QAAQ,CAACmB;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjFzB,OAAA;QAAAoB,QAAA,GAAM,mBACK,EAAC,GAAG,eACXpB,OAAA;UAAMc,SAAS,EAAC,eAAe;UAAAM,QAAA,EAC1B,EAAAf,cAAA,GAAAQ,KAAK,CAAC,SAAS,CAAC,cAAAR,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKtB,QAAQ,CAACuB,OAAO,CAAC,cAAAxB,mBAAA,uBAAxDA,mBAAA,CAA0DyB,WAAW,KAAI;QAAe;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENzB,OAAA;MAAKc,SAAS,EAAC,qBAAqB;MAAAM,QAAA,gBAChCpB,OAAA;QAAKc,SAAS,EAAC,yCAAyC;QAAAM,QAAA,eACpDpB,OAAA,CAACH,aAAa;UAACiB,SAAS,EAAC,uBAAuB;UAACkB,IAAI,EAAEzB,QAAQ,CAAC0B;QAAQ;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,EACLlB,QAAQ,CAAC2B,QAAQ,iBACdlC,OAAA;QAAKc,SAAS,EAAC,iEAAiE;QAAAM,QAAA,eAC5EpB,OAAA;UACImC,GAAG,EAAE5B,QAAQ,CAAC2B,QAAS;UACvBE,GAAG,EAAC,UAAU;UACdtB,SAAS,EAAC;QAA8B;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAELlB,QAAQ,CAAC8B,QAAQ,iBACdrC,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAM,QAAA,gBACjBpB,OAAA;QAAIc,SAAS,EAAC,mBAAmB;QAAAM,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/CzB,OAAA,CAACF,uBAAuB;QAACmC,OAAO,EAAE1B,QAAQ,CAAC8B,QAAS;QAACvB,SAAS,EAAC,SAAS;QAACwB,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CACR;EAAA,GAtCIlB,QAAQ,CAACiC,EAAE;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAyCf,CAAC;AAEd,CAAC;AAAAtB,EAAA,CAlDKF,eAAe;EAAA,QACAN,WAAW,EACFD,WAAW,EACnBA,WAAW;AAAA;AAAA+C,EAAA,GAH3BxC,eAAe;AAoDrB,eAAeA,eAAe;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}