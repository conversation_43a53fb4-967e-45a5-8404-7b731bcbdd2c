{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { postExam, setExamData } from \"src/features/addExam/addExamSlice\";\nimport { CheckCircle, Edit, FileText } from \"lucide-react\"; // dùng icon lucide-react\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeaderStep = () => {\n  _s();\n  const {\n    step\n  } = useSelector(state => state.addExam);\n  const stepInfo = [{\n    id: 1,\n    label: \"Thông tin đề thi\",\n    icon: /*#__PURE__*/_jsxDEV(Edit, {\n      className: \"w-5 h-5 mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 51\n    }, this)\n  }, {\n    id: 2,\n    label: \"Soạn câu hỏi\",\n    icon: /*#__PURE__*/_jsxDEV(FileText, {\n      className: \"w-5 h-5 mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 47\n    }, this)\n  }, {\n    id: 3,\n    label: \"Hoàn tất & đăng đề\",\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {\n      className: \"w-5 h-5 mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 53\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3\",\n    children: stepInfo.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center \".concat(step === item.id ? \"text-blue-500\" : \"text-gray-500\"),\n      children: [item.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n        children: item.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 21\n      }, this)]\n    }, item.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 9\n  }, this);\n};\n_s(HeaderStep, \"s5rWcbXEHrIRaJo4DtZfiNxygtM=\", false, function () {\n  return [useSelector];\n});\n_c = HeaderStep;\nexport const LeftContent = () => {\n  _s2();\n  const dispatch = useDispatch();\n  const {\n    examData\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 border-r bg-white h-[calc(100vh-69px)] border-gray-300 overflow-y-auto p-6 shadow-sm\",\n    children: /*#__PURE__*/_jsxDEV(HeaderStep, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 9\n  }, this);\n};\n_s2(LeftContent, \"rLRAN3FRqlrzGY/YdC5L2ZsGJDw=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c2 = LeftContent;\nexport default LeftContent;\nvar _c, _c2;\n$RefreshReg$(_c, \"HeaderStep\");\n$RefreshReg$(_c2, \"LeftContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "postExam", "setExamData", "CheckCircle", "Edit", "FileText", "jsxDEV", "_jsxDEV", "HeaderStep", "_s", "step", "state", "addExam", "stepInfo", "id", "label", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "map", "item", "concat", "_c", "LeftContent", "_s2", "dispatch", "examData", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { postExam, setExamData } from \"src/features/addExam/addExamSlice\";\r\nimport { CheckCircle, Edit, FileText } from \"lucide-react\"; // dùng icon lucide-react\r\n\r\nconst HeaderStep = () => {\r\n    const { step } = useSelector((state) => state.addExam);\r\n\r\n    const stepInfo = [\r\n        { id: 1, label: \"Thông tin đề thi\", icon: <Edit className=\"w-5 h-5 mr-2\" /> },\r\n        { id: 2, label: \"Soạn câu hỏi\", icon: <FileText className=\"w-5 h-5 mr-2\" /> },\r\n        { id: 3, label: \"Hoàn tất & đăng đề\", icon: <CheckCircle className=\"w-5 h-5 mr-2\" /> }\r\n    ];\r\n\r\n    return (\r\n        <div className=\"space-y-3\">\r\n            {stepInfo.map((item) => (\r\n                <div key={item.id} className={`flex items-center ${step === item.id ? \"text-blue-500\" : \"text-gray-500\"}`}>\r\n                    {item.icon}\r\n                    <span>{item.label}</span>\r\n                </div>\r\n            ))}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport const LeftContent = () => {\r\n    const dispatch = useDispatch();\r\n    const { examData } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <div className=\"flex-1 border-r bg-white h-[calc(100vh-69px)] border-gray-300 overflow-y-auto p-6 shadow-sm\">\r\n            <HeaderStep />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default LeftContent;\r\n"], "mappings": ";;;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,WAAW,QAAQ,mCAAmC;AACzE,SAASC,WAAW,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEtD,MAAMC,QAAQ,GAAG,CACb;IAAEC,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,eAAET,OAAA,CAACH,IAAI;MAACa,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC7E;IAAEP,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,eAAET,OAAA,CAACF,QAAQ;MAACY,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC7E;IAAEP,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,eAAET,OAAA,CAACJ,WAAW;MAACc,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACzF;EAED,oBACId,OAAA;IAAKU,SAAS,EAAC,WAAW;IAAAK,QAAA,EACrBT,QAAQ,CAACU,GAAG,CAAEC,IAAI,iBACfjB,OAAA;MAAmBU,SAAS,uBAAAQ,MAAA,CAAuBf,IAAI,KAAKc,IAAI,CAACV,EAAE,GAAG,eAAe,GAAG,eAAe,CAAG;MAAAQ,QAAA,GACrGE,IAAI,CAACR,IAAI,eACVT,OAAA;QAAAe,QAAA,EAAOE,IAAI,CAACT;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA,GAFnBG,IAAI,CAACV,EAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGZ,CACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACZ,EAAA,CAnBID,UAAU;EAAA,QACKT,WAAW;AAAA;AAAA2B,EAAA,GAD1BlB,UAAU;AAqBhB,OAAO,MAAMmB,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B;EAAS,CAAC,GAAG/B,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE1D,oBACIL,OAAA;IAAKU,SAAS,EAAC,6FAA6F;IAAAK,QAAA,eACxGf,OAAA,CAACC,UAAU;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEd,CAAC;AAACO,GAAA,CATWD,WAAW;EAAA,QACH3B,WAAW,EACPD,WAAW;AAAA;AAAAgC,GAAA,GAFvBJ,WAAW;AAWxB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}