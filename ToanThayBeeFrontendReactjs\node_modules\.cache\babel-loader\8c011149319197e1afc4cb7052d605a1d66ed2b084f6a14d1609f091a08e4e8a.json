{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nexport const postExam = createAsyncThunk(\"addExam/postExam\", async (_ref, _ref2) => {\n  let {\n    examData,\n    examImage,\n    questions,\n    questionImages,\n    statementImages,\n    solutionImages,\n    examFile\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, examApi.postExamAPI, {\n    examData,\n    examImage,\n    questions,\n    questionImages,\n    statementImages,\n    solutionImages,\n    examFile\n  }, () => {}, true, false);\n});\nconst addExamSlice = createSlice({\n  name: \"addExam\",\n  initialState: {\n    loading: false,\n    step: 1,\n    examData: null,\n    examImage: null,\n    questions: [],\n    questionImages: [],\n    statementImages: [],\n    solutionImages: [],\n    examFile: null\n  },\n  reducers: {\n    nextStep: state => {\n      state.step++;\n    },\n    prevStep: state => {\n      state.step--;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    resetData: state => {\n      state.examData = null;\n      state.examImage = null;\n      state.questions = [];\n      state.questionImages = [];\n      state.statementImages = [];\n      state.solutionImages = [];\n      state.examFile = null;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(postExam.pending, state => {\n      state.loading = true;\n    }).addCase(postExam.fulfilled, state => {\n      state.loading = false;\n    }).addCase(postExam.rejected, state => {\n      state.loading = false;\n    });\n  }\n});\nexport const {\n  setLoading,\n  resetData,\n  nextStep,\n  prevStep\n} = addExamSlice.actions;\nexport default addExamSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postExam", "_ref", "_ref2", "examData", "examImage", "questions", "questionImages", "statementImages", "solutionImages", "examFile", "dispatch", "postExamAPI", "addExamSlice", "name", "initialState", "loading", "step", "reducers", "nextStep", "state", "prevStep", "setLoading", "action", "payload", "resetData", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/addExam/addExamSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\n\r\nexport const postExam = createAsyncThunk(\r\n    \"addExam/postExam\",\r\n    async ({ examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.postExamAPI, { examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, () => { }, true, false);\r\n    }\r\n);\r\n\r\nconst addExamSlice = createSlice({\r\n    name: \"addExam\",\r\n    initialState: {\r\n        loading: false,\r\n        step: 1,\r\n        examData: null,\r\n        examImage: null,\r\n        questions: [],\r\n        questionImages: [],\r\n        statementImages: [],\r\n        solutionImages: [],\r\n        examFile: null,\r\n    },\r\n    reducers: {\r\n        nextStep: (state) => {\r\n            state.step++;\r\n        },\r\n        prevStep: (state) => {\r\n            state.step--;\r\n        },\r\n        setLoading: (state, action) => {\r\n            state.loading = action.payload;\r\n        },\r\n        resetData: (state) => {\r\n            state.examData = null;\r\n            state.examImage = null;\r\n            state.questions = [];\r\n            state.questionImages = [];\r\n            state.statementImages = [];\r\n            state.solutionImages = [];\r\n            state.examFile = null;\r\n        }\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(postExam.pending, (state) => {\r\n                state.loading = true;\r\n            })\r\n            .addCase(postExam.fulfilled, (state) => {\r\n                state.loading = false;\r\n            })\r\n            .addCase(postExam.rejected, (state) => {\r\n                state.loading = false;\r\n            })\r\n    }\r\n});\r\n\r\nexport const { setLoading, resetData, nextStep, prevStep } = addExamSlice.actions;\r\nexport default addExamSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD;AACA,SAASC,UAAU,QAAQ,wBAAwB;AAEnD,OAAO,MAAMC,QAAQ,GAAGH,gBAAgB,CACpC,kBAAkB,EAClB,OAAAI,IAAA,EAAAC,KAAA,KAAuH;EAAA,IAAhH;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,SAAS;IAAEC,cAAc;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAS,CAAC,GAAAR,IAAA;EAAA,IAAE;IAAES;EAAS,CAAC,GAAAR,KAAA;EAC9G,OAAO,MAAMH,UAAU,CAACW,QAAQ,EAAEZ,OAAO,CAACa,WAAW,EAAE;IAAER,QAAQ;IAAEC,SAAS;IAAEC,SAAS;IAAEC,cAAc;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAS,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACjL,CACJ,CAAC;AAED,MAAMG,YAAY,GAAGhB,WAAW,CAAC;EAC7BiB,IAAI,EAAE,SAAS;EACfC,YAAY,EAAE;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,CAAC;IACPb,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE;EACd,CAAC;EACDQ,QAAQ,EAAE;IACNC,QAAQ,EAAGC,KAAK,IAAK;MACjBA,KAAK,CAACH,IAAI,EAAE;IAChB,CAAC;IACDI,QAAQ,EAAGD,KAAK,IAAK;MACjBA,KAAK,CAACH,IAAI,EAAE;IAChB,CAAC;IACDK,UAAU,EAAEA,CAACF,KAAK,EAAEG,MAAM,KAAK;MAC3BH,KAAK,CAACJ,OAAO,GAAGO,MAAM,CAACC,OAAO;IAClC,CAAC;IACDC,SAAS,EAAGL,KAAK,IAAK;MAClBA,KAAK,CAAChB,QAAQ,GAAG,IAAI;MACrBgB,KAAK,CAACf,SAAS,GAAG,IAAI;MACtBe,KAAK,CAACd,SAAS,GAAG,EAAE;MACpBc,KAAK,CAACb,cAAc,GAAG,EAAE;MACzBa,KAAK,CAACZ,eAAe,GAAG,EAAE;MAC1BY,KAAK,CAACX,cAAc,GAAG,EAAE;MACzBW,KAAK,CAACV,QAAQ,GAAG,IAAI;IACzB;EACJ,CAAC;EACDgB,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC3B,QAAQ,CAAC4B,OAAO,EAAGT,KAAK,IAAK;MAClCA,KAAK,CAACJ,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDY,OAAO,CAAC3B,QAAQ,CAAC6B,SAAS,EAAGV,KAAK,IAAK;MACpCA,KAAK,CAACJ,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDY,OAAO,CAAC3B,QAAQ,CAAC8B,QAAQ,EAAGX,KAAK,IAAK;MACnCA,KAAK,CAACJ,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEM,UAAU;EAAEG,SAAS;EAAEN,QAAQ;EAAEE;AAAS,CAAC,GAAGR,YAAY,CAACmB,OAAO;AACjF,eAAenB,YAAY,CAACoB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}