'use strict';

export default {
  up: async (queryInterface, Sequelize) => {
    // Xo<PERSON> ràng buộc cũ
    await queryInterface.removeConstraint('tuitionPayment', 'tuitionPayment_ibfk_1'); // tên constraint có thể khác

    // Thêm ràng buộc mới với onDelete: CASCADE
    await queryInterface.addConstraint('tuitionPayment', {
      fields: ['userId'],
      type: 'foreign key',
      name: 'tuitionPayment_ibfk_1', // đặt tên giống tên cũ hoặc khác nếu muốn
      references: {
        table: 'user',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Rollback: xoá constraint mới và thêm lại constraint cũ
    await queryInterface.removeConstraint('tuitionPayment', 'tuitionPayment_ibfk_1');

    await queryInterface.addConstraint('tuitionPayment', {
      fields: ['userId'],
      type: 'foreign key',
      name: 'tuitionPayment_ibfk_1',
      references: {
        table: 'user',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE'
    });
  }
};
