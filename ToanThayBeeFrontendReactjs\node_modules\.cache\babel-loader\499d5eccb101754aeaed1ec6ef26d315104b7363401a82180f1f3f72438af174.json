{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\dropMenu\\\\OptionBarAdmin.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from \"react\";\nimport { ChevronDown } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DropMenuBarAdmin = _ref => {\n  _s();\n  let {\n    options,\n    placeholder = \"Chọn một mục\",\n    selectedOption,\n    onChange,\n    className = ''\n  } = _ref;\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef(null);\n  const [p, setP] = useState(placeholder);\n  const handleSelect = option => {\n    onChange(option.code);\n    setP(option.description);\n    setIsOpen(false); // Đóng dropdown sau khi chọn\n  };\n\n  // Update placeholder text when selectedOption or options change\n  useEffect(() => {\n    if ((selectedOption || selectedOption === false) && options && options.length > 0) {\n      const selectedItem = options.find(option => option.code === selectedOption);\n      if (selectedItem) {\n        setP(selectedItem.description);\n      }\n    }\n  }, [selectedOption, options]);\n\n  // Handle click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: dropdownRef,\n    className: \"relative flex-1 \".concat(className ? className : 'w-full', \" text-md\"),\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"button\",\n      className: \"w-full bg-white border flex items-center justify-between border-gray-300 rounded-[0.5rem] py-[0.5rem] px-[0.5rem] focus:outline-none focus:ring-2 focus:ring-sky-500\",\n      onClick: () => setIsOpen(!isOpen),\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-[#303437] font-medium font-['Inter'] \".concat(selectedOption === true ? \"text-green-500\" : selectedOption === false ? \"text-red-500\" : \"\"),\n        children: selectedOption || selectedOption === false ? p : placeholder\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n        size: 12,\n        className: \"transition-transform \".concat(isOpen ? \"rotate-180\" : \"\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-lg shadow-lg z-50 overflow-y-auto hide-scrollbar max-h-60\",\n      children: (options === null || options === void 0 ? void 0 : options.length) > 0 ? options.map((option, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"px-4 py-2 hover:bg-gray-200 cursor-pointer\",\n        onClick: () => handleSelect(option),\n        children: option.description\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 29\n      }, this)) : /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"px-4 py-2 text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 9\n  }, this);\n};\n_s(DropMenuBarAdmin, \"VVynkh7vvh7PiStS3Y2lQ9q1y4o=\");\n_c = DropMenuBarAdmin;\nexport default DropMenuBarAdmin;\nvar _c;\n$RefreshReg$(_c, \"DropMenuBarAdmin\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "ChevronDown", "jsxDEV", "_jsxDEV", "DropMenuBarAdmin", "_ref", "_s", "options", "placeholder", "selectedOption", "onChange", "className", "isOpen", "setIsOpen", "dropdownRef", "p", "setP", "handleSelect", "option", "code", "description", "length", "selectedItem", "find", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "ref", "concat", "children", "type", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "map", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/dropMenu/OptionBarAdmin.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from \"react\";\r\nimport { ChevronDown } from \"lucide-react\";\r\n\r\nconst DropMenuBarAdmin = ({ options, placeholder = \"Chọn một mục\", selectedOption, onChange, className='' }) => {\r\n    const [isOpen, setIsOpen] = useState(false);\r\n    const dropdownRef = useRef(null);\r\n    const [p, setP] = useState(placeholder);\r\n\r\n    const handleSelect = (option) => {\r\n        onChange(option.code);\r\n        setP(option.description);\r\n        setIsOpen(false); // Đóng dropdown sau khi chọn\r\n    };\r\n\r\n    // Update placeholder text when selectedOption or options change\r\n    useEffect(() => {\r\n        if ((selectedOption || selectedOption === false) && options && options.length > 0) {\r\n            const selectedItem = options.find((option) => option.code === selectedOption);\r\n            if (selectedItem) {\r\n                setP(selectedItem.description);\r\n            }\r\n        }\r\n    }, [selectedOption, options]);\r\n\r\n    // <PERSON>le click outside to close dropdown\r\n    useEffect(() => {\r\n        const handleClickOutside = (event) => {\r\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n                setIsOpen(false);\r\n            }\r\n        };\r\n\r\n        document.addEventListener(\"mousedown\", handleClickOutside);\r\n        return () => {\r\n            document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        };\r\n    }, []);\r\n\r\n    return (\r\n        <div ref={dropdownRef} className={`relative flex-1 ${className ? className : 'w-full'} text-md`}>\r\n            {/* Nút chọn */}\r\n            <button\r\n                type=\"button\"\r\n                className=\"w-full bg-white border flex items-center justify-between border-gray-300 rounded-[0.5rem] py-[0.5rem] px-[0.5rem] focus:outline-none focus:ring-2 focus:ring-sky-500\"\r\n                onClick={() => setIsOpen(!isOpen)}\r\n            >\r\n                <span className={`text-[#303437] font-medium font-['Inter'] ${selectedOption === true ? \"text-green-500\" : selectedOption === false ? \"text-red-500\" : \"\"}`}>\r\n                    {(selectedOption || selectedOption === false) ? p : placeholder}\r\n                </span>\r\n                <ChevronDown size={12} className={`transition-transform ${isOpen ? \"rotate-180\" : \"\"}`} />\r\n            </button>\r\n\r\n            {/* Danh sách dropdown */}\r\n            {isOpen && (\r\n                <ul className=\"absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-lg shadow-lg z-50 overflow-y-auto hide-scrollbar max-h-60\">\r\n                    {options?.length > 0 ? (\r\n                        options.map((option, index) => (\r\n                            <li\r\n                                key={index}\r\n                                className=\"px-4 py-2 hover:bg-gray-200 cursor-pointer\"\r\n                                onClick={() => handleSelect(option)}\r\n                            >\r\n                                {option.description}\r\n                            </li>\r\n                        ))\r\n                    ) : (\r\n                        <li className=\"px-4 py-2 text-gray-500\">Không có dữ liệu</li>\r\n                    )}\r\n                </ul>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default DropMenuBarAdmin;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,gBAAgB,GAAGC,IAAA,IAAuF;EAAAC,EAAA;EAAA,IAAtF;IAAEC,OAAO;IAAEC,WAAW,GAAG,cAAc;IAAEC,cAAc;IAAEC,QAAQ;IAAEC,SAAS,GAAC;EAAG,CAAC,GAAAN,IAAA;EACvG,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAMgB,WAAW,GAAGd,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM,CAACe,CAAC,EAAEC,IAAI,CAAC,GAAGlB,QAAQ,CAACU,WAAW,CAAC;EAEvC,MAAMS,YAAY,GAAIC,MAAM,IAAK;IAC7BR,QAAQ,CAACQ,MAAM,CAACC,IAAI,CAAC;IACrBH,IAAI,CAACE,MAAM,CAACE,WAAW,CAAC;IACxBP,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EACtB,CAAC;;EAED;EACAd,SAAS,CAAC,MAAM;IACZ,IAAI,CAACU,cAAc,IAAIA,cAAc,KAAK,KAAK,KAAKF,OAAO,IAAIA,OAAO,CAACc,MAAM,GAAG,CAAC,EAAE;MAC/E,MAAMC,YAAY,GAAGf,OAAO,CAACgB,IAAI,CAAEL,MAAM,IAAKA,MAAM,CAACC,IAAI,KAAKV,cAAc,CAAC;MAC7E,IAAIa,YAAY,EAAE;QACdN,IAAI,CAACM,YAAY,CAACF,WAAW,CAAC;MAClC;IACJ;EACJ,CAAC,EAAE,CAACX,cAAc,EAAEF,OAAO,CAAC,CAAC;;EAE7B;EACAR,SAAS,CAAC,MAAM;IACZ,MAAMyB,kBAAkB,GAAIC,KAAK,IAAK;MAClC,IAAIX,WAAW,CAACY,OAAO,IAAI,CAACZ,WAAW,CAACY,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACpEf,SAAS,CAAC,KAAK,CAAC;MACpB;IACJ,CAAC;IAEDgB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACTK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IACjE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIrB,OAAA;IAAK6B,GAAG,EAAElB,WAAY;IAACH,SAAS,qBAAAsB,MAAA,CAAqBtB,SAAS,GAAGA,SAAS,GAAG,QAAQ,aAAW;IAAAuB,QAAA,gBAE5F/B,OAAA;MACIgC,IAAI,EAAC,QAAQ;MACbxB,SAAS,EAAC,sKAAsK;MAChLyB,OAAO,EAAEA,CAAA,KAAMvB,SAAS,CAAC,CAACD,MAAM,CAAE;MAAAsB,QAAA,gBAElC/B,OAAA;QAAMQ,SAAS,+CAAAsB,MAAA,CAA+CxB,cAAc,KAAK,IAAI,GAAG,gBAAgB,GAAGA,cAAc,KAAK,KAAK,GAAG,cAAc,GAAG,EAAE,CAAG;QAAAyB,QAAA,EACtJzB,cAAc,IAAIA,cAAc,KAAK,KAAK,GAAIM,CAAC,GAAGP;MAAW;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACPrC,OAAA,CAACF,WAAW;QAACwC,IAAI,EAAE,EAAG;QAAC9B,SAAS,0BAAAsB,MAAA,CAA0BrB,MAAM,GAAG,YAAY,GAAG,EAAE;MAAG;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC,EAGR5B,MAAM,iBACHT,OAAA;MAAIQ,SAAS,EAAC,+HAA+H;MAAAuB,QAAA,EACxI,CAAA3B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEc,MAAM,IAAG,CAAC,GAChBd,OAAO,CAACmC,GAAG,CAAC,CAACxB,MAAM,EAAEyB,KAAK,kBACtBxC,OAAA;QAEIQ,SAAS,EAAC,4CAA4C;QACtDyB,OAAO,EAAEA,CAAA,KAAMnB,YAAY,CAACC,MAAM,CAAE;QAAAgB,QAAA,EAEnChB,MAAM,CAACE;MAAW,GAJduB,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKV,CACP,CAAC,gBAEFrC,OAAA;QAAIQ,SAAS,EAAC,yBAAyB;QAAAuB,QAAA,EAAC;MAAgB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAC/D;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAClC,EAAA,CArEIF,gBAAgB;AAAAwC,EAAA,GAAhBxC,gBAAgB;AAuEtB,eAAeA,gBAAgB;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}