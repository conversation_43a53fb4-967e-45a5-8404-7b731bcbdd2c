{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\AiExamDetailAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport { fetchQuestionsByExamId, setModalOpen, setConfirm, commitExam } from \"src/features/examAI/examAISlice\";\nimport Header from \"src/components/PageAIexam/Header\";\nimport LeftContent from \"src/components/PageAIexam/LeftContent\";\nimport RightContent from \"src/components/PageAIexam/RightContent\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\nimport { fetchImages } from \"src/features/image/imageSlice\";\nimport AddImagesModal from \"src/components/modal/AddImagesModal\";\nimport ConfirmModal from \"src/components/modal/ConfirmModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditExamAIPage = () => {\n  _s();\n  const {\n    examId\n  } = useParams();\n  const {\n    exam,\n    modalOpen,\n    isChange,\n    confirm,\n    loadingCommit,\n    showAddImagesModal\n  } = useSelector(state => state.examAI);\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (examId) {\n      dispatch(fetchImages(\"ImageFormN8N\"));\n      dispatch(fetchQuestionsByExamId(examId));\n      dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\n    }\n  }, [examId, dispatch]);\n  useEffect(() => {\n    if (confirm) {\n      dispatch(commitExam(exam.id));\n      setConfirm(false);\n    }\n  }, [confirm]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" bg-gray-50 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \" bg-gray-50 flex flex-col \".concat(closeSidebar ? \"ml-[104px]\" : \"ml-64\"),\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        title: exam === null || exam === void 0 ? void 0 : exam.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-[calc(100vh_-_64px)] bg-gray-50 flex mt-16 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(LeftContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(RightContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this), showAddImagesModal && /*#__PURE__*/_jsxDEV(AddImagesModal, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 36\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: modalOpen,\n      onClose: () => dispatch(setModalOpen(false)),\n      onConfirm: () => dispatch(setConfirm(true)),\n      title: \"X\\xE1c nh\\u1EADn c\\u1EADp nh\\u1EADt\",\n      message: \"\".concat(isChange ? \"Bạn có sự thay đổi, s\" : \"S\", \"au khi c\\u1EADp nh\\u1EADt \\u0111\\u1EC1 thi th\\u1EADt b\\u1EA3n nh\\xE1p n\\xE0y s\\u1EBD m\\u1EA5t b\\u1EA1n ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n c\\u1EADp nh\\u1EADt kh\\xF4ng?\"),\n      loading: loadingCommit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 9\n  }, this);\n};\n_s(EditExamAIPage, \"kfy8Tqj/tbmwkBTiHCEW36GiK7E=\", false, function () {\n  return [useParams, useSelector, useSelector, useDispatch];\n});\n_c = EditExamAIPage;\nexport default EditExamAIPage;\nvar _c;\n$RefreshReg$(_c, \"EditExamAIPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "useParams", "fetchQuestionsByExamId", "setModalOpen", "setConfirm", "commitExam", "Header", "LeftContent", "RightContent", "fetchCodesByType", "AdminSidebar", "fetchImages", "AddImagesModal", "ConfirmModal", "jsxDEV", "_jsxDEV", "EditExamAIPage", "_s", "examId", "exam", "modalOpen", "isChange", "confirm", "loadingCommit", "showAddImagesModal", "state", "examAI", "closeSidebar", "sidebar", "dispatch", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "concat", "title", "name", "isOpen", "onClose", "onConfirm", "message", "loading", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/AiExamDetailAdmin.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { fetchQuestionsByExamId, setModalOpen, setConfirm, commitExam } from \"src/features/examAI/examAISlice\";\r\nimport Header from \"src/components/PageAIexam/Header\";\r\nimport LeftContent from \"src/components/PageAIexam/LeftContent\";\r\nimport RightContent from \"src/components/PageAIexam/RightContent\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\r\nimport { fetchImages } from \"src/features/image/imageSlice\";\r\nimport AddImagesModal from \"src/components/modal/AddImagesModal\";\r\nimport ConfirmModal from \"src/components/modal/ConfirmModal\";\r\n\r\nconst EditExamAIPage = () => {\r\n    const { examId } = useParams();\r\n    const { exam, modalOpen, isChange, confirm, loadingCommit, showAddImagesModal } = useSelector((state) => state.examAI);\r\n    const { closeSidebar } = useSelector((state) => state.sidebar);\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        if (examId) {\r\n            dispatch(fetchImages(\"ImageFormN8N\"));\r\n            dispatch(fetchQuestionsByExamId(examId))\r\n            dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\r\n        }\r\n    }, [examId, dispatch]);\r\n\r\n    useEffect(() => {\r\n        if (confirm) {\r\n            dispatch(commitExam(exam.id));\r\n            setConfirm(false);\r\n        }\r\n    }, [confirm]);\r\n\r\n    return (\r\n        <div className=\" bg-gray-50 flex flex-col\">\r\n            <AdminSidebar />\r\n            <div className={` bg-gray-50 flex flex-col ${closeSidebar ? \"ml-[104px]\" : \"ml-64\"}`}>\r\n                <Header title={exam?.name} />\r\n                <div className=\"h-[calc(100vh_-_64px)] bg-gray-50 flex mt-16 overflow-hidden\">\r\n                    {/* LEFT: Danh sách câu hỏi */}\r\n                    <LeftContent />\r\n\r\n                    {/* RIGHT: Form chỉnh sửa */}\r\n                    <RightContent />\r\n                </div>\r\n            </div>\r\n            {showAddImagesModal && <AddImagesModal />}\r\n            <ConfirmModal\r\n                isOpen={modalOpen}\r\n                onClose={() => dispatch(setModalOpen(false))}\r\n                onConfirm={() => dispatch(setConfirm(true))}\r\n                title=\"Xác nhận cập nhật\"\r\n                message={`${isChange ? \"Bạn có sự thay đổi, s\" : \"S\"}au khi cập nhật đề thi thật bản nháp này sẽ mất bạn chắc chắn muốn cập nhật không?`}\r\n                loading={loadingCommit}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditExamAIPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,sBAAsB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,UAAU,QAAQ,iCAAiC;AAC9G,OAAOC,MAAM,MAAM,kCAAkC;AACrD,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,YAAY,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAO,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAC9B,MAAM;IAAEkB,IAAI;IAAEC,SAAS;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,aAAa;IAAEC;EAAmB,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACtH,MAAM;IAAEC;EAAa,CAAC,GAAG5B,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACG,OAAO,CAAC;EAC9D,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACZ,IAAIoB,MAAM,EAAE;MACRW,QAAQ,CAAClB,WAAW,CAAC,cAAc,CAAC,CAAC;MACrCkB,QAAQ,CAAC3B,sBAAsB,CAACgB,MAAM,CAAC,CAAC;MACxCW,QAAQ,CAACpB,gBAAgB,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;IACvF;EACJ,CAAC,EAAE,CAACS,MAAM,EAAEW,QAAQ,CAAC,CAAC;EAEtB/B,SAAS,CAAC,MAAM;IACZ,IAAIwB,OAAO,EAAE;MACTO,QAAQ,CAACxB,UAAU,CAACc,IAAI,CAACW,EAAE,CAAC,CAAC;MAC7B1B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACkB,OAAO,CAAC,CAAC;EAEb,oBACIP,OAAA;IAAKgB,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACtCjB,OAAA,CAACL,YAAY;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBrB,OAAA;MAAKgB,SAAS,+BAAAM,MAAA,CAA+BV,YAAY,GAAG,YAAY,GAAG,OAAO,CAAG;MAAAK,QAAA,gBACjFjB,OAAA,CAACT,MAAM;QAACgC,KAAK,EAAEnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB;MAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7BrB,OAAA;QAAKgB,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAEzEjB,OAAA,CAACR,WAAW;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGfrB,OAAA,CAACP,YAAY;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACLZ,kBAAkB,iBAAIT,OAAA,CAACH,cAAc;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzCrB,OAAA,CAACF,YAAY;MACT2B,MAAM,EAAEpB,SAAU;MAClBqB,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC1B,YAAY,CAAC,KAAK,CAAC,CAAE;MAC7CuC,SAAS,EAAEA,CAAA,KAAMb,QAAQ,CAACzB,UAAU,CAAC,IAAI,CAAC,CAAE;MAC5CkC,KAAK,EAAC,qCAAmB;MACzBK,OAAO,KAAAN,MAAA,CAAKhB,QAAQ,GAAG,uBAAuB,GAAG,GAAG,sKAAqF;MACzIuB,OAAO,EAAErB;IAAc;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACnB,EAAA,CA7CID,cAAc;EAAA,QACGf,SAAS,EACsDF,WAAW,EACpEA,WAAW,EACnBC,WAAW;AAAA;AAAA6C,EAAA,GAJ1B7B,cAAc;AA+CpB,eAAeA,cAAc;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}