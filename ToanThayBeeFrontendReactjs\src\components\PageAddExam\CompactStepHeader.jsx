// Compact Step Header Component
import { useSelector, useDispatch } from "react-redux";
import { setStep } from "src/features/addExam/addExamSlice";
import { Edit, FileText, CheckCircle, ChevronRight } from "lucide-react";
import { Fragment } from "react";

const CompactStepHeader = () => {
    const { step } = useSelector((state) => state.addExam);
    const dispatch = useDispatch();

    const steps = [
        { id: 1, label: "Thông tin", icon: Edit },
        { id: 2, label: "Câu hỏi", icon: FileText },
        { id: 3, label: "<PERSON>à<PERSON> tất", icon: CheckCircle }
    ];

    return (
        <div className="flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2">
            {steps.map((stepItem, index) => {
                const Icon = stepItem.icon;
                const isActive = step === stepItem.id;
                const isCompleted = step > stepItem.id;

                return (
                    <Fragment key={stepItem.id}>
                        <button
                            onClick={() => dispatch(setStep(stepItem.id))}
                            className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-all ${isActive
                                ? 'bg-blue-100 text-blue-700 font-medium'
                                : isCompleted
                                    ? 'bg-green-100 text-green-700'
                                    : 'text-gray-500 hover:bg-gray-100'
                                }`}
                        >
                            <Icon className="w-3 h-3" />
                            <span>{stepItem.label}</span>
                            {isCompleted && <CheckCircle className="w-3 h-3 text-green-600" />}
                        </button>
                        {index < steps.length - 1 && (
                            <ChevronRight className="w-3 h-3 text-gray-400" />
                        )}
                    </Fragment>
                );
            })}
        </div>
    );
};

export default CompactStepHeader;