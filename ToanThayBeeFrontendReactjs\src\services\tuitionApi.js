import api from "./api";

// Tuition Payment APIs
/**
 * Get all tuition payment records (admin)
 * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)
 * @returns {Promise} - API response
 */
export const getAllTuitionPaymentsAPI = async (params) => {
  const response = await api.get("/v1/admin/tuition-payment", {
    params
  });
  return response;
};

/**
 * Get tuition payment records for a specific class (admin)
 * @param {string} classId - ID of the class
 * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)
 * @returns {Promise} - API response
 */
export const getClassTuitionPaymentsAPI = async (classId, params) => {
  const response = await api.get(`/v1/admin/class/${classId}/tuition-payment`, {
    params
  });
  return response;
};

/**
 * Get tuition payment records for a specific user (admin)
 * @param {string} userId - ID of the user
 * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)
 * @returns {Promise} - API response
 */
export const getUserTuitionPaymentsAdminAPI = async (userId, params) => {  // params = { search, currentPage, limit, sortOrder }
  const response = await api.get(`/v1/admin/user/${userId}/tuition-payment`, {
    params
  });
  return response;
};

/**
 * Get tuition payment records for the current user
 * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)
 * @returns {Promise} - API response
 */
export const getUserTuitionPaymentsAPI = async (params) => {
  const response = await api.get("/v1/user/tuition-payment", {
    params
  });
  return response;
};

/**
 * Get a specific tuition payment record by ID (admin)
 * @param {string} id - ID of the payment record
 * @returns {Promise} - API response
 */
export const getTuitionPaymentByIdAdminAPI = async (id) => {
  const response = await api.get(`/v1/admin/tuition-payment/${id}`);
  return response;
};

/**
 * Get a specific tuition payment record by ID (user)
 * @param {string} id - ID of the payment record
 * @returns {Promise} - API response
 */
export const getTuitionPaymentByIdUserAPI = async (id) => {
  const response = await api.get(`/v1/user/tuition-payment/${id}`);
  return response;
};

/**
 * Create a new tuition payment record
 * @param {Object} paymentData - Payment data to create
 * @returns {Promise} - API response
 */
export const createTuitionPaymentAPI = async (paymentData) => {
  const response = await api.post("/v1/admin/tuition-payment", paymentData);
  return response;
};

/**
 * Create multiple tuition payment records in batch
 * @param {Object} batchData - Batch payment data
 * @returns {Promise} - API response
 */
export const createBatchTuitionPaymentsAPI = async (batchData) => {
  const response = await api.post("/v1/admin/tuition-payment/batch", batchData);
  return response;
};

/**
 * Update a tuition payment record
 * @param {string} id - ID of the payment record to update
 * @param {Object} paymentData - Updated payment data
 * @returns {Promise} - API response
 */
export const updateTuitionPaymentAPI = async ({ id, paymentData }) => {
  const response = await api.put(`/v1/admin/tuition-payment/${id}`, paymentData);
  return response;
};

/**
 * Delete a tuition payment record
 * @param {string} id - ID of the payment record to delete
 * @returns {Promise} - API response
 */
export const deleteTuitionPaymentAPI = async (id) => {
  const response = await api.delete(`/v1/admin/tuition-payment/${id}`);
  return response;
};

/**
 * Get tuition payment statistics
 * @param {Object} params - Query parameters (startMonth, endMonth, userClass)
 * @returns {Promise} - API response with statistics data
 */
export const getTuitionStatisticsAPI = async (params) => {
  const response = await api.get("/v1/admin/tuition-payment/statistics", {
    params
  });
  return response;
};

/**
 * Get tuition summary for a user (admin view)
 * @param {string} userId - User ID
 * @returns {Promise} - API response with user tuition summary
 */
export const getUserTuitionSummaryAdminAPI = async (userId) => {
  const response = await api.get(`/v1/admin/user/${userId}/tuition-summary`);
  return response;
};

/**
 * Get tuition summary for current user
 * @returns {Promise} - API response with user tuition summary
 */
export const getUserTuitionSummaryAPI = async () => {
  const response = await api.get("/v1/user/tuition-summary");
  return response;
};

/**
 * Get class tuitions for a specific month for classes that the current user has joined
 * @param {string} month - Month in YYYY-MM format
 * @returns {Promise} - API response with class tuitions data
 */
export const getStudentClassTuitionsByMonthAPI = async (month) => {
  const response = await api.get(`/v1/user/class-tuition/${month}`);
  return response;
};

/**
 * Admin API to get class tuitions for a specific month for classes that a student has joined
 * @param {string} userId - ID of the student
 * @param {string} month - Month in YYYY-MM format
 * @returns {Promise} - API response with class tuitions data
 */
export const getStudentClassTuitionsByMonthAdminAPI = async ({ userId, month }) => {
  const response = await api.get(`/v1/admin/user/${userId}/class-tuition/${month}`);
  return response;
};

export const checkTuitionPaymentNotPaidApi = async () => {
  const response = await api.get("/v1/user/tuition-payment/not-paid");
  return response;
};