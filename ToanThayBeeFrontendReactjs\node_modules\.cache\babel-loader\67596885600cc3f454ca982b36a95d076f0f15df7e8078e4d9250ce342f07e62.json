{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\modal\\\\ConfirmModal.jsx\";\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfirmModal = _ref => {\n  let {\n    isOpen,\n    onClose,\n    onConfirm,\n    title,\n    message,\n    color = \"red\",\n    loading = false\n  } = _ref;\n  if (!isOpen) return null;\n  const colorClasses = {\n    red: \"bg-red-500 hover:bg-red-600\",\n    blue: \"bg-blue-500 hover:bg-blue-600\",\n    green: \"bg-green-500 hover:bg-green-600\",\n    yellow: \"bg-yellow-500 hover:bg-yellow-600\",\n    gray: \"bg-gray-500 hover:bg-gray-600\"\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl w-full max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500 mb-6\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300\",\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onConfirm,\n            className: \"px-4 py-2 text-white rounded-md\" + \" \".concat(colorClasses[color]),\n            children: loading ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              minHeight: \"min-h-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 40\n            }, this) : \"Xác nhận\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 9\n  }, this);\n};\n_c = ConfirmModal;\nexport default ConfirmModal;\nvar _c;\n$RefreshReg$(_c, \"ConfirmModal\");", "map": {"version": 3, "names": ["LoadingSpinner", "jsxDEV", "_jsxDEV", "ConfirmModal", "_ref", "isOpen", "onClose", "onConfirm", "title", "message", "color", "loading", "colorClasses", "red", "blue", "green", "yellow", "gray", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "concat", "minHeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/modal/ConfirmModal.jsx"], "sourcesContent": ["import LoadingSpinner from \"../loading/LoadingSpinner\";\nconst ConfirmModal = ({ isOpen, onClose, onConfirm, title, message, color = \"red\", loading = false }) => {\n    if (!isOpen) return null;\n\n    const colorClasses = {\n        red: \"bg-red-500 hover:bg-red-600\",\n        blue: \"bg-blue-500 hover:bg-blue-600\",\n        green: \"bg-green-500 hover:bg-green-600\",\n        yellow: \"bg-yellow-500 hover:bg-yellow-600\",\n        gray: \"bg-gray-500 hover:bg-gray-600\",\n    };\n\n    return (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]\">\n            <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\n                <div className=\"p-6\">\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-4\">{title}</h3>\n                    <p className=\"text-sm text-gray-500 mb-6\">{message}</p>\n                    <div className=\"flex justify-end gap-3\">\n                        <button\n                            onClick={onClose}\n                            className=\"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300\"\n                        >\n                            Hủy\n                        </button>\n                        <button\n                            onClick={onConfirm}\n                            className={\"px-4 py-2 text-white rounded-md\" + ` ${colorClasses[color]}`}\n                        >\n                            {loading ? <LoadingSpinner\n                                minHeight=\"min-h-0\"\n                            /> : \"Xác nhận\"}\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default ConfirmModal;\n"], "mappings": ";AAAA,OAAOA,cAAc,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACvD,MAAMC,YAAY,GAAGC,IAAA,IAAoF;EAAA,IAAnF;IAAEC,MAAM;IAAEC,OAAO;IAAEC,SAAS;IAAEC,KAAK;IAAEC,OAAO;IAAEC,KAAK,GAAG,KAAK;IAAEC,OAAO,GAAG;EAAM,CAAC,GAAAP,IAAA;EAChG,IAAI,CAACC,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMO,YAAY,GAAG;IACjBC,GAAG,EAAE,6BAA6B;IAClCC,IAAI,EAAE,+BAA+B;IACrCC,KAAK,EAAE,iCAAiC;IACxCC,MAAM,EAAE,mCAAmC;IAC3CC,IAAI,EAAE;EACV,CAAC;EAED,oBACIf,OAAA;IAAKgB,SAAS,EAAC,8EAA8E;IAAAC,QAAA,eACzFjB,OAAA;MAAKgB,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC1DjB,OAAA;QAAKgB,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAChBjB,OAAA;UAAIgB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAEX;QAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnErB,OAAA;UAAGgB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAEV;QAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDrB,OAAA;UAAKgB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACnCjB,OAAA;YACIsB,OAAO,EAAElB,OAAQ;YACjBY,SAAS,EAAC,kEAAkE;YAAAC,QAAA,EAC/E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrB,OAAA;YACIsB,OAAO,EAAEjB,SAAU;YACnBW,SAAS,EAAE,iCAAiC,OAAAO,MAAA,CAAOb,YAAY,CAACF,KAAK,CAAC,CAAG;YAAAS,QAAA,EAExER,OAAO,gBAAGT,OAAA,CAACF,cAAc;cACtB0B,SAAS,EAAC;YAAS;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,GAAG;UAAU;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACI,EAAA,GArCIxB,YAAY;AAuClB,eAAeA,YAAY;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}