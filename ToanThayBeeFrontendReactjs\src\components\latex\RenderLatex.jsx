import React from "react";
import "katex/dist/katex.min.css";
import { BlockMath, InlineMath } from "react-katex";
import NoTranslate from "../utils/NoTranslate";

const LatexRenderer = ({ text, className = '', style }) => {
    if (text === null || text === undefined) return null;

    // Chuyển \(...\) => $...$, \[...\] => $$...$$
    const formattedText = text
        .replace(/\\\(/g, "$")
        .replace(/\\\)/g, "$")
        .replace(/\\\[/g, "$$")
        .replace(/\\\]/g, "$$");

    // Kiểm tra xem có chứa LaTeX không
    const hasLatex = /\$\$.*?\$\$|\$.*?\$/gs.test(formattedText);

    // Nếu không có LaTeX, tr<PERSON> về text thuần
    if (!hasLatex) {
        return (
            <NoTranslate as="span" className={className} style={style}>
                {text}
            </NoTranslate>
        );
    }

    // C<PERSON>u hình <PERSON>eX để tắt warnings với Unicode
    const katexOptions = {
        strict: false, // Tắt strict mode hoàn toàn
        throwOnError: false,
        errorColor: '#cc0000',
        macros: {
            "\\RR": "\\mathbb{R}",
            "\\NN": "\\mathbb{N}",
            "\\ZZ": "\\mathbb{Z}",
            "\\QQ": "\\mathbb{Q}",
            "\\CC": "\\mathbb{C}"
        }
    };

    // Regex phân chia nội dung theo LaTeX inline/block
    const parts = formattedText.split(/(\$\$.*?\$\$|\$.*?\$)/gs);

    const elements = parts.map((part, index) => {
        try {
            if (part.startsWith("$$") && part.endsWith("$$")) {
                return <BlockMath key={`block-${index}`} settings={katexOptions}>{part.slice(2, -2)}</BlockMath>;
            } else if (part.startsWith("$") && part.endsWith("$")) {
                return <InlineMath key={`inline-${index}`} settings={katexOptions}>{part.slice(1, -1)}</InlineMath>;
            } else {
                return <span key={`text-${index}`}>{part}</span>;
            }
        } catch (error) {
            console.warn("KaTeX render error:", error);
            return <span key={`fallback-${index}`}>{part}</span>; // fallback hiển thị text
        }
    });


    return (
        <NoTranslate as="div" className={className} style={style}>
            {elements}
        </NoTranslate>
    );
};

export default LatexRenderer;
