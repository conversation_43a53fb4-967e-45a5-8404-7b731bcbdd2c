'use strict'

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    // Xo<PERSON> các cột không còn dùng
    await queryInterface.removeColumn('tuitionPayment', 'expectedAmount');
    await queryInterface.removeColumn('tuitionPayment', 'paidAmount');
    await queryInterface.removeColumn('tuitionPayment', 'status');
    await queryInterface.removeColumn('tuitionPayment', 'isCustom');

    // Thêm cột mới isPaid
    await queryInterface.addColumn('tuitionPayment', 'isPaid', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Đã đóng học phí hay chưa'
    });
  },

  async down(queryInterface, Sequelize) {
    // Thêm lại các cột cũ
    await queryInterface.addColumn('tuitionPayment', 'expectedAmount', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      comment: 'Số tiền học phí cần đóng'
    });

    await queryInterface.addColumn('tuitionPayment', 'paidAmount', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      comment: 'Số tiền học phí đã đóng'
    });

    await queryInterface.addColumn('tuitionPayment', 'status', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'UNPAID',
      comment: 'Trạng thái đóng học phí: UNPAID, PARTIAL, PAID'
    });

    await queryInterface.addColumn('tuitionPayment', 'isCustom', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Khoản học phí tạo thủ công hay tự động'
    });

    // Xoá cột mới isPaid
    await queryInterface.removeColumn('tuitionPayment', 'isPaid');
  }
};
