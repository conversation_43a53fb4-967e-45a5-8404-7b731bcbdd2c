import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { apiHandler } from "../../utils/apiHandler";
import * as LessonApi from "../../services/lessonApi";

// ========== THUNKS ==========

// [GET] L<PERSON>y tất cả buổi học từ các lớp mà người dùng đã tham gia
export const fetchUserAttendedLessons = createAsyncThunk(
    "lessons/fetchUserAttendedLessons",
    async (userId, { rejectWithValue }) => {
        try {
            const response = await LessonApi.getUserAttendedLessonsAPI(userId);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || 'Có lỗi xảy ra khi tải danh sách buổi học');
        }
    }
);

// [GET] Lấy chi tiết buổi học theo ID
export const fetchLessonById = createAsyncThunk(
    "lessons/fetchLessonById",
    async (lessonId, { dispatch }) => {
        return await apiHandler(dispatch, LessonApi.getLessonByIdAPI, lessonId, null, true, false);
    }
);

export const findLessons = createAsyncThunk(
    "lessons/findLessons",
    async (search, { dispatch }) => {
        return await apiHandler(dispatch, LessonApi.findLessonsAPI, search, null, false, false, false, false);
    }
);

// [GET] Lấy danh sách buổi học theo classId
export const fetchLessonsByClassId = createAsyncThunk(
    "lessons/fetchLessonsByClassId",
    async (classId, { dispatch }) => {
        return await apiHandler(dispatch, LessonApi.getLessonsByClassIdAPI, classId, null, true, false);
    }
);

// ========== STATE & SLICE ==========

const initialState = {
    // Danh sách buổi học từ các lớp đã tham gia
    userAttendedLessons: {
        list: [],
        totalItems: 0,
        joinedClassesCount: 0,
        loading: false,
        error: null,
        lastFetched: null
    },

    lessonsSearch: [],


    // Chi tiết buổi học
    lessonDetail: {
        data: null,
        loading: false,
        error: null
    },

    // Danh sách buổi học theo lớp
    lessonsByClass: {
        list: [],
        classId: null,
        loading: false,
        error: null
    },

    // Filters và UI state
    filters: {
        searchTerm: '',
        selectedClass: null,
        selectedChapter: null,
        sortBy: 'day', // 'day', 'name', 'createdAt'
        sortOrder: 'desc' // 'asc', 'desc'
    }
};

const lessonSlice = createSlice({
    name: 'lessons',
    initialState,
    reducers: {
        // Reset toàn bộ state
        resetLessonState: () => initialState,

        // Reset user attended lessons
        resetUserAttendedLessons: (state) => {
            state.userAttendedLessons = {
                list: [],
                totalItems: 0,
                joinedClassesCount: 0,
                loading: false,
                error: null,
                lastFetched: null
            };
        },

        // Reset lesson detail
        resetLessonDetail: (state) => {
            state.lessonDetail = {
                data: null,
                loading: false,
                error: null
            };
        },

        // Reset lessons by class
        resetLessonsByClass: (state) => {
            state.lessonsByClass = {
                list: [],
                classId: null,
                loading: false,
                error: null
            };
        },

        // Filters
        setSearchTerm: (state, action) => {
            state.filters.searchTerm = action.payload;
        },

        setSelectedClass: (state, action) => {
            state.filters.selectedClass = action.payload;
        },

        setSelectedChapter: (state, action) => {
            state.filters.selectedChapter = action.payload;
        },

        setSortBy: (state, action) => {
            state.filters.sortBy = action.payload;
        },

        setSortOrder: (state, action) => {
            state.filters.sortOrder = action.payload;
        },

        // Clear filters
        clearFilters: (state) => {
            state.filters = {
                searchTerm: '',
                selectedClass: null,
                selectedChapter: null,
                sortBy: 'day',
                sortOrder: 'desc'
            };
        },

        // Clear errors
        clearUserAttendedLessonsError: (state) => {
            state.userAttendedLessons.error = null;
        },

        clearLessonDetailError: (state) => {
            state.lessonDetail.error = null;
        },

        clearLessonsByClassError: (state) => {
            state.lessonsByClass.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // FETCH USER ATTENDED LESSONS
            .addCase(fetchUserAttendedLessons.pending, (state) => {
                state.userAttendedLessons.loading = true;
                state.userAttendedLessons.error = null;
            })
            .addCase(fetchUserAttendedLessons.fulfilled, (state, action) => {
                state.userAttendedLessons.loading = false;
                state.userAttendedLessons.list = action.payload.data || [];
                state.userAttendedLessons.totalItems = action.payload.totalItems || 0;
                state.userAttendedLessons.joinedClassesCount = action.payload.joinedClassesCount || 0;
                state.userAttendedLessons.lastFetched = new Date().toISOString();
                state.userAttendedLessons.error = null;
            })
            .addCase(fetchUserAttendedLessons.rejected, (state, action) => {
                state.userAttendedLessons.loading = false;
                state.userAttendedLessons.error = action.payload || action.error.message;
            })

            // FETCH LESSON BY ID
            .addCase(fetchLessonById.pending, (state) => {
                state.lessonDetail.loading = true;
                state.lessonDetail.error = null;
            })
            .addCase(fetchLessonById.fulfilled, (state, action) => {
                state.lessonDetail.loading = false;
                state.lessonDetail.data = action.payload;
                state.lessonDetail.error = null;
            })
            .addCase(fetchLessonById.rejected, (state, action) => {
                state.lessonDetail.loading = false;
                state.lessonDetail.error = action.error.message;
            })

            .addCase(findLessons.pending, (state) => {
                state.lessonsSearch = [];
            })
            .addCase(findLessons.fulfilled, (state, action) => {
                if (action.payload) {
                    state.lessonsSearch = action.payload.data;
                }
            })

            // FETCH LESSONS BY CLASS ID
            .addCase(fetchLessonsByClassId.pending, (state) => {
                state.lessonsByClass.loading = true;
                state.lessonsByClass.error = null;
            })
            .addCase(fetchLessonsByClassId.fulfilled, (state, action) => {
                state.lessonsByClass.loading = false;
                state.lessonsByClass.list = action.payload.data || [];
                state.lessonsByClass.classId = action.payload.classId;
                state.lessonsByClass.error = null;
            })
            .addCase(fetchLessonsByClassId.rejected, (state, action) => {
                state.lessonsByClass.loading = false;
                state.lessonsByClass.error = action.error.message;
            });
    }
});

export const {
    resetLessonState,
    resetUserAttendedLessons,
    resetLessonDetail,
    resetLessonsByClass,
    setSearchTerm,
    setSelectedClass,
    setSelectedChapter,
    setSortBy,
    setSortOrder,
    clearFilters,
    clearUserAttendedLessonsError,
    clearLessonDetailError,
    clearLessonsByClassError
} = lessonSlice.actions;

export default lessonSlice.reducer;
