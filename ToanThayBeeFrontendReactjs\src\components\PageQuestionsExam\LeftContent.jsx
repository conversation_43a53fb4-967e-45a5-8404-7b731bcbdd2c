import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import DropMenuBarAdmin from "src/components/dropMenu/OptionBarAdmin";
import SuggestInputBarAdmin from "src/components/input/suggestInputBarAdmin";
import TextArea from "src/components/input/TextArea";
import ImageDropZone from "src/components/image/ImageDropZone";
import SolutionEditor from "src/components/PageAddExam/SolutionEditor";
import { setQuestion } from "src/features/questionsExam/questionsExamSlice";
import { CheckCircle } from "lucide-react";
import { setQuestions, setNewQuestion, addQuestion } from "src/features/questionsExam/questionsExamSlice";

const DetailQuestionView = ({ optionChapter }) => {
    const { questionsExam, selectedId, view } = useSelector((state) => state.questionsExam);
    const { codes } = useSelector((state) => state.codes);
    const dispatch = useDispatch();
    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];
    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];

    const [question, setQuestion] = useState(null);

    useEffect(() => {
        // console.log(selectedId);

        if (selectedId && questionsExam?.length > 0) {
            const selectedQuestion = questionsExam?.find(q => q.id === selectedId);
            setQuestion(selectedQuestion);
        }
    }, [selectedId, questionsExam]);

    const handleQuestionChange = (e, field) => {
        const updatedQuestion = { ...question, [field]: e.target.value };
        dispatch(setQuestions(updatedQuestion));
    };

    const handleStatementChange = (index, value, field) => {
        const updatedStatements = [...question.statements];
        updatedStatements[index] = { ...updatedStatements[index], [field]: value };
        const updatedQuestion = { ...question, statements: updatedStatements };
        dispatch(setQuestions(updatedQuestion));
    };

    const handleSolutionQuestionChange = (value) => {
        const updatedQuestion = { ...question, solution: value };
        dispatch(setQuestions(updatedQuestion));
    };

    return (
        <div className="space-y-3 p-3 w-full">
            {question && (
                <div className="space-y-3 w-full">
                    <div className="flex flex-col gap-2">
                        <h3 className="text-sm font-medium text-gray-900 mb-1">Phân loại</h3>
                        <div className="flex flex-row gap-2">
                            <DropMenuBarAdmin
                                selectedOption={question.class}
                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}
                                options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                                className="text-xs"
                            />
                            <SuggestInputBarAdmin
                                selectedOption={question.chapter}
                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}
                                options={optionChapter}
                                className="text-xs"
                            />
                            <DropMenuBarAdmin
                                selectedOption={question.difficulty}
                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}
                                options={Array.isArray(codes["difficulty"]) ? codes["difficulty"] : []}
                                className="text-xs"
                            />
                        </div>
                    </div>
                    <hr className=" bg-gray-200"></hr>
                    <div className="flex flex-col gap-2">
                        <h3 className="text-sm font-medium text-gray-900 mb-1">Thông tin câu hỏi</h3>
                        <div className="space-y-2">
                            <TextArea
                                value={question.content}
                                onChange={(e) => handleQuestionChange(e, 'content')}
                                placeholder="Nhập nội dung câu hỏi"
                                label="Câu hỏi"
                            />
                            {(view === 'image' || question.imageUrl) && (
                                <ImageDropZone
                                    imageUrl={question.imageUrl}
                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}
                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}
                                />
                            )}
                            {question.typeOfQuestion !== 'TLN' && (
                                <div className="space-y-2">
                                    {question.statements.map((statement, index) => (
                                        <div key={index} className="flex flex-col gap-2 items-center w-full">
                                            <div className="flex flex-row gap-2 items-center w-full">
                                                <p className="text-xs font-bold whitespace-nowrap">
                                                    {question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}
                                                </p>
                                                <TextArea
                                                    value={statement.content}
                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}
                                                    placeholder="Nhập nội dung mệnh đề"
                                                />
                                            </div>
                                            {(view === 'image' || statement.imageUrl) && (
                                                <ImageDropZone
                                                    imageUrl={statement.imageUrl}
                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}
                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}
                                                />
                                            )}

                                        </div>
                                    ))}
                                </div>
                            )}
                            {question.typeOfQuestion === 'TLN' && (
                                <div className="space-y-2">
                                    <TextArea
                                        value={question.correctAnswer}
                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}
                                        placeholder="Nhập đáp án"
                                        label="Đáp án"
                                        Icon={CheckCircle}
                                    />
                                </div>
                            )}
                            {/* <TextArea
                                        value={question.solution}
                                        onChange={(e) => handleQuestionChange(e, 'solution')}
                                        placeholder="Nhập lời giải"
                                        label="Lời giải"
                                        Icon={CheckCircle}
                                    /> */}
                            <SolutionEditor
                                solution={question.solution}
                                onSolutionChange={handleSolutionQuestionChange}
                            />
                        </div>
                    </div>

                </div>
            )}
        </div>
    )
}

const AddQuestionView = ({ optionChapter }) => {
    const dispatch = useDispatch();
    const { newQuestion } = useSelector((state) => state.questionsExam);
    const { codes } = useSelector((state) => state.codes);
    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];
    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];

    const handleNewQuestionChange = (e, field) => {
        const updatedQuestion = { ...newQuestion, [field]: e.target.value };
        dispatch(setNewQuestion(updatedQuestion));
    };

    const handleNewStatementChange = (index, value, field) => {
        const updatedStatements = [...newQuestion.statements];
        updatedStatements[index] = { ...updatedStatements[index], [field]: value };
        const updatedQuestion = { ...newQuestion, statements: updatedStatements };
        dispatch(setNewQuestion(updatedQuestion));
    };

    const handleNewSolutionQuestionChange = (value) => {
        const updatedQuestion = { ...newQuestion, solution: value };
        dispatch(setNewQuestion(updatedQuestion));
    };

    return (
        <div className="space-y-3 p-3 w-full">
            <div className="flex flex-col gap-2">
                <h3 className="text-sm font-medium text-gray-900 mb-1">Phân loại</h3>
                <div className="flex flex-row gap-2">
                    <DropMenuBarAdmin
                        selectedOption={newQuestion.typeOfQuestion}
                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'typeOfQuestion')}
                        options={[{ code: "TN", description: "Trắc nghiệm" }, { code: "DS", description: "Đúng sai" }, { code: "TLN", description: "Trả lời ngắn" }]}
                        className="text-xs"
                    />
                    <DropMenuBarAdmin
                        selectedOption={newQuestion.class}
                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'class')}
                        options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                        className="text-xs"
                    />
                    <SuggestInputBarAdmin
                        selectedOption={newQuestion.chapter}
                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'chapter')}
                        options={optionChapter}
                        className="text-xs"
                    />
                    <DropMenuBarAdmin
                        selectedOption={newQuestion.difficulty}
                        onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'difficulty')}
                        options={Array.isArray(codes["difficulty"]) ? codes["difficulty"] : []}
                        className="text-xs"
                    />
                </div>
                <hr className=" bg-gray-200"></hr>
                <div className="flex flex-col gap-2">
                    <h3 className="text-sm font-medium text-gray-900 mb-1">Thông tin câu hỏi</h3>
                    <div className="space-y-2">
                        <TextArea
                            value={newQuestion.content}
                            onChange={(e) => handleNewQuestionChange(e, 'content')}
                            placeholder="Nhập nội dung câu hỏi"
                            label="Câu hỏi"
                        />
                        <ImageDropZone
                            imageUrl={newQuestion.imageUrl}
                            onImageDrop={(image) => handleNewQuestionChange({ target: { value: image } }, 'imageUrl')}
                            onImageRemove={() => handleNewQuestionChange({ target: { value: '' } }, 'imageUrl')}
                        />
                        {newQuestion.typeOfQuestion !== 'TLN' && (
                            <div className="space-y-2">
                                {newQuestion.statements.map((statement, index) => (
                                    <div key={index} className="flex flex-col gap-2 items-center w-full">
                                        <div className="flex flex-row gap-2 items-center w-full">
                                            <p className="text-xs font-bold whitespace-nowrap">
                                                {newQuestion.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}
                                            </p>
                                            <TextArea
                                                value={statement.content}
                                                onChange={(e) => handleNewStatementChange(index, e.target.value, 'content')}
                                                placeholder="Nhập nội dung mệnh đề"
                                            />
                                        </div>
                                        <ImageDropZone
                                            imageUrl={statement.imageUrl}
                                            onImageDrop={(image) => handleNewStatementChange(index, image, 'imageUrl')}
                                            onImageRemove={() => handleNewStatementChange(index, '', 'imageUrl')}
                                        />
                                    </div>
                                ))}
                            </div>
                        )}
                        {newQuestion.typeOfQuestion === 'TLN' && (
                            <div className="space-y-2">
                                <TextArea
                                    value={newQuestion.correctAnswer}
                                    onChange={(e) => handleNewQuestionChange(e, 'correctAnswer')}
                                    placeholder="Nhập đáp án"
                                    label="Đáp án"
                                    Icon={CheckCircle}
                                />
                            </div>
                        )}
                    </div>
                </div>
                <div className="space-y-2">
                    <SolutionEditor
                        solution={newQuestion.solution}
                        onSolutionChange={handleNewSolutionQuestionChange}
                    />
                </div>
            </div>
        </div>
    )
}


const LeftContent = () => {
    const [view, setView] = useState('questionDetail');
    const { question } = useSelector((state) => state.questionsExam);
    const { codes } = useSelector((state) => state.codes);
    const [optionChapter, setOptionChapter] = useState([]);
    const dispatch = useDispatch();

    useEffect(() => {
        if (Array.isArray(codes["chapter"])) {
            if (question?.class && question?.class.trim() !== "") {
                setOptionChapter(
                    codes["chapter"].filter((code) => code.code.startsWith(question?.class) && code.code.length === 5)
                );
            } else {
                setOptionChapter(codes["chapter"].filter((code) => code.code.length === 5));
            }
        } else {
            setOptionChapter([]);
        }
    }, [codes, question?.class]);

    return (
        <div className="flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto">
            {/* Compact Preview Header */}
            <div className="flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10">
                <div className="flex items-center gap-2">
                    <h2 className="text-xs font-semibold text-gray-900">Chi tiết câu hỏi</h2>
                </div>
                <div className="flex items-center gap-2">
                    {
                        view === 'questionDetail' ? (
                            <button
                                onClick={() => setView('addQuestion')}
                                className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white`}
                            >
                                Thêm câu hỏi
                            </button>
                        ) : (
                            <>
                                <button
                                    onClick={() => {
                                        dispatch(addQuestion())
                                        setView('questionDetail')
                                    }}
                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white`}
                                >
                                    Lưu
                                </button>
                                <button
                                    onClick={() => setView('questionDetail')}
                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white`}
                                >
                                    Hủy
                                </button>

                            </>
                        )
                    }

                </div>
            </div>
            {view === 'questionDetail' && <DetailQuestionView optionChapter={optionChapter} />}
            {view === 'addQuestion' && <AddQuestionView optionChapter={optionChapter} />}
        </div>
    )
}


export default LeftContent;