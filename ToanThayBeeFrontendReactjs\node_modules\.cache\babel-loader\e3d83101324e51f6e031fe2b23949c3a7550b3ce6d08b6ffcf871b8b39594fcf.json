{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\SolutionEditor.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { ImagePlus, Upload } from \"lucide-react\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SolutionEditor = _ref => {\n  _s();\n  let {\n    solution,\n    onSolutionChange,\n    isAddImage\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const textareaRef = useRef(null);\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\");\n    if (!draggedImage || !onSolutionChange) return;\n\n    // Lấy vị trí cursor từ textarea\n    let insertPosition = 0;\n    if (textareaRef.current) {\n      // Lấy vị trí cursor hiện tại trong textarea\n      const cursorPos = textareaRef.current.selectionStart;\n      insertPosition = cursorPos !== undefined ? cursorPos : solution ? solution.length : 0;\n    } else {\n      // Fallback: chèn vào cuối\n      insertPosition = solution ? solution.length : 0;\n    }\n\n    // Tạo markdown image syntax\n    const imageMarkdown = \"\\n![\\u1EA2nh](\".concat(draggedImage, \")\\n\");\n\n    // Chèn vào vị trí cursor\n    const newSolution = solution ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition) : imageMarkdown;\n    onSolutionChange(newSolution);\n\n    // Đặt lại cursor sau khi chèn ảnh\n    setTimeout(() => {\n      if (textareaRef.current) {\n        const newCursorPos = insertPosition + imageMarkdown.length;\n        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);\n        textareaRef.current.focus();\n      }\n    }, 0);\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (isAddImage && e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!e.currentTarget.contains(e.relatedTarget)) {\n      setIsDraggingOver(false);\n      // setDragPosition(null);\n    }\n  };\n  const handleTextareaChange = e => {\n    onSolutionChange(e.target.value);\n  };\n  if (!isAddImage) {\n    // Chế độ xem bình thường\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"L\\u1EDDi gi\\u1EA3i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this), \" \", /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-sm font-semibold text-green-700\",\n      children: \"L\\u1EDDi gi\\u1EA3i:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative border rounded-lg transition-all duration-200 \".concat(isDraggingOver ? \"border-2 border-dashed border-blue-400 bg-blue-50\" : \"border-gray-300 hover:border-blue-300\"),\n      onDragOver: handleDragOver,\n      onDragEnter: handleDragEnter,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n        ref: textareaRef,\n        value: solution || '',\n        onChange: handleTextareaChange,\n        placeholder: \"Nh\\u1EADp l\\u1EDDi gi\\u1EA3i (h\\u1ED7 tr\\u1EE3 Markdown v\\xE0 LaTeX)...\",\n        className: \"w-full p-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px]\",\n        style: {\n          lineHeight: '24px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 17\n      }, this), !solution && !isDraggingOver && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none\",\n        children: [/*#__PURE__*/_jsxDEV(ImagePlus, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"K\\xE9o \\u1EA3nh v\\xE0o \\u0111\\u1EC3 ch\\xE8n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this), solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-3 bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 mb-2\",\n        children: \"Xem tr\\u01B0\\u1EDBc:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 9\n  }, this);\n};\n_s(SolutionEditor, \"jrKPzES6ojhforPNO/47cd9Twdk=\");\n_c = SolutionEditor;\nexport default SolutionEditor;\nvar _c;\n$RefreshReg$(_c, \"SolutionEditor\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "ImagePlus", "Upload", "MarkdownPreviewWithMath", "jsxDEV", "_jsxDEV", "SolutionEditor", "_ref", "_s", "solution", "onSolutionChange", "isAddImage", "isDraggingOver", "setIsDraggingOver", "textareaRef", "handleDrop", "e", "preventDefault", "stopPropagation", "draggedImage", "dataTransfer", "getData", "insertPosition", "current", "cursorPos", "selectionStart", "undefined", "length", "imageMarkdown", "concat", "newSolution", "slice", "setTimeout", "newCursorPos", "setSelectionRange", "focus", "handleDragOver", "handleDragEnter", "types", "includes", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "handleTextareaChange", "target", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "ref", "onChange", "placeholder", "style", "lineHeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/SolutionEditor.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { ImagePlus, Upload } from \"lucide-react\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\n\r\nconst SolutionEditor = ({ solution, onSolutionChange, isAddImage }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n    const textareaRef = useRef(null);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\");\r\n        if (!draggedImage || !onSolutionChange) return;\r\n\r\n        // Lấy vị trí cursor từ textarea\r\n        let insertPosition = 0;\r\n\r\n        if (textareaRef.current) {\r\n            // Lấy vị trí cursor hiện tại trong textarea\r\n            const cursorPos = textareaRef.current.selectionStart;\r\n            insertPosition = cursorPos !== undefined ? cursorPos : (solution ? solution.length : 0);\r\n        } else {\r\n            // Fallback: chèn vào cuối\r\n            insertPosition = solution ? solution.length : 0;\r\n        }\r\n\r\n        // Tạo markdown image syntax\r\n        const imageMarkdown = `\\n![Ảnh](${draggedImage})\\n`;\r\n\r\n        // Chèn vào vị trí cursor\r\n        const newSolution = solution\r\n            ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition)\r\n            : imageMarkdown;\r\n\r\n        onSolutionChange(newSolution);\r\n\r\n        // Đặt lại cursor sau khi chèn ảnh\r\n        setTimeout(() => {\r\n            if (textareaRef.current) {\r\n                const newCursorPos = insertPosition + imageMarkdown.length;\r\n                textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);\r\n                textareaRef.current.focus();\r\n            }\r\n        }, 0);\r\n    };\r\n\r\n    const handleDragOver = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n    };\r\n\r\n    const handleDragEnter = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (isAddImage && e.dataTransfer.types.includes('text/plain')) {\r\n            setIsDraggingOver(true);\r\n        }\r\n    };\r\n\r\n    const handleDragLeave = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (!e.currentTarget.contains(e.relatedTarget)) {\r\n            setIsDraggingOver(false);\r\n            // setDragPosition(null);\r\n        }\r\n    };\r\n\r\n    const handleTextareaChange = (e) => {\r\n        onSolutionChange(e.target.value);\r\n    };\r\n\r\n\r\n\r\n    if (!isAddImage) {\r\n        // Chế độ xem bình thường\r\n        return (\r\n            <div className=\"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\">\r\n                <span className=\"font-semibold\">Lời giải:</span>{\" \"}\r\n                <MarkdownPreviewWithMath content={solution} />\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"mt-2 space-y-3\">\r\n            <div className=\"text-sm font-semibold text-green-700\">Lời giải:</div>\r\n            {/* Editor với drop zone */}\r\n            <div\r\n                className={`relative border rounded-lg transition-all duration-200 ${isDraggingOver\r\n                        ? \"border-2 border-dashed border-blue-400 bg-blue-50\"\r\n                        : \"border-gray-300 hover:border-blue-300\"\r\n                    }`}\r\n                onDragOver={handleDragOver}\r\n                onDragEnter={handleDragEnter}\r\n                onDragLeave={handleDragLeave}\r\n                onDrop={handleDrop}\r\n            >\r\n                {/* Textarea */}\r\n                <textarea\r\n                    ref={textareaRef}\r\n                    value={solution || ''}\r\n                    onChange={handleTextareaChange}\r\n                    placeholder=\"Nhập lời giải (hỗ trợ Markdown và LaTeX)...\"\r\n                    className=\"w-full p-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px]\"\r\n                    style={{ lineHeight: '24px' }}\r\n                />\r\n\r\n                {/* Hint */}\r\n                {!solution && !isDraggingOver && (\r\n                    <div className=\"absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none\">\r\n                        <ImagePlus className=\"w-4 h-4\" />\r\n                        <span>Kéo ảnh vào để chèn</span>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Preview */}\r\n            {solution && (\r\n                <div className=\"border border-gray-200 rounded-lg p-3 bg-gray-50\">\r\n                    <div className=\"text-xs text-gray-500 mb-2\">Xem trước:</div>\r\n                    <MarkdownPreviewWithMath content={solution} />\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default SolutionEditor;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,MAAM,QAAQ,cAAc;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,cAAc,GAAGC,IAAA,IAAgD;EAAAC,EAAA;EAAA,IAA/C;IAAEC,QAAQ;IAAEC,gBAAgB;IAAEC;EAAW,CAAC,GAAAJ,IAAA;EAC9D,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMgB,WAAW,GAAGf,MAAM,CAAC,IAAI,CAAC;EAEhC,MAAMgB,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBL,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMM,YAAY,GAAGH,CAAC,CAACI,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,IAAI,CAACF,YAAY,IAAI,CAACT,gBAAgB,EAAE;;IAExC;IACA,IAAIY,cAAc,GAAG,CAAC;IAEtB,IAAIR,WAAW,CAACS,OAAO,EAAE;MACrB;MACA,MAAMC,SAAS,GAAGV,WAAW,CAACS,OAAO,CAACE,cAAc;MACpDH,cAAc,GAAGE,SAAS,KAAKE,SAAS,GAAGF,SAAS,GAAIf,QAAQ,GAAGA,QAAQ,CAACkB,MAAM,GAAG,CAAE;IAC3F,CAAC,MAAM;MACH;MACAL,cAAc,GAAGb,QAAQ,GAAGA,QAAQ,CAACkB,MAAM,GAAG,CAAC;IACnD;;IAEA;IACA,MAAMC,aAAa,oBAAAC,MAAA,CAAeV,YAAY,QAAK;;IAEnD;IACA,MAAMW,WAAW,GAAGrB,QAAQ,GACtBA,QAAQ,CAACsB,KAAK,CAAC,CAAC,EAAET,cAAc,CAAC,GAAGM,aAAa,GAAGnB,QAAQ,CAACsB,KAAK,CAACT,cAAc,CAAC,GAClFM,aAAa;IAEnBlB,gBAAgB,CAACoB,WAAW,CAAC;;IAE7B;IACAE,UAAU,CAAC,MAAM;MACb,IAAIlB,WAAW,CAACS,OAAO,EAAE;QACrB,MAAMU,YAAY,GAAGX,cAAc,GAAGM,aAAa,CAACD,MAAM;QAC1Db,WAAW,CAACS,OAAO,CAACW,iBAAiB,CAACD,YAAY,EAAEA,YAAY,CAAC;QACjEnB,WAAW,CAACS,OAAO,CAACY,KAAK,CAAC,CAAC;MAC/B;IACJ,CAAC,EAAE,CAAC,CAAC;EACT,CAAC;EAED,MAAMC,cAAc,GAAIpB,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAMmB,eAAe,GAAIrB,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIP,UAAU,IAAIK,CAAC,CAACI,YAAY,CAACkB,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC3D1B,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAM2B,eAAe,GAAIxB,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAI,CAACF,CAAC,CAACyB,aAAa,CAACC,QAAQ,CAAC1B,CAAC,CAAC2B,aAAa,CAAC,EAAE;MAC5C9B,iBAAiB,CAAC,KAAK,CAAC;MACxB;IACJ;EACJ,CAAC;EAED,MAAM+B,oBAAoB,GAAI5B,CAAC,IAAK;IAChCN,gBAAgB,CAACM,CAAC,CAAC6B,MAAM,CAACC,KAAK,CAAC;EACpC,CAAC;EAID,IAAI,CAACnC,UAAU,EAAE;IACb;IACA,oBACIN,OAAA;MAAK0C,SAAS,EAAC,uFAAuF;MAAAC,QAAA,gBAClG3C,OAAA;QAAM0C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAG,eACpD/C,OAAA,CAACF,uBAAuB;QAACkD,OAAO,EAAE5C;MAAS;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEd;EAEA,oBACI/C,OAAA;IAAK0C,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3B3C,OAAA;MAAK0C,SAAS,EAAC,sCAAsC;MAAAC,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAErE/C,OAAA;MACI0C,SAAS,4DAAAlB,MAAA,CAA4DjB,cAAc,GACzE,mDAAmD,GACnD,uCAAuC,CAC1C;MACP0C,UAAU,EAAElB,cAAe;MAC3BmB,WAAW,EAAElB,eAAgB;MAC7BmB,WAAW,EAAEhB,eAAgB;MAC7BiB,MAAM,EAAE1C,UAAW;MAAAiC,QAAA,gBAGnB3C,OAAA;QACIqD,GAAG,EAAE5C,WAAY;QACjBgC,KAAK,EAAErC,QAAQ,IAAI,EAAG;QACtBkD,QAAQ,EAAEf,oBAAqB;QAC/BgB,WAAW,EAAC,yEAA6C;QACzDb,SAAS,EAAC,8GAA8G;QACxHc,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAO;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAGD,CAAC3C,QAAQ,IAAI,CAACG,cAAc,iBACzBP,OAAA;QAAK0C,SAAS,EAAC,6FAA6F;QAAAC,QAAA,gBACxG3C,OAAA,CAACJ,SAAS;UAAC8C,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjC/C,OAAA;UAAA2C,QAAA,EAAM;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGL3C,QAAQ,iBACLJ,OAAA;MAAK0C,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC7D3C,OAAA;QAAK0C,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5D/C,OAAA,CAACF,uBAAuB;QAACkD,OAAO,EAAE5C;MAAS;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC5C,EAAA,CA5HIF,cAAc;AAAAyD,EAAA,GAAdzD,cAAc;AA8HpB,eAAeA,cAAc;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}