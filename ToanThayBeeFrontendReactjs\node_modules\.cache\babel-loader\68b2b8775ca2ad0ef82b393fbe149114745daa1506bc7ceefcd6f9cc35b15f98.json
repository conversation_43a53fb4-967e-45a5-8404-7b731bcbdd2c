{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\ImageView.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setShowAddImagesModal } from \"src/features/addExam/addExamSlice\";\nimport { ImagePlus } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageView = () => {\n  _s();\n  var _images$folder;\n  const {\n    images\n  } = useSelector(state => state.images);\n  const {\n    folder\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-xs grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => dispatch(setShowAddImagesModal(true)),\n      className: \"border-dashed border-2 hover:bg-gray-100 border-gray-300 hover:shadow-lg rounded-lg p-8 flex flex-col items-center justify-center gap-2\",\n      children: \"Th\\xEAm \\u1EA3nh\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this), images === null || images === void 0 ? void 0 : (_images$folder = images[folder]) === null || _images$folder === void 0 ? void 0 : _images$folder.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 group relative w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: [\"H\\xECnh \\u1EA3nh \", index + 1, \":\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative w-full h-48 rounded-lg overflow-hidden border transition-all duration-300 \\r hover:border-sky-400 hover:shadow-lg group cursor-move\",\n        draggable: true,\n        onDragStart: e => {\n          e.dataTransfer.setData(\"text/plain\", image);\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: image,\n          alt: \"image-\".concat(index),\n          className: \"w-full h-full object-contain transition-all duration-300 group-hover:brightness-75\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(ImagePlus, {\n            className: \"w-8 h-8 text-white bg-sky-500 p-1 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 21\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 17\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 9\n  }, this);\n};\n_s(ImageView, \"Pmjg4fgXkLCx9z/NxyHwysJT9Lc=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = ImageView;\nexport default ImageView;\nvar _c;\n$RefreshReg$(_c, \"ImageView\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "setShowAddImagesModal", "ImagePlus", "jsxDEV", "_jsxDEV", "ImageView", "_s", "_images$folder", "images", "state", "folder", "addExam", "dispatch", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "image", "index", "draggable", "onDragStart", "e", "dataTransfer", "setData", "src", "alt", "concat", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/ImageView.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { setShowAddImagesModal } from \"src/features/addExam/addExamSlice\";\r\nimport { ImagePlus } from \"lucide-react\";\r\n\r\nconst ImageView = () => {\r\n    const { images } = useSelector((state) => state.images);\r\n    const { folder } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n\r\n    return (\r\n        <div className=\"text-xs grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-3\">\r\n            <button\r\n                onClick={() => dispatch(setShowAddImagesModal(true))}\r\n                className=\"border-dashed border-2 hover:bg-gray-100 border-gray-300 hover:shadow-lg rounded-lg p-8 flex flex-col items-center justify-center gap-2\"\r\n            >\r\n                Thêm ảnh\r\n            </button>\r\n            {images?.[folder]?.map((image, index) => (\r\n                <div key={index} className=\"mb-6 group relative w-full\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Hình ảnh {index + 1}:\r\n                    </label>\r\n\r\n                    <div\r\n                        className=\"relative w-full h-48 rounded-lg overflow-hidden border transition-all duration-300 \r\n                   hover:border-sky-400 hover:shadow-lg group cursor-move\"\r\n                        draggable\r\n                        onDragStart={(e) => {\r\n                            e.dataTransfer.setData(\"text/plain\", image);\r\n                        }}\r\n                    >\r\n                        <img\r\n                            src={image}\r\n                            alt={`image-${index}`}\r\n                            className=\"w-full h-full object-contain transition-all duration-300 group-hover:brightness-75\"\r\n                        />\r\n\r\n                        {/* Icon hiện khi hover */}\r\n                        <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\r\n                            <ImagePlus className=\"w-8 h-8 text-white bg-sky-500 p-1 rounded-full shadow-lg\" />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            ))}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default ImageView;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,cAAA;EACpB,MAAM;IAAEC;EAAO,CAAC,GAAGT,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACD,MAAM,CAAC;EACvD,MAAM;IAAEE;EAAO,CAAC,GAAGX,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACE,OAAO,CAAC;EACxD,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,oBACII,OAAA;IAAKS,SAAS,EAAC,iFAAiF;IAAAC,QAAA,gBAC5FV,OAAA;MACIW,OAAO,EAAEA,CAAA,KAAMH,QAAQ,CAACX,qBAAqB,CAAC,IAAI,CAAC,CAAE;MACrDY,SAAS,EAAC,yIAAyI;MAAAC,QAAA,EACtJ;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EACRX,MAAM,aAANA,MAAM,wBAAAD,cAAA,GAANC,MAAM,CAAGE,MAAM,CAAC,cAAAH,cAAA,uBAAhBA,cAAA,CAAkBa,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAChClB,OAAA;MAAiBS,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACnDV,OAAA;QAAOS,SAAS,EAAC,8CAA8C;QAAAC,QAAA,GAAC,mBACnD,EAACQ,KAAK,GAAG,CAAC,EAAC,GACxB;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERf,OAAA;QACIS,SAAS,EAAC,8IACwC;QAClDU,SAAS;QACTC,WAAW,EAAGC,CAAC,IAAK;UAChBA,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEN,KAAK,CAAC;QAC/C,CAAE;QAAAP,QAAA,gBAEFV,OAAA;UACIwB,GAAG,EAAEP,KAAM;UACXQ,GAAG,WAAAC,MAAA,CAAWR,KAAK,CAAG;UACtBT,SAAS,EAAC;QAAoF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC,eAGFf,OAAA;UAAKS,SAAS,EAAC,iHAAiH;UAAAC,QAAA,eAC5HV,OAAA,CAACF,SAAS;YAACW,SAAS,EAAC;UAA0D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,GAvBAG,KAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwBV,CACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAAb,EAAA,CA1CKD,SAAS;EAAA,QACQN,WAAW,EACXA,WAAW,EACbC,WAAW;AAAA;AAAA+B,EAAA,GAH1B1B,SAAS;AA4Cf,eAAeA,SAAS;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}