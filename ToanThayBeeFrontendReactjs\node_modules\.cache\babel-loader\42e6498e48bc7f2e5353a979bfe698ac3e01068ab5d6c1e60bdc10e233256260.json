{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\latex\\\\RenderLatex.jsx\";\nimport React from \"react\";\nimport \"katex/dist/katex.min.css\";\nimport { BlockMath, InlineMath } from \"react-katex\";\nimport NoTranslate from \"../utils/NoTranslate\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LatexRenderer = _ref => {\n  let {\n    text,\n    className = '',\n    style\n  } = _ref;\n  if (text === null || text === undefined) return null;\n\n  // Chuyển \\(...\\) => $...$, \\[...\\] => $$...$$\n  const formattedText = text.replace(/\\\\\\(/g, \"$\").replace(/\\\\\\)/g, \"$\").replace(/\\\\\\[/g, \"$$\").replace(/\\\\\\]/g, \"$$\");\n\n  // Kiểm tra xem có chứa LaTeX không\n  const hasLatex = /\\$\\$.*?\\$\\$|\\$.*?\\$/gs.test(formattedText);\n\n  // Nếu không có LaTeX, trả về text thuần\n  if (!hasLatex) {\n    return /*#__PURE__*/_jsxDEV(NoTranslate, {\n      as: \"span\",\n      className: className,\n      style: style,\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Cấu hình KaTeX để tắt warnings với Unicode\n  const katexOptions = {\n    strict: false,\n    // Tắt strict mode hoàn toàn\n    throwOnError: false,\n    errorColor: '#cc0000',\n    macros: {\n      \"\\\\RR\": \"\\\\mathbb{R}\",\n      \"\\\\NN\": \"\\\\mathbb{N}\",\n      \"\\\\ZZ\": \"\\\\mathbb{Z}\",\n      \"\\\\QQ\": \"\\\\mathbb{Q}\",\n      \"\\\\CC\": \"\\\\mathbb{C}\"\n    }\n  };\n\n  // Regex phân chia nội dung theo LaTeX inline/block\n  const parts = formattedText.split(/(\\$\\$.*?\\$\\$|\\$.*?\\$)/gs);\n  const elements = parts.map((part, index) => {\n    try {\n      // Backup warn\n      const originalWarn = console.warn;\n      console.warn = () => {};\n      if (part.startsWith(\"$$\") && part.endsWith(\"$$\")) {\n        const node = /*#__PURE__*/_jsxDEV(BlockMath, {\n          settings: katexOptions,\n          children: part.slice(2, -2)\n        }, \"block-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 30\n        }, this);\n        console.warn = originalWarn; // Restore\n        return node;\n      } else if (part.startsWith(\"$\") && part.endsWith(\"$\")) {\n        const node = /*#__PURE__*/_jsxDEV(InlineMath, {\n          settings: katexOptions,\n          children: part.slice(1, -1)\n        }, \"inline-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 30\n        }, this);\n        console.warn = originalWarn; // Restore\n        return node;\n      } else {\n        console.warn = originalWarn; // Restore\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          children: part\n        }, \"text-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 24\n        }, this);\n      }\n    } catch (error) {\n      console.warn(\"KaTeX render error:\", error);\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        children: part\n      }, \"fallback-\".concat(index), false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 20\n      }, this);\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(NoTranslate, {\n    as: \"div\",\n    className: className,\n    style: style,\n    children: elements\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 9\n  }, this);\n};\n_c = LatexRenderer;\nexport default LatexRenderer;\nvar _c;\n$RefreshReg$(_c, \"LatexRenderer\");", "map": {"version": 3, "names": ["React", "BlockMath", "InlineMath", "NoTranslate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "text", "className", "style", "undefined", "formattedText", "replace", "hasLatex", "test", "as", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "katexOptions", "strict", "throwOnError", "errorColor", "macros", "parts", "split", "elements", "map", "part", "index", "originalWarn", "console", "warn", "startsWith", "endsWith", "node", "settings", "slice", "concat", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/latex/RenderLatex.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport \"katex/dist/katex.min.css\";\r\nimport { BlockMath, InlineMath } from \"react-katex\";\r\nimport NoTranslate from \"../utils/NoTranslate\";\r\n\r\nconst LatexRenderer = ({ text, className = '', style }) => {\r\n    if (text === null || text === undefined) return null;\r\n\r\n    // Chuyển \\(...\\) => $...$, \\[...\\] => $$...$$\r\n    const formattedText = text\r\n        .replace(/\\\\\\(/g, \"$\")\r\n        .replace(/\\\\\\)/g, \"$\")\r\n        .replace(/\\\\\\[/g, \"$$\")\r\n        .replace(/\\\\\\]/g, \"$$\");\r\n\r\n    // Kiểm tra xem có chứa LaTeX không\r\n    const hasLatex = /\\$\\$.*?\\$\\$|\\$.*?\\$/gs.test(formattedText);\r\n\r\n    // Nếu không có LaTeX, tr<PERSON> về text thuần\r\n    if (!hasLatex) {\r\n        return (\r\n            <NoTranslate as=\"span\" className={className} style={style}>\r\n                {text}\r\n            </NoTranslate>\r\n        );\r\n    }\r\n\r\n    // C<PERSON>u hình <PERSON>eX để tắt warnings với Unicode\r\n    const katexOptions = {\r\n        strict: false, // Tắt strict mode hoàn toàn\r\n        throwOnError: false,\r\n        errorColor: '#cc0000',\r\n        macros: {\r\n            \"\\\\RR\": \"\\\\mathbb{R}\",\r\n            \"\\\\NN\": \"\\\\mathbb{N}\",\r\n            \"\\\\ZZ\": \"\\\\mathbb{Z}\",\r\n            \"\\\\QQ\": \"\\\\mathbb{Q}\",\r\n            \"\\\\CC\": \"\\\\mathbb{C}\"\r\n        }\r\n    };\r\n\r\n    // Regex phân chia nội dung theo LaTeX inline/block\r\n    const parts = formattedText.split(/(\\$\\$.*?\\$\\$|\\$.*?\\$)/gs);\r\n\r\n    const elements = parts.map((part, index) => {\r\n        try {\r\n            // Backup warn\r\n            const originalWarn = console.warn;\r\n            console.warn = () => { };\r\n\r\n            if (part.startsWith(\"$$\") && part.endsWith(\"$$\")) {\r\n                const node = <BlockMath key={`block-${index}`} settings={katexOptions}>{part.slice(2, -2)}</BlockMath>;\r\n                console.warn = originalWarn; // Restore\r\n                return node;\r\n            } else if (part.startsWith(\"$\") && part.endsWith(\"$\")) {\r\n                const node = <InlineMath key={`inline-${index}`} settings={katexOptions}>{part.slice(1, -1)}</InlineMath>;\r\n                console.warn = originalWarn; // Restore\r\n                return node;\r\n            } else {\r\n                console.warn = originalWarn; // Restore\r\n                return <span key={`text-${index}`}>{part}</span>;\r\n            }\r\n        } catch (error) {\r\n            console.warn(\"KaTeX render error:\", error);\r\n            return <span key={`fallback-${index}`}>{part}</span>;\r\n        }\r\n    });\r\n\r\n\r\n    return (\r\n        <NoTranslate as=\"div\" className={className} style={style}>\r\n            {elements}\r\n        </NoTranslate>\r\n    );\r\n};\r\n\r\nexport default LatexRenderer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,SAASC,SAAS,EAAEC,UAAU,QAAQ,aAAa;AACnD,OAAOC,WAAW,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,aAAa,GAAGC,IAAA,IAAqC;EAAA,IAApC;IAAEC,IAAI;IAAEC,SAAS,GAAG,EAAE;IAAEC;EAAM,CAAC,GAAAH,IAAA;EAClD,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKG,SAAS,EAAE,OAAO,IAAI;;EAEpD;EACA,MAAMC,aAAa,GAAGJ,IAAI,CACrBK,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;;EAE3B;EACA,MAAMC,QAAQ,GAAG,uBAAuB,CAACC,IAAI,CAACH,aAAa,CAAC;;EAE5D;EACA,IAAI,CAACE,QAAQ,EAAE;IACX,oBACIT,OAAA,CAACF,WAAW;MAACa,EAAE,EAAC,MAAM;MAACP,SAAS,EAAEA,SAAU;MAACC,KAAK,EAAEA,KAAM;MAAAO,QAAA,EACrDT;IAAI;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEtB;;EAEA;EACA,MAAMC,YAAY,GAAG;IACjBC,MAAM,EAAE,KAAK;IAAE;IACfC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,SAAS;IACrBC,MAAM,EAAE;MACJ,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE;IACZ;EACJ,CAAC;;EAED;EACA,MAAMC,KAAK,GAAGf,aAAa,CAACgB,KAAK,CAAC,yBAAyB,CAAC;EAE5D,MAAMC,QAAQ,GAAGF,KAAK,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IACxC,IAAI;MACA;MACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,IAAI;MACjCD,OAAO,CAACC,IAAI,GAAG,MAAM,CAAE,CAAC;MAExB,IAAIJ,IAAI,CAACK,UAAU,CAAC,IAAI,CAAC,IAAIL,IAAI,CAACM,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9C,MAAMC,IAAI,gBAAGjC,OAAA,CAACJ,SAAS;UAAwBsC,QAAQ,EAAEjB,YAAa;UAAAL,QAAA,EAAEc,IAAI,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAAC,YAAAC,MAAA,CAAnDT,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA0D,CAAC;QACtGa,OAAO,CAACC,IAAI,GAAGF,YAAY,CAAC,CAAC;QAC7B,OAAOK,IAAI;MACf,CAAC,MAAM,IAAIP,IAAI,CAACK,UAAU,CAAC,GAAG,CAAC,IAAIL,IAAI,CAACM,QAAQ,CAAC,GAAG,CAAC,EAAE;QACnD,MAAMC,IAAI,gBAAGjC,OAAA,CAACH,UAAU;UAAyBqC,QAAQ,EAAEjB,YAAa;UAAAL,QAAA,EAAEc,IAAI,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAAC,aAAAC,MAAA,CAAnDT,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA2D,CAAC;QACzGa,OAAO,CAACC,IAAI,GAAGF,YAAY,CAAC,CAAC;QAC7B,OAAOK,IAAI;MACf,CAAC,MAAM;QACHJ,OAAO,CAACC,IAAI,GAAGF,YAAY,CAAC,CAAC;QAC7B,oBAAO5B,OAAA;UAAAY,QAAA,EAA6Bc;QAAI,WAAAU,MAAA,CAAdT,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MACpD;IACJ,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACZR,OAAO,CAACC,IAAI,CAAC,qBAAqB,EAAEO,KAAK,CAAC;MAC1C,oBAAOrC,OAAA;QAAAY,QAAA,EAAiCc;MAAI,eAAAU,MAAA,CAAdT,KAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC;IACxD;EACJ,CAAC,CAAC;EAGF,oBACIhB,OAAA,CAACF,WAAW;IAACa,EAAE,EAAC,KAAK;IAACP,SAAS,EAAEA,SAAU;IAACC,KAAK,EAAEA,KAAM;IAAAO,QAAA,EACpDY;EAAQ;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEtB,CAAC;AAACsB,EAAA,GArEIrC,aAAa;AAuEnB,eAAeA,aAAa;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}