{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\SortableStatementsContainer.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch } from \"react-redux\";\nimport { reorderStatements } from \"src/features/examAI/examAISlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableStatementsContainer = _ref => {\n  _s();\n  let {\n    question\n  } = _ref;\n  const dispatch = useDispatch();\n  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n  const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      const oldIndex = question.statement1s.findIndex((item, idx) => \"\".concat(item.id || idx) === active.id);\n      const newIndex = question.statement1s.findIndex((item, idx) => \"\".concat(item.id || idx) === over.id);\n      if (oldIndex !== -1 && newIndex !== -1) {\n        dispatch(reorderStatements({\n          questionId: question.id,\n          oldIndex,\n          newIndex\n        }));\n      }\n    }\n  };\n  const getPrefix = index => {\n    if (question.typeOfQuestion === \"TN\") {\n      return prefixTN[index] || \"\".concat(String.fromCharCode(65 + index), \".\");\n    } else if (question.typeOfQuestion === \"DS\") {\n      return prefixDS[index] || \"\".concat(String.fromCharCode(97 + index), \")\");\n    }\n    return \"\".concat(index + 1, \".\");\n  };\n  if (question.typeOfQuestion === \"TLN\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-800\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"\\u0110\\xE1p \\xE1n: \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: question.correctAnswer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this);\n  }\n  if (!question.statement1s || question.statement1s.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(DndContext, {\n    sensors: sensors,\n    collisionDetection: closestCenter,\n    onDragEnd: handleDragEnd,\n    children: /*#__PURE__*/_jsxDEV(SortableContext, {\n      items: question.statement1s.map((item, idx) => \"\".concat(item.id || idx)),\n      strategy: verticalListSortingStrategy,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: question.statement1s.map((item, idx) => /*#__PURE__*/_jsxDEV(SortableStatementItem, {\n          statement: item,\n          index: idx,\n          prefix: getPrefix(idx),\n          isCorrect: item.isCorrect,\n          questionType: question.typeOfQuestion\n        }, \"\".concat(item.id || idx), false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableStatementsContainer, \"fKWeCsq/OsPww/eQUkThDyU1Mpg=\", false, function () {\n  return [useDispatch, useSensors, useSensor, useSensor];\n});\n_c = SortableStatementsContainer;\nexport default SortableStatementsContainer;\nvar _c;\n$RefreshReg$(_c, \"SortableStatementsContainer\");", "map": {"version": 3, "names": ["React", "useDispatch", "reorderStatements", "SortableStatementItem", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "jsxDEV", "_jsxDEV", "SortableStatementsContainer", "_ref", "_s", "question", "dispatch", "sensors", "coordinateGetter", "prefixTN", "prefixDS", "handleDragEnd", "event", "active", "over", "id", "oldIndex", "statement1s", "findIndex", "item", "idx", "concat", "newIndex", "questionId", "getPrefix", "index", "typeOfQuestion", "String", "fromCharCode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "length", "collisionDetection", "onDragEnd", "items", "map", "strategy", "statement", "prefix", "isCorrect", "questionType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/SortableStatementsContainer.jsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch } from \"react-redux\";\nimport { reorderStatements } from \"src/features/examAI/examAISlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport {\n    DndContext,\n    closestCenter,\n    KeyboardSensor,\n    PointerSensor,\n    useSensor,\n    useSensors,\n} from '@dnd-kit/core';\nimport {\n    SortableContext,\n    sortableKeyboardCoordinates,\n    verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\n\nconst SortableStatementsContainer = ({ question }) => {\n    const dispatch = useDispatch();\n    \n    const sensors = useSensors(\n        useSensor(PointerSensor),\n        useSensor(KeyboardSensor, {\n            coordinateGetter: sortableKeyboardCoordinates,\n        })\n    );\n\n    const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n    const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n\n    const handleDragEnd = (event) => {\n        const { active, over } = event;\n\n        if (active.id !== over?.id) {\n            const oldIndex = question.statement1s.findIndex((item, idx) => \n                `${item.id || idx}` === active.id\n            );\n            const newIndex = question.statement1s.findIndex((item, idx) => \n                `${item.id || idx}` === over.id\n            );\n\n            if (oldIndex !== -1 && newIndex !== -1) {\n                dispatch(reorderStatements({\n                    questionId: question.id,\n                    oldIndex,\n                    newIndex\n                }));\n            }\n        }\n    };\n\n    const getPrefix = (index) => {\n        if (question.typeOfQuestion === \"TN\") {\n            return prefixTN[index] || `${String.fromCharCode(65 + index)}.`;\n        } else if (question.typeOfQuestion === \"DS\") {\n            return prefixDS[index] || `${String.fromCharCode(97 + index)})`;\n        }\n        return `${index + 1}.`;\n    };\n\n    if (question.typeOfQuestion === \"TLN\") {\n        return (\n            <div className=\"text-gray-800\">\n                <span className=\"font-semibold\">Đáp án: </span>\n                <span>{question.correctAnswer}</span>\n            </div>\n        );\n    }\n\n    if (!question.statement1s || question.statement1s.length === 0) {\n        return null;\n    }\n\n    return (\n        <DndContext\n            sensors={sensors}\n            collisionDetection={closestCenter}\n            onDragEnd={handleDragEnd}\n        >\n            <SortableContext\n                items={question.statement1s.map((item, idx) => `${item.id || idx}`)}\n                strategy={verticalListSortingStrategy}\n            >\n                <div className=\"space-y-1\">\n                    {question.statement1s.map((item, idx) => (\n                        <SortableStatementItem\n                            key={`${item.id || idx}`}\n                            statement={item}\n                            index={idx}\n                            prefix={getPrefix(idx)}\n                            isCorrect={item.isCorrect}\n                            questionType={question.typeOfQuestion}\n                        />\n                    ))}\n                </div>\n            </SortableContext>\n        </DndContext>\n    );\n};\n\nexport default SortableStatementsContainer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,2BAA2B,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC7C,MAAMG,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9B,MAAMmB,OAAO,GAAGX,UAAU,CACtBD,SAAS,CAACD,aAAa,CAAC,EACxBC,SAAS,CAACF,cAAc,EAAE;IACtBe,gBAAgB,EAAEV;EACtB,CAAC,CACL,CAAC;EAED,MAAMW,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB,MAAMC,QAAQ,GAAGX,QAAQ,CAACY,WAAW,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACtD,GAAAC,MAAA,CAAGF,IAAI,CAACJ,EAAE,IAAIK,GAAG,MAAOP,MAAM,CAACE,EACnC,CAAC;MACD,MAAMO,QAAQ,GAAGjB,QAAQ,CAACY,WAAW,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACtD,GAAAC,MAAA,CAAGF,IAAI,CAACJ,EAAE,IAAIK,GAAG,MAAON,IAAI,CAACC,EACjC,CAAC;MAED,IAAIC,QAAQ,KAAK,CAAC,CAAC,IAAIM,QAAQ,KAAK,CAAC,CAAC,EAAE;QACpChB,QAAQ,CAACjB,iBAAiB,CAAC;UACvBkC,UAAU,EAAElB,QAAQ,CAACU,EAAE;UACvBC,QAAQ;UACRM;QACJ,CAAC,CAAC,CAAC;MACP;IACJ;EACJ,CAAC;EAED,MAAME,SAAS,GAAIC,KAAK,IAAK;IACzB,IAAIpB,QAAQ,CAACqB,cAAc,KAAK,IAAI,EAAE;MAClC,OAAOjB,QAAQ,CAACgB,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE,CAAC,MAAM,IAAIpB,QAAQ,CAACqB,cAAc,KAAK,IAAI,EAAE;MACzC,OAAOhB,QAAQ,CAACe,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE;IACA,UAAAJ,MAAA,CAAUI,KAAK,GAAG,CAAC;EACvB,CAAC;EAED,IAAIpB,QAAQ,CAACqB,cAAc,KAAK,KAAK,EAAE;IACnC,oBACIzB,OAAA;MAAK4B,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1B7B,OAAA;QAAM4B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CjC,OAAA;QAAA6B,QAAA,EAAOzB,QAAQ,CAAC8B;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEd;EAEA,IAAI,CAAC7B,QAAQ,CAACY,WAAW,IAAIZ,QAAQ,CAACY,WAAW,CAACmB,MAAM,KAAK,CAAC,EAAE;IAC5D,OAAO,IAAI;EACf;EAEA,oBACInC,OAAA,CAACV,UAAU;IACPgB,OAAO,EAAEA,OAAQ;IACjB8B,kBAAkB,EAAE7C,aAAc;IAClC8C,SAAS,EAAE3B,aAAc;IAAAmB,QAAA,eAEzB7B,OAAA,CAACJ,eAAe;MACZ0C,KAAK,EAAElC,QAAQ,CAACY,WAAW,CAACuB,GAAG,CAAC,CAACrB,IAAI,EAAEC,GAAG,QAAAC,MAAA,CAAQF,IAAI,CAACJ,EAAE,IAAIK,GAAG,CAAE,CAAE;MACpEqB,QAAQ,EAAE1C,2BAA4B;MAAA+B,QAAA,eAEtC7B,OAAA;QAAK4B,SAAS,EAAC,WAAW;QAAAC,QAAA,EACrBzB,QAAQ,CAACY,WAAW,CAACuB,GAAG,CAAC,CAACrB,IAAI,EAAEC,GAAG,kBAChCnB,OAAA,CAACX,qBAAqB;UAElBoD,SAAS,EAAEvB,IAAK;UAChBM,KAAK,EAAEL,GAAI;UACXuB,MAAM,EAAEnB,SAAS,CAACJ,GAAG,CAAE;UACvBwB,SAAS,EAAEzB,IAAI,CAACyB,SAAU;UAC1BC,YAAY,EAAExC,QAAQ,CAACqB;QAAe,MAAAL,MAAA,CAL9BF,IAAI,CAACJ,EAAE,IAAIK,GAAG;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMzB,CACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAErB,CAAC;AAAC9B,EAAA,CAjFIF,2BAA2B;EAAA,QACZd,WAAW,EAEZQ,UAAU,EACtBD,SAAS,EACTA,SAAS;AAAA;AAAAmD,EAAA,GALX5C,2BAA2B;AAmFjC,eAAeA,2BAA2B;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}