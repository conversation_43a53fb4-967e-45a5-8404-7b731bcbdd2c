'use strict';

export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('exam1', 'markdownExam', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn('exam1', 'typeOfExam', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn('exam1', 'year', {
      type: Sequelize.STRING,
      allowNull: true,
    })
    await queryInterface.addColumn('exam1', 'public', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
    await queryInterface.addColumn('exam1', 'class', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('exam1', 'markdownExam');
    await queryInterface.removeColumn('exam1', 'typeOfExam');
    await queryInterface.removeColumn('exam1', 'year');
    await queryInterface.removeColumn('exam1', 'public');
    await queryInterface.removeColumn('exam1', 'class');
  },
};
