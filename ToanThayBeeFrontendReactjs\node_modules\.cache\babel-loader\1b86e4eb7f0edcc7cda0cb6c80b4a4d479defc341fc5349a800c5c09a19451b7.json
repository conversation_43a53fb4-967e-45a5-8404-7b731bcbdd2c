{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\RightContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setSelectedQuestion, setEditedExam, toggleAddImage, setShowAddImagesModal, setImages } from \"src/features/examAI/examAISlice\";\nimport { useState, useEffect } from \"react\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport { ImagePlus } from \"lucide-react\";\nimport AddImagesModal from \"./AddImagesModal\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ExamEditView = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    editedExam\n  } = useSelector(state => state.examAI);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const handleExamChange = (field, value) => {\n    const updatedExam = {\n      ...editedExam,\n      [field]: value\n    };\n    dispatch(setEditedExam(updatedExam));\n  };\n  if (!editedExam) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-gray-500 mt-8\",\n      children: \"Kh\\xF4ng c\\xF3 th\\xF4ng tin \\u0111\\u1EC1 thi \\u0111\\u1EC3 ch\\u1EC9nh s\\u1EEDa\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold text-gray-800 mb-6\",\n      children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin \\u0111\\u1EC1 thi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"T\\xEAn \\u0111\\u1EC1 thi:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        className: \"w-full border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n        value: (editedExam === null || editedExam === void 0 ? void 0 : editedExam.name) || \"\",\n        onChange: e => handleExamChange('name', e.target.value),\n        placeholder: \"Nh\\u1EADp t\\xEAn \\u0111\\u1EC1 thi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"Lo\\u1EA1i \\u0111\\u1EC1 thi:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: editedExam === null || editedExam === void 0 ? void 0 : editedExam.typeOfExam,\n        onChange: value => handleExamChange('typeOfExam', value),\n        options: Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : [],\n        placeholder: \"Ch\\u1ECDn lo\\u1EA1i \\u0111\\u1EC1 thi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"L\\u1EDBp:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: editedExam === null || editedExam === void 0 ? void 0 : editedExam.class,\n        onChange: value => handleExamChange('class', value),\n        options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n        placeholder: \"Ch\\u1ECDn l\\u1EDBp\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"N\\u0103m:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: editedExam === null || editedExam === void 0 ? void 0 : editedExam.year,\n        onChange: value => handleExamChange('year', value),\n        options: Array.isArray(codes[\"year\"]) ? codes[\"year\"] : [],\n        placeholder: \"Ch\\u1ECDn n\\u0103m\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"Tr\\u1EA1ng th\\xE1i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center gap-2 cursor-pointer\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"radio\",\n            name: \"public\",\n            checked: (editedExam === null || editedExam === void 0 ? void 0 : editedExam.public) === true,\n            onChange: () => handleExamChange('public', true),\n            className: \"w-4 h-4 text-blue-600 focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-700\",\n            children: \"C\\xF4ng khai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center gap-2 cursor-pointer\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"radio\",\n            name: \"public\",\n            checked: (editedExam === null || editedExam === void 0 ? void 0 : editedExam.public) === false,\n            onChange: () => handleExamChange('public', false),\n            className: \"w-4 h-4 text-blue-600 focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-700\",\n            children: \"Ri\\xEAng t\\u01B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(ExamEditView, \"jDgMJAXlJQF4qBjC6w23Jl7m1Io=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = ExamEditView;\nconst QuestionEditContainer = () => {\n  _s2();\n  const dispatch = useDispatch();\n  const {\n    selectedQuestion\n  } = useSelector(state => state.examAI);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n\n  // Filter chapter options based on class\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (selectedQuestion !== null && selectedQuestion !== void 0 && selectedQuestion.class && selectedQuestion.class.trim() !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.class]);\n  const [optionChapter, setOptionChapter] = useState([]);\n  const handleQuestionChange = (field, value) => {\n    const updatedQuestion = {\n      ...selectedQuestion,\n      [field]: value\n    };\n    dispatch(setSelectedQuestion(updatedQuestion));\n  };\n  const handleStatementChange = (index, field, value) => {\n    const updatedStatements = [...selectedQuestion.statement1s];\n\n    // ✅ Tạo bản sao mới của object tại index\n    const updatedStatement = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n\n    // ✅ Gán lại object đã sửa vào mảng mới\n    updatedStatements[index] = updatedStatement;\n    const updatedQuestion = {\n      ...selectedQuestion,\n      statement1s: updatedStatements\n    };\n    dispatch(setSelectedQuestion(updatedQuestion));\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"N\\u1ED9i dung c\\xE2u h\\u1ECFi:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        className: \"w-full min-h-[150px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n        value: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.content) || \"\",\n        onChange: e => handleQuestionChange('content', e.target.value),\n        placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"L\\u1EDBp:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.class,\n        onChange: value => handleQuestionChange('class', value),\n        options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n        placeholder: \"Ch\\u1ECDn l\\u1EDBp\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"\\u0110\\u1ED9 kh\\xF3:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.difficulty,\n        onChange: value => handleQuestionChange('difficulty', value),\n        options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n        placeholder: \"Ch\\u1ECDn \\u0111\\u1ED9 kh\\xF3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"Ch\\u01B0\\u01A1ng:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n        selectedOption: selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.chapter,\n        onChange: value => handleQuestionChange('chapter', value),\n        options: optionChapter,\n        placeholder: \"Ch\\u1ECDn ch\\u01B0\\u01A1ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this), (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.typeOfQuestion) === \"TLN\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0110\\xE1p \\xE1n:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"w-full border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n          value: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.correctAnswer) || \"\",\n          onChange: e => handleQuestionChange('correctAnswer', e.target.value),\n          placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 21\n      }, this)\n    }, void 0, false), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"L\\u1EDDi gi\\u1EA3i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        className: \"w-full min-h-[120px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n        value: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.solution) || \"\",\n        onChange: e => handleQuestionChange('solution', e.target.value),\n        placeholder: \"Nh\\u1EADp l\\u1EDDi gi\\u1EA3i...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 13\n    }, this), (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.statement1s) && selectedQuestion.statement1s.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-3\",\n        children: \"C\\xE1c m\\u1EC7nh \\u0111\\u1EC1:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: selectedQuestion.statement1s.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: [\"M\\u1EC7nh \\u0111\\u1EC1 \", String.fromCharCode(65 + index)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center gap-2 cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"statement-\".concat(index),\n                  checked: statement.isCorrect === true,\n                  onChange: () => handleStatementChange(index, 'isCorrect', true),\n                  className: \"w-4 h-4 text-green-600 focus:ring-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-green-600 font-medium\",\n                  children: \"\\u0110\\xFAng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center gap-2 cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"statement-\".concat(index),\n                  checked: statement.isCorrect === false,\n                  onChange: () => handleStatementChange(index, 'isCorrect', false),\n                  className: \"w-4 h-4 text-red-600 focus:ring-red-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-red-600 font-medium\",\n                  children: \"Sai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"w-full min-h-[80px] border border-gray-300 rounded-lg p-2 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n            value: statement.content || \"\",\n            onChange: e => handleStatementChange(index, 'content', e.target.value),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1 \".concat(String.fromCharCode(65 + index), \"...\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 33\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 29\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true);\n};\n_s2(QuestionEditContainer, \"LkfQPotlyVCgk/GPeSeHCSS/XBE=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c2 = QuestionEditContainer;\nconst QuestionImageContainer = () => {\n  _s3();\n  var _images$ImageFormN8N;\n  const {\n    images\n  } = useSelector(state => state.images);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => {},\n      className: \"px-2 py-1 w-full text-sm rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors\",\n      children: \"Th\\xEAm \\u1EA3nh\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 13\n    }, this), images === null || images === void 0 ? void 0 : (_images$ImageFormN8N = images.ImageFormN8N) === null || _images$ImageFormN8N === void 0 ? void 0 : _images$ImageFormN8N.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 group relative w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: [\"H\\xECnh \\u1EA3nh \", index + 1, \":\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative w-full h-48 rounded-lg overflow-hidden border transition-all duration-300 \\r hover:border-sky-400 hover:shadow-lg group cursor-move\",\n        draggable: true,\n        onDragStart: e => {\n          e.dataTransfer.setData(\"text/plain\", image);\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: image,\n          alt: \"image-\".concat(index),\n          className: \"w-full h-full object-contain transition-all duration-300 group-hover:brightness-75\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(ImagePlus, {\n            className: \"w-8 h-8 text-white bg-sky-500 p-1 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 21\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 17\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 279,\n    columnNumber: 9\n  }, this);\n};\n_s3(QuestionImageContainer, \"xwWzCV13OkPV9TNK6o7qWnn5Pfc=\", false, function () {\n  return [useSelector];\n});\n_c3 = QuestionImageContainer;\nconst QuestionEditView = () => {\n  _s4();\n  const dispatch = useDispatch();\n  const {\n    selectedQuestion,\n    isAddImage\n  } = useSelector(state => state.examAI);\n  if (!selectedQuestion) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-gray-500 mt-8\",\n      children: \"Ch\\u1ECDn m\\u1ED9t c\\xE2u h\\u1ECFi \\u0111\\u1EC3 ch\\u1EC9nh s\\u1EEDa\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex text-center justify-between items-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-800\",\n        children: \"Ch\\u1EC9nh s\\u1EEDa c\\xE2u h\\u1ECFi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => dispatch(toggleAddImage()),\n        className: \"px-2 py-1 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors\",\n        children: isAddImage ? \"Thông tin\" : \"Thêm ảnh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 13\n    }, this), isAddImage ? /*#__PURE__*/_jsxDEV(QuestionImageContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 27\n    }, this) : /*#__PURE__*/_jsxDEV(QuestionEditContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 56\n    }, this)]\n  }, void 0, true);\n};\n_s4(QuestionEditView, \"iHUvFFktins1m09Vqz1dTfgigyU=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c4 = QuestionEditView;\nexport const RightContent = () => {\n  _s5();\n  const {\n    viewEdit\n  } = useSelector(state => state.examAI);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-[25rem] p-4 overflow-y-auto bg-white border-l border-gray-200 flex-shrink-0\",\n    children: [viewEdit === 'exam' && /*#__PURE__*/_jsxDEV(ExamEditView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 37\n    }, this), viewEdit === 'question' && /*#__PURE__*/_jsxDEV(QuestionEditView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 41\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 351,\n    columnNumber: 9\n  }, this);\n};\n_s5(RightContent, \"plqPiIWI8H/U9YD7djE1+JqN1rI=\", false, function () {\n  return [useSelector];\n});\n_c5 = RightContent;\nexport default RightContent;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"ExamEditView\");\n$RefreshReg$(_c2, \"QuestionEditContainer\");\n$RefreshReg$(_c3, \"QuestionImageContainer\");\n$RefreshReg$(_c4, \"QuestionEditView\");\n$RefreshReg$(_c5, \"RightContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "setSelectedQuestion", "setEditedExam", "toggleAddImage", "setShowAddImagesModal", "setImages", "useState", "useEffect", "DropMenuBarAdmin", "SuggestInputBarAdmin", "ImagePlus", "AddImagesModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ExamEdit<PERSON>iew", "_s", "dispatch", "editedExam", "state", "examAI", "codes", "handleExamChange", "field", "value", "updatedExam", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "name", "onChange", "e", "target", "placeholder", "selectedOption", "typeOfExam", "options", "Array", "isArray", "class", "year", "checked", "public", "_c", "QuestionEditContainer", "_s2", "selectedQuestion", "trim", "setOptionChapter", "filter", "code", "length", "optionChapter", "handleQuestionChange", "updatedQuestion", "handleStatementChange", "index", "updatedStatements", "statement1s", "updatedStatement", "content", "difficulty", "chapter", "typeOfQuestion", "<PERSON><PERSON><PERSON><PERSON>", "solution", "map", "statement", "String", "fromCharCode", "concat", "isCorrect", "_c2", "QuestionImageContainer", "_s3", "_images$ImageFormN8N", "images", "onClick", "ImageFormN8N", "image", "draggable", "onDragStart", "dataTransfer", "setData", "src", "alt", "_c3", "QuestionEdit<PERSON>iew", "_s4", "isAddImage", "_c4", "RightContent", "_s5", "viewEdit", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/RightContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { setSelectedQuestion, setEditedExam, toggleAddImage, setShowAddImagesModal, setImages } from \"src/features/examAI/examAISlice\";\r\nimport { useState, useEffect } from \"react\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport { ImagePlus } from \"lucide-react\";\r\nimport AddImagesModal from \"./AddImagesModal\";\r\n\r\nconst ExamEditView = () => {\r\n    const dispatch = useDispatch();\r\n    const { editedExam } = useSelector((state) => state.examAI);\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    const handleExamChange = (field, value) => {\r\n        const updatedExam = { ...editedExam, [field]: value };\r\n        dispatch(setEditedExam(updatedExam));\r\n    };\r\n\r\n    if (!editedExam) {\r\n        return (\r\n            <div className=\"text-center text-gray-500 mt-8\">\r\n                <PERSON>h<PERSON>ng có thông tin đề thi để chỉnh sửa\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <h2 className=\"text-xl font-semibold text-gray-800 mb-6\">Chỉnh sửa thông tin đề thi</h2>\r\n\r\n            {/* Tên đề thi */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Tên đề thi:</label>\r\n                <input\r\n                    type=\"text\"\r\n                    className=\"w-full border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                    value={editedExam?.name || \"\"}\r\n                    onChange={(e) => handleExamChange('name', e.target.value)}\r\n                    placeholder=\"Nhập tên đề thi...\"\r\n                />\r\n            </div>\r\n\r\n            {/* Loại đề thi */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Loại đề thi:</label>\r\n                <DropMenuBarAdmin\r\n                    selectedOption={editedExam?.typeOfExam}\r\n                    onChange={(value) => handleExamChange('typeOfExam', value)}\r\n                    options={Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : []}\r\n                    placeholder=\"Chọn loại đề thi\"\r\n                />\r\n            </div>\r\n\r\n            {/* Lớp */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Lớp:</label>\r\n                <DropMenuBarAdmin\r\n                    selectedOption={editedExam?.class}\r\n                    onChange={(value) => handleExamChange('class', value)}\r\n                    options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                    placeholder=\"Chọn lớp\"\r\n                />\r\n            </div>\r\n\r\n            {/* Năm */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Năm:</label>\r\n                <DropMenuBarAdmin\r\n                    selectedOption={editedExam?.year}\r\n                    onChange={(value) => handleExamChange('year', value)}\r\n                    options={Array.isArray(codes[\"year\"]) ? codes[\"year\"] : []}\r\n                    placeholder=\"Chọn năm\"\r\n                />\r\n            </div>\r\n\r\n            {/* Công khai */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Trạng thái:</label>\r\n                <div className=\"flex items-center gap-6\">\r\n                    <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                        <input\r\n                            type=\"radio\"\r\n                            name=\"public\"\r\n                            checked={editedExam?.public === true}\r\n                            onChange={() => handleExamChange('public', true)}\r\n                            className=\"w-4 h-4 text-blue-600 focus:ring-blue-500\"\r\n                        />\r\n                        <span className=\"text-sm text-gray-700\">Công khai</span>\r\n                    </label>\r\n                    <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                        <input\r\n                            type=\"radio\"\r\n                            name=\"public\"\r\n                            checked={editedExam?.public === false}\r\n                            onChange={() => handleExamChange('public', false)}\r\n                            className=\"w-4 h-4 text-blue-600 focus:ring-blue-500\"\r\n                        />\r\n                        <span className=\"text-sm text-gray-700\">Riêng tư</span>\r\n                    </label>\r\n                </div>\r\n            </div>\r\n        </>\r\n    );\r\n};\r\n\r\nconst QuestionEditContainer = () => {\r\n    const dispatch = useDispatch();\r\n    const { selectedQuestion } = useSelector((state) => state.examAI);\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    // Filter chapter options based on class\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (selectedQuestion?.class && selectedQuestion.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, selectedQuestion?.class]);\r\n\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n\r\n    const handleQuestionChange = (field, value) => {\r\n        const updatedQuestion = { ...selectedQuestion, [field]: value };\r\n        dispatch(setSelectedQuestion(updatedQuestion));\r\n    };\r\n\r\n    const handleStatementChange = (index, field, value) => {\r\n        const updatedStatements = [...selectedQuestion.statement1s];\r\n\r\n        // ✅ Tạo bản sao mới của object tại index\r\n        const updatedStatement = {\r\n            ...updatedStatements[index],\r\n            [field]: value,\r\n        };\r\n\r\n        // ✅ Gán lại object đã sửa vào mảng mới\r\n        updatedStatements[index] = updatedStatement;\r\n\r\n        const updatedQuestion = {\r\n            ...selectedQuestion,\r\n            statement1s: updatedStatements,\r\n        };\r\n\r\n        dispatch(setSelectedQuestion(updatedQuestion));\r\n    };\r\n\r\n\r\n    return (\r\n        <>\r\n            {/* Nội dung câu hỏi */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Nội dung câu hỏi:</label>\r\n                <textarea\r\n                    className=\"w-full min-h-[150px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                    value={selectedQuestion?.content || \"\"}\r\n                    onChange={(e) => handleQuestionChange('content', e.target.value)}\r\n                    placeholder=\"Nhập nội dung câu hỏi...\"\r\n                />\r\n            </div>\r\n\r\n            {/* Lớp */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Lớp:</label>\r\n                <DropMenuBarAdmin\r\n                    selectedOption={selectedQuestion?.class}\r\n                    onChange={(value) => handleQuestionChange('class', value)}\r\n                    options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                    placeholder=\"Chọn lớp\"\r\n                />\r\n            </div>\r\n\r\n            {/* Độ khó */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Độ khó:</label>\r\n                <DropMenuBarAdmin\r\n                    selectedOption={selectedQuestion?.difficulty}\r\n                    onChange={(value) => handleQuestionChange('difficulty', value)}\r\n                    options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                    placeholder=\"Chọn độ khó\"\r\n                />\r\n            </div>\r\n\r\n            {/* Chương */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Chương:</label>\r\n                <SuggestInputBarAdmin\r\n                    selectedOption={selectedQuestion?.chapter}\r\n                    onChange={(value) => handleQuestionChange('chapter', value)}\r\n                    options={optionChapter}\r\n                    placeholder=\"Chọn chương\"\r\n                />\r\n            </div>\r\n\r\n            {selectedQuestion?.typeOfQuestion === \"TLN\" && (\r\n                <>\r\n                    <div className=\"mb-6\">\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">Đáp án:</label>\r\n                        <input\r\n                            type=\"text\"\r\n                            className=\"w-full border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                            value={selectedQuestion?.correctAnswer || \"\"}\r\n                            onChange={(e) => handleQuestionChange('correctAnswer', e.target.value)}\r\n                            placeholder=\"Nhập đáp án...\"\r\n                        />\r\n                    </div>\r\n                </>\r\n            )}\r\n\r\n            {/* Lời giải */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Lời giải:</label>\r\n                <textarea\r\n                    className=\"w-full min-h-[120px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                    value={selectedQuestion?.solution || \"\"}\r\n                    onChange={(e) => handleQuestionChange('solution', e.target.value)}\r\n                    placeholder=\"Nhập lời giải...\"\r\n                />\r\n            </div>\r\n\r\n            {/* Các mệnh đề/đáp án */}\r\n            {selectedQuestion?.statement1s && selectedQuestion.statement1s.length > 0 && (\r\n                <div className=\"mb-6\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-3\">Các mệnh đề:</label>\r\n                    <div className=\"space-y-4\">\r\n                        {selectedQuestion.statement1s.map((statement, index) => (\r\n                            <div key={index} className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\r\n                                <div className=\"flex items-center justify-between mb-3\">\r\n                                    <span className=\"text-sm font-medium text-gray-600\">\r\n                                        Mệnh đề {String.fromCharCode(65 + index)}\r\n                                    </span>\r\n                                    <div className=\"flex items-center gap-4\">\r\n                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name={`statement-${index}`}\r\n                                                checked={statement.isCorrect === true}\r\n                                                onChange={() => handleStatementChange(index, 'isCorrect', true)}\r\n                                                className=\"w-4 h-4 text-green-600 focus:ring-green-500\"\r\n                                            />\r\n                                            <span className=\"text-sm text-green-600 font-medium\">Đúng</span>\r\n                                        </label>\r\n                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name={`statement-${index}`}\r\n                                                checked={statement.isCorrect === false}\r\n                                                onChange={() => handleStatementChange(index, 'isCorrect', false)}\r\n                                                className=\"w-4 h-4 text-red-600 focus:ring-red-500\"\r\n                                            />\r\n                                            <span className=\"text-sm text-red-600 font-medium\">Sai</span>\r\n                                        </label>\r\n                                    </div>\r\n                                </div>\r\n                                <textarea\r\n                                    className=\"w-full min-h-[80px] border border-gray-300 rounded-lg p-2 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                                    value={statement.content || \"\"}\r\n                                    onChange={(e) => handleStatementChange(index, 'content', e.target.value)}\r\n                                    placeholder={`Nhập nội dung mệnh đề ${String.fromCharCode(65 + index)}...`}\r\n                                />\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionImageContainer = () => {\r\n    const { images } = useSelector((state) => state.images);\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-4\">\r\n            <button\r\n                onClick={() => { }}\r\n                className=\"px-2 py-1 w-full text-sm rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors\"\r\n            >\r\n                Thêm ảnh\r\n            </button>\r\n            {images?.ImageFormN8N?.map((image, index) => (\r\n                <div key={index} className=\"mb-6 group relative w-full\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Hình ảnh {index + 1}:\r\n                    </label>\r\n\r\n                    <div\r\n                        className=\"relative w-full h-48 rounded-lg overflow-hidden border transition-all duration-300 \r\n                   hover:border-sky-400 hover:shadow-lg group cursor-move\"\r\n                        draggable\r\n                        onDragStart={(e) => {\r\n                            e.dataTransfer.setData(\"text/plain\", image);\r\n                        }}\r\n                    >\r\n                        <img\r\n                            src={image}\r\n                            alt={`image-${index}`}\r\n                            className=\"w-full h-full object-contain transition-all duration-300 group-hover:brightness-75\"\r\n                        />\r\n\r\n                        {/* Icon hiện khi hover */}\r\n                        <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\r\n                            <ImagePlus className=\"w-8 h-8 text-white bg-sky-500 p-1 rounded-full shadow-lg\" />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            ))}\r\n        </div>\r\n    );\r\n}\r\n\r\n\r\nconst QuestionEditView = () => {\r\n    const dispatch = useDispatch();\r\n    const { selectedQuestion, isAddImage } = useSelector((state) => state.examAI);\r\n\r\n    if (!selectedQuestion) {\r\n        return (\r\n            <div className=\"text-center text-gray-500 mt-8\">\r\n                Chọn một câu hỏi để chỉnh sửa\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <div className=\"flex text-center justify-between items-center mb-6\">\r\n                <h2 className=\"text-xl font-semibold text-gray-800\">Chỉnh sửa câu hỏi</h2>\r\n                <button\r\n                    onClick={() => dispatch(toggleAddImage())}\r\n                    className=\"px-2 py-1 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors\"\r\n                >\r\n                    {isAddImage ? \"Thông tin\" : \"Thêm ảnh\"}\r\n                </button>\r\n            </div>\r\n            {isAddImage ? <QuestionImageContainer /> : <QuestionEditContainer />}\r\n        </>\r\n    );\r\n}\r\n\r\n\r\nexport const RightContent = () => {\r\n    const { viewEdit } = useSelector((state) => state.examAI);\r\n\r\n    return (\r\n        <div className=\"w-[25rem] p-4 overflow-y-auto bg-white border-l border-gray-200 flex-shrink-0\">\r\n            {viewEdit === 'exam' && <ExamEditView />}\r\n            {viewEdit === 'question' && <QuestionEditView />}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RightContent;"], "mappings": ";;;;;;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,mBAAmB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,SAAS,QAAQ,iCAAiC;AACtI,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB;EAAW,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAC3D,MAAM;IAAEC;EAAM,CAAC,GAAGvB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACvC,MAAMC,WAAW,GAAG;MAAE,GAAGP,UAAU;MAAE,CAACK,KAAK,GAAGC;IAAM,CAAC;IACrDP,QAAQ,CAAChB,aAAa,CAACwB,WAAW,CAAC,CAAC;EACxC,CAAC;EAED,IAAI,CAACP,UAAU,EAAE;IACb,oBACIN,OAAA;MAAKc,SAAS,EAAC,gCAAgC;MAAAC,QAAA,EAAC;IAEhD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEd;EAEA,oBACInB,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBACIf,OAAA;MAAIc,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGxFnB,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnFnB,OAAA;QACIoB,IAAI,EAAC,MAAM;QACXN,SAAS,EAAC,mHAAmH;QAC7HF,KAAK,EAAE,CAAAN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEe,IAAI,KAAI,EAAG;QAC9BC,QAAQ,EAAGC,CAAC,IAAKb,gBAAgB,CAAC,MAAM,EAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;QAC1Da,WAAW,EAAC;MAAoB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpFnB,OAAA,CAACL,gBAAgB;QACb+B,cAAc,EAAEpB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,UAAW;QACvCL,QAAQ,EAAGV,KAAK,IAAKF,gBAAgB,CAAC,YAAY,EAAEE,KAAK,CAAE;QAC3DgB,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,WAAW,CAAC,CAAC,GAAGA,KAAK,CAAC,WAAW,CAAC,GAAG,EAAG;QACrEgB,WAAW,EAAC;MAAkB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5EnB,OAAA,CAACL,gBAAgB;QACb+B,cAAc,EAAEpB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEyB,KAAM;QAClCT,QAAQ,EAAGV,KAAK,IAAKF,gBAAgB,CAAC,OAAO,EAAEE,KAAK,CAAE;QACtDgB,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;QAC7DgB,WAAW,EAAC;MAAU;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5EnB,OAAA,CAACL,gBAAgB;QACb+B,cAAc,EAAEpB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0B,IAAK;QACjCV,QAAQ,EAAGV,KAAK,IAAKF,gBAAgB,CAAC,MAAM,EAAEE,KAAK,CAAE;QACrDgB,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,MAAM,CAAC,CAAC,GAAGA,KAAK,CAAC,MAAM,CAAC,GAAG,EAAG;QAC3DgB,WAAW,EAAC;MAAU;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnFnB,OAAA;QAAKc,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACpCf,OAAA;UAAOc,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDf,OAAA;YACIoB,IAAI,EAAC,OAAO;YACZC,IAAI,EAAC,QAAQ;YACbY,OAAO,EAAE,CAAA3B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE4B,MAAM,MAAK,IAAK;YACrCZ,QAAQ,EAAEA,CAAA,KAAMZ,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAE;YACjDI,SAAS,EAAC;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACFnB,OAAA;YAAMc,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACRnB,OAAA;UAAOc,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDf,OAAA;YACIoB,IAAI,EAAC,OAAO;YACZC,IAAI,EAAC,QAAQ;YACbY,OAAO,EAAE,CAAA3B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE4B,MAAM,MAAK,KAAM;YACtCZ,QAAQ,EAAEA,CAAA,KAAMZ,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAE;YAClDI,SAAS,EAAC;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACFnB,OAAA;YAAMc,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAACf,EAAA,CA/FID,YAAY;EAAA,QACGhB,WAAW,EACLD,WAAW,EAChBA,WAAW;AAAA;AAAAiD,EAAA,GAH3BhC,YAAY;AAiGlB,MAAMiC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAChC,MAAMhC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmD;EAAiB,CAAC,GAAGpD,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACjE,MAAM;IAAEC;EAAM,CAAC,GAAGvB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;;EAErD;EACAf,SAAS,CAAC,MAAM;IACZ,IAAImC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAI6B,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEP,KAAK,IAAIO,gBAAgB,CAACP,KAAK,CAACQ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjEC,gBAAgB,CACZ/B,KAAK,CAAC,SAAS,CAAC,CAACgC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,MAAM,KAAK,CAAC,CAC5D,CAAC;MACL,CAAC,MAAM;QACHH,gBAAgB,CAAC/B,KAAK,CAAC,SAAS,CAAC,CAACgC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHH,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAAC/B,KAAK,EAAE6B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEP,KAAK,CAAC,CAAC;EAEpC,MAAM,CAACa,aAAa,EAAEJ,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAMoD,oBAAoB,GAAGA,CAAClC,KAAK,EAAEC,KAAK,KAAK;IAC3C,MAAMkC,eAAe,GAAG;MAAE,GAAGR,gBAAgB;MAAE,CAAC3B,KAAK,GAAGC;IAAM,CAAC;IAC/DP,QAAQ,CAACjB,mBAAmB,CAAC0D,eAAe,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAACC,KAAK,EAAErC,KAAK,EAAEC,KAAK,KAAK;IACnD,MAAMqC,iBAAiB,GAAG,CAAC,GAAGX,gBAAgB,CAACY,WAAW,CAAC;;IAE3D;IACA,MAAMC,gBAAgB,GAAG;MACrB,GAAGF,iBAAiB,CAACD,KAAK,CAAC;MAC3B,CAACrC,KAAK,GAAGC;IACb,CAAC;;IAED;IACAqC,iBAAiB,CAACD,KAAK,CAAC,GAAGG,gBAAgB;IAE3C,MAAML,eAAe,GAAG;MACpB,GAAGR,gBAAgB;MACnBY,WAAW,EAAED;IACjB,CAAC;IAED5C,QAAQ,CAACjB,mBAAmB,CAAC0D,eAAe,CAAC,CAAC;EAClD,CAAC;EAGD,oBACI9C,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBAEIf,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACzFnB,OAAA;QACIc,SAAS,EAAC,iIAAiI;QAC3IF,KAAK,EAAE,CAAA0B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEc,OAAO,KAAI,EAAG;QACvC9B,QAAQ,EAAGC,CAAC,IAAKsB,oBAAoB,CAAC,SAAS,EAAEtB,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;QACjEa,WAAW,EAAC;MAA0B;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5EnB,OAAA,CAACL,gBAAgB;QACb+B,cAAc,EAAEY,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEP,KAAM;QACxCT,QAAQ,EAAGV,KAAK,IAAKiC,oBAAoB,CAAC,OAAO,EAAEjC,KAAK,CAAE;QAC1DgB,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;QAC7DgB,WAAW,EAAC;MAAU;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/EnB,OAAA,CAACL,gBAAgB;QACb+B,cAAc,EAAEY,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEe,UAAW;QAC7C/B,QAAQ,EAAGV,KAAK,IAAKiC,oBAAoB,CAAC,YAAY,EAAEjC,KAAK,CAAE;QAC/DgB,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;QACvEgB,WAAW,EAAC;MAAa;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/EnB,OAAA,CAACJ,oBAAoB;QACjB8B,cAAc,EAAEY,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgB,OAAQ;QAC1ChC,QAAQ,EAAGV,KAAK,IAAKiC,oBAAoB,CAAC,SAAS,EAAEjC,KAAK,CAAE;QAC5DgB,OAAO,EAAEgB,aAAc;QACvBnB,WAAW,EAAC;MAAa;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEL,CAAAmB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEiB,cAAc,MAAK,KAAK,iBACvCvD,OAAA,CAAAE,SAAA;MAAAa,QAAA,eACIf,OAAA;QAAKc,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACjBf,OAAA;UAAOc,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/EnB,OAAA;UACIoB,IAAI,EAAC,MAAM;UACXN,SAAS,EAAC,mHAAmH;UAC7HF,KAAK,EAAE,CAAA0B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkB,aAAa,KAAI,EAAG;UAC7ClC,QAAQ,EAAGC,CAAC,IAAKsB,oBAAoB,CAAC,eAAe,EAAEtB,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;UACvEa,WAAW,EAAC;QAAgB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC,gBACR,CACL,eAGDnB,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACjFnB,OAAA;QACIc,SAAS,EAAC,iIAAiI;QAC3IF,KAAK,EAAE,CAAA0B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEmB,QAAQ,KAAI,EAAG;QACxCnC,QAAQ,EAAGC,CAAC,IAAKsB,oBAAoB,CAAC,UAAU,EAAEtB,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;QAClEa,WAAW,EAAC;MAAkB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAGL,CAAAmB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEY,WAAW,KAAIZ,gBAAgB,CAACY,WAAW,CAACP,MAAM,GAAG,CAAC,iBACrE3C,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpFnB,OAAA;QAAKc,SAAS,EAAC,WAAW;QAAAC,QAAA,EACrBuB,gBAAgB,CAACY,WAAW,CAACQ,GAAG,CAAC,CAACC,SAAS,EAAEX,KAAK,kBAC/ChD,OAAA;UAAiBc,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBACzEf,OAAA;YAAKc,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACnDf,OAAA;cAAMc,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,yBACxC,EAAC6C,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGb,KAAK,CAAC;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACPnB,OAAA;cAAKc,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACpCf,OAAA;gBAAOc,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDf,OAAA;kBACIoB,IAAI,EAAC,OAAO;kBACZC,IAAI,eAAAyC,MAAA,CAAed,KAAK,CAAG;kBAC3Bf,OAAO,EAAE0B,SAAS,CAACI,SAAS,KAAK,IAAK;kBACtCzC,QAAQ,EAAEA,CAAA,KAAMyB,qBAAqB,CAACC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAE;kBAChElC,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACFnB,OAAA;kBAAMc,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACRnB,OAAA;gBAAOc,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDf,OAAA;kBACIoB,IAAI,EAAC,OAAO;kBACZC,IAAI,eAAAyC,MAAA,CAAed,KAAK,CAAG;kBAC3Bf,OAAO,EAAE0B,SAAS,CAACI,SAAS,KAAK,KAAM;kBACvCzC,QAAQ,EAAEA,CAAA,KAAMyB,qBAAqB,CAACC,KAAK,EAAE,WAAW,EAAE,KAAK,CAAE;kBACjElC,SAAS,EAAC;gBAAyC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACFnB,OAAA;kBAAMc,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNnB,OAAA;YACIc,SAAS,EAAC,gIAAgI;YAC1IF,KAAK,EAAE+C,SAAS,CAACP,OAAO,IAAI,EAAG;YAC/B9B,QAAQ,EAAGC,CAAC,IAAKwB,qBAAqB,CAACC,KAAK,EAAE,SAAS,EAAEzB,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;YACzEa,WAAW,oDAAAqC,MAAA,CAA2BF,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGb,KAAK,CAAC;UAAM;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA,GAjCI6B,KAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkCV,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA,eACH,CAAC;AAEX,CAAC;AAAAkB,GAAA,CAvKKD,qBAAqB;EAAA,QACNjD,WAAW,EACCD,WAAW,EACtBA,WAAW;AAAA;AAAA8E,GAAA,GAH3B5B,qBAAqB;AAyK3B,MAAM6B,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,oBAAA;EACjC,MAAM;IAAEC;EAAO,CAAC,GAAGlF,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAAC6D,MAAM,CAAC;EAEvD,oBACIpE,OAAA;IAAKc,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChCf,OAAA;MACIqE,OAAO,EAAEA,CAAA,KAAM,CAAE,CAAE;MACnBvD,SAAS,EAAC,gGAAgG;MAAAC,QAAA,EAC7G;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EACRiD,MAAM,aAANA,MAAM,wBAAAD,oBAAA,GAANC,MAAM,CAAEE,YAAY,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBT,GAAG,CAAC,CAACa,KAAK,EAAEvB,KAAK,kBACpChD,OAAA;MAAiBc,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACnDf,OAAA;QAAOc,SAAS,EAAC,8CAA8C;QAAAC,QAAA,GAAC,mBACnD,EAACiC,KAAK,GAAG,CAAC,EAAC,GACxB;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERnB,OAAA;QACIc,SAAS,EAAC,8IACwC;QAClD0D,SAAS;QACTC,WAAW,EAAGlD,CAAC,IAAK;UAChBA,CAAC,CAACmD,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEJ,KAAK,CAAC;QAC/C,CAAE;QAAAxD,QAAA,gBAEFf,OAAA;UACI4E,GAAG,EAAEL,KAAM;UACXM,GAAG,WAAAf,MAAA,CAAWd,KAAK,CAAG;UACtBlC,SAAS,EAAC;QAAoF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC,eAGFnB,OAAA;UAAKc,SAAS,EAAC,iHAAiH;UAAAC,QAAA,eAC5Hf,OAAA,CAACH,SAAS;YAACiB,SAAS,EAAC;UAA0D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,GAvBA6B,KAAK;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwBV,CACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAA+C,GAAA,CAxCKD,sBAAsB;EAAA,QACL/E,WAAW;AAAA;AAAA4F,GAAA,GAD5Bb,sBAAsB;AA2C5B,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAM3E,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmD,gBAAgB;IAAE2C;EAAW,CAAC,GAAG/F,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAE7E,IAAI,CAAC8B,gBAAgB,EAAE;IACnB,oBACItC,OAAA;MAAKc,SAAS,EAAC,gCAAgC;MAAAC,QAAA,EAAC;IAEhD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEd;EAEA,oBACInB,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBACIf,OAAA;MAAKc,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBAC/Df,OAAA;QAAIc,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EnB,OAAA;QACIqE,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAACf,cAAc,CAAC,CAAC,CAAE;QAC1CwB,SAAS,EAAC,iFAAiF;QAAAC,QAAA,EAE1FkE,UAAU,GAAG,WAAW,GAAG;MAAU;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EACL8D,UAAU,gBAAGjF,OAAA,CAACiE,sBAAsB;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGnB,OAAA,CAACoC,qBAAqB;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACtE,CAAC;AAEX,CAAC;AAAA6D,GAAA,CA1BKD,gBAAgB;EAAA,QACD5F,WAAW,EACaD,WAAW;AAAA;AAAAgG,GAAA,GAFlDH,gBAAgB;AA6BtB,OAAO,MAAMI,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC9B,MAAM;IAAEC;EAAS,CAAC,GAAGnG,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEzD,oBACIR,OAAA;IAAKc,SAAS,EAAC,+EAA+E;IAAAC,QAAA,GACzFsE,QAAQ,KAAK,MAAM,iBAAIrF,OAAA,CAACG,YAAY;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACvCkE,QAAQ,KAAK,UAAU,iBAAIrF,OAAA,CAAC+E,gBAAgB;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC;AAEd,CAAC;AAACiE,GAAA,CATWD,YAAY;EAAA,QACAjG,WAAW;AAAA;AAAAoG,GAAA,GADvBH,YAAY;AAWzB,eAAeA,YAAY;AAAC,IAAAhD,EAAA,EAAA6B,GAAA,EAAAc,GAAA,EAAAI,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAApD,EAAA;AAAAoD,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}