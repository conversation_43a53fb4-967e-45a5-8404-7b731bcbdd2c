{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { postExam } from \"src/features/addExam/addExamSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LeftContent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 border-r bg-white min-h-screen border-gray-300 overflow-y-auto p-4\",\n    children: \"Left Content\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n};\n_c = LeftContent;\nexport default LeftContent;\nvar _c;\n$RefreshReg$(_c, \"LeftContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "postExam", "jsxDEV", "_jsxDEV", "LeftContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { postExam } from \"src/features/addExam/addExamSlice\";\r\n\r\n\r\nexport const LeftContent = () => {\r\n    return (\r\n        <div className=\"flex-1 border-r bg-white min-h-screen border-gray-300 overflow-y-auto p-4\">\r\n            Left Content\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default LeftContent;"], "mappings": ";AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG7D,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAC7B,oBACID,OAAA;IAAKE,SAAS,EAAC,2EAA2E;IAAAC,QAAA,EAAC;EAE3F;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAEd,CAAC;AAAAC,EAAA,GANYP,WAAW;AAQxB,eAAeA,WAAW;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}