import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    dropdownOpen: false,
    closeSidebar: false,
    examDropdownOpen: false,
}

const sidebarSlice = createSlice({
    name: 'sidebar',
    initialState,
    reducers: {
        toggleDropdown: (state) => {
            state.dropdownOpen = !state.dropdownOpen;
        },
        toggleCloseSidebar: (state) => {
            state.closeSidebar = !state.closeSidebar;
        },
        toggleExamDropdown: (state) => {
            state.examDropdownOpen = !state.examDropdownOpen;
        },
    }
})

export const { toggleCloseSidebar, toggleExamDropdown, toggleDropdown } = sidebarSlice.actions;
export default sidebarSlice.reducer;