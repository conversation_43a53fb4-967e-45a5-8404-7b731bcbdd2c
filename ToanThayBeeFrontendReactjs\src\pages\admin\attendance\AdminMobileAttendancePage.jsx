import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import AdminLayout from '../../../layouts/AdminLayout';
import ConfirmModal from '../../../components/modal/ConfirmModal';
import {
    Calendar,
    CheckCircle,
    XCircle,
    AlertCircle,
    User,
    Plus,
    ArrowLeft,
    Clock,
    BookOpen,
    Users,
    Search,
    Filter,
    Edit3,
    Save,
    Trash2
} from 'lucide-react';
import {
    fetchUserAttendancesAdmin,
    setSelectedYear,
    setSelectedMonth,
    clearUserAttendancesFilters,
    updateAttendance,
    deleteAttendance
} from '../../../features/attendance/attendanceSlice';
import { fetchUserById } from '../../../features/user/userSlice';
import { fetchUserAttendedLessons } from '../../../features/lesson/lessonSlice';

import { postAttendanceAPI } from '../../../services/attendanceApi';

const AdminMobileAttendancePage = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { userId } = useParams();
    const { student } = useSelector(state => state.users);
    const {
        userAttendances,
        userAttendancesLoading,
        userAttendancesError,
        selectedYear,
        selectedMonth
    } = useSelector(state => state.attendances);

    const [lessons, setLessons] = useState([]);
    const [showAddAttendance, setShowAddAttendance] = useState(false);
    const [newAttendance, setNewAttendance] = useState({
        lessonId: '',
        status: 'present',
        note: ''
    });
    const [loading, setLoading] = useState(false);
    const [showFilters, setShowFilters] = useState(false);

    // Edit attendance states
    const [editingAttendance, setEditingAttendance] = useState(null);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editFormData, setEditFormData] = useState({
        status: '',
        note: ''
    });

    // Confirm modal states
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [attendanceToDelete, setAttendanceToDelete] = useState(null);

    // Generate year options
    const currentYear = new Date().getFullYear();
    const yearOptions = Array.from({ length: 3 }, (_, i) => currentYear - i);

    // Month options
    const monthOptions = [
        { value: null, label: 'Tất cả tháng' },
        { value: 1, label: 'Tháng 1' },
        { value: 2, label: 'Tháng 2' },
        { value: 3, label: 'Tháng 3' },
        { value: 4, label: 'Tháng 4' },
        { value: 5, label: 'Tháng 5' },
        { value: 6, label: 'Tháng 6' },
        { value: 7, label: 'Tháng 7' },
        { value: 8, label: 'Tháng 8' },
        { value: 9, label: 'Tháng 9' },
        { value: 10, label: 'Tháng 10' },
        { value: 11, label: 'Tháng 11' },
        { value: 12, label: 'Tháng 12' }
    ];

    // Load user attendances
    useEffect(() => {
        if (userId) {
            const params = { year: selectedYear };
            if (selectedMonth) {
                params.month = selectedMonth;
            }
            dispatch(fetchUserAttendancesAdmin({ userId: parseInt(userId), params }));
        }
    }, [dispatch, userId, selectedYear, selectedMonth]);

    // Load user detail
    useEffect(() => {
        if (userId) {
            dispatch(fetchUserById(userId));
        }
    }, [dispatch, userId]);

    // Load lessons for attendance creation
    useEffect(() => {
        const loadLessons = async () => {
            try {
                const response = await dispatch(fetchUserAttendedLessons(userId));
                // console.log('Lessons:', response.payload);
                setLessons(response.payload)
            } catch (error) {
                console.error('Error loading lessons:', error);
            }
        };
        loadLessons();
    }, []);

    // Handle filter changes
    const handleYearChange = (year) => {
        dispatch(setSelectedYear(year));
    };

    const handleMonthChange = (month) => {
        dispatch(setSelectedMonth(month));
    };

    // Handle add attendance
    const handleAddAttendance = async () => {
        if (!newAttendance.lessonId) {
            alert('Vui lòng chọn buổi học');
            return;
        }

        setLoading(true);
        try {
            await postAttendanceAPI({
                userId: parseInt(userId),
                lessonId: parseInt(newAttendance.lessonId),
                status: newAttendance.status,
                note: newAttendance.note,
                attendanceTime: new Date().toISOString()
            });

            // Reload data
            const params = { year: selectedYear };
            if (selectedMonth) {
                params.month = selectedMonth;
            }
            dispatch(fetchUserAttendancesAdmin({ userId: parseInt(userId), params }));

            // Reset form
            setNewAttendance({
                lessonId: '',
                status: 'present',
                note: ''
            });
            setShowAddAttendance(false);
            // alert('Điểm danh thành công!');
        } catch (error) {
            alert('Có lỗi xảy ra khi điểm danh');
            console.error('Error adding attendance:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle edit attendance
    const handleEditAttendance = (attendance) => {
        setEditingAttendance(attendance);
        setEditFormData({
            status: attendance.status,
            note: attendance.note || ''
        });
        setShowEditModal(true);
    };

    // Handle update attendance
    const handleUpdateAttendance = async () => {
        if (!editingAttendance) return;

        setLoading(true);
        try {
            await dispatch(updateAttendance({
                attendanceId: editingAttendance.id,
                data: {
                    status: editFormData.status,
                    note: editFormData.note
                }
            }));

            // Reload data
            const params = { year: selectedYear };
            if (selectedMonth) {
                params.month = selectedMonth;
            }
            dispatch(fetchUserAttendancesAdmin({ userId: parseInt(userId), params }));

            setShowEditModal(false);
            setEditingAttendance(null);
            setEditFormData({ status: '', note: '' });
        } catch (error) {
            alert('Có lỗi xảy ra khi cập nhật điểm danh');
            console.error('Error updating attendance:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle delete attendance
    const handleDeleteAttendance = (attendanceId) => {
        setAttendanceToDelete(attendanceId);
        setShowConfirmModal(true);
    };

    // Confirm delete attendance
    const confirmDeleteAttendance = async () => {
        if (!attendanceToDelete) return;

        setLoading(true);
        try {
            await dispatch(deleteAttendance(attendanceToDelete));

            // Reload data
            const params = { year: selectedYear };
            if (selectedMonth) {
                params.month = selectedMonth;
            }
            dispatch(fetchUserAttendancesAdmin({ userId: parseInt(userId), params }));

            setShowConfirmModal(false);
            setAttendanceToDelete(null);
        } catch (error) {
            alert('Có lỗi xảy ra khi xóa điểm danh');
            console.error('Error deleting attendance:', error);
        } finally {
            setLoading(false);
        }
    };

    // Cancel delete attendance
    const cancelDeleteAttendance = () => {
        setShowConfirmModal(false);
        setAttendanceToDelete(null);
    };

    // Get status display
    const getStatusDisplay = (status) => {
        switch (status) {
            case 'present':
                return {
                    icon: <CheckCircle size={20} className="text-green-600" />,
                    text: 'Có mặt',
                    bgColor: 'bg-green-50',
                    textColor: 'text-green-700',
                    borderColor: 'border-green-200'
                };
            case 'absent':
                return {
                    icon: <XCircle size={20} className="text-red-600" />,
                    text: 'Vắng mặt',
                    bgColor: 'bg-red-50',
                    textColor: 'text-red-700',
                    borderColor: 'border-red-200'
                };
            case 'late':
                return {
                    icon: <AlertCircle size={20} className="text-yellow-600" />,
                    text: 'Muộn',
                    bgColor: 'bg-yellow-50',
                    textColor: 'text-yellow-700',
                    borderColor: 'border-yellow-200'
                };
            default:
                return {
                    icon: <XCircle size={20} className="text-gray-600" />,
                    text: 'Không xác định',
                    bgColor: 'bg-gray-50',
                    textColor: 'text-gray-700',
                    borderColor: 'border-gray-200'
                };
        }
    };

    // Format date
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    };

    // Format time
    const formatTime = (timeString) => {
        if (!timeString) return '';
        return timeString.substring(0, 5);
    };

    if (userAttendancesLoading) {
        return (
                <div className="min-h-screen bg-gray-50 p-4">
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-500"></div>
                    </div>
                </div>
        );
    }

    return (
            <div className="min-h-screen bg-gray-50">
                {/* Mobile Header */}
                <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
                    <div className="p-4">
                        <div className="flex items-center justify-between mb-4">
                            <button
                                onClick={() => navigate(-1)}
                                className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
                            >
                                <ArrowLeft size={20} />
                                <span>Quay lại</span>
                            </button>

                            <button
                                onClick={() => setShowFilters(!showFilters)}
                                className="flex items-center gap-2 px-3 py-2 bg-sky-50 text-sky-600 rounded-lg"
                            >
                                <Filter size={16} />
                                <span>Lọc</span>
                            </button>
                        </div>

                        <div className="flex items-center gap-3 mb-4">
                            <div className="p-2 bg-sky-100 rounded-full">
                                <User className="w-6 h-6 text-sky-600" />
                            </div>
                            <div>
                                <h1 className="text-lg font-bold text-gray-800">
                                    Điểm danh học viên
                                </h1>
                                <p className="text-sm text-gray-600">
                                    ID: {userId}
                                </p>
                                <p className="text-sm text-gray-600">
                                    {student?.lastName} {student?.firstName}
                                </p>
                                <p className="text-sm text-gray-600">
                                    {student?.highSchool} | {student?.class}
                                </p>
                                <p className="text-sm text-gray-600">
                                    {student?.phone}
                                </p>
                            </div>
                        </div>

                        {/* Filters */}
                        {showFilters && (
                            <div className="bg-gray-50 rounded-lg p-3 mb-4">
                                <div className="grid grid-cols-2 gap-3">
                                    <div>
                                        <label className="block text-xs font-medium text-gray-700 mb-1">
                                            Năm
                                        </label>
                                        <select
                                            value={selectedYear}
                                            onChange={(e) => handleYearChange(parseInt(e.target.value))}
                                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-sky-500"
                                        >
                                            {yearOptions.map(year => (
                                                <option key={year} value={year}>{year}</option>
                                            ))}
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block text-xs font-medium text-gray-700 mb-1">
                                            Tháng
                                        </label>
                                        <select
                                            value={selectedMonth || ''}
                                            onChange={(e) => handleMonthChange(e.target.value ? parseInt(e.target.value) : null)}
                                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-sky-500"
                                        >
                                            {monthOptions.map(option => (
                                                <option key={option.value || 'all'} value={option.value || ''}>
                                                    {option.label}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Quick Stats */}
                        {userAttendances.monthlyData.length > 0 && (
                            <div className="grid grid-cols-4 gap-2 mb-4">
                                <div className="bg-blue-50 rounded-lg p-2 text-center">
                                    <div className="text-lg font-bold text-blue-600">
                                        {userAttendances.totalAttendances}
                                    </div>
                                    <div className="text-xs text-blue-600">Tổng</div>
                                </div>
                                <div className="bg-green-50 rounded-lg p-2 text-center">
                                    <div className="text-lg font-bold text-green-600">
                                        {userAttendances.monthlyData.reduce((sum, month) => sum + month.summary.present, 0)}
                                    </div>
                                    <div className="text-xs text-green-600">Có mặt</div>
                                </div>
                                <div className="bg-red-50 rounded-lg p-2 text-center">
                                    <div className="text-lg font-bold text-red-600">
                                        {userAttendances.monthlyData.reduce((sum, month) => sum + month.summary.absent, 0)}
                                    </div>
                                    <div className="text-xs text-red-600">Vắng</div>
                                </div>
                                <div className="bg-yellow-50 rounded-lg p-2 text-center">
                                    <div className="text-lg font-bold text-yellow-600">
                                        {userAttendances.monthlyData.reduce((sum, month) => sum + month.summary.late, 0)}
                                    </div>
                                    <div className="text-xs text-yellow-600">Muộn</div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Content */}
                <div className="p-4">
                    {/* Add Attendance Button */}
                    <button
                        onClick={() => setShowAddAttendance(true)}
                        className="w-full mb-4 flex items-center justify-center gap-2 bg-sky-600 hover:bg-sky-700 text-white py-3 px-4 rounded-lg font-medium transition-colors"
                    >
                        <Plus size={20} />
                        <span>Thêm điểm danh mới</span>
                    </button>

                    {/* Error Message */}
                    {userAttendancesError && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                            <p className="text-sm">{userAttendancesError}</p>
                        </div>
                    )}

                    {/* Attendance List */}
                    {userAttendances.monthlyData.length === 0 ? (
                        <div className="bg-white rounded-lg p-8 text-center">
                            <Calendar size={48} className="mx-auto text-gray-300 mb-4" />
                            <h3 className="text-lg font-medium text-gray-800 mb-2">
                                Không có dữ liệu điểm danh
                            </h3>
                            <p className="text-gray-600 text-sm">
                                Chưa có buổi học nào được ghi nhận.
                            </p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {userAttendances.monthlyData.map((monthData) => (
                                <div key={monthData.month} className="bg-white rounded-lg shadow-sm">
                                    {/* Month Header */}
                                    <div className="p-4 border-b border-gray-200">
                                        <div className="flex justify-between items-center">
                                            <h3 className="font-semibold text-gray-800">
                                                Tháng {monthData.monthNumber}/{monthData.year}
                                            </h3>
                                            <div className="flex items-center gap-2 text-sm">
                                                <span className="flex items-center gap-1 text-green-600">
                                                    <CheckCircle size={14} />
                                                    {monthData.summary.present}
                                                </span>
                                                <span className="flex items-center gap-1 text-red-600">
                                                    <XCircle size={14} />
                                                    {monthData.summary.absent}
                                                </span>
                                                <span className="flex items-center gap-1 text-yellow-600">
                                                    <AlertCircle size={14} />
                                                    {monthData.summary.late}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Attendance Items */}
                                    <div className="p-4 space-y-3">
                                        {monthData.attendances.map((attendance) => {
                                            const statusDisplay = getStatusDisplay(attendance.status);
                                            return (
                                                <div key={attendance.id} className={`border rounded-lg p-3 ${statusDisplay.borderColor} ${statusDisplay.bgColor}`}>
                                                    <div className="flex items-start justify-between gap-3">
                                                        <div className="flex-1 min-w-0">
                                                            <div className="flex items-center gap-2 mb-2">
                                                                <BookOpen size={16} className="text-sky-600 flex-shrink-0" />
                                                                <h4 className="font-medium text-gray-800 text-sm truncate">
                                                                    {attendance.lesson.name}
                                                                </h4>
                                                            </div>

                                                            <div className="space-y-1 text-xs text-gray-600">
                                                                <div className="flex items-center gap-1">
                                                                    <Users size={12} />
                                                                    <span>{attendance.lesson.class.name}</span>
                                                                </div>
                                                                <div className="flex items-center gap-1">
                                                                    <Calendar size={12} />
                                                                    <span>{formatDate(attendance.createdAt)}</span>
                                                                </div>
                                                                {attendance.lesson.class.startTime1 && (
                                                                    <div className="flex items-center gap-1">
                                                                        <Clock size={12} />
                                                                        <span>
                                                                            {formatTime(attendance.lesson.class.startTime1)} - {formatTime(attendance.lesson.class.endTime1)}
                                                                        </span>
                                                                    </div>
                                                                )}
                                                            </div>

                                                            {attendance.note && (
                                                                <p className="text-xs text-gray-600 mt-2 italic">
                                                                    Ghi chú: {attendance.note}
                                                                </p>
                                                            )}
                                                        </div>

                                                        <div className="flex flex-col gap-2 items-end">
                                                            <div className={`px-2 py-1 rounded-full ${statusDisplay.bgColor} ${statusDisplay.textColor} border ${statusDisplay.borderColor} flex items-center gap-1 text-xs font-medium flex-shrink-0`}>
                                                                {statusDisplay.icon}
                                                                <span>{statusDisplay.text}</span>
                                                            </div>

                                                            {/* Action Buttons */}
                                                            <div className="flex gap-1">
                                                                <button
                                                                    onClick={() => handleEditAttendance(attendance)}
                                                                    className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                                                                    title="Chỉnh sửa"
                                                                >
                                                                    <Edit3 size={14} />
                                                                </button>
                                                                <button
                                                                    onClick={() => handleDeleteAttendance(attendance.id)}
                                                                    className="p-1 text-red-600 hover:bg-red-50 rounded"
                                                                    title="Xóa"
                                                                >
                                                                    <Trash2 size={14} />
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Add Attendance Modal */}
                {showAddAttendance && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50">
                        <div className="bg-white rounded-t-xl w-full max-w-md max-h-[80vh] overflow-y-auto">
                            <div className="p-4 border-b border-gray-200">
                                <div className="flex justify-between items-center">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                        Thêm điểm danh mới
                                    </h3>
                                    <button
                                        onClick={() => setShowAddAttendance(false)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <XCircle size={24} />
                                    </button>
                                </div>
                            </div>

                            <div className="p-4 space-y-4">
                                {/* Lesson Selection */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Chọn buổi học
                                    </label>
                                    <select
                                        value={newAttendance.lessonId}
                                        onChange={(e) => setNewAttendance({ ...newAttendance, lessonId: e.target.value })}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                                    >
                                        <option value="">Chọn buổi học...</option>
                                        {lessons.map(lesson => (
                                            <option key={lesson.id} value={lesson.id}>
                                                {lesson.name} - {lesson.class?.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                {/* Status Selection */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Trạng thái
                                    </label>
                                    <div className="grid grid-cols-3 gap-2">
                                        <button
                                            onClick={() => setNewAttendance({ ...newAttendance, status: 'present' })}
                                            className={`p-3 rounded-lg border text-sm font-medium ${newAttendance.status === 'present'
                                                    ? 'bg-green-50 border-green-200 text-green-700'
                                                    : 'bg-white border-gray-200 text-gray-700'
                                                }`}
                                        >
                                            <CheckCircle size={16} className="mx-auto mb-1" />
                                            Có mặt
                                        </button>
                                        <button
                                            onClick={() => setNewAttendance({ ...newAttendance, status: 'absent' })}
                                            className={`p-3 rounded-lg border text-sm font-medium ${newAttendance.status === 'absent'
                                                    ? 'bg-red-50 border-red-200 text-red-700'
                                                    : 'bg-white border-gray-200 text-gray-700'
                                                }`}
                                        >
                                            <XCircle size={16} className="mx-auto mb-1" />
                                            Vắng mặt
                                        </button>
                                        <button
                                            onClick={() => setNewAttendance({ ...newAttendance, status: 'late' })}
                                            className={`p-3 rounded-lg border text-sm font-medium ${newAttendance.status === 'late'
                                                    ? 'bg-yellow-50 border-yellow-200 text-yellow-700'
                                                    : 'bg-white border-gray-200 text-gray-700'
                                                }`}
                                        >
                                            <AlertCircle size={16} className="mx-auto mb-1" />
                                            Muộn
                                        </button>
                                    </div>
                                </div>

                                {/* Note */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Ghi chú (tùy chọn)
                                    </label>
                                    <textarea
                                        value={newAttendance.note}
                                        onChange={(e) => setNewAttendance({ ...newAttendance, note: e.target.value })}
                                        placeholder="Nhập ghi chú..."
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                                        rows={3}
                                    />
                                </div>

                                {/* Action Buttons */}
                                <div className="flex gap-3 pt-4">
                                    <button
                                        onClick={() => setShowAddAttendance(false)}
                                        className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                                    >
                                        Hủy
                                    </button>
                                    <button
                                        onClick={handleAddAttendance}
                                        disabled={loading || !newAttendance.lessonId}
                                        className="flex-1 px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {loading ? 'Đang lưu...' : 'Lưu'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Edit Attendance Modal */}
                {showEditModal && editingAttendance && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50">
                        <div className="bg-white rounded-t-xl w-full max-w-md max-h-[80vh] overflow-y-auto">
                            <div className="p-4 border-b border-gray-200">
                                <div className="flex justify-between items-center">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                        Chỉnh sửa điểm danh
                                    </h3>
                                    <button
                                        onClick={() => setShowEditModal(false)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <XCircle size={24} />
                                    </button>
                                </div>
                            </div>

                            <div className="p-4 space-y-4">
                                {/* Lesson Info (Read-only) */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Buổi học
                                    </label>
                                    <div className="p-3 bg-gray-50 rounded-lg">
                                        <div className="font-medium text-gray-800">
                                            {editingAttendance.lesson.name}
                                        </div>
                                        <div className="text-sm text-gray-600">
                                            {editingAttendance.lesson.class.name}
                                        </div>
                                    </div>
                                </div>

                                {/* Status Selection */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Trạng thái
                                    </label>
                                    <div className="grid grid-cols-3 gap-2">
                                        <button
                                            onClick={() => setEditFormData({ ...editFormData, status: 'present' })}
                                            className={`p-3 rounded-lg border text-sm font-medium ${editFormData.status === 'present'
                                                    ? 'bg-green-50 border-green-200 text-green-700'
                                                    : 'bg-white border-gray-200 text-gray-700'
                                                }`}
                                        >
                                            <CheckCircle size={16} className="mx-auto mb-1" />
                                            Có mặt
                                        </button>
                                        <button
                                            onClick={() => setEditFormData({ ...editFormData, status: 'absent' })}
                                            className={`p-3 rounded-lg border text-sm font-medium ${editFormData.status === 'absent'
                                                    ? 'bg-red-50 border-red-200 text-red-700'
                                                    : 'bg-white border-gray-200 text-gray-700'
                                                }`}
                                        >
                                            <XCircle size={16} className="mx-auto mb-1" />
                                            Vắng mặt
                                        </button>
                                        <button
                                            onClick={() => setEditFormData({ ...editFormData, status: 'late' })}
                                            className={`p-3 rounded-lg border text-sm font-medium ${editFormData.status === 'late'
                                                    ? 'bg-yellow-50 border-yellow-200 text-yellow-700'
                                                    : 'bg-white border-gray-200 text-gray-700'
                                                }`}
                                        >
                                            <AlertCircle size={16} className="mx-auto mb-1" />
                                            Muộn
                                        </button>
                                    </div>
                                </div>

                                {/* Note */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Ghi chú (tùy chọn)
                                    </label>
                                    <textarea
                                        value={editFormData.note}
                                        onChange={(e) => setEditFormData({ ...editFormData, note: e.target.value })}
                                        placeholder="Nhập ghi chú..."
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                                        rows={3}
                                    />
                                </div>

                                {/* Action Buttons */}
                                <div className="flex gap-3 pt-4">
                                    <button
                                        onClick={() => setShowEditModal(false)}
                                        className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                                    >
                                        Hủy
                                    </button>
                                    <button
                                        onClick={handleUpdateAttendance}
                                        disabled={loading}
                                        className="flex-1 px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {loading ? 'Đang cập nhật...' : 'Cập nhật'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Confirm Delete Modal */}
                <ConfirmModal
                    isOpen={showConfirmModal}
                    onClose={cancelDeleteAttendance}
                    onConfirm={confirmDeleteAttendance}
                    title="Xác nhận xóa điểm danh"
                    message="Bạn có chắc chắn muốn xóa điểm danh này? Hành động này không thể hoàn tác."
                />
            </div>
    );
};

export default AdminMobileAttendancePage;
