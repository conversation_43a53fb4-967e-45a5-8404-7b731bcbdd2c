import { FileText } from "lucide-react";
import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { setSelectedIndex } from "src/features/questionsExam/questionsExamSlice";
import LatexRenderer from "../latex/RenderLatex";
import MarkdownPreviewWithMath from "../latex/MarkDownPreview";
import { reorderStatements, reorderQuestions } from "src/features/questionsExam/questionsExamSlice";
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import SortableQuestionItem from "./SortableQuestionItem";
import QuestionContent from "./QuestionContent";


const QuestionViewHeader = ({ title, count, noQuestionText }) => {
    return (
        <div className=" p-3 ">
            <div className="flex items-center gap-1 mb-2">
                <FileText className="w-3 h-3 text-gray-600" />
                <h4 className="text-xs font-semibold text-gray-900">{title} ({count})</h4>
            </div>
            {count === 0 && (
                <div className="text-center py-4 text-gray-500">
                    <FileText className="w-6 h-6 mx-auto mb-1 opacity-50" />
                    <p className="text-xs">{noQuestionText}</p>
                </div>
            )}
        </div>
    )
}

const QuestionTNView = () => {
    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);
    const [questionsTN, setQuestionsTN] = useState([]);
    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];
    const dispatch = useDispatch();
    
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event) => {
        const { active, over } = event;

        if (active.id !== over?.id) {
            // Tìm index trong mảng gốc questionsExam, không phải mảng filtered
            const oldIndex = questionsExam.findIndex(q => q.id === active.id);
            const newIndex = questionsExam.findIndex(q => q.id === over.id);

            // Optimistic update: cập nhật local state ngay lập tức
            const oldIndexInFiltered = questionsTN.findIndex(q => q.id === active.id);
            const newIndexInFiltered = questionsTN.findIndex(q => q.id === over.id);

            if (oldIndexInFiltered !== -1 && newIndexInFiltered !== -1) {
                const newQuestionsTN = [...questionsTN];
                const [movedQuestion] = newQuestionsTN.splice(oldIndexInFiltered, 1);
                newQuestionsTN.splice(newIndexInFiltered, 0, movedQuestion);
                setQuestionsTN(newQuestionsTN);
            }

            // Dispatch Redux action
            dispatch(reorderQuestions({
                oldIndex,
                newIndex
            }));
        }
    };

    useEffect(() => {
        const filteredTN = questionsExam.filter(q => q.typeOfQuestion === 'TN');
        // Chỉ cập nhật nếu có sự thay đổi thực sự về số lượng hoặc id
        if (filteredTN.length !== questionsTN.length ||
            !filteredTN.every((q, index) => q.id === questionsTN[index]?.id)) {
            setQuestionsTN(filteredTN);
        }
    }, [questionsExam, questionsTN]);

    return (
        <>
            <QuestionViewHeader title="Câu hỏi trắc nghiệm" count={questionsTN.length} noQuestionText="Chưa có câu hỏi trắc nghiệm" />
            <div className="p-3 flex flex-col gap-4">
                <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                >
                    <SortableContext
                        items={questionsTN.map(q => q.id)}
                        strategy={verticalListSortingStrategy}
                    >
                        {questionsTN.map((q, index) => (
                            <SortableQuestionItem
                                key={q.id}
                                question={q}
                                index={index}
                            />
                        ))}
                    </SortableContext>
                </DndContext>
            </div>
        </>
    )
}

const QuestionDSView = () => {
    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);
    const [questionsDS, setQuestionsDS] = useState([]);
    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];
    const dispatch = useDispatch();
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event) => {
        const { active, over } = event;

        if (active.id !== over?.id) {
            // Tìm index trong mảng gốc questionsExam, không phải mảng filtered
            const oldIndex = questionsExam.findIndex(q => q.id === active.id);
            const newIndex = questionsExam.findIndex(q => q.id === over.id);

            // Optimistic update: cập nhật local state ngay lập tức
            const oldIndexInFiltered = questionsDS.findIndex(q => q.id === active.id);
            const newIndexInFiltered = questionsDS.findIndex(q => q.id === over.id);

            if (oldIndexInFiltered !== -1 && newIndexInFiltered !== -1) {
                const newQuestionsDS = [...questionsDS];
                const [movedQuestion] = newQuestionsDS.splice(oldIndexInFiltered, 1);
                newQuestionsDS.splice(newIndexInFiltered, 0, movedQuestion);
                setQuestionsDS(newQuestionsDS);
            }

            // Dispatch Redux action
            dispatch(reorderQuestions({
                oldIndex,
                newIndex
            }));
        }
    };
    useEffect(() => {
        const filteredDS = questionsExam.filter(q => q.typeOfQuestion === 'DS');
        // Chỉ cập nhật nếu có sự thay đổi thực sự về số lượng hoặc id
        if (filteredDS.length !== questionsDS.length ||
            !filteredDS.every((q, index) => q.id === questionsDS[index]?.id)) {
            setQuestionsDS(filteredDS);
        }
    }, [questionsExam, questionsDS]);

    return (
        <>
            <QuestionViewHeader title="Câu hỏi đúng sai" count={questionsDS.length} noQuestionText="Chưa có câu hỏi đúng sai" />
            <div className="p-3 flex flex-col gap-4">
                <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                >
                    <SortableContext
                        items={questionsDS.map(q => q.id)}
                        strategy={verticalListSortingStrategy}
                    >
                        {questionsDS.map((q, index) => (
                            <SortableQuestionItem
                                key={q.id}
                                question={q}
                                index={index}
                            />
                        ))}
                    </SortableContext>
                </DndContext>
            </div>
        </>
    )
}

const QuestionTLNView = () => {
    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);
    const dispatch = useDispatch();
    const [questionsTLN, setQuestionsTLN] = useState([]);
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event) => {
        const { active, over } = event;

        if (active.id !== over?.id) {
            // Tìm index trong mảng gốc questionsExam, không phải mảng filtered
            const oldIndex = questionsExam.findIndex(q => q.id === active.id);
            const newIndex = questionsExam.findIndex(q => q.id === over.id);

            // Optimistic update: cập nhật local state ngay lập tức
            const oldIndexInFiltered = questionsTLN.findIndex(q => q.id === active.id);
            const newIndexInFiltered = questionsTLN.findIndex(q => q.id === over.id);

            if (oldIndexInFiltered !== -1 && newIndexInFiltered !== -1) {
                const newQuestionsTLN = [...questionsTLN];
                const [movedQuestion] = newQuestionsTLN.splice(oldIndexInFiltered, 1);
                newQuestionsTLN.splice(newIndexInFiltered, 0, movedQuestion);
                setQuestionsTLN(newQuestionsTLN);
            }

            // Dispatch Redux action
            dispatch(reorderQuestions({
                oldIndex,
                newIndex
            }));
        }
    };
    useEffect(() => {
        const filteredTLN = questionsExam.filter(q => q.typeOfQuestion === 'TLN');
        // Chỉ cập nhật nếu có sự thay đổi thực sự về số lượng hoặc id
        if (filteredTLN.length !== questionsTLN.length ||
            !filteredTLN.every((q, index) => q.id === questionsTLN[index]?.id)) {
            setQuestionsTLN(filteredTLN);
        }
    }, [questionsExam, questionsTLN]);

    return (
        <>
            <QuestionViewHeader title="Câu hỏi trả lời ngắn" count={questionsTLN.length} noQuestionText="Chưa có câu hỏi trả lời ngắn" />
            <div className="p-3 flex flex-col gap-4">
                <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                >
                    <SortableContext
                        items={questionsTLN.map(q => q.id)}
                        strategy={verticalListSortingStrategy}
                    >
                        {questionsTLN.map((q, index) => (
                            <SortableQuestionItem
                                key={q.id}
                                question={q}
                                index={index}
                            />
                        ))}
                    </SortableContext>
                </DndContext>
            </div>
        </>
    )
}

const QuestionView = () => {

    return (
        <>
            <QuestionTNView />
            <hr className="border-gray-200" />
            <QuestionDSView />
            <hr className="border-gray-200" />
            <QuestionTLNView />
        </>
    )
}

export default QuestionView;