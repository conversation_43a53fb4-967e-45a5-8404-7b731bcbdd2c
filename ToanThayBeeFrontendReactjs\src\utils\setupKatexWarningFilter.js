/**
 * Global filter for KaTeX warnings
 * This should be imported and called once in the main App.js or index.js
 */

export const setupKatexWarningFilter = () => {
    // Store the original console.warn
    const originalWarn = console.warn;
    
    // Override console.warn with a filtered version
    console.warn = (...args) => {
        const message = args.join(' ');
        
        // Filter out KaTeX-related warnings that we don't want to see
        const katexWarningPatterns = [
            'No character metrics for',
            'Unrecognized Unicode character',
            'LaTeX-incompatible input and strict mode is set to',
            'unknownSymbol'
        ];
        
        // Check if this warning matches any KaTeX pattern
        const isKatexWarning = katexWarningPatterns.some(pattern => 
            message.includes(pattern)
        );
        
        // Only show the warning if it's not a KaTeX warning we want to suppress
        if (!isKatexWarning) {
            originalWarn.apply(console, args);
        }
    };
    
    // Return a function to restore original console.warn if needed
    return () => {
        console.warn = originalWarn;
    };
};
