export const processRegisterForm = (data) => {
    let processedData = { ...data };

    processedData.lastName = processedData.lastName ? processedData.lastName.trim() : "";
    processedData.firstName = processedData.firstName ? processedData.firstName.trim() : "";
    processedData.highSchool = processedData.highSchool ? processedData.highSchool.trim() : null;

    // Process phone number (now parent's phone)
    processedData.phone = processedData.phone && processedData.phone.trim() !== "" ? processedData.phone.trim() : null;

    // Process grade
    processedData.class = processedData.class || "10";

    // Username and password are now generated in the component, but we'll still trim them if they exist
    if (processedData.username) processedData.username = processedData.username.trim();
    if (processedData.password) processedData.password = processedData.password.trim();

    // Make sure classIds is an array of integers
    if (processedData.classIds) {
        processedData.classIds = processedData.classIds.map(id =>
            typeof id === 'string' ? parseInt(id, 10) : id
        );
    } else {
        processedData.classIds = [];
    }

    processedData.gender = processedData.gender === 1 ? true : processedData.gender === 0 ? false : -1;

    return processedData;
};
