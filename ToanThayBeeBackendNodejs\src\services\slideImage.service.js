import db from '../models/index.js';
import { Op } from 'sequelize';
import { uploadImage, cleanupUploadedFiles } from '../utils/imageUpload.js';
import * as classService from './class.service.js';
const { SlideImage, Slide } = db;

/**
 * <PERSON><PERSON>y tất cả các SlideImage theo slideId
 * @param {number} slideId - ID của slide
 * @param {Object} options - <PERSON><PERSON><PERSON> tù<PERSON> chọn bổ sung (transaction, ...)
 * @returns {Promise<Array>} Danh sách các SlideImage
 */
export const getSlideImageBySlideId = async (slideId, options = {}) => {
    const slideImages = await SlideImage.findAll({
        where: { slideId },
        ...options
    });
    return slideImages;
};

/**
 * L<PERSON>y thông tin Slide theo ID
 * @param {number} id - ID của slide
 * @param {Object} options - <PERSON><PERSON><PERSON> tù<PERSON> chọn bổ sung (transaction, ...)
 * @returns {Promise<Object>} Thông tin slide
 */
export const getSlideById = async (id, options = {}) => {
    const slide = await Slide.findByPk(id, options);
    return slide;
};

/**
 * Xóa SlideImage và Slide
 * @param {number} slideId - ID của slide cần xóa
 * @param {Array} imageUrls - Danh sách URL ảnh cần xóa trên Firebase
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<boolean>} Kết quả xóa
 */
export const deleteSlideImage = async (slideId, imageUrls, transaction = null) => {
    try {
        // Tạo options với transaction nếu có
        const options = transaction ? { transaction } : {};

        // 1. Xóa dữ liệu SlideImage
        await SlideImage.destroy({
            where: { slideId },
            ...options
        });

        // 2. Xóa Slide
        await Slide.destroy({
            where: { id: slideId },
            ...options
        });

        // 3. Xóa ảnh trên Firebase (không đưa vào transaction vì là thao tác bên ngoài DB)
        // Nếu có transaction, chỉ xóa ảnh sau khi transaction commit thành công
        if (transaction) {
            transaction.afterCommit(() => {
                if (imageUrls && imageUrls.length > 0) {
                    cleanupUploadedFiles(imageUrls).catch(err => {
                        console.error('Lỗi khi xóa ảnh trên Firebase:', err);
                    });
                }
            });
        } else {
            // Nếu không có transaction, xóa ảnh ngay lập tức
            if (imageUrls && imageUrls.length > 0) {
                await cleanupUploadedFiles(imageUrls);
            }
        }

        return true;
    } catch (error) {
        console.error('Xóa slide thất bại:', error);
        throw error; // Ném lỗi để caller có thể xử lý
    }
};

// Sử dụng lại hàm getClassById từ class.service.js

/**
 * Tìm slide theo ID kèm theo các ảnh liên quan
 * @param {number} slideId - ID của slide
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Thông tin slide và các ảnh
 */
export const findSlideWithImages = async (slideId, transaction = null) => {
    const options = {
        include: [{ model: db.SlideImage, as: 'slideImages' }]
    };

    if (transaction) {
        options.transaction = transaction;
    }

    const slide = await Slide.findByPk(slideId, options);
    return slide;
};

/**
 * Tạo slide mới
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Slide mới được tạo
 */
export const createSlide = async (transaction = null) => {
    const options = transaction ? { transaction } : {};
    const slide = await Slide.create({}, options);
    return slide;
};

/**
 * Xóa các ảnh slide theo danh sách ID
 * @param {Array} imageIds - Danh sách ID của các ảnh cần xóa
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<number>} Số lượng ảnh đã xóa
 */
export const deleteSlideImagesByIds = async (imageIds, transaction = null) => {
    if (!imageIds || imageIds.length === 0) {
        return 0;
    }

    const options = { where: { id: imageIds } };
    if (transaction) {
        options.transaction = transaction;
    }

    const result = await SlideImage.destroy(options);
    return result;
};

/**
 * Upload ảnh và tạo bản ghi SlideImage
 * @param {Object} file - File ảnh cần upload
 * @param {number} slideId - ID của slide
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Thông tin ảnh đã upload
 */
export const uploadAndCreateSlideImage = async (file, slideId, transaction = null) => {
    // Upload ảnh lên Firebase
    const imageUrl = await uploadImage(file, 'slideImage');

    // Tạo bản ghi SlideImage
    const options = transaction ? { transaction } : {};
    const slideImage = await SlideImage.create({ slideId, imageUrl }, options);

    return { slideImage, imageUrl };
};

/**
 * Cập nhật slideId cho lớp học
 * @param {Object} classItem - Đối tượng lớp học
 * @param {number} slideId - ID của slide
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Lớp học đã được cập nhật
 */
export const updateClassSlideId = async (classItem, slideId, transaction = null) => {
    classItem.slideId = slideId;

    const options = transaction ? { transaction } : {};
    await classItem.save(options);

    return classItem;
};

/**
 * Xử lý cập nhật slide cho lớp học
 * @param {number} classId - ID của lớp học
 * @param {number|null} slideId - ID của slide (nếu có)
 * @param {Array} files - Danh sách file ảnh cần upload
 * @param {Array} keepImageIds - Danh sách ID của các ảnh cần giữ lại
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Kết quả xử lý
 */
export const processSlideImagesUpdate = async (classId, slideId, files, keepImageIds, transaction) => {
    // Mảng lưu ảnh đã upload thành công
    const uploadedUrls = [];

    try {
        // Tìm lớp học
        const classItem = await classService.getClassById(classId, { transaction });
        if (!classItem) {
            throw new Error('Lớp không tồn tại.');
        }

        let slide;

        if (slideId) {
            // Trường hợp cập nhật slide hiện có
            slide = await findSlideWithImages(slideId, transaction);

            if (!slide) {
                throw new Error('Slide không tồn tại.');
            }

            // Xử lý xóa ảnh cũ
            const oldImages = slide.slideImages || [];
            const imagesToDelete = oldImages.filter(img => !keepImageIds.includes(img.id));
            const imageIdsToDelete = imagesToDelete.map(img => img.id);
            const urlsToDelete = imagesToDelete.map(img => img.imageUrl);

            if (imagesToDelete.length > 0) {
                await deleteSlideImagesByIds(imageIdsToDelete, transaction);

                // Xóa ảnh trên Firebase
                await cleanupUploadedFiles(urlsToDelete);
            }

            // Upload và tạo ảnh mới
            for (const file of files) {
                const { imageUrl } = await uploadAndCreateSlideImage(file, slideId, transaction);
                uploadedUrls.push(imageUrl);
            }
        } else {
            // Trường hợp tạo slide mới
            slide = await createSlide(transaction);

            // Upload và tạo ảnh mới
            for (const file of files) {
                const { imageUrl } = await uploadAndCreateSlideImage(file, slide.id, transaction);
                uploadedUrls.push(imageUrl);
            }

            // Cập nhật slideId cho lớp học
            await updateClassSlideId(classItem, slide.id, transaction);
        }

        return { success: true, uploadedUrls };
    } catch (error) {
        // Nếu có lỗi, trả về danh sách ảnh đã upload để xóa
        return { success: false, error, uploadedUrls };
    }
};