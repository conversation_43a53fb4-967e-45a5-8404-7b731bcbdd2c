import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import uploadGoogleImageMiddleware from '../middlewares/imageGoogleUpload.js'
import * as ExamController1 from '../controllers/Exam1Controller.js'
import uploadPDF from '../middlewares/pdfGoogleUpload.js'
import { checkAndSubmitTimedOutExams } from '../utils/examAutoSubmit.js'
import uploadAll from '../middlewares/upload.js'

const router = express.Router()

router.post('/v1/admin/exam1',
    asyncHandler(ExamController1.postExam1)
)

router.get('/v1/admin/exam1/:examId',
    async<PERSON>and<PERSON>(ExamController1.getAllQuestions1ByExamId)
)

router.get('/v1/admin/exam1',
    asyncHandler(ExamController1.getAllExams1)
)

router.put('/v1/admin/exam1/:id',
    asyncHandler(ExamController1.putExam1)
)

router.post('/v1/admin/exam1/:id/commit',
    asyncHandler(ExamController1.commitExam1ToExam)
)



export default router