import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchUserClasses } from "../../features/user/userSlice";
import { setSortOrder } from "../../features/user/userSlice";
import LoadingSpinner from "../loading/LoadingSpinner";
import { useNavigate } from "react-router-dom";
import { fetchCodesByType } from "../../features/code/codeSlice";
import { acceptStudentClass } from "../../features/class/classSlice";
import { TotalComponent } from "./TotalComponent";

const UserClassTable = ({ classId, onKickStudent }) => {
    const dispatch = useDispatch();
    const { users } = useSelector((state) => state.users);
    const { search, pagination } = useSelector(state => state.users);
    const { page, pageSize, total, totalPages, sortOrder } = pagination;
    const { loading } = useSelector(state => state.states);
    const navigate = useNavigate();
    const { codes } = useSelector(state => state.codes);

    useEffect(() => {
        dispatch(fetchCodesByType("wait status"));
    }, [dispatch]);

    const handleApprove = (userId) => {
        dispatch(acceptStudentClass({ classId, studentId: userId }))
            .unwrap()
            .then(() => {
                dispatch(fetchUserClasses({ id: classId, search, currentPage: page, limit: pageSize, sortOrder }))
            })
    };

    useEffect(() => {
        dispatch(fetchUserClasses({ id: classId, search, currentPage: page, limit: pageSize, sortOrder }))
    }, [dispatch, search, page, pageSize, sortOrder]);

    if (loading) return (
        <div className="flex items-center justify-center h-screen">
            <LoadingSpinner color="border-black" size="5rem" />
        </div>
    )

    return (
        <div className="flex flex-col h-full gap-4 text-sm">
            {/* Header with pagination info */}
            <TotalComponent
                total={total}
                page={page}
                pageSize={pageSize}
                setSortOrder={() => dispatch(setSortOrder())}
            />
            {/* Table container - takes remaining space */}
            <div className="flex-1 overflow-hidden border border-[#E7E7ED] rounded-lg">
                <div className="h-full overflow-y-auto hide-scrollbar">
                    <table className="w-full border-collapse">
                        <thead className="bg-[#F6FAFD]">
                            <tr>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">ID</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Họ và Tên</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Loại Người Dùng</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Giới Tính</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Ngày Sinh</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Số Điện Thoại</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Trường Học</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Lớp</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Trạng Thái</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Ngày tham gia</th>
                                <th className="p-3 text-center border-b border-[#E7E7ED]">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            {users.map((user, index) => (
                                <tr
                                    key={user.id}
                                    onClick={() => navigate(`/admin/student-management/${user.id}`)}
                                    className="border-b border-[#E7E7ED] hover:bg-gray-50 cursor-pointer">
                                    <td className="p-3 text-center">{user.id}</td>
                                    <td className="p-3 text-center">{user.lastName} {user.firstName}</td>
                                    <td className="p-3 text-center">{user.userType}</td>
                                    <td className="p-3 text-center">{user.gender ? "Nam" : "Nữ"}</td>
                                    <td className="p-3 text-center">
                                        {user.birthDate ? new Date(user.birthDate).toLocaleDateString() : "Chưa cập nhật"}
                                    </td>
                                    <td className="p-3 text-center">{user.phone || "Chưa có"}</td>
                                    <td className="p-3 text-center">{user.highSchool || "Chưa cập nhật"}</td>
                                    <td className="p-3 text-center">{user.class || "Chưa cập nhật"}</td>
                                    <td className={`p-3 text-center ${user.studentClassStatus === 'JS' ? "text-green-500" : user.studentClassStatus === 'WS' ? "text-yellow-500" : "text-red-500"}`}>
                                        {
                                            user.studentClassStatus === 'WS' ? (
                                                <button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleApprove(user.id);
                                                    }}
                                                    className="text-yellow-600 underline hover:text-yellow-800"
                                                >
                                                    Phê duyệt
                                                </button>
                                            ) : (
                                                codes['wait status']?.find((code) => code.code === user.studentClassStatus)?.description || "Chưa cập nhật"
                                            )
                                        }
                                    </td>
                                    <td className="p-3 text-center">
                                        {new Date(user.createdAt).toLocaleDateString()}
                                    </td>
                                    <td className="p-3 text-center">
                                        <div className="flex justify-center gap-2">
                                            {user.studentClassStatus === 'JS' && onKickStudent && (
                                                <button
                                                    onClick={(e) => {
                                                        e.stopPropagation(); // Prevent row click
                                                        onKickStudent(user.id, `${user.lastName} ${user.firstName}`);
                                                    }}
                                                    className="text-red-600 hover:text-red-800 underline text-sm"
                                                >
                                                    Kick
                                                </button>
                                            )}
                                            {user.studentClassStatus !== 'JS' && (
                                                <span className="text-gray-400 text-sm">-</span>
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );

};

export default UserClassTable;
