import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import Roles from '../constants/Roles.js'
import uploadGoogleImageMiddleware from '../middlewares/imageGoogleUpload.js'
import { handleMulterError } from '../middlewares/handelMulter.js'
import * as AchievementController from '../controllers/AchievementController.js'


const router = express.Router()

// Achievement Categories routes
// lấy tất cả danh mục thành tích ai cũng có thể xem
router.get('/v1/achievement/categories',
    asyncHandler(AchievementController.getAllAchievementCategories)
)

// lấy danh mục thành tích theo id chỉ có marketing và admin mới có thể xem
router.get('/v1/achievement/categories/:id',
    requireRoles(Roles.JustMarketing),
    asyncHandler(AchievementController.getAchievementCategoryById)
)

// tạo mới danh mục thành tích chỉ có marketing mới có thể tạo
router.post('/v1/admin/achievement/categories',
    requireRoles(Roles.JustMarketing),
    asyncHandler(AchievementController.createAchievementCategory)
)

// cập nhật danh mục thành tích chỉ có marketing mới có thể cập nhật
router.put('/v1/admin/achievement/categories/:id',
    requireRoles(Roles.JustMarketing),
    asyncHandler(AchievementController.updateAchievementCategory)
)

// xóa danh mục thành tích chỉ có marketing mới có thể xóa
router.delete('/v1/admin/achievement/categories/:id',
    requireRoles(Roles.JustMarketing),
    asyncHandler(AchievementController.deleteAchievementCategory)
)

// Achievement Stats routes
router.get('/v1/achievement/stats',
    asyncHandler(AchievementController.getAllAchievementStats)
)

router.get('/v1/achievement/stats/:id',
    asyncHandler(AchievementController.getAchievementStatById)
)

// chỉ có marketing mới có thể tạo thành tích
router.post('/v1/admin/achievement/stats',
    requireRoles(Roles.JustMarketing),
    asyncHandler(AchievementController.createAchievementStat)
)

// chỉ có marketing mới có thể cập nhật thành tích
router.put('/v1/admin/achievement/stats/:id',
    requireRoles(Roles.JustMarketing),
    asyncHandler(AchievementController.updateAchievementStat)
)

// chỉ có marketing mới có thể xóa thành tích
router.delete('/v1/admin/achievement/stats/:id',
    requireRoles(Roles.JustMarketing),
    asyncHandler(AchievementController.deleteAchievementStat)
)

// Achievement Images routes
router.get('/v1/achievement/images',
    asyncHandler(AchievementController.getAllAchievementImages)
)

router.get('/v1/achievement/images/:id',
    asyncHandler(AchievementController.getAchievementImageById)
)

// chỉ có marketing mới có thể tạo ảnh cho thành tích
router.post('/v1/admin/achievement/images',
    requireRoles(Roles.JustMarketing),
    uploadGoogleImageMiddleware.single('image'),
    handleMulterError,
    asyncHandler(AchievementController.createAchievementImage)
)

// chỉ có marketing mới có thể cập nhật ảnh cho thành tích
router.put('/v1/admin/achievement/images/:id',
    requireRoles(Roles.JustMarketing),
    uploadGoogleImageMiddleware.single('image'),
    handleMulterError,
    asyncHandler(AchievementController.updateAchievementImage)
)

// chỉ có marketing mới có thể xóa ảnh cho thành tích
router.delete('/v1/admin/achievement/images/:id',
    requireRoles(Roles.JustMarketing),
    asyncHandler(AchievementController.deleteAchievementImage)
)

export default router
