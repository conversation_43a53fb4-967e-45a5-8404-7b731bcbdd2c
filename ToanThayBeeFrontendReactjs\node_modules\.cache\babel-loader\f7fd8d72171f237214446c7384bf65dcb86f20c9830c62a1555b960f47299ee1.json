{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\SortableStatementsContainer.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/questionsExam/questionsExamSlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableStatementsContainer = _ref => {\n  _s();\n  let {\n    question\n  } = _ref;\n  const dispatch = useDispatch();\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n  const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    console.log('Statement drag end:', {\n      activeId: active.id,\n      overId: over === null || over === void 0 ? void 0 : over.id\n    });\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      const oldIndex = question.statements.findIndex((item, idx) => \"\".concat(item.id || idx) === active.id);\n      const newIndex = question.statements.findIndex((item, idx) => \"\".concat(item.id || idx) === over.id);\n      console.log('Statement indices:', {\n        oldIndex,\n        newIndex,\n        questionId: question.id\n      });\n      if (oldIndex !== -1 && newIndex !== -1) {\n        dispatch(reorderStatements({\n          questionId: question.id,\n          oldIndex,\n          newIndex\n        }));\n      }\n    }\n  };\n  const getPrefix = index => {\n    if (question.typeOfQuestion === \"TN\") {\n      return prefixTN[index] || \"\".concat(String.fromCharCode(65 + index), \".\");\n    } else if (question.typeOfQuestion === \"DS\") {\n      return prefixDS[index] || \"\".concat(String.fromCharCode(97 + index), \")\");\n    }\n    return \"\".concat(index + 1, \".\");\n  };\n  if (question.typeOfQuestion === \"TLN\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-800 mt-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"\\u0110\\xE1p \\xE1n: \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: question.correctAnswer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this);\n  }\n  if (!question.statements || question.statements.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2\",\n    children: /*#__PURE__*/_jsxDEV(DndContext, {\n      sensors: sensors,\n      collisionDetection: closestCenter,\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(SortableContext, {\n        items: question.statements.map((item, idx) => \"\".concat(item.id || idx)),\n        strategy: verticalListSortingStrategy,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1\",\n          children: question.statements.map((item, idx) => /*#__PURE__*/_jsxDEV(SortableStatementItem, {\n            statement: item,\n            index: idx,\n            prefix: getPrefix(idx),\n            isCorrect: item.isCorrect,\n            questionType: question.typeOfQuestion\n          }, \"\".concat(item.id || idx), false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableStatementsContainer, \"fKWeCsq/OsPww/eQUkThDyU1Mpg=\", false, function () {\n  return [useDispatch, useSensors, useSensor, useSensor];\n});\n_c = SortableStatementsContainer;\nexport default SortableStatementsContainer;\nvar _c;\n$RefreshReg$(_c, \"SortableStatementsContainer\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "reorderStatements", "SortableStatementItem", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SortableStatementsContainer", "_ref", "_s", "question", "dispatch", "sensors", "activationConstraint", "distance", "coordinateGetter", "prefixTN", "prefixDS", "handleDragEnd", "event", "active", "over", "console", "log", "activeId", "id", "overId", "oldIndex", "statements", "findIndex", "item", "idx", "concat", "newIndex", "questionId", "getPrefix", "index", "typeOfQuestion", "String", "fromCharCode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "length", "collisionDetection", "onDragEnd", "items", "map", "strategy", "statement", "prefix", "isCorrect", "questionType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/SortableStatementsContainer.jsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/questionsExam/questionsExamSlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport {\n    DndContext,\n    closestCenter,\n    KeyboardSensor,\n    PointerSensor,\n    useSensor,\n    useSensors,\n} from '@dnd-kit/core';\nimport {\n    SortableContext,\n    sortableKeyboardCoordinates,\n    verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport LatexRenderer from \"../latex/RenderLatex\";\n\nconst SortableStatementsContainer = ({ question }) => {\n    const dispatch = useDispatch();\n    const sensors = useSensors(\n        useSensor(PointerSensor, {\n            activationConstraint: {\n                distance: 8,\n            },\n        }),\n        useSensor(KeyboardSensor, {\n            coordinateGetter: sortableKeyboardCoordinates,\n        })\n    );\n\n    const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n    const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n\n    const handleDragEnd = (event) => {\n        const { active, over } = event;\n        console.log('Statement drag end:', { activeId: active.id, overId: over?.id });\n\n        if (active.id !== over?.id) {\n            const oldIndex = question.statements.findIndex((item, idx) =>\n                `${item.id || idx}` === active.id\n            );\n            const newIndex = question.statements.findIndex((item, idx) =>\n                `${item.id || idx}` === over.id\n            );\n\n            console.log('Statement indices:', { oldIndex, newIndex, questionId: question.id });\n\n            if (oldIndex !== -1 && newIndex !== -1) {\n                dispatch(reorderStatements({\n                    questionId: question.id,\n                    oldIndex,\n                    newIndex\n                }));\n            }\n        }\n    };\n\n    const getPrefix = (index) => {\n        if (question.typeOfQuestion === \"TN\") {\n            return prefixTN[index] || `${String.fromCharCode(65 + index)}.`;\n        } else if (question.typeOfQuestion === \"DS\") {\n            return prefixDS[index] || `${String.fromCharCode(97 + index)})`;\n        }\n        return `${index + 1}.`;\n    };\n\n    if (question.typeOfQuestion === \"TLN\") {\n        return (\n            <div className=\"text-gray-800 mt-2\">\n                <span className=\"font-semibold\">Đáp án: </span>\n                <span>{question.correctAnswer}</span>\n            </div>\n        );\n    }\n\n    if (!question.statements || question.statements.length === 0) {\n        return null;\n    }\n\n    return (\n        <div className=\"mt-2\">\n            <DndContext\n                sensors={sensors}\n                collisionDetection={closestCenter}\n                onDragEnd={handleDragEnd}\n            >\n                <SortableContext\n                    items={question.statements.map((item, idx) => `${item.id || idx}`)}\n                    strategy={verticalListSortingStrategy}\n                >\n                    <div className=\"space-y-1\">\n                        {question.statements.map((item, idx) => (\n                            <SortableStatementItem\n                                key={`${item.id || idx}`}\n                                statement={item}\n                                index={idx}\n                                prefix={getPrefix(idx)}\n                                isCorrect={item.isCorrect}\n                                questionType={question.typeOfQuestion}\n                            />\n                        ))}\n                    </div>\n                </SortableContext>\n            </DndContext>\n        </div>\n    );\n};\n\nexport default SortableStatementsContainer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAC1B,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,2BAA2B,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC7C,MAAMG,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,OAAO,GAAGZ,UAAU,CACtBD,SAAS,CAACD,aAAa,EAAE;IACrBe,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACFf,SAAS,CAACF,cAAc,EAAE;IACtBkB,gBAAgB,EAAEb;EACtB,CAAC,CACL,CAAC;EAED,MAAMc,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAC9BG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MAAEC,QAAQ,EAAEJ,MAAM,CAACK,EAAE;MAAEC,MAAM,EAAEL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI;IAAG,CAAC,CAAC;IAE7E,IAAIL,MAAM,CAACK,EAAE,MAAKJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,EAAE,GAAE;MACxB,MAAME,QAAQ,GAAGjB,QAAQ,CAACkB,UAAU,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACrD,GAAAC,MAAA,CAAGF,IAAI,CAACL,EAAE,IAAIM,GAAG,MAAOX,MAAM,CAACK,EACnC,CAAC;MACD,MAAMQ,QAAQ,GAAGvB,QAAQ,CAACkB,UAAU,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACrD,GAAAC,MAAA,CAAGF,IAAI,CAACL,EAAE,IAAIM,GAAG,MAAOV,IAAI,CAACI,EACjC,CAAC;MAEDH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;QAAEI,QAAQ;QAAEM,QAAQ;QAAEC,UAAU,EAAExB,QAAQ,CAACe;MAAG,CAAC,CAAC;MAElF,IAAIE,QAAQ,KAAK,CAAC,CAAC,IAAIM,QAAQ,KAAK,CAAC,CAAC,EAAE;QACpCtB,QAAQ,CAAClB,iBAAiB,CAAC;UACvByC,UAAU,EAAExB,QAAQ,CAACe,EAAE;UACvBE,QAAQ;UACRM;QACJ,CAAC,CAAC,CAAC;MACP;IACJ;EACJ,CAAC;EAED,MAAME,SAAS,GAAIC,KAAK,IAAK;IACzB,IAAI1B,QAAQ,CAAC2B,cAAc,KAAK,IAAI,EAAE;MAClC,OAAOrB,QAAQ,CAACoB,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE,CAAC,MAAM,IAAI1B,QAAQ,CAAC2B,cAAc,KAAK,IAAI,EAAE;MACzC,OAAOpB,QAAQ,CAACmB,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE;IACA,UAAAJ,MAAA,CAAUI,KAAK,GAAG,CAAC;EACvB,CAAC;EAED,IAAI1B,QAAQ,CAAC2B,cAAc,KAAK,KAAK,EAAE;IACnC,oBACI/B,OAAA;MAAKkC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBAC/BnC,OAAA;QAAMkC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CvC,OAAA;QAAAmC,QAAA,EAAO/B,QAAQ,CAACoC;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEd;EAEA,IAAI,CAACnC,QAAQ,CAACkB,UAAU,IAAIlB,QAAQ,CAACkB,UAAU,CAACmB,MAAM,KAAK,CAAC,EAAE;IAC1D,OAAO,IAAI;EACf;EAEA,oBACIzC,OAAA;IAAKkC,SAAS,EAAC,MAAM;IAAAC,QAAA,eACjBnC,OAAA,CAACX,UAAU;MACPiB,OAAO,EAAEA,OAAQ;MACjBoC,kBAAkB,EAAEpD,aAAc;MAClCqD,SAAS,EAAE/B,aAAc;MAAAuB,QAAA,eAEzBnC,OAAA,CAACL,eAAe;QACZiD,KAAK,EAAExC,QAAQ,CAACkB,UAAU,CAACuB,GAAG,CAAC,CAACrB,IAAI,EAAEC,GAAG,QAAAC,MAAA,CAAQF,IAAI,CAACL,EAAE,IAAIM,GAAG,CAAE,CAAE;QACnEqB,QAAQ,EAAEjD,2BAA4B;QAAAsC,QAAA,eAEtCnC,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACrB/B,QAAQ,CAACkB,UAAU,CAACuB,GAAG,CAAC,CAACrB,IAAI,EAAEC,GAAG,kBAC/BzB,OAAA,CAACZ,qBAAqB;YAElB2D,SAAS,EAAEvB,IAAK;YAChBM,KAAK,EAAEL,GAAI;YACXuB,MAAM,EAAEnB,SAAS,CAACJ,GAAG,CAAE;YACvBwB,SAAS,EAAEzB,IAAI,CAACyB,SAAU;YAC1BC,YAAY,EAAE9C,QAAQ,CAAC2B;UAAe,MAAAL,MAAA,CAL9BF,IAAI,CAACL,EAAE,IAAIM,GAAG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMzB,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEd,CAAC;AAACpC,EAAA,CAzFIF,2BAA2B;EAAA,QACZhB,WAAW,EACZS,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAA0D,EAAA,GARXlD,2BAA2B;AA2FjC,eAAeA,2BAA2B;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}