{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\layouts\\\\ExamAdminLayout.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams, useLocation } from 'react-router-dom';\nimport AdminSidebar from '../components/sidebar/AdminSidebar';\nimport { Home } from 'lucide-react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { fetchExamById } from '../features/exam/examSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamAdminLayout = _ref => {\n  _s();\n  var _tabs$find;\n  let {\n    children\n  } = _ref;\n  const {\n    examId\n  } = useParams();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    exam\n  } = useSelector(state => state.exams);\n  const [tabs, setTabs] = useState([{\n    name: '<PERSON> tiết',\n    path: \"/admin/exam-management/\".concat(examId),\n    active: true\n  }, {\n    name: 'Danh sách câu hỏi',\n    path: \"/admin/exam-management/\".concat(examId, \"/questions\"),\n    active: false\n  }, {\n    name: 'Xem trước',\n    path: \"/admin/exam-management/\".concat(examId, \"/preview\"),\n    active: false\n  }, {\n    name: 'Theo dõi',\n    path: \"/admin/exam-management/\".concat(examId, \"/tracking\"),\n    active: false\n  }]);\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n  const handleTabClick = tab => {\n    setTabs(prevTabs => prevTabs.map(t => ({\n      ...t,\n      active: t.name === tab.name\n    })));\n    navigate(tab.path);\n  };\n  useEffect(() => {\n    setTabs(prevTabs => prevTabs.map(tab => ({\n      ...tab,\n      active: location.pathname === tab.path\n    })));\n  }, [location.pathname]);\n  useEffect(() => {\n    if (examId && String(exam === null || exam === void 0 ? void 0 : exam.id) !== String(examId)) {\n      dispatch(fetchExamById(examId));\n    }\n  }, [examId, dispatch, exam === null || exam === void 0 ? void 0 : exam.id]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 transition-all duration-300 \".concat(closeSidebar ? 'ml-[104px]' : 'ml-64'),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col min-h-screen\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white shadow-sm border-b border-gray-200 px-6 py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(Home, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Trang ch\\u1EE7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"/\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Qu\\u1EA3n l\\xFD \\u0111\\u1EC1 thi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"/\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold\",\n                children: (_tabs$find = tabs.find(tab => tab.active)) === null || _tabs$find === void 0 ? void 0 : _tabs$find.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900 mt-2\",\n            children: [\"Chi ti\\u1EBFt \\u0111\\u1EC1 thi - \", (exam === null || exam === void 0 ? void 0 : exam.name) || examId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white border-b border-gray-200 px-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-8\",\n            children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleTabClick(tab),\n              className: \"py-3 px-4 text-sm font-medium \".concat(tab.active ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'),\n              children: tab.name\n            }, tab.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this), children]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 9\n  }, this);\n};\n_s(ExamAdminLayout, \"uCBnFCum8Ukj7h7f8GaQsyz12cI=\", false, function () {\n  return [useParams, useLocation, useNavigate, useDispatch, useSelector, useSelector];\n});\n_c = ExamAdminLayout;\nexport default ExamAdminLayout;\nvar _c;\n$RefreshReg$(_c, \"ExamAdminLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useParams", "useLocation", "AdminSidebar", "Home", "useDispatch", "useSelector", "fetchExamById", "jsxDEV", "_jsxDEV", "ExamAdminLayout", "_ref", "_s", "_tabs$find", "children", "examId", "location", "navigate", "dispatch", "exam", "state", "exams", "tabs", "setTabs", "name", "path", "concat", "active", "closeSidebar", "sidebar", "handleTabClick", "tab", "prevTabs", "map", "t", "pathname", "String", "id", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "find", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/layouts/ExamAdminLayout.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate, useParams, useLocation } from 'react-router-dom';\r\nimport AdminSidebar from '../components/sidebar/AdminSidebar';\r\nimport { Home } from 'lucide-react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { fetchExamById } from '../features/exam/examSlice';\r\n\r\n\r\nconst ExamAdminLayout = ({ children }) => {\r\n    const { examId } = useParams();\r\n    const location = useLocation();\r\n    const navigate = useNavigate();\r\n    const dispatch = useDispatch();\r\n    const { exam } = useSelector(state => state.exams);\r\n    const [tabs, setTabs] = useState([\r\n        { name: '<PERSON> tiết', path: `/admin/exam-management/${examId}`, active: true },\r\n        { name: '<PERSON>h sách câu hỏi', path: `/admin/exam-management/${examId}/questions`, active: false },\r\n        { name: 'Xem trước', path: `/admin/exam-management/${examId}/preview`, active: false },\r\n        { name: '<PERSON>õ<PERSON>', path: `/admin/exam-management/${examId}/tracking`, active: false }\r\n    ]);\r\n    const { closeSidebar } = useSelector((state) => state.sidebar);\r\n\r\n    const handleTabClick = (tab) => {\r\n        setTabs(prevTabs => prevTabs.map(t => ({\r\n            ...t,\r\n            active: t.name === tab.name\r\n        })));\r\n        navigate(tab.path);\r\n    };\r\n\r\n    useEffect(() => {\r\n        setTabs(prevTabs =>\r\n            prevTabs.map(tab => ({\r\n                ...tab,\r\n                active: location.pathname === tab.path\r\n            }))\r\n        );\r\n    }, [location.pathname]);\r\n\r\n    useEffect(() => {\r\n        if (examId && String(exam?.id) !== String(examId)) {\r\n            dispatch(fetchExamById(examId));\r\n        }\r\n    }, [examId, dispatch, exam?.id]);\r\n\r\n    return (\r\n        <div className=\"flex min-h-screen bg-gray-50\">\r\n            <AdminSidebar />\r\n            <div className={`flex-1 transition-all duration-300 ${closeSidebar ? 'ml-[104px]' : 'ml-64'}`}>\r\n                <div className=\"flex flex-col min-h-screen\">\r\n                    {/* Header */}\r\n                    <div className=\"bg-white shadow-sm border-b border-gray-200 px-6 py-4\">\r\n                        <div className=\"flex items-center gap-4\">\r\n                            {/* Breadcrumb */}\r\n                            <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                                <Home size={16} />\r\n                                <span>Trang chủ</span>\r\n                                <span>/</span>\r\n                                <span>Quản lý đề thi</span>\r\n                                <span>/</span>\r\n                                <span className=\"font-semibold\">{tabs.find(tab => tab.active)?.name}</span>\r\n                            </div>\r\n                        </div>\r\n                        <h1 className=\"text-2xl font-bold text-gray-900 mt-2\">Chi tiết đề thi - {exam?.name || examId}</h1>\r\n                    </div>\r\n\r\n                    {/* Navigation Tabs */}\r\n                    <div className=\"bg-white border-b border-gray-200 px-6\">\r\n                        <div className=\"flex space-x-8\">\r\n                            {tabs.map((tab) => (\r\n                                <button\r\n                                    key={tab.name}\r\n                                    onClick={() => handleTabClick(tab)}\r\n                                    className={`py-3 px-4 text-sm font-medium ${tab.active ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}\r\n                                >\r\n                                    {tab.name}\r\n                                </button>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                    {/* Main Content */}\r\n                    {children}\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n    );\r\n};\r\n\r\nexport default ExamAdminLayout;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,aAAa,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3D,MAAMC,eAAe,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAAC,UAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAH,IAAA;EACjC,MAAM;IAAEI;EAAO,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc;EAAK,CAAC,GAAGb,WAAW,CAACc,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;EAClD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,CAC7B;IAAE0B,IAAI,EAAE,UAAU;IAAEC,IAAI,4BAAAC,MAAA,CAA4BX,MAAM,CAAE;IAAEY,MAAM,EAAE;EAAK,CAAC,EAC5E;IAAEH,IAAI,EAAE,mBAAmB;IAAEC,IAAI,4BAAAC,MAAA,CAA4BX,MAAM,eAAY;IAAEY,MAAM,EAAE;EAAM,CAAC,EAChG;IAAEH,IAAI,EAAE,WAAW;IAAEC,IAAI,4BAAAC,MAAA,CAA4BX,MAAM,aAAU;IAAEY,MAAM,EAAE;EAAM,CAAC,EACtF;IAAEH,IAAI,EAAE,UAAU;IAAEC,IAAI,4BAAAC,MAAA,CAA4BX,MAAM,cAAW;IAAEY,MAAM,EAAE;EAAM,CAAC,CACzF,CAAC;EACF,MAAM;IAAEC;EAAa,CAAC,GAAGtB,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACS,OAAO,CAAC;EAE9D,MAAMC,cAAc,GAAIC,GAAG,IAAK;IAC5BR,OAAO,CAACS,QAAQ,IAAIA,QAAQ,CAACC,GAAG,CAACC,CAAC,KAAK;MACnC,GAAGA,CAAC;MACJP,MAAM,EAAEO,CAAC,CAACV,IAAI,KAAKO,GAAG,CAACP;IAC3B,CAAC,CAAC,CAAC,CAAC;IACJP,QAAQ,CAACc,GAAG,CAACN,IAAI,CAAC;EACtB,CAAC;EAED1B,SAAS,CAAC,MAAM;IACZwB,OAAO,CAACS,QAAQ,IACZA,QAAQ,CAACC,GAAG,CAACF,GAAG,KAAK;MACjB,GAAGA,GAAG;MACNJ,MAAM,EAAEX,QAAQ,CAACmB,QAAQ,KAAKJ,GAAG,CAACN;IACtC,CAAC,CAAC,CACN,CAAC;EACL,CAAC,EAAE,CAACT,QAAQ,CAACmB,QAAQ,CAAC,CAAC;EAEvBpC,SAAS,CAAC,MAAM;IACZ,IAAIgB,MAAM,IAAIqB,MAAM,CAACjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,EAAE,CAAC,KAAKD,MAAM,CAACrB,MAAM,CAAC,EAAE;MAC/CG,QAAQ,CAACX,aAAa,CAACQ,MAAM,CAAC,CAAC;IACnC;EACJ,CAAC,EAAE,CAACA,MAAM,EAAEG,QAAQ,EAAEC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,EAAE,CAAC,CAAC;EAEhC,oBACI5B,OAAA;IAAK6B,SAAS,EAAC,8BAA8B;IAAAxB,QAAA,gBACzCL,OAAA,CAACN,YAAY;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBjC,OAAA;MAAK6B,SAAS,wCAAAZ,MAAA,CAAwCE,YAAY,GAAG,YAAY,GAAG,OAAO,CAAG;MAAAd,QAAA,eAC1FL,OAAA;QAAK6B,SAAS,EAAC,4BAA4B;QAAAxB,QAAA,gBAEvCL,OAAA;UAAK6B,SAAS,EAAC,uDAAuD;UAAAxB,QAAA,gBAClEL,OAAA;YAAK6B,SAAS,EAAC,yBAAyB;YAAAxB,QAAA,eAEpCL,OAAA;cAAK6B,SAAS,EAAC,+CAA+C;cAAAxB,QAAA,gBAC1DL,OAAA,CAACL,IAAI;gBAACuC,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClBjC,OAAA;gBAAAK,QAAA,EAAM;cAAS;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtBjC,OAAA;gBAAAK,QAAA,EAAM;cAAC;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACdjC,OAAA;gBAAAK,QAAA,EAAM;cAAc;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BjC,OAAA;gBAAAK,QAAA,EAAM;cAAC;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACdjC,OAAA;gBAAM6B,SAAS,EAAC,eAAe;gBAAAxB,QAAA,GAAAD,UAAA,GAAES,IAAI,CAACsB,IAAI,CAACb,GAAG,IAAIA,GAAG,CAACJ,MAAM,CAAC,cAAAd,UAAA,uBAA5BA,UAAA,CAA8BW;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNjC,OAAA;YAAI6B,SAAS,EAAC,uCAAuC;YAAAxB,QAAA,GAAC,mCAAkB,EAAC,CAAAK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,KAAIT,MAAM;UAAA;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,eAGNjC,OAAA;UAAK6B,SAAS,EAAC,wCAAwC;UAAAxB,QAAA,eACnDL,OAAA;YAAK6B,SAAS,EAAC,gBAAgB;YAAAxB,QAAA,EAC1BQ,IAAI,CAACW,GAAG,CAAEF,GAAG,iBACVtB,OAAA;cAEIoC,OAAO,EAAEA,CAAA,KAAMf,cAAc,CAACC,GAAG,CAAE;cACnCO,SAAS,mCAAAZ,MAAA,CAAmCK,GAAG,CAACJ,MAAM,GAAG,0CAA0C,GAAG,mCAAmC,CAAG;cAAAb,QAAA,EAE3IiB,GAAG,CAACP;YAAI,GAJJO,GAAG,CAACP,IAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAEL5B,QAAQ;MAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAGd,CAAC;AAAC9B,EAAA,CA/EIF,eAAe;EAAA,QACET,SAAS,EACXC,WAAW,EACXF,WAAW,EACXK,WAAW,EACXC,WAAW,EAOHA,WAAW;AAAA;AAAAwC,EAAA,GAZlCpC,eAAe;AAiFrB,eAAeA,eAAe;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}