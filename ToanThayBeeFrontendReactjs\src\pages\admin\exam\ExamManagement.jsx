import { useEffect } from "react";
import AdminLayout from "../../../layouts/AdminLayout";
import FunctionBarAdmin from "../../../components/bar/FunctionBarAdmin";
import ExamTable from "../../../components/table/ExamTable";
import { useSelector, useDispatch } from "react-redux";
import { setIsAddView, setIsFilterView } from "../../../features/filter/filterSlice";
import AddExamModal from "../../../components/modal/AddExamModal";
import AdminModal from "../../../components/modal/AdminModal";
import { fetchExams } from "../../../features/exam/examSlice";
import { setCurrentPage, setLimit, setSearch } from "src/features/exam/examSlice";


const ExamManagement = () => {
    const dispatch = useDispatch();
    const { isAddView, isFilterVIew } = useSelector(state => state.filter);
    const { exams, pagination, search } = useSelector(state => state.exams);

    const { page: currentPage, pageSize: limit, sortOrder } = pagination;

    useEffect(() => {
        dispatch(fetchExams({ search, currentPage, limit, sortOrder }));
    }, [dispatch, search, currentPage, limit, sortOrder]);

    return (
        <AdminLayout>
            <AdminModal isOpen={isAddView} headerText={'Tạo câu hỏi mới'} onClose={() => dispatch(setIsAddView(false))} >
                <AddExamModal onClose={() => dispatch(setIsAddView(false))} fetchExams={fetchExams} />
            </AdminModal>
            <div className="text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9">
                Danh sách đề thi
            </div>
            <FunctionBarAdmin
                currentPage={pagination.page}
                totalItems={pagination.total}
                totalPages={pagination.totalPages}
                limit={pagination.pageSize}
                setLimit={(newLimit) => dispatch(setLimit(newLimit))}
                setCurrentPage={(newPage) => dispatch(setCurrentPage(newPage))}
                setSearch={(value) => dispatch(setSearch(value))}
            />
            <ExamTable exams={exams} fetchExams={fetchExams} />
        </AdminLayout >
    );
}

export default ExamManagement;