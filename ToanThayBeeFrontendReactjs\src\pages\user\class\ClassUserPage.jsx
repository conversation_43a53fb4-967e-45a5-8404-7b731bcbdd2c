import UserLayout from "../../../layouts/UserLayout"
import { useDispatch, useSelector } from "react-redux"
import { useEffect, useState, useRef } from "react"
import { fetchClassesByUser } from "../../../features/class/classSlice"
import InputSearch from "../../../components/input/InputSearch"
import ClassImage from "../../../components/image/ClassImage"
import { setSearch, setCurrentPage, setLimit } from "../../../features/class/classSlice"
import Pagination from "../../../components/Pagination"
import { useNavigate } from "react-router-dom"
import JoinClassModal from "../../../components/modal/JoinClassModal"
import { motion } from "framer-motion"
import {
    GraduationCap,
    Calendar,
    BookOpen,
    Users,
    Search,
    Filter,
    Plus,
    Home,
    ExternalLink,
    Loader,
    ChevronDown,
    Clock,
    User<PERSON><PERSON><PERSON>,
    Timer
} from "lucide-react"
import LoadingSpinner from "../../../components/loading/LoadingSpinner"

const ButtonSidebar = ({ choice, onClick, value, text, icon, isOpen, count = null }) => {
    const isActive = choice === value;
    const Icon = icon;

    return (
        <button
            onClick={onClick}
            className={`cursor-pointer self-stretch p-2 ${isActive
                ? 'bg-sky-100 text-sky-700 font-medium'
                : 'hover:bg-gray-100 text-gray-700'
                } rounded-lg inline-flex w-full justify-start items-center gap-3 transition-colors`}
        >
            <div className={`flex justify-center items-center p-2 ${isActive ? 'bg-sky-200 text-sky-700' : 'bg-gray-100 text-gray-600'} rounded-full transition-colors`}>
                <Icon size={16} />
            </div>
            <motion.div
                initial={false}
                animate={{
                    opacity: isOpen ? 1 : 0,
                    width: isOpen ? '100%' : 0,
                }}
                transition={{
                    duration: 0.2,
                    ease: [0.25, 0.1, 0.25, 1.0],
                }}
                className="flex flex-row w-full items-center justify-between gap-2"
            >
                <p className="text-sm font-medium text-start truncate w-full">{text}</p>
                {count !== null && (
                    <div className={`px-2 py-1 text-xs rounded-full ${isActive ? 'bg-sky-200 text-sky-700' : 'bg-gray-200 text-gray-700'} font-medium min-w-[1.5rem] text-center`}>
                        {count}
                    </div>
                )}
            </motion.div>
        </button>
    );
};

const ClassCard = ({ cls, onClick, index, startIndex }) => {
    return (
        <div
            onClick={onClick}
            className="flex flex-col sm:flex-row justify-center items-start sm:items-center p-3 lg:p-4 gap-3 lg:gap-4 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-cyan-300 cursor-pointer transition-all duration-200 group"
        >
            {/* Class Image */}
            <div className="flex-shrink-0 self-center sm:self-auto">
                <ClassImage
                    name={cls.name}
                    className="h-16 w-28 sm:h-20 sm:w-32 group-hover:scale-105 transition-transform duration-200"
                />
            </div>

            {/* Class Content */}
            <div className="flex-1 flex flex-col justify-between w-full sm:w-auto">
                {/* Class Name and Code */}
                <div>
                    <h3 className="text-zinc-900 font-semibold font-bevietnam text-sm sm:text-base truncate group-hover:text-cyan-700 transition-colors">
                        {cls.name}
                    </h3>
                    <p className="text-xs text-gray-500 mt-1">Mã lớp: {cls.class_code}</p>
                </div>

                {/* Schedule and Info */}
                <div className="mt-2 space-y-1 sm:space-y-2">
                    {/* Schedule Information */}
                    <div className="flex items-center gap-2 text-slate-700">
                        <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-cyan-600 flex-shrink-0" />
                        <p className="text-xs font-medium">
                            {cls.dayOfWeek1 || 'Chưa có lịch'} {cls.startTime1 && `- ${cls.startTime1} - ${cls.endTime1}`}
                        </p>
                    </div>
                    {cls.dayOfWeek2 && (
                        <div className="flex items-center gap-2 text-slate-700">
                            <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-cyan-600 flex-shrink-0" />
                            <p className="text-xs font-medium">
                                {cls.dayOfWeek2 || 'Chưa có lịch'} {cls.startTime2 && `- ${cls.startTime2} - ${cls.endTime2}`}
                            </p>
                        </div>
                    )}

                    {/* Additional Info Row */}
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                            <GraduationCap className="w-3 h-3" />
                            <span>Năm học: {cls.academicYear || 'N/A'}</span>
                        </div>
                        <div className="flex items-center gap-1">
                            <Users className="w-3 h-3" />
                            <span>Sĩ số: {cls.studentCount || 0}</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Status Badge */}
            <div className="flex-shrink-0 flex items-center self-center sm:self-auto">
                <div className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1 rounded-full text-xs font-medium ${cls.studentClassStatus === 'JS'
                    ? 'bg-green-50 text-green-700 border border-green-200'
                    : 'bg-yellow-50 text-yellow-700 border border-yellow-200'
                    }`}>
                    {cls.studentClassStatus === 'JS' ? (
                        <>
                            <UserCheck className="w-3 h-3" />
                            <span className="hidden sm:inline">Đã tham gia</span>
                            <span className="sm:hidden">Tham gia</span>
                        </>
                    ) : (
                        <>
                            <Timer className="w-3 h-3" />
                            <span className="hidden sm:inline">Đang chờ</span>
                            <span className="sm:hidden">Chờ</span>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

const ClassUserPage = () => {
    const navigate = useNavigate()
    const { classes, search, pagination } = useSelector(state => state.classes)
    const { page: currentPage, pageSize: limit } = pagination;

    const dispatch = useDispatch()
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [status, setStatus] = useState('JS');
    const [sortOption, setSortOption] = useState('default');
    const [showSortDropdown, setShowSortDropdown] = useState(false);
    const [choice, setChoice] = useState(0);
    const [loading, setLoading] = useState(false);
    const sortDropdownRef = useRef();

    useEffect(() => {
        dispatch(fetchClassesByUser())
        if (limit !== 4) dispatch(setLimit(4))
    }, [dispatch])

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (sortDropdownRef.current && !sortDropdownRef.current.contains(event.target)) {
                setShowSortDropdown(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Sync choice with status
    useEffect(() => {
        if (status === 'JS') {
            setChoice(0);
        } else if (status === 'WS') {
            setChoice(1);
        }
    }, [status]);

    const filteredClasses = classes.filter(cls =>
        (cls.name?.toLowerCase().includes(search.toLowerCase()) ||
            cls.class_code?.toLowerCase().includes(search.toLowerCase())) &&
        cls.studentClassStatus === status
    )

    const sortedClasses = [...filteredClasses].sort((a, b) => {
        switch (sortOption) {
            case 'az':
                return a.name.localeCompare(b.name);
            case 'za':
                return b.name.localeCompare(a.name);
            case 'newest':
                return new Date(b.createdAt) - new Date(a.createdAt);
            case 'oldest':
                return new Date(a.createdAt) - new Date(b.createdAt);
            default:
                return 0;
        }
    });

    const startIndex = (currentPage - 1) * limit;
    const paginatedClasses = sortedClasses.slice(startIndex, startIndex + limit);

    return (
        <UserLayout>
            <JoinClassModal isOpen={isModalOpen} setIsModalOpen={setIsModalOpen} />
            <div className="flex w-full min-h-screen bg-gray-50">

                {/* Main Content */}
                <div className="flex-1 transition-all duration-300">
                    <div className="container px-4 py-4 lg:py-8 max-w-none lg:max-w-4xl xl:max-w-5xl 2xl:max-w-6xl mx-auto">
                        {/* Page Header */}
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 lg:mb-8 gap-4">
                            <div className="flex-1">
                                <h1 className="text-2xl lg:text-3xl font-bold text-gray-800 flex items-center gap-2 lg:gap-3">
                                    <div className="w-8 h-8 lg:w-10 lg:h-10 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl flex items-center justify-center">
                                        <GraduationCap className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
                                    </div>
                                    Lớp học của bạn
                                </h1>
                                <p className="text-gray-600 mt-1 lg:mt-2 text-sm lg:text-base">Quản lý và theo dõi các lớp học bạn đã tham gia</p>
                            </div>
                            <div className="flex items-center gap-2 px-3 lg:px-4 py-2 bg-sky-50 border border-sky-200 rounded-lg self-start sm:self-auto">
                                <div className="w-2 h-2 bg-sky-500 rounded-full animate-pulse"></div>
                                <span className="text-sky-700 text-sm font-medium">
                                    {filteredClasses.length} lớp học
                                </span>
                            </div>
                        </div>

                        {/* Filter and Search Section */}
                        <div className="bg-white p-3 lg:p-4 rounded-lg shadow-sm border border-gray-200 mb-4 lg:mb-6">
                            <div className="flex flex-col gap-3 lg:gap-4">
                                {/* Top Row: Status Filter */}
                                <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
                                    <div className="flex gap-2 overflow-x-auto pb-1">
                                        <button
                                            onClick={() => setStatus('JS')}
                                            className={`px-3 lg:px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${status === 'JS'
                                                ? 'bg-sky-100 text-sky-700 border border-sky-300'
                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                                }`}
                                        >
                                            <UserCheck className="w-4 h-4 inline mr-1 lg:mr-2" />
                                            <span className="hidden sm:inline">Lớp của bạn</span>
                                            <span className="sm:hidden">Của bạn</span>
                                        </button>
                                        <button
                                            onClick={() => setStatus('WS')}
                                            className={`px-3 lg:px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${status === 'WS'
                                                ? 'bg-yellow-100 text-yellow-700 border border-yellow-300'
                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                                }`}
                                        >
                                            <Timer className="w-4 h-4 inline mr-1 lg:mr-2" />
                                            <span className="hidden sm:inline">Lớp đang chờ</span>
                                            <span className="sm:hidden">Đang chờ</span>
                                        </button>
                                    </div>
                                </div>

                                {/* Bottom Row: Search and Actions */}
                                <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
                                    {/* Search */}
                                    <div className="flex-1">
                                        <InputSearch
                                            placeholder="Tìm kiếm lớp học..."
                                            className="w-full h-10"
                                            onDebouncedChange={(value) => {
                                                if (value !== search) {
                                                    dispatch(setSearch(value))
                                                    dispatch(setCurrentPage(1))
                                                }
                                            }}
                                        />
                                    </div>

                                    {/* Sort and Join Class */}
                                    <div className="flex gap-2">
                                        <div className="relative" ref={sortDropdownRef}>
                                            <button
                                                onClick={() => setShowSortDropdown(prev => !prev)}
                                                className="px-3 lg:px-4 py-2 rounded-md bg-white border border-gray-300 text-sm hover:bg-gray-50 transition-colors flex items-center gap-1 lg:gap-2"
                                            >
                                                <Filter className="w-4 h-4" />
                                                <span className="hidden sm:inline">Sắp xếp</span>
                                                <ChevronDown className="w-4 h-4" />
                                            </button>
                                            {showSortDropdown && (
                                                <div className="absolute right-0 mt-2 w-36 lg:w-40 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                                                    <ul className="text-sm text-gray-700">
                                                        <li className="px-3 lg:px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => { setSortOption('az'); setShowSortDropdown(false); }}>A - Z</li>
                                                        <li className="px-3 lg:px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => { setSortOption('za'); setShowSortDropdown(false); }}>Z - A</li>
                                                        <li className="px-3 lg:px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => { setSortOption('newest'); setShowSortDropdown(false); }}>Mới nhất</li>
                                                        <li className="px-3 lg:px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => { setSortOption('oldest'); setShowSortDropdown(false); }}>Cũ nhất</li>
                                                    </ul>
                                                </div>
                                            )}
                                        </div>

                                        <button
                                            onClick={() => setIsModalOpen(true)}
                                            className="px-3 lg:px-4 py-2 bg-sky-600 text-white rounded-md text-sm hover:bg-sky-700 transition-colors flex items-center gap-1 lg:gap-2"
                                        >
                                            <Plus className="w-4 h-4" />
                                            <span className="hidden sm:inline">Tham gia lớp</span>
                                            <span className="sm:hidden">Tham gia</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Classes Section */}
                        <div className="mb-6 lg:mb-8">
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 lg:mb-6 gap-3">
                                <h2 className="text-xl lg:text-2xl font-bold text-gray-800 flex items-center gap-2">
                                    {status === 'JS' ? (
                                        <>
                                            <UserCheck className="text-sky-600 w-5 h-5 lg:w-6 lg:h-6" />
                                            Lớp của bạn
                                        </>
                                    ) : (
                                        <>
                                            <Timer className="text-yellow-600 w-5 h-5 lg:w-6 lg:h-6" />
                                            Lớp đang chờ
                                        </>
                                    )}
                                    {paginatedClasses.length > 0 && (
                                        <span className={`ml-2 text-white text-xs lg:text-sm font-bold px-2 py-1 rounded-full ${status === 'JS' ? 'bg-sky-500' : 'bg-yellow-500'
                                            }`}>
                                            {paginatedClasses.length}
                                        </span>
                                    )}
                                </h2>
                            </div>

                            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                                {loading ? (
                                    <div className="p-6 lg:p-8 text-center text-gray-500">
                                        <Loader size={32} className="mx-auto mb-4 text-gray-300 animate-spin lg:w-10 lg:h-10" />
                                        <p className="text-sm lg:text-base">Đang tải danh sách lớp học...</p>
                                    </div>
                                ) : paginatedClasses && paginatedClasses.length > 0 ? (
                                    <div className="p-4 lg:p-6">
                                        <div className="space-y-3 lg:space-y-4">
                                            {paginatedClasses.map((cls, index) => (
                                                <ClassCard
                                                    key={cls._id}
                                                    cls={cls}
                                                    onClick={() => navigate(`/class/${cls.class_code}`)}
                                                    index={index}
                                                    startIndex={startIndex}
                                                />
                                            ))}
                                        </div>

                                        {/* Pagination */}
                                        <div className="mt-4 lg:mt-6 pt-3 lg:pt-4 border-t border-gray-100">
                                            <Pagination
                                                currentPage={currentPage}
                                                limit={limit}
                                                totalItems={sortedClasses.length}
                                                onPageChange={(p) => dispatch(setCurrentPage(p))}
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <div className="p-6 lg:p-8 text-center text-gray-500">
                                        {status === 'JS' ? (
                                            <>
                                                <GraduationCap size={32} className="mx-auto mb-4 text-gray-300 lg:w-10 lg:h-10" />
                                                <p className="text-sm lg:text-base mb-4">Bạn chưa tham gia lớp học nào.</p>
                                                <button
                                                    onClick={() => setIsModalOpen(true)}
                                                    className="px-4 py-2 bg-sky-600 text-white rounded-md text-sm hover:bg-sky-700 transition-colors"
                                                >
                                                    Tham gia lớp ngay
                                                </button>
                                            </>
                                        ) : (
                                            <>
                                                <Timer size={32} className="mx-auto mb-4 text-gray-300 lg:w-10 lg:h-10" />
                                                <p className="text-sm lg:text-base">Không có lớp nào đang chờ duyệt.</p>
                                            </>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </UserLayout>
    )
}

export default ClassUserPage