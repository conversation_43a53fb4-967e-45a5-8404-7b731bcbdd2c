'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('notification', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'Loại thông báo: SYSTEM, CLASS, EXAM, LESSON, etc.'
      },
      isRead: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      relatedId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'ID của đối tượng liên quan (lớp, bài thi, bài học, etc.)'
      },
      relatedType: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '<PERSON><PERSON>i đối tượng liên quan: CLASS, EXAM, LESSON, etc.'
      },
      actionUrl: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: 'URL để chuyển hướng khi người dùng nhấp vào thông báo'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add index for faster queries
    await queryInterface.addIndex('notification', ['userId']);
    await queryInterface.addIndex('notification', ['isRead']);
    await queryInterface.addIndex('notification', ['type']);
    await queryInterface.addIndex('notification', ['createdAt']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('notification');
  }
};
