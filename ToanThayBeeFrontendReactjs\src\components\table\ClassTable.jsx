import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchClasses, deleteClass } from "../../features/class/classSlice";
import { setSortOrder } from "../../features/class/classSlice";
import { useNavigate } from "react-router-dom";
import { resetFilters } from "../../features/filter/filterSlice";
import ConfirmDeleteModal from "../modal/ConfirmDeleteModal";
import { TotalComponent } from "./TotalComponent";
import LoadingData from "../loading/LoadingData";

const ClassTable = () => {
    const dispatch = useDispatch();

    const { classes, search, pagination } = useSelector(state => state.classes);
    const { page, pageSize, total, totalPages, sortOrder } = pagination;

    const { loading } = useSelector(state => state.states);
    const navigate = useNavigate();
    const [deleteMode, setDeleteMode] = useState(false);
    const [isOpenConfirmDeleteModal, setIsOpenConfirmDeleteModal] = useState(false);
    const [id, setId] = useState(null);

    const handleClick = (classId) => {
        if (deleteMode) {
            setIsOpenConfirmDeleteModal(true);
            setId(classId);
        } else {
            navigate(`/admin/class-management/${classId}`)
        }
    }

    const confirmDeleteModal = () => {
        if (id === null) return;
        dispatch(deleteClass({ classId: id }))
            .unwrap()
            .then(() => {
                dispatch(fetchClasses({ search, page, pageSize, sortOrder })).unwrap()
                setIsOpenConfirmDeleteModal(false);
            });
    };

    useEffect(() => {
        dispatch(fetchClasses({ search, page, pageSize, sortOrder }));
    }, [dispatch, search, page, pageSize, sortOrder]);

    return (
        <LoadingData
            loading={loading}
            isNoData={classes.length > 0 ? false : true}
            loadText="Đang tải danh sách lớp học"
            noDataText="Không có lớp học nào.">
            <div className="flex flex-col gap-4 min-h-0 text-sm">
                <ConfirmDeleteModal
                    isOpen={isOpenConfirmDeleteModal}
                    onClose={() => setIsOpenConfirmDeleteModal(false)}
                    onConfirm={confirmDeleteModal}
                />
                <TotalComponent
                    total={total}
                    page={page}
                    pageSize={pageSize}
                    setSortOrder={() => dispatch(setSortOrder())}
                />

                <div className="flex-grow h-[70vh] overflow-y-auto hide-scrollbar">
                    <table className="w-full border-collapse border border-[#E7E7ED]">
                        <thead className="bg-[#F6FAFD] sticky top-0 z-10">
                            <tr className="border border-[#E7E7ED]">
                                <th className="p-3 text-center">ID</th>
                                <th className="p-3 text-center">Mã lớp</th>
                                <th className="p-3 text-center">Tên lớp</th>
                                <th className="p-3 text-center">Thứ B1</th>
                                <th className="p-3 text-center">Thứ B2</th>
                                <th className="p-3 text-center">Thời gian B1</th>
                                <th className="p-3 text-center">Thời gian B2</th>
                                <th className="p-3 text-center">Năm học</th>
                                <th className="p-3 text-center">Số học viên</th>
                                <th className="p-3 text-center">Công khai</th>
                                <th className="p-3 text-center">Trạng thái</th>
                                <th className="p-3 text-center">Ngày tạo</th>
                                <th className="p-3 text-center">Ngày cập nhật</th>
                            </tr>
                        </thead>
                        <tbody>
                            {classes.map((item, index) => (
                                <tr key={index}
                                    onClick={() => handleClick(item.id)}
                                    className={`border border-[#E7E7ED] ${deleteMode ? 'hover:bg-red-50' : 'hover:bg-gray-50'} cursor-pointer`}>
                                    <td className="p-3 text-center">{item.id}</td>
                                    <td className="p-3 text-center">{item.class_code}</td>
                                    <td className="p-3 text-center">{item.name}</td>
                                    <td className="p-3 text-center">{item.dayOfWeek1}</td>
                                    <td className="p-3 text-center">{item.dayOfWeek2}</td>
                                    <td className="p-3 text-center">{item.startTime1} - {item.endTime1} </td>
                                    <td className="p-3 text-center">{item.startTime2} - {item.endTime2} </td>
                                    <td className="p-3 text-center">{item.academicYear}</td>
                                    <td className="p-3 text-center">{item.studentCount}</td>
                                    <td className="p-3 text-center">{item.public ? "Có" : "Không"}</td>
                                    <td className="p-3 text-center">{item.status}</td>
                                    <td className="p-3 text-center">{new Date(item.createdAt).toLocaleDateString()}</td>
                                    <td className="p-3 text-center">{new Date(item.createdAt).toLocaleDateString()}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </LoadingData>
    )
}


export default ClassTable;