{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\SortableStatementsContainer.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements, setSelectedQuestion } from \"src/features/examAI/examAISlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport DropZone from \"./DropZone\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableStatementsContainer = _ref => {\n  _s();\n  let {\n    question\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    isAddImage\n  } = useSelector(state => state.examAI);\n  const handleStatementImageDrop = (statementIndex, imageUrl) => {\n    const updatedQuestion = {\n      ...question\n    };\n    if (updatedQuestion.statement1s && updatedQuestion.statement1s[statementIndex]) {\n      updatedQuestion.statement1s[statementIndex].imageUrl = imageUrl;\n      dispatch(setSelectedQuestion(updatedQuestion));\n    }\n  };\n  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n  const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      const oldIndex = question.statement1s.findIndex((item, idx) => \"\".concat(item.id || idx) === active.id);\n      const newIndex = question.statement1s.findIndex((item, idx) => \"\".concat(item.id || idx) === over.id);\n      if (oldIndex !== -1 && newIndex !== -1) {\n        dispatch(reorderStatements({\n          questionId: question.id,\n          oldIndex,\n          newIndex\n        }));\n      }\n    }\n  };\n  const getPrefix = index => {\n    if (question.typeOfQuestion === \"TN\") {\n      return prefixTN[index] || \"\".concat(String.fromCharCode(65 + index), \".\");\n    } else if (question.typeOfQuestion === \"DS\") {\n      return prefixDS[index] || \"\".concat(String.fromCharCode(97 + index), \")\");\n    }\n    return \"\".concat(index + 1, \".\");\n  };\n  if (question.typeOfQuestion === \"TLN\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-800\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"\\u0110\\xE1p \\xE1n: \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: question.correctAnswer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this);\n  }\n  if (!question.statement1s || question.statement1s.length === 0) {\n    return null;\n  }\n\n  // Xác định màu sắc dựa trên loại câu hỏi và tính đúng/sai\n  const getTextColor = isCorrect => {\n    if (question.typeOfQuestion === \"TN\") {\n      return isCorrect ? \"text-green-600\" : \"text-gray-800\";\n    } else if (question.typeOfQuestion === \"DS\") {\n      return isCorrect ? \"text-green-600\" : \"text-red-600\";\n    }\n    return \"text-gray-800\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAddImage ?\n    /*#__PURE__*/\n    // Hiển thị bình thường khi isAddImage = true (không drag-and-drop)\n    _jsxDEV(\"div\", {\n      className: \"space-y-1\",\n      children: question.statement1s.map((item, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row gap-1 \".concat(getTextColor(item.isCorrect)),\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold\",\n          children: [getPrefix(idx), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          text: item.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 29\n        }, this)]\n      }, \"\".concat(item.id || idx), true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 25\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 17\n    }, this) :\n    /*#__PURE__*/\n    // Hiển thị với drag-and-drop khi isAddImage = false\n    _jsxDEV(DndContext, {\n      sensors: sensors,\n      collisionDetection: closestCenter,\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(SortableContext, {\n        items: question.statement1s.map((item, idx) => \"\".concat(item.id || idx)),\n        strategy: verticalListSortingStrategy,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1\",\n          children: question.statement1s.map((item, idx) => /*#__PURE__*/_jsxDEV(SortableStatementItem, {\n            statement: item,\n            index: idx,\n            prefix: getPrefix(idx),\n            isCorrect: item.isCorrect,\n            questionType: question.typeOfQuestion\n          }, \"\".concat(item.id || idx), false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableStatementsContainer, \"Ba0KReOlD6ojHwLmVxD/12/DPAE=\", false, function () {\n  return [useDispatch, useSelector, useSensors, useSensor, useSensor];\n});\n_c = SortableStatementsContainer;\nexport default SortableStatementsContainer;\nvar _c;\n$RefreshReg$(_c, \"SortableStatementsContainer\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "reorderStatements", "setSelectedQuestion", "SortableStatementItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DropZone", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "jsxDEV", "_jsxDEV", "SortableStatementsContainer", "_ref", "_s", "question", "dispatch", "isAddImage", "state", "examAI", "handleStatementImageDrop", "statementIndex", "imageUrl", "updatedQuestion", "statement1s", "sensors", "coordinateGetter", "prefixTN", "prefixDS", "handleDragEnd", "event", "active", "over", "id", "oldIndex", "findIndex", "item", "idx", "concat", "newIndex", "questionId", "getPrefix", "index", "typeOfQuestion", "String", "fromCharCode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "length", "getTextColor", "isCorrect", "map", "text", "content", "collisionDetection", "onDragEnd", "items", "strategy", "statement", "prefix", "questionType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/SortableStatementsContainer.jsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements, setSelectedQuestion } from \"src/features/examAI/examAISlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport DropZone from \"./DropZone\";\nimport {\n    DndContext,\n    closestCenter,\n    KeyboardSensor,\n    PointerSensor,\n    useSensor,\n    useSensors,\n} from '@dnd-kit/core';\nimport {\n    SortableContext,\n    sortableKeyboardCoordinates,\n    verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\n\nconst SortableStatementsContainer = ({ question }) => {\n    const dispatch = useDispatch();\n    const { isAddImage } = useSelector((state) => state.examAI);\n\n    const handleStatementImageDrop = (statementIndex, imageUrl) => {\n        const updatedQuestion = { ...question };\n        if (updatedQuestion.statement1s && updatedQuestion.statement1s[statementIndex]) {\n            updatedQuestion.statement1s[statementIndex].imageUrl = imageUrl;\n            dispatch(setSelectedQuestion(updatedQuestion));\n        }\n    };\n    \n    const sensors = useSensors(\n        useSensor(PointerSensor),\n        useSensor(KeyboardSensor, {\n            coordinateGetter: sortableKeyboardCoordinates,\n        })\n    );\n\n    const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n    const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n\n    const handleDragEnd = (event) => {\n        const { active, over } = event;\n\n        if (active.id !== over?.id) {\n            const oldIndex = question.statement1s.findIndex((item, idx) => \n                `${item.id || idx}` === active.id\n            );\n            const newIndex = question.statement1s.findIndex((item, idx) => \n                `${item.id || idx}` === over.id\n            );\n\n            if (oldIndex !== -1 && newIndex !== -1) {\n                dispatch(reorderStatements({\n                    questionId: question.id,\n                    oldIndex,\n                    newIndex\n                }));\n            }\n        }\n    };\n\n    const getPrefix = (index) => {\n        if (question.typeOfQuestion === \"TN\") {\n            return prefixTN[index] || `${String.fromCharCode(65 + index)}.`;\n        } else if (question.typeOfQuestion === \"DS\") {\n            return prefixDS[index] || `${String.fromCharCode(97 + index)})`;\n        }\n        return `${index + 1}.`;\n    };\n\n    if (question.typeOfQuestion === \"TLN\") {\n        return (\n            <div className=\"text-gray-800\">\n                <span className=\"font-semibold\">Đáp án: </span>\n                <span>{question.correctAnswer}</span>\n            </div>\n        );\n    }\n\n    if (!question.statement1s || question.statement1s.length === 0) {\n        return null;\n    }\n\n    // Xác định màu sắc dựa trên loại câu hỏi và tính đúng/sai\n    const getTextColor = (isCorrect) => {\n        if (question.typeOfQuestion === \"TN\") {\n            return isCorrect ? \"text-green-600\" : \"text-gray-800\";\n        } else if (question.typeOfQuestion === \"DS\") {\n            return isCorrect ? \"text-green-600\" : \"text-red-600\";\n        }\n        return \"text-gray-800\";\n    };\n\n    return (\n        <div>\n            {!isAddImage ? (\n                // Hiển thị bình thường khi isAddImage = true (không drag-and-drop)\n                <div className=\"space-y-1\">\n                    {question.statement1s.map((item, idx) => (\n                        <div key={`${item.id || idx}`} className={`flex flex-row gap-1 ${getTextColor(item.isCorrect)}`}>\n                            <span className=\"font-semibold\">{getPrefix(idx)} </span>\n                            <LatexRenderer text={item.content} />\n                        </div>\n                    ))}\n                </div>\n            ) : (\n                // Hiển thị với drag-and-drop khi isAddImage = false\n                <DndContext\n                    sensors={sensors}\n                    collisionDetection={closestCenter}\n                    onDragEnd={handleDragEnd}\n                >\n                    <SortableContext\n                        items={question.statement1s.map((item, idx) => `${item.id || idx}`)}\n                        strategy={verticalListSortingStrategy}\n                    >\n                        <div className=\"space-y-1\">\n                            {question.statement1s.map((item, idx) => (\n                                <SortableStatementItem\n                                    key={`${item.id || idx}`}\n                                    statement={item}\n                                    index={idx}\n                                    prefix={getPrefix(idx)}\n                                    isCorrect={item.isCorrect}\n                                    questionType={question.typeOfQuestion}\n                                />\n                            ))}\n                        </div>\n                    </SortableContext>\n                </DndContext>\n            )}\n        </div>\n    );\n};\n\nexport default SortableStatementsContainer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,EAAEC,mBAAmB,QAAQ,iCAAiC;AACxF,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,QAAQ,MAAM,YAAY;AACjC,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,2BAA2B,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC7C,MAAMG,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB;EAAW,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAE3D,MAAMC,wBAAwB,GAAGA,CAACC,cAAc,EAAEC,QAAQ,KAAK;IAC3D,MAAMC,eAAe,GAAG;MAAE,GAAGR;IAAS,CAAC;IACvC,IAAIQ,eAAe,CAACC,WAAW,IAAID,eAAe,CAACC,WAAW,CAACH,cAAc,CAAC,EAAE;MAC5EE,eAAe,CAACC,WAAW,CAACH,cAAc,CAAC,CAACC,QAAQ,GAAGA,QAAQ;MAC/DN,QAAQ,CAACnB,mBAAmB,CAAC0B,eAAe,CAAC,CAAC;IAClD;EACJ,CAAC;EAED,MAAME,OAAO,GAAGnB,UAAU,CACtBD,SAAS,CAACD,aAAa,CAAC,EACxBC,SAAS,CAACF,cAAc,EAAE;IACtBuB,gBAAgB,EAAElB;EACtB,CAAC,CACL,CAAC;EAED,MAAMmB,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB,MAAMC,QAAQ,GAAGnB,QAAQ,CAACS,WAAW,CAACW,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACtD,GAAAC,MAAA,CAAGF,IAAI,CAACH,EAAE,IAAII,GAAG,MAAON,MAAM,CAACE,EACnC,CAAC;MACD,MAAMM,QAAQ,GAAGxB,QAAQ,CAACS,WAAW,CAACW,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACtD,GAAAC,MAAA,CAAGF,IAAI,CAACH,EAAE,IAAII,GAAG,MAAOL,IAAI,CAACC,EACjC,CAAC;MAED,IAAIC,QAAQ,KAAK,CAAC,CAAC,IAAIK,QAAQ,KAAK,CAAC,CAAC,EAAE;QACpCvB,QAAQ,CAACpB,iBAAiB,CAAC;UACvB4C,UAAU,EAAEzB,QAAQ,CAACkB,EAAE;UACvBC,QAAQ;UACRK;QACJ,CAAC,CAAC,CAAC;MACP;IACJ;EACJ,CAAC;EAED,MAAME,SAAS,GAAIC,KAAK,IAAK;IACzB,IAAI3B,QAAQ,CAAC4B,cAAc,KAAK,IAAI,EAAE;MAClC,OAAOhB,QAAQ,CAACe,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE,CAAC,MAAM,IAAI3B,QAAQ,CAAC4B,cAAc,KAAK,IAAI,EAAE;MACzC,OAAOf,QAAQ,CAACc,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE;IACA,UAAAJ,MAAA,CAAUI,KAAK,GAAG,CAAC;EACvB,CAAC;EAED,IAAI3B,QAAQ,CAAC4B,cAAc,KAAK,KAAK,EAAE;IACnC,oBACIhC,OAAA;MAAKmC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BpC,OAAA;QAAMmC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CxC,OAAA;QAAAoC,QAAA,EAAOhC,QAAQ,CAACqC;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEd;EAEA,IAAI,CAACpC,QAAQ,CAACS,WAAW,IAAIT,QAAQ,CAACS,WAAW,CAAC6B,MAAM,KAAK,CAAC,EAAE;IAC5D,OAAO,IAAI;EACf;;EAEA;EACA,MAAMC,YAAY,GAAIC,SAAS,IAAK;IAChC,IAAIxC,QAAQ,CAAC4B,cAAc,KAAK,IAAI,EAAE;MAClC,OAAOY,SAAS,GAAG,gBAAgB,GAAG,eAAe;IACzD,CAAC,MAAM,IAAIxC,QAAQ,CAAC4B,cAAc,KAAK,IAAI,EAAE;MACzC,OAAOY,SAAS,GAAG,gBAAgB,GAAG,cAAc;IACxD;IACA,OAAO,eAAe;EAC1B,CAAC;EAED,oBACI5C,OAAA;IAAAoC,QAAA,EACK,CAAC9B,UAAU;IAAA;IACR;IACAN,OAAA;MAAKmC,SAAS,EAAC,WAAW;MAAAC,QAAA,EACrBhC,QAAQ,CAACS,WAAW,CAACgC,GAAG,CAAC,CAACpB,IAAI,EAAEC,GAAG,kBAChC1B,OAAA;QAA+BmC,SAAS,yBAAAR,MAAA,CAAyBgB,YAAY,CAAClB,IAAI,CAACmB,SAAS,CAAC,CAAG;QAAAR,QAAA,gBAC5FpC,OAAA;UAAMmC,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAEN,SAAS,CAACJ,GAAG,CAAC,EAAC,GAAC;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDxC,OAAA,CAACZ,aAAa;UAAC0D,IAAI,EAAErB,IAAI,CAACsB;QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,MAAAb,MAAA,CAF5BF,IAAI,CAACH,EAAE,IAAII,GAAG;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGtB,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;IAAA;IAEN;IACAxC,OAAA,CAACV,UAAU;MACPwB,OAAO,EAAEA,OAAQ;MACjBkC,kBAAkB,EAAEzD,aAAc;MAClC0D,SAAS,EAAE/B,aAAc;MAAAkB,QAAA,eAEzBpC,OAAA,CAACJ,eAAe;QACZsD,KAAK,EAAE9C,QAAQ,CAACS,WAAW,CAACgC,GAAG,CAAC,CAACpB,IAAI,EAAEC,GAAG,QAAAC,MAAA,CAAQF,IAAI,CAACH,EAAE,IAAII,GAAG,CAAE,CAAE;QACpEyB,QAAQ,EAAErD,2BAA4B;QAAAsC,QAAA,eAEtCpC,OAAA;UAAKmC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACrBhC,QAAQ,CAACS,WAAW,CAACgC,GAAG,CAAC,CAACpB,IAAI,EAAEC,GAAG,kBAChC1B,OAAA,CAACb,qBAAqB;YAElBiE,SAAS,EAAE3B,IAAK;YAChBM,KAAK,EAAEL,GAAI;YACX2B,MAAM,EAAEvB,SAAS,CAACJ,GAAG,CAAE;YACvBkB,SAAS,EAAEnB,IAAI,CAACmB,SAAU;YAC1BU,YAAY,EAAElD,QAAQ,CAAC4B;UAAe,MAAAL,MAAA,CAL9BF,IAAI,CAACH,EAAE,IAAII,GAAG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMzB,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EACf;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACrC,EAAA,CAnHIF,2BAA2B;EAAA,QACZlB,WAAW,EACLC,WAAW,EAUlBW,UAAU,EACtBD,SAAS,EACTA,SAAS;AAAA;AAAA6D,EAAA,GAdXtD,2BAA2B;AAqHjC,eAAeA,2BAA2B;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}