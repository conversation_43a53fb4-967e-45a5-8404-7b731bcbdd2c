import React from 'react';
import { Loader } from 'lucide-react';

/**
 * LoadingSpinner component with default style options
 * @param {Object} props - Component props
 * @param {string} props.size - Size of the spinner (default: "20")
 * @param {boolean} props.showText - Whether to show loading text (default: false)
 * @param {string} props.text - Text to display (default: "Loading...")
 * @param {string} props.color - Color of the spinner (default: "text-gray-300")
 * @param {string} props.minHeight - Minimum height of the spinner container (default: "min-h-[200px]")
 * @returns {JSX.Element} - The spinner component
 */
const LoadingSpinner = ({
    size = "20",
    showText = false,
    color = "text-gray-300",
    text = "Loading...",
    minHeight = "min-h-[200px]"
}) => {
    return (
        <div className={`text-center text-gray-500 flex flex-col items-center justify-center ${minHeight}`}>
            <Loader size={size} className={`mx-auto animate-spin ${color}`} />
            {showText && (
                <p>{text}</p>
            )}
        </div>
    );
};

export default LoadingSpinner;
