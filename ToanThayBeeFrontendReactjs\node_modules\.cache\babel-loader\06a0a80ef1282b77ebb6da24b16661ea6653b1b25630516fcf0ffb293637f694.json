{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\ImageDropZone.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDropZone = _ref => {\n  _s();\n  let {\n    imageUrl,\n    onImageDrop,\n    onImageRemove,\n    content\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\n    if (draggedImage && onImageDrop) {\n      onImageDrop(draggedImage);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-2 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative flex items-center  rounded-md p-4 cursor-pointer\\n        \".concat(isDraggingOver || isHovering ? \"border border-dashed border-sky-500 bg-sky-50 justify-center\" : \"justify-start\"),\n      onDragOver: e => e.preventDefault(),\n      onDragEnter: e => {\n        e.preventDefault();\n        setDragCounter(prev => prev + 1);\n        setIsDraggingOver(true);\n      },\n      onDragLeave: e => {\n        e.preventDefault();\n        setDragCounter(prev => {\n          const newCount = prev - 1;\n          if (newCount <= 0) {\n            setIsDraggingOver(false);\n            return 0;\n          }\n          return newCount;\n        });\n      },\n      onDrop: e => {\n        e.preventDefault();\n        setDragCounter(0);\n        setIsDraggingOver(false);\n        handleDrop(e);\n      },\n      onMouseEnter: () => setIsHovering(true),\n      onMouseLeave: () => setIsHovering(false),\n      children: isDraggingOver || isHovering ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500 text-sm text-center\",\n        children: \"Th\\xEAm \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        text: content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this), imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-fit group/image\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: \"dropped\",\n        className: \"rounded-md max-h-40 object-contain w-full border transition-all group-hover/image:brightness-75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20 rounded-md\",\n        onClick: e => {\n          e.stopPropagation();\n          onImageRemove === null || onImageRemove === void 0 ? void 0 : onImageRemove();\n        },\n        title: \"X\\xF3a \\u1EA3nh\",\n        children: /*#__PURE__*/_jsxDEV(Trash2, {\n          className: \"w-6 h-6 text-white bg-red-500 p-1 rounded-full group-hover/image:opacity-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_s(ImageDropZone, \"VKW1ZLDpPe+zo2/qAoOWVX5YkVs=\");\n_c = ImageDropZone;\nexport default ImageDropZone;\nvar _c;\n$RefreshReg$(_c, \"ImageDropZone\");", "map": {"version": 3, "names": ["React", "useState", "Trash2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ImageDropZone", "_ref", "_s", "imageUrl", "onImageDrop", "onImageRemove", "content", "isDraggingOver", "setIsDraggingOver", "handleDrop", "e", "preventDefault", "draggedImage", "dataTransfer", "getData", "className", "children", "concat", "isHovering", "onDragOver", "onDragEnter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prev", "onDragLeave", "newCount", "onDrop", "onMouseEnter", "setIsHovering", "onMouseLeave", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "src", "alt", "onClick", "stopPropagation", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/ImageDropZone.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nconst ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove, content }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\r\n        if (draggedImage && onImageDrop) {\r\n            onImageDrop(draggedImage);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-2 w-full\">\r\n            <div\r\n                className={`relative flex items-center  rounded-md p-4 cursor-pointer\r\n        ${isDraggingOver || isHovering ? \"border border-dashed border-sky-500 bg-sky-50 justify-center\" : \"justify-start\"}`}\r\n                onDragOver={(e) => e.preventDefault()}\r\n                onDragEnter={(e) => {\r\n                    e.preventDefault();\r\n                    setDragCounter(prev => prev + 1);\r\n                    setIsDraggingOver(true);\r\n                }}\r\n                onDragLeave={(e) => {\r\n                    e.preventDefault();\r\n                    setDragCounter(prev => {\r\n                        const newCount = prev - 1;\r\n                        if (newCount <= 0) {\r\n                            setIsDraggingOver(false);\r\n                            return 0;\r\n                        }\r\n                        return newCount;\r\n                    });\r\n                }}\r\n                onDrop={(e) => {\r\n                    e.preventDefault();\r\n                    setDragCounter(0);\r\n                    setIsDraggingOver(false);\r\n                    handleDrop(e);\r\n                }}\r\n                onMouseEnter={() => setIsHovering(true)}\r\n                onMouseLeave={() => setIsHovering(false)}\r\n            >\r\n                {isDraggingOver || isHovering ? (\r\n                    <p className=\"text-gray-500 text-sm text-center\">Thêm ảnh</p>\r\n                ) : (\r\n                    <LatexRenderer text={content} />\r\n                )}\r\n            </div>\r\n            {imageUrl && (\r\n                <div className=\"relative w-fit group/image\">\r\n                    <img\r\n                        src={imageUrl}\r\n                        alt=\"dropped\"\r\n                        className=\"rounded-md max-h-40 object-contain w-full border transition-all group-hover/image:brightness-75\"\r\n                    />\r\n                    <button\r\n                        className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20 rounded-md\"\r\n                        onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            onImageRemove?.();\r\n                        }}\r\n                        title=\"Xóa ảnh\"\r\n                    >\r\n                        <Trash2 className=\"w-6 h-6 text-white bg-red-500 p-1 rounded-full group-hover/image:opacity-100\" />\r\n                    </button>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ImageDropZone;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,cAAc,CAAC,CAAC;AACvC,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACjD,MAAMC,aAAa,GAAGC,IAAA,IAAuD;EAAAC,EAAA;EAAA,IAAtD;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,aAAa;IAAEC;EAAQ,CAAC,GAAAL,IAAA;EACpE,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMc,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMI,YAAY,GAAGF,CAAC,CAACG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3D,IAAIF,YAAY,IAAIR,WAAW,EAAE;MAC7BA,WAAW,CAACQ,YAAY,CAAC;IAC7B;EACJ,CAAC;EAED,oBACIb,OAAA;IAAKgB,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACvCjB,OAAA;MACIgB,SAAS,wEAAAE,MAAA,CACfV,cAAc,IAAIW,UAAU,GAAG,8DAA8D,GAAG,eAAe,CAAG;MAC5GC,UAAU,EAAGT,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;MACtCS,WAAW,EAAGV,CAAC,IAAK;QAChBA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBU,cAAc,CAACC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAChCd,iBAAiB,CAAC,IAAI,CAAC;MAC3B,CAAE;MACFe,WAAW,EAAGb,CAAC,IAAK;QAChBA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBU,cAAc,CAACC,IAAI,IAAI;UACnB,MAAME,QAAQ,GAAGF,IAAI,GAAG,CAAC;UACzB,IAAIE,QAAQ,IAAI,CAAC,EAAE;YACfhB,iBAAiB,CAAC,KAAK,CAAC;YACxB,OAAO,CAAC;UACZ;UACA,OAAOgB,QAAQ;QACnB,CAAC,CAAC;MACN,CAAE;MACFC,MAAM,EAAGf,CAAC,IAAK;QACXA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBU,cAAc,CAAC,CAAC,CAAC;QACjBb,iBAAiB,CAAC,KAAK,CAAC;QACxBC,UAAU,CAACC,CAAC,CAAC;MACjB,CAAE;MACFgB,YAAY,EAAEA,CAAA,KAAMC,aAAa,CAAC,IAAI,CAAE;MACxCC,YAAY,EAAEA,CAAA,KAAMD,aAAa,CAAC,KAAK,CAAE;MAAAX,QAAA,EAExCT,cAAc,IAAIW,UAAU,gBACzBnB,OAAA;QAAGgB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAAQ;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAE7DjC,OAAA,CAACF,aAAa;QAACoC,IAAI,EAAE3B;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAClC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EACL7B,QAAQ,iBACLJ,OAAA;MAAKgB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACvCjB,OAAA;QACImC,GAAG,EAAE/B,QAAS;QACdgC,GAAG,EAAC,SAAS;QACbpB,SAAS,EAAC;MAAiG;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9G,CAAC,eACFjC,OAAA;QACIgB,SAAS,EAAC,6JAA6J;QACvKqB,OAAO,EAAG1B,CAAC,IAAK;UACZA,CAAC,CAAC2B,eAAe,CAAC,CAAC;UACnBhC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAG,CAAC;QACrB,CAAE;QACFiC,KAAK,EAAC,iBAAS;QAAAtB,QAAA,eAEfjB,OAAA,CAACH,MAAM;UAACmB,SAAS,EAAC;QAA8E;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC9B,EAAA,CAvEIF,aAAa;AAAAuC,EAAA,GAAbvC,aAAa;AAyEnB,eAAeA,aAAa;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}