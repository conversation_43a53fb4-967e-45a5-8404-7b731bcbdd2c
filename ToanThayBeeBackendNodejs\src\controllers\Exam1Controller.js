import db from "../models/index.js"
import { Op, or, where } from "sequelize";
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js";

export const postExam1 = async (req, res) => {
    // console.log(req.body);
    const { data } = req.body;
    const nameExam = "exam" + new Date().getTime();

    // Tạo transaction lớn cho toàn bộ bài thi
    const transaction = await db.sequelize.transaction();
    try {
        const exam = await db.Exam1.create({
            name: nameExam
        }, { transaction });

        for (const [index, item] of data.entries()) {
            if (item.type === "TN") {
                const question = await db.Question1.create({
                    examId: exam.id,
                    content: item.content || null,
                    typeOfQuestion: item.type || "TN",
                    difficulty: item.difficulty || null,
                    solution: item.solution || null,
                    chapter: item.chapter || null,
                    class: item.class || null,
                    order: index,
                }, { transaction });

                const statements = item.statement.map((st, index) => ({
                    questionId: question.id,
                    content: st.content || null,
                    isCorrect: st.isCorrect || false,
                    order: index,
                }));

                await db.Statement1.bulkCreate(statements, { transaction });
            }

            else if (item.type === "DS") {
                const question = await db.Question1.create({
                    examId: exam.id,
                    content: item.content || null,
                    typeOfQuestion: item.type || "DS",
                    difficulty: item.difficulty || null,
                    solution: item.solution || null,
                    chapter: item.chapter || null,
                    class: item.class || null,
                    order: index,
                }, { transaction });


                const statements = item.statement.map((st, idx) => ({
                    questionId: question.id,
                    content: st.content || null,
                    isCorrect: st.isCorrect || false,
                    order: idx,
                }));

                await db.Statement1.bulkCreate(statements, { transaction });
            }

            else if (item.type === "TLN") {
                await db.Question1.create({
                    examId: exam.id,
                    content: item.content || null,
                    typeOfQuestion: item.type || "TLN",
                    difficulty: item.difficulty || null,
                    solution: item.solution || null,
                    correctAnswer: item.correctAnswer || null,
                    chapter: item.chapter || null,
                    class: item.class || null,
                    order: index,
                }, { transaction });
            }
        }

        await transaction.commit();
        return res.status(200).json({ message: "Post exam1 successfully" });

    } catch (error) {
        await transaction.rollback();
        console.error("Lỗi khi thêm exam1:", error);
        return res.status(500).json({ message: "Đã xảy ra lỗi khi post exam1" });
    }
}

export const getAllExams1 = async (req, res) => {
    const search = req.query.search || ''
    const page = parseInt(req.query.page, 10) || 1
    const pageSize = parseInt(req.query.pageSize, 10) || 10
    const sortOrder = req.query.sortOrder || 'DESC'
    const offset = (page - 1) * pageSize

    const { count, rows } = await db.Exam1.findAndCountAll({
        where: {
            name: {
                [Op.like]: `%${search}%`
            }
        },
        order: [['createdAt', sortOrder]],
        limit: pageSize,
        offset: offset,
    });

    return res.status(200).json({
        message: "Lấy danh sách exam AI thành công",
        ... new ResponseDataPagination(rows, {
            page,
            pageSize,
            total: count,
            totalPages: Math.ceil(count / pageSize),
            sortOrder
        })
    });
}

export const getAllQuestions1ByExamId = async (req, res) => {
    const { examId } = req.params;

    const result = await db.Exam1.findOne({
        where: { id: examId },
        include: [
            {
                model: db.Question1,
                as: 'question1s',
                include: [
                    {
                        model: db.Statement1,
                        as: 'statement1s'
                    }
                ]
            }
        ]
    });

    if (!result) {
        return res.status(404).json({ message: "Không tìm thấy bài thi" });
    }

    return res.status(200).json({
        message: "Lấy danh sách câu hỏi của bài thi thành công",
        data: result
    });
}