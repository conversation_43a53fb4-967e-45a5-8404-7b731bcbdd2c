'use strict'
import { Model } from 'sequelize'

export default (sequelize, DataTypes) => {
  class Answer extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      Answer.belongsTo(models.Question, { foreignKey: 'questionId' });
    }
  }
  Answer.init({
    attemptId: DataTypes.INTEGER,
    questionId: DataTypes.INTEGER,
    answerContent: DataTypes.STRING,
    result: DataTypes.BOOLEAN,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE
  }, {
    sequelize,
    modelName: 'Answer',
    tableName: 'answer'
  })
  return Answer
}