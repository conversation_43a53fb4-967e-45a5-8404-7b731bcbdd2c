import { useSelector, useDispatch } from "react-redux";
import { reorderQuestions, setEditedExam, setSelectedQuestion } from "src/features/examAI/examAISlice";
import LoadingData from "../loading/LoadingData";
import SortableQuestionItem from "./SortableQuestionItem";
import QuestionContent from "./QuestionContent";
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import MarkdownPreviewWithMath from "../latex/MarkDownPreview";
import LatexRenderer from "../latex/RenderLatex";
import { useState } from "react";
import { FileText } from "lucide-react";

export const EditQuestionView = () => {
    const dispatch = useDispatch();
    const { questionsEdited, loading, isAddImage } = useSelector((state) => state.examAI);

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event) => {
        const { active, over } = event;

        if (!over) return;

        // Xử lý drag-and-drop cho questions (khi isAddImage = false)
        if (!isAddImage && active.id !== over.id) {
            const oldIndex = questionsEdited.findIndex(q => q.id === active.id);
            const newIndex = questionsEdited.findIndex(q => q.id === over.id);

            if (oldIndex !== -1 && newIndex !== -1) {
                dispatch(reorderQuestions({
                    oldIndex,
                    newIndex
                }));
            }
        }

        // Xử lý drag-and-drop cho images (khi isAddImage = true)
        if (isAddImage && active.data.current?.type === 'image') {
            const imageUrl = active.data.current.imageUrl;
            const dropZoneId = over.id;

            // Tìm question tương ứng với drop zone
            if (dropZoneId.startsWith('question-content-')) {
                const questionId = dropZoneId.replace('question-content-', '');
                const questionIndex = questionsEdited.findIndex(q => q.id === questionId);
                if (questionIndex !== -1) {
                    const updatedQuestion = { ...questionsEdited[questionIndex], imageUrl };
                    dispatch(setSelectedQuestion(updatedQuestion));
                }
            } else if (dropZoneId.startsWith('question-solution-')) {
                const questionId = dropZoneId.replace('question-solution-', '');
                const questionIndex = questionsEdited.findIndex(q => q.id === questionId);
                if (questionIndex !== -1) {
                    const updatedQuestion = { ...questionsEdited[questionIndex], solutionImageUrl: imageUrl };
                    dispatch(setSelectedQuestion(updatedQuestion));
                }
            } else if (dropZoneId.startsWith('statement-')) {
                const parts = dropZoneId.split('-');
                const questionId = parts[1];
                const statementIndex = parseInt(parts[2]);
                const questionIndex = questionsEdited.findIndex(q => q.id === questionId);

                if (questionIndex !== -1) {
                    const updatedQuestion = { ...questionsEdited[questionIndex] };
                    if (updatedQuestion.statement1s && updatedQuestion.statement1s[statementIndex]) {
                        updatedQuestion.statement1s[statementIndex].imageUrl = imageUrl;
                        dispatch(setSelectedQuestion(updatedQuestion));
                    }
                }
            }
        }
    };

    return (
        <LoadingData loading={loading} loadText="Đang tải danh sách câu hỏi..." isNoData={questionsEdited.length > 0 ? false : true} noDataText="Không có câu hỏi nào.">
            <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
            >
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Danh sách câu hỏi</h2>
                <div className="space-y-3">
                    {questionsEdited.length > 0 ? (
                        !isAddImage ? (
                            // Hiển thị bình thường khi isAddImage = true (không drag-and-drop)
                            questionsEdited.map((q, index) => (
                                <QuestionContent
                                    key={q.id}
                                    question={q}
                                    index={index}
                                />
                            ))
                        ) : (
                            // Hiển thị với drag-and-drop khi isAddImage = false
                            <SortableContext
                                items={questionsEdited.map(q => q.id)}
                                strategy={verticalListSortingStrategy}
                            >
                                {questionsEdited.map((q, index) => (
                                    <SortableQuestionItem
                                        key={q.id}
                                        question={q}
                                        index={index}
                                    />
                                ))}
                            </SortableContext>
                        )
                    ) : (
                        <p className="text-gray-500">Không có câu hỏi nào.</p>
                    )}
                </div>
            </DndContext>
        </LoadingData>
    )
}

const EditExamView = () => {
    const { editedExam, loading } = useSelector((state) => state.examAI);
    const dispatch = useDispatch();
    const [markdownView, setMarkdownView] = useState(false);

    return (
        <LoadingData loading={loading} loadText="Đang tải đề thi" isNoData={editedExam ? false : true} noDataText="Không có đề thi.">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Chi tiết đề thi</h2>
            <div className="flex justify-between items-center mb-4">
                <button
                    onClick={() => setMarkdownView(!markdownView)}
                    className={` text-sm flex items-center gap-2 px-2 py-1 rounded-md
   bg-sky-600 hover:bg-sky-700 text-white`}
                    title={markdownView ? "Chỉnh sửa đề thi" : "Xem đề thi"}
                >
                    <FileText className="w-4 h-4" />
                    <span className="font-semibold">
                        {markdownView ? "Chỉnh sửa đề thi" : "Xem đề thi"}
                    </span>
                </button>
            </div>
            <p className="text-sm text-gray-500 mb-4">
                Đây là chỉ bản ban đầu của đề thi thêm sửa xóa gì thoải mái sau khi tạo đề chính thức phần này sẽ mất đi
            </p>
            {markdownView ? (
                <div className="w-full bg-white min-h-[80vh] overflow-y-auto hide-scrollbar">
                    <LatexRenderer text={editedExam?.markdownExam} />
                </div>
            ) : (
                <textarea
                    value={editedExam?.markdownExam}
                    onChange={(e) => dispatch(setEditedExam({ ...editedExam, markdownExam: e.target.value }))}
                    className="w-full min-h-[80vh] px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
                    placeholder="Nhập mô tả cho mục học tập này..."
                />
            )}
        </LoadingData>
    )
}

export const LeftContent = () => {
    const { viewEdit } = useSelector((state) => state.examAI);

    return (
        <div className="flex-1 border-r border-gray-300 overflow-y-auto p-4">
            {viewEdit === 'question' && <EditQuestionView />}
            {viewEdit === 'exam' && <EditExamView />}
        </div>
    )

}
export default LeftContent;