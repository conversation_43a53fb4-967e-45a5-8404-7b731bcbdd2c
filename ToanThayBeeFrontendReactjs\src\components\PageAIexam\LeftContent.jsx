import { useSelector, useDispatch } from "react-redux";
import { reorderQuestions } from "src/features/examAI/examAISlice";
import LoadingData from "../loading/LoadingData";
import SortableQuestionItem from "./SortableQuestionItem";
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';

export const EditQuestionView = () => {
    const dispatch = useDispatch();
    const { questionsEdited, loading } = useSelector((state) => state.examAI);

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event) => {
        const { active, over } = event;

        if (active.id !== over?.id) {
            const oldIndex = questionsEdited.findIndex(q => q.id === active.id);
            const newIndex = questionsEdited.findIndex(q => q.id === over.id);

            dispatch(reorderQuestions({
                oldIndex,
                newIndex
            }));
        }
    };

    return (
        <LoadingData loading={loading} loadText="Đang tải danh sách câu hỏi..." isNoData={questionsEdited.length > 0 ? false : true} noDataText="Không có câu hỏi nào.">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Danh sách câu hỏi</h2>
            <div className="space-y-3">
                {questionsEdited.length > 0 ? (
                    <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={handleDragEnd}
                    >
                        <SortableContext
                            items={questionsEdited.map(q => q.id)}
                            strategy={verticalListSortingStrategy}
                        >
                            {questionsEdited.map((q, index) => (
                                <SortableQuestionItem
                                    key={q.id}
                                    question={q}
                                    index={index}
                                />
                            ))}
                        </SortableContext>
                    </DndContext>
                ) : (
                    <p className="text-gray-500">Không có câu hỏi nào.</p>
                )}
            </div>
        </LoadingData>
    )
}

export const LeftContent = () => {
    const { viewEdit } = useSelector((state) => state.examAI);

    return (
        <div className="w-2/3 border-r border-gray-300 overflow-y-auto p-4">
            {viewEdit === 'question' && <EditQuestionView />}
        </div>
    )

}
export default LeftContent;