import { useSelector, useDispatch } from "react-redux";
import { setSelectedQuestion, selectQuestion } from "src/features/examAI/examAISlice";
import LatexRenderer from "src/components/latex/RenderLatex";
import MarkdownPreviewWithMath from "../latex/MarkDownPreview";
import LoadingSpinner from "../loading/LoadingSpinner";
import QuestionContent from "./QuestionContent";
import LoadingData from "../loading/LoadingData";

export const EditQuestionView = () => {
    const dispatch = useDispatch();
    const { questionsEdited, selectedQuestion, loading } = useSelector((state) => state.examAI);

    return (
        <LoadingData loading={loading} loadText="Đang tải danh sách câu hỏi..." isNoData={questionsEdited.length > 0 ? false : true} noDataText="Không có câu hỏi nào.">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Danh sách câu hỏi</h2>
            <div className="space-y-3">
                {questionsEdited.length > 0 ? (
                    questionsEdited.map((q, index) => (
                        <QuestionContent
                            question={q}
                            index={index}
                        />
                    ))
                ) : (
                    <p className="text-gray-500">Không có câu hỏi nào.</p>
                )}
            </div>
        </LoadingData>
    )
}

export const LeftContent = () => {
    const { viewEdit } = useSelector((state) => state.examAI);

    return (
        <div className="w-2/3 border-r border-gray-300 overflow-y-auto p-4">
            {viewEdit === 'question' && <EditQuestionView />}
        </div>
    )

}
export default LeftContent;