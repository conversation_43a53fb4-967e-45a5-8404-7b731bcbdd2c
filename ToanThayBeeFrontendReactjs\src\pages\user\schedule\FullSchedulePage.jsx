import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Calendar, Clock, ChevronLeft, ChevronRight, CheckCircle } from 'lucide-react';
import UserLayout from '../../../layouts/UserLayout';
import { fetchClassesOverview } from '../../../features/class/classSlice';
import { getLearningItemWeekend } from '../../../features/learningItem/learningItemSlice';
import CalendarMonth from 'src/components/calendar/CalenderMonth';
import { resetCalendar, setSelectedDay } from 'src/features/calendar/calendarSlice';
import SidebarCalendar from 'src/components/calendar/SidebarCalender';
import WeekView from 'src/components/calendar/WeekView';
import DayView from 'src/components/calendar/DayView';
import MonthView from 'src/components/calendar/MonthView';

const FullSchedulePage = () => {
    const dispatch = useDispatch();
    const { view, startOfWeek } = useSelector((state) => state.calendar);
    const [isMobile, setIsMobile] = useState(false);

    const formatDate = (dateStr) => {
        const d = new Date(dateStr);
        // console.log("Formatted date:", `${String(d.getDate()).padStart(2, '0')}/${String(d.getMonth() + 1).padStart(2, '0')}`);
        return `${String(d.getDate()).padStart(2, '0')}-${String(d.getMonth() + 1).padStart(2, '0')}`;
    };

    // Check if the device is mobile
    useEffect(() => {
        const checkIfMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkIfMobile();
        window.addEventListener('resize', checkIfMobile);

        return () => {
            window.removeEventListener('resize', checkIfMobile);
        };
    }, []);

    useEffect(() => {
        dispatch(fetchClassesOverview());
    }, [dispatch]);

    const getClassHeight = (startTime, endTime) => {
        // Format times to HH:MM if they are in HH:MM:SS format
        const formattedStartTime = startTime.length > 5 ? startTime.substring(0, 5) : startTime;
        const formattedEndTime = endTime.length > 5 ? endTime.substring(0, 5) : endTime;

        const [sh, sm] = formattedStartTime.split(':').map(Number);
        const [eh, em] = formattedEndTime.split(':').map(Number);
        const durationHours = (eh - sh) + (em - sm) / 60;
        return `${durationHours * 3}rem`;
    };

    const getWeekDays = (startOfWeek) => {
        if (!startOfWeek) return [];

        const labels = [
            { short: 'T2', long: 'Thứ 2' },
            { short: 'T3', long: 'Thứ 3' },
            { short: 'T4', long: 'Thứ 4' },
            { short: 'T5', long: 'Thứ 5' },
            { short: 'T6', long: 'Thứ 6' },
            { short: 'T7', long: 'Thứ 7' },
            { short: 'CN', long: 'Chủ nhật' },
        ];

        const days = [];
        for (let i = 0; i < 7; i++) {
            const date = new Date(startOfWeek);
            date.setDate(startOfWeek.getDate() + i);

            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');

            days.push({
                ...labels[i],
                date: `${day}-${month}`,
                fullDate: date, // optional: bạn có thể truyền thêm Date thực tế
            });
        }

        return days;
    };


    const weekDays = getWeekDays(new Date(startOfWeek));



    return (
        <UserLayout>
            <div className="flex flex-col lg:flex-row w-full bg-gray-50">
                {/* left sidebar */}
                <div className={`lg:w-[280px] xl:w-[300px] w-full transition-all duration-300`}>
                    <SidebarCalendar />
                </div>

                <div className="flex-1 transition-all duration-300 lg:max-w-none">

                    {view === 'week' ? (
                        <WeekView weekDays={weekDays} formatDate={formatDate} getClassHeight={getClassHeight} isMobile={isMobile} />
                    ) : view === 'month' ? (
                        <MonthView isMobile={isMobile}/>
                    ) : view === 'day' ? (
                        <DayView formatDate={formatDate} getClassHeight={getClassHeight} isMobile={isMobile} />
                    ) : (
                        <CalendarMonth />
                    )}
                </div>
            </div>
        </UserLayout >
    );
};

export default FullSchedulePage;