{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { postExam, setExamData } from \"src/features/addExam/addExamSlice\";\nimport { CheckCircle, Edit, FileText } from \"lucide-react\"; // dùng icon lucide-react\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeaderStep = () => {\n  _s();\n  const {\n    step\n  } = useSelector(state => state.addExam);\n  const stepInfo = [{\n    id: 1,\n    label: \"Thông tin đề thi\",\n    icon: /*#__PURE__*/_jsxDEV(Edit, {\n      className: \"w-5 h-5 mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 51\n    }, this)\n  }, {\n    id: 2,\n    label: \"Soạn câu hỏi\",\n    icon: /*#__PURE__*/_jsxDEV(FileText, {\n      className: \"w-5 h-5 mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 47\n    }, this)\n  }, {\n    id: 3,\n    label: \"Hoàn tất & đăng đề\",\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {\n      className: \"w-5 h-5 mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 53\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold mb-4\",\n      children: \"C\\xE1c b\\u01B0\\u1EDBc t\\u1EA1o \\u0111\\u1EC1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this), stepInfo.map(s => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center px-3 py-2 rounded-lg border \\n                    \".concat(step === s.id ? \"bg-blue-100 border-blue-500 text-blue-700 font-medium\" : \"bg-gray-50 border-gray-300 text-gray-600\"),\n      children: [s.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n        children: s.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 21\n      }, this)]\n    }, s.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 17\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 9\n  }, this);\n};\n_s(HeaderStep, \"s5rWcbXEHrIRaJo4DtZfiNxygtM=\", false, function () {\n  return [useSelector];\n});\n_c = HeaderStep;\nexport const LeftContent = () => {\n  _s2();\n  const dispatch = useDispatch();\n  const {\n    examData\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 border-r bg-white h-[calc(100vh-69px)] border-gray-300 overflow-y-auto p-6 shadow-sm\",\n    children: /*#__PURE__*/_jsxDEV(HeaderStep, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 9\n  }, this);\n};\n_s2(LeftContent, \"rLRAN3FRqlrzGY/YdC5L2ZsGJDw=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c2 = LeftContent;\nexport default LeftContent;\nvar _c, _c2;\n$RefreshReg$(_c, \"HeaderStep\");\n$RefreshReg$(_c2, \"LeftContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "postExam", "setExamData", "CheckCircle", "Edit", "FileText", "jsxDEV", "_jsxDEV", "HeaderStep", "_s", "step", "state", "addExam", "stepInfo", "id", "label", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "map", "s", "concat", "_c", "LeftContent", "_s2", "dispatch", "examData", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { postExam, setExamData } from \"src/features/addExam/addExamSlice\";\r\nimport { CheckCircle, Edit, FileText } from \"lucide-react\"; // dùng icon lucide-react\r\n\r\nconst HeaderStep = () => {\r\n    const { step } = useSelector((state) => state.addExam);\r\n\r\n    const stepInfo = [\r\n        { id: 1, label: \"Thông tin đề thi\", icon: <Edit className=\"w-5 h-5 mr-2\" /> },\r\n        { id: 2, label: \"Soạn câu hỏi\", icon: <FileText className=\"w-5 h-5 mr-2\" /> },\r\n        { id: 3, label: \"Hoàn tất & đăng đề\", icon: <CheckCircle className=\"w-5 h-5 mr-2\" /> }\r\n    ];\r\n\r\n    return (\r\n        <div className=\"space-y-3\">\r\n            <h2 className=\"text-xl font-semibold mb-4\"><PERSON><PERSON><PERSON> bước tạo đề</h2>\r\n            {stepInfo.map((s) => (\r\n                <div\r\n                    key={s.id}\r\n                    className={`flex items-center px-3 py-2 rounded-lg border \r\n                    ${step === s.id ? \"bg-blue-100 border-blue-500 text-blue-700 font-medium\" : \"bg-gray-50 border-gray-300 text-gray-600\"}`}\r\n                >\r\n                    {s.icon}\r\n                    <span>{s.label}</span>\r\n                </div>\r\n            ))}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport const LeftContent = () => {\r\n    const dispatch = useDispatch();\r\n    const { examData } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <div className=\"flex-1 border-r bg-white h-[calc(100vh-69px)] border-gray-300 overflow-y-auto p-6 shadow-sm\">\r\n            <HeaderStep />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default LeftContent;\r\n"], "mappings": ";;;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,WAAW,QAAQ,mCAAmC;AACzE,SAASC,WAAW,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEtD,MAAMC,QAAQ,GAAG,CACb;IAAEC,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,eAAET,OAAA,CAACH,IAAI;MAACa,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC7E;IAAEP,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,eAAET,OAAA,CAACF,QAAQ;MAACY,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC7E;IAAEP,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,eAAET,OAAA,CAACJ,WAAW;MAACc,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACzF;EAED,oBACId,OAAA;IAAKU,SAAS,EAAC,WAAW;IAAAK,QAAA,gBACtBf,OAAA;MAAIU,SAAS,EAAC,4BAA4B;MAAAK,QAAA,EAAC;IAAe;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAC9DR,QAAQ,CAACU,GAAG,CAAEC,CAAC,iBACZjB,OAAA;MAEIU,SAAS,yEAAAQ,MAAA,CACPf,IAAI,KAAKc,CAAC,CAACV,EAAE,GAAG,uDAAuD,GAAG,0CAA0C,CAAG;MAAAQ,QAAA,GAExHE,CAAC,CAACR,IAAI,eACPT,OAAA;QAAAe,QAAA,EAAOE,CAAC,CAACT;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA,GALjBG,CAAC,CAACV,EAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMR,CACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACZ,EAAA,CAxBID,UAAU;EAAA,QACKT,WAAW;AAAA;AAAA2B,EAAA,GAD1BlB,UAAU;AA0BhB,OAAO,MAAMmB,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B;EAAS,CAAC,GAAG/B,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE1D,oBACIL,OAAA;IAAKU,SAAS,EAAC,6FAA6F;IAAAK,QAAA,eACxGf,OAAA,CAACC,UAAU;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEd,CAAC;AAACO,GAAA,CATWD,WAAW;EAAA,QACH3B,WAAW,EACPD,WAAW;AAAA;AAAAgC,GAAA,GAFvBJ,WAAW;AAWxB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}