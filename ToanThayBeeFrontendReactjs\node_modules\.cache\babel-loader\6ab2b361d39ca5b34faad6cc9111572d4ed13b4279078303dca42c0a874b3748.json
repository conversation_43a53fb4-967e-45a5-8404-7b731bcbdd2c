{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\AddExamAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport { postExam } from \"src/features/exam/examSlice\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport ImageUpload from \"src/components/image/UploadImage\";\nimport UploadPdf from \"src/components/UploadPdf\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport { ArrowLeft, Save, Eye, FileText, Plus, Trash2 } from \"lucide-react\";\nimport LeftContent from \"src/components/PageAddExam/LeftContent\";\nimport RightContent from \"src/components/PageAddExam/RightContent\";\n\n// Main Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AddExamAdmin = () => {\n  _s();\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    step\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" bg-gray-50 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \" bg-gray-50 flex flex-col \".concat(closeSidebar ? \"ml-[104px]\" : \"ml-64\"),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border-b border-gray-200 flex justify-between px-6 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/admin/exam-management'),\n            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(ArrowLeft, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"T\\u1EA1o \\u0111\\u1EC1 thi m\\u1EDBi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {},\n            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(Save, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(LeftContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(RightContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n};\n_s(AddExamAdmin, \"EOu7HGm/bkVFVOpxRSdxFHUNnGM=\", false, function () {\n  return [useSelector, useNavigate, useDispatch, useSelector];\n});\n_c = AddExamAdmin;\nexport default AddExamAdmin;\nvar _c;\n$RefreshReg$(_c, \"AddExamAdmin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "useNavigate", "AdminSidebar", "fetchCodesByType", "postExam", "DropMenuBarAdmin", "SuggestInputBarAdmin", "ImageUpload", "UploadPdf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "ArrowLeft", "Save", "Eye", "FileText", "Plus", "Trash2", "LeftContent", "RightContent", "jsxDEV", "_jsxDEV", "AddExamAdmin", "_s", "closeSidebar", "state", "sidebar", "navigate", "dispatch", "step", "addExam", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "concat", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/AddExamAdmin.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport { postExam } from \"src/features/exam/examSlice\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport ImageUpload from \"src/components/image/UploadImage\";\r\nimport UploadPdf from \"src/components/UploadPdf\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport { ArrowLeft, Save, Eye, FileText, Plus, Trash2 } from \"lucide-react\";\r\n\r\n\r\nimport LeftContent from \"src/components/PageAddExam/LeftContent\";\r\nimport RightContent from \"src/components/PageAddExam/RightContent\";\r\n\r\n// Main Component\r\nexport const AddExamAdmin = () => {\r\n\r\n    const { closeSidebar } = useSelector((state) => state.sidebar);\r\n    const navigate = useNavigate();\r\n    const dispatch = useDispatch();\r\n\r\n    const { step } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <div className=\" bg-gray-50 flex flex-col\">\r\n            <AdminSidebar />\r\n            <div className={` bg-gray-50 flex flex-col ${closeSidebar ? \"ml-[104px]\" : \"ml-64\"}`}>\r\n                {/* Header */}\r\n                <div className=\"bg-white border-b border-gray-200 flex justify-between px-6 py-4\">\r\n                    <div className=\"flex items-center gap-4\">\r\n                        <button\r\n                            onClick={() => navigate('/admin/exam-management')}\r\n                            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\r\n                        >\r\n                            <ArrowLeft className=\"w-5 h-5 text-gray-600\" />\r\n                        </button>\r\n                        <div>\r\n                            <h1 className=\"text-2xl font-bold text-gray-900\">Tạo đề thi mới</h1>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-4\">\r\n                        <button\r\n                            onClick={() => { }}\r\n                            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\r\n                        >\r\n                            <Save className=\"w-5 h-5 text-gray-600\" />\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Main Content */}\r\n                <div className=\"flex-1 flex overflow-hidden\">\r\n                    <LeftContent />\r\n                    <RightContent />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AddExamAdmin;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,cAAc,MAAM,uCAAuC;AAClE,SAASC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AAG3E,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,YAAY,MAAM,yCAAyC;;AAElE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAE9B,MAAM;IAAEC;EAAa,CAAC,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC9D,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAE4B;EAAK,CAAC,GAAG7B,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACK,OAAO,CAAC;EAEtD,oBACIT,OAAA;IAAKU,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACtCX,OAAA,CAAClB,YAAY;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBf,OAAA;MAAKU,SAAS,+BAAAM,MAAA,CAA+Bb,YAAY,GAAG,YAAY,GAAG,OAAO,CAAG;MAAAQ,QAAA,gBAEjFX,OAAA;QAAKU,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC7EX,OAAA;UAAKU,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpCX,OAAA;YACIiB,OAAO,EAAEA,CAAA,KAAMX,QAAQ,CAAC,wBAAwB,CAAE;YAClDI,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eAE9DX,OAAA,CAACT,SAAS;cAACmB,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACTf,OAAA;YAAAW,QAAA,eACIX,OAAA;cAAIU,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNf,OAAA;UAAKU,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACpCX,OAAA;YACIiB,OAAO,EAAEA,CAAA,KAAM,CAAE,CAAE;YACnBP,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eAE9DX,OAAA,CAACR,IAAI;cAACkB,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNf,OAAA;QAAKU,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBACxCX,OAAA,CAACH,WAAW;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACff,OAAA,CAACF,YAAY;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACb,EAAA,CA3CWD,YAAY;EAAA,QAEItB,WAAW,EACnBE,WAAW,EACXD,WAAW,EAEXD,WAAW;AAAA;AAAAuC,EAAA,GANnBjB,YAAY;AA6CzB,eAAeA,YAAY;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}