import Joi from "joi"

class PutClassRequest {
    constructor(data) {
        this.name = data.name
        this.grade = data.grade
        this.description = data.description
        this.academicYear = data.academicYear
        this.status = data.status
        this.dayOfWeek1 = data.dayOfWeek1
        this.dayOfWeek2 = data.dayOfWeek2
        this.startTime1 = data.startTime1
        this.startTime2 = data.startTime2
        this.endTime1 = data.endTime1
        this.endTime2 = data.endTime2
        this.public = data.public
        this.dow = data.dow
    }

    static validate(data) {
        const schema = Joi.object({
            name: Joi.string().optional(),
            grade: Joi.string().length(2).optional().allow(null),
            description: Joi.string().optional().allow(''),
            academicYear: Joi.string().optional(),
            status: Joi.string().optional(),
            dayOfWeek1: Joi.string().optional().allow(null),
            dayOfWeek2: Joi.string().optional().allow(null),
            startTime1: Joi.string().optional().allow(null),
            startTime2: Joi.string().optional().allow(null),
            endTime1: Joi.string().optional().allow(null),
            endTime2: Joi.string().optional().allow(null),
            public: Joi.boolean().optional(),
            dow: Joi.string().optional(),
        })

        return schema.validate(data)
    }
}

export default PutClassRequest
