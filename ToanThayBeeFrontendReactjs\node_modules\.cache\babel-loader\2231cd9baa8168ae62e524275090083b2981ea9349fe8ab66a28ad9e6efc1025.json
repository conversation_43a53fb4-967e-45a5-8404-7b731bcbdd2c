{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\SortableStatementItem.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { GripVertical } from 'lucide-react';\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableStatementItem = _ref => {\n  _s();\n  let {\n    statement,\n    index,\n    prefix,\n    isCorrect,\n    questionType\n  } = _ref;\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging\n  } = useSortable({\n    id: \"\".concat(statement.id || index)\n  });\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n    opacity: isDragging ? 0.5 : 1\n  };\n\n  // <PERSON>ác định màu sắc dựa trên loại câu hỏi và tính đúng/sai\n  const getTextColor = () => {\n    if (questionType === \"TN\") {\n      return isCorrect ? \"text-green-600\" : \"text-gray-800\";\n    } else if (questionType === \"DS\") {\n      return isCorrect ? \"text-green-600\" : \"text-red-600\";\n    }\n    return \"text-gray-800\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    style: style,\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ...attributes,\n      ...listeners,\n      className: \"absolute left-0 top-1 z-10 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-200 transition-colors\",\n      title: \"K\\xE9o \\u0111\\u1EC3 s\\u1EAFp x\\u1EBFp l\\u1EA1i th\\u1EE9 t\\u1EF1 m\\u1EC7nh \\u0111\\u1EC1\",\n      children: /*#__PURE__*/_jsxDEV(GripVertical, {\n        size: 12,\n        className: \"text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pl-6 flex flex-row gap-1 \".concat(getTextColor(), \" text-xs\"),\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: [prefix, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        text: statement.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this), statement.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 pl-6\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: statement.imageUrl,\n        alt: \"statement\",\n        className: \"max-h-32 object-contain rounded-md border border-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableStatementItem, \"bHcBvfvWWXIh6rUHlnQDLt/lZWI=\", false, function () {\n  return [useSortable];\n});\n_c = SortableStatementItem;\nexport default SortableStatementItem;\nvar _c;\n$RefreshReg$(_c, \"SortableStatementItem\");", "map": {"version": 3, "names": ["React", "useSortable", "CSS", "GripVertical", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SortableStatementItem", "_ref", "_s", "statement", "index", "prefix", "isCorrect", "questionType", "attributes", "listeners", "setNodeRef", "transform", "transition", "isDragging", "id", "concat", "style", "Transform", "toString", "opacity", "getTextColor", "ref", "className", "children", "title", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "content", "imageUrl", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/SortableStatementItem.jsx"], "sourcesContent": ["import React from 'react';\nimport { useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { GripVertical } from 'lucide-react';\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\n\nconst SortableStatementItem = ({ statement, index, prefix, isCorrect, questionType }) => {\n    const {\n        attributes,\n        listeners,\n        setNodeRef,\n        transform,\n        transition,\n        isDragging,\n    } = useSortable({ id: `${statement.id || index}` });\n\n    const style = {\n        transform: CSS.Transform.toString(transform),\n        transition,\n        opacity: isDragging ? 0.5 : 1,\n    };\n\n    // Xác định màu sắc dựa trên loại câu hỏi và tính đúng/sai\n    const getTextColor = () => {\n        if (questionType === \"TN\") {\n            return isCorrect ? \"text-green-600\" : \"text-gray-800\";\n        } else if (questionType === \"DS\") {\n            return isCorrect ? \"text-green-600\" : \"text-red-600\";\n        }\n        return \"text-gray-800\";\n    };\n\n    return (\n        <div ref={setNodeRef} style={style} className=\"relative\">\n            {/* Drag Handle */}\n            <div\n                {...attributes}\n                {...listeners}\n                className=\"absolute left-0 top-1 z-10 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-200 transition-colors\"\n                title=\"Kéo để sắp xếp lại thứ tự mệnh đề\"\n            >\n                <GripVertical size={12} className=\"text-gray-400\" />\n            </div>\n\n            {/* Statement Content with left padding for drag handle */}\n            <div className={`pl-6 flex flex-row gap-1 ${getTextColor()} text-xs`}>\n                <span className=\"font-semibold\">{prefix} </span>\n                <LatexRenderer text={statement.content} />\n            </div>\n\n            {statement.imageUrl && (\n                <div className=\"mt-2 pl-6\">\n                    <img\n                        src={statement.imageUrl}\n                        alt=\"statement\"\n                        className=\"max-h-32 object-contain rounded-md border border-gray-200\"\n                    />\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default SortableStatementItem;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,GAAG,QAAQ,oBAAoB;AACxC,SAASC,YAAY,QAAQ,cAAc;AAC3C,OAAOC,aAAa,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,qBAAqB,GAAGC,IAAA,IAA2D;EAAAC,EAAA;EAAA,IAA1D;IAAEC,SAAS;IAAEC,KAAK;IAAEC,MAAM;IAAEC,SAAS;IAAEC;EAAa,CAAC,GAAAN,IAAA;EAChF,MAAM;IACFO,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC;EACJ,CAAC,GAAGnB,WAAW,CAAC;IAAEoB,EAAE,KAAAC,MAAA,CAAKZ,SAAS,CAACW,EAAE,IAAIV,KAAK;EAAG,CAAC,CAAC;EAEnD,MAAMY,KAAK,GAAG;IACVL,SAAS,EAAEhB,GAAG,CAACsB,SAAS,CAACC,QAAQ,CAACP,SAAS,CAAC;IAC5CC,UAAU;IACVO,OAAO,EAAEN,UAAU,GAAG,GAAG,GAAG;EAChC,CAAC;;EAED;EACA,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIb,YAAY,KAAK,IAAI,EAAE;MACvB,OAAOD,SAAS,GAAG,gBAAgB,GAAG,eAAe;IACzD,CAAC,MAAM,IAAIC,YAAY,KAAK,IAAI,EAAE;MAC9B,OAAOD,SAAS,GAAG,gBAAgB,GAAG,cAAc;IACxD;IACA,OAAO,eAAe;EAC1B,CAAC;EAED,oBACIP,OAAA;IAAKsB,GAAG,EAAEX,UAAW;IAACM,KAAK,EAAEA,KAAM;IAACM,SAAS,EAAC,UAAU;IAAAC,QAAA,gBAEpDxB,OAAA;MAAA,GACQS,UAAU;MAAA,GACVC,SAAS;MACba,SAAS,EAAC,+GAA+G;MACzHE,KAAK,EAAC,wFAAmC;MAAAD,QAAA,eAEzCxB,OAAA,CAACH,YAAY;QAAC6B,IAAI,EAAE,EAAG;QAACH,SAAS,EAAC;MAAe;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAGN9B,OAAA;MAAKuB,SAAS,8BAAAP,MAAA,CAA8BK,YAAY,CAAC,CAAC,aAAW;MAAAG,QAAA,gBACjExB,OAAA;QAAMuB,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAElB,MAAM,EAAC,GAAC;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChD9B,OAAA,CAACF,aAAa;QAACiC,IAAI,EAAE3B,SAAS,CAAC4B;MAAQ;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC,EAEL1B,SAAS,CAAC6B,QAAQ,iBACfjC,OAAA;MAAKuB,SAAS,EAAC,WAAW;MAAAC,QAAA,eACtBxB,OAAA;QACIkC,GAAG,EAAE9B,SAAS,CAAC6B,QAAS;QACxBE,GAAG,EAAC,WAAW;QACfZ,SAAS,EAAC;MAA2D;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC3B,EAAA,CAvDIF,qBAAqB;EAAA,QAQnBN,WAAW;AAAA;AAAAyC,EAAA,GARbnC,qBAAqB;AAyD3B,eAAeA,qBAAqB;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}