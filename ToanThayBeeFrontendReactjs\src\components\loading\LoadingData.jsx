import LoadingSpinner from "./LoadingSpinner"

export const LoadingData = ({ loading, isNoData = true, loadText = "Đang tải dữ liệu", noDataText = "Không có dữ liệu.", children }) => {
    if (loading) return (
        <div className="flex items-center justify-center h-64">
            <LoadingSpinner color="text-gray-200" size="40" showText={true} text={loadText} />
        </div>
    )

    if (isNoData) {
        return (
            <div className="text-center text-gray-500">
                <p className="text-sm">{noDataText}</p>
            </div>
        );
    }

    return children

}

export default LoadingData;