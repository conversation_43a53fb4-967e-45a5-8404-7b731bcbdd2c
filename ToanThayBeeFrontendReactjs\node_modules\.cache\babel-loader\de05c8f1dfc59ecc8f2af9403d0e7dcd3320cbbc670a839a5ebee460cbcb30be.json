{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\DraggableImage.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDraggable } from '@dnd-kit/core';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DraggableImage = _ref => {\n  _s();\n  let {\n    imageUrl,\n    index\n  } = _ref;\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    isDragging\n  } = useDraggable({\n    id: \"image-\".concat(index),\n    data: {\n      type: 'image',\n      imageUrl: imageUrl\n    }\n  });\n  const style = {\n    opacity: isDragging ? 0.3 : 1,\n    cursor: isDragging ? 'grabbing' : 'grab'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    style: style,\n    ...listeners,\n    ...attributes,\n    className: \"mb-6 border-2 border-dashed border-gray-300 rounded-lg p-2 hover:border-blue-400 transition-colors\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-700 mb-2\",\n      children: [\"H\\xECnh \\u1EA3nh \", index + 1, \" (K\\xE9o \\u0111\\u1EC3 th\\xEAm v\\xE0o c\\xE2u h\\u1ECFi):\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      src: imageUrl,\n      alt: \"image-\".concat(index),\n      className: \"w-full h-48 object-contain rounded-lg\",\n      draggable: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 9\n  }, this);\n};\n_s(DraggableImage, \"bgRtWipobPD+D8MAR4JQB5wIZOg=\", false, function () {\n  return [useDraggable];\n});\n_c = DraggableImage;\nexport default DraggableImage;\nvar _c;\n$RefreshReg$(_c, \"DraggableImage\");", "map": {"version": 3, "names": ["React", "useDraggable", "jsxDEV", "_jsxDEV", "DraggableImage", "_ref", "_s", "imageUrl", "index", "attributes", "listeners", "setNodeRef", "isDragging", "id", "concat", "data", "type", "style", "opacity", "cursor", "ref", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "draggable", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/DraggableImage.jsx"], "sourcesContent": ["import React from 'react';\nimport { useDraggable } from '@dnd-kit/core';\n\nconst DraggableImage = ({ imageUrl, index }) => {\n    const {\n        attributes,\n        listeners,\n        setNodeRef,\n        isDragging,\n    } = useDraggable({\n        id: `image-${index}`,\n        data: {\n            type: 'image',\n            imageUrl: imageUrl,\n        },\n    });\n\n    const style = {\n        opacity: isDragging ? 0.3 : 1,\n        cursor: isDragging ? 'grabbing' : 'grab',\n    };\n\n    return (\n        <div\n            ref={setNodeRef}\n            style={style}\n            {...listeners}\n            {...attributes}\n            className=\"mb-6 border-2 border-dashed border-gray-300 rounded-lg p-2 hover:border-blue-400 transition-colors\"\n        >\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                H<PERSON>nh <PERSON>nh {index + 1} (Kéo để thêm vào câu hỏi):\n            </label>\n            <img \n                src={imageUrl} \n                alt={`image-${index}`} \n                className=\"w-full h-48 object-contain rounded-lg\"\n                draggable={false}\n            />\n        </div>\n    );\n};\n\nexport default DraggableImage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,cAAc,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAH,IAAA;EACvC,MAAM;IACFI,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC;EACJ,CAAC,GAAGX,YAAY,CAAC;IACbY,EAAE,WAAAC,MAAA,CAAWN,KAAK,CAAE;IACpBO,IAAI,EAAE;MACFC,IAAI,EAAE,OAAO;MACbT,QAAQ,EAAEA;IACd;EACJ,CAAC,CAAC;EAEF,MAAMU,KAAK,GAAG;IACVC,OAAO,EAAEN,UAAU,GAAG,GAAG,GAAG,CAAC;IAC7BO,MAAM,EAAEP,UAAU,GAAG,UAAU,GAAG;EACtC,CAAC;EAED,oBACIT,OAAA;IACIiB,GAAG,EAAET,UAAW;IAChBM,KAAK,EAAEA,KAAM;IAAA,GACTP,SAAS;IAAA,GACTD,UAAU;IACdY,SAAS,EAAC,oGAAoG;IAAAC,QAAA,gBAE9GnB,OAAA;MAAOkB,SAAS,EAAC,8CAA8C;MAAAC,QAAA,GAAC,mBACnD,EAACd,KAAK,GAAG,CAAC,EAAC,wDACxB;IAAA;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRvB,OAAA;MACIwB,GAAG,EAAEpB,QAAS;MACdqB,GAAG,WAAAd,MAAA,CAAWN,KAAK,CAAG;MACtBa,SAAS,EAAC,uCAAuC;MACjDQ,SAAS,EAAE;IAAM;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACpB,EAAA,CAtCIF,cAAc;EAAA,QAMZH,YAAY;AAAA;AAAA6B,EAAA,GANd1B,cAAc;AAwCpB,eAAeA,cAAc;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}