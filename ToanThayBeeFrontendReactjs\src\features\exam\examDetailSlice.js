import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as examApi from "../../services/examApi";
import { apiHandler } from "../../utils/apiHandler";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";

export const fetchPublicExamById = createAsyncThunk(
    "examDetail/fetchPublicExamById",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, examApi.getExamPublic, id, () => { }, false, false);
    }
);

export const saveExamForUser = createAsyncThunk(
    "examDetail/saveExamForUser",
    async ({ examId }, { dispatch }) => {
        return await apiHandler(dispatch, examApi.saveExamForUserAPI, { examId }, () => { }, true, false, false, false);
    }
);

export const fetchRelatedExams = createAsyncThunk(
    "exams/fetchRelatedExams",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => { }, false, false);
    }
);

// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết
export const fetchRelatedExamsIfNeeded = createAsyncThunk(
    "exams/fetchRelatedExamsIfNeeded",
    async (id, { dispatch, getState }) => {
        const state = getState();
        const { relatedExams, lastFetchedRelatedExams } = state.exams;

        // Kiểm tra xem đã có dữ liệu trong cache chưa
        const hasData = relatedExams[id] && relatedExams[id].length > 0;

        // Kiểm tra thời gian cache (5 phút = 300000ms)
        const now = Date.now();
        const lastFetched = lastFetchedRelatedExams[id] || 0;
        const isCacheValid = now - lastFetched < 300000;

        // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache
        if (hasData && isCacheValid) {
            // Cập nhật state.exams để hiển thị dữ liệu cache
            return { data: relatedExams[id] };
        }

        // Nếu không có dữ liệu hoặc cache hết hạn, gọi API
        return await dispatch(fetchRelatedExams(id)).unwrap();
    }
);

const examDetailSlice = createSlice({
    name: "examDetail",
    initialState: {
        exam: null,
        loading: false,
        relatedExams: [],
        loadingRelatedExams: false,
        view: 'detail',
    },
    reducers: {
        setView: (state, action) => {
            state.view = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchPublicExamById.pending, (state) => {
                state.exam = null;
                state.loading = true;
            })
            .addCase(fetchPublicExamById.fulfilled, (state, action) => {
                if (action.payload) {
                    state.exam = action.payload.data;
                }
                state.loading = false;
            })
            .addCase(fetchPublicExamById.rejected, (state) => {
                state.exam = null;
                state.loading = false;
            })
            .addCase(saveExamForUser.fulfilled, (state, action) => {
                if (action.payload) {
                    const { examId, isSave } = action.payload;

                    if (state.exam) {
                        state.exam.isSave = isSave;
                    }
                }
            })
            .addCase(fetchRelatedExams.pending, (state) => {
                state.loadingRelatedExams = true;
            })
            .addCase(fetchRelatedExams.fulfilled, (state, action) => {
                if (action.payload) {
                    state.relatedExams = action.payload.data;
                }
                state.loadingRelatedExams = false;
            })
            .addCase(fetchRelatedExams.rejected, (state) => {
                state.loadingRelatedExams = false;
            })
            .addCase(fetchRelatedExamsIfNeeded.pending, (state) => {
                state.loadingRelatedExams = true;
            })
            .addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {
                if (action.payload) {
                    state.relatedExams = action.payload.data;
                }
                state.loadingRelatedExams = false;
            })
            .addCase(fetchRelatedExamsIfNeeded.rejected, (state) => {
                state.loadingRelatedExams = false;
            })
    },
});

export const { setView } = examDetailSlice.actions;
export default examDetailSlice.reducer;
