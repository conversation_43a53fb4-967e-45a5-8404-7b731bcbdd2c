{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nexport const postExam = createAsyncThunk(\"addExam/postExam\", async (_ref, _ref2) => {\n  let {\n    examData,\n    examImage,\n    questions,\n    questionImages,\n    statementImages,\n    solutionImages,\n    examFile\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, examApi.postExamAPI, {\n    examData,\n    examImage,\n    questions,\n    questionImages,\n    statementImages,\n    solutionImages,\n    examFile\n  }, () => {}, true, false);\n});\nconst addExamSlice = createSlice({\n  name: \"addExam\",\n  initialState: {\n    loading: false,\n    step: 1,\n    selectedIndex: 0,\n    questionTNContent: \"\",\n    questionDSContent: \"\",\n    questionTLNContent: \"\",\n    folder: \"questionImage\",\n    correctAnswerTN: \"\",\n    correctAnswerDS: \"\",\n    correctAnswerTLN: \"\",\n    showAddImagesModal: false,\n    examData: {\n      name: \"\",\n      typeOfExam: null,\n      class: null,\n      chapter: null,\n      year: null,\n      description: \"\",\n      testDuration: null,\n      passRate: null,\n      solutionUrl: \"\",\n      imageUrl: \"\",\n      public: false,\n      isClassroomExam: false\n    },\n    examImage: null,\n    questions: [],\n    questionImages: [],\n    statementImages: [],\n    solutionImages: [],\n    examFile: null\n  },\n  reducers: {\n    nextStep: state => {\n      state.step++;\n    },\n    prevStep: state => {\n      state.step--;\n    },\n    setSelectedIndex: (state, action) => {\n      state.selectedIndex = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setQuestionTNContent: (state, action) => {\n      state.questionTNContent = action.payload;\n    },\n    setQuestionDSContent: (state, action) => {\n      state.questionDSContent = action.payload;\n    },\n    setQuestionTLNContent: (state, action) => {\n      state.questionTLNContent = action.payload;\n    },\n    setCorrectAnswerTN: (state, action) => {\n      state.correctAnswerTN = action.payload;\n    },\n    setCorrectAnswerDS: (state, action) => {\n      state.correctAnswerDS = action.payload;\n    },\n    setCorrectAnswerTLN: (state, action) => {\n      state.correctAnswerTLN = action.payload;\n    },\n    setExamData: (state, action) => {\n      const {\n        field,\n        value\n      } = action.payload;\n      // console.log('Setting exam data in slice:', field, value);\n      state.examData[field] = value;\n    },\n    setExamImage: (state, action) => {\n      state.examImage = action.payload;\n    },\n    setExamFile: (state, action) => {\n      state.examFile = action.payload;\n    },\n    setStep: (state, action) => {\n      state.step = action.payload;\n    },\n    resetData: state => {\n      state.examData = {\n        name: \"\",\n        typeOfExam: null,\n        class: null,\n        chapter: null,\n        year: null,\n        description: \"\",\n        testDuration: null,\n        passRate: null,\n        solutionUrl: \"\",\n        imageUrl: \"\",\n        public: false,\n        isClassroomExam: false\n      };\n      state.examImage = null;\n      state.questions = [];\n      state.questionImages = [];\n      state.statementImages = [];\n      state.solutionImages = [];\n      state.examFile = null;\n    },\n    setQuestions: (state, action) => {\n      state.questions = action.payload;\n    },\n    setShowAddImagesModal: (state, action) => {\n      state.showAddImagesModal = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(postExam.pending, state => {\n      state.loading = true;\n    }).addCase(postExam.fulfilled, state => {\n      state.loading = false;\n    }).addCase(postExam.rejected, state => {\n      state.loading = false;\n    });\n  }\n});\nexport const {\n  setLoading,\n  resetData,\n  nextStep,\n  prevStep,\n  setExamData,\n  setStep,\n  setExamImage,\n  setExamFile,\n  setQuestionTNContent,\n  setQuestionDSContent,\n  setQuestionTLNContent,\n  setCorrectAnswerTN,\n  setCorrectAnswerDS,\n  setCorrectAnswerTLN,\n  setQuestions,\n  setSelectedIndex,\n  setShowAddImagesModal\n} = addExamSlice.actions;\nexport default addExamSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postExam", "_ref", "_ref2", "examData", "examImage", "questions", "questionImages", "statementImages", "solutionImages", "examFile", "dispatch", "postExamAPI", "addExamSlice", "name", "initialState", "loading", "step", "selectedIndex", "questionT<PERSON>ontent", "question<PERSON><PERSON><PERSON><PERSON>", "questionTLNContent", "folder", "correctAnswerTN", "correctAnswerDS", "correctAnswerTLN", "showAddImagesModal", "typeOfExam", "class", "chapter", "year", "description", "testDuration", "passRate", "solutionUrl", "imageUrl", "public", "isClassroomExam", "reducers", "nextStep", "state", "prevStep", "setSelectedIndex", "action", "payload", "setLoading", "setQuestionTNContent", "setQuestionDSContent", "setQuestionTLNContent", "setCorrectAnswerTN", "setCorrectAnswerDS", "setCorrectAnswerTLN", "setExamData", "field", "value", "setExamImage", "setExamFile", "setStep", "resetData", "setQuestions", "setShowAddImagesModal", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/addExam/addExamSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\n\r\nexport const postExam = createAsyncThunk(\r\n    \"addExam/postExam\",\r\n    async ({ examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.postExamAPI, { examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, () => { }, true, false);\r\n    }\r\n);\r\n\r\nconst addExamSlice = createSlice({\r\n    name: \"addExam\",\r\n    initialState: {\r\n        loading: false,\r\n        step: 1,\r\n        selectedIndex: 0,\r\n        questionTNContent: \"\",\r\n        questionDSContent: \"\",\r\n        questionTLNContent: \"\",\r\n        folder: \"questionImage\",\r\n        correctAnswerTN: \"\",\r\n        correctAnswerDS: \"\",\r\n        correctAnswerTLN: \"\",\r\n        showAddImagesModal: false,\r\n        examData:\r\n        {\r\n            name: \"\",\r\n            typeOfExam: null,\r\n            class: null,\r\n            chapter: null,\r\n            year: null,\r\n            description: \"\",\r\n            testDuration: null,\r\n            passRate: null,\r\n            solutionUrl: \"\",\r\n            imageUrl: \"\",\r\n            public: false,\r\n            isClassroomExam: false,\r\n        },\r\n        examImage: null,\r\n        questions: [],\r\n        questionImages: [],\r\n        statementImages: [],\r\n        solutionImages: [],\r\n        examFile: null,\r\n    },\r\n    reducers: {\r\n        nextStep: (state) => {\r\n            state.step++;\r\n        },\r\n        prevStep: (state) => {\r\n            state.step--;\r\n        },\r\n        setSelectedIndex: (state, action) => {\r\n            state.selectedIndex = action.payload;\r\n        },\r\n        setLoading: (state, action) => {\r\n            state.loading = action.payload;\r\n        },\r\n        setQuestionTNContent: (state, action) => {\r\n            state.questionTNContent = action.payload;\r\n        },\r\n        setQuestionDSContent: (state, action) => {\r\n            state.questionDSContent = action.payload;\r\n        },\r\n        setQuestionTLNContent: (state, action) => {\r\n            state.questionTLNContent = action.payload;\r\n        },\r\n        setCorrectAnswerTN: (state, action) => {\r\n            state.correctAnswerTN = action.payload;\r\n        },\r\n        setCorrectAnswerDS: (state, action) => {\r\n            state.correctAnswerDS = action.payload;\r\n        },\r\n        setCorrectAnswerTLN: (state, action) => {\r\n            state.correctAnswerTLN = action.payload;\r\n        },\r\n\r\n        setExamData: (state, action) => {\r\n            const { field, value } = action.payload;\r\n            // console.log('Setting exam data in slice:', field, value);\r\n            state.examData[field] = value;\r\n        },\r\n        setExamImage: (state, action) => {\r\n            state.examImage = action.payload;\r\n        },\r\n        setExamFile: (state, action) => {\r\n            state.examFile = action.payload;\r\n        },\r\n        setStep: (state, action) => {\r\n            state.step = action.payload;\r\n        },\r\n        resetData: (state) => {\r\n            state.examData = {\r\n                name: \"\",\r\n                typeOfExam: null,\r\n                class: null,\r\n                chapter: null,\r\n                year: null,\r\n                description: \"\",\r\n                testDuration: null,\r\n                passRate: null,\r\n                solutionUrl: \"\",\r\n                imageUrl: \"\",\r\n                public: false,\r\n                isClassroomExam: false,\r\n            };\r\n            state.examImage = null;\r\n            state.questions = [];\r\n            state.questionImages = [];\r\n            state.statementImages = [];\r\n            state.solutionImages = [];\r\n            state.examFile = null;\r\n        },\r\n        setQuestions: (state, action) => {\r\n            state.questions = action.payload;\r\n        },\r\n        setShowAddImagesModal: (state, action) => {\r\n            state.showAddImagesModal = action.payload;\r\n        },\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(postExam.pending, (state) => {\r\n                state.loading = true;\r\n            })\r\n            .addCase(postExam.fulfilled, (state) => {\r\n                state.loading = false;\r\n            })\r\n            .addCase(postExam.rejected, (state) => {\r\n                state.loading = false;\r\n            })\r\n    }\r\n});\r\n\r\nexport const {\r\n    setLoading,\r\n    resetData,\r\n    nextStep,\r\n    prevStep,\r\n    setExamData,\r\n    setStep,\r\n    setExamImage,\r\n    setExamFile,\r\n    setQuestionTNContent,\r\n    setQuestionDSContent,\r\n    setQuestionTLNContent,\r\n    setCorrectAnswerTN,\r\n    setCorrectAnswerDS,\r\n    setCorrectAnswerTLN,\r\n    setQuestions,\r\n    setSelectedIndex,\r\n    setShowAddImagesModal,\r\n} = addExamSlice.actions;\r\nexport default addExamSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD;AACA,SAASC,UAAU,QAAQ,wBAAwB;AAEnD,OAAO,MAAMC,QAAQ,GAAGH,gBAAgB,CACpC,kBAAkB,EAClB,OAAAI,IAAA,EAAAC,KAAA,KAAuH;EAAA,IAAhH;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,SAAS;IAAEC,cAAc;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAS,CAAC,GAAAR,IAAA;EAAA,IAAE;IAAES;EAAS,CAAC,GAAAR,KAAA;EAC9G,OAAO,MAAMH,UAAU,CAACW,QAAQ,EAAEZ,OAAO,CAACa,WAAW,EAAE;IAAER,QAAQ;IAAEC,SAAS;IAAEC,SAAS;IAAEC,cAAc;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAS,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACjL,CACJ,CAAC;AAED,MAAMG,YAAY,GAAGhB,WAAW,CAAC;EAC7BiB,IAAI,EAAE,SAAS;EACfC,YAAY,EAAE;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,kBAAkB,EAAE,EAAE;IACtBC,MAAM,EAAE,eAAe;IACvBC,eAAe,EAAE,EAAE;IACnBC,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,kBAAkB,EAAE,KAAK;IACzBtB,QAAQ,EACR;MACIU,IAAI,EAAE,EAAE;MACRa,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,KAAK;MACbC,eAAe,EAAE;IACrB,CAAC;IACDhC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE;EACd,CAAC;EACD4B,QAAQ,EAAE;IACNC,QAAQ,EAAGC,KAAK,IAAK;MACjBA,KAAK,CAACvB,IAAI,EAAE;IAChB,CAAC;IACDwB,QAAQ,EAAGD,KAAK,IAAK;MACjBA,KAAK,CAACvB,IAAI,EAAE;IAChB,CAAC;IACDyB,gBAAgB,EAAEA,CAACF,KAAK,EAAEG,MAAM,KAAK;MACjCH,KAAK,CAACtB,aAAa,GAAGyB,MAAM,CAACC,OAAO;IACxC,CAAC;IACDC,UAAU,EAAEA,CAACL,KAAK,EAAEG,MAAM,KAAK;MAC3BH,KAAK,CAACxB,OAAO,GAAG2B,MAAM,CAACC,OAAO;IAClC,CAAC;IACDE,oBAAoB,EAAEA,CAACN,KAAK,EAAEG,MAAM,KAAK;MACrCH,KAAK,CAACrB,iBAAiB,GAAGwB,MAAM,CAACC,OAAO;IAC5C,CAAC;IACDG,oBAAoB,EAAEA,CAACP,KAAK,EAAEG,MAAM,KAAK;MACrCH,KAAK,CAACpB,iBAAiB,GAAGuB,MAAM,CAACC,OAAO;IAC5C,CAAC;IACDI,qBAAqB,EAAEA,CAACR,KAAK,EAAEG,MAAM,KAAK;MACtCH,KAAK,CAACnB,kBAAkB,GAAGsB,MAAM,CAACC,OAAO;IAC7C,CAAC;IACDK,kBAAkB,EAAEA,CAACT,KAAK,EAAEG,MAAM,KAAK;MACnCH,KAAK,CAACjB,eAAe,GAAGoB,MAAM,CAACC,OAAO;IAC1C,CAAC;IACDM,kBAAkB,EAAEA,CAACV,KAAK,EAAEG,MAAM,KAAK;MACnCH,KAAK,CAAChB,eAAe,GAAGmB,MAAM,CAACC,OAAO;IAC1C,CAAC;IACDO,mBAAmB,EAAEA,CAACX,KAAK,EAAEG,MAAM,KAAK;MACpCH,KAAK,CAACf,gBAAgB,GAAGkB,MAAM,CAACC,OAAO;IAC3C,CAAC;IAEDQ,WAAW,EAAEA,CAACZ,KAAK,EAAEG,MAAM,KAAK;MAC5B,MAAM;QAAEU,KAAK;QAAEC;MAAM,CAAC,GAAGX,MAAM,CAACC,OAAO;MACvC;MACAJ,KAAK,CAACpC,QAAQ,CAACiD,KAAK,CAAC,GAAGC,KAAK;IACjC,CAAC;IACDC,YAAY,EAAEA,CAACf,KAAK,EAAEG,MAAM,KAAK;MAC7BH,KAAK,CAACnC,SAAS,GAAGsC,MAAM,CAACC,OAAO;IACpC,CAAC;IACDY,WAAW,EAAEA,CAAChB,KAAK,EAAEG,MAAM,KAAK;MAC5BH,KAAK,CAAC9B,QAAQ,GAAGiC,MAAM,CAACC,OAAO;IACnC,CAAC;IACDa,OAAO,EAAEA,CAACjB,KAAK,EAAEG,MAAM,KAAK;MACxBH,KAAK,CAACvB,IAAI,GAAG0B,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDc,SAAS,EAAGlB,KAAK,IAAK;MAClBA,KAAK,CAACpC,QAAQ,GAAG;QACbU,IAAI,EAAE,EAAE;QACRa,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,IAAI;QACVC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,IAAI;QAClBC,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,KAAK;QACbC,eAAe,EAAE;MACrB,CAAC;MACDG,KAAK,CAACnC,SAAS,GAAG,IAAI;MACtBmC,KAAK,CAAClC,SAAS,GAAG,EAAE;MACpBkC,KAAK,CAACjC,cAAc,GAAG,EAAE;MACzBiC,KAAK,CAAChC,eAAe,GAAG,EAAE;MAC1BgC,KAAK,CAAC/B,cAAc,GAAG,EAAE;MACzB+B,KAAK,CAAC9B,QAAQ,GAAG,IAAI;IACzB,CAAC;IACDiD,YAAY,EAAEA,CAACnB,KAAK,EAAEG,MAAM,KAAK;MAC7BH,KAAK,CAAClC,SAAS,GAAGqC,MAAM,CAACC,OAAO;IACpC,CAAC;IACDgB,qBAAqB,EAAEA,CAACpB,KAAK,EAAEG,MAAM,KAAK;MACtCH,KAAK,CAACd,kBAAkB,GAAGiB,MAAM,CAACC,OAAO;IAC7C;EACJ,CAAC;EACDiB,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC9D,QAAQ,CAAC+D,OAAO,EAAGxB,KAAK,IAAK;MAClCA,KAAK,CAACxB,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACD+C,OAAO,CAAC9D,QAAQ,CAACgE,SAAS,EAAGzB,KAAK,IAAK;MACpCA,KAAK,CAACxB,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACD+C,OAAO,CAAC9D,QAAQ,CAACiE,QAAQ,EAAG1B,KAAK,IAAK;MACnCA,KAAK,CAACxB,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACT6B,UAAU;EACVa,SAAS;EACTnB,QAAQ;EACRE,QAAQ;EACRW,WAAW;EACXK,OAAO;EACPF,YAAY;EACZC,WAAW;EACXV,oBAAoB;EACpBC,oBAAoB;EACpBC,qBAAqB;EACrBC,kBAAkB;EAClBC,kBAAkB;EAClBC,mBAAmB;EACnBQ,YAAY;EACZjB,gBAAgB;EAChBkB;AACJ,CAAC,GAAG/C,YAAY,CAACsD,OAAO;AACxB,eAAetD,YAAY,CAACuD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}