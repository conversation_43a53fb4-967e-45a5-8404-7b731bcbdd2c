import NavigateBar from "../PageAddExam/NavigateBar";
import { Eye } from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import { setViewRightContent } from "src/features/questionsExam/questionsExamSlice";
import QuestionView from "./QuestionView";
import ImageView from "./ImageView";

const RightContent = () => {
    const { view } = useSelector((state) => state.questionsExam);
    const dispatch = useDispatch();

    return (
        <div className="flex flex-col h-[calc(100vh_-_138px)] bg-gray-50">
            {/* Compact Preview Header */}
            <div className="flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10">
                <div className="flex items-center gap-2">
                    <Eye className="w-3 h-3 text-gray-600" />
                    <h2 className="text-xs font-semibold text-gray-900">Xem trước</h2>
                </div>
            </div>

            {/* Scrollable Preview Content */}
            <div className="flex-1 overflow-y-auto p-3">
                <div className="bg-white rounded border border-gray-200 ">
                    <NavigateBar
                        list={[
                        {
                            id: 1,
                            name: 'Câu hỏi',
                            value: 'question'
                        },
                        {
                            id: 2,
                            name: 'Ảnh',
                            value: 'image'
                        }
                        ]}
                        active={view}
                        setActive={(value) => dispatch(setViewRightContent(value))}
                    />
                    {view === 'question' && <QuestionView />}
                    {view === 'image' && <ImageView />}
                </div>
            </div>
        </div>
    )
}

export default RightContent;