import { useSelector, useDispatch } from "react-redux";
import { setSelectedQuestion, setEditedExam, toggleAddImage, setShowAddImagesModal, setImages } from "src/features/examAI/examAISlice";
import { useState, useEffect } from "react";
import DropMenuBarAdmin from "src/components/dropMenu/OptionBarAdmin";
import SuggestInputBarAdmin from "src/components/input/suggestInputBarAdmin";
import { ImagePlus } from "lucide-react";
import AddImagesModal from "./AddImagesModal";

const ExamEditView = () => {
    const dispatch = useDispatch();
    const { editedExam } = useSelector((state) => state.examAI);
    const { codes } = useSelector((state) => state.codes);

    const handleExamChange = (field, value) => {
        const updatedExam = { ...editedExam, [field]: value };
        dispatch(setEditedExam(updatedExam));
    };

    if (!editedExam) {
        return (
            <div className="text-center text-gray-500 mt-8">
                <PERSON>h<PERSON>ng có thông tin đề thi để chỉnh sửa
            </div>
        );
    }

    return (
        <>
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Chỉnh sửa thông tin đề thi</h2>

            {/* Tên đề thi */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Tên đề thi:</label>
                <input
                    type="text"
                    className="w-full border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={editedExam?.name || ""}
                    onChange={(e) => handleExamChange('name', e.target.value)}
                    placeholder="Nhập tên đề thi..."
                />
            </div>

            {/* Loại đề thi */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Loại đề thi:</label>
                <DropMenuBarAdmin
                    selectedOption={editedExam?.typeOfExam}
                    onChange={(value) => handleExamChange('typeOfExam', value)}
                    options={Array.isArray(codes["exam type"]) ? codes["exam type"] : []}
                    placeholder="Chọn loại đề thi"
                />
            </div>

            {/* Lớp */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Lớp:</label>
                <DropMenuBarAdmin
                    selectedOption={editedExam?.class}
                    onChange={(value) => handleExamChange('class', value)}
                    options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                    placeholder="Chọn lớp"
                />
            </div>

            {/* Năm */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Năm:</label>
                <DropMenuBarAdmin
                    selectedOption={editedExam?.year}
                    onChange={(value) => handleExamChange('year', value)}
                    options={Array.isArray(codes["year"]) ? codes["year"] : []}
                    placeholder="Chọn năm"
                />
            </div>

            {/* Công khai */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Trạng thái:</label>
                <div className="flex items-center gap-6">
                    <label className="flex items-center gap-2 cursor-pointer">
                        <input
                            type="radio"
                            name="public"
                            checked={editedExam?.public === true}
                            onChange={() => handleExamChange('public', true)}
                            className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">Công khai</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                        <input
                            type="radio"
                            name="public"
                            checked={editedExam?.public === false}
                            onChange={() => handleExamChange('public', false)}
                            className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">Riêng tư</span>
                    </label>
                </div>
            </div>
        </>
    );
};

const QuestionEditContainer = () => {
    const dispatch = useDispatch();
    const { selectedQuestion } = useSelector((state) => state.examAI);
    const { codes } = useSelector((state) => state.codes);

    // Filter chapter options based on class
    useEffect(() => {
        if (Array.isArray(codes["chapter"])) {
            if (selectedQuestion?.class && selectedQuestion.class.trim() !== "") {
                setOptionChapter(
                    codes["chapter"].filter((code) => code.code.length === 5)
                );
            } else {
                setOptionChapter(codes["chapter"].filter((code) => code.code.length === 5));
            }
        } else {
            setOptionChapter([]);
        }
    }, [codes, selectedQuestion?.class]);

    const [optionChapter, setOptionChapter] = useState([]);

    const handleQuestionChange = (field, value) => {
        const updatedQuestion = { ...selectedQuestion, [field]: value };
        dispatch(setSelectedQuestion(updatedQuestion));
    };

    const handleStatementChange = (index, field, value) => {
        const updatedStatements = [...selectedQuestion.statement1s];

        // ✅ Tạo bản sao mới của object tại index
        const updatedStatement = {
            ...updatedStatements[index],
            [field]: value,
        };

        // ✅ Gán lại object đã sửa vào mảng mới
        updatedStatements[index] = updatedStatement;

        const updatedQuestion = {
            ...selectedQuestion,
            statement1s: updatedStatements,
        };

        dispatch(setSelectedQuestion(updatedQuestion));
    };


    return (
        <>
            {/* Nội dung câu hỏi */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Nội dung câu hỏi:</label>
                <textarea
                    className="w-full min-h-[150px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={selectedQuestion?.content || ""}
                    onChange={(e) => handleQuestionChange('content', e.target.value)}
                    placeholder="Nhập nội dung câu hỏi..."
                />
            </div>

            {/* Lớp */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Lớp:</label>
                <DropMenuBarAdmin
                    selectedOption={selectedQuestion?.class}
                    onChange={(value) => handleQuestionChange('class', value)}
                    options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                    placeholder="Chọn lớp"
                />
            </div>

            {/* Độ khó */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Độ khó:</label>
                <DropMenuBarAdmin
                    selectedOption={selectedQuestion?.difficulty}
                    onChange={(value) => handleQuestionChange('difficulty', value)}
                    options={Array.isArray(codes["difficulty"]) ? codes["difficulty"] : []}
                    placeholder="Chọn độ khó"
                />
            </div>

            {/* Chương */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Chương:</label>
                <SuggestInputBarAdmin
                    selectedOption={selectedQuestion?.chapter}
                    onChange={(value) => handleQuestionChange('chapter', value)}
                    options={optionChapter}
                    placeholder="Chọn chương"
                />
            </div>

            {selectedQuestion?.typeOfQuestion === "TLN" && (
                <>
                    <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Đáp án:</label>
                        <input
                            type="text"
                            className="w-full border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            value={selectedQuestion?.correctAnswer || ""}
                            onChange={(e) => handleQuestionChange('correctAnswer', e.target.value)}
                            placeholder="Nhập đáp án..."
                        />
                    </div>
                </>
            )}

            {/* Lời giải */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Lời giải:</label>
                <textarea
                    className="w-full min-h-[120px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={selectedQuestion?.solution || ""}
                    onChange={(e) => handleQuestionChange('solution', e.target.value)}
                    placeholder="Nhập lời giải..."
                />
            </div>

            {/* Các mệnh đề/đáp án */}
            {selectedQuestion?.statement1s && selectedQuestion.statement1s.length > 0 && (
                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">Các mệnh đề:</label>
                    <div className="space-y-4">
                        {selectedQuestion.statement1s.map((statement, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                <div className="flex items-center justify-between mb-3">
                                    <span className="text-sm font-medium text-gray-600">
                                        Mệnh đề {String.fromCharCode(65 + index)}
                                    </span>
                                    <div className="flex items-center gap-4">
                                        <label className="flex items-center gap-2 cursor-pointer">
                                            <input
                                                type="radio"
                                                name={`statement-${index}`}
                                                checked={statement.isCorrect === true}
                                                onChange={() => handleStatementChange(index, 'isCorrect', true)}
                                                className="w-4 h-4 text-green-600 focus:ring-green-500"
                                            />
                                            <span className="text-sm text-green-600 font-medium">Đúng</span>
                                        </label>
                                        <label className="flex items-center gap-2 cursor-pointer">
                                            <input
                                                type="radio"
                                                name={`statement-${index}`}
                                                checked={statement.isCorrect === false}
                                                onChange={() => handleStatementChange(index, 'isCorrect', false)}
                                                className="w-4 h-4 text-red-600 focus:ring-red-500"
                                            />
                                            <span className="text-sm text-red-600 font-medium">Sai</span>
                                        </label>
                                    </div>
                                </div>
                                <textarea
                                    className="w-full min-h-[80px] border border-gray-300 rounded-lg p-2 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    value={statement.content || ""}
                                    onChange={(e) => handleStatementChange(index, 'content', e.target.value)}
                                    placeholder={`Nhập nội dung mệnh đề ${String.fromCharCode(65 + index)}...`}
                                />
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </>
    )
}

const QuestionImageContainer = () => {
    const { images } = useSelector((state) => state.images);

    return (
        <div className="flex flex-col gap-4">
            <button
                onClick={() => { }}
                className="px-2 py-1 w-full text-sm rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors"
            >
                Thêm ảnh
            </button>
            {images?.ImageFormN8N?.map((image, index) => (
                <div key={index} className="mb-6 group relative w-full">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hình ảnh {index + 1}:
                    </label>

                    <div
                        className="relative w-full h-48 rounded-lg overflow-hidden border transition-all duration-300 
                   hover:border-sky-400 hover:shadow-lg group cursor-move"
                        draggable
                        onDragStart={(e) => {
                            e.dataTransfer.setData("text/plain", image);
                        }}
                    >
                        <img
                            src={image}
                            alt={`image-${index}`}
                            className="w-full h-full object-contain transition-all duration-300 group-hover:brightness-75"
                        />

                        {/* Icon hiện khi hover */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                            <ImagePlus className="w-8 h-8 text-white bg-sky-500 p-1 rounded-full shadow-lg" />
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
}


const QuestionEditView = () => {
    const dispatch = useDispatch();
    const { selectedQuestion, isAddImage } = useSelector((state) => state.examAI);

    if (!selectedQuestion) {
        return (
            <div className="text-center text-gray-500 mt-8">
                Chọn một câu hỏi để chỉnh sửa
            </div>
        );
    }

    return (
        <>
            <div className="flex text-center justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-800">Chỉnh sửa câu hỏi</h2>
                <div className="flex gap-2">
                    <button
                        onClick={() => dispatch(setShowAddImagesModal(true))}
                        className="px-3 py-1 rounded-lg bg-green-500 text-white hover:bg-green-600 transition-colors flex items-center gap-1"
                    >
                        <ImagePlus className="w-4 h-4" />
                        Thêm ảnh
                    </button>
                    <button
                        onClick={() => dispatch(toggleAddImage())}
                        className="px-2 py-1 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors"
                    >
                        {isAddImage ? "Thông tin" : "Kéo ảnh"}
                    </button>
                </div>
            </div>
            {isAddImage ? <QuestionImageContainer /> : <QuestionEditContainer />}
        </>
    );
}


export const RightContent = () => {
    const dispatch = useDispatch();
    const { viewEdit, images } = useSelector((state) => state.examAI);

    // Load existing images when component mounts
    useEffect(() => {
        // Mock data for existing images - replace with actual API call
        const existingImages = [
            { ImageFormN8N: "https://example.com/image1.jpg" },
            { ImageFormN8N: "https://example.com/image2.jpg" },
            // Add more mock images or fetch from API
        ];

        if (images.length === 0) {
            dispatch(setImages(existingImages));
        }
    }, [dispatch, images.length]);

    return (
        <>
            <div className="w-[25rem] p-4 overflow-y-auto bg-white border-l border-gray-200 flex-shrink-0">
                {viewEdit === 'exam' && <ExamEditView />}
                {viewEdit === 'question' && <QuestionEditView />}
            </div>
            <AddImagesModal />
        </>
    );
};

export default RightContent;