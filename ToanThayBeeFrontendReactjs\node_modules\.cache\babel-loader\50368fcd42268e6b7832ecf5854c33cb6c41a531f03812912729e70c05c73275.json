{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\AddExamAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport { postExam } from \"src/features/exam/examSlice\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport ImageUpload from \"src/components/image/UploadImage\";\nimport UploadPdf from \"src/components/UploadPdf\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport { ArrowLeft, Save, Eye, FileText, Plus, Trash2 } from \"lucide-react\";\n\n// Left Panel Component - Form Controls\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ExamFormPanel = _ref => {\n  let {\n    examData,\n    setExamData,\n    examImage,\n    setExamImage,\n    examFile,\n    setExamFile,\n    codes,\n    optionChapter,\n    currentStep,\n    setCurrentStep,\n    questions,\n    setQuestions,\n    questionImages,\n    setQuestionImages,\n    statementImages,\n    setStatementImages,\n    solutionImages,\n    setSolutionImages,\n    contentTN,\n    setContentTN,\n    contentDS,\n    setContentDS,\n    contentTLN,\n    setContentTLN,\n    correctAnswerTN,\n    setCorrectAnswerTN,\n    correctAnswerDS,\n    setCorrectAnswerDS,\n    correctAnswerTLN,\n    setCorrectAnswerTLN,\n    previewTN,\n    previewDS,\n    previewTLN,\n    handleSubmit,\n    loading\n  } = _ref;\n  const isStep1 = currentStep === 1;\n  const isStep2 = currentStep === 2;\n  const isStep3 = currentStep === 3;\n  const handleNext = () => {\n    if (currentStep < 3) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const handlePrev = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-1/2 bg-white border-r border-gray-200 p-6 overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900 mb-2\",\n        children: \"T\\u1EA1o \\u0111\\u1EC1 thi m\\u1EDBi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(isStep1 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'),\n          children: \"1. Th\\xF4ng tin \\u0111\\u1EC1 thi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(isStep2 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'),\n          children: \"2. C\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(isStep3 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'),\n          children: \"3. X\\xE1c nh\\u1EADn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this), isStep1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: [\"T\\xEAn \\u0111\\u1EC1 thi \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 44\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            required: true,\n            value: examData.name,\n            onChange: e => setExamData({\n              ...examData,\n              name: e.target.value\n            }),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n            placeholder: \"Nh\\u1EADp t\\xEAn \\u0111\\u1EC1 thi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"\\u1EA2nh \\u0111\\u1EC1 thi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(ImageUpload, {\n            image: examImage,\n            setImage: setExamImage,\n            inputId: \"exam-image-upload\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: [\"Ki\\u1EC3u \\u0111\\u1EC1 \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: examData.typeOfExam,\n            onChange: option => setExamData({\n              ...examData,\n              typeOfExam: option\n            }),\n            options: Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : []\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: examData.class,\n            onChange: option => setExamData({\n              ...examData,\n              class: option\n            }),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: [\"N\\u0103m \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: examData.year,\n            onChange: option => setExamData({\n              ...examData,\n              year: option\n            }),\n            options: Array.isArray(codes[\"year\"]) ? codes[\"year\"] : []\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 25\n        }, this), examData.typeOfExam === \"OT\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Ch\\u01B0\\u01A1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: examData.chapter,\n            onChange: option => setExamData({\n              ...examData,\n              chapter: option\n            }),\n            options: optionChapter\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Th\\u1EDDi gian l\\xE0m b\\xE0i (ph\\xFAt)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: examData.testDuration || '',\n            onChange: e => setExamData({\n              ...examData,\n              testDuration: e.target.value\n            }),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n            placeholder: \"90\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"\\u0110i\\u1EC3m \\u0111\\u1EA1t (%)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: examData.passRate || '',\n            onChange: e => setExamData({\n              ...examData,\n              passRate: e.target.value\n            }),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n            placeholder: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"M\\xF4 t\\u1EA3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: examData.description,\n          onChange: e => setExamData({\n            ...examData,\n            description: e.target.value\n          }),\n          rows: 3,\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"M\\xF4 t\\u1EA3 v\\u1EC1 \\u0111\\u1EC1 thi...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: examData.public,\n            onChange: e => setExamData({\n              ...examData,\n              public: e.target.checked\n            }),\n            className: \"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-gray-700\",\n            children: \"C\\xF4ng khai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: examData.isClassroomExam,\n            onChange: e => setExamData({\n              ...examData,\n              isClassroomExam: e.target.checked\n            }),\n            className: \"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-gray-700\",\n            children: \"\\u0110\\u1EC1 thi tr\\xEAn l\\u1EDBp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"File \\u0111\\u1EC1 thi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(UploadPdf, {\n          setPdf: setExamFile,\n          deleteButton: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 17\n    }, this), isStep2 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-16 h-16 mx-auto text-gray-400 mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"B\\u1EA1n c\\xF3 th\\u1EC3 th\\xEAm c\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m, \\u0111\\xFAng sai, v\\xE0 tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full px-4 py-3 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                className: \"w-5 h-5 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600 font-medium\",\n                children: \"Th\\xEAm c\\xE2u tr\\u1EAFc nghi\\u1EC7m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full px-4 py-3 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                className: \"w-5 h-5 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-600 font-medium\",\n                children: \"Th\\xEAm c\\xE2u \\u0111\\xFAng sai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full px-4 py-3 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                className: \"w-5 h-5 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-purple-600 font-medium\",\n                children: \"Th\\xEAm c\\xE2u tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 21\n      }, this), questions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: [\"Danh s\\xE1ch c\\xE2u h\\u1ECFi (\", questions.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 29\n        }, this), questions.map((question, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border border-gray-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded\",\n                  children: [\"C\\xE2u \", index + 1]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded\",\n                  children: question.questionData.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-900\",\n                children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                  text: question.questionData.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 37\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 33\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 17\n    }, this), isStep3 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Save, {\n            className: \"w-8 h-8 text-green-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"X\\xE1c nh\\u1EADn t\\u1EA1o \\u0111\\u1EC1 thi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Ki\\u1EC3m tra l\\u1EA1i th\\xF4ng tin v\\xE0 nh\\u1EA5n \\\"T\\u1EA1o \\u0111\\u1EC1 thi\\\" \\u0111\\u1EC3 ho\\xE0n th\\xE0nh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold text-gray-900 mb-3\",\n          children: \"Th\\xF4ng tin t\\xF3m t\\u1EAFt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"T\\xEAn \\u0111\\u1EC1 thi:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 font-medium\",\n              children: examData.name || \"Chưa nhập\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Ki\\u1EC3u \\u0111\\u1EC1:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 font-medium\",\n              children: examData.typeOfExam || \"Chưa chọn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"L\\u1EDBp:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 font-medium\",\n              children: examData.class || \"Chưa chọn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"N\\u0103m:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 font-medium\",\n              children: examData.year || \"Chưa chọn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"S\\u1ED1 c\\xE2u h\\u1ECFi:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 font-medium\",\n              children: questions.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Tr\\u1EA1ng th\\xE1i:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 font-medium\",\n              children: examData.public ? \"Công khai\" : \"Riêng tư\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 21\n      }, this), (!examData.name || !examData.typeOfExam || !examData.class || !examData.year) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"font-medium text-red-800 mb-2\",\n          children: \"Th\\xF4ng tin b\\u1EAFt bu\\u1ED9c ch\\u01B0a \\u0111\\u1EA7y \\u0111\\u1EE7:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-sm text-red-700 space-y-1\",\n          children: [!examData.name && /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 T\\xEAn \\u0111\\u1EC1 thi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 52\n          }, this), !examData.typeOfExam && /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Ki\\u1EC3u \\u0111\\u1EC1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 58\n          }, this), !examData.class && /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 L\\u1EDBp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 53\n          }, this), !examData.year && /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 N\\u0103m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 52\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between mt-8 pt-6 border-t border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handlePrev,\n        disabled: currentStep === 1,\n        className: \"px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\",\n        children: \"Quay l\\u1EA1i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 17\n      }, this), currentStep < 3 ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleNext,\n        className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n        children: \"Ti\\u1EBFp theo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSubmit,\n        disabled: loading,\n        className: \"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2\",\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            size: \"sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 33\n          }, this), \"\\u0110ang t\\u1EA1o...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 33\n          }, this), \"T\\u1EA1o \\u0111\\u1EC1 thi\"]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 9\n  }, this);\n};\n\n// Right Panel Component - Live Preview\n_c = ExamFormPanel;\nconst ExamPreviewPanel = _ref2 => {\n  let {\n    examData,\n    examImage,\n    questions,\n    currentStep\n  } = _ref2;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-1/2 bg-gray-50 p-6 overflow-y-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Eye, {\n          className: \"w-5 h-5 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Xem tr\\u01B0\\u1EDBc \\u0111\\u1EC1 thi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900 mb-2\",\n              children: examData.name || \"Tên đề thi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Ki\\u1EC3u \\u0111\\u1EC1:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 37\n                }, this), \" \", examData.typeOfExam || \"Chưa chọn\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"L\\u1EDBp:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 37\n                }, this), \" \", examData.class || \"Chưa chọn\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"N\\u0103m:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 37\n                }, this), \" \", examData.year || \"Chưa chọn\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 33\n              }, this), examData.chapter && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Ch\\u01B0\\u01A1ng:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 41\n                }, this), \" \", examData.chapter]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 37\n              }, this), examData.testDuration && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Th\\u1EDDi gian:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 41\n                }, this), \" \", examData.testDuration, \" ph\\xFAt\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 37\n              }, this), examData.passRate && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"\\u0110i\\u1EC3m \\u0111\\u1EA1t:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 41\n                }, this), \" \", examData.passRate, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 29\n            }, this), examData.description && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-gray-700\",\n                children: \"M\\xF4 t\\u1EA3:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mt-1\",\n                children: examData.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 25\n          }, this), examImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-32 h-32 border border-gray-200 rounded-lg overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: URL.createObjectURL(examImage),\n              alt: \"Exam preview\",\n              className: \"w-full h-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2\",\n          children: [examData.public && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\",\n            children: \"C\\xF4ng khai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 29\n          }, this), examData.isClassroomExam && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full\",\n            children: \"\\u0110\\u1EC1 thi tr\\xEAn l\\u1EDBp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 29\n          }, this), !examData.public && !examData.isClassroomExam && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full\",\n            children: \"Ri\\xEAng t\\u01B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 17\n      }, this), currentStep >= 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-5 h-5 text-gray-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: [\"C\\xE2u h\\u1ECFi (\", questions.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 25\n        }, this), questions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8 text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi n\\xE0o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: \"Th\\xEAm c\\xE2u h\\u1ECFi \\u1EDF b\\u01B0\\u1EDBc 2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: questions.map((question, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-200 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 text-sm font-medium rounded-full flex items-center justify-center\",\n                children: index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-900 mb-2\",\n                  children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n                    text: question.questionData.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 49\n                }, this), question.questionData.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: question.questionData.imageUrl,\n                    alt: \"Question\",\n                    className: \"max-w-full h-auto rounded border\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 57\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Lo\\u1EA1i: \", question.questionData.type, \" | L\\u1EDBp: \", question.questionData.class, \" | \\u0110\\u1ED9 kh\\xF3: \", question.questionData.difficulty]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 41\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 37\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 21\n      }, this), currentStep === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 pt-6 border-t border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-gray-900 mb-3\",\n          children: \"T\\xF3m t\\u1EAFt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-700\",\n              children: \"T\\u1ED5ng s\\u1ED1 c\\xE2u h\\u1ECFi:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 text-gray-900\",\n              children: questions.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-700\",\n              children: \"Tr\\u1EA1ng th\\xE1i:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 text-gray-900\",\n              children: examData.public ? \"Công khai\" : \"Riêng tư\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 424,\n    columnNumber: 9\n  }, this);\n};\n\n// Main Component\n_c2 = ExamPreviewPanel;\nexport const AddExamAdmin = () => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const {\n    loading\n  } = useSelector(state => state.exams);\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n\n  // Form state\n  const [currentStep, setCurrentStep] = useState(1);\n  const [examData, setExamData] = useState({\n    name: \"\",\n    typeOfExam: null,\n    class: null,\n    chapter: null,\n    year: null,\n    description: \"\",\n    testDuration: null,\n    passRate: null,\n    solutionUrl: \"\",\n    imageUrl: \"\",\n    public: false,\n    isClassroomExam: false\n  });\n\n  // File states\n  const [examImage, setExamImage] = useState(null);\n  const [examFile, setExamFile] = useState(null);\n\n  // Question states\n  const [questions, setQuestions] = useState([]);\n  const [questionImages, setQuestionImages] = useState([]);\n  const [statementImages, setStatementImages] = useState([]);\n  const [solutionImages, setSolutionImages] = useState([]);\n\n  // Question content states\n  const [contentTN, setContentTN] = useState(\"\");\n  const [contentDS, setContentDS] = useState(\"\");\n  const [contentTLN, setContentTLN] = useState(\"\");\n  const [correctAnswerTN, setCorrectAnswerTN] = useState(\"\");\n  const [correctAnswerDS, setCorrectAnswerDS] = useState(\"\");\n  const [correctAnswerTLN, setCorrectAnswerTLN] = useState(\"\");\n\n  // Preview states\n  const [previewTN, setPreviewTN] = useState([]);\n  const [previewDS, setPreviewDS] = useState([]);\n  const [previewTLN, setPreviewTLN] = useState([]);\n\n  // Chapter options\n  const optionChapter = Array.isArray(codes[\"chapter\"]) ? codes[\"chapter\"] : [];\n\n  // Load codes on mount\n  useEffect(() => {\n    dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\n  }, [dispatch]);\n\n  // Update preview when answers change\n  useEffect(() => {\n    const normalize = text => text.trim().replace(/-/g, \"\").toUpperCase().split(/\\s+/).filter(Boolean);\n    const normalizeTLN = text => text.trim().toUpperCase().split(/\\s+/).filter(Boolean);\n    setPreviewTN(normalize(correctAnswerTN));\n    setPreviewDS(normalize(correctAnswerDS));\n    setPreviewTLN(normalizeTLN(correctAnswerTLN));\n  }, [correctAnswerTN, correctAnswerDS, correctAnswerTLN]);\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    var _examData$passRate, _examData$passRate2;\n    e.preventDefault();\n    const sanitize = val => val && typeof val === \"string\" ? val.trim() || null : val;\n    const sanitizeExamData = {\n      name: sanitize(examData.name),\n      typeOfExam: sanitize(examData.typeOfExam),\n      class: sanitize(examData.class),\n      chapter: sanitize(examData.chapter),\n      year: sanitize(examData.year),\n      description: sanitize(examData.description),\n      testDuration: sanitize(examData.testDuration),\n      passRate: (_examData$passRate = examData.passRate) !== null && _examData$passRate !== void 0 && _examData$passRate.trim() ? (_examData$passRate2 = examData.passRate) === null || _examData$passRate2 === void 0 ? void 0 : _examData$passRate2.trim() : null,\n      solutionUrl: sanitize(examData.solutionUrl),\n      public: examData.public,\n      imageUrl: sanitize(examData.imageUrl),\n      isClassroomExam: examData.isClassroomExam\n    };\n    try {\n      await dispatch(postExam({\n        examData: sanitizeExamData,\n        examImage,\n        questions,\n        questionImages,\n        statementImages,\n        solutionImages,\n        examFile\n      })).unwrap();\n\n      // Navigate back to exam management\n      navigate('/admin/exam-management');\n    } catch (error) {\n      console.error('Error creating exam:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-50 flex flex-col min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 flex flex-col \".concat(closeSidebar ? \"ml-[104px]\" : \"ml-64\"),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/admin/exam-management'),\n            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(ArrowLeft, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"T\\u1EA1o \\u0111\\u1EC1 thi m\\u1EDBi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"T\\u1EA1o \\u0111\\u1EC1 thi v\\u1EDBi c\\xE2u h\\u1ECFi v\\xE0 c\\u1EA5u h\\xECnh chi ti\\u1EBFt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 703,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(ExamFormPanel, {\n          examData: examData,\n          setExamData: setExamData,\n          examImage: examImage,\n          setExamImage: setExamImage,\n          examFile: examFile,\n          setExamFile: setExamFile,\n          codes: codes,\n          optionChapter: optionChapter,\n          currentStep: currentStep,\n          setCurrentStep: setCurrentStep,\n          questions: questions,\n          setQuestions: setQuestions,\n          questionImages: questionImages,\n          setQuestionImages: setQuestionImages,\n          statementImages: statementImages,\n          setStatementImages: setStatementImages,\n          solutionImages: solutionImages,\n          setSolutionImages: setSolutionImages,\n          contentTN: contentTN,\n          setContentTN: setContentTN,\n          contentDS: contentDS,\n          setContentDS: setContentDS,\n          contentTLN: contentTLN,\n          setContentTLN: setContentTLN,\n          correctAnswerTN: correctAnswerTN,\n          setCorrectAnswerTN: setCorrectAnswerTN,\n          correctAnswerDS: correctAnswerDS,\n          setCorrectAnswerDS: setCorrectAnswerDS,\n          correctAnswerTLN: correctAnswerTLN,\n          setCorrectAnswerTLN: setCorrectAnswerTLN,\n          previewTN: previewTN,\n          previewDS: previewDS,\n          previewTLN: previewTLN,\n          handleSubmit: handleSubmit,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ExamPreviewPanel, {\n          examData: examData,\n          examImage: examImage,\n          questions: questions,\n          currentStep: currentStep\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 719,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 701,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 699,\n    columnNumber: 9\n  }, this);\n};\n_s(AddExamAdmin, \"OuHgUsCQGOFm2FCT+h4NBGc8D4U=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector, useSelector];\n});\n_c3 = AddExamAdmin;\nexport default AddExamAdmin;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ExamFormPanel\");\n$RefreshReg$(_c2, \"ExamPreviewPanel\");\n$RefreshReg$(_c3, \"AddExamAdmin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "useNavigate", "AdminSidebar", "fetchCodesByType", "postExam", "DropMenuBarAdmin", "SuggestInputBarAdmin", "ImageUpload", "UploadPdf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "ArrowLeft", "Save", "Eye", "FileText", "Plus", "Trash2", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ExamFormPanel", "_ref", "examData", "setExamData", "examImage", "setExamImage", "examFile", "setExamFile", "codes", "optionChapter", "currentStep", "setCurrentStep", "questions", "setQuestions", "questionImages", "setQuestionImages", "statementImages", "setStatementImages", "solutionImages", "setSolutionImages", "contentTN", "setContentTN", "contentDS", "setContentDS", "contentTLN", "setContentTLN", "correctAnswerTN", "setCorrectAnswerTN", "correctAnswerDS", "setCorrectAnswerDS", "correctAnswerTLN", "setCorrectAnswerTLN", "previewTN", "previewDS", "previewTLN", "handleSubmit", "loading", "isStep1", "isStep2", "isStep3", "handleNext", "handlePrev", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "concat", "type", "required", "value", "name", "onChange", "e", "target", "placeholder", "image", "setImage", "inputId", "selectedOption", "typeOfExam", "option", "options", "Array", "isArray", "class", "year", "chapter", "testDuration", "passRate", "description", "rows", "checked", "public", "isClassroomExam", "setPdf", "deleteButton", "length", "map", "question", "index", "questionData", "text", "content", "onClick", "disabled", "size", "_c", "ExamPreviewPanel", "_ref2", "src", "URL", "createObjectURL", "alt", "imageUrl", "difficulty", "_c2", "AddExamAdmin", "_s", "dispatch", "navigate", "state", "exams", "closeSidebar", "sidebar", "solutionUrl", "setPreviewTN", "setPreviewDS", "setPreviewTLN", "normalize", "trim", "replace", "toUpperCase", "split", "filter", "Boolean", "normalizeTLN", "_examData$passRate", "_examData$passRate2", "preventDefault", "sanitize", "val", "sanitizeExamData", "unwrap", "error", "console", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/AddExamAdmin.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport { postExam } from \"src/features/exam/examSlice\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport ImageUpload from \"src/components/image/UploadImage\";\r\nimport UploadPdf from \"src/components/UploadPdf\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport { ArrowLeft, Save, Eye, FileText, Plus, Trash2 } from \"lucide-react\";\r\n\r\n// Left Panel Component - Form Controls\r\nconst ExamFormPanel = ({\r\n    examData,\r\n    setExamData,\r\n    examImage,\r\n    setExamImage,\r\n    examFile,\r\n    setExamFile,\r\n    codes,\r\n    optionChapter,\r\n    currentStep,\r\n    setCurrentStep,\r\n    questions,\r\n    setQuestions,\r\n    questionImages,\r\n    setQuestionImages,\r\n    statementImages,\r\n    setStatementImages,\r\n    solutionImages,\r\n    setSolutionImages,\r\n    contentTN,\r\n    setContentTN,\r\n    contentDS,\r\n    setContentDS,\r\n    contentTLN,\r\n    setContentTLN,\r\n    correctAnswerTN,\r\n    setCorrectAnswerTN,\r\n    correctAnswerDS,\r\n    setCorrectAnswerDS,\r\n    correctAnswerTLN,\r\n    setCorrectAnswerTLN,\r\n    previewTN,\r\n    previewDS,\r\n    previewTLN,\r\n    handleSubmit,\r\n    loading\r\n}) => {\r\n    const isStep1 = currentStep === 1;\r\n    const isStep2 = currentStep === 2;\r\n    const isStep3 = currentStep === 3;\r\n\r\n    const handleNext = () => {\r\n        if (currentStep < 3) {\r\n            setCurrentStep(currentStep + 1);\r\n        }\r\n    };\r\n\r\n    const handlePrev = () => {\r\n        if (currentStep > 1) {\r\n            setCurrentStep(currentStep - 1);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"w-1/2 bg-white border-r border-gray-200 p-6 overflow-y-auto\">\r\n            {/* Header */}\r\n            <div className=\"mb-6\">\r\n                <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Tạo đề thi mới</h1>\r\n                <div className=\"flex items-center space-x-4\">\r\n                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${\r\n                        isStep1 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'\r\n                    }`}>\r\n                        1. Thông tin đề thi\r\n                    </div>\r\n                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${\r\n                        isStep2 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'\r\n                    }`}>\r\n                        2. Câu hỏi\r\n                    </div>\r\n                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${\r\n                        isStep3 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'\r\n                    }`}>\r\n                        3. Xác nhận\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Step 1: Exam Information */}\r\n            {isStep1 && (\r\n                <div className=\"space-y-6\">\r\n                    {/* Name and Image */}\r\n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                Tên đề thi <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                type=\"text\"\r\n                                required\r\n                                value={examData.name}\r\n                                onChange={(e) => setExamData({ ...examData, name: e.target.value })}\r\n                                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                                placeholder=\"Nhập tên đề thi\"\r\n                            />\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                Ảnh đề thi\r\n                            </label>\r\n                            <ImageUpload\r\n                                image={examImage}\r\n                                setImage={setExamImage}\r\n                                inputId=\"exam-image-upload\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Type and Class */}\r\n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                Kiểu đề <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={examData.typeOfExam}\r\n                                onChange={(option) => setExamData({ ...examData, typeOfExam: option })}\r\n                                options={Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : []}\r\n                            />\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                Lớp <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={examData.class}\r\n                                onChange={(option) => setExamData({ ...examData, class: option })}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Year and Chapter */}\r\n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                Năm <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={examData.year}\r\n                                onChange={(option) => setExamData({ ...examData, year: option })}\r\n                                options={Array.isArray(codes[\"year\"]) ? codes[\"year\"] : []}\r\n                            />\r\n                        </div>\r\n                        {examData.typeOfExam === \"OT\" && (\r\n                            <div>\r\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                    Chương\r\n                                </label>\r\n                                <SuggestInputBarAdmin\r\n                                    selectedOption={examData.chapter}\r\n                                    onChange={(option) => setExamData({ ...examData, chapter: option })}\r\n                                    options={optionChapter}\r\n                                />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Duration and Pass Rate */}\r\n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                Thời gian làm bài (phút)\r\n                            </label>\r\n                            <input\r\n                                type=\"number\"\r\n                                value={examData.testDuration || ''}\r\n                                onChange={(e) => setExamData({ ...examData, testDuration: e.target.value })}\r\n                                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                                placeholder=\"90\"\r\n                            />\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                Điểm đạt (%)\r\n                            </label>\r\n                            <input\r\n                                type=\"number\"\r\n                                value={examData.passRate || ''}\r\n                                onChange={(e) => setExamData({ ...examData, passRate: e.target.value })}\r\n                                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                                placeholder=\"50\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Description */}\r\n                    <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Mô tả\r\n                        </label>\r\n                        <textarea\r\n                            value={examData.description}\r\n                            onChange={(e) => setExamData({ ...examData, description: e.target.value })}\r\n                            rows={3}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                            placeholder=\"Mô tả về đề thi...\"\r\n                        />\r\n                    </div>\r\n\r\n                    {/* Checkboxes */}\r\n                    <div className=\"space-y-3\">\r\n                        <label className=\"flex items-center\">\r\n                            <input\r\n                                type=\"checkbox\"\r\n                                checked={examData.public}\r\n                                onChange={(e) => setExamData({ ...examData, public: e.target.checked })}\r\n                                className=\"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"\r\n                            />\r\n                            <span className=\"ml-2 text-gray-700\">Công khai</span>\r\n                        </label>\r\n                        <label className=\"flex items-center\">\r\n                            <input\r\n                                type=\"checkbox\"\r\n                                checked={examData.isClassroomExam}\r\n                                onChange={(e) => setExamData({ ...examData, isClassroomExam: e.target.checked })}\r\n                                className=\"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"\r\n                            />\r\n                            <span className=\"ml-2 text-gray-700\">Đề thi trên lớp</span>\r\n                        </label>\r\n                    </div>\r\n\r\n                    {/* File Upload */}\r\n                    <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            File đề thi\r\n                        </label>\r\n                        <UploadPdf\r\n                            setPdf={setExamFile}\r\n                            deleteButton={false}\r\n                        />\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {/* Step 2: Questions */}\r\n            {isStep2 && (\r\n                <div className=\"space-y-6\">\r\n                    <div className=\"text-center py-8\">\r\n                        <FileText className=\"w-16 h-16 mx-auto text-gray-400 mb-4\" />\r\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Thêm câu hỏi</h3>\r\n                        <p className=\"text-gray-600 mb-4\">\r\n                            Bạn có thể thêm câu hỏi trắc nghiệm, đúng sai, và trả lời ngắn\r\n                        </p>\r\n                        <div className=\"space-y-3\">\r\n                            <button className=\"w-full px-4 py-3 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors\">\r\n                                <div className=\"flex items-center justify-center gap-2\">\r\n                                    <Plus className=\"w-5 h-5 text-blue-600\" />\r\n                                    <span className=\"text-blue-600 font-medium\">Thêm câu trắc nghiệm</span>\r\n                                </div>\r\n                            </button>\r\n                            <button className=\"w-full px-4 py-3 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors\">\r\n                                <div className=\"flex items-center justify-center gap-2\">\r\n                                    <Plus className=\"w-5 h-5 text-green-600\" />\r\n                                    <span className=\"text-green-600 font-medium\">Thêm câu đúng sai</span>\r\n                                </div>\r\n                            </button>\r\n                            <button className=\"w-full px-4 py-3 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors\">\r\n                                <div className=\"flex items-center justify-center gap-2\">\r\n                                    <Plus className=\"w-5 h-5 text-purple-600\" />\r\n                                    <span className=\"text-purple-600 font-medium\">Thêm câu trả lời ngắn</span>\r\n                                </div>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Questions List */}\r\n                    {questions.length > 0 && (\r\n                        <div className=\"space-y-4\">\r\n                            <h4 className=\"text-lg font-semibold text-gray-900\">\r\n                                Danh sách câu hỏi ({questions.length})\r\n                            </h4>\r\n                            {questions.map((question, index) => (\r\n                                <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\r\n                                    <div className=\"flex items-start justify-between\">\r\n                                        <div className=\"flex-1\">\r\n                                            <div className=\"flex items-center gap-2 mb-2\">\r\n                                                <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded\">\r\n                                                    Câu {index + 1}\r\n                                                </span>\r\n                                                <span className=\"px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded\">\r\n                                                    {question.questionData.type}\r\n                                                </span>\r\n                                            </div>\r\n                                            <div className=\"text-gray-900\">\r\n                                                <LatexRenderer text={question.questionData.content} />\r\n                                            </div>\r\n                                        </div>\r\n                                        <button className=\"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\">\r\n                                            <Trash2 className=\"w-4 h-4\" />\r\n                                        </button>\r\n                                    </div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            )}\r\n\r\n            {/* Step 3: Confirmation */}\r\n            {isStep3 && (\r\n                <div className=\"space-y-6\">\r\n                    <div className=\"text-center\">\r\n                        <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                            <Save className=\"w-8 h-8 text-green-600\" />\r\n                        </div>\r\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Xác nhận tạo đề thi</h3>\r\n                        <p className=\"text-gray-600\">\r\n                            Kiểm tra lại thông tin và nhấn \"Tạo đề thi\" để hoàn thành\r\n                        </p>\r\n                    </div>\r\n\r\n                    {/* Summary */}\r\n                    <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                        <h4 className=\"font-semibold text-gray-900 mb-3\">Thông tin tóm tắt</h4>\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm\">\r\n                            <div>\r\n                                <span className=\"text-gray-600\">Tên đề thi:</span>\r\n                                <span className=\"ml-2 font-medium\">{examData.name || \"Chưa nhập\"}</span>\r\n                            </div>\r\n                            <div>\r\n                                <span className=\"text-gray-600\">Kiểu đề:</span>\r\n                                <span className=\"ml-2 font-medium\">{examData.typeOfExam || \"Chưa chọn\"}</span>\r\n                            </div>\r\n                            <div>\r\n                                <span className=\"text-gray-600\">Lớp:</span>\r\n                                <span className=\"ml-2 font-medium\">{examData.class || \"Chưa chọn\"}</span>\r\n                            </div>\r\n                            <div>\r\n                                <span className=\"text-gray-600\">Năm:</span>\r\n                                <span className=\"ml-2 font-medium\">{examData.year || \"Chưa chọn\"}</span>\r\n                            </div>\r\n                            <div>\r\n                                <span className=\"text-gray-600\">Số câu hỏi:</span>\r\n                                <span className=\"ml-2 font-medium\">{questions.length}</span>\r\n                            </div>\r\n                            <div>\r\n                                <span className=\"text-gray-600\">Trạng thái:</span>\r\n                                <span className=\"ml-2 font-medium\">\r\n                                    {examData.public ? \"Công khai\" : \"Riêng tư\"}\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Validation Errors */}\r\n                    {(!examData.name || !examData.typeOfExam || !examData.class || !examData.year) && (\r\n                        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\r\n                            <h5 className=\"font-medium text-red-800 mb-2\">Thông tin bắt buộc chưa đầy đủ:</h5>\r\n                            <ul className=\"text-sm text-red-700 space-y-1\">\r\n                                {!examData.name && <li>• Tên đề thi</li>}\r\n                                {!examData.typeOfExam && <li>• Kiểu đề</li>}\r\n                                {!examData.class && <li>• Lớp</li>}\r\n                                {!examData.year && <li>• Năm</li>}\r\n                            </ul>\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            )}\r\n\r\n            {/* Navigation Buttons */}\r\n            <div className=\"flex justify-between mt-8 pt-6 border-t border-gray-200\">\r\n                <button\r\n                    onClick={handlePrev}\r\n                    disabled={currentStep === 1}\r\n                    className=\"px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                    Quay lại\r\n                </button>\r\n\r\n                {currentStep < 3 ? (\r\n                    <button\r\n                        onClick={handleNext}\r\n                        className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n                    >\r\n                        Tiếp theo\r\n                    </button>\r\n                ) : (\r\n                    <button\r\n                        onClick={handleSubmit}\r\n                        disabled={loading}\r\n                        className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2\"\r\n                    >\r\n                        {loading ? (\r\n                            <>\r\n                                <LoadingSpinner size=\"sm\" />\r\n                                Đang tạo...\r\n                            </>\r\n                        ) : (\r\n                            <>\r\n                                <Save className=\"w-4 h-4\" />\r\n                                Tạo đề thi\r\n                            </>\r\n                        )}\r\n                    </button>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\n// Right Panel Component - Live Preview\r\nconst ExamPreviewPanel = ({\r\n    examData,\r\n    examImage,\r\n    questions,\r\n    currentStep\r\n}) => {\r\n    return (\r\n        <div className=\"w-1/2 bg-gray-50 p-6 overflow-y-auto\">\r\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n                <div className=\"flex items-center gap-2 mb-6\">\r\n                    <Eye className=\"w-5 h-5 text-gray-600\" />\r\n                    <h2 className=\"text-xl font-semibold text-gray-900\">Xem trước đề thi</h2>\r\n                </div>\r\n\r\n                {/* Exam Header */}\r\n                <div className=\"mb-6\">\r\n                    <div className=\"flex items-start gap-4\">\r\n                        <div className=\"flex-1\">\r\n                            <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\r\n                                {examData.name || \"Tên đề thi\"}\r\n                            </h3>\r\n                            <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600\">\r\n                                <div>\r\n                                    <span className=\"font-medium\">Kiểu đề:</span> {examData.typeOfExam || \"Chưa chọn\"}\r\n                                </div>\r\n                                <div>\r\n                                    <span className=\"font-medium\">Lớp:</span> {examData.class || \"Chưa chọn\"}\r\n                                </div>\r\n                                <div>\r\n                                    <span className=\"font-medium\">Năm:</span> {examData.year || \"Chưa chọn\"}\r\n                                </div>\r\n                                {examData.chapter && (\r\n                                    <div>\r\n                                        <span className=\"font-medium\">Chương:</span> {examData.chapter}\r\n                                    </div>\r\n                                )}\r\n                                {examData.testDuration && (\r\n                                    <div>\r\n                                        <span className=\"font-medium\">Thời gian:</span> {examData.testDuration} phút\r\n                                    </div>\r\n                                )}\r\n                                {examData.passRate && (\r\n                                    <div>\r\n                                        <span className=\"font-medium\">Điểm đạt:</span> {examData.passRate}%\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                            {examData.description && (\r\n                                <div className=\"mt-3\">\r\n                                    <span className=\"font-medium text-gray-700\">Mô tả:</span>\r\n                                    <p className=\"text-gray-600 mt-1\">{examData.description}</p>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                        {examImage && (\r\n                            <div className=\"w-32 h-32 border border-gray-200 rounded-lg overflow-hidden\">\r\n                                <img\r\n                                    src={URL.createObjectURL(examImage)}\r\n                                    alt=\"Exam preview\"\r\n                                    className=\"w-full h-full object-cover\"\r\n                                />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Exam Status */}\r\n                <div className=\"mb-6\">\r\n                    <div className=\"flex flex-wrap gap-2\">\r\n                        {examData.public && (\r\n                            <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\r\n                                Công khai\r\n                            </span>\r\n                        )}\r\n                        {examData.isClassroomExam && (\r\n                            <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full\">\r\n                                Đề thi trên lớp\r\n                            </span>\r\n                        )}\r\n                        {!examData.public && !examData.isClassroomExam && (\r\n                            <span className=\"px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full\">\r\n                                Riêng tư\r\n                            </span>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Questions Preview */}\r\n                {currentStep >= 2 && (\r\n                    <div>\r\n                        <div className=\"flex items-center gap-2 mb-4\">\r\n                            <FileText className=\"w-5 h-5 text-gray-600\" />\r\n                            <h4 className=\"text-lg font-semibold text-gray-900\">\r\n                                Câu hỏi ({questions.length})\r\n                            </h4>\r\n                        </div>\r\n\r\n                        {questions.length === 0 ? (\r\n                            <div className=\"text-center py-8 text-gray-500\">\r\n                                <FileText className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\r\n                                <p>Chưa có câu hỏi nào</p>\r\n                                <p className=\"text-sm\">Thêm câu hỏi ở bước 2</p>\r\n                            </div>\r\n                        ) : (\r\n                            <div className=\"space-y-4\">\r\n                                {questions.map((question, index) => (\r\n                                    <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\r\n                                        <div className=\"flex items-start gap-3\">\r\n                                            <span className=\"flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 text-sm font-medium rounded-full flex items-center justify-center\">\r\n                                                {index + 1}\r\n                                            </span>\r\n                                            <div className=\"flex-1\">\r\n                                                <div className=\"text-gray-900 mb-2\">\r\n                                                    <LatexRenderer text={question.questionData.content} />\r\n                                                </div>\r\n                                                {question.questionData.imageUrl && (\r\n                                                    <div className=\"mb-2\">\r\n                                                        <img\r\n                                                            src={question.questionData.imageUrl}\r\n                                                            alt=\"Question\"\r\n                                                            className=\"max-w-full h-auto rounded border\"\r\n                                                        />\r\n                                                    </div>\r\n                                                )}\r\n                                                <div className=\"text-xs text-gray-500\">\r\n                                                    Loại: {question.questionData.type} |\r\n                                                    Lớp: {question.questionData.class} |\r\n                                                    Độ khó: {question.questionData.difficulty}\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                )}\r\n\r\n                {/* Summary */}\r\n                {currentStep === 3 && (\r\n                    <div className=\"mt-6 pt-6 border-t border-gray-200\">\r\n                        <h4 className=\"text-lg font-semibold text-gray-900 mb-3\">Tóm tắt</h4>\r\n                        <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n                            <div>\r\n                                <span className=\"font-medium text-gray-700\">Tổng số câu hỏi:</span>\r\n                                <span className=\"ml-2 text-gray-900\">{questions.length}</span>\r\n                            </div>\r\n                            <div>\r\n                                <span className=\"font-medium text-gray-700\">Trạng thái:</span>\r\n                                <span className=\"ml-2 text-gray-900\">\r\n                                    {examData.public ? \"Công khai\" : \"Riêng tư\"}\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\n// Main Component\r\nexport const AddExamAdmin = () => {\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const { codes } = useSelector(state => state.codes);\r\n    const { loading } = useSelector(state => state.exams);\r\n    const { closeSidebar } = useSelector(state => state.sidebar);\r\n\r\n    // Form state\r\n    const [currentStep, setCurrentStep] = useState(1);\r\n    const [examData, setExamData] = useState({\r\n        name: \"\",\r\n        typeOfExam: null,\r\n        class: null,\r\n        chapter: null,\r\n        year: null,\r\n        description: \"\",\r\n        testDuration: null,\r\n        passRate: null,\r\n        solutionUrl: \"\",\r\n        imageUrl: \"\",\r\n        public: false,\r\n        isClassroomExam: false,\r\n    });\r\n\r\n    // File states\r\n    const [examImage, setExamImage] = useState(null);\r\n    const [examFile, setExamFile] = useState(null);\r\n\r\n    // Question states\r\n    const [questions, setQuestions] = useState([]);\r\n    const [questionImages, setQuestionImages] = useState([]);\r\n    const [statementImages, setStatementImages] = useState([]);\r\n    const [solutionImages, setSolutionImages] = useState([]);\r\n\r\n    // Question content states\r\n    const [contentTN, setContentTN] = useState(\"\");\r\n    const [contentDS, setContentDS] = useState(\"\");\r\n    const [contentTLN, setContentTLN] = useState(\"\");\r\n    const [correctAnswerTN, setCorrectAnswerTN] = useState(\"\");\r\n    const [correctAnswerDS, setCorrectAnswerDS] = useState(\"\");\r\n    const [correctAnswerTLN, setCorrectAnswerTLN] = useState(\"\");\r\n\r\n    // Preview states\r\n    const [previewTN, setPreviewTN] = useState([]);\r\n    const [previewDS, setPreviewDS] = useState([]);\r\n    const [previewTLN, setPreviewTLN] = useState([]);\r\n\r\n    // Chapter options\r\n    const optionChapter = Array.isArray(codes[\"chapter\"]) ? codes[\"chapter\"] : [];\r\n\r\n    // Load codes on mount\r\n    useEffect(() => {\r\n        dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\r\n    }, [dispatch]);\r\n\r\n    // Update preview when answers change\r\n    useEffect(() => {\r\n        const normalize = (text) =>\r\n            text\r\n                .trim()\r\n                .replace(/-/g, \"\")\r\n                .toUpperCase()\r\n                .split(/\\s+/)\r\n                .filter(Boolean);\r\n\r\n        const normalizeTLN = (text) =>\r\n            text\r\n                .trim()\r\n                .toUpperCase()\r\n                .split(/\\s+/)\r\n                .filter(Boolean);\r\n\r\n        setPreviewTN(normalize(correctAnswerTN));\r\n        setPreviewDS(normalize(correctAnswerDS));\r\n        setPreviewTLN(normalizeTLN(correctAnswerTLN));\r\n    }, [correctAnswerTN, correctAnswerDS, correctAnswerTLN]);\r\n\r\n    // Handle form submission\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n\r\n        const sanitize = (val) =>\r\n            val && typeof val === \"string\" ? val.trim() || null : val;\r\n\r\n        const sanitizeExamData = {\r\n            name: sanitize(examData.name),\r\n            typeOfExam: sanitize(examData.typeOfExam),\r\n            class: sanitize(examData.class),\r\n            chapter: sanitize(examData.chapter),\r\n            year: sanitize(examData.year),\r\n            description: sanitize(examData.description),\r\n            testDuration: sanitize(examData.testDuration),\r\n            passRate: examData.passRate?.trim() ? examData.passRate?.trim() : null,\r\n            solutionUrl: sanitize(examData.solutionUrl),\r\n            public: examData.public,\r\n            imageUrl: sanitize(examData.imageUrl),\r\n            isClassroomExam: examData.isClassroomExam,\r\n        };\r\n\r\n        try {\r\n            await dispatch(\r\n                postExam({\r\n                    examData: sanitizeExamData,\r\n                    examImage,\r\n                    questions,\r\n                    questionImages,\r\n                    statementImages,\r\n                    solutionImages,\r\n                    examFile,\r\n                })\r\n            ).unwrap();\r\n\r\n            // Navigate back to exam management\r\n            navigate('/admin/exam-management');\r\n        } catch (error) {\r\n            console.error('Error creating exam:', error);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"bg-gray-50 flex flex-col min-h-screen\">\r\n            <AdminSidebar />\r\n            <div className={`bg-gray-50 flex flex-col ${closeSidebar ? \"ml-[104px]\" : \"ml-64\"}`}>\r\n                {/* Header */}\r\n                <div className=\"bg-white border-b border-gray-200 px-6 py-4\">\r\n                    <div className=\"flex items-center gap-4\">\r\n                        <button\r\n                            onClick={() => navigate('/admin/exam-management')}\r\n                            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\r\n                        >\r\n                            <ArrowLeft className=\"w-5 h-5 text-gray-600\" />\r\n                        </button>\r\n                        <div>\r\n                            <h1 className=\"text-2xl font-bold text-gray-900\">Tạo đề thi mới</h1>\r\n                            <p className=\"text-gray-600\">Tạo đề thi với câu hỏi và cấu hình chi tiết</p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Main Content */}\r\n                <div className=\"flex-1 flex overflow-hidden\">\r\n                    <ExamFormPanel\r\n                        examData={examData}\r\n                        setExamData={setExamData}\r\n                        examImage={examImage}\r\n                        setExamImage={setExamImage}\r\n                        examFile={examFile}\r\n                        setExamFile={setExamFile}\r\n                        codes={codes}\r\n                        optionChapter={optionChapter}\r\n                        currentStep={currentStep}\r\n                        setCurrentStep={setCurrentStep}\r\n                        questions={questions}\r\n                        setQuestions={setQuestions}\r\n                        questionImages={questionImages}\r\n                        setQuestionImages={setQuestionImages}\r\n                        statementImages={statementImages}\r\n                        setStatementImages={setStatementImages}\r\n                        solutionImages={solutionImages}\r\n                        setSolutionImages={setSolutionImages}\r\n                        contentTN={contentTN}\r\n                        setContentTN={setContentTN}\r\n                        contentDS={contentDS}\r\n                        setContentDS={setContentDS}\r\n                        contentTLN={contentTLN}\r\n                        setContentTLN={setContentTLN}\r\n                        correctAnswerTN={correctAnswerTN}\r\n                        setCorrectAnswerTN={setCorrectAnswerTN}\r\n                        correctAnswerDS={correctAnswerDS}\r\n                        setCorrectAnswerDS={setCorrectAnswerDS}\r\n                        correctAnswerTLN={correctAnswerTLN}\r\n                        setCorrectAnswerTLN={setCorrectAnswerTLN}\r\n                        previewTN={previewTN}\r\n                        previewDS={previewDS}\r\n                        previewTLN={previewTLN}\r\n                        handleSubmit={handleSubmit}\r\n                        loading={loading}\r\n                    />\r\n                    <ExamPreviewPanel\r\n                        examData={examData}\r\n                        examImage={examImage}\r\n                        questions={questions}\r\n                        currentStep={currentStep}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AddExamAdmin;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,cAAc,MAAM,uCAAuC;AAClE,SAASC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;;AAE3E;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,aAAa,GAAGC,IAAA,IAoChB;EAAA,IApCiB;IACnBC,QAAQ;IACRC,WAAW;IACXC,SAAS;IACTC,YAAY;IACZC,QAAQ;IACRC,WAAW;IACXC,KAAK;IACLC,aAAa;IACbC,WAAW;IACXC,cAAc;IACdC,SAAS;IACTC,YAAY;IACZC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,kBAAkB;IAClBC,cAAc;IACdC,iBAAiB;IACjBC,SAAS;IACTC,YAAY;IACZC,SAAS;IACTC,YAAY;IACZC,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC,kBAAkB;IAClBC,eAAe;IACfC,kBAAkB;IAClBC,gBAAgB;IAChBC,mBAAmB;IACnBC,SAAS;IACTC,SAAS;IACTC,UAAU;IACVC,YAAY;IACZC;EACJ,CAAC,GAAAnC,IAAA;EACG,MAAMoC,OAAO,GAAG3B,WAAW,KAAK,CAAC;EACjC,MAAM4B,OAAO,GAAG5B,WAAW,KAAK,CAAC;EACjC,MAAM6B,OAAO,GAAG7B,WAAW,KAAK,CAAC;EAEjC,MAAM8B,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAI9B,WAAW,GAAG,CAAC,EAAE;MACjBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACnC;EACJ,CAAC;EAED,MAAM+B,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAI/B,WAAW,GAAG,CAAC,EAAE;MACjBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACnC;EACJ,CAAC;EAED,oBACIb,OAAA;IAAK6C,SAAS,EAAC,6DAA6D;IAAAC,QAAA,gBAExE9C,OAAA;MAAK6C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB9C,OAAA;QAAI6C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzElD,OAAA;QAAK6C,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBACxC9C,OAAA;UAAK6C,SAAS,gDAAAM,MAAA,CACVX,OAAO,GAAG,2BAA2B,GAAG,2BAA2B,CACpE;UAAAM,QAAA,EAAC;QAEJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNlD,OAAA;UAAK6C,SAAS,gDAAAM,MAAA,CACVV,OAAO,GAAG,2BAA2B,GAAG,2BAA2B,CACpE;UAAAK,QAAA,EAAC;QAEJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNlD,OAAA;UAAK6C,SAAS,gDAAAM,MAAA,CACVT,OAAO,GAAG,2BAA2B,GAAG,2BAA2B,CACpE;UAAAI,QAAA,EAAC;QAEJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLV,OAAO,iBACJxC,OAAA;MAAK6C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAEtB9C,OAAA;QAAK6C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAClD9C,OAAA;UAAA8C,QAAA,gBACI9C,OAAA;YAAO6C,SAAS,EAAC,8CAA8C;YAAAC,QAAA,GAAC,0BACjD,eAAA9C,OAAA;cAAM6C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACRlD,OAAA;YACIoD,IAAI,EAAC,MAAM;YACXC,QAAQ;YACRC,KAAK,EAAEjD,QAAQ,CAACkD,IAAK;YACrBC,QAAQ,EAAGC,CAAC,IAAKnD,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEkD,IAAI,EAAEE,CAAC,CAACC,MAAM,CAACJ;YAAM,CAAC,CAAE;YACpET,SAAS,EAAC,2GAA2G;YACrHc,WAAW,EAAC;UAAiB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlD,OAAA;UAAA8C,QAAA,gBACI9C,OAAA;YAAO6C,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA,CAACX,WAAW;YACRuE,KAAK,EAAErD,SAAU;YACjBsD,QAAQ,EAAErD,YAAa;YACvBsD,OAAO,EAAC;UAAmB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAClD9C,OAAA;UAAA8C,QAAA,gBACI9C,OAAA;YAAO6C,SAAS,EAAC,8CAA8C;YAAAC,QAAA,GAAC,yBACpD,eAAA9C,OAAA;cAAM6C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACRlD,OAAA,CAACb,gBAAgB;YACb4E,cAAc,EAAE1D,QAAQ,CAAC2D,UAAW;YACpCR,QAAQ,EAAGS,MAAM,IAAK3D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAE2D,UAAU,EAAEC;YAAO,CAAC,CAAE;YACvEC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACzD,KAAK,CAAC,WAAW,CAAC,CAAC,GAAGA,KAAK,CAAC,WAAW,CAAC,GAAG;UAAG;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlD,OAAA;UAAA8C,QAAA,gBACI9C,OAAA;YAAO6C,SAAS,EAAC,8CAA8C;YAAAC,QAAA,GAAC,WACxD,eAAA9C,OAAA;cAAM6C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACRlD,OAAA,CAACb,gBAAgB;YACb4E,cAAc,EAAE1D,QAAQ,CAACgE,KAAM;YAC/Bb,QAAQ,EAAGS,MAAM,IAAK3D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEgE,KAAK,EAAEJ;YAAO,CAAC,CAAE;YAClEC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACzD,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG;UAAG;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAClD9C,OAAA;UAAA8C,QAAA,gBACI9C,OAAA;YAAO6C,SAAS,EAAC,8CAA8C;YAAAC,QAAA,GAAC,WACxD,eAAA9C,OAAA;cAAM6C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACRlD,OAAA,CAACb,gBAAgB;YACb4E,cAAc,EAAE1D,QAAQ,CAACiE,IAAK;YAC9Bd,QAAQ,EAAGS,MAAM,IAAK3D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEiE,IAAI,EAAEL;YAAO,CAAC,CAAE;YACjEC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACzD,KAAK,CAAC,MAAM,CAAC,CAAC,GAAGA,KAAK,CAAC,MAAM,CAAC,GAAG;UAAG;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL7C,QAAQ,CAAC2D,UAAU,KAAK,IAAI,iBACzBhE,OAAA;UAAA8C,QAAA,gBACI9C,OAAA;YAAO6C,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA,CAACZ,oBAAoB;YACjB2E,cAAc,EAAE1D,QAAQ,CAACkE,OAAQ;YACjCf,QAAQ,EAAGS,MAAM,IAAK3D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEkE,OAAO,EAAEN;YAAO,CAAC,CAAE;YACpEC,OAAO,EAAEtD;UAAc;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAClD9C,OAAA;UAAA8C,QAAA,gBACI9C,OAAA;YAAO6C,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA;YACIoD,IAAI,EAAC,QAAQ;YACbE,KAAK,EAAEjD,QAAQ,CAACmE,YAAY,IAAI,EAAG;YACnChB,QAAQ,EAAGC,CAAC,IAAKnD,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEmE,YAAY,EAAEf,CAAC,CAACC,MAAM,CAACJ;YAAM,CAAC,CAAE;YAC5ET,SAAS,EAAC,2GAA2G;YACrHc,WAAW,EAAC;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlD,OAAA;UAAA8C,QAAA,gBACI9C,OAAA;YAAO6C,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA;YACIoD,IAAI,EAAC,QAAQ;YACbE,KAAK,EAAEjD,QAAQ,CAACoE,QAAQ,IAAI,EAAG;YAC/BjB,QAAQ,EAAGC,CAAC,IAAKnD,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEoE,QAAQ,EAAEhB,CAAC,CAACC,MAAM,CAACJ;YAAM,CAAC,CAAE;YACxET,SAAS,EAAC,2GAA2G;YACrHc,WAAW,EAAC;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlD,OAAA;QAAA8C,QAAA,gBACI9C,OAAA;UAAO6C,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRlD,OAAA;UACIsD,KAAK,EAAEjD,QAAQ,CAACqE,WAAY;UAC5BlB,QAAQ,EAAGC,CAAC,IAAKnD,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEqE,WAAW,EAAEjB,CAAC,CAACC,MAAM,CAACJ;UAAM,CAAC,CAAE;UAC3EqB,IAAI,EAAE,CAAE;UACR9B,SAAS,EAAC,2GAA2G;UACrHc,WAAW,EAAC;QAAoB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtB9C,OAAA;UAAO6C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9C,OAAA;YACIoD,IAAI,EAAC,UAAU;YACfwB,OAAO,EAAEvE,QAAQ,CAACwE,MAAO;YACzBrB,QAAQ,EAAGC,CAAC,IAAKnD,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEwE,MAAM,EAAEpB,CAAC,CAACC,MAAM,CAACkB;YAAQ,CAAC,CAAE;YACxE/B,SAAS,EAAC;UAAiE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACFlD,OAAA;YAAM6C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACRlD,OAAA;UAAO6C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9C,OAAA;YACIoD,IAAI,EAAC,UAAU;YACfwB,OAAO,EAAEvE,QAAQ,CAACyE,eAAgB;YAClCtB,QAAQ,EAAGC,CAAC,IAAKnD,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEyE,eAAe,EAAErB,CAAC,CAACC,MAAM,CAACkB;YAAQ,CAAC,CAAE;YACjF/B,SAAS,EAAC;UAAiE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACFlD,OAAA;YAAM6C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGNlD,OAAA;QAAA8C,QAAA,gBACI9C,OAAA;UAAO6C,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRlD,OAAA,CAACV,SAAS;UACNyF,MAAM,EAAErE,WAAY;UACpBsE,YAAY,EAAE;QAAM;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAGAT,OAAO,iBACJzC,OAAA;MAAK6C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtB9C,OAAA;QAAK6C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7B9C,OAAA,CAACJ,QAAQ;UAACiD,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DlD,OAAA;UAAI6C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxElD,OAAA;UAAG6C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJlD,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB9C,OAAA;YAAQ6C,SAAS,EAAC,mGAAmG;YAAAC,QAAA,eACjH9C,OAAA;cAAK6C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD9C,OAAA,CAACH,IAAI;gBAACgD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1ClD,OAAA;gBAAM6C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACTlD,OAAA;YAAQ6C,SAAS,EAAC,sGAAsG;YAAAC,QAAA,eACpH9C,OAAA;cAAK6C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD9C,OAAA,CAACH,IAAI;gBAACgD,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3ClD,OAAA;gBAAM6C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACTlD,OAAA;YAAQ6C,SAAS,EAAC,yGAAyG;YAAAC,QAAA,eACvH9C,OAAA;cAAK6C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD9C,OAAA,CAACH,IAAI;gBAACgD,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5ClD,OAAA;gBAAM6C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGLnC,SAAS,CAACkE,MAAM,GAAG,CAAC,iBACjBjF,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtB9C,OAAA;UAAI6C,SAAS,EAAC,qCAAqC;UAAAC,QAAA,GAAC,gCAC7B,EAAC/B,SAAS,CAACkE,MAAM,EAAC,GACzC;QAAA;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACJnC,SAAS,CAACmE,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC3BpF,OAAA;UAAiB6C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eAC9D9C,OAAA;YAAK6C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC7C9C,OAAA;cAAK6C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACnB9C,OAAA;gBAAK6C,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBACzC9C,OAAA;kBAAM6C,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,GAAC,SAC1E,EAACsC,KAAK,GAAG,CAAC;gBAAA;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACPlD,OAAA;kBAAM6C,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,EAC5EqC,QAAQ,CAACE,YAAY,CAACjC;gBAAI;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNlD,OAAA;gBAAK6C,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC1B9C,OAAA,CAACT,aAAa;kBAAC+F,IAAI,EAAEH,QAAQ,CAACE,YAAY,CAACE;gBAAQ;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNlD,OAAA;cAAQ6C,SAAS,EAAC,+DAA+D;cAAAC,QAAA,eAC7E9C,OAAA,CAACF,MAAM;gBAAC+C,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC,GAlBAkC,KAAK;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBV,CACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR,EAGAR,OAAO,iBACJ1C,OAAA;MAAK6C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtB9C,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB9C,OAAA;UAAK6C,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAC9F9C,OAAA,CAACN,IAAI;YAACmD,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNlD,OAAA;UAAI6C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/ElD,OAAA;UAAG6C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACtC9C,OAAA;UAAI6C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvElD,OAAA;UAAK6C,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC1D9C,OAAA;YAAA8C,QAAA,gBACI9C,OAAA;cAAM6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDlD,OAAA;cAAM6C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEzC,QAAQ,CAACkD,IAAI,IAAI;YAAW;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNlD,OAAA;YAAA8C,QAAA,gBACI9C,OAAA;cAAM6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/ClD,OAAA;cAAM6C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEzC,QAAQ,CAAC2D,UAAU,IAAI;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACNlD,OAAA;YAAA8C,QAAA,gBACI9C,OAAA;cAAM6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ClD,OAAA;cAAM6C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEzC,QAAQ,CAACgE,KAAK,IAAI;YAAW;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACNlD,OAAA;YAAA8C,QAAA,gBACI9C,OAAA;cAAM6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ClD,OAAA;cAAM6C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEzC,QAAQ,CAACiE,IAAI,IAAI;YAAW;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNlD,OAAA;YAAA8C,QAAA,gBACI9C,OAAA;cAAM6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDlD,OAAA;cAAM6C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAE/B,SAAS,CAACkE;YAAM;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNlD,OAAA;YAAA8C,QAAA,gBACI9C,OAAA;cAAM6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDlD,OAAA;cAAM6C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC7BzC,QAAQ,CAACwE,MAAM,GAAG,WAAW,GAAG;YAAU;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL,CAAC,CAAC7C,QAAQ,CAACkD,IAAI,IAAI,CAAClD,QAAQ,CAAC2D,UAAU,IAAI,CAAC3D,QAAQ,CAACgE,KAAK,IAAI,CAAChE,QAAQ,CAACiE,IAAI,kBACzEtE,OAAA;QAAK6C,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC3D9C,OAAA;UAAI6C,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFlD,OAAA;UAAI6C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,GACzC,CAACzC,QAAQ,CAACkD,IAAI,iBAAIvD,OAAA;YAAA8C,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACvC,CAAC7C,QAAQ,CAAC2D,UAAU,iBAAIhE,OAAA;YAAA8C,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC1C,CAAC7C,QAAQ,CAACgE,KAAK,iBAAIrE,OAAA;YAAA8C,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjC,CAAC7C,QAAQ,CAACiE,IAAI,iBAAItE,OAAA;YAAA8C,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR,eAGDlD,OAAA;MAAK6C,SAAS,EAAC,yDAAyD;MAAAC,QAAA,gBACpE9C,OAAA;QACIwF,OAAO,EAAE5C,UAAW;QACpB6C,QAAQ,EAAE5E,WAAW,KAAK,CAAE;QAC5BgC,SAAS,EAAC,6FAA6F;QAAAC,QAAA,EAC1G;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERrC,WAAW,GAAG,CAAC,gBACZb,OAAA;QACIwF,OAAO,EAAE7C,UAAW;QACpBE,SAAS,EAAC,iFAAiF;QAAAC,QAAA,EAC9F;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETlD,OAAA;QACIwF,OAAO,EAAElD,YAAa;QACtBmD,QAAQ,EAAElD,OAAQ;QAClBM,SAAS,EAAC,+HAA+H;QAAAC,QAAA,EAExIP,OAAO,gBACJvC,OAAA,CAAAE,SAAA;UAAA4C,QAAA,gBACI9C,OAAA,CAACR,cAAc;YAACkG,IAAI,EAAC;UAAI;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAEhC;QAAA,eAAE,CAAC,gBAEHlD,OAAA,CAAAE,SAAA;UAAA4C,QAAA,gBACI9C,OAAA,CAACN,IAAI;YAACmD,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEhC;QAAA,eAAE;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;;AAED;AAAAyC,EAAA,GAhZMxF,aAAa;AAiZnB,MAAMyF,gBAAgB,GAAGC,KAAA,IAKnB;EAAA,IALoB;IACtBxF,QAAQ;IACRE,SAAS;IACTQ,SAAS;IACTF;EACJ,CAAC,GAAAgF,KAAA;EACG,oBACI7F,OAAA;IAAK6C,SAAS,EAAC,sCAAsC;IAAAC,QAAA,eACjD9C,OAAA;MAAK6C,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACrE9C,OAAA;QAAK6C,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBACzC9C,OAAA,CAACL,GAAG;UAACkD,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzClD,OAAA;UAAI6C,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACjB9C,OAAA;UAAK6C,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACnC9C,OAAA;YAAK6C,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACnB9C,OAAA;cAAI6C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAChDzC,QAAQ,CAACkD,IAAI,IAAI;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACLlD,OAAA;cAAK6C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,gBACzD9C,OAAA;gBAAA8C,QAAA,gBACI9C,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC7C,QAAQ,CAAC2D,UAAU,IAAI,WAAW;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACNlD,OAAA;gBAAA8C,QAAA,gBACI9C,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC7C,QAAQ,CAACgE,KAAK,IAAI,WAAW;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,eACNlD,OAAA;gBAAA8C,QAAA,gBACI9C,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC7C,QAAQ,CAACiE,IAAI,IAAI,WAAW;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,EACL7C,QAAQ,CAACkE,OAAO,iBACbvE,OAAA;gBAAA8C,QAAA,gBACI9C,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC7C,QAAQ,CAACkE,OAAO;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CACR,EACA7C,QAAQ,CAACmE,YAAY,iBAClBxE,OAAA;gBAAA8C,QAAA,gBACI9C,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC7C,QAAQ,CAACmE,YAAY,EAAC,UAC3E;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACR,EACA7C,QAAQ,CAACoE,QAAQ,iBACdzE,OAAA;gBAAA8C,QAAA,gBACI9C,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC7C,QAAQ,CAACoE,QAAQ,EAAC,GACtE;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EACL7C,QAAQ,CAACqE,WAAW,iBACjB1E,OAAA;cAAK6C,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACjB9C,OAAA;gBAAM6C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDlD,OAAA;gBAAG6C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEzC,QAAQ,CAACqE;cAAW;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EACL3C,SAAS,iBACNP,OAAA;YAAK6C,SAAS,EAAC,6DAA6D;YAAAC,QAAA,eACxE9C,OAAA;cACI8F,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACzF,SAAS,CAAE;cACpC0F,GAAG,EAAC,cAAc;cAClBpD,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACjB9C,OAAA;UAAK6C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAChCzC,QAAQ,CAACwE,MAAM,iBACZ7E,OAAA;YAAM6C,SAAS,EAAC,wEAAwE;YAAAC,QAAA,EAAC;UAEzF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACT,EACA7C,QAAQ,CAACyE,eAAe,iBACrB9E,OAAA;YAAM6C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,EAAC;UAEvF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACT,EACA,CAAC7C,QAAQ,CAACwE,MAAM,IAAI,CAACxE,QAAQ,CAACyE,eAAe,iBAC1C9E,OAAA;YAAM6C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,EAAC;UAEvF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGLrC,WAAW,IAAI,CAAC,iBACbb,OAAA;QAAA8C,QAAA,gBACI9C,OAAA;UAAK6C,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBACzC9C,OAAA,CAACJ,QAAQ;YAACiD,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ClD,OAAA;YAAI6C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,mBACvC,EAAC/B,SAAS,CAACkE,MAAM,EAAC,GAC/B;UAAA;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELnC,SAAS,CAACkE,MAAM,KAAK,CAAC,gBACnBjF,OAAA;UAAK6C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC3C9C,OAAA,CAACJ,QAAQ;YAACiD,SAAS,EAAC;UAAmC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DlD,OAAA;YAAA8C,QAAA,EAAG;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1BlD,OAAA;YAAG6C,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,gBAENlD,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,EACrB/B,SAAS,CAACmE,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC3BpF,OAAA;YAAiB6C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eAC9D9C,OAAA;cAAK6C,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACnC9C,OAAA;gBAAM6C,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,EAC9HsC,KAAK,GAAG;cAAC;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACPlD,OAAA;gBAAK6C,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACnB9C,OAAA;kBAAK6C,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eAC/B9C,OAAA,CAACT,aAAa;oBAAC+F,IAAI,EAAEH,QAAQ,CAACE,YAAY,CAACE;kBAAQ;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,EACLiC,QAAQ,CAACE,YAAY,CAACa,QAAQ,iBAC3BlG,OAAA;kBAAK6C,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACjB9C,OAAA;oBACI8F,GAAG,EAAEX,QAAQ,CAACE,YAAY,CAACa,QAAS;oBACpCD,GAAG,EAAC,UAAU;oBACdpD,SAAS,EAAC;kBAAkC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR,eACDlD,OAAA;kBAAK6C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,aAC7B,EAACqC,QAAQ,CAACE,YAAY,CAACjC,IAAI,EAAC,eAC7B,EAAC+B,QAAQ,CAACE,YAAY,CAAChB,KAAK,EAAC,0BAC1B,EAACc,QAAQ,CAACE,YAAY,CAACc,UAAU;gBAAA;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC,GAxBAkC,KAAK;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBV,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR,EAGArC,WAAW,KAAK,CAAC,iBACdb,OAAA;QAAK6C,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBAC/C9C,OAAA;UAAI6C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrElD,OAAA;UAAK6C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC3C9C,OAAA;YAAA8C,QAAA,gBACI9C,OAAA;cAAM6C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnElD,OAAA;cAAM6C,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAE/B,SAAS,CAACkE;YAAM;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNlD,OAAA;YAAA8C,QAAA,gBACI9C,OAAA;cAAM6C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DlD,OAAA;cAAM6C,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC/BzC,QAAQ,CAACwE,MAAM,GAAG,WAAW,GAAG;YAAU;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;;AAED;AAAAkD,GAAA,GAjKMR,gBAAgB;AAkKtB,OAAO,MAAMS,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGzH,WAAW,CAAC,CAAC;EAC9B,MAAM0H,QAAQ,GAAGzH,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B;EAAM,CAAC,GAAG9B,WAAW,CAAC4H,KAAK,IAAIA,KAAK,CAAC9F,KAAK,CAAC;EACnD,MAAM;IAAE4B;EAAQ,CAAC,GAAG1D,WAAW,CAAC4H,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAM;IAAEC;EAAa,CAAC,GAAG9H,WAAW,CAAC4H,KAAK,IAAIA,KAAK,CAACG,OAAO,CAAC;;EAE5D;EACA,MAAM,CAAC/F,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC;IACrC4E,IAAI,EAAE,EAAE;IACRS,UAAU,EAAE,IAAI;IAChBK,KAAK,EAAE,IAAI;IACXE,OAAO,EAAE,IAAI;IACbD,IAAI,EAAE,IAAI;IACVI,WAAW,EAAE,EAAE;IACfF,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,IAAI;IACdoC,WAAW,EAAE,EAAE;IACfX,QAAQ,EAAE,EAAE;IACZrB,MAAM,EAAE,KAAK;IACbC,eAAe,EAAE;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACvE,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAACwD,SAAS,EAAE2E,YAAY,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyD,SAAS,EAAE2E,YAAY,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0D,UAAU,EAAE2E,aAAa,CAAC,GAAGrI,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMiC,aAAa,GAAGuD,KAAK,CAACC,OAAO,CAACzD,KAAK,CAAC,SAAS,CAAC,CAAC,GAAGA,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;;EAE7E;EACA/B,SAAS,CAAC,MAAM;IACZ2H,QAAQ,CAACtH,gBAAgB,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;EACvF,CAAC,EAAE,CAACsH,QAAQ,CAAC,CAAC;;EAEd;EACA3H,SAAS,CAAC,MAAM;IACZ,MAAMqI,SAAS,GAAI3B,IAAI,IACnBA,IAAI,CACC4B,IAAI,CAAC,CAAC,CACNC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CACjBC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,KAAK,CAAC,CACZC,MAAM,CAACC,OAAO,CAAC;IAExB,MAAMC,YAAY,GAAIlC,IAAI,IACtBA,IAAI,CACC4B,IAAI,CAAC,CAAC,CACNE,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,KAAK,CAAC,CACZC,MAAM,CAACC,OAAO,CAAC;IAExBT,YAAY,CAACG,SAAS,CAACpF,eAAe,CAAC,CAAC;IACxCkF,YAAY,CAACE,SAAS,CAAClF,eAAe,CAAC,CAAC;IACxCiF,aAAa,CAACQ,YAAY,CAACvF,gBAAgB,CAAC,CAAC;EACjD,CAAC,EAAE,CAACJ,eAAe,EAAEE,eAAe,EAAEE,gBAAgB,CAAC,CAAC;;EAExD;EACA,MAAMK,YAAY,GAAG,MAAOmB,CAAC,IAAK;IAAA,IAAAgE,kBAAA,EAAAC,mBAAA;IAC9BjE,CAAC,CAACkE,cAAc,CAAC,CAAC;IAElB,MAAMC,QAAQ,GAAIC,GAAG,IACjBA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,GAAGA,GAAG,CAACX,IAAI,CAAC,CAAC,IAAI,IAAI,GAAGW,GAAG;IAE7D,MAAMC,gBAAgB,GAAG;MACrBvE,IAAI,EAAEqE,QAAQ,CAACvH,QAAQ,CAACkD,IAAI,CAAC;MAC7BS,UAAU,EAAE4D,QAAQ,CAACvH,QAAQ,CAAC2D,UAAU,CAAC;MACzCK,KAAK,EAAEuD,QAAQ,CAACvH,QAAQ,CAACgE,KAAK,CAAC;MAC/BE,OAAO,EAAEqD,QAAQ,CAACvH,QAAQ,CAACkE,OAAO,CAAC;MACnCD,IAAI,EAAEsD,QAAQ,CAACvH,QAAQ,CAACiE,IAAI,CAAC;MAC7BI,WAAW,EAAEkD,QAAQ,CAACvH,QAAQ,CAACqE,WAAW,CAAC;MAC3CF,YAAY,EAAEoD,QAAQ,CAACvH,QAAQ,CAACmE,YAAY,CAAC;MAC7CC,QAAQ,EAAE,CAAAgD,kBAAA,GAAApH,QAAQ,CAACoE,QAAQ,cAAAgD,kBAAA,eAAjBA,kBAAA,CAAmBP,IAAI,CAAC,CAAC,IAAAQ,mBAAA,GAAGrH,QAAQ,CAACoE,QAAQ,cAAAiD,mBAAA,uBAAjBA,mBAAA,CAAmBR,IAAI,CAAC,CAAC,GAAG,IAAI;MACtEL,WAAW,EAAEe,QAAQ,CAACvH,QAAQ,CAACwG,WAAW,CAAC;MAC3ChC,MAAM,EAAExE,QAAQ,CAACwE,MAAM;MACvBqB,QAAQ,EAAE0B,QAAQ,CAACvH,QAAQ,CAAC6F,QAAQ,CAAC;MACrCpB,eAAe,EAAEzE,QAAQ,CAACyE;IAC9B,CAAC;IAED,IAAI;MACA,MAAMyB,QAAQ,CACVrH,QAAQ,CAAC;QACLmB,QAAQ,EAAEyH,gBAAgB;QAC1BvH,SAAS;QACTQ,SAAS;QACTE,cAAc;QACdE,eAAe;QACfE,cAAc;QACdZ;MACJ,CAAC,CACL,CAAC,CAACsH,MAAM,CAAC,CAAC;;MAEV;MACAvB,QAAQ,CAAC,wBAAwB,CAAC;IACtC,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAChD;EACJ,CAAC;EAED,oBACIhI,OAAA;IAAK6C,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBAClD9C,OAAA,CAAChB,YAAY;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBlD,OAAA;MAAK6C,SAAS,8BAAAM,MAAA,CAA8BwD,YAAY,GAAG,YAAY,GAAG,OAAO,CAAG;MAAA7D,QAAA,gBAEhF9C,OAAA;QAAK6C,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eACxD9C,OAAA;UAAK6C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpC9C,OAAA;YACIwF,OAAO,EAAEA,CAAA,KAAMgB,QAAQ,CAAC,wBAAwB,CAAE;YAClD3D,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eAE9D9C,OAAA,CAACP,SAAS;cAACoD,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACTlD,OAAA;YAAA8C,QAAA,gBACI9C,OAAA;cAAI6C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpElD,OAAA;cAAG6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA2C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBACxC9C,OAAA,CAACG,aAAa;UACVE,QAAQ,EAAEA,QAAS;UACnBC,WAAW,EAAEA,WAAY;UACzBC,SAAS,EAAEA,SAAU;UACrBC,YAAY,EAAEA,YAAa;UAC3BC,QAAQ,EAAEA,QAAS;UACnBC,WAAW,EAAEA,WAAY;UACzBC,KAAK,EAAEA,KAAM;UACbC,aAAa,EAAEA,aAAc;UAC7BC,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/BC,SAAS,EAAEA,SAAU;UACrBC,YAAY,EAAEA,YAAa;UAC3BC,cAAc,EAAEA,cAAe;UAC/BC,iBAAiB,EAAEA,iBAAkB;UACrCC,eAAe,EAAEA,eAAgB;UACjCC,kBAAkB,EAAEA,kBAAmB;UACvCC,cAAc,EAAEA,cAAe;UAC/BC,iBAAiB,EAAEA,iBAAkB;UACrCC,SAAS,EAAEA,SAAU;UACrBC,YAAY,EAAEA,YAAa;UAC3BC,SAAS,EAAEA,SAAU;UACrBC,YAAY,EAAEA,YAAa;UAC3BC,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7BC,eAAe,EAAEA,eAAgB;UACjCC,kBAAkB,EAAEA,kBAAmB;UACvCC,eAAe,EAAEA,eAAgB;UACjCC,kBAAkB,EAAEA,kBAAmB;UACvCC,gBAAgB,EAAEA,gBAAiB;UACnCC,mBAAmB,EAAEA,mBAAoB;UACzCC,SAAS,EAAEA,SAAU;UACrBC,SAAS,EAAEA,SAAU;UACrBC,UAAU,EAAEA,UAAW;UACvBC,YAAY,EAAEA,YAAa;UAC3BC,OAAO,EAAEA;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACFlD,OAAA,CAAC4F,gBAAgB;UACbvF,QAAQ,EAAEA,QAAS;UACnBE,SAAS,EAAEA,SAAU;UACrBQ,SAAS,EAAEA,SAAU;UACrBF,WAAW,EAAEA;QAAY;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACoD,EAAA,CA5LWD,YAAY;EAAA,QACJvH,WAAW,EACXC,WAAW,EACVF,WAAW,EACTA,WAAW,EACNA,WAAW;AAAA;AAAAqJ,GAAA,GAL3B7B,YAAY;AA8LzB,eAAeA,YAAY;AAAC,IAAAV,EAAA,EAAAS,GAAA,EAAA8B,GAAA;AAAAC,YAAA,CAAAxC,EAAA;AAAAwC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}