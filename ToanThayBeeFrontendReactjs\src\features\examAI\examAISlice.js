import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as ocrExamApi from "../../services/ocrExamApi";
// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";

export const uploadExam = createAsyncThunk(
    'examAI/uploadExam',
    async (file, { dispatch }) => {
        return apiHandler(dispatch, ocrExamApi.uploadFile, file, null, true, false, false, false);
    }
);

export const fetchExams = createAsyncThunk(
    'examAI/fetchExams',
    async ({ search, page, pageSize, sortOrder }, { dispatch }) => {
        const params = {
            search,
            page,
            pageSize,
            sortOrder
        };
        return apiHandler(dispatch, ocrExamApi.getAllExamAPI, params, null, false, false, false, false);
    }
);

export const fetchQuestionsByExamId = createAsyncThunk(
    'examAI/fetchQuestionsByExamId',
    async (examId, { dispatch }) => {
        return apiHandler(dispatch, ocrExamApi.getAllQuestionsByExamIdAPI, examId, null, false, false, false, false);
    }
);

const examSlice = createSlice({
    name: "examAI",
    initialState: {
        exams: [],
        loadingAdd: false,
        exam: null,
        pagination: { ...initialPaginationState },
        ...initialFilterState,
        questions: [],
        questionsEdited: [],
        selectedQuestion: null,
        viewEdit: 'exam'
    },

    reducers: {
        ...paginationReducers,
        ...filterReducers,
        setSelectedQuestion: (state, action) => {
            const question = action.payload;
            state.selectedQuestion = question;
            const index = state.questionsEdited.findIndex(q => q.id === state.selectedQuestion.id);
            if (index !== -1) {
                state.questionsEdited[index] = question;
            } else {
                state.questionsEdited.push(question);
            }
        },
        selectQuestion: (state, action) => {
            const question = action.payload;
            state.selectedQuestion = question;
        },
        setQuestions: (state, action) => {
            const question = action.payload;
            const index = state.questions.findIndex(q => q.id === question.id);
            if (index !== -1) {
                state.questions[index] = question;
            } else {
                state.questions.push(question);
            }
        },
        setQuestionsEdited: (state, action) => {
            const question = action.payload;
            const index = state.questionsEdited.findIndex(q => q.id === question.id);
            if (index !== -1) {
                state.questionsEdited[index] = question;
            } else {
                state.questionsEdited.push(question);
            }
        },
        saveQuestions: (state, action) => {
            state.questions = state.questionsEdited
        },
        setViewEdit: (state, action) => {
            state.viewEdit = action.payload;
        }
    },

    extraReducers: (builder) => {
        builder
            .addCase(uploadExam.pending, (state) => {
                state.loadingAdd = true;
            })
            .addCase(uploadExam.fulfilled, (state, action) => {
                state.loadingAdd = false;
                state.exam = action.payload;
            })
            .addCase(uploadExam.rejected, (state) => {
                state.loadingAdd = false;
            })
            .addCase(fetchExams.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchExams.fulfilled, (state, action) => {
                state.loading = false;
                state.exams = action.payload.data;
                state.pagination = action.payload.pagination;
            })
            .addCase(fetchExams.rejected, (state) => {
                state.loading = false;
            })
            .addCase(fetchQuestionsByExamId.pending, (state) => {
                state.loading = true;
                state.questions = [];
                state.exam = null;
            })
            .addCase(fetchQuestionsByExamId.fulfilled, (state, action) => {
                state.loading = false;

                const { question1s, ...examWithoutQuestions } = action.payload.data;

                state.exam = examWithoutQuestions;
                state.questions = question1s || [];
                state.questionsEdited = state.questions
                state.selectedQuestion = question1s && question1s.length > 0 ? question1s[0] : null;
            })
            .addCase(fetchQuestionsByExamId.rejected, (state) => {
                state.loading = false;
                state.exam = null;
                state.questions = [];
            });
    }
});

export const {
    setCurrentPage,
    setTotalPages,
    setTotalItems,
    setLimit,
    setSearch,
    setSortOrder,
    resetFilters,
    resetPagination,
    setSelectedQuestion,
    setQuestions,
    setQuestionsEdited,
    saveQuestions,
    selectQuestion,
    setViewEdit
} = examSlice.actions;
export default examSlice.reducer;