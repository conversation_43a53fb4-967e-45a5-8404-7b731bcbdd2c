import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as ocrExamApi from "../../services/ocrExamApi";
// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";

export const uploadExam = createAsyncThunk(
    'examAI/uploadExam',
    async (file, { dispatch }) => {
        return apiHandler(dispatch, ocrExamApi.uploadFile, file, null, true, false, false, false);
    }
);

export const fetchExams = createAsyncThunk(
    'examAI/fetchExams',
    async ({ search, page, pageSize, sortOrder }, { dispatch }) => {
        const params = {
            search,
            page,
            pageSize,
            sortOrder
        };
        return apiHandler(dispatch, ocrExamApi.getAllExamAPI, params, null, false, false, false, false);
    }
);

export const fetchQuestionsByExamId = createAsyncThunk(
    'examAI/fetchQuestionsByExamId',
    async (examId, { dispatch }) => {
        return apiHandler(dispatch, ocrExamApi.getAllQuestionsByExamIdAPI, examId, null, false, false, false, false);
    }
);

export const uploadMultipleImages = createAsyncThunk(
    'examAI/uploadMultipleImages',
    async (files, { dispatch }) => {
        return apiHandler(dispatch, ocrExamApi.uploadMultipleImages, files, null, true, false, false, false);
    }
);

export const saveExam = createAsyncThunk(
    'examAI/saveExam',
    async ({ examData, questions, id }, { dispatch }) => {
        return apiHandler(dispatch, ocrExamApi.saveExam1API, { examData, questions, id }, null, true, false, false, false);
    }
);

export const commitExam = createAsyncThunk(
    'examAI/commitExam',
    async (id, { dispatch }) => {
        return apiHandler(dispatch, ocrExamApi.commitExam1API, id, null, true, false, false, false);
    }
);

const examSlice = createSlice({
    name: "examAI",
    initialState: {
        exams: [],
        loadingAdd: false,
        exam: null,
        editedExam: null,
        pagination: { ...initialPaginationState },
        ...initialFilterState,
        questions: [],
        questionsEdited: [],
        selectedQuestion: null,
        viewEdit: 'exam',
        isAddImage: false,
        isChange: false,
        loadingSave: false,
        loadingCommit: false,
        // Images state
        images: [],
        loadingUploadImages: false,
        showAddImagesModal: false,
    },

    reducers: {
        ...paginationReducers,
        ...filterReducers,
        setEditedExam: (state, action) => {
            state.editedExam = action.payload;
        },
        toggleAddImage: (state) => {
            state.isAddImage = !state.isAddImage;
        },
        setIsChange: (state, action) => {
            state.isChange = action.payload;
        },
        setSelectedQuestion: (state, action) => {
            const question = action.payload;
            state.selectedQuestion = question;
            const index = state.questionsEdited.findIndex(q => q.id === state.selectedQuestion.id);
            if (index !== -1) {
                state.questionsEdited[index] = question;
            } else {
                state.questionsEdited.push(question);
            }
        },
        selectQuestion: (state, action) => {
            const question = action.payload;
            state.selectedQuestion = question;
        },
        setQuestions: (state, action) => {
            const question = action.payload;
            const index = state.questions.findIndex(q => q.id === question.id);
            if (index !== -1) {
                state.questions[index] = question;
            } else {
                state.questions.push(question);
            }
        },
        setQuestionsEdited: (state, action) => {
            const question = action.payload;
            const index = state.questionsEdited.findIndex(q => q.id === question.id);
            if (index !== -1) {
                state.questionsEdited[index] = question;
            } else {
                state.questionsEdited.push(question);
            }
        },
        saveQuestions: (state, action) => {
            state.questions = state.questionsEdited
        },
        setViewEdit: (state, action) => {
            state.viewEdit = action.payload;
        },
        reorderQuestions: (state, action) => {
            const { oldIndex, newIndex } = action.payload;

            if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0 &&
                oldIndex < state.questionsEdited.length && newIndex < state.questionsEdited.length) {

                // Tạo mảng mới với thứ tự đã thay đổi
                const newQuestions = [...state.questionsEdited];
                const [movedQuestion] = newQuestions.splice(oldIndex, 1);
                newQuestions.splice(newIndex, 0, movedQuestion);

                // Cập nhật thuộc tính order cho tất cả câu hỏi
                const updatedQuestions = newQuestions.map((question, index) => ({
                    ...question,
                    order: index
                }));

                state.questionsEdited = updatedQuestions;
            }
        },
        reorderStatements: (state, action) => {
            const { questionId, oldIndex, newIndex } = action.payload;

            const questionIndex = state.questionsEdited.findIndex(q => q.id === questionId);
            if (questionIndex === -1) return;

            const question = state.questionsEdited[questionIndex];
            if (!question.statement1s || oldIndex === newIndex) return;

            if (oldIndex >= 0 && newIndex >= 0 &&
                oldIndex < question.statement1s.length && newIndex < question.statement1s.length) {

                // Tạo mảng mới với thứ tự đã thay đổi
                const newStatements = [...question.statement1s];
                const [movedStatement] = newStatements.splice(oldIndex, 1);
                newStatements.splice(newIndex, 0, movedStatement);

                // Cập nhật thuộc tính order cho tất cả statements
                const updatedStatements = newStatements.map((statement, index) => ({
                    ...statement,
                    order: index
                }));

                // Cập nhật question với statements mới
                const updatedQuestion = {
                    ...question,
                    statement1s: updatedStatements
                };

                state.questionsEdited[questionIndex] = updatedQuestion;

                // Cập nhật selectedQuestion nếu đang chọn question này
                if (state.selectedQuestion?.id === questionId) {
                    state.selectedQuestion = updatedQuestion;
                }
            }
        },
        // Images modal actions
        setShowAddImagesModal: (state, action) => {
            state.showAddImagesModal = action.payload;
        },
        setImages: (state, action) => {
            state.images = action.payload;
        },
        addImages: (state, action) => {
            state.images = [...state.images, ...action.payload];
        }
    },

    extraReducers: (builder) => {
        builder
            .addCase(uploadExam.pending, (state) => {
                state.loadingAdd = true;
            })
            .addCase(uploadExam.fulfilled, (state, action) => {
                state.loadingAdd = false;
                state.exam = action.payload;
            })
            .addCase(uploadExam.rejected, (state) => {
                state.loadingAdd = false;
            })
            .addCase(fetchExams.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchExams.fulfilled, (state, action) => {
                state.loading = false;
                state.exams = action.payload.data;
                state.pagination = action.payload.pagination;
            })
            .addCase(fetchExams.rejected, (state) => {
                state.loading = false;
            })
            .addCase(fetchQuestionsByExamId.pending, (state) => {
                state.loading = true;
                state.questions = [];
                state.exam = null;
                state.questionsEdited = [];
                state.selectedQuestion = null;
                state.editedExam = null;
                state.isChange = false;
            })
            .addCase(fetchQuestionsByExamId.fulfilled, (state, action) => {
                state.loading = false;
                const { question1s, ...examWithoutQuestions } = action.payload.data;
                state.exam = examWithoutQuestions;
                state.questions = question1s || [];
                state.questionsEdited = state.questions
                state.selectedQuestion = question1s && question1s.length > 0 ? question1s[0] : null;
                state.editedExam = examWithoutQuestions;
            })
            .addCase(fetchQuestionsByExamId.rejected, (state) => {
                state.loading = false;
                state.exam = null;
                state.questions = [];
                state.questionsEdited = [];
                state.selectedQuestion = null;
                state.editedExam = null;
            })
            .addCase(saveExam.pending, (state) => {
                state.loadingSave = true;
            })
            .addCase(saveExam.fulfilled, (state, action) => {
                state.loadingSave = false;
                state.exam = state.editedExam;
                state.questions = state.questionsEdited;
                if (state.isChange) {
                    state.isChange = false;
                }
            })
            .addCase(saveExam.rejected, (state) => {
                state.loadingSave = false;
            })
            .addCase(commitExam.pending, (state) => {
                state.loadingCommit = true;
            })
            .addCase(commitExam.fulfilled, (state, action) => {
                state.loadingCommit = false;
            })
            .addCase(commitExam.rejected, (state) => {
                state.loadingCommit = false;
            })
            // Upload multiple images
            .addCase(uploadMultipleImages.pending, (state) => {
                state.loadingUploadImages = true;
            })
            .addCase(uploadMultipleImages.fulfilled, (state, action) => {
                state.loadingUploadImages = false;
                if (action.payload && action.payload.length > 0) {
                    state.images = [...state.images, ...action.payload];
                }
            })
            .addCase(uploadMultipleImages.rejected, (state) => {
                state.loadingUploadImages = false;
            });
    }
});

export const {
    setCurrentPage,
    setTotalPages,
    setTotalItems,
    setLimit,
    setSearch,
    setSortOrder,
    resetFilters,
    resetPagination,
    setSelectedQuestion,
    setQuestions,
    setQuestionsEdited,
    saveQuestions,
    selectQuestion,
    setViewEdit,
    reorderQuestions,
    reorderStatements,
    setEditedExam,
    toggleAddImage,
    setIsChange,
    setShowAddImagesModal,
    setImages,
    addImages
} = examSlice.actions;
export default examSlice.reducer;