'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('sheetLinks', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Tiêu đề của sheet'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Mô tả về sheet'
      },
      sheetUrl: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Link đến Google Sheets hoặc Excel file'
      },
      category: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Danh mục của sheet (ví dụ: attendance, grades, reports)'
      },
      type: {
        type: Sequelize.ENUM('USER_LIST', 'ATTENDANCE', 'GRADES', 'REPORTS', 'OTHER'),
        allowNull: false,
        defaultValue: 'OTHER',
        comment: 'Loại sheet: USER_LIST (danh sách user), ATTENDANCE (điểm danh), GRADES (điểm số), REPORTS (báo cáo), OTHER (khác)'
      },
      relatedId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'ID liên quan (ví dụ: examId, lessonId tùy theo type)'
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        comment: 'ID người tạo sheet'
      },

      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Trạng thái hoạt động của sheet'
      },
      accessLevel: {
        type: Sequelize.ENUM('PUBLIC', 'CLASS_ONLY', 'ADMIN_ONLY'),
        allowNull: false,
        defaultValue: 'CLASS_ONLY',
        comment: 'Mức độ truy cập: PUBLIC (tất cả), CLASS_ONLY (chỉ lớp), ADMIN_ONLY (chỉ admin)'
      },
      lastUpdated: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Thời gian cập nhật cuối cùng của sheet'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Thêm index để tối ưu truy vấn
    await queryInterface.addIndex('sheetLinks', ['relatedId'], {
      name: 'idx_sheet_links_related_id'
    });

    await queryInterface.addIndex('sheetLinks', ['createdBy'], {
      name: 'idx_sheet_links_created_by'
    });

    await queryInterface.addIndex('sheetLinks', ['category'], {
      name: 'idx_sheet_links_category'
    });

    await queryInterface.addIndex('sheetLinks', ['isActive'], {
      name: 'idx_sheet_links_is_active'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('sheetLinks');
  }
};
