{"ast": null, "code": "import api from \"./api\";\nexport const getAllExamAPI = _ref => {\n  let {\n    search = \"\",\n    currentPage = 1,\n    limit = 10,\n    sortOrder = 'asc'\n  } = _ref;\n  return api.get(\"/v1/admin/exam\", {\n    params: {\n      search,\n      page: currentPage,\n      limit,\n      sortOrder\n    }\n  });\n};\nexport const getNewestExamAPI = () => {\n  return api.get(\"/v1/user/exam/newest\");\n};\nexport const getRelatedExamAPI = id => {\n  return api.get(\"/v1/user/exam/\".concat(id, \"/related\"));\n};\nexport const getExamsSavedAPI = () => {\n  return api.get(\"/v1/user/exam/saved\");\n};\nexport const getAllPublicExamAPI = data => {\n  return api.get(\"/v1/user/exam\", {\n    params: data\n  });\n};\nexport const getExamByIdAPI = id => {\n  return api.get(\"/v1/admin/exam/\".concat(id));\n};\nexport const findExamsAPI = search => {\n  return api.get(\"/v1/admin/exam/search\", {\n    params: {\n      search\n    }\n  });\n};\nexport const findPublicExamsAPI = search => {\n  return api.get(\"/v1/user/exam/search\", {\n    params: {\n      search\n    }\n  });\n};\nexport const getExamPublic = id => {\n  return api.get(\"/v1/user/exam/\".concat(id));\n};\nexport const reExamination = id => {\n  return api.get(\"/v1/user/answer/re-examination/\".concat(id));\n};\nexport const putExamAPI = async _ref2 => {\n  let {\n    examId,\n    examData\n  } = _ref2;\n  const response = await api.put(\"/v1/admin/exam/\".concat(examId), examData);\n  return response.data;\n};\nexport const uploadSolutionPdfAPI = async _ref3 => {\n  let {\n    examId,\n    pdfFile\n  } = _ref3;\n  const formData = new FormData();\n  formData.append(\"pdf\", pdfFile);\n  const response = await api.post(\"/v1/admin/exam/\".concat(examId, \"/upload-pdf/solution\"), formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n  return response.data;\n};\nexport const uploadExamPdfAPI = async _ref4 => {\n  let {\n    examId,\n    pdfFile\n  } = _ref4;\n  const formData = new FormData();\n  formData.append(\"pdf\", pdfFile);\n  const response = await api.post(\"/v1/admin/exam/\".concat(examId, \"/upload-pdf/examFile\"), formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n  return response.data;\n};\nexport const putImageExamAPI = async _ref5 => {\n  let {\n    examId,\n    examImage\n  } = _ref5;\n  const formData = new FormData();\n  formData.append(\"examImage\", examImage);\n  const response = await api.put(\"/v1/admin/exam/\".concat(examId, \"/image\"), formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n  return response.data;\n};\nexport const postExamAPI = async _ref6 => {\n  let {\n    examData,\n    examImage,\n    questions,\n    examFile\n  } = _ref6;\n  const formData = new FormData();\n  formData.append(\"data\", JSON.stringify({\n    examData,\n    questions\n  }));\n  if (examImage) {\n    formData.append(\"examImage\", examImage);\n  }\n  if (examFile) {\n    formData.append(\"pdf\", examFile);\n  }\n  const response = await api.post(\"/v1/admin/exam\", formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n  return response.data;\n};\nexport const saveExamForUserAPI = async _ref7 => {\n  let {\n    examId\n  } = _ref7;\n  const response = await api.post('/v1/user/save-exam', {\n    examId\n  });\n  return response.data;\n};\nexport const deleteExamAPI = async id => {\n  const response = await api.delete(\"/v1/admin/exam/\".concat(id));\n  return response.data;\n};", "map": {"version": 3, "names": ["api", "getAllExamAPI", "_ref", "search", "currentPage", "limit", "sortOrder", "get", "params", "page", "getNewestExamAPI", "getRelatedExamAPI", "id", "concat", "getExamsSavedAPI", "getAllPublicExamAPI", "data", "getExamByIdAPI", "findExamsAPI", "findPublicExamsAPI", "getExamPublic", "reExamination", "putExamAPI", "_ref2", "examId", "examData", "response", "put", "uploadSolutionPdfAPI", "_ref3", "pdfFile", "formData", "FormData", "append", "post", "headers", "uploadExamPdfAPI", "_ref4", "putImageExamAPI", "_ref5", "examImage", "postExamAPI", "_ref6", "questions", "examFile", "JSON", "stringify", "saveExamForUserAPI", "_ref7", "deleteExamAPI", "delete"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/services/examApi.js"], "sourcesContent": ["import api from \"./api\";\r\n\r\nexport const getAllExamAPI = ({ search = \"\", currentPage = 1, limit = 10, sortOrder = 'asc' }) => {\r\n    return api.get(\"/v1/admin/exam\", {\r\n        params: {\r\n            search,\r\n            page: currentPage,\r\n            limit,\r\n            sortOrder,\r\n        }\r\n    });\r\n};\r\n\r\nexport const getNewestExamAPI = () => {\r\n    return api.get(\"/v1/user/exam/newest\");\r\n}\r\n\r\nexport const getRelatedExamAPI = (id) => {\r\n    return api.get(`/v1/user/exam/${id}/related`);\r\n}\r\n\r\nexport const getExamsSavedAPI = () => {\r\n    return api.get(\"/v1/user/exam/saved\");\r\n}\r\n\r\nexport const getAllPublicExamAPI = (data) => {\r\n    return api.get(\"/v1/user/exam\", {\r\n        params: data\r\n    });\r\n}\r\n\r\nexport const getExamByIdAPI = (id) => {\r\n    return api.get(`/v1/admin/exam/${id}`);\r\n}\r\n\r\nexport const findExamsAPI = (search) => {\r\n    return api.get(\"/v1/admin/exam/search\", {\r\n        params: {\r\n            search,\r\n        }\r\n    });\r\n}\r\n\r\nexport const findPublicExamsAPI = (search) => {\r\n    return api.get(\"/v1/user/exam/search\", {\r\n        params: {\r\n            search,\r\n        }\r\n    });\r\n}\r\n\r\nexport const getExamPublic = (id) => {\r\n    return api.get(`/v1/user/exam/${id}`);\r\n}\r\n\r\nexport const reExamination = (id) => {\r\n    return api.get(`/v1/user/answer/re-examination/${id}`);\r\n}\r\n\r\nexport const putExamAPI = async ({ examId, examData }) => {\r\n    const response = await api.put(`/v1/admin/exam/${examId}`, examData);\r\n    return response.data;\r\n}\r\n\r\n\r\n\r\nexport const uploadSolutionPdfAPI = async ({ examId, pdfFile }) => {\r\n    const formData = new FormData();\r\n\r\n    formData.append(\"pdf\", pdfFile);\r\n\r\n    const response = await api.post(`/v1/admin/exam/${examId}/upload-pdf/solution`, formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n\r\n    return response.data;\r\n};\r\n\r\nexport const uploadExamPdfAPI = async ({ examId, pdfFile }) => {\r\n    const formData = new FormData();\r\n\r\n    formData.append(\"pdf\", pdfFile);\r\n\r\n    const response = await api.post(`/v1/admin/exam/${examId}/upload-pdf/examFile`, formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n\r\n    return response.data;\r\n}\r\n\r\nexport const putImageExamAPI = async ({ examId, examImage }) => {\r\n    const formData = new FormData();\r\n    formData.append(\"examImage\", examImage);\r\n    const response = await api.put(`/v1/admin/exam/${examId}/image`, formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n    return response.data;\r\n}\r\n\r\nexport const postExamAPI = async ({ examData, examImage, questions, examFile }) => {\r\n    const formData = new FormData();\r\n\r\n\r\n    formData.append(\"data\", JSON.stringify({ examData, questions }));\r\n\r\n    if (examImage) {\r\n        formData.append(\"examImage\", examImage);\r\n    }\r\n\r\n    if (examFile) {\r\n        formData.append(\"pdf\", examFile);\r\n    }\r\n\r\n    const response = await api.post(\"/v1/admin/exam\", formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n    return response.data;\r\n}\r\n\r\nexport const saveExamForUserAPI = async ({ examId }) => {\r\n    const response = await api.post('/v1/user/save-exam', { examId });\r\n    return response.data;\r\n}\r\n\r\nexport const deleteExamAPI = async (id) => {\r\n    const response = await api.delete(`/v1/admin/exam/${id}`);\r\n    return response.data;\r\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,OAAO,MAAMC,aAAa,GAAGC,IAAA,IAAqE;EAAA,IAApE;IAAEC,MAAM,GAAG,EAAE;IAAEC,WAAW,GAAG,CAAC;IAAEC,KAAK,GAAG,EAAE;IAAEC,SAAS,GAAG;EAAM,CAAC,GAAAJ,IAAA;EACzF,OAAOF,GAAG,CAACO,GAAG,CAAC,gBAAgB,EAAE;IAC7BC,MAAM,EAAE;MACJL,MAAM;MACNM,IAAI,EAAEL,WAAW;MACjBC,KAAK;MACLC;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;EAClC,OAAOV,GAAG,CAACO,GAAG,CAAC,sBAAsB,CAAC;AAC1C,CAAC;AAED,OAAO,MAAMI,iBAAiB,GAAIC,EAAE,IAAK;EACrC,OAAOZ,GAAG,CAACO,GAAG,kBAAAM,MAAA,CAAkBD,EAAE,aAAU,CAAC;AACjD,CAAC;AAED,OAAO,MAAME,gBAAgB,GAAGA,CAAA,KAAM;EAClC,OAAOd,GAAG,CAACO,GAAG,CAAC,qBAAqB,CAAC;AACzC,CAAC;AAED,OAAO,MAAMQ,mBAAmB,GAAIC,IAAI,IAAK;EACzC,OAAOhB,GAAG,CAACO,GAAG,CAAC,eAAe,EAAE;IAC5BC,MAAM,EAAEQ;EACZ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMC,cAAc,GAAIL,EAAE,IAAK;EAClC,OAAOZ,GAAG,CAACO,GAAG,mBAAAM,MAAA,CAAmBD,EAAE,CAAE,CAAC;AAC1C,CAAC;AAED,OAAO,MAAMM,YAAY,GAAIf,MAAM,IAAK;EACpC,OAAOH,GAAG,CAACO,GAAG,CAAC,uBAAuB,EAAE;IACpCC,MAAM,EAAE;MACJL;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMgB,kBAAkB,GAAIhB,MAAM,IAAK;EAC1C,OAAOH,GAAG,CAACO,GAAG,CAAC,sBAAsB,EAAE;IACnCC,MAAM,EAAE;MACJL;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMiB,aAAa,GAAIR,EAAE,IAAK;EACjC,OAAOZ,GAAG,CAACO,GAAG,kBAAAM,MAAA,CAAkBD,EAAE,CAAE,CAAC;AACzC,CAAC;AAED,OAAO,MAAMS,aAAa,GAAIT,EAAE,IAAK;EACjC,OAAOZ,GAAG,CAACO,GAAG,mCAAAM,MAAA,CAAmCD,EAAE,CAAE,CAAC;AAC1D,CAAC;AAED,OAAO,MAAMU,UAAU,GAAG,MAAAC,KAAA,IAAgC;EAAA,IAAzB;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAAF,KAAA;EACjD,MAAMG,QAAQ,GAAG,MAAM1B,GAAG,CAAC2B,GAAG,mBAAAd,MAAA,CAAmBW,MAAM,GAAIC,QAAQ,CAAC;EACpE,OAAOC,QAAQ,CAACV,IAAI;AACxB,CAAC;AAID,OAAO,MAAMY,oBAAoB,GAAG,MAAAC,KAAA,IAA+B;EAAA,IAAxB;IAAEL,MAAM;IAAEM;EAAQ,CAAC,GAAAD,KAAA;EAC1D,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAE/BD,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;EAE/B,MAAMJ,QAAQ,GAAG,MAAM1B,GAAG,CAACkC,IAAI,mBAAArB,MAAA,CAAmBW,MAAM,2BAAwBO,QAAQ,EAAE;IACtFI,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EAEF,OAAOT,QAAQ,CAACV,IAAI;AACxB,CAAC;AAED,OAAO,MAAMoB,gBAAgB,GAAG,MAAAC,KAAA,IAA+B;EAAA,IAAxB;IAAEb,MAAM;IAAEM;EAAQ,CAAC,GAAAO,KAAA;EACtD,MAAMN,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAE/BD,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;EAE/B,MAAMJ,QAAQ,GAAG,MAAM1B,GAAG,CAACkC,IAAI,mBAAArB,MAAA,CAAmBW,MAAM,2BAAwBO,QAAQ,EAAE;IACtFI,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EAEF,OAAOT,QAAQ,CAACV,IAAI;AACxB,CAAC;AAED,OAAO,MAAMsB,eAAe,GAAG,MAAAC,KAAA,IAAiC;EAAA,IAA1B;IAAEf,MAAM;IAAEgB;EAAU,CAAC,GAAAD,KAAA;EACvD,MAAMR,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEO,SAAS,CAAC;EACvC,MAAMd,QAAQ,GAAG,MAAM1B,GAAG,CAAC2B,GAAG,mBAAAd,MAAA,CAAmBW,MAAM,aAAUO,QAAQ,EAAE;IACvEI,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOT,QAAQ,CAACV,IAAI;AACxB,CAAC;AAED,OAAO,MAAMyB,WAAW,GAAG,MAAAC,KAAA,IAAwD;EAAA,IAAjD;IAAEjB,QAAQ;IAAEe,SAAS;IAAEG,SAAS;IAAEC;EAAS,CAAC,GAAAF,KAAA;EAC1E,MAAMX,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAG/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEY,IAAI,CAACC,SAAS,CAAC;IAAErB,QAAQ;IAAEkB;EAAU,CAAC,CAAC,CAAC;EAEhE,IAAIH,SAAS,EAAE;IACXT,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEO,SAAS,CAAC;EAC3C;EAEA,IAAII,QAAQ,EAAE;IACVb,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEW,QAAQ,CAAC;EACpC;EAEA,MAAMlB,QAAQ,GAAG,MAAM1B,GAAG,CAACkC,IAAI,CAAC,gBAAgB,EAAEH,QAAQ,EAAE;IACxDI,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOT,QAAQ,CAACV,IAAI;AACxB,CAAC;AAED,OAAO,MAAM+B,kBAAkB,GAAG,MAAAC,KAAA,IAAsB;EAAA,IAAf;IAAExB;EAAO,CAAC,GAAAwB,KAAA;EAC/C,MAAMtB,QAAQ,GAAG,MAAM1B,GAAG,CAACkC,IAAI,CAAC,oBAAoB,EAAE;IAAEV;EAAO,CAAC,CAAC;EACjE,OAAOE,QAAQ,CAACV,IAAI;AACxB,CAAC;AAED,OAAO,MAAMiC,aAAa,GAAG,MAAOrC,EAAE,IAAK;EACvC,MAAMc,QAAQ,GAAG,MAAM1B,GAAG,CAACkD,MAAM,mBAAArC,MAAA,CAAmBD,EAAE,CAAE,CAAC;EACzD,OAAOc,QAAQ,CAACV,IAAI;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}