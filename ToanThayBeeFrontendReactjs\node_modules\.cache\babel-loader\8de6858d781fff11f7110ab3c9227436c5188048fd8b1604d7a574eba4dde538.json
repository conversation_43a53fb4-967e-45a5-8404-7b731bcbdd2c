{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\QuestionContent.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { selectQuestion } from \"src/features/examAI/examAISlice\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const QuestionContent = _ref => {\n  _s();\n  var _codes$chapter, _codes$chapter$find;\n  let {\n    question,\n    index\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    selectedQuestion,\n    isAddImage\n  } = useSelector(state => state.examAI);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\\n            \".concat((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.id) === question.id ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\", \"\\n        \"),\n    onClick: () => dispatch(selectQuestion(question)),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-gray-700\",\n        children: [\"C\\xE2u \", index + 1]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\u0110\\u1ED9 kh\\xF3: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: question.difficulty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Ch\\u01B0\\u01A1ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: ((_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === question.chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || \"Chưa xác định\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-base text-gray-800 leading-relaxed\",\n      children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        className: \"text-gray-800\",\n        text: question.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(SortableStatementsContainer, {\n      question: question\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"L\\u1EDDi gi\\u1EA3i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 21\n      }, this), \" \", /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: question.solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 17\n    }, this)]\n  }, question.id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionContent, \"MTz42/6xoRQRjp3Dnj9r7syqXC4=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = QuestionContent;\nexport default QuestionContent;\nvar _c;\n$RefreshReg$(_c, \"QuestionContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "selectQuestion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "SortableStatementsContainer", "jsxDEV", "_jsxDEV", "QuestionContent", "_ref", "_s", "_codes$chapter", "_codes$chapter$find", "question", "index", "dispatch", "selectedQuestion", "isAddImage", "state", "examAI", "codes", "className", "concat", "id", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "difficulty", "find", "c", "code", "chapter", "description", "text", "content", "solution", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/QuestionContent.jsx"], "sourcesContent": ["\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { selectQuestion } from \"src/features/examAI/examAISlice\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\r\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\r\n\r\nexport const QuestionContent = ({ question, index }) => {\r\n    const dispatch = useDispatch();\r\n    const { selectedQuestion, isAddImage } = useSelector((state) => state.examAI);\r\n\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    return (\r\n        <div\r\n            key={question.id}\r\n            className={`\r\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\r\n            ${selectedQuestion?.id === question.id\r\n                    ? \"bg-blue-50 border-blue-500\"\r\n                    : \"bg-white hover:bg-gray-50 border-gray-300\"\r\n                }\r\n        `}\r\n            onClick={() => dispatch(selectQuestion(question))}\r\n        >\r\n            {/* Thông tin câu hỏi */}\r\n            <div className=\"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\">\r\n                <span className=\"font-medium text-gray-700\">Câu {index + 1}</span>\r\n                <span>Độ khó: <span className=\"text-gray-700\">{question.difficulty}</span></span>\r\n                <span>\r\n                    Chương:{\" \"}\r\n                    <span className=\"text-gray-700\">\r\n                        {codes[\"chapter\"]?.find(c => c.code === question.chapter)?.description || \"Chưa xác định\"}\r\n                    </span>\r\n                </span>\r\n            </div>\r\n\r\n            {/* Nội dung câu hỏi */}\r\n            <div className=\"text-base text-gray-800 leading-relaxed\">\r\n                <LatexRenderer className=\"text-gray-800\" text={question.content} />\r\n            </div>\r\n\r\n\r\n            <SortableStatementsContainer question={question} />\r\n            {/* Statement: A, B, C,... */}\r\n\r\n            {/* Lời giải (nếu có) */}\r\n            {question.solution && (\r\n                <div className=\"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\">\r\n                    <span className=\"font-semibold\">Lời giải:</span>{\" \"}\r\n                    <MarkdownPreviewWithMath content={question.solution} />\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default QuestionContent;"], "mappings": ";;AACA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,2BAA2B,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,OAAO,MAAMC,eAAe,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,mBAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAL,IAAA;EAC/C,MAAMM,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe,gBAAgB;IAAEC;EAAW,CAAC,GAAGjB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAE7E,MAAM;IAAEC;EAAM,CAAC,GAAGpB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,oBACIb,OAAA;IAEIc,SAAS,oGAAAC,MAAA,CAEP,CAAAN,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEO,EAAE,MAAKV,QAAQ,CAACU,EAAE,GAC5B,4BAA4B,GAC5B,2CAA2C,eAEvD;IACEC,OAAO,EAAEA,CAAA,KAAMT,QAAQ,CAACb,cAAc,CAACW,QAAQ,CAAC,CAAE;IAAAY,QAAA,gBAGlDlB,OAAA;MAAKc,SAAS,EAAC,mEAAmE;MAAAI,QAAA,gBAC9ElB,OAAA;QAAMc,SAAS,EAAC,2BAA2B;QAAAI,QAAA,GAAC,SAAI,EAACX,KAAK,GAAG,CAAC;MAAA;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEtB,OAAA;QAAAkB,QAAA,GAAM,uBAAQ,eAAAlB,OAAA;UAAMc,SAAS,EAAC,eAAe;UAAAI,QAAA,EAAEZ,QAAQ,CAACiB;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjFtB,OAAA;QAAAkB,QAAA,GAAM,mBACK,EAAC,GAAG,eACXlB,OAAA;UAAMc,SAAS,EAAC,eAAe;UAAAI,QAAA,EAC1B,EAAAd,cAAA,GAAAS,KAAK,CAAC,SAAS,CAAC,cAAAT,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKpB,QAAQ,CAACqB,OAAO,CAAC,cAAAtB,mBAAA,uBAAxDA,mBAAA,CAA0DuB,WAAW,KAAI;QAAe;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNtB,OAAA;MAAKc,SAAS,EAAC,yCAAyC;MAAAI,QAAA,eACpDlB,OAAA,CAACJ,aAAa;QAACkB,SAAS,EAAC,eAAe;QAACe,IAAI,EAAEvB,QAAQ,CAACwB;MAAQ;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAGNtB,OAAA,CAACF,2BAA2B;MAACQ,QAAQ,EAAEA;IAAS;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAIlDhB,QAAQ,CAACyB,QAAQ,iBACd/B,OAAA;MAAKc,SAAS,EAAC,uFAAuF;MAAAI,QAAA,gBAClGlB,OAAA;QAAMc,SAAS,EAAC,eAAe;QAAAI,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAG,eACpDtB,OAAA,CAACH,uBAAuB;QAACiC,OAAO,EAAExB,QAAQ,CAACyB;MAAS;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CACR;EAAA,GArCIhB,QAAQ,CAACU,EAAE;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAsCf,CAAC;AAEd,CAAC;AAAAnB,EAAA,CAhDYF,eAAe;EAAA,QACPP,WAAW,EACaD,WAAW,EAElCA,WAAW;AAAA;AAAAuC,EAAA,GAJpB/B,eAAe;AAkD5B,eAAeA,eAAe;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}