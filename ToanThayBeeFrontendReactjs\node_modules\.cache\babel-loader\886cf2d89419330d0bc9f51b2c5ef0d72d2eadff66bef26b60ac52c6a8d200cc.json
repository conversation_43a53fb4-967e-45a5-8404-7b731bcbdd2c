{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\ImageDropZone.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDropZone = _ref => {\n  _s();\n  let {\n    imageUrl,\n    onImageDrop,\n    onImageRemove,\n    content\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\n    if (draggedImage && onImageDrop) {\n      onImageDrop(draggedImage);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-2 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative flex items-center justify-start rounded-md p-4 cursor-pointer\\n        \".concat(isDraggingOver ? \"border border-dashed border-sky-500 bg-sky-50\" : \"hover:border hover:border-dashed hover:border-sky-500 hover:bg-sky-50\"),\n      onDragOver: e => e.preventDefault(),\n      onDragEnter: () => setIsDraggingOver(true),\n      onDragLeave: () => setIsDraggingOver(false),\n      onDrop: e => {\n        setIsDraggingOver(false); // reset lại trạng thái sau khi drop\n        handleDrop(e);\n      },\n      children: isDraggingOver ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500 text-sm\",\n        children: \"Th\\xEAm \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        text: content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this), imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-fit group/image\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: \"dropped\",\n        className: \"rounded-md max-h-40 object-contain w-full border transition-all group-hover/image:brightness-75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20 rounded-md\",\n        onClick: e => {\n          e.stopPropagation();\n          onImageRemove === null || onImageRemove === void 0 ? void 0 : onImageRemove();\n        },\n        title: \"X\\xF3a \\u1EA3nh\",\n        children: /*#__PURE__*/_jsxDEV(Trash2, {\n          className: \"w-6 h-6 text-white bg-red-500 p-1 rounded-full group-hover/image:opacity-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_s(ImageDropZone, \"VKW1ZLDpPe+zo2/qAoOWVX5YkVs=\");\n_c = ImageDropZone;\nexport default ImageDropZone;\nvar _c;\n$RefreshReg$(_c, \"ImageDropZone\");", "map": {"version": 3, "names": ["React", "useState", "Trash2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ImageDropZone", "_ref", "_s", "imageUrl", "onImageDrop", "onImageRemove", "content", "isDraggingOver", "setIsDraggingOver", "handleDrop", "e", "preventDefault", "draggedImage", "dataTransfer", "getData", "className", "children", "concat", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "src", "alt", "onClick", "stopPropagation", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/ImageDropZone.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nconst ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove, content }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\r\n        if (draggedImage && onImageDrop) {\r\n            onImageDrop(draggedImage);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-2 w-full\">\r\n            <div\r\n                className={`relative flex items-center justify-start rounded-md p-4 cursor-pointer\r\n        ${isDraggingOver ? \"border border-dashed border-sky-500 bg-sky-50\"\r\n                        : \"hover:border hover:border-dashed hover:border-sky-500 hover:bg-sky-50\"}`}\r\n                onDragOver={(e) => e.preventDefault()}\r\n                onDragEnter={() => setIsDraggingOver(true)}\r\n                onDragLeave={() => setIsDraggingOver(false)}\r\n                onDrop={(e) => {\r\n                    setIsDraggingOver(false); // reset lại trạng thái sau khi drop\r\n                    handleDrop(e);\r\n                }}\r\n            >\r\n                {isDraggingOver ? (\r\n                    <p className=\"text-gray-500 text-sm\">Thêm ảnh</p>\r\n                ) : (\r\n                    <LatexRenderer text={content} />\r\n                )}\r\n            </div>\r\n            {imageUrl && (\r\n                <div className=\"relative w-fit group/image\">\r\n                    <img\r\n                        src={imageUrl}\r\n                        alt=\"dropped\"\r\n                        className=\"rounded-md max-h-40 object-contain w-full border transition-all group-hover/image:brightness-75\"\r\n                    />\r\n                    <button\r\n                        className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20 rounded-md\"\r\n                        onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            onImageRemove?.();\r\n                        }}\r\n                        title=\"Xóa ảnh\"\r\n                    >\r\n                        <Trash2 className=\"w-6 h-6 text-white bg-red-500 p-1 rounded-full group-hover/image:opacity-100\" />\r\n                    </button>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ImageDropZone;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,cAAc,CAAC,CAAC;AACvC,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACjD,MAAMC,aAAa,GAAGC,IAAA,IAAuD;EAAAC,EAAA;EAAA,IAAtD;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,aAAa;IAAEC;EAAQ,CAAC,GAAAL,IAAA;EACpE,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMc,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMI,YAAY,GAAGF,CAAC,CAACG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3D,IAAIF,YAAY,IAAIR,WAAW,EAAE;MAC7BA,WAAW,CAACQ,YAAY,CAAC;IAC7B;EACJ,CAAC;EAED,oBACIb,OAAA;IAAKgB,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACvCjB,OAAA;MACIgB,SAAS,qFAAAE,MAAA,CACfV,cAAc,GAAG,+CAA+C,GAChD,uEAAuE,CAAG;MACpFW,UAAU,EAAGR,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;MACtCQ,WAAW,EAAEA,CAAA,KAAMX,iBAAiB,CAAC,IAAI,CAAE;MAC3CY,WAAW,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC,KAAK,CAAE;MAC5Ca,MAAM,EAAGX,CAAC,IAAK;QACXF,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1BC,UAAU,CAACC,CAAC,CAAC;MACjB,CAAE;MAAAM,QAAA,EAEDT,cAAc,gBACXR,OAAA;QAAGgB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAQ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAEjD1B,OAAA,CAACF,aAAa;QAAC6B,IAAI,EAAEpB;MAAQ;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAClC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EACLtB,QAAQ,iBACLJ,OAAA;MAAKgB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACvCjB,OAAA;QACI4B,GAAG,EAAExB,QAAS;QACdyB,GAAG,EAAC,SAAS;QACbb,SAAS,EAAC;MAAiG;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9G,CAAC,eACF1B,OAAA;QACIgB,SAAS,EAAC,6JAA6J;QACvKc,OAAO,EAAGnB,CAAC,IAAK;UACZA,CAAC,CAACoB,eAAe,CAAC,CAAC;UACnBzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAG,CAAC;QACrB,CAAE;QACF0B,KAAK,EAAC,iBAAS;QAAAf,QAAA,eAEfjB,OAAA,CAACH,MAAM;UAACmB,SAAS,EAAC;QAA8E;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACvB,EAAA,CAtDIF,aAAa;AAAAgC,EAAA,GAAbhC,aAAa;AAwDnB,eAAeA,aAAa;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}