{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\SortableStatementsContainer.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/examAI/examAISlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableStatementsContainer = _ref => {\n  _s();\n  let {\n    question\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    isAddImage\n  } = useSelector(state => state.examAI);\n  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n  const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      const oldIndex = question.statement1s.findIndex((item, idx) => \"\".concat(item.id || idx) === active.id);\n      const newIndex = question.statement1s.findIndex((item, idx) => \"\".concat(item.id || idx) === over.id);\n      if (oldIndex !== -1 && newIndex !== -1) {\n        dispatch(reorderStatements({\n          questionId: question.id,\n          oldIndex,\n          newIndex\n        }));\n      }\n    }\n  };\n  const getPrefix = index => {\n    if (question.typeOfQuestion === \"TN\") {\n      return prefixTN[index] || \"\".concat(String.fromCharCode(65 + index), \".\");\n    } else if (question.typeOfQuestion === \"DS\") {\n      return prefixDS[index] || \"\".concat(String.fromCharCode(97 + index), \")\");\n    }\n    return \"\".concat(index + 1, \".\");\n  };\n  if (question.typeOfQuestion === \"TLN\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-800\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"\\u0110\\xE1p \\xE1n: \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: question.correctAnswer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this);\n  }\n  if (!question.statement1s || question.statement1s.length === 0) {\n    return null;\n  }\n\n  // Xác định màu sắc dựa trên loại câu hỏi và tính đúng/sai\n  const getTextColor = isCorrect => {\n    if (question.typeOfQuestion === \"TN\") {\n      return isCorrect ? \"text-green-600\" : \"text-gray-800\";\n    } else if (question.typeOfQuestion === \"DS\") {\n      return isCorrect ? \"text-green-600\" : \"text-red-600\";\n    }\n    return \"text-gray-800\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAddImage ?\n    /*#__PURE__*/\n    // Hiển thị bình thường khi isAddImage = true (không drag-and-drop)\n    _jsxDEV(\"div\", {\n      className: \"space-y-1\",\n      children: question.statement1s.map((item, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row gap-1 \".concat(getTextColor(item.isCorrect)),\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold\",\n          children: [getPrefix(idx), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          text: item.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 29\n        }, this)]\n      }, \"\".concat(item.id || idx), true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 25\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 17\n    }, this) :\n    /*#__PURE__*/\n    // Hiển thị với drag-and-drop khi isAddImage = false\n    _jsxDEV(DndContext, {\n      sensors: sensors,\n      collisionDetection: closestCenter,\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(SortableContext, {\n        items: question.statement1s.map((item, idx) => \"\".concat(item.id || idx)),\n        strategy: verticalListSortingStrategy,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1\",\n          children: question.statement1s.map((item, idx) => /*#__PURE__*/_jsxDEV(SortableStatementItem, {\n            statement: item,\n            index: idx,\n            prefix: getPrefix(idx),\n            isCorrect: item.isCorrect,\n            questionType: question.typeOfQuestion\n          }, \"\".concat(item.id || idx), false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableStatementsContainer, \"Ba0KReOlD6ojHwLmVxD/12/DPAE=\", false, function () {\n  return [useDispatch, useSelector, useSensors, useSensor, useSensor];\n});\n_c = SortableStatementsContainer;\nexport default SortableStatementsContainer;\nvar _c;\n$RefreshReg$(_c, \"SortableStatementsContainer\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "reorderStatements", "SortableStatementItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "jsxDEV", "_jsxDEV", "SortableStatementsContainer", "_ref", "_s", "question", "dispatch", "isAddImage", "state", "examAI", "sensors", "coordinateGetter", "prefixTN", "prefixDS", "handleDragEnd", "event", "active", "over", "id", "oldIndex", "statement1s", "findIndex", "item", "idx", "concat", "newIndex", "questionId", "getPrefix", "index", "typeOfQuestion", "String", "fromCharCode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "length", "getTextColor", "isCorrect", "map", "text", "content", "collisionDetection", "onDragEnd", "items", "strategy", "statement", "prefix", "questionType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/SortableStatementsContainer.jsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/examAI/examAISlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport {\n    DndContext,\n    closestCenter,\n    KeyboardSensor,\n    PointerSensor,\n    useSensor,\n    useSensors,\n} from '@dnd-kit/core';\nimport {\n    SortableContext,\n    sortableKeyboardCoordinates,\n    verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\n\nconst SortableStatementsContainer = ({ question }) => {\n    const dispatch = useDispatch();\n    const { isAddImage } = useSelector((state) => state.examAI);\n    \n    const sensors = useSensors(\n        useSensor(PointerSensor),\n        useSensor(KeyboardSensor, {\n            coordinateGetter: sortableKeyboardCoordinates,\n        })\n    );\n\n    const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n    const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n\n    const handleDragEnd = (event) => {\n        const { active, over } = event;\n\n        if (active.id !== over?.id) {\n            const oldIndex = question.statement1s.findIndex((item, idx) => \n                `${item.id || idx}` === active.id\n            );\n            const newIndex = question.statement1s.findIndex((item, idx) => \n                `${item.id || idx}` === over.id\n            );\n\n            if (oldIndex !== -1 && newIndex !== -1) {\n                dispatch(reorderStatements({\n                    questionId: question.id,\n                    oldIndex,\n                    newIndex\n                }));\n            }\n        }\n    };\n\n    const getPrefix = (index) => {\n        if (question.typeOfQuestion === \"TN\") {\n            return prefixTN[index] || `${String.fromCharCode(65 + index)}.`;\n        } else if (question.typeOfQuestion === \"DS\") {\n            return prefixDS[index] || `${String.fromCharCode(97 + index)})`;\n        }\n        return `${index + 1}.`;\n    };\n\n    if (question.typeOfQuestion === \"TLN\") {\n        return (\n            <div className=\"text-gray-800\">\n                <span className=\"font-semibold\">Đáp án: </span>\n                <span>{question.correctAnswer}</span>\n            </div>\n        );\n    }\n\n    if (!question.statement1s || question.statement1s.length === 0) {\n        return null;\n    }\n\n    // Xác định màu sắc dựa trên loại câu hỏi và tính đúng/sai\n    const getTextColor = (isCorrect) => {\n        if (question.typeOfQuestion === \"TN\") {\n            return isCorrect ? \"text-green-600\" : \"text-gray-800\";\n        } else if (question.typeOfQuestion === \"DS\") {\n            return isCorrect ? \"text-green-600\" : \"text-red-600\";\n        }\n        return \"text-gray-800\";\n    };\n\n    return (\n        <div>\n            {!isAddImage ? (\n                // Hiển thị bình thường khi isAddImage = true (không drag-and-drop)\n                <div className=\"space-y-1\">\n                    {question.statement1s.map((item, idx) => (\n                        <div key={`${item.id || idx}`} className={`flex flex-row gap-1 ${getTextColor(item.isCorrect)}`}>\n                            <span className=\"font-semibold\">{getPrefix(idx)} </span>\n                            <LatexRenderer text={item.content} />\n                        </div>\n                    ))}\n                </div>\n            ) : (\n                // Hiển thị với drag-and-drop khi isAddImage = false\n                <DndContext\n                    sensors={sensors}\n                    collisionDetection={closestCenter}\n                    onDragEnd={handleDragEnd}\n                >\n                    <SortableContext\n                        items={question.statement1s.map((item, idx) => `${item.id || idx}`)}\n                        strategy={verticalListSortingStrategy}\n                    >\n                        <div className=\"space-y-1\">\n                            {question.statement1s.map((item, idx) => (\n                                <SortableStatementItem\n                                    key={`${item.id || idx}`}\n                                    statement={item}\n                                    index={idx}\n                                    prefix={getPrefix(idx)}\n                                    isCorrect={item.isCorrect}\n                                    questionType={question.typeOfQuestion}\n                                />\n                            ))}\n                        </div>\n                    </SortableContext>\n                </DndContext>\n            )}\n        </div>\n    );\n};\n\nexport default SortableStatementsContainer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,2BAA2B,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC7C,MAAMG,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqB;EAAW,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAE3D,MAAMC,OAAO,GAAGd,UAAU,CACtBD,SAAS,CAACD,aAAa,CAAC,EACxBC,SAAS,CAACF,cAAc,EAAE;IACtBkB,gBAAgB,EAAEb;EACtB,CAAC,CACL,CAAC;EAED,MAAMc,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB,MAAMC,QAAQ,GAAGd,QAAQ,CAACe,WAAW,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACtD,GAAAC,MAAA,CAAGF,IAAI,CAACJ,EAAE,IAAIK,GAAG,MAAOP,MAAM,CAACE,EACnC,CAAC;MACD,MAAMO,QAAQ,GAAGpB,QAAQ,CAACe,WAAW,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACtD,GAAAC,MAAA,CAAGF,IAAI,CAACJ,EAAE,IAAIK,GAAG,MAAON,IAAI,CAACC,EACjC,CAAC;MAED,IAAIC,QAAQ,KAAK,CAAC,CAAC,IAAIM,QAAQ,KAAK,CAAC,CAAC,EAAE;QACpCnB,QAAQ,CAAClB,iBAAiB,CAAC;UACvBsC,UAAU,EAAErB,QAAQ,CAACa,EAAE;UACvBC,QAAQ;UACRM;QACJ,CAAC,CAAC,CAAC;MACP;IACJ;EACJ,CAAC;EAED,MAAME,SAAS,GAAIC,KAAK,IAAK;IACzB,IAAIvB,QAAQ,CAACwB,cAAc,KAAK,IAAI,EAAE;MAClC,OAAOjB,QAAQ,CAACgB,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE,CAAC,MAAM,IAAIvB,QAAQ,CAACwB,cAAc,KAAK,IAAI,EAAE;MACzC,OAAOhB,QAAQ,CAACe,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE;IACA,UAAAJ,MAAA,CAAUI,KAAK,GAAG,CAAC;EACvB,CAAC;EAED,IAAIvB,QAAQ,CAACwB,cAAc,KAAK,KAAK,EAAE;IACnC,oBACI5B,OAAA;MAAK+B,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BhC,OAAA;QAAM+B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CpC,OAAA;QAAAgC,QAAA,EAAO5B,QAAQ,CAACiC;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEd;EAEA,IAAI,CAAChC,QAAQ,CAACe,WAAW,IAAIf,QAAQ,CAACe,WAAW,CAACmB,MAAM,KAAK,CAAC,EAAE;IAC5D,OAAO,IAAI;EACf;;EAEA;EACA,MAAMC,YAAY,GAAIC,SAAS,IAAK;IAChC,IAAIpC,QAAQ,CAACwB,cAAc,KAAK,IAAI,EAAE;MAClC,OAAOY,SAAS,GAAG,gBAAgB,GAAG,eAAe;IACzD,CAAC,MAAM,IAAIpC,QAAQ,CAACwB,cAAc,KAAK,IAAI,EAAE;MACzC,OAAOY,SAAS,GAAG,gBAAgB,GAAG,cAAc;IACxD;IACA,OAAO,eAAe;EAC1B,CAAC;EAED,oBACIxC,OAAA;IAAAgC,QAAA,EACK,CAAC1B,UAAU;IAAA;IACR;IACAN,OAAA;MAAK+B,SAAS,EAAC,WAAW;MAAAC,QAAA,EACrB5B,QAAQ,CAACe,WAAW,CAACsB,GAAG,CAAC,CAACpB,IAAI,EAAEC,GAAG,kBAChCtB,OAAA;QAA+B+B,SAAS,yBAAAR,MAAA,CAAyBgB,YAAY,CAAClB,IAAI,CAACmB,SAAS,CAAC,CAAG;QAAAR,QAAA,gBAC5FhC,OAAA;UAAM+B,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAEN,SAAS,CAACJ,GAAG,CAAC,EAAC,GAAC;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDpC,OAAA,CAACX,aAAa;UAACqD,IAAI,EAAErB,IAAI,CAACsB;QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,MAAAb,MAAA,CAF5BF,IAAI,CAACJ,EAAE,IAAIK,GAAG;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGtB,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;IAAA;IAEN;IACApC,OAAA,CAACV,UAAU;MACPmB,OAAO,EAAEA,OAAQ;MACjBmC,kBAAkB,EAAErD,aAAc;MAClCsD,SAAS,EAAEhC,aAAc;MAAAmB,QAAA,eAEzBhC,OAAA,CAACJ,eAAe;QACZkD,KAAK,EAAE1C,QAAQ,CAACe,WAAW,CAACsB,GAAG,CAAC,CAACpB,IAAI,EAAEC,GAAG,QAAAC,MAAA,CAAQF,IAAI,CAACJ,EAAE,IAAIK,GAAG,CAAE,CAAE;QACpEyB,QAAQ,EAAEjD,2BAA4B;QAAAkC,QAAA,eAEtChC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,EACrB5B,QAAQ,CAACe,WAAW,CAACsB,GAAG,CAAC,CAACpB,IAAI,EAAEC,GAAG,kBAChCtB,OAAA,CAACZ,qBAAqB;YAElB4D,SAAS,EAAE3B,IAAK;YAChBM,KAAK,EAAEL,GAAI;YACX2B,MAAM,EAAEvB,SAAS,CAACJ,GAAG,CAAE;YACvBkB,SAAS,EAAEnB,IAAI,CAACmB,SAAU;YAC1BU,YAAY,EAAE9C,QAAQ,CAACwB;UAAe,MAAAL,MAAA,CAL9BF,IAAI,CAACJ,EAAE,IAAIK,GAAG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMzB,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EACf;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACjC,EAAA,CA3GIF,2BAA2B;EAAA,QACZhB,WAAW,EACLC,WAAW,EAElBS,UAAU,EACtBD,SAAS,EACTA,SAAS;AAAA;AAAAyD,EAAA,GANXlD,2BAA2B;AA6GjC,eAAeA,2BAA2B;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}