import { <PERSON><PERSON>ath<PERSON>ogo } from '../logo/BeeMathLogo';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { toggleDropdown, toggleExamDropdown, toggleCloseSidebar } from '../../features/sidebar/sidebarSlice';
import { logout } from '../../features/auth/authSlice';
import HeaderSidebar from './HeaderSidebar';
import UserSidebar from './UserSidebar';
import UserType from 'src/constants/UserType';
import { ChevronLeft, ChevronRight, Users, FileText, CreditCard, Newspaper, Globe, LogOut, School, Code, Trophy, ClipboardList, FileSearch, Bot } from "lucide-react";

const MenuSidebar = ({ onClick, route, Icon, text, icon2, role = [] }) => {
    const location = useLocation();
    const closeSidebar = useSelector(state => state.sidebar?.closeSidebar); // Fix lỗi undefined

    const user = useSelector((state) => state.auth.user);
    if (role.length > 0 && !role.includes(user?.userType)) {
        return null;
    }

    return (
        <div className="flex items-center justify-center px-3"
        >
            <button
                onClick={onClick}
                className={`flex items-center rounded-lg
        ${closeSidebar ? 'justify-center w-10 h-10' : 'justify-start gap-4 w-full p-2'}
        ${location.pathname.includes(route)
                        ? 'bg-[#253f61] text-white'
                        : 'bg-white text-[#253f61] hover:bg-[#f0f4fa] hover:text-[#253f61]'}`}
            >
                <Icon className="w-5 h-5" />
                {!closeSidebar && (
                    <div className="flex items-center justify-between flex-row">
                        <div className="text-sm font-medium font-bevietnam leading-none whitespace-nowrap flex-shrink-0">
                            {text}
                        </div>
                        {icon2}
                    </div>

                )}
            </button>
        </div>
    );
}

const AdminSidebar = () => {
    const dropdown = useSelector(state => state.sidebar.dropdownOpen);
    const tuitionDropdown = useSelector(state => state.sidebar.tuitionDropdownOpen);
    const closeSidebar = useSelector(state => state.sidebar.closeSidebar);
    const examDropdown = useSelector(state => state.sidebar.examDropdownOpen);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const handleLogout = async () => {
        await dispatch(logout());
        navigate('/login');
    };

    const icon5 = (
        <div data-svg-wrapper className="relative">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.4916 10.8333L8.57496 12.7416C8.49685 12.8191 8.43486 12.9113 8.39255 13.0128C8.35024 13.1144 8.32846 13.2233 8.32846 13.3333C8.32846 13.4433 8.35024 13.5522 8.39255 13.6538C8.43486 13.7553 8.49685 13.8475 8.57496 13.925C8.65243 14.0031 8.7446 14.0651 8.84615 14.1074C8.9477 14.1497 9.05662 14.1715 9.16663 14.1715C9.27664 14.1715 9.38556 14.1497 9.48711 14.1074C9.58866 14.0651 9.68082 14.0031 9.75829 13.925L13.0916 10.5916C13.1675 10.5124 13.227 10.4189 13.2666 10.3166C13.35 10.1138 13.35 9.8862 13.2666 9.68331C13.227 9.58102 13.1675 9.48757 13.0916 9.40831L9.75829 6.07498C9.68059 5.99728 9.58835 5.93565 9.48683 5.8936C9.38532 5.85155 9.27651 5.8299 9.16663 5.8299C9.05674 5.8299 8.94794 5.85155 8.84642 5.8936C8.7449 5.93565 8.65266 5.99728 8.57496 6.07498C8.49726 6.15268 8.43563 6.24492 8.39358 6.34644C8.35153 6.44796 8.32988 6.55677 8.32988 6.66665C8.32988 6.77653 8.35153 6.88534 8.39358 6.98686C8.43563 7.08837 8.49726 7.18062 8.57496 7.25831L10.4916 9.16665H2.49996C2.27895 9.16665 2.06698 9.25445 1.9107 9.41073C1.75442 9.56701 1.66663 9.77897 1.66663 9.99998C1.66663 10.221 1.75442 10.433 1.9107 10.5892C2.06698 10.7455 2.27895 10.8333 2.49996 10.8333H10.4916ZM9.99996 1.66665C8.44254 1.65969 6.91433 2.08933 5.58868 2.90681C4.26304 3.72429 3.193 4.89691 2.49996 6.29165C2.4005 6.49056 2.38414 6.72083 2.45446 6.93181C2.52479 7.14279 2.67605 7.31719 2.87496 7.41665C3.07387 7.5161 3.30415 7.53247 3.51512 7.46214C3.7261 7.39182 3.9005 7.24056 3.99996 7.04165C4.52679 5.97775 5.32815 5.07383 6.32125 4.4233C7.31435 3.77278 8.46314 3.39925 9.64892 3.34131C10.8347 3.28337 12.0144 3.54313 13.0662 4.09374C14.118 4.64435 15.0037 5.46584 15.6317 6.47331C16.2598 7.48078 16.6074 8.63769 16.6386 9.82447C16.6699 11.0112 16.3837 12.1848 15.8096 13.224C15.2354 14.2631 14.3942 15.1301 13.3729 15.7353C12.3516 16.3406 11.1871 16.6621 9.99996 16.6666C8.75736 16.672 7.53842 16.327 6.48304 15.671C5.42765 15.0151 4.57859 14.0749 4.03329 12.9583C3.93384 12.7594 3.75944 12.6081 3.54846 12.5378C3.33748 12.4675 3.10721 12.4839 2.90829 12.5833C2.70938 12.6828 2.55812 12.8572 2.4878 13.0681C2.41747 13.2791 2.43384 13.5094 2.53329 13.7083C3.19398 15.0379 4.19789 16.1668 5.44119 16.9784C6.68448 17.7899 8.122 18.2545 9.60506 18.3241C11.0881 18.3938 12.5629 18.066 13.8767 17.3746C15.1906 16.6832 16.2959 15.6533 17.0784 14.3915C17.8608 13.1297 18.2919 11.6818 18.327 10.1975C18.3622 8.7132 18.0002 7.24647 17.2785 5.94901C16.5568 4.65154 15.5015 3.57045 14.2219 2.81757C12.9422 2.06469 11.4847 1.66735 9.99996 1.66665V1.66665Z" fill="#71839B" />
            </svg>
        </div>
    )

    return (
        <div className={`fixed left-0 flex flex-col min-h-screen justify-between bg-white ${closeSidebar ? '' : 'w-[16rem]'} p-[1.25rem] shadow-[0px_1px_8px_2px_rgba(20,20,20,0.08)]`}>
            <button
                onClick={() => dispatch(toggleCloseSidebar())}
                className="absolute top-1/2 -right-4 transform -translate-y-1/2 bg-white border rounded-full p-1 shadow-md hover:bg-gray-100 transition"
            >
                {closeSidebar ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
            </button>

            <div className="flex-col w-full justify-start items-start gap-5 inline-flex">
                <HeaderSidebar />
                <div className="flex flex-col gap-3 w-full">
                    {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon1} text={'Tổng quan'} /> */}

                    <div className="flex flex-col gap-3 overflow-y-auto max-h-[calc(100vh-200px)]">
                        <MenuSidebar
                            onClick={() => navigate('/admin/student-management')}
                            route={'/admin/student-management'}
                            Icon={Users}
                            text={'Học sinh'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT, UserType.ASSISTANT]}

                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/class-management')}
                            route={'/admin/class-management'}
                            Icon={School}
                            text={'Lớp học'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT, UserType.ASSISTANT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/question-management')}
                            route={'/admin/question-management'}
                            Icon={ClipboardList}
                            text={'Câu hỏi'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/question-report-management')}
                            route={'/admin/question-report-management'}
                            Icon={FileSearch}
                            text={'Báo cáo câu hỏi'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/exam-management')}
                            route={'/admin/exam-management'}
                            Icon={FileText}
                            text={'Đề thi'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/AI/exam-management')}
                            route={'/admin/AI/exam-management'}
                            Icon={Bot}
                            text={'Đề thi tạo bởi AI'}
                            role={[UserType.ADMIN]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/code-management')}
                            route={'/admin/code-management'}
                            Icon={Code}
                            text={'Code'}
                            role={[UserType.ADMIN]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/achievement-management')}
                            route={'/admin/achievement-management'}
                            Icon={Trophy}
                            text={'Thành tích'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.MARKETING]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/tuition-payment')}
                            route={'/admin/tuition-payment'}
                            Icon={CreditCard}
                            text={'Học phí'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.HUMANRESOURCEMANAGEMENT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/article-management')}
                            route={'/admin/article-management'}
                            Icon={Newspaper}
                            text={'Bài viết'}
                            role={[UserType.ADMIN]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/homepage-management')}
                            route={'/admin/homepage-management'}
                            Icon={Globe}
                            text={'Trang chủ'}
                            role={[UserType.ADMIN, UserType.MARKETING]}
                        />
                    </div>
                </div>
            </div>
            <div className="flex-col w-full justify-start items-start gap-3 inline-flex">
                {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon3} text={'Thông báo'} icon2={notification} /> */}
                {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon4} text={'Trợ giúp'} /> */}
                <MenuSidebar onClick={handleLogout} route={'/login'} Icon={LogOut} text={'Đăng xuất'} />
                <UserSidebar />
            </div>
        </div>
    );
}

export default AdminSidebar;
