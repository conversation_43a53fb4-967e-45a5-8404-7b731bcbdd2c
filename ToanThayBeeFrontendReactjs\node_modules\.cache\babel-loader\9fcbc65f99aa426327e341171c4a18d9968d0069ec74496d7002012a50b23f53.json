{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\SortableQuestionItem.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { GripVertical } from 'lucide-react';\nimport QuestionContent from './QuestionContent';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableQuestionItem = _ref => {\n  _s();\n  let {\n    question,\n    index\n  } = _ref;\n  // Debug: Check if question has id\n  console.log('SortableQuestionItem:', {\n    id: question.id,\n    hasId: !!question.id\n  });\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging\n  } = useSortable({\n    id: question.id\n  });\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n    opacity: isDragging ? 0.5 : 1\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    style: style,\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ...attributes,\n      ...listeners,\n      className: \"absolute left-2 top-4 z-10 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-200 transition-colors\",\n      title: \"K\\xE9o \\u0111\\u1EC3 s\\u1EAFp x\\u1EBFp l\\u1EA1i th\\u1EE9 t\\u1EF1\",\n      children: /*#__PURE__*/_jsxDEV(GripVertical, {\n        size: 16,\n        className: \"text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pl-8\",\n      children: /*#__PURE__*/_jsxDEV(QuestionContent, {\n        question: question,\n        index: index\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableQuestionItem, \"/TOZZ/rmlDvX8r+a5PgN0n4tnHE=\", false, function () {\n  return [useSortable];\n});\n_c = SortableQuestionItem;\nexport default SortableQuestionItem;\nvar _c;\n$RefreshReg$(_c, \"SortableQuestionItem\");", "map": {"version": 3, "names": ["React", "useSortable", "CSS", "GripVertical", "QuestionContent", "jsxDEV", "_jsxDEV", "SortableQuestionItem", "_ref", "_s", "question", "index", "console", "log", "id", "hasId", "attributes", "listeners", "setNodeRef", "transform", "transition", "isDragging", "style", "Transform", "toString", "opacity", "ref", "className", "children", "title", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/SortableQuestionItem.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { useSortable } from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\nimport { GripVertical } from 'lucide-react';\r\nimport QuestionContent from './QuestionContent';\r\n\r\nconst SortableQuestionItem = ({ question, index }) => {\r\n    // Debug: Check if question has id\r\n    console.log('SortableQuestionItem:', { id: question.id, hasId: !!question.id });\r\n\r\n    const {\r\n        attributes,\r\n        listeners,\r\n        setNodeRef,\r\n        transform,\r\n        transition,\r\n        isDragging,\r\n    } = useSortable({ id: question.id });\r\n\r\n    const style = {\r\n        transform: CSS.Transform.toString(transform),\r\n        transition,\r\n        opacity: isDragging ? 0.5 : 1,\r\n    };\r\n\r\n    return (\r\n        <div ref={setNodeRef} style={style} className=\"relative\">\r\n            {/* Drag Handle */}\r\n            <div\r\n                {...attributes}\r\n                {...listeners}\r\n                className=\"absolute left-2 top-4 z-10 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-200 transition-colors\"\r\n                title=\"Kéo để sắp xếp lại thứ tự\"\r\n            >\r\n                <GripVertical size={16} className=\"text-gray-400\" />\r\n            </div>\r\n\r\n            {/* Question Content with left padding for drag handle */}\r\n            <div className=\"pl-8\">\r\n                <QuestionContent question={question} index={index} />\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default SortableQuestionItem;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,GAAG,QAAQ,oBAAoB;AACxC,SAASC,YAAY,QAAQ,cAAc;AAC3C,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,oBAAoB,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAH,IAAA;EAC7C;EACAI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;IAAEC,EAAE,EAAEJ,QAAQ,CAACI,EAAE;IAAEC,KAAK,EAAE,CAAC,CAACL,QAAQ,CAACI;EAAG,CAAC,CAAC;EAE/E,MAAM;IACFE,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC;EACJ,CAAC,GAAGpB,WAAW,CAAC;IAAEa,EAAE,EAAEJ,QAAQ,CAACI;EAAG,CAAC,CAAC;EAEpC,MAAMQ,KAAK,GAAG;IACVH,SAAS,EAAEjB,GAAG,CAACqB,SAAS,CAACC,QAAQ,CAACL,SAAS,CAAC;IAC5CC,UAAU;IACVK,OAAO,EAAEJ,UAAU,GAAG,GAAG,GAAG;EAChC,CAAC;EAED,oBACIf,OAAA;IAAKoB,GAAG,EAAER,UAAW;IAACI,KAAK,EAAEA,KAAM;IAACK,SAAS,EAAC,UAAU;IAAAC,QAAA,gBAEpDtB,OAAA;MAAA,GACQU,UAAU;MAAA,GACVC,SAAS;MACbU,SAAS,EAAC,+GAA+G;MACzHE,KAAK,EAAC,iEAA2B;MAAAD,QAAA,eAEjCtB,OAAA,CAACH,YAAY;QAAC2B,IAAI,EAAE,EAAG;QAACH,SAAS,EAAC;MAAe;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAGN5B,OAAA;MAAKqB,SAAS,EAAC,MAAM;MAAAC,QAAA,eACjBtB,OAAA,CAACF,eAAe;QAACM,QAAQ,EAAEA,QAAS;QAACC,KAAK,EAAEA;MAAM;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzB,EAAA,CArCIF,oBAAoB;EAAA,QAWlBN,WAAW;AAAA;AAAAkC,EAAA,GAXb5B,oBAAoB;AAuC1B,eAAeA,oBAAoB;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}