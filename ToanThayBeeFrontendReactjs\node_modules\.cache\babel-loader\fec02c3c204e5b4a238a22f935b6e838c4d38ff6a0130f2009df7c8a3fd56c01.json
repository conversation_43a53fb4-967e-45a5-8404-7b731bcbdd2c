{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\DropZone.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDroppable } from '@dnd-kit/core';\nimport { ImagePlus } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DropZone = _ref => {\n  _s();\n  let {\n    id,\n    children,\n    onDrop,\n    className = \"\",\n    placeholder = \"Thả hình ảnh vào đây\"\n  } = _ref;\n  const {\n    isOver,\n    setNodeRef\n  } = useDroppable({\n    id: id\n  });\n  const style = {\n    backgroundColor: isOver ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n    border: isOver ? '2px dashed #3b82f6' : '2px dashed transparent',\n    transition: 'all 0.2s ease'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    style: style,\n    className: \"relative min-h-[60px] rounded-lg p-2 \".concat(className),\n    children: [children, isOver && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 flex items-center justify-center bg-blue-50 bg-opacity-90 rounded-lg border-2 border-dashed border-blue-400\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 text-blue-600\",\n        children: [/*#__PURE__*/_jsxDEV(ImagePlus, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: placeholder\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 9\n  }, this);\n};\n_s(DropZone, \"fT702R7NW3L8KUJObOwGrnMsXMQ=\", false, function () {\n  return [useDroppable];\n});\n_c = DropZone;\nexport default DropZone;\nvar _c;\n$RefreshReg$(_c, \"DropZone\");", "map": {"version": 3, "names": ["React", "useDroppable", "ImagePlus", "jsxDEV", "_jsxDEV", "DropZone", "_ref", "_s", "id", "children", "onDrop", "className", "placeholder", "isOver", "setNodeRef", "style", "backgroundColor", "border", "transition", "ref", "concat", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/DropZone.jsx"], "sourcesContent": ["import React from 'react';\nimport { useDroppable } from '@dnd-kit/core';\nimport { ImagePlus } from 'lucide-react';\n\nconst DropZone = ({ id, children, onDrop, className = \"\", placeholder = \"Thả hình ảnh vào đây\" }) => {\n    const { isOver, setNodeRef } = useDroppable({\n        id: id,\n    });\n\n    const style = {\n        backgroundColor: isOver ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n        border: isOver ? '2px dashed #3b82f6' : '2px dashed transparent',\n        transition: 'all 0.2s ease',\n    };\n\n    return (\n        <div\n            ref={setNodeRef}\n            style={style}\n            className={`relative min-h-[60px] rounded-lg p-2 ${className}`}\n        >\n            {children}\n            {isOver && (\n                <div className=\"absolute inset-0 flex items-center justify-center bg-blue-50 bg-opacity-90 rounded-lg border-2 border-dashed border-blue-400\">\n                    <div className=\"flex items-center gap-2 text-blue-600\">\n                        <ImagePlus size={20} />\n                        <span className=\"text-sm font-medium\">{placeholder}</span>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default DropZone;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,QAAQ,GAAGC,IAAA,IAAoF;EAAAC,EAAA;EAAA,IAAnF;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,MAAM;IAAEC,SAAS,GAAG,EAAE;IAAEC,WAAW,GAAG;EAAuB,CAAC,GAAAN,IAAA;EAC5F,MAAM;IAAEO,MAAM;IAAEC;EAAW,CAAC,GAAGb,YAAY,CAAC;IACxCO,EAAE,EAAEA;EACR,CAAC,CAAC;EAEF,MAAMO,KAAK,GAAG;IACVC,eAAe,EAAEH,MAAM,GAAG,yBAAyB,GAAG,aAAa;IACnEI,MAAM,EAAEJ,MAAM,GAAG,oBAAoB,GAAG,wBAAwB;IAChEK,UAAU,EAAE;EAChB,CAAC;EAED,oBACId,OAAA;IACIe,GAAG,EAAEL,UAAW;IAChBC,KAAK,EAAEA,KAAM;IACbJ,SAAS,0CAAAS,MAAA,CAA0CT,SAAS,CAAG;IAAAF,QAAA,GAE9DA,QAAQ,EACRI,MAAM,iBACHT,OAAA;MAAKO,SAAS,EAAC,8HAA8H;MAAAF,QAAA,eACzIL,OAAA;QAAKO,SAAS,EAAC,uCAAuC;QAAAF,QAAA,gBAClDL,OAAA,CAACF,SAAS;UAACmB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvBrB,OAAA;UAAMO,SAAS,EAAC,qBAAqB;UAAAF,QAAA,EAAEG;QAAW;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAClB,EAAA,CA5BIF,QAAQ;EAAA,QACqBJ,YAAY;AAAA;AAAAyB,EAAA,GADzCrB,QAAQ;AA8Bd,eAAeA,QAAQ;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}