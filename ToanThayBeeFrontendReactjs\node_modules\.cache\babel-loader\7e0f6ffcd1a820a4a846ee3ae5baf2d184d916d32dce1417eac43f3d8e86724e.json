{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\RightContent.jsx\",\n  _s = $RefreshSig$();\nimport NavigateBar from \"../PageAddExam/NavigateBar\";\nimport { Eye } from \"lucide-react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setViewRightContent } from \"src/features/questionsExam/questionsExamSlice\";\nimport ExamView from \"./ExamView\";\nimport QuestionView from \"./QuestionView\";\nimport ImageView from \"./ImageView\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RightContent = () => {\n  _s();\n  const {\n    view\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Eye, {\n          className: \"w-3 h-3 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Xem tr\\u01B0\\u1EDBc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded border border-gray-200 \",\n        children: /*#__PURE__*/_jsxDEV(NavigateBar, {\n          list: [{\n            id: 1,\n            name: 'Câu hỏi',\n            value: 'question'\n          }, {\n            id: 2,\n            name: 'Ảnh',\n            value: 'image'\n          }],\n          active: view,\n          setActive: value => dispatch(setViewRightContent(value))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(RightContent, \"COxbS310QlJgXkLnrWEZDqrpNug=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = RightContent;\nexport default RightContent;\nvar _c;\n$RefreshReg$(_c, \"RightContent\");", "map": {"version": 3, "names": ["NavigateBar", "Eye", "useSelector", "useDispatch", "setViewRightContent", "<PERSON><PERSON><PERSON>ie<PERSON>", "Question<PERSON>iew", "ImageView", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RightContent", "_s", "view", "state", "questionsExam", "dispatch", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "list", "id", "name", "value", "active", "setActive", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/RightContent.jsx"], "sourcesContent": ["import NavigateBar from \"../PageAddExam/NavigateBar\";\r\nimport { Eye } from \"lucide-react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setViewRightContent } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport ExamView from \"./ExamView\";\r\nimport QuestionView from \"./QuestionView\";\r\nimport ImageView from \"./ImageView\";\r\n\r\nconst RightContent = () => {\r\n    const { view } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n\r\n    return (\r\n        <>\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <Eye className=\"w-3 h-3 text-gray-600\" />\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Xem trước</h2>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Scrollable Preview Content */}\r\n            <div className=\"flex-1 overflow-y-auto p-3\">\r\n                <div className=\"bg-white rounded border border-gray-200 \">\r\n                    <NavigateBar\r\n                        list={[\r\n                        {\r\n                            id: 1,\r\n                            name: 'Câu hỏi',\r\n                            value: 'question'\r\n                        },\r\n                        {\r\n                            id: 2,\r\n                            name: 'Ảnh',\r\n                            value: 'image'\r\n                        }\r\n                        ]}\r\n                        active={view}\r\n                        setActive={(value) => dispatch(setViewRightContent(value))}\r\n                    />\r\n                    {/* {view === 'question' && <QuestionView />}\r\n                    {view === 'image' && <ImageView />} */}\r\n                </div>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default RightContent;"], "mappings": ";;AAAA,OAAOA,WAAW,MAAM,4BAA4B;AACpD,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGZ,WAAW,CAAEa,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EAC5D,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,oBACIM,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBAEIT,OAAA;MAAKU,SAAS,EAAC,kFAAkF;MAAAD,QAAA,eAC7FT,OAAA;QAAKU,SAAS,EAAC,yBAAyB;QAAAD,QAAA,gBACpCT,OAAA,CAACR,GAAG;UAACkB,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCd,OAAA;UAAIU,SAAS,EAAC,qCAAqC;UAAAD,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNd,OAAA;MAAKU,SAAS,EAAC,4BAA4B;MAAAD,QAAA,eACvCT,OAAA;QAAKU,SAAS,EAAC,0CAA0C;QAAAD,QAAA,eACrDT,OAAA,CAACT,WAAW;UACRwB,IAAI,EAAE,CACN;YACIC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE;UACX,CAAC,EACD;YACIF,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,KAAK,EAAE;UACX,CAAC,CACC;UACFC,MAAM,EAAEd,IAAK;UACbe,SAAS,EAAGF,KAAK,IAAKV,QAAQ,CAACb,mBAAmB,CAACuB,KAAK,CAAC;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAV,EAAA,CAvCKD,YAAY;EAAA,QACGV,WAAW,EACXC,WAAW;AAAA;AAAA2B,EAAA,GAF1BlB,YAAY;AAyClB,eAAeA,YAAY;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}