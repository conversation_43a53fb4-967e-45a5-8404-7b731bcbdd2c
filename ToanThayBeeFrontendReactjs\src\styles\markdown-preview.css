/* Custom styles for Markdown preview */
.markdown-custom-size {
  font-size: 14px !important;
}

.markdown-custom-size * {
  font-size: inherit !important;
}
/* Container for the markdown preview */
.markdown-preview-container {
  width: 100%;
  height: 100%;
}

/* Ensure proper scrolling for editor and preview */
.w-md-editor {
  height: 100% !important;
}

.w-md-editor-text-input {
  overflow: auto !important;
}

.custom-markdown-preview {
  height: 100%;
  overflow: auto;
  scroll-behavior: smooth;
}

/* Center math formulas */
.custom-markdown-preview .katex-display,
.wmde-markdown .katex-display,
.markdown-preview .katex-display {
  display: flex !important;
  justify-content: center !important;
  margin: 1.5rem auto !important;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0.5rem 0;
}

/* Center images */
.custom-markdown-preview img,
.wmde-markdown img,
.markdown-preview img {
  display: block !important;
  margin: 1.5rem auto !important;
  max-width: 100% !important;
  height: auto !important;
}

/* Center tables */
.custom-markdown-preview table,
.wmde-markdown table,
.markdown-preview table {
  margin: 1.5rem auto !important;
  border-collapse: collapse !important;
  width: auto !important;
  max-width: 100% !important;
}

.custom-markdown-preview table th,
.custom-markdown-preview table td,
.wmde-markdown table th,
.wmde-markdown table td,
.markdown-preview table th,
.markdown-preview table td {
  border: 1px solid #ddd !important;
  padding: 8px !important;
  text-align: center !important;
}

.custom-markdown-preview table th,
.wmde-markdown table th,
.markdown-preview table th {
  background-color: #f2f2f2 !important;
}

/* Improve code blocks */
.custom-markdown-preview pre,
.wmde-markdown pre,
.markdown-preview pre {
  background-color: #f6f8fa !important;
  border-radius: 6px !important;
  padding: 16px !important;
  overflow: auto !important;
  margin: 1rem auto !important;
}

/* Improve inline code */
.custom-markdown-preview code:not([class*="language-"]),
.wmde-markdown code:not([class*="language-"]),
.markdown-preview code:not([class*="language-"]) {
  background-color: rgba(175, 184, 193, 0.2) !important;
  border-radius: 6px !important;
  padding: 0.2em 0.4em !important;
  font-family: monospace !important;
}

/* Improve blockquotes */
.custom-markdown-preview blockquote,
.wmde-markdown blockquote,
.markdown-preview blockquote {
  border-left: 4px solid #dfe2e5 !important;
  padding-left: 16px !important;
  color: #6a737d !important;
  margin: 1rem 0 !important;
}
