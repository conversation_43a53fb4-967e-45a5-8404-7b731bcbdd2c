'use strict';

import { Model } from 'sequelize';

export default (sequelize, DataTypes) => {
  class TuitionPayment extends Model {
    static associate(models) {
      // M<PERSON>i quan hệ với bảng User
      TuitionPayment.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user'
      });
    }
  }

  TuitionPayment.init({
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'user',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
      comment: 'ID của học sinh đóng học phí'
    },
    month: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Tháng đóng học phí (định dạng YYYY-MM)'
    },
    isPaid: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Đ<PERSON> đóng học phí hay chưa'
    },
    paymentDate: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '<PERSON><PERSON><PERSON> đóng học phí'
    },
    dueDate: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Ngày đến hạn đóng học phí'
    },

    note: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Ghi chú về việc đóng học phí'
    },

    createdAt: {
      allowNull: false,
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      allowNull: false,
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'TuitionPayment',
    tableName: 'tuitionPayment',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['userId', 'month'],
        name: 'idx_tuition_payment_user_month'
      },
      {
        fields: ['status'],
        name: 'idx_tuition_payment_status'
      }
    ]
  });

  return TuitionPayment;
};
