{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\RightContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\n// Optimized Preview Panel Component\nimport { useSelector } from \"react-redux\";\nimport { Eye, FileText, Image, File } from \"lucide-react\";\nimport { useState } from \"react\";\nimport PdfViewer from \"../ViewPdf\";\nimport NavigateBar from \"./NavigateBar\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageView = () => {\n  _s();\n  const {\n    examImage\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: examImage ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: URL.createObjectURL(examImage),\n        alt: \"exam\",\n        className: \"w-full object-contain\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col items-center\",\n      children: [/*#__PURE__*/_jsxDEV(Image, {\n        className: \"w-6 h-6 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: \"Ch\\u01B0a c\\xF3 \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n_s(ImageView, \"n7yNOFYIxX43G5cs3TSUaLVZo44=\", false, function () {\n  return [useSelector];\n});\n_c = ImageView;\nconst PdfView = () => {\n  _s2();\n  const {\n    examFile\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: examFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(PdfViewer, {\n        url: URL.createObjectURL(examFile)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col items-center\",\n      children: [/*#__PURE__*/_jsxDEV(File, {\n        className: \"w-6 h-6 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: \"Ch\\u01B0a c\\xF3 file PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n_s2(PdfView, \"Z7mKjY/U71p40B1bogstxoIyP1g=\", false, function () {\n  return [useSelector];\n});\n_c2 = PdfView;\nconst ExamView = () => {\n  _s3();\n  const {\n    examData,\n    examImage,\n    examFile\n  } = useSelector(state => state.addExam);\n  const [view, setView] = useState('image');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      div: true,\n      className: \"mb-3 p-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-bold text-gray-900 mb-2\",\n        children: examData.name || \"Tên đề thi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-1 text-xs text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Ki\\u1EC3u:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 26\n          }, this), \" \", examData.typeOfExam || \"Chưa chọn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"L\\u1EDBp:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 26\n          }, this), \" \", examData.class || \"Chưa chọn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"N\\u0103m:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 26\n          }, this), \" \", examData.year || \"Chưa chọn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 21\n        }, this), examData.chapter && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Ch\\u01B0\\u01A1ng:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 47\n          }, this), \" \", examData.chapter]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 42\n        }, this), examData.testDuration && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Th\\u1EDDi gian:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 52\n          }, this), \" \", examData.testDuration, \"p\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 47\n        }, this), examData.passRate && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"\\u0110i\\u1EC3m \\u0111\\u1EA1t:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 48\n          }, this), \" \", examData.passRate, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 43\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this), examData.solutionUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600\",\n          children: [\"Link l\\u1EDDi gi\\u1EA3i: \", examData.solutionUrl]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 25\n      }, this), examData.description && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600\",\n          children: examData.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-1\",\n        children: [examData.public && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full\",\n          children: \"C\\xF4ng khai\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 25\n        }, this), examData.isClassroomExam && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full\",\n          children: \"\\u0110\\u1EC1 thi l\\u1EDBp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 25\n        }, this), !examData.public && !examData.isClassroomExam && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full\",\n          children: \"Ri\\xEAng t\\u01B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(NavigateBar, {\n      list: ['Ảnh đề thi', 'File đề thi'],\n      active: view,\n      setActive: setView\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this), view === 'image' && /*#__PURE__*/_jsxDEV(ImageView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 34\n    }, this), view === 'pdf' && /*#__PURE__*/_jsxDEV(PdfView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 32\n    }, this)]\n  }, void 0, true);\n};\n_s3(ExamView, \"2rrSv/c6+wbxcLl4f1+uyIHx6fc=\", false, function () {\n  return [useSelector];\n});\n_c3 = ExamView;\nconst QuestionView = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \" pt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-1 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-3 h-3 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"C\\xE2u h\\u1ECFi (0)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-4 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-6 h-6 mx-auto mb-1 opacity-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs\",\n          children: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_c4 = QuestionView;\nconst RightContent = () => {\n  _s4();\n  const {\n    examData\n  } = useSelector(state => state.addExam);\n  const [view, setView] = useState('exam');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_42px)] bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Eye, {\n          className: \"w-3 h-3 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Xem tr\\u01B0\\u1EDBc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded border border-gray-200 \",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row text-xs\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => setView('exam'),\n            className: \"flex-1 p-2 text-center \".concat(view === 'exam' ? 'font-bold' : 'border-r border-b border-gray-200', \" cursor-pointer \"),\n            children: \"\\u0110\\u1EC1 thi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => setView('question'),\n            className: \"flex-1 p-2 text-center \".concat(view === 'question' ? 'font-bold' : 'border-l border-b border-gray-200 cursor-pointer'),\n            children: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 21\n        }, this), view === 'exam' && /*#__PURE__*/_jsxDEV(ExamView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 41\n        }, this), view === 'question' && /*#__PURE__*/_jsxDEV(QuestionView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 45\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 9\n  }, this);\n};\n_s4(RightContent, \"jr8dYAPmVJTXzPutd/U0S+QPSCE=\", false, function () {\n  return [useSelector];\n});\n_c5 = RightContent;\nexport default RightContent;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"ImageView\");\n$RefreshReg$(_c2, \"PdfView\");\n$RefreshReg$(_c3, \"ExamView\");\n$RefreshReg$(_c4, \"QuestionView\");\n$RefreshReg$(_c5, \"RightContent\");", "map": {"version": 3, "names": ["useSelector", "Eye", "FileText", "Image", "File", "useState", "PdfViewer", "NavigateBar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageView", "_s", "examImage", "state", "addExam", "children", "className", "src", "URL", "createObjectURL", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PdfView", "_s2", "examFile", "url", "_c2", "<PERSON><PERSON><PERSON>ie<PERSON>", "_s3", "examData", "view", "<PERSON><PERSON><PERSON><PERSON>", "div", "name", "typeOfExam", "class", "year", "chapter", "testDuration", "passRate", "solutionUrl", "description", "public", "isClassroomExam", "list", "active", "setActive", "_c3", "Question<PERSON>iew", "_c4", "RightContent", "_s4", "onClick", "concat", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/RightContent.jsx"], "sourcesContent": ["// Optimized Preview Panel Component\r\nimport { useSelector } from \"react-redux\";\r\nimport { Eye, FileText, Image, File } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\nimport PdfViewer from \"../ViewPdf\";\r\nimport NavigateBar from \"./NavigateBar\";\r\n\r\nconst ImageView = () => {\r\n    const { examImage } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <>\r\n            {examImage ? (\r\n                <div className=\"p-3\">\r\n                    <img src={URL.createObjectURL(examImage)} alt=\"exam\" className=\"w-full object-contain\" />\r\n                </div>\r\n            ) : (\r\n                <div className=\"p-3 flex flex-col items-center\">\r\n                    <Image className=\"w-6 h-6 text-gray-400\" />\r\n                    <p className=\"text-sm text-gray-500\">Chưa có ảnh</p>\r\n                </div>\r\n            )}\r\n        </>\r\n    )\r\n}\r\n\r\nconst PdfView = () => {\r\n    const { examFile } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <>\r\n            {examFile ? (\r\n                <div className=\"p-3\">\r\n                    <PdfViewer url={URL.createObjectURL(examFile)} />\r\n                </div>\r\n            ) : (\r\n                <div className=\"p-3 flex flex-col items-center\">\r\n                    <File className=\"w-6 h-6 text-gray-400\" />\r\n                    <p className=\"text-sm text-gray-500\">Chưa có file PDF</p>\r\n                </div>\r\n            )}\r\n        </>\r\n    )\r\n}\r\n\r\nconst ExamView = () => {\r\n    const { examData, examImage, examFile } = useSelector((state) => state.addExam);\r\n    const [view, setView] = useState('image');\r\n\r\n    return (\r\n        <>\r\n            {/* Compact Exam Header */}\r\n            <div div className=\"mb-3 p-3\" >\r\n                <h3 className=\"text-sm font-bold text-gray-900 mb-2\">\r\n                    {examData.name || \"Tên đề thi\"}\r\n                </h3>\r\n                <div className=\"grid grid-cols-2 gap-1 text-xs text-gray-600\">\r\n                    <div><span className=\"font-medium\">Kiểu:</span> {examData.typeOfExam || \"Chưa chọn\"}</div>\r\n                    <div><span className=\"font-medium\">Lớp:</span> {examData.class || \"Chưa chọn\"}</div>\r\n                    <div><span className=\"font-medium\">Năm:</span> {examData.year || \"Chưa chọn\"}</div>\r\n                    {examData.chapter && <div><span className=\"font-medium\">Chương:</span> {examData.chapter}</div>}\r\n                    {examData.testDuration && <div><span className=\"font-medium\">Thời gian:</span> {examData.testDuration}p</div>}\r\n                    {examData.passRate && <div><span className=\"font-medium\">Điểm đạt:</span> {examData.passRate}%</div>}\r\n                </div>\r\n                {\r\n                    examData.solutionUrl && (\r\n                        <div className=\"mt-2\">\r\n                            <p className=\"text-xs text-gray-600\">Link lời giải: {examData.solutionUrl}</p>\r\n                        </div>\r\n                    )\r\n                }\r\n                {\r\n                    examData.description && (\r\n                        <div className=\"mt-2\">\r\n                            <p className=\"text-xs text-gray-600\">{examData.description}</p>\r\n                        </div>\r\n                    )\r\n                }\r\n            </div>\r\n\r\n            {/* Compact Status Badges */}\r\n            <div className=\"p-3\" >\r\n                <div className=\"flex flex-wrap gap-1\">\r\n                    {examData.public && (\r\n                        <span className=\"px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\r\n                            Công khai\r\n                        </span>\r\n                    )}\r\n                    {examData.isClassroomExam && (\r\n                        <span className=\"px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full\">\r\n                            Đề thi lớp\r\n                        </span>\r\n                    )}\r\n                    {!examData.public && !examData.isClassroomExam && (\r\n                        <span className=\"px-2 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full\">\r\n                            Riêng tư\r\n                        </span>\r\n                    )}\r\n                </div>\r\n            </div>\r\n            <NavigateBar\r\n                list={['Ảnh đề thi', 'File đề thi']}\r\n                active={view}\r\n                setActive={setView}\r\n            />\r\n            {view === 'image' && <ImageView />}\r\n            {view === 'pdf' && <PdfView />}\r\n\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionView = () => {\r\n    return (\r\n        <>\r\n            {/* Questions Preview Placeholder */}\r\n            <div className=\" pt-3\">\r\n                <div className=\"flex items-center gap-1 mb-2\">\r\n                    <FileText className=\"w-3 h-3 text-gray-600\" />\r\n                    <h4 className=\"text-xs font-semibold text-gray-900\">Câu hỏi (0)</h4>\r\n                </div>\r\n                <div className=\"text-center py-4 text-gray-500\">\r\n                    <FileText className=\"w-6 h-6 mx-auto mb-1 opacity-50\" />\r\n                    <p className=\"text-xs\">Chưa có câu hỏi</p>\r\n                </div>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst RightContent = () => {\r\n    const { examData } = useSelector((state) => state.addExam);\r\n    const [view, setView] = useState('exam');\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_42px)] bg-gray-50\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <Eye className=\"w-3 h-3 text-gray-600\" />\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Xem trước</h2>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Scrollable Preview Content */}\r\n            <div className=\"flex-1 overflow-y-auto p-3\">\r\n                <div className=\"bg-white rounded border border-gray-200 \">\r\n                    <div className=\"flex flex-row text-xs\">\r\n                        <div\r\n                            onClick={() => setView('exam')}\r\n                            className={`flex-1 p-2 text-center ${view === 'exam' ? 'font-bold' : 'border-r border-b border-gray-200'} cursor-pointer `}>\r\n                            Đề thi\r\n                        </div>\r\n                        <div\r\n                            onClick={() => setView('question')}\r\n                            className={`flex-1 p-2 text-center ${view === 'question' ? 'font-bold' : 'border-l border-b border-gray-200 cursor-pointer'}`}>\r\n                            Câu hỏi\r\n                        </div>\r\n                    </div>\r\n                    {view === 'exam' && <ExamView />}\r\n                    {view === 'question' && <QuestionView />}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\n\r\nexport default RightContent;"], "mappings": ";;;;;AAAA;AACA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AACzD,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC;EAAU,CAAC,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE3D,oBACIP,OAAA,CAAAE,SAAA;IAAAM,QAAA,EACKH,SAAS,gBACNL,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,eAChBR,OAAA;QAAKU,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACP,SAAS,CAAE;QAACQ,GAAG,EAAC,MAAM;QAACJ,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxF,CAAC,gBAENjB,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAC3CR,OAAA,CAACN,KAAK;QAACe,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CjB,OAAA;QAAGS,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAC;MAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD;EACR,gBACH,CAAC;AAEX,CAAC;AAAAb,EAAA,CAjBKD,SAAS;EAAA,QACWZ,WAAW;AAAA;AAAA2B,EAAA,GAD/Bf,SAAS;AAmBf,MAAMgB,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAClB,MAAM;IAAEC;EAAS,CAAC,GAAG9B,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE1D,oBACIP,OAAA,CAAAE,SAAA;IAAAM,QAAA,EACKa,QAAQ,gBACLrB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,eAChBR,OAAA,CAACH,SAAS;QAACyB,GAAG,EAAEX,GAAG,CAACC,eAAe,CAACS,QAAQ;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,gBAENjB,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAC3CR,OAAA,CAACL,IAAI;QAACc,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CjB,OAAA;QAAGS,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAC;MAAgB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EACR,gBACH,CAAC;AAEX,CAAC;AAAAG,GAAA,CAjBKD,OAAO;EAAA,QACY5B,WAAW;AAAA;AAAAgC,GAAA,GAD9BJ,OAAO;AAmBb,MAAMK,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACnB,MAAM;IAAEC,QAAQ;IAAErB,SAAS;IAAEgB;EAAS,CAAC,GAAG9B,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC/E,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,OAAO,CAAC;EAEzC,oBACII,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBAEIR,OAAA;MAAK6B,GAAG;MAACpB,SAAS,EAAC,UAAU;MAAAD,QAAA,gBACzBR,OAAA;QAAIS,SAAS,EAAC,sCAAsC;QAAAD,QAAA,EAC/CkB,QAAQ,CAACI,IAAI,IAAI;MAAY;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACLjB,OAAA;QAAKS,SAAS,EAAC,8CAA8C;QAAAD,QAAA,gBACzDR,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACK,UAAU,IAAI,WAAW;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1FjB,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACM,KAAK,IAAI,WAAW;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFjB,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACO,IAAI,IAAI,WAAW;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAClFS,QAAQ,CAACQ,OAAO,iBAAIlC,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAO;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACQ,OAAO;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC9FS,QAAQ,CAACS,YAAY,iBAAInC,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAU;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACS,YAAY,EAAC,GAAC;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC5GS,QAAQ,CAACU,QAAQ,iBAAIpC,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACU,QAAQ,EAAC,GAAC;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,EAEFS,QAAQ,CAACW,WAAW,iBAChBrC,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAD,QAAA,eACjBR,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAD,QAAA,GAAC,2BAAe,EAACkB,QAAQ,CAACW,WAAW;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CACR,EAGDS,QAAQ,CAACY,WAAW,iBAChBtC,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAD,QAAA,eACjBR,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAEkB,QAAQ,CAACY;QAAW;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEJ,CAAC,eAGNjB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,eAChBR,OAAA;QAAKS,SAAS,EAAC,sBAAsB;QAAAD,QAAA,GAChCkB,QAAQ,CAACa,MAAM,iBACZvC,OAAA;UAAMS,SAAS,EAAC,0EAA0E;UAAAD,QAAA,EAAC;QAE3F;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACAS,QAAQ,CAACc,eAAe,iBACrBxC,OAAA;UAAMS,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EAAC;QAEzF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAACS,QAAQ,CAACa,MAAM,IAAI,CAACb,QAAQ,CAACc,eAAe,iBAC1CxC,OAAA;UAAMS,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EAAC;QAEzF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNjB,OAAA,CAACF,WAAW;MACR2C,IAAI,EAAE,CAAC,YAAY,EAAE,aAAa,CAAE;MACpCC,MAAM,EAAEf,IAAK;MACbgB,SAAS,EAAEf;IAAQ;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,EACDU,IAAI,KAAK,OAAO,iBAAI3B,OAAA,CAACG,SAAS;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACjCU,IAAI,KAAK,KAAK,iBAAI3B,OAAA,CAACmB,OAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAEhC,CAAC;AAEX,CAAC;AAAAQ,GAAA,CAjEKD,QAAQ;EAAA,QACgCjC,WAAW;AAAA;AAAAqD,GAAA,GADnDpB,QAAQ;AAmEd,MAAMqB,YAAY,GAAGA,CAAA,KAAM;EACvB,oBACI7C,OAAA,CAAAE,SAAA;IAAAM,QAAA,eAEIR,OAAA;MAAKS,SAAS,EAAC,OAAO;MAAAD,QAAA,gBAClBR,OAAA;QAAKS,SAAS,EAAC,8BAA8B;QAAAD,QAAA,gBACzCR,OAAA,CAACP,QAAQ;UAACgB,SAAS,EAAC;QAAuB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CjB,OAAA;UAAIS,SAAS,EAAC,qCAAqC;UAAAD,QAAA,EAAC;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACNjB,OAAA;QAAKS,SAAS,EAAC,gCAAgC;QAAAD,QAAA,gBAC3CR,OAAA,CAACP,QAAQ;UAACgB,SAAS,EAAC;QAAiC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDjB,OAAA;UAAGS,SAAS,EAAC,SAAS;UAAAD,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC,gBACR,CAAC;AAEX,CAAC;AAAA6B,GAAA,GAhBKD,YAAY;AAkBlB,MAAME,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM;IAAEtB;EAAS,CAAC,GAAGnC,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC1D,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,MAAM,CAAC;EAExC,oBACII,OAAA;IAAKS,SAAS,EAAC,iDAAiD;IAAAD,QAAA,gBAE5DR,OAAA;MAAKS,SAAS,EAAC,kFAAkF;MAAAD,QAAA,eAC7FR,OAAA;QAAKS,SAAS,EAAC,yBAAyB;QAAAD,QAAA,gBACpCR,OAAA,CAACR,GAAG;UAACiB,SAAS,EAAC;QAAuB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCjB,OAAA;UAAIS,SAAS,EAAC,qCAAqC;UAAAD,QAAA,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjB,OAAA;MAAKS,SAAS,EAAC,4BAA4B;MAAAD,QAAA,eACvCR,OAAA;QAAKS,SAAS,EAAC,0CAA0C;QAAAD,QAAA,gBACrDR,OAAA;UAAKS,SAAS,EAAC,uBAAuB;UAAAD,QAAA,gBAClCR,OAAA;YACIiD,OAAO,EAAEA,CAAA,KAAMrB,OAAO,CAAC,MAAM,CAAE;YAC/BnB,SAAS,4BAAAyC,MAAA,CAA4BvB,IAAI,KAAK,MAAM,GAAG,WAAW,GAAG,mCAAmC,qBAAmB;YAAAnB,QAAA,EAAC;UAEhI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNjB,OAAA;YACIiD,OAAO,EAAEA,CAAA,KAAMrB,OAAO,CAAC,UAAU,CAAE;YACnCnB,SAAS,4BAAAyC,MAAA,CAA4BvB,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG,kDAAkD,CAAG;YAAAnB,QAAA,EAAC;UAEnI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACLU,IAAI,KAAK,MAAM,iBAAI3B,OAAA,CAACwB,QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/BU,IAAI,KAAK,UAAU,iBAAI3B,OAAA,CAAC6C,YAAY;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC+B,GAAA,CAnCID,YAAY;EAAA,QACOxD,WAAW;AAAA;AAAA4D,GAAA,GAD9BJ,YAAY;AAsClB,eAAeA,YAAY;AAAC,IAAA7B,EAAA,EAAAK,GAAA,EAAAqB,GAAA,EAAAE,GAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAlC,EAAA;AAAAkC,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}