import React from 'react';
import { Settings, Clock, AlertTriangle, RefreshCw } from 'lucide-react';

const MaintenancePage = () => {
    const handleRefresh = () => {
        window.location.reload();
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <div className="max-w-md w-full">
                {/* Main Card */}
                <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
                    {/* Icon */}
                    <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <Settings size={40} className="text-blue-600 animate-spin" style={{
                            animation: 'spin 3s linear infinite'
                        }} />
                    </div>

                    {/* Title */}
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">
                        <PERSON><PERSON> thống đang bảo trì
                    </h1>

                    {/* Description */}
                    <p className="text-gray-600 mb-6 leading-relaxed">
                        Chúng tôi đang thực hiện bảo trì hệ thống để mang đến trải nghiệm tốt hơn cho bạn.
                        Vui lòng quay lại sau ít phút.
                    </p>

                    {/* Status */}
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <div className="flex items-center justify-center gap-2 text-yellow-800">
                            <AlertTriangle size={20} />
                            <span className="font-medium">Đang bảo trì</span>
                        </div>
                        <div className="flex items-center justify-center gap-2 text-yellow-600 mt-2">
                            <Clock size={16} />
                            <span className="text-sm">Thời gian dự kiến: 15-30 phút</span>
                        </div>
                    </div>

                    {/* Refresh Button */}
                    {/* <button
                        onClick={handleRefresh}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
                    >
                        <RefreshCw size={20} />
                        Thử lại
                    </button> */}

                    {/* Footer */}
                    <div className="mt-6 pt-6 border-t border-gray-200">
                        <p className="text-sm text-gray-500">
                            Cảm ơn bạn đã kiên nhẫn chờ đợi
                        </p>
                    </div>
                </div>

                {/* Additional Info */}
                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-600">
                        Nếu có vấn đề khẩn cấp, vui lòng liên hệ:{' '}
                        <a
                            href="https://www.facebook.com/nm.duc7904"
                            className="text-blue-600 hover:text-blue-700 font-medium"
                        >
                            https://www.facebook.com/nm.duc7904
                        </a>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default MaintenancePage;
