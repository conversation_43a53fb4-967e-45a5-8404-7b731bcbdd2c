{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\input\\\\suggestInputBarAdmin.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SuggestInputBarAdmin = _ref => {\n  _s();\n  let {\n    options,\n    placeholder = \"Chọn một mục\",\n    selectedOption,\n    onChange,\n    className = \"w-full\"\n  } = _ref;\n  const [inputValue, setInputValue] = useState(\"\");\n  const [filteredOptions, setFilteredOptions] = useState([...options]);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const wrapperRef = useRef(null);\n\n  // <PERSON><PERSON> có selectedOption, cập nhật giá trị input ban đầu\n  useEffect(() => {\n    if (selectedOption) {\n      const selected = options.find(option => option.code === selectedOption);\n      if (selected) {\n        setInputValue(selected.description);\n      } else {\n        setInputValue(\"\");\n      }\n    } else {\n      // Reset input value khi selectedOption là null/undefined\n      setInputValue(\"\");\n    }\n  }, [selectedOption, options]);\n  useEffect(() => {\n    setFilteredOptions(options);\n  }, [options]);\n  const handleInputChange = e => {\n    var _options$find;\n    const value = e.target.value;\n    setInputValue(value);\n    // Lọc các option theo giá trị input (không phân biệt chữ hoa thường)\n\n    const filtered = options.filter(option => option.description.toLowerCase().includes(value.toLowerCase()));\n    const change = options === null || options === void 0 ? void 0 : (_options$find = options.find(option => option.description.toLowerCase() === value.toLowerCase())) === null || _options$find === void 0 ? void 0 : _options$find.code;\n    if (change) onChange(change);\n    if (value === \"\") onChange(null);\n    setFilteredOptions(filtered);\n    setShowSuggestions(true);\n  };\n  const handleOptionClick = option => {\n    setInputValue(option.description);\n    onChange(option.code);\n    setShowSuggestions(false);\n  };\n\n  // Ẩn suggestions khi click bên ngoài component\n  useEffect(() => {\n    const handleClickOutside = e => {\n      if (wrapperRef.current && !wrapperRef.current.contains(e.target)) {\n        setShowSuggestions(false);\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: wrapperRef,\n    className: \"relative \".concat(className, \" flex-1\"),\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      className: \"w-full bg-white border flex items-center justify-between border-gray-300 rounded py-1.5 px-2 focus:outline-none focus:ring-2 focus:ring-sky-500\",\n      placeholder: placeholder,\n      value: inputValue,\n      onChange: handleInputChange,\n      onFocus: () => setShowSuggestions(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this), showSuggestions && /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-lg shadow-lg z-50 overflow-y-auto hide-scrollbar max-h-60\",\n      children: (filteredOptions === null || filteredOptions === void 0 ? void 0 : filteredOptions.length) > 0 ? filteredOptions.map((option, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"px-4 py-2 hover:bg-gray-200 cursor-pointer text-md\",\n        onClick: () => handleOptionClick(option),\n        children: option.description\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 29\n      }, this)) : /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"px-4 py-2 text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 9\n  }, this);\n};\n_s(SuggestInputBarAdmin, \"ourjZRB0LETAmpSymbvvcL8DbgQ=\");\n_c = SuggestInputBarAdmin;\nexport default SuggestInputBarAdmin;\nvar _c;\n$RefreshReg$(_c, \"SuggestInputBarAdmin\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "SuggestInputBarAdmin", "_ref", "_s", "options", "placeholder", "selectedOption", "onChange", "className", "inputValue", "setInputValue", "filteredOptions", "setFilteredOptions", "showSuggestions", "setShowSuggestions", "wrapperRef", "selected", "find", "option", "code", "description", "handleInputChange", "e", "_options$find", "value", "target", "filtered", "filter", "toLowerCase", "includes", "change", "handleOptionClick", "handleClickOutside", "current", "contains", "document", "addEventListener", "removeEventListener", "ref", "concat", "children", "type", "onFocus", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "index", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/input/suggestInputBarAdmin.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from \"react\";\r\n\r\nconst SuggestInputBarAdmin = ({ options, placeholder = \"Chọn một mục\", selectedOption, onChange, className = \"w-full\" }) => {\r\n    const [inputValue, setInputValue] = useState(\"\");\r\n    const [filteredOptions, setFilteredOptions] = useState([...options]);\r\n    const [showSuggestions, setShowSuggestions] = useState(false);\r\n    const wrapperRef = useRef(null);\r\n\r\n    // Khi có selectedOption, cập nhật giá trị input ban đầu\r\n    useEffect(() => {\r\n        if (selectedOption) {\r\n            const selected = options.find((option) => option.code === selectedOption);\r\n            if (selected) {\r\n                setInputValue(selected.description);\r\n            } else {\r\n                setInputValue(\"\");\r\n            }\r\n        } else {\r\n            // Reset input value khi selectedOption là null/undefined\r\n            setInputValue(\"\");\r\n        }\r\n    }, [selectedOption, options]);\r\n\r\n    useEffect(() => {\r\n        setFilteredOptions(options);\r\n    }, [options]);\r\n\r\n    const handleInputChange = (e) => {\r\n        const value = e.target.value;\r\n        setInputValue(value);\r\n        // Lọc các option theo giá trị input (không phân biệt chữ hoa thường)\r\n\r\n\r\n        const filtered = options.filter((option) =>\r\n            option.description.toLowerCase().includes(value.toLowerCase())\r\n        );\r\n        const change = options?.find((option) => option.description.toLowerCase() === value.toLowerCase())?.code\r\n        if (change) onChange(change);\r\n        if (value === \"\") onChange(null);\r\n        setFilteredOptions(filtered);\r\n        setShowSuggestions(true);\r\n    };\r\n\r\n    const handleOptionClick = (option) => {\r\n        setInputValue(option.description);\r\n        onChange(option.code);\r\n        setShowSuggestions(false);\r\n    };\r\n\r\n    // Ẩn suggestions khi click bên ngoài component\r\n    useEffect(() => {\r\n        const handleClickOutside = (e) => {\r\n            if (wrapperRef.current && !wrapperRef.current.contains(e.target)) {\r\n                setShowSuggestions(false);\r\n            }\r\n        };\r\n        document.addEventListener(\"mousedown\", handleClickOutside);\r\n        return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }, []);\r\n\r\n    return (\r\n        <div ref={wrapperRef} className={`relative ${className} flex-1`}>\r\n            <input\r\n                type=\"text\"\r\n                className=\"w-full bg-white border flex items-center justify-between border-gray-300 rounded py-1.5 px-2 focus:outline-none focus:ring-2 focus:ring-sky-500\"\r\n                placeholder={placeholder}\r\n                value={inputValue}\r\n                onChange={handleInputChange}\r\n                onFocus={() => setShowSuggestions(true)}\r\n            />\r\n            {showSuggestions && (\r\n                <ul className=\"absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-lg shadow-lg z-50 overflow-y-auto hide-scrollbar max-h-60\">\r\n                    {filteredOptions?.length > 0 ? (\r\n                        filteredOptions.map((option, index) => (\r\n                            <li\r\n                                key={index}\r\n                                className=\"px-4 py-2 hover:bg-gray-200 cursor-pointer text-md\"\r\n                                onClick={() => handleOptionClick(option)}\r\n                            >\r\n                                {option.description}\r\n                            </li>\r\n                        ))\r\n                    ) : (\r\n                        <li className=\"px-4 py-2 text-gray-500\">Không có dữ liệu</li>\r\n                    )}\r\n                </ul>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default SuggestInputBarAdmin;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,oBAAoB,GAAGC,IAAA,IAA+F;EAAAC,EAAA;EAAA,IAA9F;IAAEC,OAAO;IAAEC,WAAW,GAAG,cAAc;IAAEC,cAAc;IAAEC,QAAQ;IAAEC,SAAS,GAAG;EAAS,CAAC,GAAAN,IAAA;EACnH,MAAM,CAACO,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,GAAGQ,OAAO,CAAC,CAAC;EACpE,MAAM,CAACS,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMmB,UAAU,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAE/B;EACAD,SAAS,CAAC,MAAM;IACZ,IAAIS,cAAc,EAAE;MAChB,MAAMU,QAAQ,GAAGZ,OAAO,CAACa,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,IAAI,KAAKb,cAAc,CAAC;MACzE,IAAIU,QAAQ,EAAE;QACVN,aAAa,CAACM,QAAQ,CAACI,WAAW,CAAC;MACvC,CAAC,MAAM;QACHV,aAAa,CAAC,EAAE,CAAC;MACrB;IACJ,CAAC,MAAM;MACH;MACAA,aAAa,CAAC,EAAE,CAAC;IACrB;EACJ,CAAC,EAAE,CAACJ,cAAc,EAAEF,OAAO,CAAC,CAAC;EAE7BP,SAAS,CAAC,MAAM;IACZe,kBAAkB,CAACR,OAAO,CAAC;EAC/B,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAEb,MAAMiB,iBAAiB,GAAIC,CAAC,IAAK;IAAA,IAAAC,aAAA;IAC7B,MAAMC,KAAK,GAAGF,CAAC,CAACG,MAAM,CAACD,KAAK;IAC5Bd,aAAa,CAACc,KAAK,CAAC;IACpB;;IAGA,MAAME,QAAQ,GAAGtB,OAAO,CAACuB,MAAM,CAAET,MAAM,IACnCA,MAAM,CAACE,WAAW,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,KAAK,CAACI,WAAW,CAAC,CAAC,CACjE,CAAC;IACD,MAAME,MAAM,GAAG1B,OAAO,aAAPA,OAAO,wBAAAmB,aAAA,GAAPnB,OAAO,CAAEa,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACE,WAAW,CAACQ,WAAW,CAAC,CAAC,KAAKJ,KAAK,CAACI,WAAW,CAAC,CAAC,CAAC,cAAAL,aAAA,uBAAnFA,aAAA,CAAqFJ,IAAI;IACxG,IAAIW,MAAM,EAAEvB,QAAQ,CAACuB,MAAM,CAAC;IAC5B,IAAIN,KAAK,KAAK,EAAE,EAAEjB,QAAQ,CAAC,IAAI,CAAC;IAChCK,kBAAkB,CAACc,QAAQ,CAAC;IAC5BZ,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMiB,iBAAiB,GAAIb,MAAM,IAAK;IAClCR,aAAa,CAACQ,MAAM,CAACE,WAAW,CAAC;IACjCb,QAAQ,CAACW,MAAM,CAACC,IAAI,CAAC;IACrBL,kBAAkB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACAjB,SAAS,CAAC,MAAM;IACZ,MAAMmC,kBAAkB,GAAIV,CAAC,IAAK;MAC9B,IAAIP,UAAU,CAACkB,OAAO,IAAI,CAAClB,UAAU,CAACkB,OAAO,CAACC,QAAQ,CAACZ,CAAC,CAACG,MAAM,CAAC,EAAE;QAC9DX,kBAAkB,CAAC,KAAK,CAAC;MAC7B;IACJ,CAAC;IACDqB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIhC,OAAA;IAAKsC,GAAG,EAAEvB,UAAW;IAACP,SAAS,cAAA+B,MAAA,CAAc/B,SAAS,YAAU;IAAAgC,QAAA,gBAC5DxC,OAAA;MACIyC,IAAI,EAAC,MAAM;MACXjC,SAAS,EAAC,iJAAiJ;MAC3JH,WAAW,EAAEA,WAAY;MACzBmB,KAAK,EAAEf,UAAW;MAClBF,QAAQ,EAAEc,iBAAkB;MAC5BqB,OAAO,EAAEA,CAAA,KAAM5B,kBAAkB,CAAC,IAAI;IAAE;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,EACDjC,eAAe,iBACZb,OAAA;MAAIQ,SAAS,EAAC,+HAA+H;MAAAgC,QAAA,EACxI,CAAA7B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoC,MAAM,IAAG,CAAC,GACxBpC,eAAe,CAACqC,GAAG,CAAC,CAAC9B,MAAM,EAAE+B,KAAK,kBAC9BjD,OAAA;QAEIQ,SAAS,EAAC,oDAAoD;QAC9D0C,OAAO,EAAEA,CAAA,KAAMnB,iBAAiB,CAACb,MAAM,CAAE;QAAAsB,QAAA,EAExCtB,MAAM,CAACE;MAAW,GAJd6B,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKV,CACP,CAAC,gBAEF9C,OAAA;QAAIQ,SAAS,EAAC,yBAAyB;QAAAgC,QAAA,EAAC;MAAgB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAC/D;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC3C,EAAA,CAvFIF,oBAAoB;AAAAkD,EAAA,GAApBlD,oBAAoB;AAyF1B,eAAeA,oBAAoB;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}