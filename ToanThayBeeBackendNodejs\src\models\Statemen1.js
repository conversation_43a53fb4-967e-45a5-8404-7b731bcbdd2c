'use strict'
import { Model } from 'sequelize'
export default (sequelize, DataTypes) => {
  class Statement1 extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
        Statement1.belongsTo(models.Question1, {
            foreignKey: 'questionId',
            as: 'question1'
        })
    }
  }
  Statement1.init({
    content: DataTypes.TEXT,
    questionId: DataTypes.INTEGER,
    isCorrect: DataTypes.BOOLEAN,
    imageUrl: DataTypes.TEXT,
    order: DataTypes.INTEGER,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: 'Statement1',
    tableName: 'statement1'
  })
  return Statement1
}
