'use strict'
import { Model } from 'sequelize'
export default (sequelize, DataTypes) => {
    class Exam1 extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            Exam1.hasMany(models.Question1, {
                foreignKey: 'examId',
                as: 'question1s'
            })
        }
    }
    Exam1.init({
        name: DataTypes.STRING,
        createdAt: DataTypes.DATE,
        updatedAt: DataTypes.DATE
    }, {
        sequelize,
        modelName: 'Exam1',
        tableName: 'exam1'
    })
    return Exam1
}