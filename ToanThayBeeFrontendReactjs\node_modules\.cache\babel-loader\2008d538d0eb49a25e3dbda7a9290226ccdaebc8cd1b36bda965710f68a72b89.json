{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\QuestionContent.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { selectQuestion } from \"src/features/examAI/examAISlice\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\nimport { setQuestionsEdited } from \"src/features/examAI/examAISlice\";\nimport { Trash2 } from \"lucide-react\";\nimport ImageDropZone from \"./ImageDropZone\";\nimport SolutionEditor from \"./SolutionEditor\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const QuestionContent = _ref => {\n  _s();\n  var _codes$chapter, _codes$chapter$find;\n  let {\n    question,\n    index\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    selectedQuestion,\n    isAddImage\n  } = useSelector(state => state.examAI);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\\n            \".concat((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.id) === question.id && !isAddImage ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\", \"\\n        \"),\n    onClick: () => {\n      if (isAddImage) return;\n      dispatch(selectQuestion(question));\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      div: true,\n      className: \"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-gray-700\",\n        children: [\"C\\xE2u \", index + 1]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\u0110\\u1ED9 kh\\xF3: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: question.difficulty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Ch\\u01B0\\u01A1ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: ((_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === question.chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || \"Chưa xác định\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this), isAddImage ? /*#__PURE__*/_jsxDEV(ImageDropZone, {\n      imageUrl: question.imageUrl,\n      onImageDrop: newUrl => {\n        dispatch(setQuestionsEdited({\n          ...question,\n          imageUrl: newUrl\n        }));\n      },\n      onImageRemove: () => {\n        dispatch(setQuestionsEdited({\n          ...question,\n          imageUrl: null\n        }));\n      },\n      content: question.content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 21\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-base text-gray-800 leading-relaxed\",\n      children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        className: \"text-gray-800\",\n        text: question.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 21\n    }, this), /*#__PURE__*/_jsxDEV(SortableStatementsContainer, {\n      question: question\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this), question.solution && /*#__PURE__*/_jsxDEV(SolutionEditor, {\n      solution: question.solution,\n      onSolutionChange: newSolution => {\n        dispatch(setQuestionsEdited({\n          ...question,\n          solution: newSolution\n        }));\n      },\n      isAddImage: isAddImage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 17\n    }, this)]\n  }, question.id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionContent, \"MTz42/6xoRQRjp3Dnj9r7syqXC4=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = QuestionContent;\nexport default QuestionContent;\nvar _c;\n$RefreshReg$(_c, \"QuestionContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "selectQuestion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "SortableStatementsContainer", "setQuestionsEdited", "Trash2", "ImageDropZone", "SolutionEditor", "jsxDEV", "_jsxDEV", "QuestionContent", "_ref", "_s", "_codes$chapter", "_codes$chapter$find", "question", "index", "dispatch", "selectedQuestion", "isAddImage", "state", "examAI", "codes", "className", "concat", "id", "onClick", "children", "div", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "difficulty", "find", "c", "code", "chapter", "description", "imageUrl", "onImageDrop", "newUrl", "onImageRemove", "content", "text", "solution", "onSolutionChange", "newSolution", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/QuestionContent.jsx"], "sourcesContent": ["\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { selectQuestion } from \"src/features/examAI/examAISlice\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\r\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\r\nimport { setQuestionsEdited } from \"src/features/examAI/examAISlice\";\r\nimport { Trash2 } from \"lucide-react\";\r\nimport ImageDropZone from \"./ImageDropZone\";\r\nimport SolutionEditor from \"./SolutionEditor\";\r\n\r\nexport const QuestionContent = ({ question, index }) => {\r\n    const dispatch = useDispatch();\r\n    const { selectedQuestion, isAddImage } = useSelector((state) => state.examAI);\r\n\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    return (\r\n        <div\r\n            key={question.id}\r\n            className={`\r\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\r\n            ${selectedQuestion?.id === question.id && !isAddImage\r\n                    ? \"bg-blue-50 border-blue-500\"\r\n                    : \"bg-white hover:bg-gray-50 border-gray-300\"\r\n                }\r\n        `}\r\n            onClick={() => {\r\n                if (isAddImage) return\r\n                dispatch(selectQuestion(question))\r\n            }}\r\n        >\r\n            {/* Thông tin câu hỏi */}\r\n            <div div className=\"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\" >\r\n                <span className=\"font-medium text-gray-700\">Câu {index + 1}</span>\r\n                <span>Độ khó: <span className=\"text-gray-700\">{question.difficulty}</span></span>\r\n                <span>\r\n                    Chương:{\" \"}\r\n                    <span className=\"text-gray-700\">\r\n                        {codes[\"chapter\"]?.find(c => c.code === question.chapter)?.description || \"Chưa xác định\"}\r\n                    </span>\r\n                </span>\r\n            </div>\r\n\r\n            {/* Nội dung câu hỏi */}\r\n\r\n            {\r\n                isAddImage ? (\r\n                    <ImageDropZone\r\n                        imageUrl={question.imageUrl}\r\n                        onImageDrop={(newUrl) => {\r\n                            dispatch(\r\n                                setQuestionsEdited({\r\n                                    ...question,\r\n                                    imageUrl: newUrl\r\n                                })\r\n                            );\r\n                        }}\r\n                        onImageRemove={() => {\r\n                            dispatch(\r\n                                setQuestionsEdited({\r\n                                    ...question,\r\n                                    imageUrl: null\r\n                                })\r\n                            );\r\n                        }}\r\n                        content={question.content}\r\n                    />\r\n                ) : (\r\n                    <div className=\"text-base text-gray-800 leading-relaxed\">\r\n                        <LatexRenderer className=\"text-gray-800\" text={question.content} />\r\n                    </div>\r\n                )\r\n            }\r\n\r\n\r\n            <SortableStatementsContainer question={question} />\r\n            {/* Statement: A, B, C,... */}\r\n\r\n            {/* Lời giải (nếu có) */}\r\n            {question.solution && (\r\n                <SolutionEditor\r\n                    solution={question.solution}\r\n                    onSolutionChange={(newSolution) => {\r\n                        dispatch(setQuestionsEdited({ ...question, solution: newSolution }));\r\n                    }}\r\n                    isAddImage={isAddImage}\r\n                />\r\n            )}\r\n\r\n\r\n        </div >\r\n    );\r\n}\r\n\r\nexport default QuestionContent;"], "mappings": ";;AACA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,OAAO,MAAMC,eAAe,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,mBAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAL,IAAA;EAC/C,MAAMM,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB,gBAAgB;IAAEC;EAAW,CAAC,GAAGrB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAE7E,MAAM;IAAEC;EAAM,CAAC,GAAGxB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,oBACIb,OAAA;IAEIc,SAAS,oGAAAC,MAAA,CAEP,CAAAN,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEO,EAAE,MAAKV,QAAQ,CAACU,EAAE,IAAI,CAACN,UAAU,GAC3C,4BAA4B,GAC5B,2CAA2C,eAEvD;IACEO,OAAO,EAAEA,CAAA,KAAM;MACX,IAAIP,UAAU,EAAE;MAChBF,QAAQ,CAACjB,cAAc,CAACe,QAAQ,CAAC,CAAC;IACtC,CAAE;IAAAY,QAAA,gBAGFlB,OAAA;MAAKmB,GAAG;MAACL,SAAS,EAAC,mEAAmE;MAAAI,QAAA,gBAClFlB,OAAA;QAAMc,SAAS,EAAC,2BAA2B;QAAAI,QAAA,GAAC,SAAI,EAACX,KAAK,GAAG,CAAC;MAAA;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEvB,OAAA;QAAAkB,QAAA,GAAM,uBAAQ,eAAAlB,OAAA;UAAMc,SAAS,EAAC,eAAe;UAAAI,QAAA,EAAEZ,QAAQ,CAACkB;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjFvB,OAAA;QAAAkB,QAAA,GAAM,mBACK,EAAC,GAAG,eACXlB,OAAA;UAAMc,SAAS,EAAC,eAAe;UAAAI,QAAA,EAC1B,EAAAd,cAAA,GAAAS,KAAK,CAAC,SAAS,CAAC,cAAAT,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKrB,QAAQ,CAACsB,OAAO,CAAC,cAAAvB,mBAAA,uBAAxDA,mBAAA,CAA0DwB,WAAW,KAAI;QAAe;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAKFb,UAAU,gBACNV,OAAA,CAACH,aAAa;MACViC,QAAQ,EAAExB,QAAQ,CAACwB,QAAS;MAC5BC,WAAW,EAAGC,MAAM,IAAK;QACrBxB,QAAQ,CACJb,kBAAkB,CAAC;UACf,GAAGW,QAAQ;UACXwB,QAAQ,EAAEE;QACd,CAAC,CACL,CAAC;MACL,CAAE;MACFC,aAAa,EAAEA,CAAA,KAAM;QACjBzB,QAAQ,CACJb,kBAAkB,CAAC;UACf,GAAGW,QAAQ;UACXwB,QAAQ,EAAE;QACd,CAAC,CACL,CAAC;MACL,CAAE;MACFI,OAAO,EAAE5B,QAAQ,CAAC4B;IAAQ;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,gBAEFvB,OAAA;MAAKc,SAAS,EAAC,yCAAyC;MAAAI,QAAA,eACpDlB,OAAA,CAACR,aAAa;QAACsB,SAAS,EAAC,eAAe;QAACqB,IAAI,EAAE7B,QAAQ,CAAC4B;MAAQ;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CACR,eAILvB,OAAA,CAACN,2BAA2B;MAACY,QAAQ,EAAEA;IAAS;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAIlDjB,QAAQ,CAAC8B,QAAQ,iBACdpC,OAAA,CAACF,cAAc;MACXsC,QAAQ,EAAE9B,QAAQ,CAAC8B,QAAS;MAC5BC,gBAAgB,EAAGC,WAAW,IAAK;QAC/B9B,QAAQ,CAACb,kBAAkB,CAAC;UAAE,GAAGW,QAAQ;UAAE8B,QAAQ,EAAEE;QAAY,CAAC,CAAC,CAAC;MACxE,CAAE;MACF5B,UAAU,EAAEA;IAAW;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACJ;EAAA,GArEIjB,QAAQ,CAACU,EAAE;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAwEd,CAAC;AAEf,CAAC;AAAApB,EAAA,CAlFYF,eAAe;EAAA,QACPX,WAAW,EACaD,WAAW,EAElCA,WAAW;AAAA;AAAAkD,EAAA,GAJpBtC,eAAe;AAoF5B,eAAeA,eAAe;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}