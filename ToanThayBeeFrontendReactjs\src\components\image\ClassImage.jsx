// Gradient styles for different class types
const classTypeGradients = {
    // Đ<PERSON>i số (Algebra) - Blue/Cyan theme
    algebra: [
        ["from-blue-500", "to-cyan-500"],
        ["from-sky-500", "to-blue-600"],
        ["from-cyan-500", "to-teal-500"],
        ["from-indigo-500", "to-blue-500"],
    ],
    // Hình học (Geometry) - Green/Emerald theme
    geometry: [
        ["from-green-500", "to-emerald-500"],
        ["from-emerald-500", "to-teal-600"],
        ["from-lime-500", "to-green-600"],
        ["from-teal-500", "to-emerald-600"],
    ],
    // <PERSON><PERSON><PERSON>n tập (Practice) - Orange/Red theme
    practice: [
        ["from-orange-500", "to-red-500"],
        ["from-red-500", "to-pink-500"],
        ["from-yellow-500", "to-orange-600"],
        ["from-pink-500", "to-red-600"],
    ],
    // Default - Purple/Mixed theme
    default: [
        ["from-purple-500", "to-indigo-500"],
        ["from-violet-500", "to-purple-600"],
        ["from-indigo-500", "to-purple-500"],
        ["from-slate-500", "to-gray-600"],
    ]
};

// Function to determine class type based on name
const getClassType = (name) => {
    if (!name) return 'default';

    const lowerName = name.toLowerCase();

    // Check for "đại" (algebra)
    if (lowerName.includes('đại')) {
        return 'algebra';
    }

    // Check for "hình" (geometry)
    if (lowerName.includes('hình')) {
        return 'geometry';
    }

    // Check for "luyện" (practice)
    if (lowerName.includes('luyện')) {
        return 'practice';
    }

    return 'default';
};

// Function to get gradient based on class type
const getGradientForClassType = (name) => {
    const classType = getClassType(name);
    const gradients = classTypeGradients[classType];

    // Use a consistent hash-based selection instead of random
    // This ensures the same class name always gets the same gradient
    let hash = 0;
    if (name) {
        for (let i = 0; i < name.length; i++) {
            const char = name.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
    }

    const index = Math.abs(hash) % gradients.length;
    return gradients[index];
};

const ClassImage = ({ name, className = "" }) => {
    const [fromColor, toColor] = getGradientForClassType(name);
    const classType = getClassType(name);

    // Get background symbols based on class type
    const getBackgroundSymbols = () => {
        switch (classType) {
            case 'algebra':
                return ['∑', '∫', '∞', '√', '±', 'π', 'α', 'β', 'γ', 'δ', '∆', '∂']; // Math symbols for algebra
            case 'geometry':
                return ['△', '□', '○', '◇', '▲', '■', '●', '◆', '▽', '▢', '◯', '◈']; // Geometric shapes
            case 'practice':
                return ['⚡', '★', '✓', '⭐', '🔥', '💪', '🎯', '🏆', '⚡', '✨', '🚀', '💯']; // Practice/achievement symbols
            default:
                return ['📚', '📖', '✏️', '📝', '🎓', '📊', '📈', '🔍', '💡', '🧠', '📋', '📌']; // General education symbols
        }
    };

    const backgroundSymbols = getBackgroundSymbols();

    return (
        <div
            className={`relative px-6 py-3 bg-gradient-to-br ${fromColor} ${toColor} rounded-lg flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden ${className}`}
        >
            {/* Background symbols */}
            <div className="absolute inset-0 pointer-events-none">
                {/* Top row symbols */}
                <div className="absolute top-[5%] left-[5%] text-white text-lg opacity-20 transform -rotate-12">
                    {backgroundSymbols[0]}
                </div>
                <div className="absolute top-[5%] right-[5%] text-white text-sm opacity-15 transform rotate-12">
                    {backgroundSymbols[1]}
                </div>

                {/* Middle row symbols */}
                <div className="absolute top-[50%] left-[2%] text-white text-xl opacity-10 transform -translate-y-1/2 rotate-45">
                    {backgroundSymbols[2]}
                </div>
                <div className="absolute top-[50%] right-[2%] text-white text-lg opacity-10 transform -translate-y-1/2 -rotate-45">
                    {backgroundSymbols[3]}
                </div>

                {/* Bottom row symbols */}
                <div className="absolute bottom-[5%] left-[10%] text-white text-sm opacity-15 transform rotate-45">
                    {backgroundSymbols[4]}
                </div>
                <div className="absolute bottom-[5%] right-[10%] text-white text-lg opacity-20 transform -rotate-45">
                    {backgroundSymbols[5]}
                </div>

                {/* Additional scattered symbols for larger cards */}
                <div className="absolute top-[25%] left-[25%] text-white text-xs opacity-10 transform rotate-90">
                    {backgroundSymbols[6]}
                </div>
                <div className="absolute top-[75%] right-[25%] text-white text-xs opacity-10 transform -rotate-90">
                    {backgroundSymbols[7]}
                </div>

                {/* Center background symbol - very faded */}
                <div className="absolute top-[50%] left-[50%] text-white text-6xl opacity-5 transform -translate-x-1/2 -translate-y-1/2 rotate-12">
                    {backgroundSymbols[8]}
                </div>
            </div>

            {/* Class name - centered and prominent */}
            <div className="relative z-10 text-white text-lg font-bold font-cubano text-center [text-shadow:_2px_2px_4px_rgb(0_0_0_/_0.7)] leading-tight">
                {name}
            </div>
        </div>
    );
};

export default ClassImage;
