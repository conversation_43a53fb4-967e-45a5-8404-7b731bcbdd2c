'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('exam', 'isClassroomExam', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true, // Assuming existing exams are classroom exams by default
      comment: 'True for classroom exams, false for self-practice exams'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('exam', 'isClassroomExam');
  }
};
