import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Video, BookOpen, PenTool, Calculator, Users, Brain, GraduationCap, Play, ExternalLink, FileText } from 'lucide-react';
import UserLayoutHome from '../../../layouts/UserLayoutHome';

const AllFeaturesPage = () => {
    const navigate = useNavigate();
    const [selectedFeature, setSelectedFeature] = useState(null);

    // Features data with YouTube video IDs - synchronized with Home.jsx
    const featuresData = [
        {
            id: 1,
            title: "Tài liệu học tập",
            description: "Truy cập kho tài liệu phong phú với đầy đủ lý thuyết và bài tập theo từng chương trình học.",
            icon: BookOpen,
            color: "indigo",
            videoId: "2zF8Gk4Mmh8", // Replace with actual YouTube video ID
            features: [
                "<PERSON>ho tài liệu phong phú",
                "<PERSON><PERSON> thuyết đầy đủ",
                "<PERSON><PERSON><PERSON> tập theo chương trình",
                "Cập nhật liên tục"
            ],
            benefits: [
                "Học tập toàn diện",
                "Tiết kiệm thời gian",
                "Chất lượng cao",
                "Dễ dàng truy cập"
            ]
        },
        {
            id: 2,
            title: "Video bài giảng",
            description: "Xem các video bài giảng chất lượng cao với phương pháp giảng dạy dễ hiểu và trực quan.",
            icon: Video,
            color: "purple",
            videoId: "2zF8Gk4Mmh8", // Replace with actual YouTube video ID
            features: [
                "Video chất lượng cao",
                "Phương pháp dễ hiểu",
                "Giảng dạy trực quan",
                "Có thể xem lại nhiều lần"
            ],
            benefits: [
                "Hiểu bài nhanh chóng",
                "Học mọi lúc mọi nơi",
                "Tiết kiệm chi phí",
                "Tương tác cao"
            ]
        },
        {
            id: 3,
            title: "Đề thi & Bài tập",
            description: "Luyện tập với ngân hàng đề thi và bài tập đa dạng từ cơ bản đến nâng cao.",
            icon: FileText,
            color: "blue",
            videoId: "2zF8Gk4Mmh8", // Replace with actual YouTube video ID
            features: [
                "Ngân hàng đề thi đa dạng",
                "Bài tập từ cơ bản đến nâng cao",
                "Phân loại theo chủ đề",
                "Có đáp án chi tiết"
            ],
            benefits: [
                "Luyện tập hiệu quả",
                "Nâng cao kỹ năng",
                "Chuẩn bị thi tốt",
                "Tự đánh giá năng lực"
            ]
        },
        {
            id: 4,
            title: "Luyện thi online",
            description: "Tham gia các bài kiểm tra trực tuyến với đánh giá chi tiết và phản hồi ngay lập tức.",
            icon: PenTool,
            color: "pink",
            videoId: "2zF8Gk4Mmh8", // Replace with actual YouTube video ID
            features: [
                "Kiểm tra trực tuyến",
                "Đánh giá chi tiết",
                "Phản hồi ngay lập tức",
                "Theo dõi tiến độ"
            ],
            benefits: [
                "Tiện lợi và nhanh chóng",
                "Kết quả chính xác",
                "Tiết kiệm thời gian",
                "Theo dõi được tiến bộ"
            ]
        },
        {
            id: 5,
            title: "Công cụ tính toán",
            description: "Sử dụng các công cụ tính toán thông minh giúp giải quyết bài toán nhanh chóng và chính xác.",
            icon: Calculator,
            color: "amber",
            videoId: "2zF8Gk4Mmh8", // Replace with actual YouTube video ID
            features: [
                "Máy tính khoa học tích hợp",
                "Vẽ đồ thị tự động",
                "Giải phương trình",
                "Tính toán ma trận"
            ],
            benefits: [
                "Tính toán nhanh chóng",
                "Giảm sai sót",
                "Tiết kiệm thời gian",
                "Hỗ trợ trực quan"
            ]
        },
        {
            id: 6,
            title: "Lớp học đa dạng",
            description: "Tham gia các lớp học tập để trao đổi kiến thức và giải đáp thắc mắc cùng bạn bè.",
            icon: Users,
            color: "teal",
            videoId: "2zF8Gk4Mmh8", // Replace with actual YouTube video ID
            features: [
                "Tạo nhóm học tập",
                "Chat trực tiếp",
                "Chia sẻ tài liệu",
                "Thảo luận theo chủ đề"
            ],
            benefits: [
                "Học tập hiệu quả",
                "Tăng tương tác",
                "Chia sẻ kiến thức",
                "Hỗ trợ lẫn nhau"
            ]
        },
        {
            id: 7,
            title: "Phương pháp học",
            description: "Tiếp cận các phương pháp học tập hiệu quả và kỹ thuật ghi nhớ giúp việc học toán dễ dàng hơn.",
            icon: Brain,
            color: "rose",
            videoId: "2zF8Gk4Mmh8", // Replace with actual YouTube video ID
            features: [
                "Phương pháp học hiệu quả",
                "Kỹ thuật ghi nhớ",
                "Lộ trình học tập",
                "Tư vấn cá nhân hóa"
            ],
            benefits: [
                "Học tập thông minh",
                "Tiết kiệm thời gian",
                "Nâng cao hiệu quả",
                "Phát triển tư duy"
            ]
        },
        {
            id: 8,
            title: "Tư vấn đại học",
            description: "Nhận tư vấn về định hướng nghề nghiệp và chiến lược ôn thi đại học hiệu quả.",
            icon: GraduationCap,
            color: "violet",
            videoId: "2zF8Gk4Mmh8", // Replace with actual YouTube video ID
            features: [
                "Tư vấn định hướng nghề nghiệp",
                "Chiến lược ôn thi đại học",
                "Hướng dẫn chọn trường",
                "Hỗ trợ hồ sơ xét tuyển"
            ],
            benefits: [
                "Định hướng rõ ràng",
                "Chuẩn bị tốt cho đại học",
                "Tăng cơ hội đỗ",
                "Tiết kiệm thời gian"
            ]
        }
    ];

    const getColorClasses = (color) => {
        const colorMap = {
            indigo: {
                bg: "from-indigo-50 to-white",
                border: "border-indigo-100",
                icon: "bg-indigo-600",
                iconHover: "group-hover:bg-indigo-700",
                text: "text-indigo-600",
                button: "from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700"
            },
            emerald: {
                bg: "from-emerald-50 to-white",
                border: "border-emerald-100",
                icon: "bg-emerald-600",
                iconHover: "group-hover:bg-emerald-700",
                text: "text-emerald-600",
                button: "from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700"
            },
            purple: {
                bg: "from-purple-50 to-white",
                border: "border-purple-100",
                icon: "bg-purple-600",
                iconHover: "group-hover:bg-purple-700",
                text: "text-purple-600",
                button: "from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700"
            },
            blue: {
                bg: "from-blue-50 to-white",
                border: "border-blue-100",
                icon: "bg-blue-600",
                iconHover: "group-hover:bg-blue-700",
                text: "text-blue-600",
                button: "from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
            },
            pink: {
                bg: "from-pink-50 to-white",
                border: "border-pink-100",
                icon: "bg-pink-600",
                iconHover: "group-hover:bg-pink-700",
                text: "text-pink-600",
                button: "from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700"
            },
            amber: {
                bg: "from-amber-50 to-white",
                border: "border-amber-100",
                icon: "bg-amber-600",
                iconHover: "group-hover:bg-amber-700",
                text: "text-amber-600",
                button: "from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
            },
            yellow: {
                bg: "from-yellow-50 to-white",
                border: "border-yellow-100",
                icon: "bg-yellow-600",
                iconHover: "group-hover:bg-yellow-700",
                text: "text-yellow-600",
                button: "from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700"
            },
            teal: {
                bg: "from-teal-50 to-white",
                border: "border-teal-100",
                icon: "bg-teal-600",
                iconHover: "group-hover:bg-teal-700",
                text: "text-teal-600",
                button: "from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700"
            },
            rose: {
                bg: "from-rose-50 to-white",
                border: "border-rose-100",
                icon: "bg-rose-600",
                iconHover: "group-hover:bg-rose-700",
                text: "text-rose-600",
                button: "from-rose-500 to-rose-600 hover:from-rose-600 hover:to-rose-700"
            },
            violet: {
                bg: "from-violet-50 to-white",
                border: "border-violet-100",
                icon: "bg-violet-600",
                iconHover: "group-hover:bg-violet-700",
                text: "text-violet-600",
                button: "from-violet-500 to-violet-600 hover:from-violet-600 hover:to-violet-700"
            }
        };
        return colorMap[color] || colorMap.indigo;
    };

    return (
        <UserLayoutHome>
            <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
                {/* Header */}
                <section className="w-full px-4 py-16">
                    <div className="max-w-screen-xl mx-auto">
                        <motion.div
                            className="text-center mb-12"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            {/* Back Button */}
                            <motion.button
                                onClick={() => navigate('/')}
                                className="inline-flex items-center gap-2 px-4 py-2 text-indigo-700 hover:text-indigo-800 transition-colors duration-300 mb-6"
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.5 }}
                                whileHover={{ x: -5 }}
                            >
                                <ArrowLeft size={20} />
                                Quay về trang chủ
                            </motion.button>

                            <motion.div
                                className="inline-block px-4 py-2 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium mb-4"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 0.2 }}
                            >
                                Học tập hiệu quả
                            </motion.div>

                            <motion.h1
                                className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 font-cubano mb-6 flex items-center justify-center gap-4"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                            >
                                <Video className="w-12 h-12 text-indigo-600" />
                                Tất cả tính năng
                            </motion.h1>

                            <motion.p
                                className="text-gray-600 max-w-3xl mx-auto text-lg lg:text-xl"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.5 }}
                            >
                                Khám phá toàn bộ tính năng đa dạng của hệ thống học tập. Mỗi tính năng đều có video hướng dẫn chi tiết
                                giúp bạn sử dụng hiệu quả và tối ưu hóa quá trình học tập.
                            </motion.p>
                        </motion.div>
                    </div>
                </section>

                {/* Features Grid */}
                <section className="w-full px-4 pb-16">
                    <div className="max-w-screen-xl mx-auto">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                            {featuresData.map((feature, index) => {
                                const colors = getColorClasses(feature.color);
                                const IconComponent = feature.icon;

                                return (
                                    <motion.div
                                        key={feature.id}
                                        className={`bg-gradient-to-br ${colors.bg} p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border ${colors.border} group cursor-pointer`}
                                        initial={{ opacity: 0, y: 30 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        whileHover={{ scale: 1.02, y: -5 }}
                                        onClick={() => setSelectedFeature(feature)}
                                    >
                                        <motion.div
                                            className={`w-16 h-16 rounded-2xl ${colors.icon} ${colors.iconHover} flex items-center justify-center mb-5 mx-auto transition-colors rotate-3 shadow-md`}
                                            whileHover={{ rotate: 6, scale: 1.1 }}
                                        >
                                            <IconComponent size={32} className="text-white" />
                                        </motion.div>

                                        <h3 className="text-xl font-bold text-gray-800 mb-3 text-center group-hover:text-gray-900 transition-colors">
                                            {feature.title}
                                        </h3>

                                        <p className="text-gray-600 text-center mb-4 leading-relaxed">
                                            {feature.description}
                                        </p>

                                        <div className="flex items-center justify-center gap-2 text-sm text-gray-500 mb-4">
                                            <Video size={16} />
                                            <span>Video hướng dẫn có sẵn</span>
                                        </div>

                                        <motion.button
                                            className={`w-full px-4 py-2 bg-gradient-to-r ${colors.button} text-white rounded-lg transition-all duration-300 text-sm font-semibold shadow-md hover:shadow-lg flex items-center justify-center gap-2`}
                                            whileHover={{ scale: 1.02 }}
                                            whileTap={{ scale: 0.98 }}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setSelectedFeature(feature);
                                            }}
                                        >
                                            <Play size={16} />
                                            Xem chi tiết
                                        </motion.button>
                                    </motion.div>
                                );
                            })}
                        </div>
                    </div>
                </section>

                {/* Feature Detail Modal */}
                {selectedFeature && (
                    <motion.div
                        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={() => setSelectedFeature(null)}
                    >
                        <motion.div
                            className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto"
                            initial={{ scale: 0.9, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.9, opacity: 0 }}
                            onClick={(e) => e.stopPropagation()}
                        >
                            {/* Modal Header */}
                            <div className="p-6 border-b border-gray-200">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className={`w-12 h-12 rounded-xl ${getColorClasses(selectedFeature.color).icon} flex items-center justify-center`}>
                                            <selectedFeature.icon size={24} className="text-white" />
                                        </div>
                                        <div>
                                            <h2 className="text-2xl font-bold text-gray-900">{selectedFeature.title}</h2>
                                            <p className="text-gray-600">{selectedFeature.description}</p>
                                        </div>
                                    </div>
                                    <button
                                        onClick={() => setSelectedFeature(null)}
                                        className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                                    >
                                        ×
                                    </button>
                                </div>
                            </div>

                            {/* Modal Content */}
                            <div className="p-6">
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    {/* Left - Video */}
                                    <div>
                                        <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                                            <Video className="w-5 h-5 text-red-600" />
                                            Video hướng dẫn
                                        </h3>
                                        <div className="relative aspect-video bg-gray-100 rounded-xl overflow-hidden shadow-lg">
                                            <iframe
                                                src={`https://www.youtube.com/embed/${selectedFeature.videoId}?rel=0&modestbranding=1`}
                                                title={selectedFeature.title}
                                                className="w-full h-full"
                                                frameBorder="0"
                                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                                allowFullScreen
                                            ></iframe>
                                        </div>

                                        {/* Video Actions */}
                                        <div className="mt-4 flex gap-3">
                                            <motion.a
                                                href={`https://www.youtube.com/watch?v=${selectedFeature.videoId}`}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                                                whileHover={{ scale: 1.02 }}
                                                whileTap={{ scale: 0.98 }}
                                            >
                                                <ExternalLink size={16} />
                                                Xem trên YouTube
                                            </motion.a>
                                            <motion.button
                                                className={`flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${getColorClasses(selectedFeature.color).button} text-white rounded-lg transition-colors text-sm font-medium`}
                                                whileHover={{ scale: 1.02 }}
                                                whileTap={{ scale: 0.98 }}
                                            >
                                                <Play size={16} />
                                                Thử ngay
                                            </motion.button>
                                        </div>
                                    </div>

                                    {/* Right - Details */}
                                    <div className="space-y-6">
                                        {/* Features List */}
                                        <div>
                                            <h3 className="text-xl font-bold text-gray-900 mb-4">Tính năng chính</h3>
                                            <div className="space-y-3">
                                                {selectedFeature.features.map((feature, index) => (
                                                    <motion.div
                                                        key={index}
                                                        className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                                                        initial={{ opacity: 0, x: 20 }}
                                                        animate={{ opacity: 1, x: 0 }}
                                                        transition={{ delay: index * 0.1 }}
                                                    >
                                                        <div className={`w-2 h-2 rounded-full ${getColorClasses(selectedFeature.color).icon}`}></div>
                                                        <span className="text-gray-700">{feature}</span>
                                                    </motion.div>
                                                ))}
                                            </div>
                                        </div>

                                        {/* Benefits List */}
                                        <div>
                                            <h3 className="text-xl font-bold text-gray-900 mb-4">Lợi ích</h3>
                                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                                {selectedFeature.benefits.map((benefit, index) => (
                                                    <motion.div
                                                        key={index}
                                                        className={`p-3 bg-gradient-to-r ${getColorClasses(selectedFeature.color).bg} border ${getColorClasses(selectedFeature.color).border} rounded-lg`}
                                                        initial={{ opacity: 0, scale: 0.9 }}
                                                        animate={{ opacity: 1, scale: 1 }}
                                                        transition={{ delay: index * 0.1 }}
                                                    >
                                                        <div className="flex items-center gap-2">
                                                            <div className={`w-1.5 h-1.5 rounded-full ${getColorClasses(selectedFeature.color).icon}`}></div>
                                                            <span className="text-gray-700 text-sm font-medium">{benefit}</span>
                                                        </div>
                                                    </motion.div>
                                                ))}
                                            </div>
                                        </div>

                                        {/* Call to Action */}
                                        <div className={`p-6 bg-gradient-to-r ${getColorClasses(selectedFeature.color).bg} border ${getColorClasses(selectedFeature.color).border} rounded-xl`}>
                                            <h4 className="text-lg font-bold text-gray-900 mb-2">Sẵn sàng trải nghiệm?</h4>
                                            <p className="text-gray-600 mb-4 text-sm">
                                                Đăng ký ngay để sử dụng tính năng này và nhiều tính năng khác trong hệ thống học tập của chúng tôi.
                                            </p>
                                            <div className="flex gap-3">
                                                <motion.button
                                                    onClick={() => navigate('/login')}
                                                    className={`px-6 py-2 bg-gradient-to-r ${getColorClasses(selectedFeature.color).button} text-white rounded-lg transition-colors text-sm font-semibold flex-1`}
                                                    whileHover={{ scale: 1.02 }}
                                                    whileTap={{ scale: 0.98 }}
                                                >
                                                    Đăng ký ngay
                                                </motion.button>
                                                <motion.button
                                                    onClick={() => navigate('/practice')}
                                                    className="px-6 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-semibold flex-1"
                                                    whileHover={{ scale: 1.02 }}
                                                    whileTap={{ scale: 0.98 }}
                                                >
                                                    Dùng thử
                                                </motion.button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </div>
        </UserLayoutHome>
    );
};

export default AllFeaturesPage;
