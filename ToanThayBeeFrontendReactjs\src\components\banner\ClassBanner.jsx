import { Users, Calendar } from "lucide-react";

// Function to determine class type based on name (same as ClassImage)
const getClassType = (name) => {
    if (!name) return 'default';

    const lowerName = name.toLowerCase();

    // Check for "đại" (algebra)
    if (lowerName.includes('đại')) {
        return 'algebra';
    }

    // Check for "hình" (geometry)
    if (lowerName.includes('hình')) {
        return 'geometry';
    }

    // Check for "luyện" (practice)
    if (lowerName.includes('luyện')) {
        return 'practice';
    }

    return 'default';
};

// Gradient styles for different class types (same as ClassImage)
const classTypeGradients = {
    algebra: [
        ["from-blue-500", "to-cyan-500"],
        ["from-sky-500", "to-blue-600"],
        ["from-cyan-500", "to-teal-500"],
        ["from-indigo-500", "to-blue-500"],
    ],
    geometry: [
        ["from-green-500", "to-emerald-500"],
        ["from-emerald-500", "to-teal-600"],
        ["from-lime-500", "to-green-600"],
        ["from-teal-500", "to-emerald-600"],
    ],
    practice: [
        ["from-orange-500", "to-red-500"],
        ["from-red-500", "to-pink-500"],
        ["from-yellow-500", "to-orange-600"],
        ["from-pink-500", "to-red-600"],
    ],
    default: [
        ["from-purple-500", "to-indigo-500"],
        ["from-violet-500", "to-purple-600"],
        ["from-indigo-500", "to-purple-500"],
        ["from-slate-500", "to-gray-600"],
    ]
};

// Function to get gradient based on class type
const getGradientForClassType = (name) => {
    const classType = getClassType(name);
    const gradients = classTypeGradients[classType];

    // Use a consistent hash-based selection instead of random
    let hash = 0;
    if (name) {
        for (let i = 0; i < name.length; i++) {
            const char = name.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
    }

    const index = Math.abs(hash) % gradients.length;
    return gradients[index];
};

// Get background symbols based on class type with math formulas and geometric shapes
const getBackgroundSymbols = (classType) => {
    switch (classType) {
        case 'algebra':
            return [
                // Ký hiệu cơ bản và hằng số
                'π', 'e', '∞', '√', '∛', '∜', '±', '|x|', 'x²', 'x³', 'y²', 'x!', 'n!',

                // Hàm số phổ biến
                'f(x)', 'g(x)', 'h(x)', 'f\'(x)', 'f\'\'(x)', 'dy/dx', 'd²y/dx²', '∂f/∂x',

                // Hàm lượng giác và logarit
                'sin(x)', 'cos(x)', 'tan(x)', 'cot(x)', 'log(x)', 'ln(x)', 'log₂(x)', 'log₁₀(x)', 'logₐ(x)',

                // Biểu thức đại số
                'a + b', 'x - y', 'ab', 'x/y', 'a^2 + b^2', 'a^n', '(a + b)²', 'x² + y² = r²',

                // Phương trình mẫu
                'ax² + bx + c = 0', 'y = mx + b', 'y = a(x - h)² + k', 'x² + y² = 1',

                // Giới hạn và đạo hàm
                'limₓ→∞ f(x)', 'limₓ→0 sin(x)/x', '∫f(x)dx', '∫₀¹ x² dx', '∫_a^b f(x)dx',

                // Tổ hợp xác suất
                'C(n,k)', 'P(n,k)', 'A(n,k)', 'Σxᵢ', 'Σx²',

                // Ký hiệu tập hợp và mệnh đề logic
                '∈', '∉', '⊂', '⊆', '∪', '∩', '∅', '∀', '∃',

                // Ký hiệu so sánh
                '=', '≠', '≈', '≡', '≤', '≥', '⇒', '⇔',

                // Greek letters phổ biến
                'α', 'β', 'γ', 'δ', 'θ', 'λ', 'μ', 'σ', 'φ', 'ω',

                // Một số tổ hợp ký hiệu
                'x → 0', 'n → ∞', 'Δy/Δx', '∴', '∵'
            ]; // Math symbols and formulas for algebra
        case 'geometry':
            return [
                '△', '□', '○', '◇', '▲', '■', '●', '◆', '▽', '▢', '◯', '◈', '⬟', '⬢', '⬡', '⬠', '⬣', '⬤', '⬭', '⬮', '⬯', '⬰', '⬱', '⬲', '⬳', '⬴', '⬵', '⬶',
                '∠', '⊥', '∥', '≅', '∼', '⌒', '⊙', '⊕', '⊗', '⊘', '⊚', '⊛', '⊜', '⊝', '⊞', '⊟', '⊠', '⊡', '⊢', '⊣', '⊤', '⊥', '⊦', '⊧', '⊨', '⊩', '⊪', '⊫',
                'V=', 'S=', 'P=', 'r=', 'd=', 'h=', 'a²', 'πr²', '4πr²', '⅓πr³', 'abc', '½bh', 'a+b+c', '⅔πr³', '4/3πr³', 'πr²h', '2πr²', '2πrh', 'a³', 'l×w×h',
                'V=abc', 'S=6a²', 'V=⅓Bh', 'C=2πr', 'A=πr²', 'V=πr²h', 'S=2πr(r+h)', 'P=2(a+b)', 'A=ab', 'V=⅓πr²h', 'sin²+cos²=1', 'a²+b²=c²'
            ]; // Geometric shapes and spatial formulas
        case 'practice':
            return ['⚡', '★', '✓', '⭐', '🔥', '💪', '🎯', '🏆', '✨', '🚀', '💯', '⭐', '🌟', '💫', '🎊', '🎉', '🏅', '🥇']; // Practice/achievement symbols
        default:
            return ['📚', '📖', '✏️', '📝', '🎓', '📊', '📈', '🔍', '💡', '🧠', '📋', '📌', '📎', '📐', '📏', '🖊️', '🖋️', '📄']; // General education symbols
    }
};

const ClassBanner = ({ className, title, subtitle, studentCount, lessonCount }) => {
    const [fromColor, toColor] = getGradientForClassType(className);
    const classType = getClassType(className);
    const backgroundSymbols = getBackgroundSymbols(classType);

    return (
        <div className={`relative w-full h-[20rem] rounded-lg overflow-hidden shadow-lg bg-gradient-to-br ${fromColor} ${toColor} flex flex-col justify-center items-center`}>
            {/* Background overlay */}
            <div className="absolute inset-0 bg-black/10"></div>

            {/* Background symbols */}
            <div className="absolute inset-0 pointer-events-none">
                {/* Top row symbols */}
                <div className="absolute top-[5%] left-[5%] text-white text-3xl opacity-[0.5] transform -rotate-12">
                    {backgroundSymbols[0]}
                </div>
                <div className="absolute top-[8%] right-[10%] text-white text-2xl opacity-[0.4] transform rotate-12">
                    {backgroundSymbols[1]}
                </div>
                <div className="absolute top-[15%] left-[25%] text-white text-xl opacity-[0.4] transform rotate-45">
                    {backgroundSymbols[2]}
                </div>
                <div className="absolute top-[12%] right-[25%] text-white text-2xl opacity-[0.3] transform -rotate-30">
                    {backgroundSymbols[3]}
                </div>

                {/* Middle row symbols */}
                <div className="absolute top-[50%] left-[8%] text-white text-4xl opacity-[0.3] transform -translate-y-1/2 rotate-45">
                    {backgroundSymbols[4]}
                </div>
                <div className="absolute top-[50%] right-[8%] text-white text-3xl opacity-[0.4] transform -translate-y-1/2 -rotate-45">
                    {backgroundSymbols[5]}
                </div>
                <div className="absolute top-[33%] left-[15%] text-white text-lg opacity-[0.5] transform rotate-90">
                    {backgroundSymbols[6]}
                </div>
                <div className="absolute top-[67%] right-[15%] text-white text-xl opacity-[0.4] transform -rotate-90">
                    {backgroundSymbols[7]}
                </div>

                {/* Bottom row symbols */}
                <div className="absolute bottom-[10%] left-[8%] text-white text-2xl opacity-[0.4] transform rotate-45">
                    {backgroundSymbols[8]}
                </div>
                <div className="absolute bottom-[8%] right-[8%] text-white text-3xl opacity-[0.5] transform -rotate-45">
                    {backgroundSymbols[9]}
                </div>
                <div className="absolute bottom-[15%] left-[33%] text-white text-xl opacity-[0.4] transform rotate-30">
                    {backgroundSymbols[10]}
                </div>
                <div className="absolute bottom-[12%] right-[33%] text-white text-2xl opacity-[0.3] transform -rotate-60">
                    {backgroundSymbols[11]}
                </div>

                {/* Additional scattered symbols for larger banner */}
                <div className="absolute top-[25%] left-[17%] text-white text-lg opacity-[0.3] transform rotate-75">
                    {backgroundSymbols[12]}
                </div>
                <div className="absolute top-[75%] right-[17%] text-white text-lg opacity-[0.4] transform -rotate-75">
                    {backgroundSymbols[13]}
                </div>
                <div className="absolute top-[17%] left-[50%] text-white text-xl opacity-[0.3] transform rotate-15">
                    {backgroundSymbols[14]}
                </div>
                <div className="absolute bottom-[17%] left-[50%] text-white text-xl opacity-[0.3] transform -rotate-15">
                    {backgroundSymbols[15]}
                </div>

                {/* More scattered math/geometry symbols */}
                <div className="absolute top-[35%] left-[12%] text-white text-base opacity-[0.3] transform rotate-30">
                    {backgroundSymbols[16]}
                </div>
                <div className="absolute top-[65%] right-[12%] text-white text-base opacity-[0.3] transform -rotate-30">
                    {backgroundSymbols[17]}
                </div>
                <div className="absolute top-[45%] left-[75%] text-white text-sm opacity-[0.2] transform rotate-60">
                    {backgroundSymbols[18]}
                </div>
                <div className="absolute top-[55%] left-[20%] text-white text-sm opacity-[0.2] transform -rotate-60">
                    {backgroundSymbols[19]}
                </div>
                <div className="absolute top-[30%] right-[35%] text-white text-lg opacity-[0.3] transform rotate-45">
                    {backgroundSymbols[20]}
                </div>
                <div className="absolute bottom-[25%] left-[65%] text-white text-lg opacity-[0.3] transform -rotate-45">
                    {backgroundSymbols[21]}
                </div>

                {/* Additional corner and edge symbols */}
                <div className="absolute top-[2%] left-[2%] text-white text-sm opacity-[0.2] transform rotate-45">
                    {backgroundSymbols[22]}
                </div>
                <div className="absolute bottom-[2%] right-[2%] text-white text-sm opacity-[0.2] transform -rotate-45">
                    {backgroundSymbols[23]}
                </div>
                <div className="absolute top-[10%] right-[5%] text-white text-xs opacity-[0.2] transform rotate-90">
                    {backgroundSymbols[24]}
                </div>
                <div className="absolute bottom-[10%] left-[5%] text-white text-xs opacity-[0.2] transform -rotate-90">
                    {backgroundSymbols[25]}
                </div>

                {/* More random scattered symbols */}
                <div className="absolute top-[22%] left-[8%] text-white text-lg opacity-[0.2] transform rotate-25">
                    {backgroundSymbols[26]}
                </div>
                <div className="absolute top-[78%] right-[22%] text-white text-base opacity-[0.2] transform -rotate-35">
                    {backgroundSymbols[27]}
                </div>
                <div className="absolute top-[42%] left-[85%] text-white text-sm opacity-[0.2] transform rotate-80">
                    {backgroundSymbols[28]}
                </div>
                <div className="absolute top-[58%] left-[6%] text-white text-xl opacity-[0.2] transform -rotate-25">
                    {backgroundSymbols[29]}
                </div>
                <div className="absolute top-[18%] right-[45%] text-white text-base opacity-[0.2] transform rotate-55">
                    {backgroundSymbols[30]}
                </div>
                <div className="absolute bottom-[22%] left-[42%] text-white text-lg opacity-[0.2] transform -rotate-65">
                    {backgroundSymbols[31]}
                </div>
                <div className="absolute top-[38%] left-[28%] text-white text-sm opacity-[0.2] transform rotate-15">
                    {backgroundSymbols[32]}
                </div>
                <div className="absolute top-[62%] right-[38%] text-white text-base opacity-[0.2] transform -rotate-40">
                    {backgroundSymbols[33]}
                </div>
                <div className="absolute top-[28%] left-[68%] text-white text-xs opacity-[0.2] transform rotate-70">
                    {backgroundSymbols[34]}
                </div>
                <div className="absolute bottom-[35%] right-[8%] text-white text-lg opacity-[0.2] transform -rotate-20">
                    {backgroundSymbols[35]}
                </div>
                <div className="absolute top-[48%] left-[45%] text-white text-sm opacity-[0.1] transform rotate-85">
                    {backgroundSymbols[36]}
                </div>
                <div className="absolute top-[72%] left-[78%] text-white text-base opacity-[0.2] transform -rotate-55">
                    {backgroundSymbols[37]}
                </div>
                <div className="absolute top-[14%] left-[38%] text-white text-xs opacity-[0.2] transform rotate-35">
                    {backgroundSymbols[38]}
                </div>
                <div className="absolute bottom-[18%] right-[62%] text-white text-lg opacity-[0.2] transform -rotate-75">
                    {backgroundSymbols[39]}
                </div>
                <div className="absolute top-[52%] right-[18%] text-white text-sm opacity-[0.2] transform rotate-50">
                    {backgroundSymbols[40]}
                </div>
                <div className="absolute top-[32%] left-[52%] text-white text-base opacity-[0.2] transform -rotate-30">
                    {backgroundSymbols[41]}
                </div>
                <div className="absolute bottom-[28%] left-[28%] text-white text-xs opacity-[0.2] transform rotate-65">
                    {backgroundSymbols[42]}
                </div>
                <div className="absolute top-[68%] right-[52%] text-white text-lg opacity-[0.2] transform -rotate-45">
                    {backgroundSymbols[43]}
                </div>
                <div className="absolute top-[24%] right-[72%] text-white text-sm opacity-[0.2] transform rotate-40">
                    {backgroundSymbols[44]}
                </div>
                <div className="absolute bottom-[42%] left-[72%] text-white text-base opacity-[0.2] transform -rotate-60">
                    {backgroundSymbols[45]}
                </div>
            </div>

            {/* Content - centered and prominent */}
            <div className="relative z-10 text-center px-6">
                <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 [text-shadow:_2px_2px_4px_rgb(0_0_0_/_0.7)]">
                    {title}
                </h1>
                <p className="text-white/90 text-lg md:text-xl max-w-2xl [text-shadow:_1px_1px_2px_rgb(0_0_0_/_0.5)] mb-6">
                    {subtitle}
                </p>

                {/* Class info */}
                <div className="flex flex-wrap justify-center gap-4">
                    <div className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full flex items-center">
                        <Users size={16} className="text-white mr-2" />
                        <span className="text-white text-sm font-medium">{studentCount} học sinh</span>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full flex items-center">
                        <Calendar size={16} className="text-white mr-2" />
                        <span className="text-white text-sm font-medium">{lessonCount} buổi học</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ClassBanner;
