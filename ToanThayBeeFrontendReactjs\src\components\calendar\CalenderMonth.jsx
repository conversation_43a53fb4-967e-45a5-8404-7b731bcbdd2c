import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useSelector, useDispatch } from 'react-redux';
import {
    setSelectedDay,
    prevMonth,
    nextMonth,
} from '../../features/calendar/calendarSlice';

const getDaysInMonth = (year, month) => new Date(year, month + 1, 0).getDate();
const getFirstDayOfMonth = (year, month) => new Date(year, month, 1).getDay();

const CalendarMonth = () => {
    const dispatch = useDispatch();
    const { currentMonth, currentYear, selectedDay, today } = useSelector((state) => state.calendar);

    const selectedDate = new Date(selectedDay);
    const todayDate = new Date(today);

    const daysInMonth = getDaysInMonth(currentYear, currentMonth);
    const firstDay = getFirstDayOfMonth(currentYear, currentMonth);

    const isToday = (day) =>
        todayDate.getDate() === day &&
        todayDate.getMonth() === currentMonth &&
        todayDate.getFullYear() === currentYear;

    const isSelected = (day) =>
        selectedDate.getDate() === day &&
        selectedDate.getMonth() === currentMonth &&
        selectedDate.getFullYear() === currentYear;

    const vietnameseMonths = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12',
    ];

    return (
        <div className="rounded-xl bg-white p-2 text-black mt-4 shadow border text-[13px]">
            <div className="flex justify-between items-center mb-2">
                <button onClick={() => dispatch(prevMonth())} className='text-gray-600 hover:text-gray-800 p-1 rounded-full hover:bg-gray-100'>
                    <ChevronLeft className="w-4 h-4" />
                </button>
                <h2 className="text-xs font-semibold">
                    {vietnameseMonths[currentMonth]}, {currentYear}
                </h2>
                <button onClick={() => dispatch(nextMonth())} className='text-gray-600 hover:text-gray-800 p-1 rounded-full hover:bg-gray-100'>
                    <ChevronRight className="w-4 h-4" />
                </button>
            </div>

            <div className="grid grid-cols-7 text-[11px] text-center text-gray-600 mb-1">
                <div>Cn</div><div>T2</div><div>T3</div><div>T4</div><div>T5</div><div>T6</div><div>T7</div>
            </div>

            <div className="grid grid-cols-7 text-[12px] text-center gap-y-1">
                {[...Array(firstDay)].map((_, i) => <div key={`empty-${i}`} />)}
                {[...Array(daysInMonth)].map((_, i) => {
                    const day = i + 1;

                    const baseClass = 'w-6 h-6 flex items-center justify-center rounded-full cursor-pointer';
                    let bgClass = 'hover:bg-gray-200';
                    let textClass = '';

                    if (isToday(day)) {
                        bgClass = 'bg-sky-500';
                        textClass = 'text-white font-bold';
                    } else if (isSelected(day)) {
                        bgClass = 'bg-sky-200';
                        textClass = 'text-black font-semibold';
                    }

                    const fullDate = new Date(currentYear, currentMonth, day);

                    return (
                        <div className='flex items-center justify-center' key={day}>
                            <div
                                className={`${baseClass} ${bgClass} ${textClass}`}
                                onClick={() => dispatch(setSelectedDay(fullDate.toISOString()))}
                            >
                                {day}
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default CalendarMonth;
