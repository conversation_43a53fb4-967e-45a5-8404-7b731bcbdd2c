"use strict";

export default {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.bulkInsert("allCode", [
      // 👤 User Type
      { code: "AD", type: "user type", description: "Admin", createdAt: new Date(), updatedAt: new Date() },
      { code: "HS1", type: "user type", description: "Học sinh trên lớp", createdAt: new Date(), updatedAt: new Date() },
      { code: "GV", type: "user type", description: "Giáo viên", createdAt: new Date(), updatedAt: new Date() },
      { code: "AS", type: "user type", description: "Trợ giảng", createdAt: new Date(), updatedAt: new Date() },

      // Grade
      { code: "10", type: "grade", description: "Lớp 10", createdAt: new Date(), updatedAt: new Date() },
      { code: "11", type: "grade", description: "Lớp 11", createdAt: new Date(), updatedAt: new Date() },
      { code: "12", type: "grade", description: "Lớp 12", createdAt: new Date(), updatedAt: new Date() },

      // 📚 Class Status
      { code: "LHD", type: "class status", description: "Hoạt động", createdAt: new Date(), updatedAt: new Date() },
      { code: "LKT", type: "class status", description: "Kết thúc", createdAt: new Date(), updatedAt: new Date() },

      // 🎓 Student Status
      { code: "HSDH", type: "student status", description: "Học sinh đang học", createdAt: new Date(), updatedAt: new Date() },
      { code: "HSNH", type: "student status", description: "Học sinh nghỉ học", createdAt: new Date(), updatedAt: new Date() },
      { code: "HSTN", type: "student status", description: "Học sinh tốt nghiệp", createdAt: new Date(), updatedAt: new Date() },

      // 🗓️ Days of Week (DOW)
      { code: "T2", type: "dow", description: "Thứ 2", createdAt: new Date(), updatedAt: new Date() },
      { code: "T3", type: "dow", description: "Thứ 3", createdAt: new Date(), updatedAt: new Date() },
      { code: "T4", type: "dow", description: "Thứ 4", createdAt: new Date(), updatedAt: new Date() },
      { code: "T5", type: "dow", description: "Thứ 5", createdAt: new Date(), updatedAt: new Date() },
      { code: "T6", type: "dow", description: "Thứ 6", createdAt: new Date(), updatedAt: new Date() },
      { code: "T7", type: "dow", description: "Thứ 7", createdAt: new Date(), updatedAt: new Date() },
      { code: "CN", type: "dow", description: "Chủ nhật", createdAt: new Date(), updatedAt: new Date() },

      // ⏳ Duration
      { code: "7:00 - 9:00", type: "duration", description: "7:00 - 9:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "7:30 - 9:30", type: "duration", description: "7:30 - 9:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "8:00 - 10:00", type: "duration", description: "8:00 - 10:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "8:30 - 10:30", type: "duration", description: "8:30 - 10:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "9:00 - 11:00", type: "duration", description: "9:00 - 11:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "9:30 - 11:30", type: "duration", description: "9:30 - 11:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "10:00 - 12:00", type: "duration", description: "10:00 - 12:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "10:30 - 12:30", type: "duration", description: "10:30 - 12:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "13:00 - 15:00", type: "duration", description: "13:00 - 15:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "13:30 - 15:30", type: "duration", description: "13:30 - 15:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "14:00 - 16:00", type: "duration", description: "14:00 - 16:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "14:30 - 16:30", type: "duration", description: "14:30 - 16:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "15:00 - 17:00", type: "duration", description: "15:00 - 17:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "15:30 - 17:30", type: "duration", description: "15:30 - 17:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "16:00 - 18:00", type: "duration", description: "16:00 - 18:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "16:30 - 18:30", type: "duration", description: "16:30 - 18:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "17:00 - 19:00", type: "duration", description: "17:00 - 19:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "17:30 - 19:30", type: "duration", description: "17:30 - 19:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "18:00 - 20:00", type: "duration", description: "18:00 - 20:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "18:30 - 20:30", type: "duration", description: "18:30 - 20:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "19:00 - 21:00", type: "duration", description: "19:00 - 21:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "19:30 - 21:30", type: "duration", description: "19:30 - 21:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "20:00 - 22:00", type: "duration", description: "20:00 - 22:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "20:30 - 22:30", type: "duration", description: "20:30 - 22:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "21:00 - 23:00", type: "duration", description: "21:00 - 23:00", createdAt: new Date(), updatedAt: new Date() },
      { code: "21:30 - 23:30", type: "duration", description: "21:30 - 23:30", createdAt: new Date(), updatedAt: new Date() },
      { code: "22:00 - 24:00", type: "duration", description: "22:00 - 24:00", createdAt: new Date(), updatedAt: new Date() },

      // Article Type
      { code: "LT", type: "article type", description: "Lý thuyết", createdAt: new Date(), updatedAt: new Date() },
      { code: "SE", type: "article type", description: "Chia sẻ kinh nghiệm", createdAt: new Date(), updatedAt: new Date() },
      { code: "KT", type: "article type", description: "Các kì thi", createdAt: new Date(), updatedAt: new Date() },

      // Year
      { code: "2023 - 2024", type: "year", description: "2023 - 2024", createdAt: new Date(), updatedAt: new Date() },
      { code: "2024 - 2025", type: "year", description: "2024 - 2025", createdAt: new Date(), updatedAt: new Date() },
      { code: "2025 - 2026", type: "year", description: "2025 - 2026", createdAt: new Date(), updatedAt: new Date() },

      // 📖 Study Item Type
      { code: "BTVN", type: "study item type", description: "Bài tập về nhà", createdAt: new Date(), updatedAt: new Date() },
      { code: "DOC", type: "study item type", description: "Tài liệu", createdAt: new Date(), updatedAt: new Date() },
      { code: "VID", type: "study item type", description: "Video", createdAt: new Date(), updatedAt: new Date() },

      // 📝 Exam Type
      { code: "GK1", type: "exam type", description: "Giữa kì 1", createdAt: new Date(), updatedAt: new Date() },
      { code: "CK1", type: "exam type", description: "Cuối kì 1", createdAt: new Date(), updatedAt: new Date() },
      { code: "GK2", type: "exam type", description: "Giữa kì 2", createdAt: new Date(), updatedAt: new Date() },
      { code: "CK2", type: "exam type", description: "Cuối kì 2", createdAt: new Date(), updatedAt: new Date() },
      { code: "OT", type: "exam type", description: "Ôn tập", createdAt: new Date(), updatedAt: new Date() },
      { code: "THPT", type: "exam type", description: "Thi THPT", createdAt: new Date(), updatedAt: new Date() },
      { code: "OTTHPT", type: "exam type", description: "Ôn thi THPT", createdAt: new Date(), updatedAt: new Date() },
      { code: "HSA", type: "exam type", description: "Đánh giá năng lực", createdAt: new Date(), updatedAt: new Date() },
      { code: "TSA", type: "exam type", description: "Đánh giá tư duy", createdAt: new Date(), updatedAt: new Date() },

      // 📚 Chapter
      { code: "10C1", type: "chapter", description: "MỆNH ĐỀ VÀ TẬP HỢP", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C11", type: "chapter", description: "Mệnh đề", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C12", type: "chapter", description: "Tập hợp và các phép toán trên tập hợp", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C2", type: "chapter", description: "BẤT PHƯƠNG TRÌNH VÀ HỆ BẤT PHƯƠNG TRÌNH BẬC NHẤT HAI ẨN", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C21", type: "chapter", description: "Bất phương trình bậc nhất hai ẩn", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C22", type: "chapter", description: "Hệ bất phương trình bậc nhất hai ẩn", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C3", type: "chapter", description: "HỆ THỨC LƯỢNG TRONG TAM GIÁC", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C31", type: "chapter", description: "Giá trị lượng giác của một góc từ 0° đến 180°", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C32", type: "chapter", description: "Hệ thức lượng trong tam giác°", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C4", type: "chapter", description: "VECTƠ", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C41", type: "chapter", description: "Các khái niệm mở đầu", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C42", type: "chapter", description: "Tổng và hiệu của hai vectơ", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C43", type: "chapter", description: "Tích của một vectơ với một số", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C44", type: "chapter", description: "Vectơ trong mặt phẳng toạ độ", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C45", type: "chapter", description: "Tích vô hướng của hai vectơ", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C5", type: "chapter", description: "CÁC SỐ ĐẶC TRƯNG CỦA MẪU SỐ LIỆU. KHÔNG GHÉP NHÓM", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C51", type: "chapter", description: "Số gần đúng và sai số", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C52", type: "chapter", description: "Các số đặc trưng đo xu thế trung tâm", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C53", type: "chapter", description: "Các số đặc trưng đo độ phân tán", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C6", type: "chapter", description: "HÀM SỐ, ĐỒ THỊ VÀ ỨNG DỤNG", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C61", type: "chapter", description: "Hàm số", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C62", type: "chapter", description: "Hàm số bậc hai", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C63", type: "chapter", description: "Dấu của tam thức bậc hai", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C64", type: "chapter", description: "Phương trình quy về phương trình bậc hai", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C7", type: "chapter", description: "PHƯƠNG PHÁP TOẠ ĐỘ TRONG MẶT PHẲNG", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C71", type: "chapter", description: "Phương trình đường thẳng", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C72", type: "chapter", description: "Đường thẳng trong mặt phẳng toạ độ", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C73", type: "chapter", description: "Đường tròn trong mặt phẳng toạ độ", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C74", type: "chapter", description: "Ba đường conic", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C8", type: "chapter", description: "ĐẠI SỐ TỔ HỢP", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C81", type: "chapter", description: "Quy tắc đếm", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C82", type: "chapter", description: "Hoán vị, chỉnh hợp và tổ hợp", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C83", type: "chapter", description: "Nhị thức Newton", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C9", type: "chapter", description: "TÍNH XÁC SUẤT THEO ĐỊNH NGHĨA CÓ ĐIỂN", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C91", type: "chapter", description: "Biến cố và định nghĩa cổ điển của xác suất", createdAt: new Date(), updatedAt: new Date() },
      { code: "10C92", type: "chapter", description: "Thực hành tính xác suất theo định nghĩa cổ điển", createdAt: new Date(), updatedAt: new Date() },

      { code: "11C1", type: "chapter", description: "HÀM SỐ LƯỢNG GIÁC VÀ PHƯƠNG TRÌNH LƯỢNG GIÁC", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C11", type: "chapter", description: "Giá trị lượng giác của góc lượng giác", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C12", type: "chapter", description: "Công thức lượng giác", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C13", type: "chapter", description: "Hàm số lượng giác", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C14", type: "chapter", description: "Phương trình lượng giác cơ bản", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C2", type: "chapter", description: "DÃY SỐ. CẤP SỐ CỘNG VÀ CẤP SỐ NHÂN", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C21", type: "chapter", description: "Dãy số", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C22", type: "chapter", description: "Cấp số cộng", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C23", type: "chapter", description: "Cấp số nhân", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C3", type: "chapter", description: "CÁC SỐ ĐẶC TRƯNG ĐO XU THẾ TRUNG TÂM CỦA MẪU SỐ LIỆU GHÉP NHÓM", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C31", type: "chapter", description: "Mẫu số liệu ghép nhóm", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C32", type: "chapter", description: "Các số đặc trưng đo xu thế trung tâm", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C4", type: "chapter", description: "QUAN HỆ SONG SONG TRONG KHÔNG GIAN", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C41", type: "chapter", description: "Đường thẳng và mặt phẳng trong không gian", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C42", type: "chapter", description: "Hai đường thẳng song song", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C43", type: "chapter", description: "Đường thẳng và mặt phẳng song song", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C44", type: "chapter", description: "Hai mặt phẳng song song", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C45", type: "chapter", description: "Phép chiếu song song", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C5", type: "chapter", description: "GIỚI HẠN. HÀM SỐ LIÊN TỤC", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C51", type: "chapter", description: "Giới hạn của dãy số", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C52", type: "chapter", description: "Giới hạn của hàm số", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C53", type: "chapter", description: "Hàm số liên tục", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C6", type: "chapter", description: "HÀM SỐ MŨ VÀ HÀM SỐ LÔGARIT", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C61", type: "chapter", description: "Luỹ thừa với số mũ thực", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C62", type: "chapter", description: "Lôgarit", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C63", type: "chapter", description: "Hàm số mũ và hàm số lôgarit", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C64", type: "chapter", description: "Phương trình, bất phương trình mũ và lôgarit", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C7", type: "chapter", description: "QUAN HỆ VUÔNG GÓC TRONG KHÔNG GIAN", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C71", type: "chapter", description: "Hai đường thẳng vuông góc", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C72", type: "chapter", description: "Đường thẳng vuông góc với mặt phẳng", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C73", type: "chapter", description: "Phép chiếu vuông góc. Góc giữa đường thẳng và mặt phẳng", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C74", type: "chapter", description: "Hai mặt phẳng vuông góc", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C75", type: "chapter", description: "Khoảng cách", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C76", type: "chapter", description: "Thể tích", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C8", type: "chapter", description: "CÁC QUY TẮC TÍNH XÁC SUẤT", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C81", type: "chapter", description: "Biến cố hợp, biến cố giao, biến cố độc lập", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C82", type: "chapter", description: "Công thức cộng xác suất", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C83", type: "chapter", description: "Công thức nhân xác suất cho hai biến cố độc lập", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C9", type: "chapter", description: "ĐẠO HÀM", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C91", type: "chapter", description: "Định nghĩa và ý nghĩa của đạo hàm", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C92", type: "chapter", description: "Các quy tắc tính đạo hàm", createdAt: new Date(), updatedAt: new Date() },
      { code: "11C93", type: "chapter", description: "Đạo hàm cấp hai", createdAt: new Date(), updatedAt: new Date() },

      { code: "12C1", type: "chapter", description: "ỨNG DỤNG ĐẠO HÀM ĐỂ KHẢO SÁT VÀ VẼ ĐỒ THỊ HÀM SỐ", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C11", type: "chapter", description: "Tính đơn điệu và cực trị của hàm số", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C12", type: "chapter", description: "Giá trị lớn nhất và giá trị nhỏ nhất của hàm số", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C13", type: "chapter", description: "Đường tiệm cận của đồ thị hàm số", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C14", type: "chapter", description: "Khảo sát sự biến thiên và vẽ đồ thị của hàm số", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C15", type: "chapter", description: "Ứng dụng đạo hàm để giải quyết một số vấn đề liên quan đến thực tiễn", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C2", type: "chapter", description: "VECTƠ VÀ HỆ TRỤC TOẠ ĐỘ TRONG KHÔNG GIAN", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C21", type: "chapter", description: "Vectơ trong không gian", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C22", type: "chapter", description: "Hệ trục toạ độ trong không gian", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C23", type: "chapter", description: "Biểu thức toạ độ của các phép toán vectơ", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C3", type: "chapter", description: "CÁC SỐ ĐẶC TRƯNG ĐO MỨC ĐỘ PHÂN TÁN CỦA MẪU SỐ LIỆU GHÉP NHÓM", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C31", type: "chapter", description: "Khoảng biến thiên và khoảng tứ phân vị", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C32", type: "chapter", description: "Phương sai và độ lệch chuẩn", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C4", type: "chapter", description: "NGUYÊN HÀM VÀ TÍCH PHÂN", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C41", type: "chapter", description: "Nguyên hàm", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C42", type: "chapter", description: "Tích phân", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C43", type: "chapter", description: "Ứng dụng hình học của tích phân", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C5", type: "chapter", description: "PHƯƠNG PHÁP TOẠ ĐỘ TRONG KHÔNG GIAN", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C51", type: "chapter", description: "Phương trình mặt phẳng", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C52", type: "chapter", description: "Phương trình đường thẳng trong không gian", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C53", type: "chapter", description: "Công thức tính góc trong không gian", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C54", type: "chapter", description: "Phương trình mặt cầu", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C6", type: "chapter", description: "XÁC SUẤT CÓ ĐIỀU KIỆN", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C61", type: "chapter", description: "Xác suất có điều kiện", createdAt: new Date(), updatedAt: new Date() },
      { code: "12C62", type: "chapter", description: "Công thức xác suất toàn phần và công thức Bayes", createdAt: new Date(), updatedAt: new Date() },

      // ❓ Question Type
      { code: "TN", type: "question type", description: "Trắc nghiệm", createdAt: new Date(), updatedAt: new Date() },
      { code: "TLN", type: "question type", description: "Trả lời ngắn", createdAt: new Date(), updatedAt: new Date() },
      { code: "DS", type: "question type", description: "Đúng/Sai", createdAt: new Date(), updatedAt: new Date() },

      // 🎯 Difficulty
      { code: "NB", type: "difficulty", description: "Nhận biết", createdAt: new Date(), updatedAt: new Date() },
      { code: "TH", type: "difficulty", description: "Thông hiểu", createdAt: new Date(), updatedAt: new Date() },
      { code: "VD", type: "difficulty", description: "Vận dụng", createdAt: new Date(), updatedAt: new Date() },
      { code: "VDC", type: "difficulty", description: "Vận dụng cao", createdAt: new Date(), updatedAt: new Date() },

      { code: "WS", type: "wait status", description: "Đang chờ duyệt", createdAt: new Date(), updatedAt: new Date() },
      { code: "JS", type: "wait status", description: "Đã vào lớp", createdAt: new Date(), updatedAt: new Date() },

      { code: "EF", type: "cheat type", description: "Thoát chế độ toàn màn hình", createdAt: new Date(), updatedAt: new Date() },
      { code: "TB", type: "cheat type", description: "Chuyển tab", createdAt: new Date(), updatedAt: new Date() },
      { code: "COP", type: "cheat type", description: "Sao chép câu hỏi", createdAt: new Date(), updatedAt: new Date() },
      { code: "SK", type: "cheat type", description: "Kí tự phím tắt lạ", createdAt: new Date(), updatedAt: new Date() },


    ]);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete("allCode", null, {});
  },
};
