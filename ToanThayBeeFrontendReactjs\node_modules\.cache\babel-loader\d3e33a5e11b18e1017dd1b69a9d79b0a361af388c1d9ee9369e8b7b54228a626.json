{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { postExam, setExamData } from \"src/features/addExam/addExamSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeaderStep = () => {\n  _s();\n  const {\n    step\n  } = useSelector(state => state.addExam);\n  if (step === 1) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Header Step 1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this);\n  } else if (step === 2) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Header Step 2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 13\n    }, this);\n  } else {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Header Step 3\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 13\n    }, this);\n  }\n};\n_s(HeaderStep, \"s5rWcbXEHrIRaJo4DtZfiNxygtM=\", false, function () {\n  return [useSelector];\n});\n_c = HeaderStep;\nexport const LeftContent = () => {\n  _s2();\n  const dispatch = useDispatch();\n  const {\n    examData\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 border-r bg-white h-[calc(100vh-69px)] border-gray-300 overflow-y-auto p-4\",\n    children: /*#__PURE__*/_jsxDEV(HeaderStep, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 9\n  }, this);\n};\n_s2(LeftContent, \"rLRAN3FRqlrzGY/YdC5L2ZsGJDw=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c2 = LeftContent;\nexport default LeftContent;\nvar _c, _c2;\n$RefreshReg$(_c, \"HeaderStep\");\n$RefreshReg$(_c2, \"LeftContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "postExam", "setExamData", "jsxDEV", "_jsxDEV", "HeaderStep", "_s", "step", "state", "addExam", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "LeftContent", "_s2", "dispatch", "examData", "className", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { postExam, setExamData } from \"src/features/addExam/addExamSlice\";\r\n\r\nconst HeaderStep = () => {\r\n    const { step } = useSelector((state) => state.addExam);\r\n    if (step === 1) {\r\n        return (\r\n            <div>\r\n                Header Step 1\r\n            </div>\r\n        )\r\n    } else if (step === 2) {\r\n        return (\r\n            <div>\r\n                Header Step 2\r\n            </div>\r\n        )\r\n    } else {\r\n        return (\r\n            <div>\r\n                Header Step 3\r\n            </div>\r\n        )\r\n    }\r\n}\r\n\r\nexport const LeftContent = () => {\r\n    const dispatch = useDispatch();\r\n    const { examData } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <div className=\"flex-1 border-r bg-white h-[calc(100vh-69px)] border-gray-300 overflow-y-auto p-4\">\r\n            <HeaderStep />\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default LeftContent;"], "mappings": ";;;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,WAAW,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAGR,WAAW,CAAES,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EACtD,IAAIF,IAAI,KAAK,CAAC,EAAE;IACZ,oBACIH,OAAA;MAAAM,QAAA,EAAK;IAEL;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEd,CAAC,MAAM,IAAIP,IAAI,KAAK,CAAC,EAAE;IACnB,oBACIH,OAAA;MAAAM,QAAA,EAAK;IAEL;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEd,CAAC,MAAM;IACH,oBACIV,OAAA;MAAAM,QAAA,EAAK;IAEL;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEd;AACJ,CAAC;AAAAR,EAAA,CArBKD,UAAU;EAAA,QACKN,WAAW;AAAA;AAAAgB,EAAA,GAD1BV,UAAU;AAuBhB,OAAO,MAAMW,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB;EAAS,CAAC,GAAGpB,WAAW,CAAES,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE1D,oBACIL,OAAA;IAAKgB,SAAS,EAAC,mFAAmF;IAAAV,QAAA,eAC9FN,OAAA,CAACC,UAAU;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEd,CAAC;AAAAG,GAAA,CATYD,WAAW;EAAA,QACHhB,WAAW,EACPD,WAAW;AAAA;AAAAsB,GAAA,GAFvBL,WAAW;AAWxB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAM,GAAA;AAAAC,YAAA,CAAAP,EAAA;AAAAO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}