{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\RightContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$();\n// Optimized Preview Panel Component\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { Eye, FileText, Image, File } from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\nimport PdfViewer from \"../ViewPdf\";\nimport NavigateBar from \"./NavigateBar\";\nimport * as questionUntil from \"src/utils/question/questionUtils\";\nimport useDebouncedEffect from \"src/hooks/useDebouncedEffect\";\nimport { setQuestions } from \"src/features/addExam/addExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageView = () => {\n  _s();\n  const {\n    examImage\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: examImage ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: URL.createObjectURL(examImage),\n        alt: \"exam\",\n        className: \"w-full object-contain\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col items-center\",\n      children: [/*#__PURE__*/_jsxDEV(Image, {\n        className: \"w-6 h-6 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: \"Ch\\u01B0a c\\xF3 \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n_s(ImageView, \"n7yNOFYIxX43G5cs3TSUaLVZo44=\", false, function () {\n  return [useSelector];\n});\n_c = ImageView;\nconst PdfView = () => {\n  _s2();\n  const {\n    examFile\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: examFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(PdfViewer, {\n        url: URL.createObjectURL(examFile)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col items-center\",\n      children: [/*#__PURE__*/_jsxDEV(File, {\n        className: \"w-6 h-6 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: \"Ch\\u01B0a c\\xF3 file PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n_s2(PdfView, \"Z7mKjY/U71p40B1bogstxoIyP1g=\", false, function () {\n  return [useSelector];\n});\n_c2 = PdfView;\nconst ExamView = () => {\n  _s3();\n  const {\n    examData,\n    examImage,\n    examFile\n  } = useSelector(state => state.addExam);\n  const [view, setView] = useState('image');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      div: true,\n      className: \"mb-3 p-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-bold text-gray-900 mb-2\",\n        children: examData.name || \"Tên đề thi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-1 text-xs text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Ki\\u1EC3u:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 26\n          }, this), \" \", examData.typeOfExam || \"Chưa chọn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"L\\u1EDBp:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 26\n          }, this), \" \", examData.class || \"Chưa chọn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"N\\u0103m:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 26\n          }, this), \" \", examData.year || \"Chưa chọn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this), examData.chapter && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Ch\\u01B0\\u01A1ng:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 47\n          }, this), \" \", examData.chapter]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 42\n        }, this), examData.testDuration && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Th\\u1EDDi gian:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 52\n          }, this), \" \", examData.testDuration, \"p\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 47\n        }, this), examData.passRate && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"\\u0110i\\u1EC3m \\u0111\\u1EA1t:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 48\n          }, this), \" \", examData.passRate, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 43\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this), examData.solutionUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600\",\n          children: [\"Link l\\u1EDDi gi\\u1EA3i: \", examData.solutionUrl]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 25\n      }, this), examData.description && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600\",\n          children: examData.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-1\",\n        children: [examData.public && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full\",\n          children: \"C\\xF4ng khai\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 25\n        }, this), examData.isClassroomExam && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full\",\n          children: \"\\u0110\\u1EC1 thi l\\u1EDBp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 25\n        }, this), !examData.public && !examData.isClassroomExam && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full\",\n          children: \"Ri\\xEAng t\\u01B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(NavigateBar, {\n      list: [{\n        id: 1,\n        name: 'Ảnh đề thi',\n        value: 'image'\n      }, {\n        id: 2,\n        name: 'File đề thi',\n        value: 'pdf'\n      }],\n      active: view,\n      setActive: setView\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this), view === 'image' && /*#__PURE__*/_jsxDEV(ImageView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 34\n    }, this), view === 'pdf' && /*#__PURE__*/_jsxDEV(PdfView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 32\n    }, this)]\n  }, void 0, true);\n};\n_s3(ExamView, \"2rrSv/c6+wbxcLl4f1+uyIHx6fc=\", false, function () {\n  return [useSelector];\n});\n_c3 = ExamView;\nconst QuestionViewHeader = _ref => {\n  let {\n    title,\n    count,\n    noQuestionText\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" p-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-1 mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-3 h-3 text-gray-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-xs font-semibold text-gray-900\",\n        children: [title, \" (\", count, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 13\n    }, this), count === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-4 text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-6 h-6 mx-auto mb-1 opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs\",\n        children: noQuestionText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 9\n  }, this);\n};\n_c4 = QuestionViewHeader;\nconst QuestionTNView = () => {\n  _s4();\n  const {\n    questions\n  } = useSelector(state => state.addExam);\n  const [questionsTN, setQuestionsTN] = useState([]);\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  useEffect(() => {\n    setQuestionsTN(questions.filter(q => q.questionData.typeOfQuestion === 'TN'));\n  }, [questions]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\",\n      count: questionsTN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: questionsTN.map((question, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"text-xs font-bold\",\n          children: [\"C\\xE2u h\\u1ECFi \", index + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          text: question.questionData.content,\n          className: \"text-xs break-words w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-2 mt-2\",\n          children: question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-bold whitespace-nowrap\",\n              children: prefixTN[index]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n              text: statement.content,\n              className: \"text-xs break-words w-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 37\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 25\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s4(QuestionTNView, \"yT3oiaVUU96phRgrGEjkjOWoaAc=\", false, function () {\n  return [useSelector];\n});\n_c5 = QuestionTNView;\nconst QuestionView = () => {\n  _s5();\n  const dispatch = useDispatch();\n  const {\n    questionTNContent,\n    questionDSContent,\n    questionTLNContent,\n    correctAnswerTN,\n    correctAnswerDS,\n    correctAnswerTLN,\n    questions\n  } = useSelector(state => state.addExam);\n  useDebouncedEffect(() => {\n    const questionTN = questionUntil.splitContentTN(questionTNContent, correctAnswerTN, dispatch);\n    const questionDS = questionUntil.splitContentDS(questionDSContent, correctAnswerDS, dispatch);\n    const questionTLN = questionUntil.splitContentTLN(questionTLNContent, correctAnswerTLN, dispatch);\n    if (questionTN && questionDS && questionTLN) {\n      const newQuestions = [...questionTN, ...questionDS, ...questionTLN];\n      dispatch(setQuestions(newQuestions));\n    }\n  }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN], 500);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(QuestionTNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s5(QuestionView, \"obFjEQRK2oDZZJYHzGof534wmDg=\", false, function () {\n  return [useDispatch, useSelector, useDebouncedEffect];\n});\n_c6 = QuestionView;\nconst RightContent = () => {\n  _s6();\n  const {\n    examData\n  } = useSelector(state => state.addExam);\n  const [view, setView] = useState('exam');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_42px)] bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Eye, {\n          className: \"w-3 h-3 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Xem tr\\u01B0\\u1EDBc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded border border-gray-200 \",\n        children: [/*#__PURE__*/_jsxDEV(NavigateBar, {\n          list: [{\n            id: 1,\n            name: 'Đề thi',\n            value: 'exam'\n          }, {\n            id: 2,\n            name: 'Câu hỏi',\n            value: 'question'\n          }],\n          active: view,\n          setActive: setView\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 21\n        }, this), view === 'exam' && /*#__PURE__*/_jsxDEV(ExamView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 41\n        }, this), view === 'question' && /*#__PURE__*/_jsxDEV(QuestionView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 45\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 208,\n    columnNumber: 9\n  }, this);\n};\n_s6(RightContent, \"jr8dYAPmVJTXzPutd/U0S+QPSCE=\", false, function () {\n  return [useSelector];\n});\n_c7 = RightContent;\nexport default RightContent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"ImageView\");\n$RefreshReg$(_c2, \"PdfView\");\n$RefreshReg$(_c3, \"ExamView\");\n$RefreshReg$(_c4, \"QuestionViewHeader\");\n$RefreshReg$(_c5, \"QuestionTNView\");\n$RefreshReg$(_c6, \"QuestionView\");\n$RefreshReg$(_c7, \"RightContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "Eye", "FileText", "Image", "File", "useEffect", "useState", "PdfViewer", "NavigateBar", "questionUntil", "useDebouncedEffect", "setQuestions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageView", "_s", "examImage", "state", "addExam", "children", "className", "src", "URL", "createObjectURL", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PdfView", "_s2", "examFile", "url", "_c2", "<PERSON><PERSON><PERSON>ie<PERSON>", "_s3", "examData", "view", "<PERSON><PERSON><PERSON><PERSON>", "div", "name", "typeOfExam", "class", "year", "chapter", "testDuration", "passRate", "solutionUrl", "description", "public", "isClassroomExam", "list", "id", "value", "active", "setActive", "_c3", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "title", "count", "noQuestionText", "_c4", "QuestionTNView", "_s4", "questions", "questionsTN", "setQuestionsTN", "prefixTN", "filter", "q", "questionData", "typeOfQuestion", "length", "map", "question", "index", "text", "content", "statements", "statement", "_c5", "Question<PERSON>iew", "_s5", "dispatch", "questionT<PERSON>ontent", "question<PERSON><PERSON><PERSON><PERSON>", "questionTLNContent", "correctAnswerTN", "correctAnswerDS", "correctAnswerTLN", "questionTN", "splitContentTN", "questionDS", "splitContentDS", "questionTLN", "splitContentTLN", "newQuestions", "_c6", "RightContent", "_s6", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/RightContent.jsx"], "sourcesContent": ["// Optimized Preview Panel Component\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { Eye, FileText, Image, File } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport PdfViewer from \"../ViewPdf\";\r\nimport NavigateBar from \"./NavigateBar\";\r\nimport * as questionUntil from \"src/utils/question/questionUtils\";\r\nimport useDebouncedEffect from \"src/hooks/useDebouncedEffect\";\r\nimport { setQuestions } from \"src/features/addExam/addExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\n\r\nconst ImageView = () => {\r\n    const { examImage } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <>\r\n            {examImage ? (\r\n                <div className=\"p-3\">\r\n                    <img src={URL.createObjectURL(examImage)} alt=\"exam\" className=\"w-full object-contain\" />\r\n                </div>\r\n            ) : (\r\n                <div className=\"p-3 flex flex-col items-center\">\r\n                    <Image className=\"w-6 h-6 text-gray-400\" />\r\n                    <p className=\"text-sm text-gray-500\">Ch<PERSON>a c<PERSON></p>\r\n                </div>\r\n            )}\r\n        </>\r\n    )\r\n}\r\n\r\nconst PdfView = () => {\r\n    const { examFile } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <>\r\n            {examFile ? (\r\n                <div className=\"p-3\">\r\n                    <PdfViewer url={URL.createObjectURL(examFile)} />\r\n                </div>\r\n            ) : (\r\n                <div className=\"p-3 flex flex-col items-center\">\r\n                    <File className=\"w-6 h-6 text-gray-400\" />\r\n                    <p className=\"text-sm text-gray-500\">Chưa có file PDF</p>\r\n                </div>\r\n            )}\r\n        </>\r\n    )\r\n}\r\n\r\nconst ExamView = () => {\r\n    const { examData, examImage, examFile } = useSelector((state) => state.addExam);\r\n    const [view, setView] = useState('image');\r\n\r\n    return (\r\n        <>\r\n            {/* Compact Exam Header */}\r\n            <div div className=\"mb-3 p-3\" >\r\n                <h3 className=\"text-sm font-bold text-gray-900 mb-2\">\r\n                    {examData.name || \"Tên đề thi\"}\r\n                </h3>\r\n                <div className=\"grid grid-cols-2 gap-1 text-xs text-gray-600\">\r\n                    <div><span className=\"font-medium\">Kiểu:</span> {examData.typeOfExam || \"Chưa chọn\"}</div>\r\n                    <div><span className=\"font-medium\">Lớp:</span> {examData.class || \"Chưa chọn\"}</div>\r\n                    <div><span className=\"font-medium\">Năm:</span> {examData.year || \"Chưa chọn\"}</div>\r\n                    {examData.chapter && <div><span className=\"font-medium\">Chương:</span> {examData.chapter}</div>}\r\n                    {examData.testDuration && <div><span className=\"font-medium\">Thời gian:</span> {examData.testDuration}p</div>}\r\n                    {examData.passRate && <div><span className=\"font-medium\">Điểm đạt:</span> {examData.passRate}%</div>}\r\n                </div>\r\n                {\r\n                    examData.solutionUrl && (\r\n                        <div className=\"mt-2\">\r\n                            <p className=\"text-xs text-gray-600\">Link lời giải: {examData.solutionUrl}</p>\r\n                        </div>\r\n                    )\r\n                }\r\n                {\r\n                    examData.description && (\r\n                        <div className=\"mt-2\">\r\n                            <p className=\"text-xs text-gray-600\">{examData.description}</p>\r\n                        </div>\r\n                    )\r\n                }\r\n            </div>\r\n\r\n            {/* Compact Status Badges */}\r\n            <div className=\"p-3\" >\r\n                <div className=\"flex flex-wrap gap-1\">\r\n                    {examData.public && (\r\n                        <span className=\"px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\r\n                            Công khai\r\n                        </span>\r\n                    )}\r\n                    {examData.isClassroomExam && (\r\n                        <span className=\"px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full\">\r\n                            Đề thi lớp\r\n                        </span>\r\n                    )}\r\n                    {!examData.public && !examData.isClassroomExam && (\r\n                        <span className=\"px-2 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full\">\r\n                            Riêng tư\r\n                        </span>\r\n                    )}\r\n                </div>\r\n            </div>\r\n            <NavigateBar\r\n                list={[{\r\n                    id: 1,\r\n                    name: 'Ảnh đề thi',\r\n                    value: 'image'\r\n                },\r\n                {\r\n                    id: 2,\r\n                    name: 'File đề thi',\r\n                    value: 'pdf'\r\n                }\r\n                ]}\r\n                active={view}\r\n                setActive={setView}\r\n            />\r\n            {view === 'image' && <ImageView />}\r\n            {view === 'pdf' && <PdfView />}\r\n\r\n        </>\r\n    )\r\n}\r\nconst QuestionViewHeader = ({ title, count, noQuestionText }) => {\r\n    return (\r\n        <div className=\" p-3 \">\r\n            <div className=\"flex items-center gap-1 mb-2\">\r\n                <FileText className=\"w-3 h-3 text-gray-600\" />\r\n                <h4 className=\"text-xs font-semibold text-gray-900\">{title} ({count})</h4>\r\n            </div>\r\n            {count === 0 && (\r\n                <div className=\"text-center py-4 text-gray-500\">\r\n                    <FileText className=\"w-6 h-6 mx-auto mb-1 opacity-50\" />\r\n                    <p className=\"text-xs\">{noQuestionText}</p>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst QuestionTNView = () => {\r\n    const { questions } = useSelector((state) => state.addExam);\r\n    const [questionsTN, setQuestionsTN] = useState([]);\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n\r\n    useEffect(() => {\r\n        setQuestionsTN(questions.filter(q => q.questionData.typeOfQuestion === 'TN'));\r\n    }, [questions]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trắc nghiệm\" count={questionsTN.length} noQuestionText=\"Chưa có câu hỏi trắc nghiệm\" />\r\n            <div className=\"p-3\">\r\n                {questionsTN.map((question, index) => (\r\n                    <div key={index} className=\"mb-4\">\r\n                        <h5 className=\"text-xs font-bold\">Câu hỏi {index + 1}</h5>\r\n                        <LatexRenderer text={question.questionData.content} className=\"text-xs break-words w-full\" />\r\n                        <div className=\"flex flex-col gap-2 mt-2\">\r\n                            {question.statements.map((statement, index) => (\r\n                                <div key={index} className=\"flex items-center gap-1\">\r\n                                    <p className=\"text-sm font-bold whitespace-nowrap\">\r\n                                        {prefixTN[index]}\r\n                                    </p>\r\n                                    <LatexRenderer text={statement.content} className=\"text-xs break-words w-full\" />\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionView = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionTNContent, questionDSContent, questionTLNContent, correctAnswerTN, correctAnswerDS, correctAnswerTLN, questions } = useSelector((state) => state.addExam);\r\n\r\n    useDebouncedEffect(() => {\r\n        const questionTN = questionUntil.splitContentTN(questionTNContent, correctAnswerTN, dispatch);\r\n        const questionDS = questionUntil.splitContentDS(questionDSContent, correctAnswerDS, dispatch);\r\n        const questionTLN = questionUntil.splitContentTLN(questionTLNContent, correctAnswerTLN, dispatch);\r\n\r\n        if (questionTN && questionDS && questionTLN) {\r\n            const newQuestions = [...questionTN, ...questionDS, ...questionTLN];\r\n            dispatch(setQuestions(newQuestions));\r\n        }\r\n\r\n    }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN], 500)\r\n\r\n    return (\r\n        <>\r\n            <QuestionTNView />\r\n\r\n\r\n        </>\r\n    )\r\n}\r\n\r\nconst RightContent = () => {\r\n    const { examData } = useSelector((state) => state.addExam);\r\n    const [view, setView] = useState('exam');\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_42px)] bg-gray-50\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <Eye className=\"w-3 h-3 text-gray-600\" />\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Xem trước</h2>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Scrollable Preview Content */}\r\n            <div className=\"flex-1 overflow-y-auto p-3\">\r\n                <div className=\"bg-white rounded border border-gray-200 \">\r\n                    <NavigateBar\r\n                        list={[{\r\n                            id: 1,\r\n                            name: 'Đề thi',\r\n                            value: 'exam'\r\n                        },\r\n                        {\r\n                            id: 2,\r\n                            name: 'Câu hỏi',\r\n                            value: 'question'\r\n                        }\r\n                        ]}\r\n                        active={view}\r\n                        setActive={setView}\r\n                    />\r\n                    {view === 'exam' && <ExamView />}\r\n                    {view === 'question' && <QuestionView />}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\n\r\nexport default RightContent;"], "mappings": ";;;;;;;AAAA;AACA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AACzD,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,KAAKC,aAAa,MAAM,kCAAkC;AACjE,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,SAASC,YAAY,QAAQ,mCAAmC;AAChE,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC;EAAU,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE3D,oBACIP,OAAA,CAAAE,SAAA;IAAAM,QAAA,EACKH,SAAS,gBACNL,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,eAChBR,OAAA;QAAKU,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACP,SAAS,CAAE;QAACQ,GAAG,EAAC,MAAM;QAACJ,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxF,CAAC,gBAENjB,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAC3CR,OAAA,CAACX,KAAK;QAACoB,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CjB,OAAA;QAAGS,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAC;MAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD;EACR,gBACH,CAAC;AAEX,CAAC;AAAAb,EAAA,CAjBKD,SAAS;EAAA,QACWlB,WAAW;AAAA;AAAAiC,EAAA,GAD/Bf,SAAS;AAmBf,MAAMgB,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAClB,MAAM;IAAEC;EAAS,CAAC,GAAGpC,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE1D,oBACIP,OAAA,CAAAE,SAAA;IAAAM,QAAA,EACKa,QAAQ,gBACLrB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,eAChBR,OAAA,CAACP,SAAS;QAAC6B,GAAG,EAAEX,GAAG,CAACC,eAAe,CAACS,QAAQ;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,gBAENjB,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAC3CR,OAAA,CAACV,IAAI;QAACmB,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CjB,OAAA;QAAGS,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAC;MAAgB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EACR,gBACH,CAAC;AAEX,CAAC;AAAAG,GAAA,CAjBKD,OAAO;EAAA,QACYlC,WAAW;AAAA;AAAAsC,GAAA,GAD9BJ,OAAO;AAmBb,MAAMK,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACnB,MAAM;IAAEC,QAAQ;IAAErB,SAAS;IAAEgB;EAAS,CAAC,GAAGpC,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC/E,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGpC,QAAQ,CAAC,OAAO,CAAC;EAEzC,oBACIQ,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBAEIR,OAAA;MAAK6B,GAAG;MAACpB,SAAS,EAAC,UAAU;MAAAD,QAAA,gBACzBR,OAAA;QAAIS,SAAS,EAAC,sCAAsC;QAAAD,QAAA,EAC/CkB,QAAQ,CAACI,IAAI,IAAI;MAAY;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACLjB,OAAA;QAAKS,SAAS,EAAC,8CAA8C;QAAAD,QAAA,gBACzDR,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACK,UAAU,IAAI,WAAW;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1FjB,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACM,KAAK,IAAI,WAAW;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFjB,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACO,IAAI,IAAI,WAAW;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAClFS,QAAQ,CAACQ,OAAO,iBAAIlC,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAO;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACQ,OAAO;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC9FS,QAAQ,CAACS,YAAY,iBAAInC,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAU;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACS,YAAY,EAAC,GAAC;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC5GS,QAAQ,CAACU,QAAQ,iBAAIpC,OAAA;UAAAQ,QAAA,gBAAKR,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACS,QAAQ,CAACU,QAAQ,EAAC,GAAC;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,EAEFS,QAAQ,CAACW,WAAW,iBAChBrC,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAD,QAAA,eACjBR,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAD,QAAA,GAAC,2BAAe,EAACkB,QAAQ,CAACW,WAAW;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CACR,EAGDS,QAAQ,CAACY,WAAW,iBAChBtC,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAD,QAAA,eACjBR,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAEkB,QAAQ,CAACY;QAAW;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEJ,CAAC,eAGNjB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,eAChBR,OAAA;QAAKS,SAAS,EAAC,sBAAsB;QAAAD,QAAA,GAChCkB,QAAQ,CAACa,MAAM,iBACZvC,OAAA;UAAMS,SAAS,EAAC,0EAA0E;UAAAD,QAAA,EAAC;QAE3F;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACAS,QAAQ,CAACc,eAAe,iBACrBxC,OAAA;UAAMS,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EAAC;QAEzF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAACS,QAAQ,CAACa,MAAM,IAAI,CAACb,QAAQ,CAACc,eAAe,iBAC1CxC,OAAA;UAAMS,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EAAC;QAEzF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNjB,OAAA,CAACN,WAAW;MACR+C,IAAI,EAAE,CAAC;QACHC,EAAE,EAAE,CAAC;QACLZ,IAAI,EAAE,YAAY;QAClBa,KAAK,EAAE;MACX,CAAC,EACD;QACID,EAAE,EAAE,CAAC;QACLZ,IAAI,EAAE,aAAa;QACnBa,KAAK,EAAE;MACX,CAAC,CACC;MACFC,MAAM,EAAEjB,IAAK;MACbkB,SAAS,EAAEjB;IAAQ;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,EACDU,IAAI,KAAK,OAAO,iBAAI3B,OAAA,CAACG,SAAS;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACjCU,IAAI,KAAK,KAAK,iBAAI3B,OAAA,CAACmB,OAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAEhC,CAAC;AAEX,CAAC;AAAAQ,GAAA,CA3EKD,QAAQ;EAAA,QACgCvC,WAAW;AAAA;AAAA6D,GAAA,GADnDtB,QAAQ;AA4Ed,MAAMuB,kBAAkB,GAAGC,IAAA,IAAsC;EAAA,IAArC;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAe,CAAC,GAAAH,IAAA;EACxD,oBACIhD,OAAA;IAAKS,SAAS,EAAC,OAAO;IAAAD,QAAA,gBAClBR,OAAA;MAAKS,SAAS,EAAC,8BAA8B;MAAAD,QAAA,gBACzCR,OAAA,CAACZ,QAAQ;QAACqB,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9CjB,OAAA;QAAIS,SAAS,EAAC,qCAAqC;QAAAD,QAAA,GAAEyC,KAAK,EAAC,IAAE,EAACC,KAAK,EAAC,GAAC;MAAA;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,EACLiC,KAAK,KAAK,CAAC,iBACRlD,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAC3CR,OAAA,CAACZ,QAAQ;QAACqB,SAAS,EAAC;MAAiC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDjB,OAAA;QAAGS,SAAS,EAAC,SAAS;QAAAD,QAAA,EAAE2C;MAAc;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAmC,GAAA,GAfKL,kBAAkB;AAiBxB,MAAMM,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAEC;EAAU,CAAC,GAAGtE,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC3D,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMkE,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAE7EnE,SAAS,CAAC,MAAM;IACZkE,cAAc,CAACF,SAAS,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,CAACC,cAAc,KAAK,IAAI,CAAC,CAAC;EACjF,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;EAEf,oBACIvD,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBACIR,OAAA,CAAC+C,kBAAkB;MAACE,KAAK,EAAC,uCAAqB;MAACC,KAAK,EAAEM,WAAW,CAACO,MAAO;MAACZ,cAAc,EAAC;IAA6B;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1HjB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,EACfgD,WAAW,CAACQ,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC7BlE,OAAA;QAAiBS,SAAS,EAAC,MAAM;QAAAD,QAAA,gBAC7BR,OAAA;UAAIS,SAAS,EAAC,mBAAmB;UAAAD,QAAA,GAAC,kBAAQ,EAAC0D,KAAK,GAAG,CAAC;QAAA;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1DjB,OAAA,CAACF,aAAa;UAACqE,IAAI,EAAEF,QAAQ,CAACJ,YAAY,CAACO,OAAQ;UAAC3D,SAAS,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7FjB,OAAA;UAAKS,SAAS,EAAC,0BAA0B;UAAAD,QAAA,EACpCyD,QAAQ,CAACI,UAAU,CAACL,GAAG,CAAC,CAACM,SAAS,EAAEJ,KAAK,kBACtClE,OAAA;YAAiBS,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBAChDR,OAAA;cAAGS,SAAS,EAAC,qCAAqC;cAAAD,QAAA,EAC7CkD,QAAQ,CAACQ,KAAK;YAAC;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACJjB,OAAA,CAACF,aAAa;cAACqE,IAAI,EAAEG,SAAS,CAACF,OAAQ;cAAC3D,SAAS,EAAC;YAA4B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAJ3EiD,KAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKV,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAZAiD,KAAK;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaV,CAER;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAqC,GAAA,CAjCKD,cAAc;EAAA,QACMpE,WAAW;AAAA;AAAAsF,GAAA,GAD/BlB,cAAc;AAmCpB,MAAMmB,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAMC,QAAQ,GAAGxF,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyF,iBAAiB;IAAEC,iBAAiB;IAAEC,kBAAkB;IAAEC,eAAe;IAAEC,eAAe;IAAEC,gBAAgB;IAAEzB;EAAU,CAAC,GAAGtE,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEzKX,kBAAkB,CAAC,MAAM;IACrB,MAAMqF,UAAU,GAAGtF,aAAa,CAACuF,cAAc,CAACP,iBAAiB,EAAEG,eAAe,EAAEJ,QAAQ,CAAC;IAC7F,MAAMS,UAAU,GAAGxF,aAAa,CAACyF,cAAc,CAACR,iBAAiB,EAAEG,eAAe,EAAEL,QAAQ,CAAC;IAC7F,MAAMW,WAAW,GAAG1F,aAAa,CAAC2F,eAAe,CAACT,kBAAkB,EAAEG,gBAAgB,EAAEN,QAAQ,CAAC;IAEjG,IAAIO,UAAU,IAAIE,UAAU,IAAIE,WAAW,EAAE;MACzC,MAAME,YAAY,GAAG,CAAC,GAAGN,UAAU,EAAE,GAAGE,UAAU,EAAE,GAAGE,WAAW,CAAC;MACnEX,QAAQ,CAAC7E,YAAY,CAAC0F,YAAY,CAAC,CAAC;IACxC;EAEJ,CAAC,EAAE,CAACZ,iBAAiB,EAAEG,eAAe,EAAEF,iBAAiB,EAAEG,eAAe,EAAEF,kBAAkB,EAAEG,gBAAgB,CAAC,EAAE,GAAG,CAAC;EAEvH,oBACIhF,OAAA,CAAAE,SAAA;IAAAM,QAAA,eACIR,OAAA,CAACqD,cAAc;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC,gBAGpB,CAAC;AAEX,CAAC;AAAAwD,GAAA,CAvBKD,YAAY;EAAA,QACGtF,WAAW,EACwGD,WAAW,EAE/IW,kBAAkB;AAAA;AAAA4F,GAAA,GAJhBhB,YAAY;AAyBlB,MAAMiB,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM;IAAEhE;EAAS,CAAC,GAAGzC,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC1D,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGpC,QAAQ,CAAC,MAAM,CAAC;EAExC,oBACIQ,OAAA;IAAKS,SAAS,EAAC,iDAAiD;IAAAD,QAAA,gBAE5DR,OAAA;MAAKS,SAAS,EAAC,kFAAkF;MAAAD,QAAA,eAC7FR,OAAA;QAAKS,SAAS,EAAC,yBAAyB;QAAAD,QAAA,gBACpCR,OAAA,CAACb,GAAG;UAACsB,SAAS,EAAC;QAAuB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCjB,OAAA;UAAIS,SAAS,EAAC,qCAAqC;UAAAD,QAAA,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjB,OAAA;MAAKS,SAAS,EAAC,4BAA4B;MAAAD,QAAA,eACvCR,OAAA;QAAKS,SAAS,EAAC,0CAA0C;QAAAD,QAAA,gBACrDR,OAAA,CAACN,WAAW;UACR+C,IAAI,EAAE,CAAC;YACHC,EAAE,EAAE,CAAC;YACLZ,IAAI,EAAE,QAAQ;YACda,KAAK,EAAE;UACX,CAAC,EACD;YACID,EAAE,EAAE,CAAC;YACLZ,IAAI,EAAE,SAAS;YACfa,KAAK,EAAE;UACX,CAAC,CACC;UACFC,MAAM,EAAEjB,IAAK;UACbkB,SAAS,EAAEjB;QAAQ;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,EACDU,IAAI,KAAK,MAAM,iBAAI3B,OAAA,CAACwB,QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/BU,IAAI,KAAK,UAAU,iBAAI3B,OAAA,CAACwE,YAAY;UAAA1D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACyE,GAAA,CAtCID,YAAY;EAAA,QACOxG,WAAW;AAAA;AAAA0G,GAAA,GAD9BF,YAAY;AAyClB,eAAeA,YAAY;AAAC,IAAAvE,EAAA,EAAAK,GAAA,EAAAuB,GAAA,EAAAM,GAAA,EAAAmB,GAAA,EAAAiB,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAA1E,EAAA;AAAA0E,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}