{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\bar\\\\FunctionBarAdmin.jsx\",\n  _s = $RefreshSig$();\nimport ButtonFunctionBarAdmin from \"../button/ButtonFunctionBarAdmin\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useState, useEffect, useRef } from \"react\";\nimport Pagination from \"../Pagination\";\nimport FilterBar from \"./FilterBar\";\nimport { FileSpreadsheet } from \"lucide-react\";\nimport { setIsAddView, setIsFilterView } from \"src/features/filter/filterSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FunctionBarAdmin = _ref => {\n  _s();\n  let {\n    pagination = true,\n    isSearch = true,\n    showExportExcel = false,\n    showFilter = false,\n    limit = 10,\n    // Mặc định limit là 10\n    currentPage = 1,\n    // Mặc định trang hiện tại là 1\n    totalPages = 1,\n    // Mặc định tổng số trang là 1\n    totalItems = 0,\n    // Mặc định tổng số mục là 0\n    setLimit = () => {},\n    // Hàm callback để cập nhật limit\n    setCurrentPage = () => {},\n    // Hàm callback để cập nhật trang hiện tại\n    setSearch = () => {},\n    // Hàm callback để cập nhật từ khóa tìm kiếm\n    handleExportToExcel = () => {},\n    // Hàm callback để xuất Excel\n    handleAddExam = () => {} // Hàm callback để thêm đề thi\n  } = _ref;\n  const dispatch = useDispatch();\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n  const [isDropdownOpenPage, setIsDropdownOpenPage] = useState(false);\n  const dropdownRef = useRef(null);\n  const [inputValue, setInputValue] = useState(\"\");\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsDropdownOpen(false);\n        setIsDropdownOpenPage(false);\n      }\n    };\n\n    // Lắng nghe sự kiện click trên document\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  const options = [5, 10, 15, 20, 30];\n  // const optionsPage = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\n  const optionsPage = Array.from({\n    length: totalPages\n  }, (_, i) => i + 1);\n  const handleSelectLimit = newLimit => {\n    setLimit(newLimit); // Cập nhật limit trong Redux\n    setIsDropdownOpen(false); // Đóng dropdown sau khi chọn\n  };\n  const handlePageChange = newPage => {\n    setCurrentPage(newPage); // Cập nhật trang trong Redux\n    setIsDropdownOpenPage(false); // Đóng dropdown sau khi chọn\n  };\n\n  // Handle export to Excel\n\n  useEffect(() => {\n    const delayDebounceFn = setTimeout(() => {\n      setSearch(inputValue);\n    }, 1000);\n    return () => clearTimeout(delayDebounceFn); // Cleanup timeout nếu user tiếp tục nhập\n  }, [inputValue, dispatch]);\n  const iconAdd = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M12 4L12 20M20 12L4 12\",\n        stroke: \"#202325\",\n        strokeWidth: \"1.5\",\n        strokeLinecap: \"round\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 9\n  }, this);\n  const iconFilter = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M4.5 7H19.5M7 12H17M10 17H14\",\n        stroke: \"#202325\",\n        strokeWidth: \"1.5\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 9\n  }, this);\n  const iconExport = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M20 14.6667V17C20 18.6569 18.6569 20 17 20H7C5.34315 20 4.00001 18.6569 4.00001 17L4 14.6667M7.55556 10.2222L12 14.6667M12 14.6667L16.4444 10.2222M12 14.6667V4\",\n        stroke: \"#202325\",\n        strokeWidth: \"1.5\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full space-y-3 pb-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col lg:flex-row lg:items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 min-w-0\",\n        children: isSearch && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex-1 max-w-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 16 16\",\n            fill: \"none\",\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\",\n              stroke: \"currentColor\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"T\\xECm ki\\u1EBFm h\\u1ECDc sinh...\",\n            value: inputValue,\n            onChange: e => setInputValue(e.target.value),\n            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 flex-shrink-0\",\n        children: [showFilter && /*#__PURE__*/_jsxDEV(FilterBar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 36\n        }, this), showExportExcel && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleExportToExcel,\n          className: \"flex items-center gap-1.5 px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors whitespace-nowrap\",\n          title: \"Xu\\u1EA5t Excel\",\n          children: [/*#__PURE__*/_jsxDEV(FileSpreadsheet, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"hidden sm:inline\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n          icon: iconAdd,\n          text: 'Thêm mới',\n          onClick: () => dispatch(setIsAddView(true))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this), pagination && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col justify-end sm:flex-row sm:items-center gap-3 pt-2 border-t border-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 text-sm text-gray-700\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"limitSelect\",\n          className: \"font-medium whitespace-nowrap\",\n          children: \"Hi\\u1EC3n th\\u1ECB:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"limitSelect\",\n            value: limit,\n            onChange: e => handleSelectLimit(Number(e.target.value)),\n            className: \"appearance-none bg-white border border-gray-300 rounded-md px-3 py-1.5 pr-8 text-sm focus:outline-none focus:ring-1 focus:ring-sky-500 focus:border-sky-500\",\n            children: options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: option,\n              children: option\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-3 h-3 text-gray-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M19 9l-7 7-7-7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-500 whitespace-nowrap\",\n          children: \"/ trang\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-end\",\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          currentPage: currentPage,\n          totalItems: totalItems,\n          limit: limit,\n          onPageChange: handlePageChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 9\n  }, this);\n};\n_s(FunctionBarAdmin, \"sFq+uyLAu4DfHGyvAD8oY/GdCwE=\", false, function () {\n  return [useDispatch];\n});\n_c = FunctionBarAdmin;\nexport default FunctionBarAdmin;\nvar _c;\n$RefreshReg$(_c, \"FunctionBarAdmin\");", "map": {"version": 3, "names": ["ButtonFunctionBarAdmin", "useSelector", "useDispatch", "useState", "useEffect", "useRef", "Pagination", "FilterBar", "FileSpreadsheet", "setIsAddView", "setIsFilterView", "jsxDEV", "_jsxDEV", "FunctionBarAdmin", "_ref", "_s", "pagination", "isSearch", "showExportExcel", "showFilter", "limit", "currentPage", "totalPages", "totalItems", "setLimit", "setCurrentPage", "setSearch", "handleExportToExcel", "handleAddExam", "dispatch", "isDropdownOpen", "setIsDropdownOpen", "isDropdownOpenPage", "setIsDropdownOpenPage", "dropdownRef", "inputValue", "setInputValue", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "options", "optionsPage", "Array", "from", "length", "_", "i", "handleSelectLimit", "newLimit", "handlePageChange", "newPage", "delayDebounceFn", "setTimeout", "clearTimeout", "iconAdd", "className", "children", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "iconFilter", "strokeLinejoin", "iconExport", "type", "placeholder", "value", "onChange", "e", "onClick", "title", "icon", "text", "htmlFor", "id", "Number", "map", "option", "onPageChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/bar/FunctionBarAdmin.jsx"], "sourcesContent": ["import ButtonFunctionBarAdmin from \"../button/ButtonFunctionBarAdmin\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport Pagination from \"../Pagination\";\r\nimport FilterBar from \"./FilterBar\";\r\nimport { FileSpreadsheet } from \"lucide-react\";\r\nimport { setIsAddView, setIsFilterView } from \"src/features/filter/filterSlice\";\r\n\r\nconst FunctionBarAdmin = ({\r\n    pagination = true,\r\n    isSearch = true,\r\n    showExportExcel = false,\r\n    showFilter = false,\r\n    limit = 10, // Mặc định limit là 10\r\n    currentPage = 1, // Mặc định trang hiện tại là 1\r\n    totalPages = 1, // Mặc định tổng số trang là 1\r\n    totalItems = 0, // Mặc định tổng số mục là 0\r\n    setLimit = () => {}, // Hàm callback để cập nhật limit\r\n    setCurrentPage = () => {}, // Hàm callback để cập nhật trang hiện tại\r\n    setSearch = () => {}, // Hàm callback để cập nhật từ khóa tìm kiếm\r\n    handleExportToExcel = () => {}, // Hàm callback để xuất Excel\r\n    handleAddExam = () => {}, // Hàm callback để thêm đề thi\r\n}) => {\r\n    const dispatch = useDispatch();\r\n    const [isDropdownOpen, setIsDropdownOpen] = useState(false);\r\n    const [isDropdownOpenPage, setIsDropdownOpenPage] = useState(false);\r\n    const dropdownRef = useRef(null);\r\n    const [inputValue, setInputValue] = useState(\"\");\r\n\r\n    useEffect(() => {\r\n        const handleClickOutside = (event) => {\r\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n                setIsDropdownOpen(false);\r\n                setIsDropdownOpenPage(false);\r\n            }\r\n        };\r\n\r\n        // Lắng nghe sự kiện click trên document\r\n        document.addEventListener(\"mousedown\", handleClickOutside);\r\n        return () => {\r\n            document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        };\r\n    }, []);\r\n\r\n\r\n    const options = [5, 10, 15, 20, 30];\r\n    // const optionsPage = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\r\n    const optionsPage = Array.from({ length: totalPages }, (_, i) => i + 1);\r\n\r\n    const handleSelectLimit = (newLimit) => {\r\n        setLimit(newLimit); // Cập nhật limit trong Redux\r\n        setIsDropdownOpen(false); // Đóng dropdown sau khi chọn\r\n    };\r\n\r\n    const handlePageChange = (newPage) => {\r\n        setCurrentPage(newPage); // Cập nhật trang trong Redux\r\n        setIsDropdownOpenPage(false); // Đóng dropdown sau khi chọn\r\n    };\r\n\r\n    // Handle export to Excel\r\n\r\n\r\n    useEffect(() => {\r\n        const delayDebounceFn = setTimeout(() => {\r\n            setSearch(inputValue);\r\n        }, 1000);\r\n\r\n        return () => clearTimeout(delayDebounceFn); // Cleanup timeout nếu user tiếp tục nhập\r\n    }, [inputValue, dispatch]);\r\n\r\n    const iconAdd = (\r\n        <div data-svg-wrapper className=\"relative\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path d=\"M12 4L12 20M20 12L4 12\" stroke=\"#202325\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\r\n            </svg>\r\n        </div>\r\n    )\r\n\r\n    const iconFilter = (\r\n        <div data-svg-wrapper className=\"relative\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path d=\"M4.5 7H19.5M7 12H17M10 17H14\" stroke=\"#202325\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n            </svg>\r\n        </div>\r\n    )\r\n\r\n    const iconExport = (\r\n        <div data-svg-wrapper className=\"relative\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path d=\"M20 14.6667V17C20 18.6569 18.6569 20 17 20H7C5.34315 20 4.00001 18.6569 4.00001 17L4 14.6667M7.55556 10.2222L12 14.6667M12 14.6667L16.4444 10.2222M12 14.6667V4\" stroke=\"#202325\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n            </svg>\r\n        </div>\r\n    )\r\n\r\n    return (\r\n        <div className=\"w-full space-y-3 pb-4\">\r\n            {/* Main Action Bar */}\r\n            <div className=\"flex flex-col lg:flex-row lg:items-center gap-3\">\r\n                {/* Left Side: Search */}\r\n                <div className=\"flex items-center gap-3 min-w-0\">\r\n                    {isSearch && (\r\n                        <div className=\"relative flex-1 max-w-sm\">\r\n                            <svg\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                width=\"16\"\r\n                                height=\"16\"\r\n                                viewBox=\"0 0 16 16\"\r\n                                fill=\"none\"\r\n                                className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\r\n                            >\r\n                                <path\r\n                                    d=\"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\"\r\n                                    stroke=\"currentColor\"\r\n                                    strokeLinecap=\"round\"\r\n                                    strokeLinejoin=\"round\"\r\n                                />\r\n                            </svg>\r\n                            <input\r\n                                type=\"text\"\r\n                                placeholder=\"Tìm kiếm học sinh...\"\r\n                                value={inputValue}\r\n                                onChange={(e) => setInputValue(e.target.value)}\r\n                                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500\"\r\n                            />\r\n                        </div>\r\n                    )}\r\n                </div>\r\n\r\n                {/* Right Side: Action Buttons */}\r\n                <div className=\"flex items-center gap-2 flex-shrink-0\">\r\n                    {showFilter && <FilterBar />}\r\n\r\n                    {showExportExcel && (\r\n                        <button\r\n                            onClick={handleExportToExcel}\r\n                            className=\"flex items-center gap-1.5 px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors whitespace-nowrap\"\r\n                            title=\"Xuất Excel\"\r\n                        >\r\n                            <FileSpreadsheet className=\"w-4 h-4\" />\r\n                            <span className=\"hidden sm:inline\">Excel</span>\r\n                        </button>\r\n                    )}\r\n\r\n                    <ButtonFunctionBarAdmin\r\n                        icon={iconAdd}\r\n                        text={'Thêm mới'}\r\n                        onClick={() => dispatch(setIsAddView(true))}\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Pagination Controls */}\r\n            {pagination && (\r\n                <div className=\"flex flex-col justify-end sm:flex-row sm:items-center gap-3 pt-2 border-t border-gray-100\">\r\n                    {/* Left: Items per page */}\r\n                    <div className=\"flex items-center gap-2 text-sm text-gray-700\">\r\n                        <label htmlFor=\"limitSelect\" className=\"font-medium whitespace-nowrap\">\r\n                            Hiển thị:\r\n                        </label>\r\n                        <div className=\"relative\">\r\n                            <select\r\n                                id=\"limitSelect\"\r\n                                value={limit}\r\n                                onChange={(e) => handleSelectLimit(Number(e.target.value))}\r\n                                className=\"appearance-none bg-white border border-gray-300 rounded-md px-3 py-1.5 pr-8 text-sm focus:outline-none focus:ring-1 focus:ring-sky-500 focus:border-sky-500\"\r\n                            >\r\n                                {options.map((option) => (\r\n                                    <option key={option} value={option}>\r\n                                        {option}\r\n                                    </option>\r\n                                ))}\r\n                            </select>\r\n                            <div className=\"absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none\">\r\n                                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 9l-7 7-7-7\" />\r\n                                </svg>\r\n                            </div>\r\n                        </div>\r\n                        <span className=\"text-gray-500 whitespace-nowrap\">/ trang</span>\r\n                    </div>\r\n\r\n                    {/* Right: Pagination */}\r\n                    {/* {totalItems > limit && ( */}\r\n                        <div className=\"flex items-center justify-end\">\r\n                            <Pagination\r\n                                currentPage={currentPage}\r\n                                totalItems={totalItems}\r\n                                limit={limit}\r\n                                onPageChange={handlePageChange}\r\n                            />\r\n                        </div>\r\n                    {/* )} */}\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default FunctionBarAdmin;\r\n"], "mappings": ";;AAAA,OAAOA,sBAAsB,MAAM,kCAAkC;AACrE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,YAAY,EAAEC,eAAe,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,MAAMC,gBAAgB,GAAGC,IAAA,IAcnB;EAAAC,EAAA;EAAA,IAdoB;IACtBC,UAAU,GAAG,IAAI;IACjBC,QAAQ,GAAG,IAAI;IACfC,eAAe,GAAG,KAAK;IACvBC,UAAU,GAAG,KAAK;IAClBC,KAAK,GAAG,EAAE;IAAE;IACZC,WAAW,GAAG,CAAC;IAAE;IACjBC,UAAU,GAAG,CAAC;IAAE;IAChBC,UAAU,GAAG,CAAC;IAAE;IAChBC,QAAQ,GAAGA,CAAA,KAAM,CAAC,CAAC;IAAE;IACrBC,cAAc,GAAGA,CAAA,KAAM,CAAC,CAAC;IAAE;IAC3BC,SAAS,GAAGA,CAAA,KAAM,CAAC,CAAC;IAAE;IACtBC,mBAAmB,GAAGA,CAAA,KAAM,CAAC,CAAC;IAAE;IAChCC,aAAa,GAAGA,CAAA,KAAM,CAAC,CAAC,CAAE;EAC9B,CAAC,GAAAd,IAAA;EACG,MAAMe,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM+B,WAAW,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACZ,MAAMiC,kBAAkB,GAAIC,KAAK,IAAK;MAClC,IAAIJ,WAAW,CAACK,OAAO,IAAI,CAACL,WAAW,CAACK,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACpEV,iBAAiB,CAAC,KAAK,CAAC;QACxBE,qBAAqB,CAAC,KAAK,CAAC;MAChC;IACJ,CAAC;;IAED;IACAS,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACTK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IACjE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAGN,MAAMQ,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACnC;EACA,MAAMC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAE3B;EAAW,CAAC,EAAE,CAAC4B,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;EAEvE,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;IACpC7B,QAAQ,CAAC6B,QAAQ,CAAC,CAAC,CAAC;IACpBtB,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMuB,gBAAgB,GAAIC,OAAO,IAAK;IAClC9B,cAAc,CAAC8B,OAAO,CAAC,CAAC,CAAC;IACzBtB,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;EAClC,CAAC;;EAED;;EAGA7B,SAAS,CAAC,MAAM;IACZ,MAAMoD,eAAe,GAAGC,UAAU,CAAC,MAAM;MACrC/B,SAAS,CAACS,UAAU,CAAC;IACzB,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMuB,YAAY,CAACF,eAAe,CAAC,CAAC,CAAC;EAChD,CAAC,EAAE,CAACrB,UAAU,EAAEN,QAAQ,CAAC,CAAC;EAE1B,MAAM8B,OAAO,gBACT/C,OAAA;IAAK,wBAAgB;IAACgD,SAAS,EAAC,UAAU;IAAAC,QAAA,eACtCjD,OAAA;MAAKkD,KAAK,EAAC,4BAA4B;MAACC,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAAAL,QAAA,eAC1FjD,OAAA;QAAMuD,CAAC,EAAC,wBAAwB;QAACC,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC,KAAK;QAACC,aAAa,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,MAAMC,UAAU,gBACZ/D,OAAA;IAAK,wBAAgB;IAACgD,SAAS,EAAC,UAAU;IAAAC,QAAA,eACtCjD,OAAA;MAAKkD,KAAK,EAAC,4BAA4B;MAACC,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAAAL,QAAA,eAC1FjD,OAAA;QAAMuD,CAAC,EAAC,8BAA8B;QAACC,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC,KAAK;QAACC,aAAa,EAAC,OAAO;QAACM,cAAc,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,MAAMG,UAAU,gBACZjE,OAAA;IAAK,wBAAgB;IAACgD,SAAS,EAAC,UAAU;IAAAC,QAAA,eACtCjD,OAAA;MAAKkD,KAAK,EAAC,4BAA4B;MAACC,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAAAL,QAAA,eAC1FjD,OAAA;QAAMuD,CAAC,EAAC,iKAAiK;QAACC,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC,KAAK;QAACC,aAAa,EAAC,OAAO;QAACM,cAAc,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3P;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,oBACI9D,OAAA;IAAKgD,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAElCjD,OAAA;MAAKgD,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAE5DjD,OAAA;QAAKgD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,EAC3C5C,QAAQ,iBACLL,OAAA;UAAKgD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACrCjD,OAAA;YACIkD,KAAK,EAAC,4BAA4B;YAClCC,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,IAAI;YACXC,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXN,SAAS,EAAC,kEAAkE;YAAAC,QAAA,eAE5EjD,OAAA;cACIuD,CAAC,EAAC,mPAAmP;cACrPC,MAAM,EAAC,cAAc;cACrBE,aAAa,EAAC,OAAO;cACrBM,cAAc,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9D,OAAA;YACIkE,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,mCAAsB;YAClCC,KAAK,EAAE7C,UAAW;YAClB8C,QAAQ,EAAGC,CAAC,IAAK9C,aAAa,CAAC8C,CAAC,CAACzC,MAAM,CAACuC,KAAK,CAAE;YAC/CpB,SAAS,EAAC;UAA0I;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGN9D,OAAA;QAAKgD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,GACjD1C,UAAU,iBAAIP,OAAA,CAACL,SAAS;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAE3BxD,eAAe,iBACZN,OAAA;UACIuE,OAAO,EAAExD,mBAAoB;UAC7BiC,SAAS,EAAC,mJAAmJ;UAC7JwB,KAAK,EAAC,iBAAY;UAAAvB,QAAA,gBAElBjD,OAAA,CAACJ,eAAe;YAACoD,SAAS,EAAC;UAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvC9D,OAAA;YAAMgD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CACX,eAED9D,OAAA,CAACZ,sBAAsB;UACnBqF,IAAI,EAAE1B,OAAQ;UACd2B,IAAI,EAAE,UAAW;UACjBH,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAACpB,YAAY,CAAC,IAAI,CAAC;QAAE;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL1D,UAAU,iBACPJ,OAAA;MAAKgD,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBAEtGjD,OAAA;QAAKgD,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC1DjD,OAAA;UAAO2E,OAAO,EAAC,aAAa;UAAC3B,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAEvE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR9D,OAAA;UAAKgD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACrBjD,OAAA;YACI4E,EAAE,EAAC,aAAa;YAChBR,KAAK,EAAE5D,KAAM;YACb6D,QAAQ,EAAGC,CAAC,IAAK9B,iBAAiB,CAACqC,MAAM,CAACP,CAAC,CAACzC,MAAM,CAACuC,KAAK,CAAC,CAAE;YAC3DpB,SAAS,EAAC,6JAA6J;YAAAC,QAAA,EAEtKhB,OAAO,CAAC6C,GAAG,CAAEC,MAAM,iBAChB/E,OAAA;cAAqBoE,KAAK,EAAEW,MAAO;cAAA9B,QAAA,EAC9B8B;YAAM,GADEA,MAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACT9D,OAAA;YAAKgD,SAAS,EAAC,uEAAuE;YAAAC,QAAA,eAClFjD,OAAA;cAAKgD,SAAS,EAAC,uBAAuB;cAACM,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACH,OAAO,EAAC,WAAW;cAAAJ,QAAA,eACxFjD,OAAA;gBAAM0D,aAAa,EAAC,OAAO;gBAACM,cAAc,EAAC,OAAO;gBAACP,WAAW,EAAC,GAAG;gBAACF,CAAC,EAAC;cAAgB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN9D,OAAA;UAAMgD,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAIF9D,OAAA;QAAKgD,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC1CjD,OAAA,CAACN,UAAU;UACPe,WAAW,EAAEA,WAAY;UACzBE,UAAU,EAAEA,UAAW;UACvBH,KAAK,EAAEA,KAAM;UACbwE,YAAY,EAAEtC;QAAiB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAET,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAA3D,EAAA,CA5LKF,gBAAgB;EAAA,QAeDX,WAAW;AAAA;AAAA2F,EAAA,GAf1BhF,gBAAgB;AA8LtB,eAAeA,gBAAgB;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}