{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect } from \"react\";\n\n/**\r\n * @param {Function} effect - Hàm effect bạn muốn thực thi sau delay.\r\n * @param {Array<any>} deps - Dependencies giống như trong useEffect.\r\n * @param {number} delay - Thời gian chờ (ms) sau khi deps dừng thay đổi.\r\n */\nconst useDebouncedEffect = (effect, deps, delay) => {\n  _s();\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      effect();\n    }, delay);\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [...deps, delay]);\n};\n_s(useDebouncedEffect, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\nexport default useDebouncedEffect;", "map": {"version": 3, "names": ["useEffect", "useDebouncedEffect", "effect", "deps", "delay", "_s", "handler", "setTimeout", "clearTimeout"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/hooks/useDebouncedEffect.js"], "sourcesContent": ["import { useEffect } from \"react\";\r\n\r\n/**\r\n * @param {Function} effect - Hàm effect bạn muốn thực thi sau delay.\r\n * @param {Array<any>} deps - Dependencies giống như trong useEffect.\r\n * @param {number} delay - Th<PERSON><PERSON> gian chờ (ms) sau khi deps dừng thay đổi.\r\n */\r\nconst useDebouncedEffect = (effect, deps, delay) => {\r\n    useEffect(() => {\r\n        const handler = setTimeout(() => {\r\n            effect();\r\n        }, delay);\r\n\r\n        return () => {\r\n            clearTimeout(handler);\r\n        };\r\n    }, [...deps, delay]);\r\n};\r\n\r\nexport default useDebouncedEffect;\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,OAAO;;AAEjC;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,KAAK;EAAAC,EAAA;EAChDL,SAAS,CAAC,MAAM;IACZ,MAAMM,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC7BL,MAAM,CAAC,CAAC;IACZ,CAAC,EAAEE,KAAK,CAAC;IAET,OAAO,MAAM;MACTI,YAAY,CAACF,OAAO,CAAC;IACzB,CAAC;EACL,CAAC,EAAE,CAAC,GAAGH,IAAI,EAAEC,KAAK,CAAC,CAAC;AACxB,CAAC;AAACC,EAAA,CAVIJ,kBAAkB;AAYxB,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}