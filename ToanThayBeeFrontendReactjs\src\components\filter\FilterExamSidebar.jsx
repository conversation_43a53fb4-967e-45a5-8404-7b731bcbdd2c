import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchCodesByType } from "../../features/code/codeSlice";
import { fetchPublicExams } from "../../features/exam/examSlice";
import { setSelectedGrade, setSelectedChapters, setSelectedExamTypes, setIsSearch } from "../../features/filter/filterSlice";
import { setCurrentPage } from "../../features/exam/examSlice";
import LoadingSpinner from "../loading/LoadingSpinner";
import { Search, BookOpen, GraduationCap, FileText, Tag, Filter as FilterIcon } from "lucide-react";
import { motion } from "framer-motion";

const ButtonSidebar = ({ choice, onClick, value, text, icon, isOpen, count = null }) => {
    const isActive = choice === value;
    const Icon = icon;

    return (
        <button
            onClick={onClick}
            className={`cursor-pointer self-stretch p-2 ${isActive
                ? 'bg-sky-100 text-sky-700 font-medium'
                : 'hover:bg-gray-100 text-gray-700'
                } rounded-lg inline-flex w-full justify-start items-center gap-3 transition-colors`}
        >
            <div className={`flex justify-center items-center p-2 ${isActive ? 'bg-sky-200 text-sky-700' : 'bg-gray-100 text-gray-600'} rounded-full transition-colors`}>
                <Icon size={16} />
            </div>
            <motion.div
                initial={false}
                animate={{
                    opacity: isOpen ? 1 : 0,
                    width: isOpen ? '100%' : 0,
                }}
                transition={{
                    duration: 0.2,
                    ease: [0.25, 0.1, 0.25, 1.0],
                }}
                className="flex flex-row w-full items-center justify-between gap-2"
            >
                <p className="text-sm font-medium text-start truncate w-full">{text}</p>
                {count !== null && (
                    <div className={`px-2 py-1 text-xs rounded-full ${isActive ? 'bg-sky-200 text-sky-700' : 'bg-gray-200 text-gray-700'} font-medium min-w-[1.5rem] text-center`}>
                        {count}
                    </div>
                )}
            </motion.div>
        </button>
    );
};

const FilterExamSidebar = () => {
    const { codes } = useSelector((state) => state.codes);
    const { isSearch, selectedGrade, selectedChapters, selectedExamTypes } = useSelector((state) => state.filter);
    const dispatch = useDispatch();

    const { pagination } = useSelector((state) => state.exams);
    const { page: currentPage, pageSize: limit, sortOrder } = pagination;

    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const [isClassroomExam, setIsClassroomExam] = useState(null);
    const [activeTab, setActiveTab] = useState('all'); // 'all', 'classroom', 'self'


    useEffect(() => {
        dispatch(fetchCodesByType(['chapter', 'grade', 'exam type']));
    }, [dispatch]);

    const fetchExams = (override = {}) => {
        // Only apply filters if isSearch is true or if explicitly overridden
        const shouldApplyFilters = isSearch || override.applyFilters;

        dispatch(fetchPublicExams({
            page: override.page ?? currentPage,
            limit: 10,
            sortOrder,
            typeOfExam: shouldApplyFilters ? (override.typeOfExam ?? selectedExamTypes) : [],
            class: shouldApplyFilters ? (override.class === null ? override.class : selectedGrade) : null,
            chapter: shouldApplyFilters ? (override.chapter ?? selectedChapters) : [],
            search: shouldApplyFilters ? search : "",
            isClassroomExam: override.isClassroomExam
        }));
    }

    // Only fetch exams when page changes, not when filters change
    useEffect(() => {
        if (isSearch) {
            fetchExams({ isClassroomExam });
        }
    }, [dispatch, isSearch]);

    useEffect(() => {
        fetchExams({ isClassroomExam });
    }, [currentPage]);

    useEffect(() => {
        if (selectedChapters.length === 0 && selectedGrade === null && selectedExamTypes.length === 0 && search === "") {
            dispatch(setIsSearch(false));
        }
    }, [dispatch, selectedChapters, selectedGrade, selectedExamTypes, search]);

    const handleSearch = () => {
        setLoading(true);
        // Set isSearch to true first so filters will be applied
        dispatch(setIsSearch(true));

        dispatch(fetchPublicExams({
            page: currentPage,
            limit: 10,
            sortOrder,
            typeOfExam: selectedExamTypes,
            class: selectedGrade,
            chapter: selectedChapters,
            search,
            isClassroomExam
        }))
            .then(() => {
                setLoading(false);
            });
    }

    const resetAllFilters = () => {
        setSearch("");
        dispatch(setSelectedGrade(null));
        dispatch(setSelectedChapters([]));
        dispatch(setSelectedExamTypes([]));

        // Set isSearch to true to ensure filters are applied (in this case, empty filters)
        dispatch(setIsSearch(true));

        // Apply the reset filters immediately
        setLoading(true);
        dispatch(fetchPublicExams({
            page: currentPage,
            limit: 10,
            sortOrder,
            typeOfExam: [],
            class: null,
            chapter: [],
            search: "",
            isClassroomExam
        }))
            .then(() => {
                setLoading(false);
            });
    }

    const toggleItem = (codeList, dispatchSetAction) => (code) => (isChecked) => {
        const newList = isChecked
            ? [...codeList, code]
            : codeList.filter((item) => item !== code);

        dispatch(dispatchSetAction(newList));
    };

    const handleSelectGrade = (gradeCode) => (isChecked) => {
        dispatch(setSelectedGrade(isChecked ? gradeCode : null));
        dispatch(setSelectedChapters([])); // reset selected chapters when grade changes
    };

    return (
        <div className="sticky top-20 py-4 px-2 w-[300px] h-[90vh] overflow-y-auto hide-scrollbar hidden lg:block">
            <div className="inline-flex w-full flex-row justify-center items-center">
                <div className="text-center truncate text-zinc-900 text-xl font-semibold font-bevietnam">
                    Bộ lọc đề thi
                </div>
            </div>
            <hr className="w-full h-[1px] bg-neutral-200 my-4" />

            {/* Filter Categories */}
            <div className="self-stretch text-sm w-full rounded-md flex flex-col justify-start items-start gap-1">
                <ButtonSidebar
                    choice={activeTab}
                    onClick={() => {
                        setIsClassroomExam(null);
                        setActiveTab('all');
                        dispatch(setCurrentPage(1));
                        fetchExams({
                            page: 1,
                            isClassroomExam: null,
                            applyFilters: isSearch
                        });
                    }}
                    value="all"
                    text="Tất cả đề thi"
                    icon={FileText}
                    isOpen={true}
                />

                <ButtonSidebar
                    choice={activeTab}
                    onClick={() => {
                        setIsClassroomExam(true);
                        setActiveTab('classroom');
                        dispatch(setCurrentPage(1));
                        fetchExams({
                            page: 1,
                            isClassroomExam: true,
                            applyFilters: isSearch
                        });
                    }}
                    value="classroom"
                    text="Đề trên lớp"
                    icon={GraduationCap}
                    isOpen={true}
                />

                <ButtonSidebar
                    choice={activeTab}
                    onClick={() => {
                        setIsClassroomExam(false);
                        setActiveTab('self');
                        dispatch(setCurrentPage(1));
                        fetchExams({
                            page: 1,
                            isClassroomExam: false,
                            applyFilters: isSearch
                        });
                    }}
                    value="self"
                    text="Đề tự luyện"
                    icon={BookOpen}
                    isOpen={true}
                />
            </div>

            {/* Search Section */}
            <div className="mt-6">
                <h3 className="text-sm font-semibold text-gray-700 mb-3">Tìm kiếm</h3>
                <div className="relative w-full">
                    <input
                        type="text"
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                        placeholder="Tìm kiếm đề thi..."
                        className="w-full h-10 pl-10 pr-10 text-sm text-gray-700 placeholder-gray-400 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-400 focus:border-sky-400 transition-all duration-150"
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                handleSearch();
                            }
                        }}
                    />
                    <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                        <Search size={18} className="text-gray-400" />
                    </div>
                    {loading && (
                        <div className="absolute inset-y-0 right-3 flex items-center">
                            <LoadingSpinner color="border-black" size="1.25rem" />
                        </div>
                    )}
                </div>
            </div>

            {/* Grade Filter */}
            <div className="mt-6">
                <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <GraduationCap size={16} />
                    Lớp học
                </h3>
                <div className="flex flex-wrap gap-2">
                    {codes?.['grade']?.map((code) => (
                        <motion.div
                            key={code.code}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => handleSelectGrade(code.code)(selectedGrade !== code.code)}
                            className={`px-3 py-2 rounded-lg text-sm cursor-pointer transition-all ${selectedGrade === code.code
                                ? 'bg-sky-100 text-sky-700 border border-sky-300 font-medium shadow-sm'
                                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 hover:border-gray-300'
                                }`}
                        >
                            {code.description}
                        </motion.div>
                    ))}
                </div>
            </div>

            {/* Chapter Filter */}
            <div className="mt-6">
                <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <BookOpen size={16} />
                    Chương học
                </h3>
                {!selectedGrade ? (
                    <div className="text-sm text-gray-500 italic bg-gray-50 p-3 rounded-lg">
                        Chọn lớp để hiển thị chương
                    </div>
                ) : (
                    <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
                        {codes?.['chapter']
                            ?.filter((code) => code.code.startsWith(selectedGrade) && code.code.length === 4)
                            ?.map((code) => (
                                <motion.div
                                    key={code.code}
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={() => {
                                        toggleItem(selectedChapters, setSelectedChapters)(code.code)(
                                            !selectedChapters.includes(code.code)
                                        );
                                    }}
                                    className={`px-3 py-2 rounded-lg text-sm cursor-pointer transition-all ${selectedChapters.includes(code.code)
                                        ? 'bg-sky-100 text-sky-700 border border-sky-300 font-medium shadow-sm'
                                        : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 hover:border-gray-300'
                                        }`}
                                >
                                    {code.description}
                                </motion.div>
                            ))
                        }
                    </div>
                )}
            </div>

            {/* Exam Type Filter */}
            <div className="mt-6">
                <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <Tag size={16} />
                    Loại đề thi
                </h3>
                <div className="flex flex-wrap gap-2">
                    {codes?.['exam type']?.map((code) => (
                        <motion.div
                            key={code.code}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => {
                                toggleItem(selectedExamTypes, setSelectedExamTypes)(code.code)(
                                    !selectedExamTypes.includes(code.code)
                                );
                            }}
                            className={`px-3 py-2 rounded-lg text-sm cursor-pointer transition-all ${selectedExamTypes.includes(code.code)
                                ? 'bg-sky-100 text-sky-700 border border-sky-300 font-medium shadow-sm'
                                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 hover:border-gray-300'
                                }`}
                        >
                            {code.description}
                        </motion.div>
                    ))}
                </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-8 space-y-3">
                <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleSearch}
                    className="w-full bg-sky-600 hover:bg-sky-700 text-white text-sm font-medium py-3 px-4 rounded-lg transition-all shadow-sm hover:shadow-md flex items-center justify-center gap-2"
                >
                    <Search size={16} />
                    Tìm kiếm
                </motion.button>
                <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={resetAllFilters}
                    className="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 bg-white text-sm font-medium py-3 px-4 rounded-lg transition-all flex items-center justify-center gap-2"
                >
                    <FilterIcon size={16} />
                    Xóa bộ lọc
                </motion.button>
            </div>
        </div>
    );
};

export default FilterExamSidebar;
