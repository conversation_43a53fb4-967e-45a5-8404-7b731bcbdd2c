{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { reorderQuestions } from \"src/features/examAI/examAISlice\";\nimport LoadingData from \"../loading/LoadingData\";\nimport SortableQuestionItem from \"./SortableQuestionItem\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const EditQuestionView = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    questionsEdited,\n    loading\n  } = useSelector(state => state.examAI);\n  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      const oldIndex = questionsEdited.findIndex(q => q.id === active.id);\n      const newIndex = questionsEdited.findIndex(q => q.id === over.id);\n      dispatch(reorderQuestions({\n        oldIndex,\n        newIndex\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(LoadingData, {\n    loading: loading,\n    loadText: \"\\u0110ang t\\u1EA3i danh s\\xE1ch c\\xE2u h\\u1ECFi...\",\n    isNoData: questionsEdited.length > 0 ? false : true,\n    noDataText: \"Kh\\xF4ng c\\xF3 c\\xE2u h\\u1ECFi n\\xE0o.\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold text-gray-800 mb-4\",\n      children: \"Danh s\\xE1ch c\\xE2u h\\u1ECFi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: questionsEdited.length > 0 ? /*#__PURE__*/_jsxDEV(DndContext, {\n        sensors: sensors,\n        collisionDetection: closestCenter,\n        onDragEnd: handleDragEnd,\n        children: /*#__PURE__*/_jsxDEV(SortableContext, {\n          items: questionsEdited.map(q => q.id),\n          strategy: verticalListSortingStrategy,\n          children: questionsEdited.map((q, index) => /*#__PURE__*/_jsxDEV(SortableQuestionItem, {\n            question: q,\n            index: index\n          }, q.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 c\\xE2u h\\u1ECFi n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 9\n  }, this);\n};\n_s(EditQuestionView, \"OnJVrBptPmCkljyQk9X+uGRu22c=\", false, function () {\n  return [useDispatch, useSelector, useSensors, useSensor, useSensor];\n});\n_c = EditQuestionView;\nconst EditExamView = () => {\n  _s2();\n  const {\n    exam\n  } = useSelector(state => state.examAI);\n  return /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n    content: exam.markdownExam\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 9\n  }, this);\n};\n_s2(EditExamView, \"Drfxdfcr8W48eHwgQjaPNS6fy7M=\", false, function () {\n  return [useSelector];\n});\n_c2 = EditExamView;\nexport const LeftContent = () => {\n  _s3();\n  const {\n    viewEdit\n  } = useSelector(state => state.examAI);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-2/3 border-r border-gray-300 overflow-y-auto p-4\",\n    children: [viewEdit === 'question' && /*#__PURE__*/_jsxDEV(EditQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 41\n    }, this), viewEdit === 'exam' && /*#__PURE__*/_jsxDEV(EditExamView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 37\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 9\n  }, this);\n};\n_s3(LeftContent, \"plqPiIWI8H/U9YD7djE1+JqN1rI=\", false, function () {\n  return [useSelector];\n});\n_c3 = LeftContent;\nexport default LeftContent;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"EditQuestionView\");\n$RefreshReg$(_c2, \"EditExamView\");\n$RefreshReg$(_c3, \"LeftContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "reorderQuestions", "LoadingData", "SortableQuestionItem", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "MarkdownPreviewWithMath", "jsxDEV", "_jsxDEV", "EditQuestionView", "_s", "dispatch", "questionsEdited", "loading", "state", "examAI", "sensors", "coordinateGetter", "handleDragEnd", "event", "active", "over", "id", "oldIndex", "findIndex", "q", "newIndex", "loadText", "isNoData", "length", "noDataText", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "collisionDetection", "onDragEnd", "items", "map", "strategy", "index", "question", "_c", "EditExamView", "_s2", "exam", "content", "markdownExam", "_c2", "LeftContent", "_s3", "viewEdit", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/LeftContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { reorderQuestions } from \"src/features/examAI/examAISlice\";\r\nimport LoadingData from \"../loading/LoadingData\";\r\nimport SortableQuestionItem from \"./SortableQuestionItem\";\r\nimport {\r\n    DndContext,\r\n    closestCenter,\r\n    KeyboardSensor,\r\n    PointerSensor,\r\n    useSensor,\r\n    useSensors,\r\n} from '@dnd-kit/core';\r\nimport {\r\n    SortableContext,\r\n    sortableKeyboardCoordinates,\r\n    verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\n\r\nexport const EditQuestionView = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionsEdited, loading } = useSelector((state) => state.examAI);\r\n\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor),\r\n        useSensor(KeyboardSensor, {\r\n            coordinateGetter: sortableKeyboardCoordinates,\r\n        })\r\n    );\r\n\r\n    const handleDragEnd = (event) => {\r\n        const { active, over } = event;\r\n\r\n        if (active.id !== over?.id) {\r\n            const oldIndex = questionsEdited.findIndex(q => q.id === active.id);\r\n            const newIndex = questionsEdited.findIndex(q => q.id === over.id);\r\n\r\n            dispatch(reorderQuestions({\r\n                oldIndex,\r\n                newIndex\r\n            }));\r\n        }\r\n    };\r\n\r\n    return (\r\n        <LoadingData loading={loading} loadText=\"Đang tải danh sách câu hỏi...\" isNoData={questionsEdited.length > 0 ? false : true} noDataText=\"Không có câu hỏi nào.\">\r\n            <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Danh sách câu hỏi</h2>\r\n            <div className=\"space-y-3\">\r\n                {questionsEdited.length > 0 ? (\r\n                    <DndContext\r\n                        sensors={sensors}\r\n                        collisionDetection={closestCenter}\r\n                        onDragEnd={handleDragEnd}\r\n                    >\r\n                        <SortableContext\r\n                            items={questionsEdited.map(q => q.id)}\r\n                            strategy={verticalListSortingStrategy}\r\n                        >\r\n                            {questionsEdited.map((q, index) => (\r\n                                <SortableQuestionItem\r\n                                    key={q.id}\r\n                                    question={q}\r\n                                    index={index}\r\n                                />\r\n                            ))}\r\n                        </SortableContext>\r\n                    </DndContext>\r\n                ) : (\r\n                    <p className=\"text-gray-500\">Không có câu hỏi nào.</p>\r\n                )}\r\n            </div>\r\n        </LoadingData>\r\n    )\r\n}\r\n\r\nconst EditExamView = () => {\r\n    const { exam } = useSelector((state) => state.examAI);\r\n\r\n    return (\r\n        <MarkdownPreviewWithMath content={exam.markdownExam} />\r\n    )\r\n}\r\n\r\nexport const LeftContent = () => {\r\n    const { viewEdit } = useSelector((state) => state.examAI);\r\n\r\n    return (\r\n        <div className=\"w-2/3 border-r border-gray-300 overflow-y-auto p-4\">\r\n            {viewEdit === 'question' && <EditQuestionView />}\r\n            {viewEdit === 'exam' && <EditExamView />}\r\n        </div>\r\n    )\r\n\r\n}\r\nexport default LeftContent;"], "mappings": ";;;;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAC1B,OAAOC,uBAAuB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB,eAAe;IAAEC;EAAQ,CAAC,GAAGrB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEzE,MAAMC,OAAO,GAAGd,UAAU,CACtBD,SAAS,CAACD,aAAa,CAAC,EACxBC,SAAS,CAACF,cAAc,EAAE;IACtBkB,gBAAgB,EAAEb;EACtB,CAAC,CACL,CAAC;EAED,MAAMc,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB,MAAMC,QAAQ,GAAGX,eAAe,CAACY,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACnE,MAAMI,QAAQ,GAAGd,eAAe,CAACY,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;MAEjEX,QAAQ,CAACjB,gBAAgB,CAAC;QACtB6B,QAAQ;QACRG;MACJ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAED,oBACIlB,OAAA,CAACb,WAAW;IAACkB,OAAO,EAAEA,OAAQ;IAACc,QAAQ,EAAC,oDAA+B;IAACC,QAAQ,EAAEhB,eAAe,CAACiB,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,IAAK;IAACC,UAAU,EAAC,wCAAuB;IAAAC,QAAA,gBAC3JvB,OAAA;MAAIwB,SAAS,EAAC,0CAA0C;MAAAD,QAAA,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/E5B,OAAA;MAAKwB,SAAS,EAAC,WAAW;MAAAD,QAAA,EACrBnB,eAAe,CAACiB,MAAM,GAAG,CAAC,gBACvBrB,OAAA,CAACX,UAAU;QACPmB,OAAO,EAAEA,OAAQ;QACjBqB,kBAAkB,EAAEvC,aAAc;QAClCwC,SAAS,EAAEpB,aAAc;QAAAa,QAAA,eAEzBvB,OAAA,CAACL,eAAe;UACZoC,KAAK,EAAE3B,eAAe,CAAC4B,GAAG,CAACf,CAAC,IAAIA,CAAC,CAACH,EAAE,CAAE;UACtCmB,QAAQ,EAAEpC,2BAA4B;UAAA0B,QAAA,EAErCnB,eAAe,CAAC4B,GAAG,CAAC,CAACf,CAAC,EAAEiB,KAAK,kBAC1BlC,OAAA,CAACZ,oBAAoB;YAEjB+C,QAAQ,EAAElB,CAAE;YACZiB,KAAK,EAAEA;UAAM,GAFRjB,CAAC,CAACH,EAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAEb5B,OAAA;QAAGwB,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IACxD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAA1B,EAAA,CAtDYD,gBAAgB;EAAA,QACRhB,WAAW,EACSD,WAAW,EAEhCU,UAAU,EACtBD,SAAS,EACTA,SAAS;AAAA;AAAA2C,EAAA,GANJnC,gBAAgB;AAwD7B,MAAMoC,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGvD,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAErD,oBACIP,OAAA,CAACF,uBAAuB;IAAC0C,OAAO,EAAED,IAAI,CAACE;EAAa;IAAAhB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAE/D,CAAC;AAAAU,GAAA,CANKD,YAAY;EAAA,QACGrD,WAAW;AAAA;AAAA0D,GAAA,GAD1BL,YAAY;AAQlB,OAAO,MAAMM,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAM;IAAEC;EAAS,CAAC,GAAG7D,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEzD,oBACIP,OAAA;IAAKwB,SAAS,EAAC,oDAAoD;IAAAD,QAAA,GAC9DsB,QAAQ,KAAK,UAAU,iBAAI7C,OAAA,CAACC,gBAAgB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC/CiB,QAAQ,KAAK,MAAM,iBAAI7C,OAAA,CAACqC,YAAY;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAGd,CAAC;AAAAgB,GAAA,CAVYD,WAAW;EAAA,QACC3D,WAAW;AAAA;AAAA8D,GAAA,GADvBH,WAAW;AAWxB,eAAeA,WAAW;AAAC,IAAAP,EAAA,EAAAM,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAX,EAAA;AAAAW,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}