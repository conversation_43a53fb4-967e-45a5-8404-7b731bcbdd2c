{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\SolutionEditor.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { ImagePlus, Upload } from \"lucide-react\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SolutionEditor = _ref => {\n  _s();\n  let {\n    solution,\n    onSolutionChange,\n    isAddImage\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const [dragPosition, setDragPosition] = useState(null);\n  const textareaRef = useRef(null);\n  const previewRef = useRef(null);\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\");\n    if (!draggedImage || !onSolutionChange) return;\n\n    // Tính toán vị trí chèn ảnh\n    let insertPosition = 0;\n    if (dragPosition !== null) {\n      insertPosition = dragPosition;\n    } else {\n      // Nếu không có vị trí cụ thể, chèn vào cuối\n      insertPosition = solution ? solution.length : 0;\n    }\n\n    // Tạo markdown image syntax\n    const imageMarkdown = \"\\n![\\u1EA2nh](\".concat(draggedImage, \")\\n\");\n\n    // Chèn vào vị trí\n    const newSolution = solution ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition) : imageMarkdown;\n    onSolutionChange(newSolution);\n    setDragPosition(null);\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (isAddImage) {\n      // Tính toán vị trí dựa trên mouse position\n      const rect = e.currentTarget.getBoundingClientRect();\n      const y = e.clientY - rect.top;\n\n      // Ước tính vị trí trong text dựa trên tọa độ Y\n      const lineHeight = 24; // Ước tính chiều cao dòng\n      const lineIndex = Math.floor(y / lineHeight);\n\n      // Tính toán position trong text\n      if (solution) {\n        const lines = solution.split('\\n');\n        let position = 0;\n        for (let i = 0; i < Math.min(lineIndex, lines.length); i++) {\n          position += lines[i].length + 1; // +1 cho \\n\n        }\n        setDragPosition(position);\n      }\n    }\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (isAddImage && e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!e.currentTarget.contains(e.relatedTarget)) {\n      setIsDraggingOver(false);\n      setDragPosition(null);\n    }\n  };\n  const handleTextareaChange = e => {\n    onSolutionChange(e.target.value);\n  };\n\n  // Hiển thị drop indicator\n  const getDropIndicatorStyle = () => {\n    if (!isDraggingOver || dragPosition === null) return {};\n    const lineHeight = 24;\n    const lines = solution ? solution.split('\\n') : [''];\n    let currentPos = 0;\n    let lineIndex = 0;\n    for (let i = 0; i < lines.length; i++) {\n      if (currentPos + lines[i].length >= dragPosition) {\n        lineIndex = i;\n        break;\n      }\n      currentPos += lines[i].length + 1;\n    }\n    return {\n      position: 'absolute',\n      top: \"\".concat(lineIndex * lineHeight + 8, \"px\"),\n      left: '8px',\n      right: '8px',\n      height: '2px',\n      backgroundColor: '#3b82f6',\n      zIndex: 10,\n      borderRadius: '1px'\n    };\n  };\n  if (!isAddImage) {\n    // Chế độ xem bình thường\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"L\\u1EDDi gi\\u1EA3i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 17\n      }, this), \" \", /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-sm font-semibold text-green-700\",\n      children: \"L\\u1EDDi gi\\u1EA3i:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative border rounded-lg transition-all duration-200 \".concat(isDraggingOver ? \"border-2 border-dashed border-blue-400 bg-blue-50\" : \"border-gray-300 hover:border-blue-300\"),\n      onDragOver: handleDragOver,\n      onDragEnter: handleDragEnter,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      children: [isDraggingOver && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: getDropIndicatorStyle()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 36\n      }, this), isDraggingOver && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-blue-50 bg-opacity-90 flex items-center justify-center z-20 rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(Upload, {\n            className: \"w-8 h-8 text-blue-500 mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-600 font-medium\",\n            children: \"Th\\u1EA3 \\u1EA3nh v\\xE0o \\u0111\\xE2y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-400 text-sm\",\n            children: \"\\u1EA2nh s\\u1EBD \\u0111\\u01B0\\u1EE3c ch\\xE8n v\\xE0o v\\u1ECB tr\\xED n\\xE0y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        ref: textareaRef,\n        value: solution || '',\n        onChange: handleTextareaChange,\n        placeholder: \"Nh\\u1EADp l\\u1EDDi gi\\u1EA3i (h\\u1ED7 tr\\u1EE3 Markdown v\\xE0 LaTeX)...\",\n        className: \"w-full p-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px]\",\n        style: {\n          lineHeight: '24px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 17\n      }, this), !solution && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none\",\n        children: [/*#__PURE__*/_jsxDEV(ImagePlus, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"K\\xE9o \\u1EA3nh v\\xE0o \\u0111\\u1EC3 ch\\xE8n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 13\n    }, this), solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-3 bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 mb-2\",\n        children: \"Xem tr\\u01B0\\u1EDBc:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 9\n  }, this);\n};\n_s(SolutionEditor, \"8w8C9+oFObGoa0GKDZTE7isB1QA=\");\n_c = SolutionEditor;\nexport default SolutionEditor;\nvar _c;\n$RefreshReg$(_c, \"SolutionEditor\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "ImagePlus", "Upload", "MarkdownPreviewWithMath", "jsxDEV", "_jsxDEV", "SolutionEditor", "_ref", "_s", "solution", "onSolutionChange", "isAddImage", "isDraggingOver", "setIsDraggingOver", "dragPosition", "setDragPosition", "textareaRef", "previewRef", "handleDrop", "e", "preventDefault", "stopPropagation", "draggedImage", "dataTransfer", "getData", "insertPosition", "length", "imageMarkdown", "concat", "newSolution", "slice", "handleDragOver", "rect", "currentTarget", "getBoundingClientRect", "y", "clientY", "top", "lineHeight", "lineIndex", "Math", "floor", "lines", "split", "position", "i", "min", "handleDragEnter", "types", "includes", "handleDragLeave", "contains", "relatedTarget", "handleTextareaChange", "target", "value", "getDropIndicatorStyle", "currentPos", "left", "right", "height", "backgroundColor", "zIndex", "borderRadius", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "style", "ref", "onChange", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/SolutionEditor.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\nimport { ImagePlus, Upload } from \"lucide-react\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\n\nconst SolutionEditor = ({ solution, onSolutionChange, isAddImage }) => {\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\n    const [dragPosition, setDragPosition] = useState(null);\n    const textareaRef = useRef(null);\n    const previewRef = useRef(null);\n\n    const handleDrop = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDraggingOver(false);\n\n        const draggedImage = e.dataTransfer.getData(\"text/plain\");\n        if (!draggedImage || !onSolutionChange) return;\n\n        // Tính toán vị trí chèn ảnh\n        let insertPosition = 0;\n        \n        if (dragPosition !== null) {\n            insertPosition = dragPosition;\n        } else {\n            // Nếu không có vị trí cụ thể, chèn vào cuối\n            insertPosition = solution ? solution.length : 0;\n        }\n\n        // Tạo markdown image syntax\n        const imageMarkdown = `\\n![Ảnh](${draggedImage})\\n`;\n        \n        // Chèn vào vị trí\n        const newSolution = solution \n            ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition)\n            : imageMarkdown;\n\n        onSolutionChange(newSolution);\n        setDragPosition(null);\n    };\n\n    const handleDragOver = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        \n        if (isAddImage) {\n            // Tính toán vị trí dựa trên mouse position\n            const rect = e.currentTarget.getBoundingClientRect();\n            const y = e.clientY - rect.top;\n            \n            // Ước tính vị trí trong text dựa trên tọa độ Y\n            const lineHeight = 24; // Ước tính chiều cao dòng\n            const lineIndex = Math.floor(y / lineHeight);\n            \n            // Tính toán position trong text\n            if (solution) {\n                const lines = solution.split('\\n');\n                let position = 0;\n                for (let i = 0; i < Math.min(lineIndex, lines.length); i++) {\n                    position += lines[i].length + 1; // +1 cho \\n\n                }\n                setDragPosition(position);\n            }\n        }\n    };\n\n    const handleDragEnter = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (isAddImage && e.dataTransfer.types.includes('text/plain')) {\n            setIsDraggingOver(true);\n        }\n    };\n\n    const handleDragLeave = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (!e.currentTarget.contains(e.relatedTarget)) {\n            setIsDraggingOver(false);\n            setDragPosition(null);\n        }\n    };\n\n    const handleTextareaChange = (e) => {\n        onSolutionChange(e.target.value);\n    };\n\n    // Hiển thị drop indicator\n    const getDropIndicatorStyle = () => {\n        if (!isDraggingOver || dragPosition === null) return {};\n        \n        const lineHeight = 24;\n        const lines = solution ? solution.split('\\n') : [''];\n        let currentPos = 0;\n        let lineIndex = 0;\n        \n        for (let i = 0; i < lines.length; i++) {\n            if (currentPos + lines[i].length >= dragPosition) {\n                lineIndex = i;\n                break;\n            }\n            currentPos += lines[i].length + 1;\n        }\n        \n        return {\n            position: 'absolute',\n            top: `${lineIndex * lineHeight + 8}px`,\n            left: '8px',\n            right: '8px',\n            height: '2px',\n            backgroundColor: '#3b82f6',\n            zIndex: 10,\n            borderRadius: '1px'\n        };\n    };\n\n    if (!isAddImage) {\n        // Chế độ xem bình thường\n        return (\n            <div className=\"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\">\n                <span className=\"font-semibold\">Lời giải:</span>{\" \"}\n                <MarkdownPreviewWithMath content={solution} />\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"mt-2 space-y-3\">\n            <div className=\"text-sm font-semibold text-green-700\">Lời giải:</div>\n            \n            {/* Editor với drop zone */}\n            <div\n                className={`relative border rounded-lg transition-all duration-200 ${\n                    isDraggingOver \n                        ? \"border-2 border-dashed border-blue-400 bg-blue-50\" \n                        : \"border-gray-300 hover:border-blue-300\"\n                }`}\n                onDragOver={handleDragOver}\n                onDragEnter={handleDragEnter}\n                onDragLeave={handleDragLeave}\n                onDrop={handleDrop}\n            >\n                {/* Drop indicator */}\n                {isDraggingOver && <div style={getDropIndicatorStyle()} />}\n                \n                {/* Drag overlay */}\n                {isDraggingOver && (\n                    <div className=\"absolute inset-0 bg-blue-50 bg-opacity-90 flex items-center justify-center z-20 rounded-lg\">\n                        <div className=\"text-center\">\n                            <Upload className=\"w-8 h-8 text-blue-500 mx-auto mb-2\" />\n                            <p className=\"text-blue-600 font-medium\">Thả ảnh vào đây</p>\n                            <p className=\"text-blue-400 text-sm\">Ảnh sẽ được chèn vào vị trí này</p>\n                        </div>\n                    </div>\n                )}\n                \n                {/* Textarea */}\n                <textarea\n                    ref={textareaRef}\n                    value={solution || ''}\n                    onChange={handleTextareaChange}\n                    placeholder=\"Nhập lời giải (hỗ trợ Markdown và LaTeX)...\"\n                    className=\"w-full p-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px]\"\n                    style={{ lineHeight: '24px' }}\n                />\n                \n                {/* Hint */}\n                {!solution && (\n                    <div className=\"absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none\">\n                        <ImagePlus className=\"w-4 h-4\" />\n                        <span>Kéo ảnh vào để chèn</span>\n                    </div>\n                )}\n            </div>\n            \n            {/* Preview */}\n            {solution && (\n                <div className=\"border border-gray-200 rounded-lg p-3 bg-gray-50\">\n                    <div className=\"text-xs text-gray-500 mb-2\">Xem trước:</div>\n                    <MarkdownPreviewWithMath content={solution} />\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default SolutionEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,MAAM,QAAQ,cAAc;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,cAAc,GAAGC,IAAA,IAAgD;EAAAC,EAAA;EAAA,IAA/C;IAAEC,QAAQ;IAAEC,gBAAgB;IAAEC;EAAW,CAAC,GAAAJ,IAAA;EAC9D,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMkB,WAAW,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkB,UAAU,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMmB,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBR,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMS,YAAY,GAAGH,CAAC,CAACI,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,IAAI,CAACF,YAAY,IAAI,CAACZ,gBAAgB,EAAE;;IAExC;IACA,IAAIe,cAAc,GAAG,CAAC;IAEtB,IAAIX,YAAY,KAAK,IAAI,EAAE;MACvBW,cAAc,GAAGX,YAAY;IACjC,CAAC,MAAM;MACH;MACAW,cAAc,GAAGhB,QAAQ,GAAGA,QAAQ,CAACiB,MAAM,GAAG,CAAC;IACnD;;IAEA;IACA,MAAMC,aAAa,oBAAAC,MAAA,CAAeN,YAAY,QAAK;;IAEnD;IACA,MAAMO,WAAW,GAAGpB,QAAQ,GACtBA,QAAQ,CAACqB,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC,GAAGE,aAAa,GAAGlB,QAAQ,CAACqB,KAAK,CAACL,cAAc,CAAC,GAClFE,aAAa;IAEnBjB,gBAAgB,CAACmB,WAAW,CAAC;IAC7Bd,eAAe,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMgB,cAAc,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IAEnB,IAAIV,UAAU,EAAE;MACZ;MACA,MAAMqB,IAAI,GAAGb,CAAC,CAACc,aAAa,CAACC,qBAAqB,CAAC,CAAC;MACpD,MAAMC,CAAC,GAAGhB,CAAC,CAACiB,OAAO,GAAGJ,IAAI,CAACK,GAAG;;MAE9B;MACA,MAAMC,UAAU,GAAG,EAAE,CAAC,CAAC;MACvB,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACN,CAAC,GAAGG,UAAU,CAAC;;MAE5C;MACA,IAAI7B,QAAQ,EAAE;QACV,MAAMiC,KAAK,GAAGjC,QAAQ,CAACkC,KAAK,CAAC,IAAI,CAAC;QAClC,IAAIC,QAAQ,GAAG,CAAC;QAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACM,GAAG,CAACP,SAAS,EAAEG,KAAK,CAAChB,MAAM,CAAC,EAAEmB,CAAC,EAAE,EAAE;UACxDD,QAAQ,IAAIF,KAAK,CAACG,CAAC,CAAC,CAACnB,MAAM,GAAG,CAAC,CAAC,CAAC;QACrC;QACAX,eAAe,CAAC6B,QAAQ,CAAC;MAC7B;IACJ;EACJ,CAAC;EAED,MAAMG,eAAe,GAAI5B,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIV,UAAU,IAAIQ,CAAC,CAACI,YAAY,CAACyB,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC3DpC,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMqC,eAAe,GAAI/B,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAI,CAACF,CAAC,CAACc,aAAa,CAACkB,QAAQ,CAAChC,CAAC,CAACiC,aAAa,CAAC,EAAE;MAC5CvC,iBAAiB,CAAC,KAAK,CAAC;MACxBE,eAAe,CAAC,IAAI,CAAC;IACzB;EACJ,CAAC;EAED,MAAMsC,oBAAoB,GAAIlC,CAAC,IAAK;IAChCT,gBAAgB,CAACS,CAAC,CAACmC,MAAM,CAACC,KAAK,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC5C,cAAc,IAAIE,YAAY,KAAK,IAAI,EAAE,OAAO,CAAC,CAAC;IAEvD,MAAMwB,UAAU,GAAG,EAAE;IACrB,MAAMI,KAAK,GAAGjC,QAAQ,GAAGA,QAAQ,CAACkC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IACpD,IAAIc,UAAU,GAAG,CAAC;IAClB,IAAIlB,SAAS,GAAG,CAAC;IAEjB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAAChB,MAAM,EAAEmB,CAAC,EAAE,EAAE;MACnC,IAAIY,UAAU,GAAGf,KAAK,CAACG,CAAC,CAAC,CAACnB,MAAM,IAAIZ,YAAY,EAAE;QAC9CyB,SAAS,GAAGM,CAAC;QACb;MACJ;MACAY,UAAU,IAAIf,KAAK,CAACG,CAAC,CAAC,CAACnB,MAAM,GAAG,CAAC;IACrC;IAEA,OAAO;MACHkB,QAAQ,EAAE,UAAU;MACpBP,GAAG,KAAAT,MAAA,CAAKW,SAAS,GAAGD,UAAU,GAAG,CAAC,OAAI;MACtCoB,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,KAAK;MACbC,eAAe,EAAE,SAAS;MAC1BC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE;IAClB,CAAC;EACL,CAAC;EAED,IAAI,CAACpD,UAAU,EAAE;IACb;IACA,oBACIN,OAAA;MAAK2D,SAAS,EAAC,uFAAuF;MAAAC,QAAA,gBAClG5D,OAAA;QAAM2D,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAG,eACpDhE,OAAA,CAACF,uBAAuB;QAACmE,OAAO,EAAE7D;MAAS;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEd;EAEA,oBACIhE,OAAA;IAAK2D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3B5D,OAAA;MAAK2D,SAAS,EAAC,sCAAsC;MAAAC,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAGrEhE,OAAA;MACI2D,SAAS,4DAAApC,MAAA,CACLhB,cAAc,GACR,mDAAmD,GACnD,uCAAuC,CAC9C;MACH2D,UAAU,EAAExC,cAAe;MAC3ByC,WAAW,EAAEzB,eAAgB;MAC7B0B,WAAW,EAAEvB,eAAgB;MAC7BwB,MAAM,EAAExD,UAAW;MAAA+C,QAAA,GAGlBrD,cAAc,iBAAIP,OAAA;QAAKsE,KAAK,EAAEnB,qBAAqB,CAAC;MAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzDzD,cAAc,iBACXP,OAAA;QAAK2D,SAAS,EAAC,4FAA4F;QAAAC,QAAA,eACvG5D,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB5D,OAAA,CAACH,MAAM;YAAC8D,SAAS,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDhE,OAAA;YAAG2D,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5DhE,OAAA;YAAG2D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,eAGDhE,OAAA;QACIuE,GAAG,EAAE5D,WAAY;QACjBuC,KAAK,EAAE9C,QAAQ,IAAI,EAAG;QACtBoE,QAAQ,EAAExB,oBAAqB;QAC/ByB,WAAW,EAAC,yEAA6C;QACzDd,SAAS,EAAC,8GAA8G;QACxHW,KAAK,EAAE;UAAErC,UAAU,EAAE;QAAO;MAAE;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAGD,CAAC5D,QAAQ,iBACNJ,OAAA;QAAK2D,SAAS,EAAC,6FAA6F;QAAAC,QAAA,gBACxG5D,OAAA,CAACJ,SAAS;UAAC+D,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjChE,OAAA;UAAA4D,QAAA,EAAM;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGL5D,QAAQ,iBACLJ,OAAA;MAAK2D,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC7D5D,OAAA;QAAK2D,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DhE,OAAA,CAACF,uBAAuB;QAACmE,OAAO,EAAE7D;MAAS;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC7D,EAAA,CAnLIF,cAAc;AAAAyE,EAAA,GAAdzE,cAAc;AAqLpB,eAAeA,cAAc;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}