import { useState, useEffect, useRef } from "react";
import { ChevronDown } from "lucide-react";

const DropMenuBarAdmin = ({ options, placeholder = "Chọn một mục", selectedOption, onChange, className='' }) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef(null);
    const [p, setP] = useState(placeholder);

    const handleSelect = (option) => {
        onChange(option.code);
        setP(option.description);
        setIsOpen(false); // Đóng dropdown sau khi chọn
    };

    // Update placeholder text when selectedOption or options change
    useEffect(() => {
        if ((selectedOption || selectedOption === false) && options && options.length > 0) {
            const selectedItem = options.find((option) => option.code === selectedOption);
            if (selectedItem) {
                setP(selectedItem.description);
            }
        }
    }, [selectedOption, options]);

    // <PERSON>le click outside to close dropdown
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    return (
        <div ref={dropdownRef} className={`relative flex-1 ${className ? className : 'w-full'} text-md`}>
            {/* Nút chọn */}
            <button
                type="button"
                className="w-full bg-white border flex items-center justify-between border-gray-300 rounded py-1.5 px-2 focus:outline-none focus:ring-2 focus:ring-sky-500"
                onClick={() => setIsOpen(!isOpen)}
            >
                <span className={`text-[#303437] font-medium font-['Inter'] ${selectedOption === true ? "text-green-500" : selectedOption === false ? "text-red-500" : ""}`}>
                    {(selectedOption || selectedOption === false) ? p : placeholder}
                </span>
                <ChevronDown size={12} className={`transition-transform ${isOpen ? "rotate-180" : ""}`} />
            </button>

            {/* Danh sách dropdown */}
            {isOpen && (
                <ul className="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-lg shadow-lg z-50 overflow-y-auto hide-scrollbar max-h-60">
                    {options?.length > 0 ? (
                        options.map((option, index) => (
                            <li
                                key={index}
                                className="px-4 py-2 hover:bg-gray-200 cursor-pointer"
                                onClick={() => handleSelect(option)}
                            >
                                {option.description}
                            </li>
                        ))
                    ) : (
                        <li className="px-4 py-2 text-gray-500">Không có dữ liệu</li>
                    )}
                </ul>
            )}
        </div>
    );
};

export default DropMenuBarAdmin;
