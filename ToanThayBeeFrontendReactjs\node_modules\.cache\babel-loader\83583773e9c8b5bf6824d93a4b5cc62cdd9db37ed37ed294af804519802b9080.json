{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\ImageDropZone.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Trash2, ImagePlus, Upload } from \"lucide-react\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDropZone = _ref => {\n  _s();\n  let {\n    imageUrl,\n    onImageDrop,\n    onImageRemove,\n    content\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\");\n    if (draggedImage && onImageDrop) {\n      onImageDrop(draggedImage);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    // Chỉ tắt khi thực sự rời khỏi drop zone\n    if (!e.currentTarget.contains(e.relatedTarget)) {\n      setIsDraggingOver(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative rounded-lg p-4 transition-all duration-200 min-h-[60px] flex items-center\\n                    \".concat(isDraggingOver ? \"border-2 border-dashed border-blue-400 bg-blue-50\" : \"border border-gray-200 hover:border-blue-300 hover:bg-blue-25\"),\n      onDragOver: handleDragOver,\n      onDragEnter: handleDragEnter,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      children: isDraggingOver ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full text-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(Upload, {\n          className: \"w-8 h-8 text-blue-500 mx-auto mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-600 font-medium\",\n          children: \"Th\\u1EA3 \\u1EA3nh v\\xE0o \\u0111\\xE2y\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-400 text-sm\",\n          children: \"K\\xE9o \\u1EA3nh t\\u1EEB danh s\\xE1ch b\\xEAn ph\\u1EA3i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full\",\n        children: [/*#__PURE__*/_jsxDEV(LatexRenderer, {\n          text: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 25\n        }, this), !imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 flex items-center gap-2 text-gray-400 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(ImagePlus, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"K\\xE9o \\u1EA3nh v\\xE0o \\u0111\\xE2y \\u0111\\u1EC3 th\\xEAm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this), imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative group w-fit bg-gray-50 rounded-lg p-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: \"Attached image\",\n        className: \"rounded-md max-h-48 max-w-full object-contain\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n        onClick: e => {\n          e.stopPropagation();\n          onImageRemove === null || onImageRemove === void 0 ? void 0 : onImageRemove();\n        },\n        title: \"X\\xF3a \\u1EA3nh\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 text-xs text-gray-500 text-center\",\n        children: \"\\u1EA2nh \\u0111\\xE3 th\\xEAm \\u2022 Click v\\xE0o icon \\u0111\\u1EC3 x\\xF3a\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 9\n  }, this);\n};\n_s(ImageDropZone, \"VKW1ZLDpPe+zo2/qAoOWVX5YkVs=\");\n_c = ImageDropZone;\nexport default ImageDropZone;\nvar _c;\n$RefreshReg$(_c, \"ImageDropZone\");", "map": {"version": 3, "names": ["React", "useState", "Trash2", "ImagePlus", "Upload", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ImageDropZone", "_ref", "_s", "imageUrl", "onImageDrop", "onImageRemove", "content", "isDraggingOver", "setIsDraggingOver", "handleDrop", "e", "preventDefault", "stopPropagation", "draggedImage", "dataTransfer", "getData", "handleDragOver", "handleDragEnter", "types", "includes", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "className", "children", "concat", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "src", "alt", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/ImageDropZone.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Trash2, ImagePlus, Upload } from \"lucide-react\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\n\r\nconst ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove, content }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\");\r\n        if (draggedImage && onImageDrop) {\r\n            onImageDrop(draggedImage);\r\n        }\r\n    };\r\n\r\n    const handleDragOver = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n    };\r\n\r\n    const handleDragEnter = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (e.dataTransfer.types.includes('text/plain')) {\r\n            setIsDraggingOver(true);\r\n        }\r\n    };\r\n\r\n    const handleDragLeave = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        // Chỉ tắt khi thực sự rời khỏi drop zone\r\n        if (!e.currentTarget.contains(e.relatedTarget)) {\r\n            setIsDraggingOver(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"w-full space-y-3\">\r\n            {/* Content với drop zone */}\r\n            <div\r\n                className={`relative rounded-lg p-4 transition-all duration-200 min-h-[60px] flex items-center\r\n                    ${isDraggingOver\r\n                        ? \"border-2 border-dashed border-blue-400 bg-blue-50\"\r\n                        : \"border border-gray-200 hover:border-blue-300 hover:bg-blue-25\"\r\n                    }`}\r\n                onDragOver={handleDragOver}\r\n                onDragEnter={handleDragEnter}\r\n                onDragLeave={handleDragLeave}\r\n                onDrop={handleDrop}\r\n            >\r\n                {isDraggingOver ? (\r\n                    <div className=\"w-full text-center py-4\">\r\n                        <Upload className=\"w-8 h-8 text-blue-500 mx-auto mb-2\" />\r\n                        <p className=\"text-blue-600 font-medium\">Thả ảnh vào đây</p>\r\n                        <p className=\"text-blue-400 text-sm\">Kéo ảnh từ danh sách bên phải</p>\r\n                    </div>\r\n                ) : (\r\n                    <div className=\"w-full\">\r\n                        <LatexRenderer text={content} />\r\n                        {!imageUrl && (\r\n                            <div className=\"mt-2 flex items-center gap-2 text-gray-400 text-sm\">\r\n                                <ImagePlus className=\"w-4 h-4\" />\r\n                                <span>Kéo ảnh vào đây để thêm</span>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Hiển thị ảnh đã thêm */}\r\n            {imageUrl && (\r\n                <div className=\"relative group w-fit bg-gray-50 rounded-lg p-2\">\r\n                    <img\r\n                        src={imageUrl}\r\n                        alt=\"Attached image\"\r\n                        className=\"rounded-md max-h-48 max-w-full object-contain\"\r\n                    />\r\n                    <button\r\n                        className=\"absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\r\n                        onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            onImageRemove?.();\r\n                        }}\r\n                        title=\"Xóa ảnh\"\r\n                    >\r\n                        <div className=\"bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg\">\r\n                            <Trash2 className=\"w-4 h-4\" />\r\n                        </div>\r\n                    </button>\r\n                    <div className=\"mt-2 text-xs text-gray-500 text-center\">\r\n                        Ảnh đã thêm • Click vào icon để xóa\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ImageDropZone;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,SAAS,EAAEC,MAAM,QAAQ,cAAc;AACxD,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,aAAa,GAAGC,IAAA,IAAuD;EAAAC,EAAA;EAAA,IAAtD;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,aAAa;IAAEC;EAAQ,CAAC,GAAAL,IAAA;EACpE,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMgB,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBJ,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMK,YAAY,GAAGH,CAAC,CAACI,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,IAAIF,YAAY,IAAIT,WAAW,EAAE;MAC7BA,WAAW,CAACS,YAAY,CAAC;IAC7B;EACJ,CAAC;EAED,MAAMG,cAAc,GAAIN,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAMK,eAAe,GAAIP,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIF,CAAC,CAACI,YAAY,CAACI,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC7CX,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMY,eAAe,GAAIV,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB;IACA,IAAI,CAACF,CAAC,CAACW,aAAa,CAACC,QAAQ,CAACZ,CAAC,CAACa,aAAa,CAAC,EAAE;MAC5Cf,iBAAiB,CAAC,KAAK,CAAC;IAC5B;EACJ,CAAC;EAED,oBACIT,OAAA;IAAKyB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE7B1B,OAAA;MACIyB,SAAS,6GAAAE,MAAA,CACHnB,cAAc,GACV,mDAAmD,GACnD,+DAA+D,CAClE;MACPoB,UAAU,EAAEX,cAAe;MAC3BY,WAAW,EAAEX,eAAgB;MAC7BY,WAAW,EAAET,eAAgB;MAC7BU,MAAM,EAAErB,UAAW;MAAAgB,QAAA,EAElBlB,cAAc,gBACXR,OAAA;QAAKyB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACpC1B,OAAA,CAACH,MAAM;UAAC4B,SAAS,EAAC;QAAoC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDnC,OAAA;UAAGyB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5DnC,OAAA;UAAGyB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA6B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,gBAENnC,OAAA;QAAKyB,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACnB1B,OAAA,CAACF,aAAa;UAACsC,IAAI,EAAE7B;QAAQ;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/B,CAAC/B,QAAQ,iBACNJ,OAAA;UAAKyB,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBAC/D1B,OAAA,CAACJ,SAAS;YAAC6B,SAAS,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCnC,OAAA;YAAA0B,QAAA,EAAM;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGL/B,QAAQ,iBACLJ,OAAA;MAAKyB,SAAS,EAAC,gDAAgD;MAAAC,QAAA,gBAC3D1B,OAAA;QACIqC,GAAG,EAAEjC,QAAS;QACdkC,GAAG,EAAC,gBAAgB;QACpBb,SAAS,EAAC;MAA+C;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACFnC,OAAA;QACIyB,SAAS,EAAC,4FAA4F;QACtGc,OAAO,EAAG5B,CAAC,IAAK;UACZA,CAAC,CAACE,eAAe,CAAC,CAAC;UACnBP,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAG,CAAC;QACrB,CAAE;QACFkC,KAAK,EAAC,iBAAS;QAAAd,QAAA,eAEf1B,OAAA;UAAKyB,SAAS,EAAC,qEAAqE;UAAAC,QAAA,eAChF1B,OAAA,CAACL,MAAM;YAAC8B,SAAS,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACTnC,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAExD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAChC,EAAA,CAhGIF,aAAa;AAAAwC,EAAA,GAAbxC,aAAa;AAkGnB,eAAeA,aAAa;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}