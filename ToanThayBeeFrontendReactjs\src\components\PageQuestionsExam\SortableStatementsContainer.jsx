import React from 'react';
import { useDispatch, useSelector } from "react-redux";
import { reorderStatements } from "src/features/questionsExam/questionsExamSlice";
import SortableStatementItem from "./SortableStatementItem";
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import LatexRenderer from "../latex/RenderLatex";

const SortableStatementsContainer = ({ question }) => {
    const dispatch = useDispatch();
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const prefixTN = ["A.", "B.", "C.", "D."];
    const prefixDS = ["a)", "b)", "c)", "d)"];

    const handleDragEnd = (event) => {
        const { active, over } = event;

        if (active.id !== over?.id) {
            const oldIndex = question.statements.findIndex((item, idx) =>
                `${item.id || idx}` === active.id
            );
            const newIndex = question.statements.findIndex((item, idx) =>
                `${item.id || idx}` === over.id
            );

            if (oldIndex !== -1 && newIndex !== -1) {
                dispatch(reorderStatements({
                    questionId: question.id,
                    oldIndex,
                    newIndex
                }));
            }
        }
    };

    const getPrefix = (index) => {
        if (question.typeOfQuestion === "TN") {
            return prefixTN[index] || `${String.fromCharCode(65 + index)}.`;
        } else if (question.typeOfQuestion === "DS") {
            return prefixDS[index] || `${String.fromCharCode(97 + index)})`;
        }
        return `${index + 1}.`;
    };

    if (question.typeOfQuestion === "TLN") {
        return (
            <div className="text-gray-800 mt-2">
                <span className="font-semibold">Đáp án: </span>
                <span>{question.correctAnswer}</span>
            </div>
        );
    }

    if (!question.statements || question.statements.length === 0) {
        return null;
    }

    return (
        <div className="mt-2">
            <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
            >
                <SortableContext
                    items={question.statements.map((item, idx) => `${item.id || idx}`)}
                    strategy={verticalListSortingStrategy}
                >
                    <div className="space-y-1">
                        {question.statements.map((item, idx) => (
                            <SortableStatementItem
                                key={`${item.id || idx}`}
                                statement={item}
                                index={idx}
                                prefix={getPrefix(idx)}
                                isCorrect={item.isCorrect}
                                questionType={question.typeOfQuestion}
                            />
                        ))}
                    </div>
                </SortableContext>
            </DndContext>
        </div>
    );
};

export default SortableStatementsContainer;
