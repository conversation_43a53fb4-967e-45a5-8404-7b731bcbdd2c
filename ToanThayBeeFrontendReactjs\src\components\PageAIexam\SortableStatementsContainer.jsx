import React from 'react';
import { useDispatch, useSelector } from "react-redux";
import { reorderStatements } from "src/features/examAI/examAISlice";
import SortableStatementItem from "./SortableStatementItem";
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import LatexRenderer from "../latex/RenderLatex";
import { setQuestionsEdited } from "src/features/examAI/examAISlice";
import { Trash2 } from "lucide-react";
import ImageDropZone from './ImageDropZone';

const SortableStatementsContainer = ({ question }) => {
    const dispatch = useDispatch();
    const { isAddImage } = useSelector((state) => state.examAI);
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const prefixTN = ["A.", "B.", "C.", "D."];
    const prefixDS = ["a)", "b)", "c)", "d)"];

    const handleDragEnd = (event) => {
        const { active, over } = event;

        if (active.id !== over?.id) {
            const oldIndex = question.statement1s.findIndex((item, idx) =>
                `${item.id || idx}` === active.id
            );
            const newIndex = question.statement1s.findIndex((item, idx) =>
                `${item.id || idx}` === over.id
            );

            if (oldIndex !== -1 && newIndex !== -1) {
                dispatch(reorderStatements({
                    questionId: question.id,
                    oldIndex,
                    newIndex
                }));
            }
        }
    };

    const getPrefix = (index) => {
        if (question.typeOfQuestion === "TN") {
            return prefixTN[index] || `${String.fromCharCode(65 + index)}.`;
        } else if (question.typeOfQuestion === "DS") {
            return prefixDS[index] || `${String.fromCharCode(97 + index)})`;
        }
        return `${index + 1}.`;
    };

    if (question.typeOfQuestion === "TLN") {
        return (
            <div className="text-gray-800">
                <span className="font-semibold">Đáp án: </span>
                <span>{question.correctAnswer}</span>
            </div>
        );
    }

    if (!question.statement1s || question.statement1s.length === 0) {
        return null;
    }

    if (isAddImage) {
        return (
            <div className="space-y-1">
                {question.statement1s.map((item, idx) => (
                    <>
                        <div
                            key={`${item.id || idx}`}
                            className="flex items-center gap-2 group relative "
                        >
                            <ImageDropZone
                                content={getPrefix(idx) + " " + item.content}
                                imageUrl={item.imageUrl}
                                onImageDrop={(newUrl) => {
                                    dispatch(
                                        setQuestionsEdited({
                                            ...question,
                                            statement1s: question.statement1s.map((statement, index) =>
                                                index === idx
                                                    ? { ...statement, imageUrl: newUrl }
                                                    : statement
                                            ),
                                        })
                                    );
                                }}
                                onImageRemove={() => {
                                    dispatch(
                                        setQuestionsEdited({
                                            ...question,
                                            statement1s: question.statement1s.map((statement, index) =>
                                                index === idx
                                                    ? { ...statement, imageUrl: null }
                                                    : statement
                                            ),
                                        })
                                    );
                                }}
                            >
                                <LatexRenderer text={item.content} />
                            </ImageDropZone>
                        </div>

                    </>
                ))}
            </div>
        );
    }

    return (
        <div>
            <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
            >
                <SortableContext
                    items={question.statement1s.map((item, idx) => `${item.id || idx}`)}
                    strategy={verticalListSortingStrategy}
                >
                    <div className="space-y-1">
                        {question.statement1s.map((item, idx) => (
                            <SortableStatementItem
                                key={`${item.id || idx}`}
                                statement={item}
                                index={idx}
                                prefix={getPrefix(idx)}
                                isCorrect={item.isCorrect}
                                questionType={question.typeOfQuestion}
                            />
                        ))}
                    </div>
                </SortableContext>
            </DndContext>
        </div>
    );
};

export default SortableStatementsContainer;
