import { useSelector } from "react-redux";

const TimeSlots = () => {
    const { timeSlots } = useSelector((state) => state.calendar);

    return (
        <div className="col-span-1 border-r border-gray-200 sticky left-0 bg-white z-30">
            {timeSlots.map((time, index) => (
                <div key={index} className="h-12 relative flex justify-start items-end first:border-t border-gray-200">
                    <div className="w-1 border-b border-gray-200 ml-auto"></div> {/* 👈 lệch phải nhờ ml-auto */}
                    <span className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 text-xs sm:text-sm text-gray-700">
                        {index === 0 ? null : time}
                    </span>
                </div>
            ))}
        </div>
    );
}

export default TimeSlots;