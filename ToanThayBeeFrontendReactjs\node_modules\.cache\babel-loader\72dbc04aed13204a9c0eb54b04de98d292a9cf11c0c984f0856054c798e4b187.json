{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\AiExamDetailAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, use } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport { fetchQuestionsByExamId } from \"src/features/examAI/examAISlice\";\nimport Header from \"src/components/PageAIexam/Header\";\nimport LeftContent from \"src/components/PageAIexam/LeftContent\";\nimport RightContent from \"src/components/PageAIexam/RightContent\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditExamAIPage = () => {\n  _s();\n  const {\n    examId\n  } = useParams();\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [editedText, setEditedText] = useState(\"\");\n  const {\n    exam,\n    questions\n  } = useSelector(state => state.examAI);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    var _questions$;\n    if (!examId && questions.length > 0) return;\n    setSelectedQuestion(questions[0]);\n    setEditedText(((_questions$ = questions[0]) === null || _questions$ === void 0 ? void 0 : _questions$.content) || \"\");\n  }, [questions, examId]);\n  useEffect(() => {\n    if (examId) {\n      dispatch(fetchQuestionsByExamId(examId));\n      dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\n    }\n  }, [examId, dispatch]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full bg-gray-50 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      title: exam === null || exam === void 0 ? void 0 : exam.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full h-full bg-gray-50 flex mt-16\",\n      children: [/*#__PURE__*/_jsxDEV(LeftContent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(RightContent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 9\n  }, this);\n};\n_s(EditExamAIPage, \"6rc959FuHIF9c2X2QgaZhAAMPqw=\", false, function () {\n  return [useParams, useSelector, useDispatch];\n});\n_c = EditExamAIPage;\nexport default EditExamAIPage;\nvar _c;\n$RefreshReg$(_c, \"EditExamAIPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "use", "useSelector", "useDispatch", "useParams", "fetchQuestionsByExamId", "Header", "LeftContent", "RightContent", "fetchCodesByType", "jsxDEV", "_jsxDEV", "EditExamAIPage", "_s", "examId", "selectedQuestion", "setSelectedQuestion", "editedText", "setEditedText", "exam", "questions", "state", "examAI", "dispatch", "_questions$", "length", "content", "className", "children", "title", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/AiExamDetailAdmin.jsx"], "sourcesContent": ["import React, { useState, useEffect, use } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { fetchQuestionsByExamId } from \"src/features/examAI/examAISlice\";\r\nimport Header from \"src/components/PageAIexam/Header\";\r\nimport LeftContent from \"src/components/PageAIexam/LeftContent\";\r\nimport RightContent from \"src/components/PageAIexam/RightContent\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\n\r\nconst EditExamAIPage = () => {\r\n    const { examId } = useParams();\r\n    const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n    const [editedText, setEditedText] = useState(\"\");\r\n    const { exam, questions } = useSelector((state) => state.examAI);\r\n\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        if (!examId && questions.length > 0) return\r\n        setSelectedQuestion(questions[0]);\r\n        setEditedText(questions[0]?.content || \"\");\r\n    }, [questions, examId]);\r\n\r\n    useEffect(() => {\r\n        if (examId) {\r\n            dispatch(fetchQuestionsByExamId(examId))\r\n            dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\r\n        }\r\n    }, [examId, dispatch]);\r\n\r\n    return (\r\n        <div className=\"w-full bg-gray-50 flex flex-col\">\r\n            <Header title={exam?.name} />\r\n            <div className=\"w-full h-full bg-gray-50 flex mt-16\">\r\n                {/* LEFT: Danh sách câu hỏi */}\r\n                <LeftContent />\r\n\r\n                {/* RIGHT: Form chỉnh sửa */}\r\n                <RightContent />\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditExamAIPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,OAAO;AACvD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,OAAOC,MAAM,MAAM,kCAAkC;AACrD,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,SAASC,gBAAgB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAO,CAAC,GAAGV,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM;IAAEoB,IAAI;IAAEC;EAAU,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEhE,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9BH,SAAS,CAAC,MAAM;IAAA,IAAAwB,WAAA;IACZ,IAAI,CAACV,MAAM,IAAIM,SAAS,CAACK,MAAM,GAAG,CAAC,EAAE;IACrCT,mBAAmB,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC;IACjCF,aAAa,CAAC,EAAAM,WAAA,GAAAJ,SAAS,CAAC,CAAC,CAAC,cAAAI,WAAA,uBAAZA,WAAA,CAAcE,OAAO,KAAI,EAAE,CAAC;EAC9C,CAAC,EAAE,CAACN,SAAS,EAAEN,MAAM,CAAC,CAAC;EAEvBd,SAAS,CAAC,MAAM;IACZ,IAAIc,MAAM,EAAE;MACRS,QAAQ,CAAClB,sBAAsB,CAACS,MAAM,CAAC,CAAC;MACxCS,QAAQ,CAACd,gBAAgB,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;IACvF;EACJ,CAAC,EAAE,CAACK,MAAM,EAAES,QAAQ,CAAC,CAAC;EAEtB,oBACIZ,OAAA;IAAKgB,SAAS,EAAC,iCAAiC;IAAAC,QAAA,gBAC5CjB,OAAA,CAACL,MAAM;MAACuB,KAAK,EAAEV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7BvB,OAAA;MAAKgB,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAEhDjB,OAAA,CAACJ,WAAW;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGfvB,OAAA,CAACH,YAAY;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACrB,EAAA,CAjCID,cAAc;EAAA,QACGR,SAAS,EAGAF,WAAW,EAEtBC,WAAW;AAAA;AAAAgC,EAAA,GAN1BvB,cAAc;AAmCpB,eAAeA,cAAc;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}