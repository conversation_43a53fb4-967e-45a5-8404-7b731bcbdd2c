[{"C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx": "5", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx": "7", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx": "8", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx": "9", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx": "10", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx": "11", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx": "12", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "13", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "14", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx": "15", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx": "16", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "17", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "18", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "19", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "20", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx": "21", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "22", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "23", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx": "24", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js": "25", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js": "26", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js": "27", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js": "28", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js": "29", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js": "30", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js": "31", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js": "32", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js": "33", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js": "34", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js": "35", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js": "36", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js": "37", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js": "38", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js": "39", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx": "40", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx": "41", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx": "42", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx": "43", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx": "44", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx": "45", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx": "46", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "47", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx": "48", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx": "49", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx": "50", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx": "51", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx": "52", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx": "53", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx": "55", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx": "56", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx": "57", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx": "58", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx": "59", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx": "60", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx": "61", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx": "63", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx": "64", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx": "65", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx": "66", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx": "67", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx": "68", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx": "69", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx": "70", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx": "71", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx": "72", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx": "73", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx": "74", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx": "75", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx": "76", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js": "77", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx": "78", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx": "79", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx": "80", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js": "81", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx": "82", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx": "83", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js": "84", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx": "85", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "86", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx": "87", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx": "88", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx": "89", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx": "90", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx": "91", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx": "92", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx": "93", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx": "94", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx": "95", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx": "96", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js": "97", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx": "98", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx": "99", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js": "100", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js": "101", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js": "102", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx": "103", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js": "104", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js": "105", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx": "106", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx": "107", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx": "108", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx": "109", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx": "110", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx": "111", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx": "112", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx": "113", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx": "114", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx": "115", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx": "116", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js": "117", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js": "118", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js": "119", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx": "120", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx": "121", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx": "122", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx": "123", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx": "124", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx": "125", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx": "126", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js": "127", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx": "128", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx": "129", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx": "130", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "131", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js": "132", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx": "133", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx": "134", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx": "135", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx": "136", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx": "137", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx": "138", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx": "139", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx": "140", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx": "141", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx": "142", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js": "143", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js": "144", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx": "145", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx": "146", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx": "147", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx": "148", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx": "149", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx": "150", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx": "151", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx": "152", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx": "153", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "154", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "155", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx": "156", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx": "157", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx": "158", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx": "159", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx": "160", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx": "161", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js": "162", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js": "163", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx": "164", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "165", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx": "166", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx": "167", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js": "168", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx": "169", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx": "170", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx": "171", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx": "172", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx": "173", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx": "174", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx": "175", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx": "176", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx": "177", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx": "178", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx": "179", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx": "180", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx": "181", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx": "182", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx": "183", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx": "184", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "185", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx": "186", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx": "187", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx": "188", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx": "189", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx": "190", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx": "191", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx": "192", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx": "193", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx": "194", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx": "195", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx": "196", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx": "197", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx": "198", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx": "199", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx": "200", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx": "201", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js": "202", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx": "203", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx": "204", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js": "205", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx": "206", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js": "207", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx": "208", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx": "209", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx": "210", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx": "211", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "212", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js": "213", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js": "214", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js": "215", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx": "216", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx": "217", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "218", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "219", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx": "220", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js": "221", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "222", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "223", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js": "224", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "225", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx": "226", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx": "227", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx": "228", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx": "229", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js": "230", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js": "231", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx": "232", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "233", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx": "234", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "235", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "236", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js": "237", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "238", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "239", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "240", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js": "241", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx": "242", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx": "243", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx": "244", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx": "245", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx": "246", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx": "247", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js": "248", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js": "249", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx": "250", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx": "251", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx": "252", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx": "253", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js": "254", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx": "255", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx": "256", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js": "257", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx": "258", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js": "259", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js": "260", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx": "261", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx": "262", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js": "263", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js": "264", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx": "265", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx": "266", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx": "267", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js": "268", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx": "269", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js": "270", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx": "271", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx": "272", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx": "273", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js": "274", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx": "275", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js": "276", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx": "277", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx": "278", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx": "279", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx": "280", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx": "281", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx": "282", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx": "283", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js": "284", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx": "285", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js": "286", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx": "287", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js": "288", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js": "289", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx": "290", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx": "291", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx": "292", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx": "293", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx": "294", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx": "295", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx": "296", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx": "297", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx": "298", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx": "299", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx": "300", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx": "301", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx": "302", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx": "303", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js": "304", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\ImageDropZone.jsx": "305", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SolutionEditor.jsx": "306", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StaffManagement.jsx": "307", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\dashboard\\dashboardSlice.js": "308", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\dashboardApi.js": "309", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\layout\\AdminLayout.jsx": "310", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AddExamAdmin.jsx": "311", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\RightContent.jsx": "312", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\LeftContent.jsx": "313", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\addExam\\addExamSlice.js": "314", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\CompactStepHeader.jsx": "315", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\NavigateBar.jsx": "316", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\hooks\\useDebouncedEffect.js": "317", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddImagesModal.jsx": "318", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\SolutionEditor.jsx": "319", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\LeftContent.jsx": "320", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\RightContent.jsx": "321", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionsExam\\questionsExamSlice.js": "322", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\QuestionView.jsx": "323", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\QuestionContent.jsx": "324", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableQuestionItem.jsx": "325", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableStatementsContainer.jsx": "326", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableStatementItem.jsx": "327"}, {"size": 837, "mtime": 1748800674146, "results": "328", "hashOfConfig": "329"}, {"size": 375, "mtime": 1744531393988, "results": "330", "hashOfConfig": "329"}, {"size": 12682, "mtime": 1752000863635, "results": "331", "hashOfConfig": "329"}, {"size": 2856, "mtime": 1752228909529, "results": "332", "hashOfConfig": "329"}, {"size": 1183, "mtime": 1751787344260, "results": "333", "hashOfConfig": "329"}, {"size": 7439, "mtime": 1751975227633, "results": "334", "hashOfConfig": "329"}, {"size": 4828, "mtime": 1746378664900, "results": "335", "hashOfConfig": "329"}, {"size": 2152, "mtime": 1751276563455, "results": "336", "hashOfConfig": "329"}, {"size": 1271, "mtime": 1744531393978, "results": "337", "hashOfConfig": "329"}, {"size": 21516, "mtime": 1751827522381, "results": "338", "hashOfConfig": "329"}, {"size": 11683, "mtime": 1748250288653, "results": "339", "hashOfConfig": "329"}, {"size": 1979, "mtime": 1749722105422, "results": "340", "hashOfConfig": "329"}, {"size": 551, "mtime": 1744531393982, "results": "341", "hashOfConfig": "329"}, {"size": 275, "mtime": 1748215697376, "results": "342", "hashOfConfig": "329"}, {"size": 1739, "mtime": 1749721392840, "results": "343", "hashOfConfig": "329"}, {"size": 44964, "mtime": 1750904385044, "results": "344", "hashOfConfig": "329"}, {"size": 9223, "mtime": 1751743443365, "results": "345", "hashOfConfig": "329"}, {"size": 350, "mtime": 1750322483573, "results": "346", "hashOfConfig": "329"}, {"size": 19813, "mtime": 1750323348863, "results": "347", "hashOfConfig": "329"}, {"size": 1305, "mtime": 1752228868900, "results": "348", "hashOfConfig": "329"}, {"size": 2397, "mtime": 1752003962009, "results": "349", "hashOfConfig": "329"}, {"size": 1898, "mtime": 1750325540002, "results": "350", "hashOfConfig": "329"}, {"size": 348, "mtime": 1749202010110, "results": "351", "hashOfConfig": "329"}, {"size": 3322, "mtime": 1751743523736, "results": "352", "hashOfConfig": "329"}, {"size": 9893, "mtime": 1750849306726, "results": "353", "hashOfConfig": "329"}, {"size": 733, "mtime": 1751439773864, "results": "354", "hashOfConfig": "329"}, {"size": 7352, "mtime": 1751971764594, "results": "355", "hashOfConfig": "329"}, {"size": 3345, "mtime": 1751786228120, "results": "356", "hashOfConfig": "329"}, {"size": 3523, "mtime": 1752112732971, "results": "357", "hashOfConfig": "329"}, {"size": 7806, "mtime": 1752228844841, "results": "358", "hashOfConfig": "329"}, {"size": 17715, "mtime": 1751542931344, "results": "359", "hashOfConfig": "329"}, {"size": 14134, "mtime": 1752003896697, "results": "360", "hashOfConfig": "329"}, {"size": 1380, "mtime": 1744531393975, "results": "361", "hashOfConfig": "329"}, {"size": 2737, "mtime": 1750295163218, "results": "362", "hashOfConfig": "329"}, {"size": 5741, "mtime": 1751849172659, "results": "363", "hashOfConfig": "329"}, {"size": 5921, "mtime": 1751786223198, "results": "364", "hashOfConfig": "329"}, {"size": 2480, "mtime": 1747721626218, "results": "365", "hashOfConfig": "329"}, {"size": 5150, "mtime": 1748398556383, "results": "366", "hashOfConfig": "329"}, {"size": 1337, "mtime": 1747720637516, "results": "367", "hashOfConfig": "329"}, {"size": 690, "mtime": 1744531393950, "results": "368", "hashOfConfig": "329"}, {"size": 1339, "mtime": 1751554069606, "results": "369", "hashOfConfig": "329"}, {"size": 673, "mtime": 1744531393976, "results": "370", "hashOfConfig": "329"}, {"size": 1495, "mtime": 1751362330062, "results": "371", "hashOfConfig": "329"}, {"size": 2276, "mtime": 1751554069606, "results": "372", "hashOfConfig": "329"}, {"size": 1151, "mtime": 1749730250381, "results": "373", "hashOfConfig": "329"}, {"size": 3200, "mtime": 1744531393954, "results": "374", "hashOfConfig": "329"}, {"size": 3255, "mtime": 1752047065950, "results": "375", "hashOfConfig": "329"}, {"size": 635, "mtime": 1744531393961, "results": "376", "hashOfConfig": "329"}, {"size": 1228, "mtime": 1751361437565, "results": "377", "hashOfConfig": "329"}, {"size": 4288, "mtime": 1751977780456, "results": "378", "hashOfConfig": "329"}, {"size": 7391, "mtime": 1751977756680, "results": "379", "hashOfConfig": "329"}, {"size": 1709, "mtime": 1751982367850, "results": "380", "hashOfConfig": "329"}, {"size": 10068, "mtime": 1752112776538, "results": "381", "hashOfConfig": "329"}, {"size": 5752, "mtime": 1751276751884, "results": "382", "hashOfConfig": "329"}, {"size": 28691, "mtime": 1744958278477, "results": "383", "hashOfConfig": "329"}, {"size": 19936, "mtime": 1748984865157, "results": "384", "hashOfConfig": "329"}, {"size": 5905, "mtime": 1751977734164, "results": "385", "hashOfConfig": "329"}, {"size": 8616, "mtime": 1751977877203, "results": "386", "hashOfConfig": "329"}, {"size": 911, "mtime": 1744531393943, "results": "387", "hashOfConfig": "329"}, {"size": 3767, "mtime": 1752059450460, "results": "388", "hashOfConfig": "329"}, {"size": 31136, "mtime": 1751782971202, "results": "389", "hashOfConfig": "329"}, {"size": 9670, "mtime": 1752000747881, "results": "390", "hashOfConfig": "329"}, {"size": 1574, "mtime": 1744531393960, "results": "391", "hashOfConfig": "329"}, {"size": 7401, "mtime": 1747223318342, "results": "392", "hashOfConfig": "329"}, {"size": 2914, "mtime": 1747223318342, "results": "393", "hashOfConfig": "329"}, {"size": 28398, "mtime": 1749425223637, "results": "394", "hashOfConfig": "329"}, {"size": 17584, "mtime": 1750758162845, "results": "395", "hashOfConfig": "329"}, {"size": 17849, "mtime": 1752133016431, "results": "396", "hashOfConfig": "329"}, {"size": 1734, "mtime": 1744531393948, "results": "397", "hashOfConfig": "329"}, {"size": 6857, "mtime": 1751977816258, "results": "398", "hashOfConfig": "329"}, {"size": 5637, "mtime": 1747254491214, "results": "399", "hashOfConfig": "329"}, {"size": 22219, "mtime": 1752133135691, "results": "400", "hashOfConfig": "329"}, {"size": 10256, "mtime": 1744531393939, "results": "401", "hashOfConfig": "329"}, {"size": 1875, "mtime": 1750960485529, "results": "402", "hashOfConfig": "329"}, {"size": 19265, "mtime": 1751742523955, "results": "403", "hashOfConfig": "329"}, {"size": 6162, "mtime": 1748250288622, "results": "404", "hashOfConfig": "329"}, {"size": 3022, "mtime": 1747719253353, "results": "405", "hashOfConfig": "329"}, {"size": 7915, "mtime": 1748392811401, "results": "406", "hashOfConfig": "329"}, {"size": 6118, "mtime": 1751977901342, "results": "407", "hashOfConfig": "329"}, {"size": 3490, "mtime": 1749748056906, "results": "408", "hashOfConfig": "329"}, {"size": 935, "mtime": 1745405710864, "results": "409", "hashOfConfig": "329"}, {"size": 949, "mtime": 1747223318316, "results": "410", "hashOfConfig": "329"}, {"size": 3267, "mtime": 1747354362354, "results": "411", "hashOfConfig": "329"}, {"size": 3005, "mtime": 1750849360884, "results": "412", "hashOfConfig": "329"}, {"size": 4276, "mtime": 1751828500143, "results": "413", "hashOfConfig": "329"}, {"size": 2380, "mtime": 1744531393947, "results": "414", "hashOfConfig": "329"}, {"size": 2201, "mtime": 1744531393951, "results": "415", "hashOfConfig": "329"}, {"size": 14316, "mtime": 1749895693266, "results": "416", "hashOfConfig": "329"}, {"size": 1990, "mtime": 1744531393948, "results": "417", "hashOfConfig": "329"}, {"size": 841, "mtime": 1748984865154, "results": "418", "hashOfConfig": "329"}, {"size": 5565, "mtime": 1745690690469, "results": "419", "hashOfConfig": "329"}, {"size": 2295, "mtime": 1747223318353, "results": "420", "hashOfConfig": "329"}, {"size": 3146, "mtime": 1744531393940, "results": "421", "hashOfConfig": "329"}, {"size": 4074, "mtime": 1747223318326, "results": "422", "hashOfConfig": "329"}, {"size": 2787, "mtime": 1750173366905, "results": "423", "hashOfConfig": "329"}, {"size": 10634, "mtime": 1745483150534, "results": "424", "hashOfConfig": "425"}, {"size": 3707, "mtime": 1749722678323, "results": "426", "hashOfConfig": "329"}, {"size": 6034, "mtime": 1748364026312, "results": "427", "hashOfConfig": "329"}, {"size": 4147, "mtime": 1749731674278, "results": "428", "hashOfConfig": "329"}, {"size": 822, "mtime": 1751276455614, "results": "429", "hashOfConfig": "329"}, {"size": 3280, "mtime": 1752070395507, "results": "430", "hashOfConfig": "329"}, {"size": 297, "mtime": 1744531393989, "results": "431", "hashOfConfig": "329"}, {"size": 313, "mtime": 1744531393940, "results": "432", "hashOfConfig": "329"}, {"size": 4755, "mtime": 1751305471358, "results": "433", "hashOfConfig": "329"}, {"size": 1946, "mtime": 1751971622958, "results": "434", "hashOfConfig": "329"}, {"size": 993, "mtime": 1747223318349, "results": "435", "hashOfConfig": "329"}, {"size": 475, "mtime": 1748984865156, "results": "436", "hashOfConfig": "329"}, {"size": 902, "mtime": 1747223318353, "results": "437", "hashOfConfig": "329"}, {"size": 3053, "mtime": 1744531393946, "results": "438", "hashOfConfig": "329"}, {"size": 49554, "mtime": 1750767145500, "results": "439", "hashOfConfig": "329"}, {"size": 3402, "mtime": 1748781512174, "results": "440", "hashOfConfig": "329"}, {"size": 4872, "mtime": 1747223318349, "results": "441", "hashOfConfig": "329"}, {"size": 1641, "mtime": 1751827507517, "results": "442", "hashOfConfig": "329"}, {"size": 2297, "mtime": 1744531393945, "results": "443", "hashOfConfig": "329"}, {"size": 2193, "mtime": 1747223318349, "results": "444", "hashOfConfig": "329"}, {"size": 1359, "mtime": 1747223318349, "results": "445", "hashOfConfig": "329"}, {"size": 826, "mtime": 1748398318309, "results": "446", "hashOfConfig": "329"}, {"size": 2301, "mtime": 1751847942058, "results": "447", "hashOfConfig": "329"}, {"size": 1050, "mtime": 1751740167834, "results": "448", "hashOfConfig": "329"}, {"size": 4921, "mtime": 1747223318325, "results": "449", "hashOfConfig": "329"}, {"size": 12221, "mtime": 1751982174489, "results": "450", "hashOfConfig": "329"}, {"size": 411, "mtime": 1744531393940, "results": "451", "hashOfConfig": "329"}, {"size": 2290, "mtime": 1744531393963, "results": "452", "hashOfConfig": "329"}, {"size": 1219, "mtime": 1747467640276, "results": "453", "hashOfConfig": "329"}, {"size": 2003, "mtime": 1744531393970, "results": "454", "hashOfConfig": "329"}, {"size": 2166, "mtime": 1744531393969, "results": "455", "hashOfConfig": "329"}, {"size": 22749, "mtime": 1752066755960, "results": "456", "hashOfConfig": "329"}, {"size": 8372, "mtime": 1752047784140, "results": "457", "hashOfConfig": "329"}, {"size": 3094, "mtime": 1744531393970, "results": "458", "hashOfConfig": "329"}, {"size": 10222, "mtime": 1752047795777, "results": "459", "hashOfConfig": "329"}, {"size": 503, "mtime": 1744531393949, "results": "460", "hashOfConfig": "329"}, {"size": 394, "mtime": 1752228035461, "results": "461", "hashOfConfig": "329"}, {"size": 7876, "mtime": 1747223318342, "results": "462", "hashOfConfig": "329"}, {"size": 4922, "mtime": 1748329867180, "results": "463", "hashOfConfig": "329"}, {"size": 19965, "mtime": 1750900200879, "results": "464", "hashOfConfig": "329"}, {"size": 20555, "mtime": 1748250288625, "results": "465", "hashOfConfig": "329"}, {"size": 1337, "mtime": 1744531393967, "results": "466", "hashOfConfig": "425"}, {"size": 5412, "mtime": 1747223318349, "results": "467", "hashOfConfig": "329"}, {"size": 2938, "mtime": 1747223318353, "results": "468", "hashOfConfig": "329"}, {"size": 3182, "mtime": 1747223318353, "results": "469", "hashOfConfig": "329"}, {"size": 2928, "mtime": 1747223318349, "results": "470", "hashOfConfig": "329"}, {"size": 1885, "mtime": 1747354661883, "results": "471", "hashOfConfig": "329"}, {"size": 1345, "mtime": 1749697625937, "results": "472", "hashOfConfig": "329"}, {"size": 4099, "mtime": 1749731407409, "results": "473", "hashOfConfig": "329"}, {"size": 1576, "mtime": 1751811065450, "results": "474", "hashOfConfig": "329"}, {"size": 6380, "mtime": 1747361017417, "results": "475", "hashOfConfig": "329"}, {"size": 2600, "mtime": 1748876218633, "results": "476", "hashOfConfig": "329"}, {"size": 11541, "mtime": 1750900175948, "results": "477", "hashOfConfig": "329"}, {"size": 3297, "mtime": 1744531393957, "results": "478", "hashOfConfig": "329"}, {"size": 31011, "mtime": 1750902784209, "results": "479", "hashOfConfig": "329"}, {"size": 14565, "mtime": 1750819864802, "results": "480", "hashOfConfig": "329"}, {"size": 1205, "mtime": 1748984865161, "results": "481", "hashOfConfig": "329"}, {"size": 12148, "mtime": 1748250288636, "results": "482", "hashOfConfig": "329"}, {"size": 7688, "mtime": 1747223318312, "results": "483", "hashOfConfig": "329"}, {"size": 8344, "mtime": 1745507778499, "results": "484", "hashOfConfig": "329"}, {"size": 8025, "mtime": 1745507836095, "results": "485", "hashOfConfig": "329"}, {"size": 6988, "mtime": 1747223318344, "results": "486", "hashOfConfig": "329"}, {"size": 7719, "mtime": 1745499803667, "results": "487", "hashOfConfig": "329"}, {"size": 8378, "mtime": 1747223318344, "results": "488", "hashOfConfig": "329"}, {"size": 7254, "mtime": 1747223318344, "results": "489", "hashOfConfig": "329"}, {"size": 1770, "mtime": 1751850036982, "results": "490", "hashOfConfig": "329"}, {"size": 11464, "mtime": 1745500164258, "results": "491", "hashOfConfig": "329"}, {"size": 4650, "mtime": 1745508333678, "results": "492", "hashOfConfig": "329"}, {"size": 13822, "mtime": 1748250288625, "results": "493", "hashOfConfig": "329"}, {"size": 8599, "mtime": 1747223318326, "results": "494", "hashOfConfig": "329"}, {"size": 9774, "mtime": 1747223318326, "results": "495", "hashOfConfig": "329"}, {"size": 7914, "mtime": 1747223318326, "results": "496", "hashOfConfig": "329"}, {"size": 10728, "mtime": 1749548057374, "results": "497", "hashOfConfig": "329"}, {"size": 18582, "mtime": 1749747601919, "results": "498", "hashOfConfig": "329"}, {"size": 8954, "mtime": 1749810779685, "results": "499", "hashOfConfig": "329"}, {"size": 3429, "mtime": 1745682027607, "results": "500", "hashOfConfig": "329"}, {"size": 2298, "mtime": 1749802834779, "results": "501", "hashOfConfig": "329"}, {"size": 823, "mtime": 1748779533260, "results": "502", "hashOfConfig": "329"}, {"size": 1380, "mtime": 1745682047729, "results": "503", "hashOfConfig": "329"}, {"size": 1145, "mtime": 1747902858133, "results": "504", "hashOfConfig": "329"}, {"size": 1118, "mtime": 1745682069045, "results": "505", "hashOfConfig": "329"}, {"size": 978, "mtime": 1747902858130, "results": "506", "hashOfConfig": "329"}, {"size": 1781, "mtime": 1745682059432, "results": "507", "hashOfConfig": "425"}, {"size": 10563, "mtime": 1748585335221, "results": "508", "hashOfConfig": "329"}, {"size": 3574, "mtime": 1746378664904, "results": "509", "hashOfConfig": "329"}, {"size": 4389, "mtime": 1749980511142, "results": "510", "hashOfConfig": "329"}, {"size": 4914, "mtime": 1750326838086, "results": "511", "hashOfConfig": "329"}, {"size": 6418, "mtime": 1750326903479, "results": "512", "hashOfConfig": "329"}, {"size": 1020, "mtime": 1745682406063, "results": "513", "hashOfConfig": "329"}, {"size": 2723, "mtime": 1749810234131, "results": "514", "hashOfConfig": "329"}, {"size": 6116, "mtime": 1746378664905, "results": "515", "hashOfConfig": "329"}, {"size": 1565, "mtime": 1747223318344, "results": "516", "hashOfConfig": "329"}, {"size": 1624, "mtime": 1745689590880, "results": "517", "hashOfConfig": "329"}, {"size": 45152, "mtime": 1750295514405, "results": "518", "hashOfConfig": "329"}, {"size": 70430, "mtime": 1748984865165, "results": "519", "hashOfConfig": "329"}, {"size": 9519, "mtime": 1747354195259, "results": "520", "hashOfConfig": "329"}, {"size": 7408, "mtime": 1749894301204, "results": "521", "hashOfConfig": "329"}, {"size": 4905, "mtime": 1747354192845, "results": "522", "hashOfConfig": "329"}, {"size": 60115, "mtime": 1750947214848, "results": "523", "hashOfConfig": "329"}, {"size": 26487, "mtime": 1748278828957, "results": "524", "hashOfConfig": "329"}, {"size": 24147, "mtime": 1747354834297, "results": "525", "hashOfConfig": "329"}, {"size": 23053, "mtime": 1749730024729, "results": "526", "hashOfConfig": "329"}, {"size": 37412, "mtime": 1750487313341, "results": "527", "hashOfConfig": "329"}, {"size": 17071, "mtime": 1751543017144, "results": "528", "hashOfConfig": "329"}, {"size": 8060, "mtime": 1747223318310, "results": "529", "hashOfConfig": "329"}, {"size": 16767, "mtime": 1748876218633, "results": "530", "hashOfConfig": "329"}, {"size": 3160, "mtime": 1745731138150, "results": "531", "hashOfConfig": "329"}, {"size": 7136, "mtime": 1747223318325, "results": "532", "hashOfConfig": "329"}, {"size": 20572, "mtime": 1751978048828, "results": "533", "hashOfConfig": "329"}, {"size": 2129, "mtime": 1746378664905, "results": "534", "hashOfConfig": "329"}, {"size": 955, "mtime": 1746378664898, "results": "535", "hashOfConfig": "329"}, {"size": 1184, "mtime": 1746378664905, "results": "536", "hashOfConfig": "329"}, {"size": 13378, "mtime": 1748984865159, "results": "537", "hashOfConfig": "329"}, {"size": 1099, "mtime": 1748326442261, "results": "538", "hashOfConfig": "329"}, {"size": 15897, "mtime": 1749894770618, "results": "539", "hashOfConfig": "329"}, {"size": 11963, "mtime": 1750323324025, "results": "540", "hashOfConfig": "329"}, {"size": 12224, "mtime": 1748220515100, "results": "541", "hashOfConfig": "329"}, {"size": 10534, "mtime": 1748220627343, "results": "542", "hashOfConfig": "329"}, {"size": 4031, "mtime": 1747278525096, "results": "543", "hashOfConfig": "329"}, {"size": 2036, "mtime": 1747283717643, "results": "544", "hashOfConfig": "329"}, {"size": 61133, "mtime": 1751834903269, "results": "545", "hashOfConfig": "329"}, {"size": 3589, "mtime": 1747355350828, "results": "546", "hashOfConfig": "329"}, {"size": 7509, "mtime": 1747353152130, "results": "547", "hashOfConfig": "329"}, {"size": 6153, "mtime": 1747354063004, "results": "548", "hashOfConfig": "329"}, {"size": 16791, "mtime": 1748473697636, "results": "549", "hashOfConfig": "329"}, {"size": 12245, "mtime": 1750386603287, "results": "550", "hashOfConfig": "329"}, {"size": 13612, "mtime": 1750175198005, "results": "551", "hashOfConfig": "329"}, {"size": 73138, "mtime": 1750487274356, "results": "552", "hashOfConfig": "329"}, {"size": 5589, "mtime": 1750239106405, "results": "553", "hashOfConfig": "329"}, {"size": 9836, "mtime": 1750175833314, "results": "554", "hashOfConfig": "329"}, {"size": 7964, "mtime": 1750400179899, "results": "555", "hashOfConfig": "329"}, {"size": 4152, "mtime": 1749202010110, "results": "556", "hashOfConfig": "329"}, {"size": 4762, "mtime": 1748513292659, "results": "557", "hashOfConfig": "329"}, {"size": 2443, "mtime": 1747719362467, "results": "558", "hashOfConfig": "329"}, {"size": 16049, "mtime": 1751785268325, "results": "559", "hashOfConfig": "329"}, {"size": 2002, "mtime": 1751785248620, "results": "560", "hashOfConfig": "329"}, {"size": 55806, "mtime": 1751975191453, "results": "561", "hashOfConfig": "329"}, {"size": 16095, "mtime": 1751785298468, "results": "562", "hashOfConfig": "329"}, {"size": 8222, "mtime": 1748250288630, "results": "563", "hashOfConfig": "329"}, {"size": 11210, "mtime": 1748223444732, "results": "564", "hashOfConfig": "329"}, {"size": 41991, "mtime": 1751785335077, "results": "565", "hashOfConfig": "329"}, {"size": 8067, "mtime": 1750764769591, "results": "566", "hashOfConfig": "329"}, {"size": 28083, "mtime": 1748250288657, "results": "567", "hashOfConfig": "329"}, {"size": 29543, "mtime": 1748984865164, "results": "568", "hashOfConfig": "329"}, {"size": 36682, "mtime": 1748250768337, "results": "569", "hashOfConfig": "329"}, {"size": 1999, "mtime": 1750764523294, "results": "570", "hashOfConfig": "329"}, {"size": 9569, "mtime": 1749694485776, "results": "571", "hashOfConfig": "329"}, {"size": 13102, "mtime": 1748250288647, "results": "572", "hashOfConfig": "329"}, {"size": 16077, "mtime": 1748365756504, "results": "573", "hashOfConfig": "329"}, {"size": 3987, "mtime": 1748709335425, "results": "574", "hashOfConfig": "329"}, {"size": 3539, "mtime": 1748800991826, "results": "575", "hashOfConfig": "329"}, {"size": 1712, "mtime": 1748800656400, "results": "576", "hashOfConfig": "329"}, {"size": 1983, "mtime": 1751010202512, "results": "577", "hashOfConfig": "329"}, {"size": 3908, "mtime": 1748801325319, "results": "578", "hashOfConfig": "329"}, {"size": 839, "mtime": 1748801505979, "results": "579", "hashOfConfig": "329"}, {"size": 365, "mtime": 1748984865153, "results": "580", "hashOfConfig": "329"}, {"size": 1850, "mtime": 1752060590013, "results": "581", "hashOfConfig": "329"}, {"size": 8860, "mtime": 1749202010110, "results": "582", "hashOfConfig": "329"}, {"size": 5114, "mtime": 1750951954100, "results": "583", "hashOfConfig": "329"}, {"size": 8088, "mtime": 1751977681640, "results": "584", "hashOfConfig": "329"}, {"size": 4151, "mtime": 1751716037997, "results": "585", "hashOfConfig": "329"}, {"size": 958, "mtime": 1750951934365, "results": "586", "hashOfConfig": "329"}, {"size": 2836, "mtime": 1749722547776, "results": "587", "hashOfConfig": "329"}, {"size": 913, "mtime": 1751742948436, "results": "588", "hashOfConfig": "329"}, {"size": 388, "mtime": 1749720429395, "results": "589", "hashOfConfig": "329"}, {"size": 579, "mtime": 1749731593347, "results": "590", "hashOfConfig": "329"}, {"size": 1948, "mtime": 1751543127045, "results": "591", "hashOfConfig": "329"}, {"size": 10543, "mtime": 1750295311557, "results": "592", "hashOfConfig": "329"}, {"size": 1664, "mtime": 1749808930419, "results": "593", "hashOfConfig": "329"}, {"size": 2401, "mtime": 1750389237348, "results": "594", "hashOfConfig": "329"}, {"size": 7582, "mtime": 1750400196241, "results": "595", "hashOfConfig": "329"}, {"size": 3926, "mtime": 1752228453755, "results": "596", "hashOfConfig": "329"}, {"size": 2502, "mtime": 1751969779191, "results": "597", "hashOfConfig": "329"}, {"size": 6803, "mtime": 1751969853518, "results": "598", "hashOfConfig": "329"}, {"size": 200, "mtime": 1750487274358, "results": "599", "hashOfConfig": "329"}, {"size": 4832, "mtime": 1750574009240, "results": "600", "hashOfConfig": "329"}, {"size": 3066, "mtime": 1750766071078, "results": "601", "hashOfConfig": "329"}, {"size": 4807, "mtime": 1750765702772, "results": "602", "hashOfConfig": "329"}, {"size": 74, "mtime": 1748250288649, "results": "603", "hashOfConfig": "329"}, {"size": 4384, "mtime": 1750962443161, "results": "604", "hashOfConfig": "329"}, {"size": 4263, "mtime": 1750960348603, "results": "605", "hashOfConfig": "329"}, {"size": 4331, "mtime": 1750963155580, "results": "606", "hashOfConfig": "329"}, {"size": 3878, "mtime": 1750940043247, "results": "607", "hashOfConfig": "329"}, {"size": 23770, "mtime": 1750963670443, "results": "608", "hashOfConfig": "329"}, {"size": 17942, "mtime": 1750963374804, "results": "609", "hashOfConfig": "329"}, {"size": 22243, "mtime": 1750961198993, "results": "610", "hashOfConfig": "329"}, {"size": 1252, "mtime": 1750941383903, "results": "611", "hashOfConfig": "329"}, {"size": 846, "mtime": 1750944008903, "results": "612", "hashOfConfig": "329"}, {"size": 218, "mtime": 1751349395110, "results": "613", "hashOfConfig": "329"}, {"size": 17515, "mtime": 1751975558805, "results": "614", "hashOfConfig": "329"}, {"size": 1131, "mtime": 1751847779969, "results": "615", "hashOfConfig": "329"}, {"size": 11512, "mtime": 1751835083804, "results": "616", "hashOfConfig": "329"}, {"size": 11356, "mtime": 1751850006546, "results": "617", "hashOfConfig": "329"}, {"size": 449, "mtime": 1751515976272, "results": "618", "hashOfConfig": "329"}, {"size": 2775, "mtime": 1752061651073, "results": "619", "hashOfConfig": "329"}, {"size": 6786, "mtime": 1751849858966, "results": "620", "hashOfConfig": "329"}, {"size": 1127, "mtime": 1751843983335, "results": "621", "hashOfConfig": "329"}, {"size": 16832, "mtime": 1752061505047, "results": "622", "hashOfConfig": "329"}, {"size": 5733, "mtime": 1751847035611, "results": "623", "hashOfConfig": "329"}, {"size": 4358, "mtime": 1751835622757, "results": "624", "hashOfConfig": "329"}, {"size": 759, "mtime": 1751785941157, "results": "625", "hashOfConfig": "329"}, {"size": 4350, "mtime": 1751784560929, "results": "626", "hashOfConfig": "329"}, {"size": 4259, "mtime": 1751742649369, "results": "627", "hashOfConfig": "329"}, {"size": 851, "mtime": 1752000599842, "results": "628", "hashOfConfig": "329"}, {"size": 663, "mtime": 1751977976716, "results": "629", "hashOfConfig": "329"}, {"size": 1337, "mtime": 1752230837286, "results": "630", "hashOfConfig": "329"}, {"size": 5772, "mtime": 1751830627407, "results": "631", "hashOfConfig": "329"}, {"size": 2222, "mtime": 1751829103916, "results": "632", "hashOfConfig": "329"}, {"size": 1702, "mtime": 1751829247534, "results": "633", "hashOfConfig": "329"}, {"size": 4105, "mtime": 1751831884036, "results": "634", "hashOfConfig": "329"}, {"size": 4780, "mtime": 1752063286693, "results": "635", "hashOfConfig": "329"}, {"size": 8061, "mtime": 1751981788773, "results": "636", "hashOfConfig": "329"}, {"size": 6115, "mtime": 1751974570045, "results": "637", "hashOfConfig": "329"}, {"size": 1301, "mtime": 1751974544781, "results": "638", "hashOfConfig": "329"}, {"size": 1565, "mtime": 1751974714234, "results": "639", "hashOfConfig": "329"}, {"size": 5432, "mtime": 1752228194750, "results": "640", "hashOfConfig": "329"}, {"size": 17729, "mtime": 1752228549195, "results": "641", "hashOfConfig": "329"}, {"size": 37791, "mtime": 1752072617445, "results": "642", "hashOfConfig": "329"}, {"size": 4198, "mtime": 1752071159143, "results": "643", "hashOfConfig": "329"}, {"size": 2108, "mtime": 1752049805712, "results": "644", "hashOfConfig": "329"}, {"size": 593, "mtime": 1752051337091, "results": "645", "hashOfConfig": "329"}, {"size": 592, "mtime": 1752053261180, "results": "646", "hashOfConfig": "329"}, {"size": 12882, "mtime": 1752061763745, "results": "647", "hashOfConfig": "329"}, {"size": 4487, "mtime": 1752063476831, "results": "648", "hashOfConfig": "329"}, {"size": 382, "mtime": 1752228324238, "results": "649", "hashOfConfig": "329"}, {"size": 1981, "mtime": 1752230000176, "results": "650", "hashOfConfig": "329"}, {"size": 4737, "mtime": 1752232202711, "results": "651", "hashOfConfig": "329"}, {"size": 11371, "mtime": 1752231697656, "results": "652", "hashOfConfig": "329"}, {"size": 2791, "mtime": 1752232000712, "results": "653", "hashOfConfig": "329"}, {"size": 1654, "mtime": 1752231357126, "results": "654", "hashOfConfig": "329"}, {"size": 3484, "mtime": 1752231943422, "results": "655", "hashOfConfig": "329"}, {"size": 2239, "mtime": 1752231924079, "results": "656", "hashOfConfig": "329"}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e07wfn", {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1op6h7j", {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1380", "messages": "1381", "suppressedMessages": "1382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1383", "messages": "1384", "suppressedMessages": "1385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1386", "messages": "1387", "suppressedMessages": "1388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1389", "messages": "1390", "suppressedMessages": "1391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1392", "messages": "1393", "suppressedMessages": "1394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1395", "messages": "1396", "suppressedMessages": "1397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1398", "messages": "1399", "suppressedMessages": "1400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1401", "messages": "1402", "suppressedMessages": "1403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1404", "messages": "1405", "suppressedMessages": "1406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1407", "messages": "1408", "suppressedMessages": "1409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1410", "messages": "1411", "suppressedMessages": "1412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1413", "messages": "1414", "suppressedMessages": "1415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1416", "messages": "1417", "suppressedMessages": "1418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1419", "messages": "1420", "suppressedMessages": "1421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1422", "messages": "1423", "suppressedMessages": "1424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1425", "messages": "1426", "suppressedMessages": "1427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1428", "messages": "1429", "suppressedMessages": "1430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1431", "messages": "1432", "suppressedMessages": "1433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1434", "messages": "1435", "suppressedMessages": "1436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1437", "messages": "1438", "suppressedMessages": "1439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1440", "messages": "1441", "suppressedMessages": "1442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1443", "messages": "1444", "suppressedMessages": "1445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1446", "messages": "1447", "suppressedMessages": "1448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1449", "messages": "1450", "suppressedMessages": "1451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1452", "messages": "1453", "suppressedMessages": "1454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1455", "messages": "1456", "suppressedMessages": "1457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1458", "messages": "1459", "suppressedMessages": "1460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1461", "messages": "1462", "suppressedMessages": "1463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1464", "messages": "1465", "suppressedMessages": "1466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1467", "messages": "1468", "suppressedMessages": "1469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1470", "messages": "1471", "suppressedMessages": "1472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1473", "messages": "1474", "suppressedMessages": "1475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1476", "messages": "1477", "suppressedMessages": "1478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1479", "messages": "1480", "suppressedMessages": "1481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1482", "messages": "1483", "suppressedMessages": "1484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1485", "messages": "1486", "suppressedMessages": "1487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1488", "messages": "1489", "suppressedMessages": "1490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1491", "messages": "1492", "suppressedMessages": "1493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1494", "messages": "1495", "suppressedMessages": "1496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1497", "messages": "1498", "suppressedMessages": "1499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1500", "messages": "1501", "suppressedMessages": "1502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1503", "messages": "1504", "suppressedMessages": "1505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1506", "messages": "1507", "suppressedMessages": "1508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1509", "messages": "1510", "suppressedMessages": "1511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1512", "messages": "1513", "suppressedMessages": "1514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1515", "messages": "1516", "suppressedMessages": "1517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1518", "messages": "1519", "suppressedMessages": "1520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1521", "messages": "1522", "suppressedMessages": "1523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1524", "messages": "1525", "suppressedMessages": "1526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1527", "messages": "1528", "suppressedMessages": "1529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1530", "messages": "1531", "suppressedMessages": "1532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1533", "messages": "1534", "suppressedMessages": "1535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1536", "messages": "1537", "suppressedMessages": "1538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1539", "messages": "1540", "suppressedMessages": "1541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1542", "messages": "1543", "suppressedMessages": "1544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1545", "messages": "1546", "suppressedMessages": "1547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1548", "messages": "1549", "suppressedMessages": "1550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1551", "messages": "1552", "suppressedMessages": "1553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1554", "messages": "1555", "suppressedMessages": "1556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1557", "messages": "1558", "suppressedMessages": "1559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1560", "messages": "1561", "suppressedMessages": "1562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1563", "messages": "1564", "suppressedMessages": "1565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1566", "messages": "1567", "suppressedMessages": "1568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1569", "messages": "1570", "suppressedMessages": "1571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1572", "messages": "1573", "suppressedMessages": "1574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1575", "messages": "1576", "suppressedMessages": "1577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1578", "messages": "1579", "suppressedMessages": "1580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1581", "messages": "1582", "suppressedMessages": "1583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1584", "messages": "1585", "suppressedMessages": "1586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1587", "messages": "1588", "suppressedMessages": "1589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1590", "messages": "1591", "suppressedMessages": "1592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1593", "messages": "1594", "suppressedMessages": "1595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1596", "messages": "1597", "suppressedMessages": "1598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1599", "messages": "1600", "suppressedMessages": "1601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1602", "messages": "1603", "suppressedMessages": "1604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1605", "messages": "1606", "suppressedMessages": "1607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1608", "messages": "1609", "suppressedMessages": "1610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1611", "messages": "1612", "suppressedMessages": "1613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1614", "messages": "1615", "suppressedMessages": "1616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1617", "messages": "1618", "suppressedMessages": "1619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1620", "messages": "1621", "suppressedMessages": "1622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1623", "messages": "1624", "suppressedMessages": "1625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1626", "messages": "1627", "suppressedMessages": "1628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1629", "messages": "1630", "suppressedMessages": "1631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1632", "messages": "1633", "suppressedMessages": "1634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1635", "messages": "1636", "suppressedMessages": "1637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js", ["1638"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx", ["1639", "1640", "1641", "1642", "1643"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx", ["1644", "1645"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx", ["1646"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx", ["1647"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx", ["1648", "1649", "1650", "1651", "1652"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx", ["1653", "1654"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1655", "1656"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1657"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1658", "1659", "1660", "1661", "1662", "1663", "1664"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1665", "1666", "1667", "1668", "1669"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", ["1670"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", ["1684", "1685"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1686", "1687"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1696"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js", ["1697"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js", ["1698"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js", ["1699"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx", ["1700"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx", ["1701"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1702"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx", ["1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx", ["1711", "1712"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx", ["1713", "1714", "1715"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx", ["1716"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx", ["1717"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx", ["1718", "1719", "1720"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx", ["1721", "1722", "1723"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx", ["1724", "1725", "1726"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx", ["1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx", ["1743", "1744"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx", ["1745", "1746", "1747", "1748", "1749"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx", ["1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx", ["1758"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx", ["1759"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx", ["1760", "1761", "1762", "1763", "1764", "1765"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx", ["1766"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx", ["1767", "1768", "1769", "1770", "1771", "1772"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx", ["1773", "1774", "1775"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx", ["1776"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx", ["1777", "1778", "1779", "1780", "1781", "1782", "1783"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx", ["1784", "1785", "1786", "1787", "1788"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1789", "1790", "1791"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx", ["1792", "1793"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx", ["1794"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx", ["1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx", ["1804"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx", ["1805"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js", ["1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx", ["1824"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx", ["1825"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx", ["1826"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx", ["1827"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx", ["1828"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx", ["1829", "1830", "1831"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx", ["1832"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1833", "1834", "1835", "1836", "1837", "1838"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx", ["1839"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx", ["1840", "1841", "1842", "1843"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx", ["1844", "1845"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx", ["1846", "1847", "1848", "1849"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx", ["1850", "1851"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx", ["1852", "1853", "1854", "1855"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1856"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx", ["1857", "1858"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1859"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx", ["1860", "1861"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx", ["1862"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx", ["1863"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx", ["1864", "1865", "1866", "1867", "1868"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx", ["1869", "1870"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx", ["1871", "1872", "1873"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx", ["1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx", ["1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx", ["1902", "1903", "1904", "1905"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx", ["1906"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx", ["1907", "1908", "1909", "1910"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx", ["1911", "1912", "1913", "1914", "1915", "1916", "1917"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx", ["1918", "1919", "1920", "1921", "1922", "1923", "1924", "1925", "1926", "1927"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx", ["1928", "1929", "1930", "1931"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["1932", "1933"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx", ["1934", "1935", "1936", "1937", "1938", "1939", "1940"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx", ["1941", "1942", "1943", "1944", "1945", "1946", "1947"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx", ["1948", "1949", "1950"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js", ["1951"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx", ["1952", "1953", "1954"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx", ["1955", "1956", "1957", "1958"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js", ["1959"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx", ["1960", "1961", "1962"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx", ["1963", "1964", "1965", "1966", "1967", "1968", "1969"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js", ["1970"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["1971", "1972", "1973", "1974"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["1975", "1976", "1977", "1978", "1979", "1980", "1981", "1982", "1983", "1984", "1985", "1986", "1987", "1988", "1989", "1990", "1991", "1992", "1993", "1994", "1995", "1996", "1997", "1998", "1999", "2000", "2001", "2002", "2003", "2004", "2005", "2006"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["2007", "2008", "2009", "2010", "2011", "2012"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx", ["2013"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx", ["2014"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx", ["2015"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js", ["2016", "2017"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx", ["2018", "2019", "2020", "2021", "2022", "2023", "2024", "2025"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", ["2026"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["2027", "2028", "2029", "2030", "2031"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["2032", "2033", "2034"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["2035", "2036"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx", ["2037", "2038"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["2039", "2040", "2041"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx", ["2042"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx", ["2043", "2044", "2045", "2046"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx", ["2047"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx", ["2048"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx", ["2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx", ["2057"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx", ["2058", "2059", "2060", "2061", "2062", "2063", "2064", "2065", "2066", "2067", "2068", "2069"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx", ["2070"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx", ["2071", "2072", "2073"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx", ["2074", "2075"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx", ["2076", "2077"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx", ["2078", "2079"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx", ["2080", "2081", "2082", "2083", "2084", "2085", "2086"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx", ["2087"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx", ["2088"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx", ["2089", "2090"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx", ["2091", "2092", "2093", "2094", "2095", "2096"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx", ["2097", "2098", "2099", "2100", "2101"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx", ["2102", "2103", "2104"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx", ["2105"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\ImageDropZone.jsx", ["2106"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SolutionEditor.jsx", ["2107", "2108"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StaffManagement.jsx", ["2109", "2110", "2111"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\dashboard\\dashboardSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\dashboardApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\layout\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AddExamAdmin.jsx", ["2112", "2113", "2114", "2115", "2116", "2117", "2118", "2119", "2120", "2121", "2122", "2123", "2124", "2125", "2126", "2127", "2128", "2129", "2130", "2131", "2132", "2133", "2134", "2135", "2136", "2137", "2138", "2139", "2140", "2141", "2142"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\RightContent.jsx", ["2143", "2144", "2145", "2146", "2147", "2148"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\LeftContent.jsx", ["2149", "2150", "2151", "2152"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\addExam\\addExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\CompactStepHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\NavigateBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\hooks\\useDebouncedEffect.js", ["2153", "2154"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddImagesModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\SolutionEditor.jsx", ["2155", "2156"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\LeftContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\RightContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionsExam\\questionsExamSlice.js", ["2157"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\QuestionView.jsx", ["2158", "2159", "2160", "2161", "2162", "2163", "2164", "2165", "2166", "2167"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\QuestionContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableQuestionItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableStatementsContainer.jsx", ["2168", "2169"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageQuestionsExam\\SortableStatementItem.jsx", [], [], {"ruleId": "2170", "severity": 1, "message": "2171", "line": 51, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 51, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2174", "line": 4, "column": 23, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2175", "line": 8, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2176", "line": 11, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 11, "endColumn": 21}, {"ruleId": "2177", "severity": 1, "message": "2178", "line": 42, "column": 8, "nodeType": "2179", "endLine": 42, "endColumn": 24, "suggestions": "2180"}, {"ruleId": "2170", "severity": 1, "message": "2181", "line": 49, "column": 15, "nodeType": "2172", "messageId": "2173", "endLine": 49, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2182", "line": 10, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 10, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2183", "line": 18, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 36}, {"ruleId": "2177", "severity": 1, "message": "2184", "line": 26, "column": 8, "nodeType": "2179", "endLine": 26, "endColumn": 19, "suggestions": "2185"}, {"ruleId": "2186", "severity": 1, "message": "2187", "line": 393, "column": 45, "nodeType": "2188", "endLine": 398, "endColumn": 47}, {"ruleId": "2170", "severity": 1, "message": "2189", "line": 6, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 11}, {"ruleId": "2170", "severity": 1, "message": "2190", "line": 6, "column": 27, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 31}, {"ruleId": "2170", "severity": 1, "message": "2191", "line": 7, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2192", "line": 8, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 32}, {"ruleId": "2170", "severity": 1, "message": "2193", "line": 88, "column": 23, "nodeType": "2172", "messageId": "2173", "endLine": 88, "endColumn": 31}, {"ruleId": "2170", "severity": 1, "message": "2194", "line": 14, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2183", "line": 15, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 36}, {"ruleId": "2170", "severity": 1, "message": "2195", "line": 2, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2196", "line": 3, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2197", "line": 5, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 39}, {"ruleId": "2170", "severity": 1, "message": "2198", "line": 15, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2199", "line": 16, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 16, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2200", "line": 24, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 24, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2201", "line": 30, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 30, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2202", "line": 34, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 34, "endColumn": 23}, {"ruleId": "2177", "severity": 1, "message": "2203", "line": 73, "column": 31, "nodeType": "2172", "endLine": 73, "endColumn": 42}, {"ruleId": "2177", "severity": 1, "message": "2204", "line": 269, "column": 8, "nodeType": "2179", "endLine": 269, "endColumn": 18, "suggestions": "2205"}, {"ruleId": "2170", "severity": 1, "message": "2198", "line": 6, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2199", "line": 9, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 9, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2200", "line": 18, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2201", "line": 20, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 20, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2206", "line": 26, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 26}, {"ruleId": "2170", "severity": 1, "message": "2207", "line": 1, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2207", "line": 1, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2208", "line": 8, "column": 37, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 48}, {"ruleId": "2170", "severity": 1, "message": "2209", "line": 11, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 11, "endColumn": 30}, {"ruleId": "2170", "severity": 1, "message": "2210", "line": 22, "column": 18, "nodeType": "2172", "messageId": "2173", "endLine": 22, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2211", "line": 23, "column": 21, "nodeType": "2172", "messageId": "2173", "endLine": 23, "endColumn": 31}, {"ruleId": "2170", "severity": 1, "message": "2212", "line": 24, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 24, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2213", "line": 25, "column": 25, "nodeType": "2172", "messageId": "2173", "endLine": 25, "endColumn": 39}, {"ruleId": "2170", "severity": 1, "message": "2214", "line": 26, "column": 30, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 49}, {"ruleId": "2170", "severity": 1, "message": "2215", "line": 27, "column": 21, "nodeType": "2172", "messageId": "2173", "endLine": 27, "endColumn": 26}, {"ruleId": "2170", "severity": 1, "message": "2216", "line": 27, "column": 41, "nodeType": "2172", "messageId": "2173", "endLine": 27, "endColumn": 51}, {"ruleId": "2170", "severity": 1, "message": "2217", "line": 31, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 31, "endColumn": 30}, {"ruleId": "2170", "severity": 1, "message": "2218", "line": 34, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 34, "endColumn": 35}, {"ruleId": "2170", "severity": 1, "message": "2219", "line": 38, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 38, "endColumn": 33}, {"ruleId": "2170", "severity": 1, "message": "2196", "line": 1, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2200", "line": 12, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 12, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2220", "line": 15, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2183", "line": 15, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 36}, {"ruleId": "2170", "severity": 1, "message": "2207", "line": 1, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2195", "line": 2, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2221", "line": 6, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2222", "line": 7, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2223", "line": 8, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2217", "line": 25, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 25, "endColumn": 30}, {"ruleId": "2170", "severity": 1, "message": "2219", "line": 28, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 28, "endColumn": 33}, {"ruleId": "2170", "severity": 1, "message": "2224", "line": 31, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 31, "endColumn": 32}, {"ruleId": "2170", "severity": 1, "message": "2207", "line": 1, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2225", "line": 37, "column": 19, "nodeType": "2172", "messageId": "2173", "endLine": 37, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2226", "line": 5, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 17}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 140, "column": 80, "nodeType": "2229", "messageId": "2230", "endLine": 140, "endColumn": 82}, {"ruleId": "2170", "severity": 1, "message": "2231", "line": 3, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2232", "line": 19, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 19, "endColumn": 22}, {"ruleId": "2177", "severity": 1, "message": "2233", "line": 18, "column": 8, "nodeType": "2179", "endLine": 18, "endColumn": 10, "suggestions": "2234"}, {"ruleId": "2170", "severity": 1, "message": "2235", "line": 1, "column": 20, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 3, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2237", "line": 4, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2238", "line": 12, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 12, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2239", "line": 13, "column": 36, "nodeType": "2172", "messageId": "2173", "endLine": 13, "endColumn": 45}, {"ruleId": "2170", "severity": 1, "message": "2240", "line": 14, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2241", "line": 14, "column": 16, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2242", "line": 29, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 29, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 3, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2243", "line": 5, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2244", "line": 10, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 10, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2245", "line": 18, "column": 36, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 46}, {"ruleId": "2177", "severity": 1, "message": "2246", "line": 43, "column": 8, "nodeType": "2179", "endLine": 43, "endColumn": 26, "suggestions": "2247"}, {"ruleId": "2170", "severity": 1, "message": "2235", "line": 2, "column": 20, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2248", "line": 7, "column": 47, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 68}, {"ruleId": "2170", "severity": 1, "message": "2249", "line": 17, "column": 7, "nodeType": "2172", "messageId": "2173", "endLine": 17, "endColumn": 16}, {"ruleId": "2170", "severity": 1, "message": "2250", "line": 29, "column": 7, "nodeType": "2172", "messageId": "2173", "endLine": 29, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2251", "line": 36, "column": 7, "nodeType": "2172", "messageId": "2173", "endLine": 36, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2244", "line": 6, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2245", "line": 15, "column": 36, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 46}, {"ruleId": "2170", "severity": 1, "message": "2252", "line": 19, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 19, "endColumn": 37}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 5, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2245", "line": 16, "column": 36, "nodeType": "2172", "messageId": "2173", "endLine": 16, "endColumn": 46}, {"ruleId": "2177", "severity": 1, "message": "2253", "line": 35, "column": 8, "nodeType": "2179", "endLine": 35, "endColumn": 53, "suggestions": "2254"}, {"ruleId": "2170", "severity": 1, "message": "2255", "line": 12, "column": 55, "nodeType": "2172", "messageId": "2173", "endLine": 12, "endColumn": 61}, {"ruleId": "2170", "severity": 1, "message": "2256", "line": 12, "column": 77, "nodeType": "2172", "messageId": "2173", "endLine": 12, "endColumn": 82}, {"ruleId": "2170", "severity": 1, "message": "2257", "line": 12, "column": 84, "nodeType": "2172", "messageId": "2173", "endLine": 12, "endColumn": 94}, {"ruleId": "2170", "severity": 1, "message": "2200", "line": 18, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2202", "line": 20, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 20, "endColumn": 23}, {"ruleId": "2177", "severity": 1, "message": "2203", "line": 39, "column": 31, "nodeType": "2172", "endLine": 39, "endColumn": 42}, {"ruleId": "2170", "severity": 1, "message": "2258", "line": 96, "column": 17, "nodeType": "2172", "messageId": "2173", "endLine": 96, "endColumn": 21}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 412, "column": 68, "nodeType": "2229", "messageId": "2230", "endLine": 412, "endColumn": 70}, {"ruleId": "2170", "severity": 1, "message": "2196", "line": 2, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2197", "line": 7, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 39}, {"ruleId": "2170", "severity": 1, "message": "2259", "line": 25, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 25, "endColumn": 26}, {"ruleId": "2170", "severity": 1, "message": "2260", "line": 26, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 30}, {"ruleId": "2170", "severity": 1, "message": "2261", "line": 48, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 48, "endColumn": 22}, {"ruleId": "2177", "severity": 1, "message": "2262", "line": 69, "column": 8, "nodeType": "2179", "endLine": 69, "endColumn": 30, "suggestions": "2263"}, {"ruleId": "2170", "severity": 1, "message": "2264", "line": 79, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 79, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2265", "line": 87, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 87, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2266", "line": 8, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 28}, {"ruleId": "2177", "severity": 1, "message": "2267", "line": 45, "column": 8, "nodeType": "2179", "endLine": 45, "endColumn": 32, "suggestions": "2268"}, {"ruleId": "2170", "severity": 1, "message": "2269", "line": 6, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2270", "line": 7, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2198", "line": 13, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 13, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2199", "line": 14, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2201", "line": 23, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 23, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 3, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2271", "line": 5, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2270", "line": 6, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2266", "line": 7, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 28}, {"ruleId": "2177", "severity": 1, "message": "2272", "line": 57, "column": 8, "nodeType": "2179", "endLine": 57, "endColumn": 28, "suggestions": "2273"}, {"ruleId": "2170", "severity": 1, "message": "2219", "line": 71, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 71, "endColumn": 33}, {"ruleId": "2170", "severity": 1, "message": "2218", "line": 75, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 75, "endColumn": 35}, {"ruleId": "2170", "severity": 1, "message": "2224", "line": 79, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 79, "endColumn": 32}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 3, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2245", "line": 11, "column": 41, "nodeType": "2172", "messageId": "2173", "endLine": 11, "endColumn": 51}, {"ruleId": "2170", "severity": 1, "message": "2274", "line": 1, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 28}, {"ruleId": "2170", "severity": 1, "message": "2275", "line": 2, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2235", "line": 3, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 5, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2276", "line": 7, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2277", "line": 12, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 12, "endColumn": 31}, {"ruleId": "2170", "severity": 1, "message": "2278", "line": 1, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2279", "line": 2, "column": 37, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 45}, {"ruleId": "2170", "severity": 1, "message": "2280", "line": 2, "column": 47, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 60}, {"ruleId": "2170", "severity": 1, "message": "2281", "line": 2, "column": 62, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 67}, {"ruleId": "2170", "severity": 1, "message": "2282", "line": 2, "column": 69, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 77}, {"ruleId": "2170", "severity": 1, "message": "2283", "line": 3, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 26}, {"ruleId": "2177", "severity": 1, "message": "2284", "line": 36, "column": 8, "nodeType": "2179", "endLine": 36, "endColumn": 43, "suggestions": "2285"}, {"ruleId": "2170", "severity": 1, "message": "2278", "line": 1, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 5, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2182", "line": 7, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 18}, {"ruleId": "2177", "severity": 1, "message": "2286", "line": 25, "column": 8, "nodeType": "2179", "endLine": 25, "endColumn": 20, "suggestions": "2287"}, {"ruleId": "2170", "severity": 1, "message": "2288", "line": 1, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2289", "line": 32, "column": 83, "nodeType": "2172", "messageId": "2173", "endLine": 32, "endColumn": 91}, {"ruleId": "2170", "severity": 1, "message": "2290", "line": 121, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 121, "endColumn": 23}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 159, "column": 22, "nodeType": "2293", "messageId": "2294", "endLine": 159, "endColumn": 24}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 159, "column": 91, "nodeType": "2293", "messageId": "2294", "endLine": 159, "endColumn": 93}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 304, "column": 40, "nodeType": "2293", "messageId": "2294", "endLine": 304, "endColumn": 42}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 304, "column": 109, "nodeType": "2293", "messageId": "2294", "endLine": 304, "endColumn": 111}, {"ruleId": "2170", "severity": 1, "message": "2295", "line": 14, "column": 38, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 47}, {"ruleId": "2170", "severity": 1, "message": "2296", "line": 14, "column": 49, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 56}, {"ruleId": "2170", "severity": 1, "message": "2297", "line": 14, "column": 58, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 70}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 34, "column": 40, "nodeType": "2293", "messageId": "2294", "endLine": 34, "endColumn": 42}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 34, "column": 109, "nodeType": "2293", "messageId": "2294", "endLine": 34, "endColumn": 111}, {"ruleId": "2170", "severity": 1, "message": "2215", "line": 15, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2298", "line": 15, "column": 44, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 52}, {"ruleId": "2177", "severity": 1, "message": "2299", "line": 42, "column": 8, "nodeType": "2179", "endLine": 42, "endColumn": 40, "suggestions": "2300"}, {"ruleId": "2170", "severity": 1, "message": "2182", "line": 1, "column": 25, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 33}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 4, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2301", "line": 4, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2176", "line": 1, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2174", "line": 2, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2302", "line": 4, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2303", "line": 4, "column": 26, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 44}, {"ruleId": "2170", "severity": 1, "message": "2304", "line": 9, "column": 166, "nodeType": "2172", "messageId": "2173", "endLine": 9, "endColumn": 170}, {"ruleId": "2170", "severity": 1, "message": "2305", "line": 47, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 47, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2306", "line": 48, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 48, "endColumn": 26}, {"ruleId": "2170", "severity": 1, "message": "2307", "line": 50, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 50, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2308", "line": 59, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 59, "endColumn": 16}, {"ruleId": "2177", "severity": 1, "message": "2309", "line": 29, "column": 8, "nodeType": "2179", "endLine": 29, "endColumn": 10, "suggestions": "2310"}, {"ruleId": "2186", "severity": 1, "message": "2187", "line": 44, "column": 29, "nodeType": "2188", "endLine": 44, "endColumn": 98}, {"ruleId": "2311", "severity": 1, "message": "2312", "line": 202, "column": 24, "nodeType": "2313", "messageId": "2314", "endLine": 202, "endColumn": 25, "suggestions": "2315"}, {"ruleId": "2311", "severity": 1, "message": "2316", "line": 202, "column": 26, "nodeType": "2313", "messageId": "2314", "endLine": 202, "endColumn": 27, "suggestions": "2317"}, {"ruleId": "2311", "severity": 1, "message": "2312", "line": 204, "column": 27, "nodeType": "2313", "messageId": "2314", "endLine": 204, "endColumn": 28, "suggestions": "2318"}, {"ruleId": "2311", "severity": 1, "message": "2316", "line": 204, "column": 29, "nodeType": "2313", "messageId": "2314", "endLine": 204, "endColumn": 30, "suggestions": "2319"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 250, "column": 30, "nodeType": "2313", "messageId": "2314", "endLine": 250, "endColumn": 31, "suggestions": "2321"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 269, "column": 61, "nodeType": "2313", "messageId": "2314", "endLine": 269, "endColumn": 62, "suggestions": "2322"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 289, "column": 29, "nodeType": "2313", "messageId": "2314", "endLine": 289, "endColumn": 30, "suggestions": "2323"}, {"ruleId": "2311", "severity": 1, "message": "2324", "line": 289, "column": 31, "nodeType": "2313", "messageId": "2314", "endLine": 289, "endColumn": 32, "suggestions": "2325"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 290, "column": 51, "nodeType": "2313", "messageId": "2314", "endLine": 290, "endColumn": 52, "suggestions": "2326"}, {"ruleId": "2311", "severity": 1, "message": "2324", "line": 290, "column": 53, "nodeType": "2313", "messageId": "2314", "endLine": 290, "endColumn": 54, "suggestions": "2327"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 390, "column": 30, "nodeType": "2313", "messageId": "2314", "endLine": 390, "endColumn": 31, "suggestions": "2328"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 417, "column": 61, "nodeType": "2313", "messageId": "2314", "endLine": 417, "endColumn": 62, "suggestions": "2329"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 437, "column": 29, "nodeType": "2313", "messageId": "2314", "endLine": 437, "endColumn": 30, "suggestions": "2330"}, {"ruleId": "2311", "severity": 1, "message": "2324", "line": 437, "column": 31, "nodeType": "2313", "messageId": "2314", "endLine": 437, "endColumn": 32, "suggestions": "2331"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 438, "column": 51, "nodeType": "2313", "messageId": "2314", "endLine": 438, "endColumn": 52, "suggestions": "2332"}, {"ruleId": "2311", "severity": 1, "message": "2324", "line": 438, "column": 53, "nodeType": "2313", "messageId": "2314", "endLine": 438, "endColumn": 54, "suggestions": "2333"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 526, "column": 30, "nodeType": "2313", "messageId": "2314", "endLine": 526, "endColumn": 31, "suggestions": "2334"}, {"ruleId": "2311", "severity": 1, "message": "2320", "line": 550, "column": 61, "nodeType": "2313", "messageId": "2314", "endLine": 550, "endColumn": 62, "suggestions": "2335"}, {"ruleId": "2177", "severity": 1, "message": "2336", "line": 89, "column": 8, "nodeType": "2179", "endLine": 89, "endColumn": 10, "suggestions": "2337"}, {"ruleId": "2186", "severity": 1, "message": "2187", "line": 56, "column": 37, "nodeType": "2188", "endLine": 56, "endColumn": 107}, {"ruleId": "2170", "severity": 1, "message": "2338", "line": 3, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 27}, {"ruleId": "2177", "severity": 1, "message": "2339", "line": 73, "column": 8, "nodeType": "2179", "endLine": 73, "endColumn": 25, "suggestions": "2340"}, {"ruleId": "2170", "severity": 1, "message": "2235", "line": 3, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2341", "line": 5, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2342", "line": 14, "column": 28, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 45}, {"ruleId": "2170", "severity": 1, "message": "2343", "line": 34, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 34, "endColumn": 20}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 116, "column": 56, "nodeType": "2229", "messageId": "2230", "endLine": 116, "endColumn": 58}, {"ruleId": "2170", "severity": 1, "message": "2275", "line": 2, "column": 23, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 34}, {"ruleId": "2170", "severity": 1, "message": "2345", "line": 4, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 28}, {"ruleId": "2170", "severity": 1, "message": "2346", "line": 5, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2347", "line": 5, "column": 33, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 40}, {"ruleId": "2170", "severity": 1, "message": "2348", "line": 5, "column": 42, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 51}, {"ruleId": "2170", "severity": 1, "message": "2349", "line": 8, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 21}, {"ruleId": "2350", "severity": 1, "message": "2351", "line": 12, "column": 25, "nodeType": "2188", "endLine": 12, "endColumn": 614}, {"ruleId": "2170", "severity": 1, "message": "2278", "line": 1, "column": 46, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 49}, {"ruleId": "2170", "severity": 1, "message": "2352", "line": 3, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2353", "line": 15, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2354", "line": 15, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 37}, {"ruleId": "2177", "severity": 1, "message": "2355", "line": 21, "column": 8, "nodeType": "2179", "endLine": 21, "endColumn": 26, "suggestions": "2356"}, {"ruleId": "2177", "severity": 1, "message": "2357", "line": 40, "column": 8, "nodeType": "2179", "endLine": 40, "endColumn": 10, "suggestions": "2358"}, {"ruleId": "2170", "severity": 1, "message": "2189", "line": 2, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 11}, {"ruleId": "2170", "severity": 1, "message": "2196", "line": 3, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2359", "line": 13, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 13, "endColumn": 23}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 290, "column": 56, "nodeType": "2229", "messageId": "2230", "endLine": 290, "endColumn": 58}, {"ruleId": "2170", "severity": 1, "message": "2360", "line": 15, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2361", "line": 19, "column": 22, "nodeType": "2172", "messageId": "2173", "endLine": 19, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2341", "line": 4, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2362", "line": 5, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 16}, {"ruleId": "2170", "severity": 1, "message": "2363", "line": 100, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 100, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2364", "line": 111, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 111, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2238", "line": 15, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2223", "line": 7, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 36}, {"ruleId": "2170", "severity": 1, "message": "2238", "line": 13, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 13, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2238", "line": 14, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2223", "line": 7, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 36}, {"ruleId": "2170", "severity": 1, "message": "2238", "line": 13, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 13, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2223", "line": 7, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 36}, {"ruleId": "2365", "severity": 1, "message": "2366", "line": 28, "column": 74, "nodeType": "2229", "messageId": "2367", "endLine": 28, "endColumn": 75}, {"ruleId": "2170", "severity": 1, "message": "2368", "line": 6, "column": 25, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2369", "line": 6, "column": 31, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 37}, {"ruleId": "2170", "severity": 1, "message": "2257", "line": 6, "column": 39, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 49}, {"ruleId": "2170", "severity": 1, "message": "2281", "line": 6, "column": 51, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 56}, {"ruleId": "2170", "severity": 1, "message": "2370", "line": 6, "column": 65, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 70}, {"ruleId": "2177", "severity": 1, "message": "2371", "line": 50, "column": 8, "nodeType": "2179", "endLine": 50, "endColumn": 28, "suggestions": "2372"}, {"ruleId": "2177", "severity": 1, "message": "2371", "line": 54, "column": 8, "nodeType": "2179", "endLine": 54, "endColumn": 21, "suggestions": "2373"}, {"ruleId": "2170", "severity": 1, "message": "2374", "line": 37, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 37, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2375", "line": 44, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 44, "endColumn": 29}, {"ruleId": "2177", "severity": 1, "message": "2376", "line": 47, "column": 9, "nodeType": "2377", "endLine": 51, "endColumn": 4}, {"ruleId": "2170", "severity": 1, "message": "2378", "line": 16, "column": 45, "nodeType": "2172", "messageId": "2173", "endLine": 16, "endColumn": 57}, {"ruleId": "2177", "severity": 1, "message": "2379", "line": 55, "column": 8, "nodeType": "2179", "endLine": 55, "endColumn": 14, "suggestions": "2380"}, {"ruleId": "2177", "severity": 1, "message": "2381", "line": 109, "column": 11, "nodeType": "2377", "endLine": 115, "endColumn": 6, "suggestions": "2382"}, {"ruleId": "2177", "severity": 1, "message": "2383", "line": 109, "column": 11, "nodeType": "2377", "endLine": 115, "endColumn": 6, "suggestions": "2384"}, {"ruleId": "2177", "severity": 1, "message": "2385", "line": 109, "column": 11, "nodeType": "2377", "endLine": 115, "endColumn": 6, "suggestions": "2386"}, {"ruleId": "2170", "severity": 1, "message": "2387", "line": 117, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 117, "endColumn": 27}, {"ruleId": "2177", "severity": 1, "message": "2388", "line": 232, "column": 8, "nodeType": "2179", "endLine": 232, "endColumn": 23, "suggestions": "2389"}, {"ruleId": "2170", "severity": 1, "message": "2390", "line": 283, "column": 19, "nodeType": "2172", "messageId": "2173", "endLine": 283, "endColumn": 25}, {"ruleId": "2177", "severity": 1, "message": "2203", "line": 371, "column": 37, "nodeType": "2172", "endLine": 371, "endColumn": 48}, {"ruleId": "2177", "severity": 1, "message": "2203", "line": 398, "column": 37, "nodeType": "2172", "endLine": 398, "endColumn": 48}, {"ruleId": "2391", "severity": 1, "message": "2392", "line": 629, "column": 60, "nodeType": "2393", "messageId": "2394", "endLine": 629, "endColumn": 61}, {"ruleId": "2177", "severity": 1, "message": "2395", "line": 785, "column": 8, "nodeType": "2179", "endLine": 785, "endColumn": 99, "suggestions": "2396"}, {"ruleId": "2177", "severity": 1, "message": "2397", "line": 893, "column": 8, "nodeType": "2179", "endLine": 893, "endColumn": 36, "suggestions": "2398"}, {"ruleId": "2170", "severity": 1, "message": "2399", "line": 2, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2400", "line": 4, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2401", "line": 18, "column": 25, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2402", "line": 18, "column": 137, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 146}, {"ruleId": "2170", "severity": 1, "message": "2403", "line": 18, "column": 148, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 161}, {"ruleId": "2170", "severity": 1, "message": "2404", "line": 18, "column": 163, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 173}, {"ruleId": "2170", "severity": 1, "message": "2405", "line": 18, "column": 175, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 182}, {"ruleId": "2170", "severity": 1, "message": "2406", "line": 18, "column": 184, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 189}, {"ruleId": "2170", "severity": 1, "message": "2407", "line": 18, "column": 191, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 205}, {"ruleId": "2170", "severity": 1, "message": "2408", "line": 34, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 34, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2409", "line": 39, "column": 7, "nodeType": "2172", "messageId": "2173", "endLine": 39, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2410", "line": 40, "column": 7, "nodeType": "2172", "messageId": "2173", "endLine": 40, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2411", "line": 64, "column": 7, "nodeType": "2172", "messageId": "2173", "endLine": 64, "endColumn": 17}, {"ruleId": "2170", "severity": 1, "message": "2412", "line": 65, "column": 7, "nodeType": "2172", "messageId": "2173", "endLine": 65, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2413", "line": 67, "column": 7, "nodeType": "2172", "messageId": "2173", "endLine": 67, "endColumn": 17}, {"ruleId": "2170", "severity": 1, "message": "2278", "line": 4, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2208", "line": 6, "column": 35, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 46}, {"ruleId": "2170", "severity": 1, "message": "2414", "line": 10, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 10, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2245", "line": 26, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 21}, {"ruleId": "2177", "severity": 1, "message": "2379", "line": 37, "column": 8, "nodeType": "2179", "endLine": 37, "endColumn": 14, "suggestions": "2415"}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 15, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2416", "line": 18, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2189", "line": 21, "column": 98, "nodeType": "2172", "messageId": "2173", "endLine": 21, "endColumn": 99}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 262, "column": 38, "nodeType": "2229", "messageId": "2230", "endLine": 262, "endColumn": 40}, {"ruleId": "2170", "severity": 1, "message": "2199", "line": 7, "column": 111, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 115}, {"ruleId": "2170", "severity": 1, "message": "2417", "line": 7, "column": 117, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 127}, {"ruleId": "2170", "severity": 1, "message": "2288", "line": 10, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 10, "endColumn": 24}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 178, "column": 123, "nodeType": "2293", "messageId": "2294", "endLine": 178, "endColumn": 125}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 178, "column": 197, "nodeType": "2293", "messageId": "2294", "endLine": 178, "endColumn": 199}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 187, "column": 120, "nodeType": "2293", "messageId": "2294", "endLine": 187, "endColumn": 122}, {"ruleId": "2291", "severity": 1, "message": "2292", "line": 187, "column": 189, "nodeType": "2293", "messageId": "2294", "endLine": 187, "endColumn": 191}, {"ruleId": "2170", "severity": 1, "message": "2279", "line": 15, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2255", "line": 17, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 17, "endColumn": 11}, {"ruleId": "2170", "severity": 1, "message": "2199", "line": 20, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 20, "endColumn": 9}, {"ruleId": "2170", "severity": 1, "message": "2418", "line": 21, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 21, "endColumn": 17}, {"ruleId": "2170", "severity": 1, "message": "2256", "line": 24, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 24, "endColumn": 10}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 28, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 28, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2419", "line": 30, "column": 7, "nodeType": "2172", "messageId": "2173", "endLine": 30, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2420", "line": 159, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 159, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2421", "line": 160, "column": 21, "nodeType": "2172", "messageId": "2173", "endLine": 160, "endColumn": 31}, {"ruleId": "2177", "severity": 1, "message": "2422", "line": 166, "column": 8, "nodeType": "2179", "endLine": 166, "endColumn": 18, "suggestions": "2423"}, {"ruleId": "2170", "severity": 1, "message": "2424", "line": 15, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 16}, {"ruleId": "2170", "severity": 1, "message": "2425", "line": 16, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 16, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2426", "line": 26, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 18}, {"ruleId": "2177", "severity": 1, "message": "2427", "line": 199, "column": 8, "nodeType": "2179", "endLine": 199, "endColumn": 21, "suggestions": "2428"}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 49, "column": 37, "nodeType": "2229", "messageId": "2230", "endLine": 49, "endColumn": 39}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 51, "column": 44, "nodeType": "2229", "messageId": "2230", "endLine": 51, "endColumn": 46}, {"ruleId": "2170", "severity": 1, "message": "2429", "line": 8, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 16}, {"ruleId": "2170", "severity": 1, "message": "2189", "line": 8, "column": 18, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2282", "line": 8, "column": 21, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2430", "line": 15, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 17}, {"ruleId": "2170", "severity": 1, "message": "2431", "line": 16, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 16, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2432", "line": 26, "column": 24, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 37}, {"ruleId": "2170", "severity": 1, "message": "2433", "line": 111, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 111, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 7, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2199", "line": 13, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 13, "endColumn": 9}, {"ruleId": "2170", "severity": 1, "message": "2279", "line": 17, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 17, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2434", "line": 19, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 19, "endColumn": 13}, {"ruleId": "2177", "severity": 1, "message": "2435", "line": 129, "column": 8, "nodeType": "2179", "endLine": 129, "endColumn": 18, "suggestions": "2436"}, {"ruleId": "2170", "severity": 1, "message": "2437", "line": 155, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 155, "endColumn": 32}, {"ruleId": "2170", "severity": 1, "message": "2438", "line": 232, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 232, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 7, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 22}, {"ruleId": "2177", "severity": 1, "message": "2439", "line": 40, "column": 8, "nodeType": "2179", "endLine": 40, "endColumn": 30, "suggestions": "2440"}, {"ruleId": "2186", "severity": 1, "message": "2187", "line": 223, "column": 69, "nodeType": "2188", "endLine": 227, "endColumn": 71}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 49, "column": 108, "nodeType": "2229", "messageId": "2230", "endLine": 49, "endColumn": 110}, {"ruleId": "2170", "severity": 1, "message": "2215", "line": 55, "column": 42, "nodeType": "2172", "messageId": "2173", "endLine": 55, "endColumn": 47}, {"ruleId": "2177", "severity": 1, "message": "2371", "line": 88, "column": 8, "nodeType": "2179", "endLine": 88, "endColumn": 28, "suggestions": "2441"}, {"ruleId": "2177", "severity": 1, "message": "2371", "line": 92, "column": 8, "nodeType": "2179", "endLine": 92, "endColumn": 21, "suggestions": "2442"}, {"ruleId": "2170", "severity": 1, "message": "2443", "line": 7, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2444", "line": 8, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 12}, {"ruleId": "2170", "severity": 1, "message": "2445", "line": 13, "column": 3, "nodeType": "2172", "messageId": "2173", "endLine": 13, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2342", "line": 14, "column": 3, "nodeType": "2172", "messageId": "2173", "endLine": 14, "endColumn": 20}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 285, "column": 65, "nodeType": "2229", "messageId": "2230", "endLine": 285, "endColumn": 67}, {"ruleId": "2170", "severity": 1, "message": "2446", "line": 28, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 28, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2257", "line": 29, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 29, "endColumn": 15}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 447, "column": 101, "nodeType": "2229", "messageId": "2230", "endLine": 447, "endColumn": 103}, {"ruleId": "2170", "severity": 1, "message": "2447", "line": 55, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 55, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2448", "line": 55, "column": 32, "nodeType": "2172", "messageId": "2173", "endLine": 55, "endColumn": 42}, {"ruleId": "2170", "severity": 1, "message": "2449", "line": 59, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 59, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2450", "line": 70, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 70, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2451", "line": 82, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 82, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2452", "line": 88, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 88, "endColumn": 30}, {"ruleId": "2170", "severity": 1, "message": "2437", "line": 94, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 94, "endColumn": 32}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 336, "column": 35, "nodeType": "2229", "messageId": "2230", "endLine": 336, "endColumn": 37}, {"ruleId": "2170", "severity": 1, "message": "2244", "line": 9, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 9, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2453", "line": 11, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 11, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2255", "line": 19, "column": 3, "nodeType": "2172", "messageId": "2173", "endLine": 19, "endColumn": 9}, {"ruleId": "2170", "severity": 1, "message": "2245", "line": 31, "column": 30, "nodeType": "2172", "messageId": "2173", "endLine": 31, "endColumn": 40}, {"ruleId": "2170", "severity": 1, "message": "2454", "line": 4, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2455", "line": 5, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 14}, {"ruleId": "2170", "severity": 1, "message": "2456", "line": 17, "column": 26, "nodeType": "2172", "messageId": "2173", "endLine": 17, "endColumn": 46}, {"ruleId": "2170", "severity": 1, "message": "2457", "line": 24, "column": 96, "nodeType": "2172", "messageId": "2173", "endLine": 24, "endColumn": 100}, {"ruleId": "2170", "severity": 1, "message": "2255", "line": 24, "column": 102, "nodeType": "2172", "messageId": "2173", "endLine": 24, "endColumn": 108}, {"ruleId": "2170", "severity": 1, "message": "2195", "line": 26, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2458", "line": 27, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 27, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2200", "line": 34, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 34, "endColumn": 17}, {"ruleId": "2170", "severity": 1, "message": "2459", "line": 35, "column": 47, "nodeType": "2172", "messageId": "2173", "endLine": 35, "endColumn": 54}, {"ruleId": "2170", "severity": 1, "message": "2245", "line": 37, "column": 17, "nodeType": "2172", "messageId": "2173", "endLine": 37, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2460", "line": 91, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 91, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2461", "line": 92, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 92, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2462", "line": 98, "column": 20, "nodeType": "2172", "messageId": "2173", "endLine": 98, "endColumn": 31}, {"ruleId": "2170", "severity": 1, "message": "2463", "line": 99, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 99, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2464", "line": 99, "column": 22, "nodeType": "2172", "messageId": "2173", "endLine": 99, "endColumn": 35}, {"ruleId": "2170", "severity": 1, "message": "2465", "line": 100, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 100, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2466", "line": 100, "column": 20, "nodeType": "2172", "messageId": "2173", "endLine": 100, "endColumn": 31}, {"ruleId": "2170", "severity": 1, "message": "2467", "line": 103, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 103, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2468", "line": 104, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 104, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2469", "line": 105, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 105, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2470", "line": 106, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 106, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2471", "line": 136, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 136, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2472", "line": 137, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 137, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2473", "line": 139, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 139, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2474", "line": 139, "column": 25, "nodeType": "2172", "messageId": "2173", "endLine": 139, "endColumn": 41}, {"ruleId": "2170", "severity": 1, "message": "2475", "line": 167, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 167, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2476", "line": 173, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 173, "endColumn": 33}, {"ruleId": "2177", "severity": 1, "message": "2477", "line": 286, "column": 6, "nodeType": "2179", "endLine": 286, "endColumn": 32, "suggestions": "2478"}, {"ruleId": "2170", "severity": 1, "message": "2479", "line": 364, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 364, "endColumn": 23}, {"ruleId": "2177", "severity": 1, "message": "2480", "line": 742, "column": 6, "nodeType": "2179", "endLine": 742, "endColumn": 25, "suggestions": "2481"}, {"ruleId": "2170", "severity": 1, "message": "2482", "line": 764, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 764, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2483", "line": 776, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 776, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2453", "line": 8, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2256", "line": 19, "column": 3, "nodeType": "2172", "messageId": "2173", "endLine": 19, "endColumn": 8}, {"ruleId": "2170", "severity": 1, "message": "2484", "line": 20, "column": 3, "nodeType": "2172", "messageId": "2173", "endLine": 20, "endColumn": 10}, {"ruleId": "2170", "severity": 1, "message": "2485", "line": 30, "column": 36, "nodeType": "2172", "messageId": "2173", "endLine": 30, "endColumn": 56}, {"ruleId": "2170", "severity": 1, "message": "2486", "line": 32, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 32, "endColumn": 30}, {"ruleId": "2170", "severity": 1, "message": "2487", "line": 32, "column": 32, "nodeType": "2172", "messageId": "2173", "endLine": 32, "endColumn": 55}, {"ruleId": "2170", "severity": 1, "message": "2488", "line": 2, "column": 26, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 32}, {"ruleId": "2177", "severity": 1, "message": "2203", "line": 35, "column": 29, "nodeType": "2172", "endLine": 35, "endColumn": 40}, {"ruleId": "2177", "severity": 1, "message": "2203", "line": 35, "column": 30, "nodeType": "2172", "endLine": 35, "endColumn": 41}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 347, "column": 68, "nodeType": "2229", "messageId": "2230", "endLine": 347, "endColumn": 70}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 360, "column": 64, "nodeType": "2229", "messageId": "2230", "endLine": 360, "endColumn": 66}, {"ruleId": "2170", "severity": 1, "message": "2278", "line": 1, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2489", "line": 12, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 12, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2198", "line": 24, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 24, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2199", "line": 26, "column": 119, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 123}, {"ruleId": "2170", "severity": 1, "message": "2490", "line": 26, "column": 135, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 140}, {"ruleId": "2170", "severity": 1, "message": "2200", "line": 99, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 99, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2245", "line": 107, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 107, "endColumn": 19}, {"ruleId": "2177", "severity": 1, "message": "2491", "line": 217, "column": 8, "nodeType": "2179", "endLine": 217, "endColumn": 38, "suggestions": "2492"}, {"ruleId": "2177", "severity": 1, "message": "2493", "line": 67, "column": 8, "nodeType": "2179", "endLine": 67, "endColumn": 47, "suggestions": "2494"}, {"ruleId": "2170", "severity": 1, "message": "2207", "line": 4, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2255", "line": 17, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 17, "endColumn": 11}, {"ruleId": "2170", "severity": 1, "message": "2190", "line": 20, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 20, "endColumn": 9}, {"ruleId": "2170", "severity": 1, "message": "2495", "line": 27, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 27, "endColumn": 32}, {"ruleId": "2177", "severity": 1, "message": "2496", "line": 122, "column": 8, "nodeType": "2179", "endLine": 122, "endColumn": 10, "suggestions": "2497"}, {"ruleId": "2170", "severity": 1, "message": "2282", "line": 6, "column": 56, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 64}, {"ruleId": "2170", "severity": 1, "message": "2369", "line": 6, "column": 73, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 79}, {"ruleId": "2170", "severity": 1, "message": "2257", "line": 6, "column": 81, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 91}, {"ruleId": "2170", "severity": 1, "message": "2498", "line": 5, "column": 60, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 66}, {"ruleId": "2170", "severity": 1, "message": "2429", "line": 5, "column": 68, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 74}, {"ruleId": "2170", "severity": 1, "message": "2499", "line": 2, "column": 42, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 51}, {"ruleId": "2170", "severity": 1, "message": "2500", "line": 5, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 8, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2459", "line": 16, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 16, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2501", "line": 17, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 17, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2182", "line": 1, "column": 21, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2502", "line": 3, "column": 58, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 69}, {"ruleId": "2170", "severity": 1, "message": "2503", "line": 3, "column": 71, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 81}, {"ruleId": "2170", "severity": 1, "message": "2504", "line": 48, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 48, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2505", "line": 56, "column": 9, "nodeType": "2172", "messageId": "2173", "endLine": 56, "endColumn": 29}, {"ruleId": "2177", "severity": 1, "message": "2203", "line": 37, "column": 30, "nodeType": "2172", "endLine": 37, "endColumn": 41}, {"ruleId": "2177", "severity": 1, "message": "2203", "line": 35, "column": 32, "nodeType": "2172", "endLine": 35, "endColumn": 43}, {"ruleId": "2170", "severity": 1, "message": "2282", "line": 3, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2256", "line": 3, "column": 20, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2506", "line": 3, "column": 27, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 38}, {"ruleId": "2170", "severity": 1, "message": "2301", "line": 3, "column": 40, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 52}, {"ruleId": "2170", "severity": 1, "message": "2502", "line": 3, "column": 54, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 65}, {"ruleId": "2170", "severity": 1, "message": "2507", "line": 6, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 32}, {"ruleId": "2170", "severity": 1, "message": "2508", "line": 8, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2509", "line": 8, "column": 25, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 39}, {"ruleId": "2170", "severity": 1, "message": "2182", "line": 2, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2282", "line": 3, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2506", "line": 3, "column": 27, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 38}, {"ruleId": "2170", "severity": 1, "message": "2301", "line": 3, "column": 40, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 52}, {"ruleId": "2170", "severity": 1, "message": "2426", "line": 4, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2510", "line": 5, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 30}, {"ruleId": "2170", "severity": 1, "message": "2511", "line": 7, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2508", "line": 8, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2512", "line": 9, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 9, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2459", "line": 15, "column": 39, "nodeType": "2172", "messageId": "2173", "endLine": 15, "endColumn": 46}, {"ruleId": "2170", "severity": 1, "message": "2359", "line": 17, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 17, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2513", "line": 18, "column": 37, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 48}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 181, "column": 54, "nodeType": "2229", "messageId": "2230", "endLine": 181, "endColumn": 56}, {"ruleId": "2177", "severity": 1, "message": "2514", "line": 57, "column": 8, "nodeType": "2179", "endLine": 57, "endColumn": 38, "suggestions": "2515"}, {"ruleId": "2170", "severity": 1, "message": "2278", "line": 1, "column": 17, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2516", "line": 2, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 26}, {"ruleId": "2227", "severity": 1, "message": "2344", "line": 186, "column": 50, "nodeType": "2229", "messageId": "2230", "endLine": 186, "endColumn": 52}, {"ruleId": "2170", "severity": 1, "message": "2517", "line": 79, "column": 29, "nodeType": "2172", "messageId": "2173", "endLine": 79, "endColumn": 45}, {"ruleId": "2170", "severity": 1, "message": "2518", "line": 79, "column": 47, "nodeType": "2172", "messageId": "2173", "endLine": 79, "endColumn": 59}, {"ruleId": "2170", "severity": 1, "message": "2506", "line": 1, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 21}, {"ruleId": "2177", "severity": 1, "message": "2519", "line": 51, "column": 8, "nodeType": "2179", "endLine": 51, "endColumn": 10, "suggestions": "2520"}, {"ruleId": "2170", "severity": 1, "message": "2182", "line": 1, "column": 17, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 25}, {"ruleId": "2177", "severity": 1, "message": "2521", "line": 33, "column": 8, "nodeType": "2179", "endLine": 33, "endColumn": 17, "suggestions": "2522"}, {"ruleId": "2170", "severity": 1, "message": "2182", "line": 6, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2523", "line": 7, "column": 46, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 56}, {"ruleId": "2170", "severity": 1, "message": "2524", "line": 7, "column": 72, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 82}, {"ruleId": "2170", "severity": 1, "message": "2525", "line": 8, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2526", "line": 66, "column": 107, "nodeType": "2172", "messageId": "2173", "endLine": 66, "endColumn": 114}, {"ruleId": "2177", "severity": 1, "message": "2527", "line": 73, "column": 8, "nodeType": "2179", "endLine": 73, "endColumn": 36, "suggestions": "2528"}, {"ruleId": "2177", "severity": 1, "message": "2527", "line": 78, "column": 8, "nodeType": "2179", "endLine": 78, "endColumn": 26, "suggestions": "2529"}, {"ruleId": "2170", "severity": 1, "message": "2530", "line": 7, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2277", "line": 18, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 31}, {"ruleId": "2170", "severity": 1, "message": "2277", "line": 5, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 31}, {"ruleId": "2170", "severity": 1, "message": "2531", "line": 8, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 16}, {"ruleId": "2170", "severity": 1, "message": "2532", "line": 3, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2533", "line": 3, "column": 22, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 29}, {"ruleId": "2170", "severity": 1, "message": "2534", "line": 3, "column": 31, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 38}, {"ruleId": "2170", "severity": 1, "message": "2535", "line": 3, "column": 40, "nodeType": "2172", "messageId": "2173", "endLine": 3, "endColumn": 50}, {"ruleId": "2170", "severity": 1, "message": "2536", "line": 7, "column": 12, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 17}, {"ruleId": "2170", "severity": 1, "message": "2537", "line": 7, "column": 19, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2182", "line": 1, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2278", "line": 1, "column": 31, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 34}, {"ruleId": "2170", "severity": 1, "message": "2538", "line": 75, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 75, "endColumn": 28}, {"ruleId": "2170", "severity": 1, "message": "2459", "line": 75, "column": 30, "nodeType": "2172", "messageId": "2173", "endLine": 75, "endColumn": 37}, {"ruleId": "2170", "severity": 1, "message": "2448", "line": 75, "column": 39, "nodeType": "2172", "messageId": "2173", "endLine": 75, "endColumn": 49}, {"ruleId": "2170", "severity": 1, "message": "2239", "line": 13, "column": 45, "nodeType": "2172", "messageId": "2173", "endLine": 13, "endColumn": 54}, {"ruleId": "2170", "severity": 1, "message": "2539", "line": 66, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 66, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2216", "line": 67, "column": 26, "nodeType": "2172", "messageId": "2173", "endLine": 67, "endColumn": 36}, {"ruleId": "2170", "severity": 1, "message": "2531", "line": 20, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 20, "endColumn": 16}, {"ruleId": "2186", "severity": 1, "message": "2187", "line": 77, "column": 21, "nodeType": "2188", "endLine": 81, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2235", "line": 1, "column": 35, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 44}, {"ruleId": "2170", "severity": 1, "message": "2540", "line": 2, "column": 21, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2541", "line": 11, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 11, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2239", "line": 96, "column": 29, "nodeType": "2172", "messageId": "2173", "endLine": 96, "endColumn": 38}, {"ruleId": "2170", "severity": 1, "message": "2542", "line": 166, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 166, "endColumn": 18}, {"ruleId": "2170", "severity": 1, "message": "2182", "line": 1, "column": 17, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 25}, {"ruleId": "2170", "severity": 1, "message": "2543", "line": 7, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 17}, {"ruleId": "2170", "severity": 1, "message": "2544", "line": 7, "column": 19, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 30}, {"ruleId": "2170", "severity": 1, "message": "2545", "line": 7, "column": 32, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 40}, {"ruleId": "2170", "severity": 1, "message": "2546", "line": 7, "column": 42, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 50}, {"ruleId": "2170", "severity": 1, "message": "2547", "line": 7, "column": 52, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 60}, {"ruleId": "2170", "severity": 1, "message": "2548", "line": 7, "column": 76, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 90}, {"ruleId": "2170", "severity": 1, "message": "2270", "line": 8, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 24}, {"ruleId": "2170", "severity": 1, "message": "2266", "line": 9, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 9, "endColumn": 28}, {"ruleId": "2170", "severity": 1, "message": "2549", "line": 10, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 10, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2550", "line": 11, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 11, "endColumn": 17}, {"ruleId": "2170", "severity": 1, "message": "2271", "line": 12, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 12, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2236", "line": 13, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 13, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2190", "line": 17, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 17, "endColumn": 9}, {"ruleId": "2170", "severity": 1, "message": "2551", "line": 18, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 8}, {"ruleId": "2170", "severity": 1, "message": "2552", "line": 19, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 19, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2553", "line": 20, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 20, "endColumn": 9}, {"ruleId": "2170", "severity": 1, "message": "2531", "line": 21, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 21, "endColumn": 11}, {"ruleId": "2170", "severity": 1, "message": "2554", "line": 22, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 22, "endColumn": 9}, {"ruleId": "2170", "severity": 1, "message": "2502", "line": 23, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 23, "endColumn": 16}, {"ruleId": "2170", "severity": 1, "message": "2301", "line": 24, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 24, "endColumn": 17}, {"ruleId": "2170", "severity": 1, "message": "2506", "line": 25, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 25, "endColumn": 16}, {"ruleId": "2170", "severity": 1, "message": "2256", "line": 26, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 26, "endColumn": 10}, {"ruleId": "2170", "severity": 1, "message": "2281", "line": 27, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 27, "endColumn": 10}, {"ruleId": "2170", "severity": 1, "message": "2279", "line": 28, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 28, "endColumn": 13}, {"ruleId": "2170", "severity": 1, "message": "2555", "line": 29, "column": 14, "nodeType": "2172", "messageId": "2173", "endLine": 29, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2540", "line": 30, "column": 5, "nodeType": "2172", "messageId": "2173", "endLine": 30, "endColumn": 11}, {"ruleId": "2170", "severity": 1, "message": "2556", "line": 32, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 32, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2557", "line": 44, "column": 19, "nodeType": "2172", "messageId": "2173", "endLine": 44, "endColumn": 30}, {"ruleId": "2177", "severity": 1, "message": "2558", "line": 52, "column": 8, "nodeType": "2179", "endLine": 52, "endColumn": 18, "suggestions": "2559"}, {"ruleId": "2170", "severity": 1, "message": "2194", "line": 54, "column": 123, "nodeType": "2172", "messageId": "2173", "endLine": 54, "endColumn": 132}, {"ruleId": "2170", "severity": 1, "message": "2560", "line": 7, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 26}, {"ruleId": "2170", "severity": 1, "message": "2561", "line": 8, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 8, "endColumn": 26}, {"ruleId": "2170", "severity": 1, "message": "2562", "line": 9, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 9, "endColumn": 22}, {"ruleId": "2170", "severity": 1, "message": "2563", "line": 97, "column": 23, "nodeType": "2172", "messageId": "2173", "endLine": 97, "endColumn": 32}, {"ruleId": "2170", "severity": 1, "message": "2564", "line": 97, "column": 34, "nodeType": "2172", "messageId": "2173", "endLine": 97, "endColumn": 42}, {"ruleId": "2170", "severity": 1, "message": "2565", "line": 332, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 332, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2566", "line": 4, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 26}, {"ruleId": "2170", "severity": 1, "message": "2564", "line": 35, "column": 34, "nodeType": "2172", "messageId": "2173", "endLine": 35, "endColumn": 42}, {"ruleId": "2177", "severity": 1, "message": "2567", "line": 411, "column": 8, "nodeType": "2179", "endLine": 411, "endColumn": 118, "suggestions": "2568"}, {"ruleId": "2186", "severity": 1, "message": "2187", "line": 554, "column": 21, "nodeType": "2188", "endLine": 558, "endColumn": 23}, {"ruleId": "2177", "severity": 1, "message": "2569", "line": 17, "column": 8, "nodeType": "2179", "endLine": 17, "endColumn": 24, "suggestions": "2570"}, {"ruleId": "2177", "severity": 1, "message": "2571", "line": 17, "column": 9, "nodeType": "2572", "endLine": 17, "endColumn": 16}, {"ruleId": "2170", "severity": 1, "message": "2235", "line": 1, "column": 35, "nodeType": "2172", "messageId": "2173", "endLine": 1, "endColumn": 44}, {"ruleId": "2170", "severity": 1, "message": "2540", "line": 2, "column": 21, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2573", "line": 2, "column": 13, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 20}, {"ruleId": "2170", "severity": 1, "message": "2574", "line": 4, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 4, "endColumn": 26}, {"ruleId": "2170", "severity": 1, "message": "2271", "line": 5, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 5, "endColumn": 21}, {"ruleId": "2170", "severity": 1, "message": "2277", "line": 6, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 6, "endColumn": 31}, {"ruleId": "2170", "severity": 1, "message": "2575", "line": 7, "column": 10, "nodeType": "2172", "messageId": "2173", "endLine": 7, "endColumn": 27}, {"ruleId": "2170", "severity": 1, "message": "2576", "line": 22, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 22, "endColumn": 23}, {"ruleId": "2170", "severity": 1, "message": "2577", "line": 43, "column": 28, "nodeType": "2172", "messageId": "2173", "endLine": 43, "endColumn": 41}, {"ruleId": "2170", "severity": 1, "message": "2578", "line": 45, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 45, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2577", "line": 123, "column": 28, "nodeType": "2172", "messageId": "2173", "endLine": 123, "endColumn": 41}, {"ruleId": "2170", "severity": 1, "message": "2579", "line": 125, "column": 11, "nodeType": "2172", "messageId": "2173", "endLine": 125, "endColumn": 19}, {"ruleId": "2170", "severity": 1, "message": "2577", "line": 201, "column": 28, "nodeType": "2172", "messageId": "2173", "endLine": 201, "endColumn": 41}, {"ruleId": "2170", "severity": 1, "message": "2196", "line": 2, "column": 23, "nodeType": "2172", "messageId": "2173", "endLine": 2, "endColumn": 34}, {"ruleId": "2170", "severity": 1, "message": "2271", "line": 18, "column": 8, "nodeType": "2172", "messageId": "2173", "endLine": 18, "endColumn": 21}, "no-unused-vars", "'AdminUserSearchPage' is defined but never used.", "Identifier", "unusedVar", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'BeeMathLogo' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.username'. Either include it or remove the dependency array.", "ArrayExpression", ["2580"], "'resultAction' is assigned a value but never used.", "'useState' is defined but never used.", "'isFilterVIew' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", ["2581"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'setIsFilterView' is defined but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "'loadingExam' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2582"], "'showAddStudent' is assigned a value but never used.", "'AdminLayout' is defined but never used.", "'setAttempts' is defined but never used.", "'ScoreDistributionChart' is defined but never used.", "'setLogs' is assigned a value but never used.", "'setAnswers' is assigned a value but never used.", "'prevRanks' is assigned a value but never used.", "'setRankChanges' is assigned a value but never used.", "'setUpdatedAttemptId' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'handleClickedDetail' is assigned a value but never used.", "'handleClickedPreviewExam' is assigned a value but never used.", "'handleClickedQuestions' is assigned a value but never used.", "'isAddView' is assigned a value but never used.", "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'handleClickedTracking' is assigned a value but never used.", "'errorMsg' is assigned a value but never used.", "'setExam' is defined but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'backgroundImage' is defined but never used.", "'shouldFloat' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2583"], "'useEffect' is defined but never used.", "'LoadingSpinner' is defined but never used.", "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'setSortOrder' is defined but never used.", "'resetFilters' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2584"], "'processInputForUpdate' is defined but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'setDeleteMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2585"], "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2586"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2587"], "'setClass' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "'LatexRenderer' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2588"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'use' is defined but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2589"], "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2590"], "'ExamDefaultImage' is defined but never used.", "'imageUrl' is assigned a value but never used.", "'ActionButton' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2591"], "'ChevronRight' is defined but never used.", "'toggleDropdown' is defined but never used.", "'toggleExamDropdown' is defined but never used.", "'User' is defined but never used.", "'dropdown' is assigned a value but never used.", "'tuitionDropdown' is assigned a value but never used.", "'examDropdown' is assigned a value but never used.", "'icon5' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2592"], "no-useless-escape", "Unnecessary escape character: \\}.", "Literal", "unnecessaryEscape", ["2593", "2594"], "Unnecessary escape character: \\{.", ["2595", "2596"], ["2597", "2598"], ["2599", "2600"], "Unnecessary escape character: \\..", ["2601", "2602"], ["2603", "2604"], ["2605", "2606"], "Unnecessary escape character: \\).", ["2607", "2608"], ["2609", "2610"], ["2611", "2612"], ["2613", "2614"], ["2615", "2616"], ["2617", "2618"], ["2619", "2620"], ["2621", "2622"], ["2623", "2624"], ["2625", "2626"], ["2627", "2628"], "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2629"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2630"], "'InputSearch' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'menuOpen' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'toggleCloseSidebar' is defined but never used.", "'ArrowRight' is defined but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "'isHovered' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2631"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2632"], "'currentTime' is assigned a value but never used.", "'setSuccess' is defined but never used.", "'success' is assigned a value but never used.", "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchExams' and 'isClassroomExam'. Either include them or remove the dependency array.", ["2633"], ["2634"], "'isMobile' is assigned a value but never used.", "'currentQuestionType' is assigned a value but never used.", "The 'allQuestions' array makes the dependencies of useEffect Hook (at line 160) change on every render. To fix this, wrap the initialization of 'allQuestions' in its own useMemo() Hook.", "VariableDeclarator", "'isFullscreen' is defined but never used.", "React Hook useEffect has missing dependencies: 'examId' and 'navigate'. Either include them or remove the dependency array.", ["2635"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 668) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2636"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 679) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2637"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 691) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2638"], "'addErrorQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'flag'. Either include it or remove the dependency array.", ["2639"], "'result' is assigned a value but never used.", "no-sequences", "Unexpected use of comma operator.", "SequenceExpression", "unexpectedCommaExpression", "React Hook useEffect has a missing dependency: 'handleAutoSubmit'. Either include it or remove the dependency array.", ["2640"], "React Hook useEffect has missing dependencies: 'dispatch', 'exam?.isCheatingCheckEnabled', 'user.firstName', and 'user.lastName'. Either include them or remove the dependency array.", ["2641"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'ScoreBarChart' is defined but never used.", ["2642"], "'NoDataFound' is defined but never used.", "'BookMarked' is defined but never used.", "'ExternalLink' is defined but never used.", "'ButtonSidebar' is assigned a value but never used.", "'choice' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2643"], "'ChevronDown' is defined but never used.", "'ChevronUp' is defined but never used.", "'UserLayout' is defined but never used.", "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2644"], "'Filter' is defined but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2645"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2646"], ["2647"], ["2648"], "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'BarChart2' is defined but never used.", "'articles' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'formatNumberWithDots' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'loading' is assigned a value but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterGraduationYear', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2649"], "'handleBatchAdd' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tuitionPayments'. Either include it or remove the dependency array.", ["2650"], "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'Receipt' is defined but never used.", "'studentClassTuitions' is assigned a value but never used.", "'classTuitionsLoading' is assigned a value but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2651"], "React Hook useEffect has a missing dependency: 'user?.id'. Either include it or remove the dependency array.", ["2652"], "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2653"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", "'CheckCircle' is defined but never used.", "'DollarSign' is defined but never used.", "'getStatusBadge' is assigned a value but never used.", "'getPaymentStatusIcon' is assigned a value but never used.", "'ChevronLeft' is defined but never used.", "'getLearningItemWeekend' is defined but never used.", "'resetCalendar' is defined but never used.", "'setSelectedDay' is defined but never used.", "'fetchClassesOverview' is defined but never used.", "'CalendarMonth' is defined but never used.", "'SidebarCalendar' is defined but never used.", "'selectedDay' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'firstGridDate' and 'lastGridDate'. Either include them or remove the dependency array.", ["2654"], "'NavigateTimeButton' is defined but never used.", "'recentActivities' is assigned a value but never used.", "'systemStatus' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'onClose'. Either include it or remove the dependency array. If 'onClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2655"], "React Hook useEffect has missing dependencies: 'dispatch' and 'exam.id'. Either include them or remove the dependency array.", ["2656"], "'commitExam' is defined but never used.", "'setConfirm' is defined but never used.", "'ConfirmModal' is defined but never used.", "'confirm' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'isChange'. Either include them or remove the dependency array.", ["2657"], ["2658"], "'AddImagesModal' is defined but never used.", "'Trash2' is defined but never used.", "'TableAdmin' is defined but never used.", "'TdAdmin' is defined but never used.", "'ThAdmin' is defined but never used.", "'TheadAdmin' is defined but never used.", "'month' is assigned a value but never used.", "'setMonth' is assigned a value but never used.", "'tuitionPayments' is assigned a value but never used.", "'userAttempts' is assigned a value but never used.", "'Upload' is defined but never used.", "'Pagination' is defined but never used.", "'staff' is assigned a value but never used.", "'setStep' is defined but never used.", "'setExamData' is defined but never used.", "'postExam' is defined but never used.", "'nextStep' is defined but never used.", "'prevStep' is defined but never used.", "'setCreatedExam' is defined but never used.", "'ImageUpload' is defined but never used.", "'UploadPdf' is defined but never used.", "'Eye' is defined but never used.", "'FileText' is defined but never used.", "'Plus' is defined but never used.", "'Edit' is defined but never used.", "'ImageIcon' is defined but never used.", "'resetData' is defined but never used.", "'createdExam' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'folder'. Either include it or remove the dependency array.", ["2659"], "'questionUntil' is defined but never used.", "'useDebouncedEffect' is defined but never used.", "'setQuestions' is defined but never used.", "'examImage' is assigned a value but never used.", "'examFile' is assigned a value but never used.", "'examData' is assigned a value but never used.", "'fetchCodesByType' is defined but never used.", "React Hook useEffect has a missing dependency: 'isViewAdd'. Either include it or remove the dependency array.", ["2660"], "React Hook useEffect has a missing dependency: 'effect'. Either include it or remove the dependency array. If 'effect' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2661"], "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", "'examApi' is defined but never used.", "'setSelectedIndex' is defined but never used.", "'reorderStatements' is defined but never used.", "'QuestionContent' is defined but never used.", "'selectedIndex' is assigned a value but never used.", "'prefixTN' is assigned a value but never used.", "'prefixDS' is assigned a value but never used.", {"desc": "2662", "fix": "2663"}, {"desc": "2664", "fix": "2665"}, {"desc": "2666", "fix": "2667"}, {"desc": "2668", "fix": "2669"}, {"desc": "2670", "fix": "2671"}, {"desc": "2672", "fix": "2673"}, {"desc": "2674", "fix": "2675"}, {"desc": "2676", "fix": "2677"}, {"desc": "2678", "fix": "2679"}, {"desc": "2680", "fix": "2681"}, {"desc": "2682", "fix": "2683"}, {"desc": "2684", "fix": "2685"}, {"desc": "2686", "fix": "2687"}, {"messageId": "2688", "fix": "2689", "desc": "2690"}, {"messageId": "2691", "fix": "2692", "desc": "2693"}, {"messageId": "2688", "fix": "2694", "desc": "2690"}, {"messageId": "2691", "fix": "2695", "desc": "2693"}, {"messageId": "2688", "fix": "2696", "desc": "2690"}, {"messageId": "2691", "fix": "2697", "desc": "2693"}, {"messageId": "2688", "fix": "2698", "desc": "2690"}, {"messageId": "2691", "fix": "2699", "desc": "2693"}, {"messageId": "2688", "fix": "2700", "desc": "2690"}, {"messageId": "2691", "fix": "2701", "desc": "2693"}, {"messageId": "2688", "fix": "2702", "desc": "2690"}, {"messageId": "2691", "fix": "2703", "desc": "2693"}, {"messageId": "2688", "fix": "2704", "desc": "2690"}, {"messageId": "2691", "fix": "2705", "desc": "2693"}, {"messageId": "2688", "fix": "2706", "desc": "2690"}, {"messageId": "2691", "fix": "2707", "desc": "2693"}, {"messageId": "2688", "fix": "2708", "desc": "2690"}, {"messageId": "2691", "fix": "2709", "desc": "2693"}, {"messageId": "2688", "fix": "2710", "desc": "2690"}, {"messageId": "2691", "fix": "2711", "desc": "2693"}, {"messageId": "2688", "fix": "2712", "desc": "2690"}, {"messageId": "2691", "fix": "2713", "desc": "2693"}, {"messageId": "2688", "fix": "2714", "desc": "2690"}, {"messageId": "2691", "fix": "2715", "desc": "2693"}, {"messageId": "2688", "fix": "2716", "desc": "2690"}, {"messageId": "2691", "fix": "2717", "desc": "2693"}, {"messageId": "2688", "fix": "2718", "desc": "2690"}, {"messageId": "2691", "fix": "2719", "desc": "2693"}, {"messageId": "2688", "fix": "2720", "desc": "2690"}, {"messageId": "2691", "fix": "2721", "desc": "2693"}, {"messageId": "2688", "fix": "2722", "desc": "2690"}, {"messageId": "2691", "fix": "2723", "desc": "2693"}, {"messageId": "2688", "fix": "2724", "desc": "2690"}, {"messageId": "2691", "fix": "2725", "desc": "2693"}, {"messageId": "2688", "fix": "2726", "desc": "2690"}, {"messageId": "2691", "fix": "2727", "desc": "2693"}, {"desc": "2728", "fix": "2729"}, {"desc": "2730", "fix": "2731"}, {"desc": "2732", "fix": "2733"}, {"desc": "2734", "fix": "2735"}, {"desc": "2736", "fix": "2737"}, {"desc": "2738", "fix": "2739"}, {"desc": "2740", "fix": "2741"}, {"desc": "2742", "fix": "2743"}, {"desc": "2742", "fix": "2744"}, {"desc": "2742", "fix": "2745"}, {"desc": "2746", "fix": "2747"}, {"desc": "2748", "fix": "2749"}, {"desc": "2750", "fix": "2751"}, {"desc": "2740", "fix": "2752"}, {"desc": "2753", "fix": "2754"}, {"desc": "2755", "fix": "2756"}, {"desc": "2757", "fix": "2758"}, {"desc": "2759", "fix": "2760"}, {"desc": "2736", "fix": "2761"}, {"desc": "2738", "fix": "2762"}, {"desc": "2763", "fix": "2764"}, {"desc": "2765", "fix": "2766"}, {"desc": "2767", "fix": "2768"}, {"desc": "2769", "fix": "2770"}, {"desc": "2771", "fix": "2772"}, {"desc": "2773", "fix": "2774"}, {"desc": "2775", "fix": "2776"}, {"desc": "2777", "fix": "2778"}, {"desc": "2779", "fix": "2780"}, {"desc": "2781", "fix": "2782"}, {"desc": "2783", "fix": "2784"}, {"desc": "2785", "fix": "2786"}, {"desc": "2787", "fix": "2788"}, "Update the dependencies array to be: [user, navigate, formData.username]", {"range": "2789", "text": "2790"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "2791", "text": "2792"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "2793", "text": "2794"}, "Update the dependencies array to be: [options, selected, type]", {"range": "2795", "text": "2796"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "2797", "text": "2798"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "2799", "text": "2800"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "2801", "text": "2802"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "2803", "text": "2804"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "2805", "text": "2806"}, "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "2807", "text": "2808"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "2809", "text": "2810"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "2811", "text": "2812"}, "Update the dependencies array to be: [maxLength]", {"range": "2813", "text": "2814"}, "removeEscape", {"range": "2815", "text": "2816"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2817", "text": "2818"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "2819", "text": "2816"}, {"range": "2820", "text": "2818"}, {"range": "2821", "text": "2816"}, {"range": "2822", "text": "2818"}, {"range": "2823", "text": "2816"}, {"range": "2824", "text": "2818"}, {"range": "2825", "text": "2816"}, {"range": "2826", "text": "2818"}, {"range": "2827", "text": "2816"}, {"range": "2828", "text": "2818"}, {"range": "2829", "text": "2816"}, {"range": "2830", "text": "2818"}, {"range": "2831", "text": "2816"}, {"range": "2832", "text": "2818"}, {"range": "2833", "text": "2816"}, {"range": "2834", "text": "2818"}, {"range": "2835", "text": "2816"}, {"range": "2836", "text": "2818"}, {"range": "2837", "text": "2816"}, {"range": "2838", "text": "2818"}, {"range": "2839", "text": "2816"}, {"range": "2840", "text": "2818"}, {"range": "2841", "text": "2816"}, {"range": "2842", "text": "2818"}, {"range": "2843", "text": "2816"}, {"range": "2844", "text": "2818"}, {"range": "2845", "text": "2816"}, {"range": "2846", "text": "2818"}, {"range": "2847", "text": "2816"}, {"range": "2848", "text": "2818"}, {"range": "2849", "text": "2816"}, {"range": "2850", "text": "2818"}, {"range": "2851", "text": "2816"}, {"range": "2852", "text": "2818"}, "Update the dependencies array to be: [handlePaste]", {"range": "2853", "text": "2854"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "2855", "text": "2856"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "2857", "text": "2858"}, "Update the dependencies array to be: [handleFile]", {"range": "2859", "text": "2860"}, "Update the dependencies array to be: [dispatch, fetchExams, isClassroomExam, isSearch]", {"range": "2861", "text": "2862"}, "Update the dependencies array to be: [currentPage, fetchExams, isClassroomExam]", {"range": "2863", "text": "2864"}, "Update the dependencies array to be: [exam, examId, navigate]", {"range": "2865", "text": "2866"}, "Wrap the definition of 'addQuestion' in its own useCallback() Hook.", {"range": "2867", "text": "2868"}, {"range": "2869", "text": "2868"}, {"range": "2870", "text": "2868"}, "Update the dependencies array to be: [flag, remainingTime]", {"range": "2871", "text": "2872"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", {"range": "2873", "text": "2874"}, "Update the dependencies array to be: [user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", {"range": "2875", "text": "2876"}, {"range": "2877", "text": "2866"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "2878", "text": "2879"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "2880", "text": "2881"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "2882", "text": "2883"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "2884", "text": "2885"}, {"range": "2886", "text": "2862"}, {"range": "2887", "text": "2864"}, "Update the dependencies array to be: [dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "2888", "text": "2889"}, "Update the dependencies array to be: [tuitionPayments, tuitionStatistics]", {"range": "2890", "text": "2891"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, monthTuition, dispatch]", {"range": "2892", "text": "2893"}, "Update the dependencies array to be: [dispatch, selectedYear, selectedMonth, user?.id]", {"range": "2894", "text": "2895"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "2896", "text": "2897"}, "Update the dependencies array to be: [dispatch, currentMonth, view, firstGridDate, lastGridDate]", {"range": "2898", "text": "2899"}, "Update the dependencies array to be: [onClose]", {"range": "2900", "text": "2901"}, "Update the dependencies array to be: [confirm, dispatch, exam.id]", {"range": "2902", "text": "2903"}, "Update the dependencies array to be: [dispatch, isChange, questions, questionsEdited]", {"range": "2904", "text": "2905"}, "Update the dependencies array to be: [exam, editedExam, isChange, dispatch]", {"range": "2906", "text": "2907"}, "Update the dependencies array to be: [dispatch, folder]", {"range": "2908", "text": "2909"}, "Update the dependencies array to be: [questionT<PERSON>ontent, correctAnswerTN, questionDS<PERSON>ontent, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", {"range": "2910", "text": "2911"}, "Update the dependencies array to be: [delay, effect]", {"range": "2912", "text": "2913"}, [1894, 1910], "[user, navigate, formData.username]", [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [9548, 9558], "[dispatch, fetchPdfFiles]", [669, 671], "[options, selected, type]", [1903, 1921], "[dispatch, fetchQuestions, params]", [1569, 1614], "[dispatch, search, page, pageSize, sortOrder, classId]", [2683, 2705], "[inputValue, dispatch, setSearch]", [2124, 2148], "[codes, question, question.class]", [2461, 2481], "[codes, exam, exam?.class]", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [972, 974], "[maxLength]", [7716, 7717], "", [7716, 7716], "\\", [7718, 7719], [7718, 7718], [7915, 7916], [7915, 7915], [7917, 7918], [7917, 7917], [9661, 9662], [9661, 9661], [10418, 10419], [10418, 10418], [11057, 11058], [11057, 11057], [11059, 11060], [11059, 11059], [11129, 11130], [11129, 11129], [11131, 11132], [11131, 11131], [14006, 14007], [14006, 14006], [15147, 15148], [15147, 15147], [15753, 15754], [15753, 15753], [15755, 15756], [15755, 15755], [15825, 15826], [15825, 15825], [15827, 15828], [15827, 15827], [18697, 18698], [18697, 18697], [19612, 19613], [19612, 19612], [2566, 2568], "[handlePaste]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [2341, 2361], "[dispatch, fetchExams, isClassroomExam, isSearch]", [2439, 2452], "[currentPage, fetchExams, isClassroomExam]", [2482, 2488], "[exam, examId, navigate]", [4594, 4956], "useCallback((questionId) => {\r\n        // Only add to saveQuestions if not already there and not in errorQuestions\r\n        if (!saveQuestions.includes(questionId) && !errorQuestions.includes(questionId)) {\r\n            dispatch(setSaveQuestions([...saveQuestions, questionId]));\r\n        }\r\n        // DO NOT call removeErrorQuestion here - let Redux slice handle it\r\n    })", [4594, 4956], [4594, 4956], [9169, 9184], "[flag, remainingTime]", [31473, 31564], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", [35671, 35699], "[user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", [1496, 1502], [7568, 7578], "[dispatch, limit]", [6802, 6815], "[activeItem?.index, classDetail]", [6098, 6108], "[dispatch, location.search]", [1654, 1676], "[currentPage, didInit, loadReports]", [3871, 3891], [3965, 3978], [10387, 10413], "[dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [24202, 24221], "[tuitionPayments, tuitionStatistics]", [8386, 8416], "[<PERSON><PERSON><PERSON><PERSON>, monthTuition, dispatch]", [2133, 2172], "[dispatch, selected<PERSON>ear, selected<PERSON><PERSON><PERSON>, user?.id]", [3996, 3998], "[dispatch, userId]", [2456, 2486], "[dispatch, currentMonth, view, firstGridDate, lastGridDate]", [2008, 2010], "[onClose]", [1519, 1528], "[confirm, dispatch, exam.id]", [3118, 3146], "[dispatch, isChange, questions, questionsEdited]", [3285, 3303], "[exam, editedExam, isChange, dispatch]", [2136, 2146], "[dispatch, folder]", [17176, 17286], "[question<PERSON><PERSON><PERSON><PERSON>, correctAnswerTN, question<PERSON><PERSON><PERSON>nt, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", [511, 527], "[delay, effect]"]