[{"C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx": "5", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx": "7", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx": "8", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx": "9", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx": "10", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx": "11", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx": "12", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "13", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "14", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx": "15", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx": "16", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "17", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "18", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "19", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "20", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx": "21", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "22", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "23", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx": "24", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js": "25", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js": "26", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js": "27", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js": "28", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js": "29", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js": "30", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js": "31", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js": "32", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js": "33", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js": "34", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js": "35", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js": "36", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js": "37", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js": "38", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js": "39", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx": "40", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx": "41", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx": "42", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx": "43", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx": "44", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx": "45", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx": "46", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "47", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx": "48", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx": "49", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx": "50", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx": "51", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx": "52", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx": "53", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx": "55", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx": "56", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx": "57", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx": "58", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx": "59", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx": "60", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx": "61", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx": "63", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx": "64", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx": "65", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx": "66", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx": "67", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx": "68", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx": "69", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx": "70", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx": "71", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx": "72", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx": "73", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx": "74", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx": "75", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx": "76", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx": "77", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js": "78", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx": "79", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx": "80", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx": "81", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js": "82", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx": "83", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx": "84", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js": "85", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx": "86", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "87", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx": "88", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx": "89", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx": "90", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx": "91", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx": "92", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx": "93", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx": "94", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx": "95", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx": "96", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx": "97", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js": "98", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx": "99", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx": "100", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js": "101", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js": "102", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js": "103", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx": "104", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js": "105", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js": "106", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx": "107", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx": "108", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx": "109", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx": "110", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx": "111", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx": "112", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx": "113", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx": "114", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx": "115", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx": "116", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx": "117", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js": "118", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js": "119", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js": "120", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx": "121", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx": "122", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx": "123", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx": "124", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx": "125", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx": "126", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx": "127", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js": "128", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx": "129", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx": "130", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx": "131", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "132", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js": "133", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx": "134", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx": "135", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx": "136", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx": "137", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx": "138", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx": "139", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx": "140", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx": "141", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx": "142", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx": "143", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js": "144", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js": "145", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx": "146", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx": "147", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx": "148", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx": "149", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx": "150", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx": "151", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx": "152", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx": "153", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx": "154", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "155", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "156", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx": "157", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx": "158", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx": "159", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx": "160", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx": "161", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx": "162", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js": "163", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js": "164", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx": "165", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "166", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx": "167", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx": "168", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js": "169", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx": "170", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx": "171", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx": "172", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx": "173", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx": "174", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx": "175", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx": "176", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx": "177", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx": "178", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx": "179", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx": "180", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx": "181", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx": "182", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx": "183", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx": "184", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx": "185", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "186", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx": "187", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx": "188", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx": "189", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx": "190", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx": "191", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx": "192", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx": "193", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx": "194", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx": "195", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx": "196", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx": "197", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx": "198", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx": "199", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx": "200", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx": "201", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx": "202", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js": "203", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx": "204", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx": "205", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js": "206", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx": "207", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js": "208", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx": "209", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx": "210", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx": "211", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx": "212", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "213", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js": "214", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js": "215", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js": "216", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx": "217", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx": "218", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "219", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "220", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx": "221", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js": "222", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "223", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "224", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js": "225", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "226", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx": "227", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx": "228", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx": "229", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx": "230", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js": "231", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js": "232", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx": "233", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "234", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx": "235", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "236", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "237", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js": "238", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "239", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "240", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "241", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js": "242", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx": "243", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx": "244", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx": "245", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx": "246", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx": "247", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx": "248", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js": "249", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js": "250", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx": "251", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx": "252", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx": "253", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx": "254", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js": "255", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx": "256", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx": "257", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js": "258", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx": "259", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js": "260", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js": "261", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx": "262", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx": "263", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js": "264", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js": "265", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx": "266", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx": "267", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx": "268", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js": "269", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx": "270", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js": "271", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx": "272", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx": "273", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx": "274", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js": "275", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx": "276", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js": "277", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx": "278", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx": "279", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx": "280", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx": "281", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx": "282", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx": "283", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx": "284", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js": "285", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx": "286", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal2.jsx": "287", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js": "288", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx": "289", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js": "290", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js": "291", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx": "292", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx": "293", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx": "294", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx": "295", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx": "296", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx": "297", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx": "298", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx": "299", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx": "300", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx": "301", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx": "302", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx": "303", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx": "304", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx": "305", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js": "306"}, {"size": 837, "mtime": 1748800674146, "results": "307", "hashOfConfig": "308"}, {"size": 375, "mtime": 1744531393988, "results": "309", "hashOfConfig": "308"}, {"size": 12055, "mtime": 1751827979200, "results": "310", "hashOfConfig": "308"}, {"size": 2522, "mtime": 1751514283166, "results": "311", "hashOfConfig": "308"}, {"size": 1183, "mtime": 1751787344260, "results": "312", "hashOfConfig": "308"}, {"size": 7456, "mtime": 1751554069606, "results": "313", "hashOfConfig": "308"}, {"size": 4828, "mtime": 1746378664900, "results": "314", "hashOfConfig": "308"}, {"size": 2152, "mtime": 1751276563455, "results": "315", "hashOfConfig": "308"}, {"size": 1271, "mtime": 1744531393978, "results": "316", "hashOfConfig": "308"}, {"size": 21516, "mtime": 1751827522381, "results": "317", "hashOfConfig": "308"}, {"size": 11683, "mtime": 1748250288653, "results": "318", "hashOfConfig": "308"}, {"size": 1979, "mtime": 1749722105422, "results": "319", "hashOfConfig": "308"}, {"size": 551, "mtime": 1744531393982, "results": "320", "hashOfConfig": "308"}, {"size": 275, "mtime": 1748215697376, "results": "321", "hashOfConfig": "308"}, {"size": 1739, "mtime": 1749721392840, "results": "322", "hashOfConfig": "308"}, {"size": 44964, "mtime": 1750904385044, "results": "323", "hashOfConfig": "308"}, {"size": 9223, "mtime": 1751743443365, "results": "324", "hashOfConfig": "308"}, {"size": 350, "mtime": 1750322483573, "results": "325", "hashOfConfig": "308"}, {"size": 19813, "mtime": 1750323348863, "results": "326", "hashOfConfig": "308"}, {"size": 3066, "mtime": 1751743443365, "results": "327", "hashOfConfig": "308"}, {"size": 2158, "mtime": 1751439211245, "results": "328", "hashOfConfig": "308"}, {"size": 1898, "mtime": 1750325540002, "results": "329", "hashOfConfig": "308"}, {"size": 348, "mtime": 1749202010110, "results": "330", "hashOfConfig": "308"}, {"size": 3322, "mtime": 1751743523736, "results": "331", "hashOfConfig": "308"}, {"size": 9893, "mtime": 1750849306726, "results": "332", "hashOfConfig": "308"}, {"size": 733, "mtime": 1751439773864, "results": "333", "hashOfConfig": "308"}, {"size": 6445, "mtime": 1749721854651, "results": "334", "hashOfConfig": "308"}, {"size": 3345, "mtime": 1751786228120, "results": "335", "hashOfConfig": "308"}, {"size": 3523, "mtime": 1751745797479, "results": "336", "hashOfConfig": "308"}, {"size": 7806, "mtime": 1750325495112, "results": "337", "hashOfConfig": "308"}, {"size": 17715, "mtime": 1751542931344, "results": "338", "hashOfConfig": "308"}, {"size": 14519, "mtime": 1750767145502, "results": "339", "hashOfConfig": "308"}, {"size": 1380, "mtime": 1744531393975, "results": "340", "hashOfConfig": "308"}, {"size": 2737, "mtime": 1750295163218, "results": "341", "hashOfConfig": "308"}, {"size": 4309, "mtime": 1751822374362, "results": "342", "hashOfConfig": "308"}, {"size": 5921, "mtime": 1751786223198, "results": "343", "hashOfConfig": "308"}, {"size": 2480, "mtime": 1747721626218, "results": "344", "hashOfConfig": "308"}, {"size": 5150, "mtime": 1748398556383, "results": "345", "hashOfConfig": "308"}, {"size": 1337, "mtime": 1747720637516, "results": "346", "hashOfConfig": "308"}, {"size": 690, "mtime": 1744531393950, "results": "347", "hashOfConfig": "308"}, {"size": 1339, "mtime": 1751554069606, "results": "348", "hashOfConfig": "308"}, {"size": 673, "mtime": 1744531393976, "results": "349", "hashOfConfig": "308"}, {"size": 1495, "mtime": 1751362330062, "results": "350", "hashOfConfig": "308"}, {"size": 2276, "mtime": 1751554069606, "results": "351", "hashOfConfig": "308"}, {"size": 1151, "mtime": 1749730250381, "results": "352", "hashOfConfig": "308"}, {"size": 3200, "mtime": 1744531393954, "results": "353", "hashOfConfig": "308"}, {"size": 3578, "mtime": 1747223318325, "results": "354", "hashOfConfig": "308"}, {"size": 635, "mtime": 1744531393961, "results": "355", "hashOfConfig": "308"}, {"size": 1228, "mtime": 1751361437565, "results": "356", "hashOfConfig": "308"}, {"size": 4033, "mtime": 1751276343661, "results": "357", "hashOfConfig": "308"}, {"size": 7571, "mtime": 1747902858134, "results": "358", "hashOfConfig": "308"}, {"size": 1218, "mtime": 1744531393963, "results": "359", "hashOfConfig": "308"}, {"size": 9572, "mtime": 1750323036527, "results": "360", "hashOfConfig": "308"}, {"size": 5752, "mtime": 1751276751884, "results": "361", "hashOfConfig": "308"}, {"size": 28691, "mtime": 1744958278477, "results": "362", "hashOfConfig": "308"}, {"size": 19936, "mtime": 1748984865157, "results": "363", "hashOfConfig": "308"}, {"size": 5631, "mtime": 1749721260990, "results": "364", "hashOfConfig": "308"}, {"size": 8139, "mtime": 1749721464118, "results": "365", "hashOfConfig": "308"}, {"size": 911, "mtime": 1744531393943, "results": "366", "hashOfConfig": "308"}, {"size": 3827, "mtime": 1751811244412, "results": "367", "hashOfConfig": "308"}, {"size": 31136, "mtime": 1751782971202, "results": "368", "hashOfConfig": "308"}, {"size": 9572, "mtime": 1749693815954, "results": "369", "hashOfConfig": "308"}, {"size": 1574, "mtime": 1744531393960, "results": "370", "hashOfConfig": "308"}, {"size": 7401, "mtime": 1747223318342, "results": "371", "hashOfConfig": "308"}, {"size": 2914, "mtime": 1747223318342, "results": "372", "hashOfConfig": "308"}, {"size": 28398, "mtime": 1749425223637, "results": "373", "hashOfConfig": "308"}, {"size": 17584, "mtime": 1750758162845, "results": "374", "hashOfConfig": "308"}, {"size": 19119, "mtime": 1750767145501, "results": "375", "hashOfConfig": "308"}, {"size": 1734, "mtime": 1744531393948, "results": "376", "hashOfConfig": "308"}, {"size": 56278, "mtime": 1751372627190, "results": "377", "hashOfConfig": "308"}, {"size": 6448, "mtime": 1749729618233, "results": "378", "hashOfConfig": "308"}, {"size": 5637, "mtime": 1747254491214, "results": "379", "hashOfConfig": "308"}, {"size": 21475, "mtime": 1750325928413, "results": "380", "hashOfConfig": "308"}, {"size": 10256, "mtime": 1744531393939, "results": "381", "hashOfConfig": "308"}, {"size": 1875, "mtime": 1750960485529, "results": "382", "hashOfConfig": "308"}, {"size": 19265, "mtime": 1751742523955, "results": "383", "hashOfConfig": "308"}, {"size": 6162, "mtime": 1748250288622, "results": "384", "hashOfConfig": "308"}, {"size": 3022, "mtime": 1747719253353, "results": "385", "hashOfConfig": "308"}, {"size": 7915, "mtime": 1748392811401, "results": "386", "hashOfConfig": "308"}, {"size": 5844, "mtime": 1751743845208, "results": "387", "hashOfConfig": "308"}, {"size": 3490, "mtime": 1749748056906, "results": "388", "hashOfConfig": "308"}, {"size": 935, "mtime": 1745405710864, "results": "389", "hashOfConfig": "308"}, {"size": 949, "mtime": 1747223318316, "results": "390", "hashOfConfig": "308"}, {"size": 3267, "mtime": 1747354362354, "results": "391", "hashOfConfig": "308"}, {"size": 3005, "mtime": 1750849360884, "results": "392", "hashOfConfig": "308"}, {"size": 2487, "mtime": 1751828219092, "results": "393", "hashOfConfig": "308"}, {"size": 2380, "mtime": 1744531393947, "results": "394", "hashOfConfig": "308"}, {"size": 2201, "mtime": 1744531393951, "results": "395", "hashOfConfig": "308"}, {"size": 14316, "mtime": 1749895693266, "results": "396", "hashOfConfig": "308"}, {"size": 1990, "mtime": 1744531393948, "results": "397", "hashOfConfig": "308"}, {"size": 841, "mtime": 1748984865154, "results": "398", "hashOfConfig": "308"}, {"size": 5565, "mtime": 1745690690469, "results": "399", "hashOfConfig": "308"}, {"size": 2295, "mtime": 1747223318353, "results": "400", "hashOfConfig": "308"}, {"size": 3146, "mtime": 1744531393940, "results": "401", "hashOfConfig": "308"}, {"size": 4074, "mtime": 1747223318326, "results": "402", "hashOfConfig": "308"}, {"size": 2787, "mtime": 1750173366905, "results": "403", "hashOfConfig": "308"}, {"size": 10634, "mtime": 1745483150534, "results": "404", "hashOfConfig": "405"}, {"size": 3707, "mtime": 1749722678323, "results": "406", "hashOfConfig": "308"}, {"size": 6034, "mtime": 1748364026312, "results": "407", "hashOfConfig": "308"}, {"size": 4147, "mtime": 1749731674278, "results": "408", "hashOfConfig": "308"}, {"size": 822, "mtime": 1751276455614, "results": "409", "hashOfConfig": "308"}, {"size": 4040, "mtime": 1750767145503, "results": "410", "hashOfConfig": "308"}, {"size": 297, "mtime": 1744531393989, "results": "411", "hashOfConfig": "308"}, {"size": 313, "mtime": 1744531393940, "results": "412", "hashOfConfig": "308"}, {"size": 4755, "mtime": 1751305471358, "results": "413", "hashOfConfig": "308"}, {"size": 1652, "mtime": 1748250288663, "results": "414", "hashOfConfig": "308"}, {"size": 993, "mtime": 1747223318349, "results": "415", "hashOfConfig": "308"}, {"size": 475, "mtime": 1748984865156, "results": "416", "hashOfConfig": "308"}, {"size": 902, "mtime": 1747223318353, "results": "417", "hashOfConfig": "308"}, {"size": 3053, "mtime": 1744531393946, "results": "418", "hashOfConfig": "308"}, {"size": 49554, "mtime": 1750767145500, "results": "419", "hashOfConfig": "308"}, {"size": 3402, "mtime": 1748781512174, "results": "420", "hashOfConfig": "308"}, {"size": 4872, "mtime": 1747223318349, "results": "421", "hashOfConfig": "308"}, {"size": 1641, "mtime": 1751827507517, "results": "422", "hashOfConfig": "308"}, {"size": 2297, "mtime": 1744531393945, "results": "423", "hashOfConfig": "308"}, {"size": 2193, "mtime": 1747223318349, "results": "424", "hashOfConfig": "308"}, {"size": 1359, "mtime": 1747223318349, "results": "425", "hashOfConfig": "308"}, {"size": 826, "mtime": 1748398318309, "results": "426", "hashOfConfig": "308"}, {"size": 1769, "mtime": 1748709335429, "results": "427", "hashOfConfig": "308"}, {"size": 1050, "mtime": 1751740167834, "results": "428", "hashOfConfig": "308"}, {"size": 4921, "mtime": 1747223318325, "results": "429", "hashOfConfig": "308"}, {"size": 11407, "mtime": 1751822007149, "results": "430", "hashOfConfig": "308"}, {"size": 411, "mtime": 1744531393940, "results": "431", "hashOfConfig": "308"}, {"size": 2290, "mtime": 1744531393963, "results": "432", "hashOfConfig": "308"}, {"size": 1219, "mtime": 1747467640276, "results": "433", "hashOfConfig": "308"}, {"size": 2003, "mtime": 1744531393970, "results": "434", "hashOfConfig": "308"}, {"size": 2166, "mtime": 1744531393969, "results": "435", "hashOfConfig": "308"}, {"size": 18935, "mtime": 1751374977843, "results": "436", "hashOfConfig": "308"}, {"size": 6813, "mtime": 1747223318342, "results": "437", "hashOfConfig": "308"}, {"size": 3094, "mtime": 1744531393970, "results": "438", "hashOfConfig": "308"}, {"size": 6364, "mtime": 1750767145498, "results": "439", "hashOfConfig": "308"}, {"size": 503, "mtime": 1744531393949, "results": "440", "hashOfConfig": "308"}, {"size": 394, "mtime": 1751811266307, "results": "441", "hashOfConfig": "308"}, {"size": 7876, "mtime": 1747223318342, "results": "442", "hashOfConfig": "308"}, {"size": 4922, "mtime": 1748329867180, "results": "443", "hashOfConfig": "308"}, {"size": 19965, "mtime": 1750900200879, "results": "444", "hashOfConfig": "308"}, {"size": 20555, "mtime": 1748250288625, "results": "445", "hashOfConfig": "308"}, {"size": 1337, "mtime": 1744531393967, "results": "446", "hashOfConfig": "405"}, {"size": 5412, "mtime": 1747223318349, "results": "447", "hashOfConfig": "308"}, {"size": 2938, "mtime": 1747223318353, "results": "448", "hashOfConfig": "308"}, {"size": 3182, "mtime": 1747223318353, "results": "449", "hashOfConfig": "308"}, {"size": 2928, "mtime": 1747223318349, "results": "450", "hashOfConfig": "308"}, {"size": 1885, "mtime": 1747354661883, "results": "451", "hashOfConfig": "308"}, {"size": 1345, "mtime": 1749697625937, "results": "452", "hashOfConfig": "308"}, {"size": 4099, "mtime": 1749731407409, "results": "453", "hashOfConfig": "308"}, {"size": 1576, "mtime": 1751811065450, "results": "454", "hashOfConfig": "308"}, {"size": 6380, "mtime": 1747361017417, "results": "455", "hashOfConfig": "308"}, {"size": 2600, "mtime": 1748876218633, "results": "456", "hashOfConfig": "308"}, {"size": 11541, "mtime": 1750900175948, "results": "457", "hashOfConfig": "308"}, {"size": 3297, "mtime": 1744531393957, "results": "458", "hashOfConfig": "308"}, {"size": 31011, "mtime": 1750902784209, "results": "459", "hashOfConfig": "308"}, {"size": 14565, "mtime": 1750819864802, "results": "460", "hashOfConfig": "308"}, {"size": 1205, "mtime": 1748984865161, "results": "461", "hashOfConfig": "308"}, {"size": 12148, "mtime": 1748250288636, "results": "462", "hashOfConfig": "308"}, {"size": 7688, "mtime": 1747223318312, "results": "463", "hashOfConfig": "308"}, {"size": 8344, "mtime": 1745507778499, "results": "464", "hashOfConfig": "308"}, {"size": 8025, "mtime": 1745507836095, "results": "465", "hashOfConfig": "308"}, {"size": 6988, "mtime": 1747223318344, "results": "466", "hashOfConfig": "308"}, {"size": 7719, "mtime": 1745499803667, "results": "467", "hashOfConfig": "308"}, {"size": 8378, "mtime": 1747223318344, "results": "468", "hashOfConfig": "308"}, {"size": 7254, "mtime": 1747223318344, "results": "469", "hashOfConfig": "308"}, {"size": 1721, "mtime": 1749553291593, "results": "470", "hashOfConfig": "308"}, {"size": 11464, "mtime": 1745500164258, "results": "471", "hashOfConfig": "308"}, {"size": 4650, "mtime": 1745508333678, "results": "472", "hashOfConfig": "308"}, {"size": 13822, "mtime": 1748250288625, "results": "473", "hashOfConfig": "308"}, {"size": 8599, "mtime": 1747223318326, "results": "474", "hashOfConfig": "308"}, {"size": 9774, "mtime": 1747223318326, "results": "475", "hashOfConfig": "308"}, {"size": 7914, "mtime": 1747223318326, "results": "476", "hashOfConfig": "308"}, {"size": 10728, "mtime": 1749548057374, "results": "477", "hashOfConfig": "308"}, {"size": 18582, "mtime": 1749747601919, "results": "478", "hashOfConfig": "308"}, {"size": 8954, "mtime": 1749810779685, "results": "479", "hashOfConfig": "308"}, {"size": 3429, "mtime": 1745682027607, "results": "480", "hashOfConfig": "308"}, {"size": 2298, "mtime": 1749802834779, "results": "481", "hashOfConfig": "308"}, {"size": 823, "mtime": 1748779533260, "results": "482", "hashOfConfig": "308"}, {"size": 1380, "mtime": 1745682047729, "results": "483", "hashOfConfig": "308"}, {"size": 1145, "mtime": 1747902858133, "results": "484", "hashOfConfig": "308"}, {"size": 1118, "mtime": 1745682069045, "results": "485", "hashOfConfig": "308"}, {"size": 978, "mtime": 1747902858130, "results": "486", "hashOfConfig": "308"}, {"size": 1781, "mtime": 1745682059432, "results": "487", "hashOfConfig": "405"}, {"size": 10563, "mtime": 1748585335221, "results": "488", "hashOfConfig": "308"}, {"size": 3574, "mtime": 1746378664904, "results": "489", "hashOfConfig": "308"}, {"size": 4389, "mtime": 1749980511142, "results": "490", "hashOfConfig": "308"}, {"size": 4914, "mtime": 1750326838086, "results": "491", "hashOfConfig": "308"}, {"size": 6418, "mtime": 1750326903479, "results": "492", "hashOfConfig": "308"}, {"size": 1020, "mtime": 1745682406063, "results": "493", "hashOfConfig": "308"}, {"size": 2723, "mtime": 1749810234131, "results": "494", "hashOfConfig": "308"}, {"size": 6116, "mtime": 1746378664905, "results": "495", "hashOfConfig": "308"}, {"size": 1565, "mtime": 1747223318344, "results": "496", "hashOfConfig": "308"}, {"size": 1624, "mtime": 1745689590880, "results": "497", "hashOfConfig": "308"}, {"size": 45152, "mtime": 1750295514405, "results": "498", "hashOfConfig": "308"}, {"size": 70430, "mtime": 1748984865165, "results": "499", "hashOfConfig": "308"}, {"size": 9519, "mtime": 1747354195259, "results": "500", "hashOfConfig": "308"}, {"size": 7408, "mtime": 1749894301204, "results": "501", "hashOfConfig": "308"}, {"size": 4905, "mtime": 1747354192845, "results": "502", "hashOfConfig": "308"}, {"size": 60115, "mtime": 1750947214848, "results": "503", "hashOfConfig": "308"}, {"size": 26487, "mtime": 1748278828957, "results": "504", "hashOfConfig": "308"}, {"size": 24147, "mtime": 1747354834297, "results": "505", "hashOfConfig": "308"}, {"size": 23053, "mtime": 1749730024729, "results": "506", "hashOfConfig": "308"}, {"size": 37412, "mtime": 1750487313341, "results": "507", "hashOfConfig": "308"}, {"size": 17071, "mtime": 1751543017144, "results": "508", "hashOfConfig": "308"}, {"size": 8060, "mtime": 1747223318310, "results": "509", "hashOfConfig": "308"}, {"size": 16767, "mtime": 1748876218633, "results": "510", "hashOfConfig": "308"}, {"size": 3160, "mtime": 1745731138150, "results": "511", "hashOfConfig": "308"}, {"size": 7136, "mtime": 1747223318325, "results": "512", "hashOfConfig": "308"}, {"size": 20185, "mtime": 1747223318312, "results": "513", "hashOfConfig": "308"}, {"size": 2129, "mtime": 1746378664905, "results": "514", "hashOfConfig": "308"}, {"size": 955, "mtime": 1746378664898, "results": "515", "hashOfConfig": "308"}, {"size": 1184, "mtime": 1746378664905, "results": "516", "hashOfConfig": "308"}, {"size": 13378, "mtime": 1748984865159, "results": "517", "hashOfConfig": "308"}, {"size": 1099, "mtime": 1748326442261, "results": "518", "hashOfConfig": "308"}, {"size": 15897, "mtime": 1749894770618, "results": "519", "hashOfConfig": "308"}, {"size": 11963, "mtime": 1750323324025, "results": "520", "hashOfConfig": "308"}, {"size": 12224, "mtime": 1748220515100, "results": "521", "hashOfConfig": "308"}, {"size": 10534, "mtime": 1748220627343, "results": "522", "hashOfConfig": "308"}, {"size": 4031, "mtime": 1747278525096, "results": "523", "hashOfConfig": "308"}, {"size": 2036, "mtime": 1747283717643, "results": "524", "hashOfConfig": "308"}, {"size": 72412, "mtime": 1750397113436, "results": "525", "hashOfConfig": "308"}, {"size": 3589, "mtime": 1747355350828, "results": "526", "hashOfConfig": "308"}, {"size": 7509, "mtime": 1747353152130, "results": "527", "hashOfConfig": "308"}, {"size": 6153, "mtime": 1747354063004, "results": "528", "hashOfConfig": "308"}, {"size": 16791, "mtime": 1748473697636, "results": "529", "hashOfConfig": "308"}, {"size": 12245, "mtime": 1750386603287, "results": "530", "hashOfConfig": "308"}, {"size": 13612, "mtime": 1750175198005, "results": "531", "hashOfConfig": "308"}, {"size": 73138, "mtime": 1750487274356, "results": "532", "hashOfConfig": "308"}, {"size": 5589, "mtime": 1750239106405, "results": "533", "hashOfConfig": "308"}, {"size": 9836, "mtime": 1750175833314, "results": "534", "hashOfConfig": "308"}, {"size": 7964, "mtime": 1750400179899, "results": "535", "hashOfConfig": "308"}, {"size": 4152, "mtime": 1749202010110, "results": "536", "hashOfConfig": "308"}, {"size": 4762, "mtime": 1748513292659, "results": "537", "hashOfConfig": "308"}, {"size": 2443, "mtime": 1747719362467, "results": "538", "hashOfConfig": "308"}, {"size": 16049, "mtime": 1751785268325, "results": "539", "hashOfConfig": "308"}, {"size": 2002, "mtime": 1751785248620, "results": "540", "hashOfConfig": "308"}, {"size": 55792, "mtime": 1751616690844, "results": "541", "hashOfConfig": "308"}, {"size": 16095, "mtime": 1751785298468, "results": "542", "hashOfConfig": "308"}, {"size": 8222, "mtime": 1748250288630, "results": "543", "hashOfConfig": "308"}, {"size": 11210, "mtime": 1748223444732, "results": "544", "hashOfConfig": "308"}, {"size": 41991, "mtime": 1751785335077, "results": "545", "hashOfConfig": "308"}, {"size": 8067, "mtime": 1750764769591, "results": "546", "hashOfConfig": "308"}, {"size": 28083, "mtime": 1748250288657, "results": "547", "hashOfConfig": "308"}, {"size": 29543, "mtime": 1748984865164, "results": "548", "hashOfConfig": "308"}, {"size": 36682, "mtime": 1748250768337, "results": "549", "hashOfConfig": "308"}, {"size": 1999, "mtime": 1750764523294, "results": "550", "hashOfConfig": "308"}, {"size": 9569, "mtime": 1749694485776, "results": "551", "hashOfConfig": "308"}, {"size": 13102, "mtime": 1748250288647, "results": "552", "hashOfConfig": "308"}, {"size": 16077, "mtime": 1748365756504, "results": "553", "hashOfConfig": "308"}, {"size": 3987, "mtime": 1748709335425, "results": "554", "hashOfConfig": "308"}, {"size": 3539, "mtime": 1748800991826, "results": "555", "hashOfConfig": "308"}, {"size": 1712, "mtime": 1748800656400, "results": "556", "hashOfConfig": "308"}, {"size": 1983, "mtime": 1751010202512, "results": "557", "hashOfConfig": "308"}, {"size": 3908, "mtime": 1748801325319, "results": "558", "hashOfConfig": "308"}, {"size": 839, "mtime": 1748801505979, "results": "559", "hashOfConfig": "308"}, {"size": 365, "mtime": 1748984865153, "results": "560", "hashOfConfig": "308"}, {"size": 1800, "mtime": 1751828247066, "results": "561", "hashOfConfig": "308"}, {"size": 8860, "mtime": 1749202010110, "results": "562", "hashOfConfig": "308"}, {"size": 5114, "mtime": 1750951954100, "results": "563", "hashOfConfig": "308"}, {"size": 8088, "mtime": 1751742516897, "results": "564", "hashOfConfig": "308"}, {"size": 4151, "mtime": 1751716037997, "results": "565", "hashOfConfig": "308"}, {"size": 958, "mtime": 1750951934365, "results": "566", "hashOfConfig": "308"}, {"size": 2836, "mtime": 1749722547776, "results": "567", "hashOfConfig": "308"}, {"size": 913, "mtime": 1751742948436, "results": "568", "hashOfConfig": "308"}, {"size": 388, "mtime": 1749720429395, "results": "569", "hashOfConfig": "308"}, {"size": 579, "mtime": 1749731593347, "results": "570", "hashOfConfig": "308"}, {"size": 1948, "mtime": 1751543127045, "results": "571", "hashOfConfig": "308"}, {"size": 10543, "mtime": 1750295311557, "results": "572", "hashOfConfig": "308"}, {"size": 1664, "mtime": 1749808930419, "results": "573", "hashOfConfig": "308"}, {"size": 2401, "mtime": 1750389237348, "results": "574", "hashOfConfig": "308"}, {"size": 7582, "mtime": 1750400196241, "results": "575", "hashOfConfig": "308"}, {"size": 3921, "mtime": 1750322224906, "results": "576", "hashOfConfig": "308"}, {"size": 2025, "mtime": 1750487274354, "results": "577", "hashOfConfig": "308"}, {"size": 3458, "mtime": 1750487274353, "results": "578", "hashOfConfig": "308"}, {"size": 200, "mtime": 1750487274358, "results": "579", "hashOfConfig": "308"}, {"size": 4832, "mtime": 1750574009240, "results": "580", "hashOfConfig": "308"}, {"size": 3066, "mtime": 1750766071078, "results": "581", "hashOfConfig": "308"}, {"size": 4807, "mtime": 1750765702772, "results": "582", "hashOfConfig": "308"}, {"size": 74, "mtime": 1748250288649, "results": "583", "hashOfConfig": "308"}, {"size": 4384, "mtime": 1750962443161, "results": "584", "hashOfConfig": "308"}, {"size": 4263, "mtime": 1750960348603, "results": "585", "hashOfConfig": "308"}, {"size": 4331, "mtime": 1750963155580, "results": "586", "hashOfConfig": "308"}, {"size": 3878, "mtime": 1750940043247, "results": "587", "hashOfConfig": "308"}, {"size": 23770, "mtime": 1750963670443, "results": "588", "hashOfConfig": "308"}, {"size": 17942, "mtime": 1750963374804, "results": "589", "hashOfConfig": "308"}, {"size": 22243, "mtime": 1750961198993, "results": "590", "hashOfConfig": "308"}, {"size": 1252, "mtime": 1750941383903, "results": "591", "hashOfConfig": "308"}, {"size": 846, "mtime": 1750944008903, "results": "592", "hashOfConfig": "308"}, {"size": 218, "mtime": 1751349395110, "results": "593", "hashOfConfig": "308"}, {"size": 2323, "mtime": 1751350697129, "results": "594", "hashOfConfig": "308"}, {"size": 52491, "mtime": 1751375995915, "results": "595", "hashOfConfig": "308"}, {"size": 815, "mtime": 1751617408262, "results": "596", "hashOfConfig": "308"}, {"size": 10987, "mtime": 1751631444481, "results": "597", "hashOfConfig": "308"}, {"size": 8700, "mtime": 1751822765291, "results": "598", "hashOfConfig": "308"}, {"size": 449, "mtime": 1751515976272, "results": "599", "hashOfConfig": "308"}, {"size": 2127, "mtime": 1751825647766, "results": "600", "hashOfConfig": "308"}, {"size": 3401, "mtime": 1751822255115, "results": "601", "hashOfConfig": "308"}, {"size": 719, "mtime": 1751617844711, "results": "602", "hashOfConfig": "308"}, {"size": 14778, "mtime": 1751826579778, "results": "603", "hashOfConfig": "308"}, {"size": 5735, "mtime": 1751826157062, "results": "604", "hashOfConfig": "308"}, {"size": 2448, "mtime": 1751825667085, "results": "605", "hashOfConfig": "308"}, {"size": 759, "mtime": 1751785941157, "results": "606", "hashOfConfig": "308"}, {"size": 4350, "mtime": 1751784560929, "results": "607", "hashOfConfig": "308"}, {"size": 4259, "mtime": 1751742649369, "results": "608", "hashOfConfig": "308"}, {"size": 843, "mtime": 1751717594658, "results": "609", "hashOfConfig": "308"}, {"size": 663, "mtime": 1751742504805, "results": "610", "hashOfConfig": "308"}, {"size": 1349, "mtime": 1751812066040, "results": "611", "hashOfConfig": "308"}, {"size": 3305, "mtime": 1751826380749, "results": "612", "hashOfConfig": "308"}, {"size": 1887, "mtime": 1751826480475, "results": "613", "hashOfConfig": "308"}, {"size": 1196, "mtime": 1751827870407, "results": "614", "hashOfConfig": "308"}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e07wfn", {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1op6h7j", {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1380", "messages": "1381", "suppressedMessages": "1382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1383", "messages": "1384", "suppressedMessages": "1385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1386", "messages": "1387", "suppressedMessages": "1388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1389", "messages": "1390", "suppressedMessages": "1391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1392", "messages": "1393", "suppressedMessages": "1394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1395", "messages": "1396", "suppressedMessages": "1397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1398", "messages": "1399", "suppressedMessages": "1400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1401", "messages": "1402", "suppressedMessages": "1403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1404", "messages": "1405", "suppressedMessages": "1406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1407", "messages": "1408", "suppressedMessages": "1409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1410", "messages": "1411", "suppressedMessages": "1412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1413", "messages": "1414", "suppressedMessages": "1415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1416", "messages": "1417", "suppressedMessages": "1418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1419", "messages": "1420", "suppressedMessages": "1421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1422", "messages": "1423", "suppressedMessages": "1424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1425", "messages": "1426", "suppressedMessages": "1427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1428", "messages": "1429", "suppressedMessages": "1430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1431", "messages": "1432", "suppressedMessages": "1433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1434", "messages": "1435", "suppressedMessages": "1436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1437", "messages": "1438", "suppressedMessages": "1439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1440", "messages": "1441", "suppressedMessages": "1442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1443", "messages": "1444", "suppressedMessages": "1445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1446", "messages": "1447", "suppressedMessages": "1448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1449", "messages": "1450", "suppressedMessages": "1451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1452", "messages": "1453", "suppressedMessages": "1454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1455", "messages": "1456", "suppressedMessages": "1457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1458", "messages": "1459", "suppressedMessages": "1460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1461", "messages": "1462", "suppressedMessages": "1463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1464", "messages": "1465", "suppressedMessages": "1466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1467", "messages": "1468", "suppressedMessages": "1469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1470", "messages": "1471", "suppressedMessages": "1472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1473", "messages": "1474", "suppressedMessages": "1475", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1476", "messages": "1477", "suppressedMessages": "1478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1479", "messages": "1480", "suppressedMessages": "1481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1482", "messages": "1483", "suppressedMessages": "1484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1485", "messages": "1486", "suppressedMessages": "1487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1488", "messages": "1489", "suppressedMessages": "1490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1491", "messages": "1492", "suppressedMessages": "1493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1494", "messages": "1495", "suppressedMessages": "1496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1497", "messages": "1498", "suppressedMessages": "1499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1500", "messages": "1501", "suppressedMessages": "1502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1503", "messages": "1504", "suppressedMessages": "1505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1506", "messages": "1507", "suppressedMessages": "1508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1509", "messages": "1510", "suppressedMessages": "1511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1512", "messages": "1513", "suppressedMessages": "1514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1515", "messages": "1516", "suppressedMessages": "1517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1518", "messages": "1519", "suppressedMessages": "1520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1521", "messages": "1522", "suppressedMessages": "1523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1524", "messages": "1525", "suppressedMessages": "1526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1527", "messages": "1528", "suppressedMessages": "1529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1530", "messages": "1531", "suppressedMessages": "1532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js", ["1533"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx", ["1534", "1535", "1536", "1537", "1538"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx", ["1539", "1540"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx", ["1541"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx", ["1542"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx", ["1543", "1544", "1545", "1546", "1547"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx", ["1548", "1549"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1550", "1551"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1552"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1553", "1554", "1555", "1556", "1557", "1558", "1559"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1560", "1561", "1562", "1563", "1564"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", ["1565"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", ["1579", "1580", "1581", "1582"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1583", "1584"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1593"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js", ["1594"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js", ["1595"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js", ["1596"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx", ["1597"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx", ["1598"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1599"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx", ["1600", "1601", "1602", "1603", "1604", "1605", "1606"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx", ["1607"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx", ["1608", "1609", "1610"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx", ["1611"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx", ["1612"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx", ["1613", "1614", "1615"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx", ["1616", "1617", "1618"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx", ["1619", "1620"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx", ["1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx", ["1637", "1638"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx", ["1639", "1640", "1641", "1642", "1643"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx", ["1644", "1645", "1646", "1647", "1648", "1649", "1650"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx", ["1651", "1652"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx", ["1653"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx", ["1654", "1655", "1656", "1657", "1658"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx", ["1659"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx", ["1660", "1661", "1662", "1663", "1664", "1665"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx", ["1666", "1667"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx", ["1668"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx", ["1669", "1670", "1671", "1672", "1673", "1674", "1675"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx", ["1676", "1677", "1678", "1679", "1680"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1681", "1682", "1683"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx", ["1684", "1685"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx", ["1686"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx", ["1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx", ["1695"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx", ["1696"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js", ["1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx", ["1711"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx", ["1712"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx", ["1713"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx", ["1714"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx", ["1715"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx", ["1716", "1717", "1718"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx", ["1719"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1720", "1721", "1722", "1723", "1724", "1725"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx", ["1726"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx", ["1727", "1728", "1729", "1730"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx", ["1731", "1732"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx", ["1733", "1734", "1735", "1736"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx", ["1737", "1738"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx", ["1739", "1740", "1741", "1742"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1743"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx", ["1744", "1745"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1746"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx", ["1747", "1748"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx", ["1749"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx", ["1750"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx", ["1751", "1752", "1753", "1754", "1755"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx", ["1756", "1757"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx", ["1758", "1759", "1760"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx", ["1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx", ["1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx", ["1789", "1790", "1791", "1792"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx", ["1793"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx", ["1794", "1795", "1796", "1797"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx", ["1798", "1799", "1800", "1801", "1802", "1803", "1804"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx", ["1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx", ["1815", "1816", "1817", "1818"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["1819", "1820"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx", ["1821", "1822", "1823", "1824", "1825", "1826", "1827"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx", ["1828", "1829", "1830", "1831", "1832", "1833", "1834"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx", ["1835", "1836"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js", ["1837"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx", ["1838", "1839", "1840"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx", ["1841", "1842", "1843", "1844"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js", ["1845"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx", ["1846", "1847", "1848"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx", ["1849", "1850", "1851", "1852", "1853", "1854", "1855"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js", ["1856"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["1857", "1858", "1859", "1860"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["1893", "1894", "1895", "1896", "1897", "1898"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx", ["1899"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx", ["1900"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx", ["1901"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js", ["1902", "1903"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx", ["1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", ["1912"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["1913", "1914", "1915", "1916", "1917"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["1918", "1919", "1920"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["1921", "1922"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx", ["1923", "1924"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["1925", "1926", "1927"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx", ["1928"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx", ["1929", "1930", "1931", "1932"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx", ["1933"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx", ["1934"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx", ["1935", "1936", "1937", "1938", "1939", "1940", "1941", "1942"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx", ["1943"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx", ["1944", "1945", "1946", "1947", "1948", "1949", "1950", "1951", "1952", "1953", "1954", "1955"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx", ["1956"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx", ["1957", "1958", "1959"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal2.jsx", ["1960"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx", ["1961", "1962"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx", ["1963", "1964", "1965"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx", ["1966", "1967"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx", ["1968"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx", ["1969", "1970", "1971", "1972", "1973", "1974"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx", ["1975", "1976", "1977", "1978", "1979"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx", ["1980", "1981", "1982"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js", [], [], {"ruleId": "1983", "severity": 1, "message": "1984", "line": 51, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 51, "endColumn": 27}, {"ruleId": "1983", "severity": 1, "message": "1987", "line": 4, "column": 23, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 27}, {"ruleId": "1983", "severity": 1, "message": "1988", "line": 8, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 25}, {"ruleId": "1983", "severity": 1, "message": "1989", "line": 11, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 11, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "1991", "line": 42, "column": 8, "nodeType": "1992", "endLine": 42, "endColumn": 24, "suggestions": "1993"}, {"ruleId": "1983", "severity": 1, "message": "1994", "line": 49, "column": 15, "nodeType": "1985", "messageId": "1986", "endLine": 49, "endColumn": 27}, {"ruleId": "1983", "severity": 1, "message": "1995", "line": 10, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 10, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "1996", "line": 18, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 36}, {"ruleId": "1990", "severity": 1, "message": "1997", "line": 26, "column": 8, "nodeType": "1992", "endLine": 26, "endColumn": 19, "suggestions": "1998"}, {"ruleId": "1999", "severity": 1, "message": "2000", "line": 393, "column": 45, "nodeType": "2001", "endLine": 398, "endColumn": 47}, {"ruleId": "1983", "severity": 1, "message": "2002", "line": 6, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 11}, {"ruleId": "1983", "severity": 1, "message": "2003", "line": 6, "column": 27, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 31}, {"ruleId": "1983", "severity": 1, "message": "2004", "line": 7, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 25}, {"ruleId": "1983", "severity": 1, "message": "2005", "line": 8, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 32}, {"ruleId": "1983", "severity": 1, "message": "2006", "line": 88, "column": 23, "nodeType": "1985", "messageId": "1986", "endLine": 88, "endColumn": 31}, {"ruleId": "1983", "severity": 1, "message": "2007", "line": 14, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 14, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "1996", "line": 15, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 36}, {"ruleId": "1983", "severity": 1, "message": "2008", "line": 2, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2009", "line": 3, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2010", "line": 5, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 39}, {"ruleId": "1983", "severity": 1, "message": "2011", "line": 15, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2012", "line": 16, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 16, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2013", "line": 24, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 24, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2014", "line": 30, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 30, "endColumn": 25}, {"ruleId": "1983", "severity": 1, "message": "2015", "line": 34, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 34, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 73, "column": 31, "nodeType": "1985", "endLine": 73, "endColumn": 42}, {"ruleId": "1990", "severity": 1, "message": "2017", "line": 269, "column": 8, "nodeType": "1992", "endLine": 269, "endColumn": 18, "suggestions": "2018"}, {"ruleId": "1983", "severity": 1, "message": "2011", "line": 6, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2012", "line": 9, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 9, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2013", "line": 18, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2014", "line": 20, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 20, "endColumn": 25}, {"ruleId": "1983", "severity": 1, "message": "2019", "line": 26, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 26, "endColumn": 26}, {"ruleId": "1983", "severity": 1, "message": "2020", "line": 1, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2020", "line": 1, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2021", "line": 8, "column": 37, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 48}, {"ruleId": "1983", "severity": 1, "message": "2022", "line": 11, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 11, "endColumn": 30}, {"ruleId": "1983", "severity": 1, "message": "2023", "line": 22, "column": 18, "nodeType": "1985", "messageId": "1986", "endLine": 22, "endColumn": 25}, {"ruleId": "1983", "severity": 1, "message": "2024", "line": 23, "column": 21, "nodeType": "1985", "messageId": "1986", "endLine": 23, "endColumn": 31}, {"ruleId": "1983", "severity": 1, "message": "2025", "line": 24, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 24, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2026", "line": 25, "column": 25, "nodeType": "1985", "messageId": "1986", "endLine": 25, "endColumn": 39}, {"ruleId": "1983", "severity": 1, "message": "2027", "line": 26, "column": 30, "nodeType": "1985", "messageId": "1986", "endLine": 26, "endColumn": 49}, {"ruleId": "1983", "severity": 1, "message": "2028", "line": 27, "column": 21, "nodeType": "1985", "messageId": "1986", "endLine": 27, "endColumn": 26}, {"ruleId": "1983", "severity": 1, "message": "2029", "line": 27, "column": 41, "nodeType": "1985", "messageId": "1986", "endLine": 27, "endColumn": 51}, {"ruleId": "1983", "severity": 1, "message": "2030", "line": 31, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 31, "endColumn": 30}, {"ruleId": "1983", "severity": 1, "message": "2031", "line": 34, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 34, "endColumn": 35}, {"ruleId": "1983", "severity": 1, "message": "2032", "line": 38, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 38, "endColumn": 33}, {"ruleId": "1983", "severity": 1, "message": "2020", "line": 1, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2030", "line": 17, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 17, "endColumn": 30}, {"ruleId": "1983", "severity": 1, "message": "2031", "line": 20, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 20, "endColumn": 35}, {"ruleId": "1983", "severity": 1, "message": "2033", "line": 23, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 23, "endColumn": 32}, {"ruleId": "1983", "severity": 1, "message": "2010", "line": 6, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 39}, {"ruleId": "1983", "severity": 1, "message": "1996", "line": 15, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 36}, {"ruleId": "1983", "severity": 1, "message": "2020", "line": 1, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2008", "line": 2, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2034", "line": 6, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2035", "line": 7, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2036", "line": 8, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2030", "line": 25, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 25, "endColumn": 30}, {"ruleId": "1983", "severity": 1, "message": "2032", "line": 28, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 28, "endColumn": 33}, {"ruleId": "1983", "severity": 1, "message": "2033", "line": 31, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 31, "endColumn": 32}, {"ruleId": "1983", "severity": 1, "message": "2020", "line": 1, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2037", "line": 37, "column": 19, "nodeType": "1985", "messageId": "1986", "endLine": 37, "endColumn": 27}, {"ruleId": "1983", "severity": 1, "message": "2038", "line": 5, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 17}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 140, "column": 80, "nodeType": "2041", "messageId": "2042", "endLine": 140, "endColumn": 82}, {"ruleId": "1983", "severity": 1, "message": "2043", "line": 3, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2044", "line": 19, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 19, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2045", "line": 18, "column": 8, "nodeType": "1992", "endLine": 18, "endColumn": 10, "suggestions": "2046"}, {"ruleId": "1983", "severity": 1, "message": "2047", "line": 1, "column": 20, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2048", "line": 4, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2049", "line": 11, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 11, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2050", "line": 12, "column": 36, "nodeType": "1985", "messageId": "1986", "endLine": 12, "endColumn": 45}, {"ruleId": "1983", "severity": 1, "message": "2051", "line": 13, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 13, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2052", "line": 13, "column": 16, "nodeType": "1985", "messageId": "1986", "endLine": 13, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2053", "line": 28, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 28, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2054", "line": 5, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2055", "line": 11, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 11, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2056", "line": 18, "column": 36, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 46}, {"ruleId": "1990", "severity": 1, "message": "2057", "line": 43, "column": 8, "nodeType": "1992", "endLine": 43, "endColumn": 26, "suggestions": "2058"}, {"ruleId": "1983", "severity": 1, "message": "2047", "line": 2, "column": 20, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2059", "line": 7, "column": 47, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 68}, {"ruleId": "1983", "severity": 1, "message": "2060", "line": 17, "column": 7, "nodeType": "1985", "messageId": "1986", "endLine": 17, "endColumn": 16}, {"ruleId": "1983", "severity": 1, "message": "2061", "line": 29, "column": 7, "nodeType": "1985", "messageId": "1986", "endLine": 29, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2062", "line": 36, "column": 7, "nodeType": "1985", "messageId": "1986", "endLine": 36, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2055", "line": 7, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2056", "line": 15, "column": 36, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 46}, {"ruleId": "1983", "severity": 1, "message": "2063", "line": 19, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 19, "endColumn": 37}, {"ruleId": "1983", "severity": 1, "message": "2056", "line": 15, "column": 36, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 46}, {"ruleId": "1990", "severity": 1, "message": "2064", "line": 34, "column": 8, "nodeType": "1992", "endLine": 34, "endColumn": 53, "suggestions": "2065"}, {"ruleId": "1983", "severity": 1, "message": "2066", "line": 12, "column": 55, "nodeType": "1985", "messageId": "1986", "endLine": 12, "endColumn": 61}, {"ruleId": "1983", "severity": 1, "message": "2067", "line": 12, "column": 77, "nodeType": "1985", "messageId": "1986", "endLine": 12, "endColumn": 82}, {"ruleId": "1983", "severity": 1, "message": "2068", "line": 12, "column": 84, "nodeType": "1985", "messageId": "1986", "endLine": 12, "endColumn": 94}, {"ruleId": "1983", "severity": 1, "message": "2013", "line": 18, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2015", "line": 20, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 20, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 39, "column": 31, "nodeType": "1985", "endLine": 39, "endColumn": 42}, {"ruleId": "1983", "severity": 1, "message": "2069", "line": 96, "column": 17, "nodeType": "1985", "messageId": "1986", "endLine": 96, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 412, "column": 68, "nodeType": "2041", "messageId": "2042", "endLine": 412, "endColumn": 70}, {"ruleId": "1983", "severity": 1, "message": "2009", "line": 2, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2010", "line": 7, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 39}, {"ruleId": "1983", "severity": 1, "message": "2070", "line": 24, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 24, "endColumn": 26}, {"ruleId": "1983", "severity": 1, "message": "2071", "line": 25, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 25, "endColumn": 30}, {"ruleId": "1983", "severity": 1, "message": "2072", "line": 47, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 47, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2073", "line": 68, "column": 8, "nodeType": "1992", "endLine": 68, "endColumn": 30, "suggestions": "2074"}, {"ruleId": "1983", "severity": 1, "message": "2075", "line": 78, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 78, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2076", "line": 86, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 86, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2077", "line": 8, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 28}, {"ruleId": "1990", "severity": 1, "message": "2078", "line": 45, "column": 8, "nodeType": "1992", "endLine": 45, "endColumn": 32, "suggestions": "2079"}, {"ruleId": "1983", "severity": 1, "message": "2080", "line": 6, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2081", "line": 7, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2011", "line": 13, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 13, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2012", "line": 14, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 14, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2014", "line": 23, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 23, "endColumn": 25}, {"ruleId": "1983", "severity": 1, "message": "2082", "line": 5, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2081", "line": 6, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2077", "line": 7, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 28}, {"ruleId": "1990", "severity": 1, "message": "2083", "line": 51, "column": 8, "nodeType": "1992", "endLine": 51, "endColumn": 28, "suggestions": "2084"}, {"ruleId": "1983", "severity": 1, "message": "2032", "line": 65, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 65, "endColumn": 33}, {"ruleId": "1983", "severity": 1, "message": "2031", "line": 69, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 69, "endColumn": 35}, {"ruleId": "1983", "severity": 1, "message": "2033", "line": 73, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 73, "endColumn": 32}, {"ruleId": "1983", "severity": 1, "message": "2085", "line": 10, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 10, "endColumn": 17}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 290, "column": 59, "nodeType": "2088", "messageId": "2089", "endLine": 290, "endColumn": 60, "suggestions": "2090"}, {"ruleId": "1983", "severity": 1, "message": "2056", "line": 11, "column": 41, "nodeType": "1985", "messageId": "1986", "endLine": 11, "endColumn": 51}, {"ruleId": "1983", "severity": 1, "message": "2091", "line": 1, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 28}, {"ruleId": "1983", "severity": 1, "message": "2092", "line": 2, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2047", "line": 3, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2093", "line": 7, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2094", "line": 12, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 12, "endColumn": 31}, {"ruleId": "1983", "severity": 1, "message": "2095", "line": 1, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 13}, {"ruleId": "1983", "severity": 1, "message": "2096", "line": 2, "column": 37, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 45}, {"ruleId": "1983", "severity": 1, "message": "2097", "line": 2, "column": 47, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 60}, {"ruleId": "1983", "severity": 1, "message": "2098", "line": 2, "column": 62, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 67}, {"ruleId": "1983", "severity": 1, "message": "2099", "line": 2, "column": 69, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 77}, {"ruleId": "1983", "severity": 1, "message": "2100", "line": 3, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 26}, {"ruleId": "1990", "severity": 1, "message": "2101", "line": 36, "column": 8, "nodeType": "1992", "endLine": 36, "endColumn": 43, "suggestions": "2102"}, {"ruleId": "1983", "severity": 1, "message": "2095", "line": 1, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 13}, {"ruleId": "1983", "severity": 1, "message": "1995", "line": 7, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2103", "line": 25, "column": 8, "nodeType": "1992", "endLine": 25, "endColumn": 20, "suggestions": "2104"}, {"ruleId": "1983", "severity": 1, "message": "2105", "line": 1, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2106", "line": 32, "column": 83, "nodeType": "1985", "messageId": "1986", "endLine": 32, "endColumn": 91}, {"ruleId": "1983", "severity": 1, "message": "2107", "line": 121, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 121, "endColumn": 23}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 159, "column": 22, "nodeType": "2110", "messageId": "2111", "endLine": 159, "endColumn": 24}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 159, "column": 91, "nodeType": "2110", "messageId": "2111", "endLine": 159, "endColumn": 93}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 304, "column": 40, "nodeType": "2110", "messageId": "2111", "endLine": 304, "endColumn": 42}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 304, "column": 109, "nodeType": "2110", "messageId": "2111", "endLine": 304, "endColumn": 111}, {"ruleId": "1983", "severity": 1, "message": "2112", "line": 14, "column": 38, "nodeType": "1985", "messageId": "1986", "endLine": 14, "endColumn": 47}, {"ruleId": "1983", "severity": 1, "message": "2113", "line": 14, "column": 49, "nodeType": "1985", "messageId": "1986", "endLine": 14, "endColumn": 56}, {"ruleId": "1983", "severity": 1, "message": "2114", "line": 14, "column": 58, "nodeType": "1985", "messageId": "1986", "endLine": 14, "endColumn": 70}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 34, "column": 40, "nodeType": "2110", "messageId": "2111", "endLine": 34, "endColumn": 42}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 34, "column": 109, "nodeType": "2110", "messageId": "2111", "endLine": 34, "endColumn": 111}, {"ruleId": "1983", "severity": 1, "message": "2028", "line": 15, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2115", "line": 15, "column": 44, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 52}, {"ruleId": "1990", "severity": 1, "message": "2116", "line": 42, "column": 8, "nodeType": "1992", "endLine": 42, "endColumn": 40, "suggestions": "2117"}, {"ruleId": "1983", "severity": 1, "message": "1995", "line": 1, "column": 25, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 33}, {"ruleId": "1983", "severity": 1, "message": "2118", "line": 4, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2119", "line": 4, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "1989", "line": 1, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "1987", "line": 2, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2120", "line": 4, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2121", "line": 4, "column": 26, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 44}, {"ruleId": "1983", "severity": 1, "message": "2122", "line": 47, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 47, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2123", "line": 48, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 48, "endColumn": 26}, {"ruleId": "1983", "severity": 1, "message": "2124", "line": 50, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 50, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2125", "line": 59, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 59, "endColumn": 16}, {"ruleId": "1990", "severity": 1, "message": "2126", "line": 29, "column": 8, "nodeType": "1992", "endLine": 29, "endColumn": 10, "suggestions": "2127"}, {"ruleId": "1999", "severity": 1, "message": "2000", "line": 44, "column": 29, "nodeType": "2001", "endLine": 44, "endColumn": 98}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 238, "column": 30, "nodeType": "2088", "messageId": "2089", "endLine": 238, "endColumn": 31, "suggestions": "2128"}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 251, "column": 61, "nodeType": "2088", "messageId": "2089", "endLine": 251, "endColumn": 62, "suggestions": "2129"}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 257, "column": 29, "nodeType": "2088", "messageId": "2089", "endLine": 257, "endColumn": 30, "suggestions": "2130"}, {"ruleId": "2086", "severity": 1, "message": "2131", "line": 257, "column": 31, "nodeType": "2088", "messageId": "2089", "endLine": 257, "endColumn": 32, "suggestions": "2132"}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 258, "column": 51, "nodeType": "2088", "messageId": "2089", "endLine": 258, "endColumn": 52, "suggestions": "2133"}, {"ruleId": "2086", "severity": 1, "message": "2131", "line": 258, "column": 53, "nodeType": "2088", "messageId": "2089", "endLine": 258, "endColumn": 54, "suggestions": "2134"}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 346, "column": 30, "nodeType": "2088", "messageId": "2089", "endLine": 346, "endColumn": 31, "suggestions": "2135"}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 368, "column": 61, "nodeType": "2088", "messageId": "2089", "endLine": 368, "endColumn": 62, "suggestions": "2136"}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 374, "column": 29, "nodeType": "2088", "messageId": "2089", "endLine": 374, "endColumn": 30, "suggestions": "2137"}, {"ruleId": "2086", "severity": 1, "message": "2131", "line": 374, "column": 31, "nodeType": "2088", "messageId": "2089", "endLine": 374, "endColumn": 32, "suggestions": "2138"}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 375, "column": 51, "nodeType": "2088", "messageId": "2089", "endLine": 375, "endColumn": 52, "suggestions": "2139"}, {"ruleId": "2086", "severity": 1, "message": "2131", "line": 375, "column": 53, "nodeType": "2088", "messageId": "2089", "endLine": 375, "endColumn": 54, "suggestions": "2140"}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 456, "column": 30, "nodeType": "2088", "messageId": "2089", "endLine": 456, "endColumn": 31, "suggestions": "2141"}, {"ruleId": "2086", "severity": 1, "message": "2087", "line": 475, "column": 61, "nodeType": "2088", "messageId": "2089", "endLine": 475, "endColumn": 62, "suggestions": "2142"}, {"ruleId": "1990", "severity": 1, "message": "2143", "line": 80, "column": 8, "nodeType": "1992", "endLine": 80, "endColumn": 10, "suggestions": "2144"}, {"ruleId": "1999", "severity": 1, "message": "2000", "line": 56, "column": 37, "nodeType": "2001", "endLine": 56, "endColumn": 107}, {"ruleId": "1983", "severity": 1, "message": "2145", "line": 3, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "2146", "line": 73, "column": 8, "nodeType": "1992", "endLine": 73, "endColumn": 25, "suggestions": "2147"}, {"ruleId": "1983", "severity": 1, "message": "2047", "line": 3, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2148", "line": 5, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2149", "line": 14, "column": 28, "nodeType": "1985", "messageId": "1986", "endLine": 14, "endColumn": 45}, {"ruleId": "1983", "severity": 1, "message": "2150", "line": 34, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 34, "endColumn": 20}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 116, "column": 56, "nodeType": "2041", "messageId": "2042", "endLine": 116, "endColumn": 58}, {"ruleId": "1983", "severity": 1, "message": "2092", "line": 2, "column": 23, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 34}, {"ruleId": "1983", "severity": 1, "message": "2152", "line": 4, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 28}, {"ruleId": "1983", "severity": 1, "message": "2153", "line": 5, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2154", "line": 5, "column": 33, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 40}, {"ruleId": "1983", "severity": 1, "message": "2155", "line": 5, "column": 42, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 51}, {"ruleId": "1983", "severity": 1, "message": "2156", "line": 8, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 21}, {"ruleId": "2157", "severity": 1, "message": "2158", "line": 12, "column": 25, "nodeType": "2001", "endLine": 12, "endColumn": 614}, {"ruleId": "1983", "severity": 1, "message": "2095", "line": 1, "column": 46, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 49}, {"ruleId": "1983", "severity": 1, "message": "2159", "line": 3, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2160", "line": 15, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2161", "line": 15, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 37}, {"ruleId": "1990", "severity": 1, "message": "2162", "line": 21, "column": 8, "nodeType": "1992", "endLine": 21, "endColumn": 26, "suggestions": "2163"}, {"ruleId": "1990", "severity": 1, "message": "2164", "line": 40, "column": 8, "nodeType": "1992", "endLine": 40, "endColumn": 10, "suggestions": "2165"}, {"ruleId": "1983", "severity": 1, "message": "2002", "line": 2, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 11}, {"ruleId": "1983", "severity": 1, "message": "2009", "line": 3, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2166", "line": 13, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 13, "endColumn": 23}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 290, "column": 56, "nodeType": "2041", "messageId": "2042", "endLine": 290, "endColumn": 58}, {"ruleId": "1983", "severity": 1, "message": "2167", "line": 15, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2168", "line": 19, "column": 22, "nodeType": "1985", "messageId": "1986", "endLine": 19, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2148", "line": 4, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2169", "line": 5, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 16}, {"ruleId": "1983", "severity": 1, "message": "2170", "line": 100, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 100, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2171", "line": 111, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 111, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2049", "line": 15, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2036", "line": 7, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 36}, {"ruleId": "1983", "severity": 1, "message": "2049", "line": 13, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 13, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2049", "line": 14, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 14, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2036", "line": 7, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 36}, {"ruleId": "1983", "severity": 1, "message": "2049", "line": 13, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 13, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2036", "line": 7, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 36}, {"ruleId": "2172", "severity": 1, "message": "2173", "line": 28, "column": 74, "nodeType": "2041", "messageId": "2174", "endLine": 28, "endColumn": 75}, {"ruleId": "1983", "severity": 1, "message": "2175", "line": 6, "column": 25, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2176", "line": 6, "column": 31, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 37}, {"ruleId": "1983", "severity": 1, "message": "2068", "line": 6, "column": 39, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 49}, {"ruleId": "1983", "severity": 1, "message": "2098", "line": 6, "column": 51, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 56}, {"ruleId": "1983", "severity": 1, "message": "2177", "line": 6, "column": 65, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 70}, {"ruleId": "1990", "severity": 1, "message": "2178", "line": 50, "column": 8, "nodeType": "1992", "endLine": 50, "endColumn": 28, "suggestions": "2179"}, {"ruleId": "1990", "severity": 1, "message": "2178", "line": 54, "column": 8, "nodeType": "1992", "endLine": 54, "endColumn": 21, "suggestions": "2180"}, {"ruleId": "1983", "severity": 1, "message": "2181", "line": 37, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 37, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2182", "line": 44, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 44, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2183", "line": 47, "column": 9, "nodeType": "2184", "endLine": 51, "endColumn": 4}, {"ruleId": "1983", "severity": 1, "message": "2185", "line": 16, "column": 45, "nodeType": "1985", "messageId": "1986", "endLine": 16, "endColumn": 57}, {"ruleId": "1990", "severity": 1, "message": "2186", "line": 55, "column": 8, "nodeType": "1992", "endLine": 55, "endColumn": 14, "suggestions": "2187"}, {"ruleId": "1990", "severity": 1, "message": "2188", "line": 109, "column": 11, "nodeType": "2184", "endLine": 115, "endColumn": 6, "suggestions": "2189"}, {"ruleId": "1990", "severity": 1, "message": "2190", "line": 109, "column": 11, "nodeType": "2184", "endLine": 115, "endColumn": 6, "suggestions": "2191"}, {"ruleId": "1990", "severity": 1, "message": "2192", "line": 109, "column": 11, "nodeType": "2184", "endLine": 115, "endColumn": 6, "suggestions": "2193"}, {"ruleId": "1983", "severity": 1, "message": "2194", "line": 117, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 117, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "2195", "line": 232, "column": 8, "nodeType": "1992", "endLine": 232, "endColumn": 23, "suggestions": "2196"}, {"ruleId": "1983", "severity": 1, "message": "2197", "line": 283, "column": 19, "nodeType": "1985", "messageId": "1986", "endLine": 283, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 371, "column": 37, "nodeType": "1985", "endLine": 371, "endColumn": 48}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 398, "column": 37, "nodeType": "1985", "endLine": 398, "endColumn": 48}, {"ruleId": "2198", "severity": 1, "message": "2199", "line": 629, "column": 60, "nodeType": "2200", "messageId": "2201", "endLine": 629, "endColumn": 61}, {"ruleId": "1990", "severity": 1, "message": "2202", "line": 785, "column": 8, "nodeType": "1992", "endLine": 785, "endColumn": 99, "suggestions": "2203"}, {"ruleId": "1990", "severity": 1, "message": "2204", "line": 893, "column": 8, "nodeType": "1992", "endLine": 893, "endColumn": 36, "suggestions": "2205"}, {"ruleId": "1983", "severity": 1, "message": "2206", "line": 2, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2207", "line": 4, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2208", "line": 18, "column": 25, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2209", "line": 18, "column": 137, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 146}, {"ruleId": "1983", "severity": 1, "message": "2210", "line": 18, "column": 148, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 161}, {"ruleId": "1983", "severity": 1, "message": "2211", "line": 18, "column": 163, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 173}, {"ruleId": "1983", "severity": 1, "message": "2212", "line": 18, "column": 175, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 182}, {"ruleId": "1983", "severity": 1, "message": "2213", "line": 18, "column": 184, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 189}, {"ruleId": "1983", "severity": 1, "message": "2214", "line": 18, "column": 191, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 205}, {"ruleId": "1983", "severity": 1, "message": "2215", "line": 34, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 34, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2216", "line": 39, "column": 7, "nodeType": "1985", "messageId": "1986", "endLine": 39, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2217", "line": 40, "column": 7, "nodeType": "1985", "messageId": "1986", "endLine": 40, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2218", "line": 64, "column": 7, "nodeType": "1985", "messageId": "1986", "endLine": 64, "endColumn": 17}, {"ruleId": "1983", "severity": 1, "message": "2219", "line": 65, "column": 7, "nodeType": "1985", "messageId": "1986", "endLine": 65, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2220", "line": 67, "column": 7, "nodeType": "1985", "messageId": "1986", "endLine": 67, "endColumn": 17}, {"ruleId": "1983", "severity": 1, "message": "2095", "line": 4, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 13}, {"ruleId": "1983", "severity": 1, "message": "2021", "line": 6, "column": 35, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 46}, {"ruleId": "1983", "severity": 1, "message": "2221", "line": 10, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 10, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2056", "line": 26, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 26, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2186", "line": 37, "column": 8, "nodeType": "1992", "endLine": 37, "endColumn": 14, "suggestions": "2222"}, {"ruleId": "1983", "severity": 1, "message": "2118", "line": 15, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2223", "line": 18, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2002", "line": 21, "column": 98, "nodeType": "1985", "messageId": "1986", "endLine": 21, "endColumn": 99}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 262, "column": 38, "nodeType": "2041", "messageId": "2042", "endLine": 262, "endColumn": 40}, {"ruleId": "1983", "severity": 1, "message": "2012", "line": 7, "column": 111, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 115}, {"ruleId": "1983", "severity": 1, "message": "2224", "line": 7, "column": 117, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 127}, {"ruleId": "1983", "severity": 1, "message": "2105", "line": 10, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 10, "endColumn": 24}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 178, "column": 123, "nodeType": "2110", "messageId": "2111", "endLine": 178, "endColumn": 125}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 178, "column": 197, "nodeType": "2110", "messageId": "2111", "endLine": 178, "endColumn": 199}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 187, "column": 120, "nodeType": "2110", "messageId": "2111", "endLine": 187, "endColumn": 122}, {"ruleId": "2108", "severity": 1, "message": "2109", "line": 187, "column": 189, "nodeType": "2110", "messageId": "2111", "endLine": 187, "endColumn": 191}, {"ruleId": "1983", "severity": 1, "message": "2096", "line": 15, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 13}, {"ruleId": "1983", "severity": 1, "message": "2066", "line": 17, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 17, "endColumn": 11}, {"ruleId": "1983", "severity": 1, "message": "2012", "line": 20, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 20, "endColumn": 9}, {"ruleId": "1983", "severity": 1, "message": "2225", "line": 21, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 21, "endColumn": 17}, {"ruleId": "1983", "severity": 1, "message": "2067", "line": 24, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 24, "endColumn": 10}, {"ruleId": "1983", "severity": 1, "message": "2118", "line": 28, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 28, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2226", "line": 30, "column": 7, "nodeType": "1985", "messageId": "1986", "endLine": 30, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2227", "line": 159, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 159, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2228", "line": 160, "column": 21, "nodeType": "1985", "messageId": "1986", "endLine": 160, "endColumn": 31}, {"ruleId": "1990", "severity": 1, "message": "2229", "line": 166, "column": 8, "nodeType": "1992", "endLine": 166, "endColumn": 18, "suggestions": "2230"}, {"ruleId": "1983", "severity": 1, "message": "2231", "line": 15, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 16}, {"ruleId": "1983", "severity": 1, "message": "2232", "line": 16, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 16, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2233", "line": 26, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 26, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2234", "line": 199, "column": 8, "nodeType": "1992", "endLine": 199, "endColumn": 21, "suggestions": "2235"}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 49, "column": 37, "nodeType": "2041", "messageId": "2042", "endLine": 49, "endColumn": 39}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 51, "column": 44, "nodeType": "2041", "messageId": "2042", "endLine": 51, "endColumn": 46}, {"ruleId": "1983", "severity": 1, "message": "2236", "line": 8, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 16}, {"ruleId": "1983", "severity": 1, "message": "2002", "line": 8, "column": 18, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2099", "line": 8, "column": 21, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2237", "line": 15, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 17}, {"ruleId": "1983", "severity": 1, "message": "2238", "line": 16, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 16, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2239", "line": 26, "column": 24, "nodeType": "1985", "messageId": "1986", "endLine": 26, "endColumn": 37}, {"ruleId": "1983", "severity": 1, "message": "2240", "line": 111, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 111, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2118", "line": 7, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2012", "line": 13, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 13, "endColumn": 9}, {"ruleId": "1983", "severity": 1, "message": "2096", "line": 17, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 17, "endColumn": 13}, {"ruleId": "1983", "severity": 1, "message": "2241", "line": 19, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 19, "endColumn": 13}, {"ruleId": "1990", "severity": 1, "message": "2242", "line": 129, "column": 8, "nodeType": "1992", "endLine": 129, "endColumn": 18, "suggestions": "2243"}, {"ruleId": "1983", "severity": 1, "message": "2244", "line": 155, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 155, "endColumn": 32}, {"ruleId": "1983", "severity": 1, "message": "2245", "line": 232, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 232, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2246", "line": 39, "column": 8, "nodeType": "1992", "endLine": 39, "endColumn": 30, "suggestions": "2247"}, {"ruleId": "1999", "severity": 1, "message": "2000", "line": 226, "column": 69, "nodeType": "2001", "endLine": 230, "endColumn": 71}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 49, "column": 108, "nodeType": "2041", "messageId": "2042", "endLine": 49, "endColumn": 110}, {"ruleId": "1983", "severity": 1, "message": "2028", "line": 55, "column": 42, "nodeType": "1985", "messageId": "1986", "endLine": 55, "endColumn": 47}, {"ruleId": "1990", "severity": 1, "message": "2178", "line": 88, "column": 8, "nodeType": "1992", "endLine": 88, "endColumn": 28, "suggestions": "2248"}, {"ruleId": "1990", "severity": 1, "message": "2178", "line": 92, "column": 8, "nodeType": "1992", "endLine": 92, "endColumn": 21, "suggestions": "2249"}, {"ruleId": "1983", "severity": 1, "message": "2250", "line": 7, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2251", "line": 8, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 12}, {"ruleId": "1983", "severity": 1, "message": "2252", "line": 13, "column": 3, "nodeType": "1985", "messageId": "1986", "endLine": 13, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2149", "line": 14, "column": 3, "nodeType": "1985", "messageId": "1986", "endLine": 14, "endColumn": 20}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 285, "column": 65, "nodeType": "2041", "messageId": "2042", "endLine": 285, "endColumn": 67}, {"ruleId": "1983", "severity": 1, "message": "2253", "line": 28, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 28, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2068", "line": 29, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 29, "endColumn": 15}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 406, "column": 101, "nodeType": "2041", "messageId": "2042", "endLine": 406, "endColumn": 103}, {"ruleId": "1983", "severity": 1, "message": "2254", "line": 55, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 55, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2255", "line": 55, "column": 32, "nodeType": "1985", "messageId": "1986", "endLine": 55, "endColumn": 42}, {"ruleId": "1983", "severity": 1, "message": "2256", "line": 59, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 59, "endColumn": 27}, {"ruleId": "1983", "severity": 1, "message": "2257", "line": 70, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 70, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2258", "line": 82, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 82, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2259", "line": 88, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 88, "endColumn": 30}, {"ruleId": "1983", "severity": 1, "message": "2244", "line": 94, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 94, "endColumn": 32}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 336, "column": 35, "nodeType": "2041", "messageId": "2042", "endLine": 336, "endColumn": 37}, {"ruleId": "1983", "severity": 1, "message": "2055", "line": 9, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 9, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2260", "line": 11, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 11, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2066", "line": 19, "column": 3, "nodeType": "1985", "messageId": "1986", "endLine": 19, "endColumn": 9}, {"ruleId": "1983", "severity": 1, "message": "2056", "line": 31, "column": 30, "nodeType": "1985", "messageId": "1986", "endLine": 31, "endColumn": 40}, {"ruleId": "1983", "severity": 1, "message": "2261", "line": 4, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2262", "line": 5, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 14}, {"ruleId": "1983", "severity": 1, "message": "2263", "line": 17, "column": 26, "nodeType": "1985", "messageId": "1986", "endLine": 17, "endColumn": 46}, {"ruleId": "1983", "severity": 1, "message": "2264", "line": 24, "column": 96, "nodeType": "1985", "messageId": "1986", "endLine": 24, "endColumn": 100}, {"ruleId": "1983", "severity": 1, "message": "2066", "line": 24, "column": 102, "nodeType": "1985", "messageId": "1986", "endLine": 24, "endColumn": 108}, {"ruleId": "1983", "severity": 1, "message": "2008", "line": 26, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 26, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2265", "line": 27, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 27, "endColumn": 13}, {"ruleId": "1983", "severity": 1, "message": "2013", "line": 34, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 34, "endColumn": 17}, {"ruleId": "1983", "severity": 1, "message": "2266", "line": 35, "column": 47, "nodeType": "1985", "messageId": "1986", "endLine": 35, "endColumn": 54}, {"ruleId": "1983", "severity": 1, "message": "2056", "line": 37, "column": 17, "nodeType": "1985", "messageId": "1986", "endLine": 37, "endColumn": 27}, {"ruleId": "1983", "severity": 1, "message": "2267", "line": 91, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 91, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2268", "line": 92, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 92, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2269", "line": 98, "column": 20, "nodeType": "1985", "messageId": "1986", "endLine": 98, "endColumn": 31}, {"ruleId": "1983", "severity": 1, "message": "2270", "line": 99, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 99, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2271", "line": 99, "column": 22, "nodeType": "1985", "messageId": "1986", "endLine": 99, "endColumn": 35}, {"ruleId": "1983", "severity": 1, "message": "2272", "line": 100, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 100, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2273", "line": 100, "column": 20, "nodeType": "1985", "messageId": "1986", "endLine": 100, "endColumn": 31}, {"ruleId": "1983", "severity": 1, "message": "2274", "line": 103, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 103, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2275", "line": 104, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 104, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2276", "line": 105, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 105, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2277", "line": 106, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 106, "endColumn": 27}, {"ruleId": "1983", "severity": 1, "message": "2278", "line": 136, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 136, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2279", "line": 137, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 137, "endColumn": 27}, {"ruleId": "1983", "severity": 1, "message": "2280", "line": 139, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 139, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2281", "line": 139, "column": 25, "nodeType": "1985", "messageId": "1986", "endLine": 139, "endColumn": 41}, {"ruleId": "1983", "severity": 1, "message": "2282", "line": 167, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 167, "endColumn": 25}, {"ruleId": "1983", "severity": 1, "message": "2283", "line": 173, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 173, "endColumn": 33}, {"ruleId": "1990", "severity": 1, "message": "2284", "line": 286, "column": 6, "nodeType": "1992", "endLine": 286, "endColumn": 32, "suggestions": "2285"}, {"ruleId": "1983", "severity": 1, "message": "2286", "line": 364, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 364, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2287", "line": 742, "column": 6, "nodeType": "1992", "endLine": 742, "endColumn": 25, "suggestions": "2288"}, {"ruleId": "1983", "severity": 1, "message": "2289", "line": 764, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 764, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2290", "line": 776, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 776, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2260", "line": 8, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2067", "line": 19, "column": 3, "nodeType": "1985", "messageId": "1986", "endLine": 19, "endColumn": 8}, {"ruleId": "1983", "severity": 1, "message": "2291", "line": 20, "column": 3, "nodeType": "1985", "messageId": "1986", "endLine": 20, "endColumn": 10}, {"ruleId": "1983", "severity": 1, "message": "2292", "line": 30, "column": 36, "nodeType": "1985", "messageId": "1986", "endLine": 30, "endColumn": 56}, {"ruleId": "1983", "severity": 1, "message": "2293", "line": 32, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 32, "endColumn": 30}, {"ruleId": "1983", "severity": 1, "message": "2294", "line": 32, "column": 32, "nodeType": "1985", "messageId": "1986", "endLine": 32, "endColumn": 55}, {"ruleId": "1983", "severity": 1, "message": "2295", "line": 2, "column": 26, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 32}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 35, "column": 29, "nodeType": "1985", "endLine": 35, "endColumn": 40}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 35, "column": 30, "nodeType": "1985", "endLine": 35, "endColumn": 41}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 347, "column": 68, "nodeType": "2041", "messageId": "2042", "endLine": 347, "endColumn": 70}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 360, "column": 64, "nodeType": "2041", "messageId": "2042", "endLine": 360, "endColumn": 66}, {"ruleId": "1983", "severity": 1, "message": "2095", "line": 1, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 13}, {"ruleId": "1983", "severity": 1, "message": "2296", "line": 12, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 12, "endColumn": 13}, {"ruleId": "1983", "severity": 1, "message": "2011", "line": 24, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 24, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2012", "line": 26, "column": 119, "nodeType": "1985", "messageId": "1986", "endLine": 26, "endColumn": 123}, {"ruleId": "1983", "severity": 1, "message": "2297", "line": 26, "column": 135, "nodeType": "1985", "messageId": "1986", "endLine": 26, "endColumn": 140}, {"ruleId": "1983", "severity": 1, "message": "2013", "line": 99, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 99, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2056", "line": 107, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 107, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2298", "line": 217, "column": 8, "nodeType": "1992", "endLine": 217, "endColumn": 38, "suggestions": "2299"}, {"ruleId": "1990", "severity": 1, "message": "2300", "line": 67, "column": 8, "nodeType": "1992", "endLine": 67, "endColumn": 47, "suggestions": "2301"}, {"ruleId": "1983", "severity": 1, "message": "2020", "line": 4, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 19}, {"ruleId": "1983", "severity": 1, "message": "2066", "line": 17, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 17, "endColumn": 11}, {"ruleId": "1983", "severity": 1, "message": "2003", "line": 20, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 20, "endColumn": 9}, {"ruleId": "1983", "severity": 1, "message": "2302", "line": 27, "column": 5, "nodeType": "1985", "messageId": "1986", "endLine": 27, "endColumn": 32}, {"ruleId": "1990", "severity": 1, "message": "2303", "line": 122, "column": 8, "nodeType": "1992", "endLine": 122, "endColumn": 10, "suggestions": "2304"}, {"ruleId": "1983", "severity": 1, "message": "2099", "line": 6, "column": 56, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 64}, {"ruleId": "1983", "severity": 1, "message": "2176", "line": 6, "column": 73, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 79}, {"ruleId": "1983", "severity": 1, "message": "2068", "line": 6, "column": 81, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 91}, {"ruleId": "1983", "severity": 1, "message": "2305", "line": 5, "column": 60, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 66}, {"ruleId": "1983", "severity": 1, "message": "2236", "line": 5, "column": 68, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 74}, {"ruleId": "1983", "severity": 1, "message": "2306", "line": 2, "column": 42, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 51}, {"ruleId": "1983", "severity": 1, "message": "2307", "line": 5, "column": 11, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 24}, {"ruleId": "1983", "severity": 1, "message": "2118", "line": 8, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 22}, {"ruleId": "1983", "severity": 1, "message": "2266", "line": 16, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 16, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2308", "line": 17, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 17, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "1995", "line": 1, "column": 21, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2309", "line": 3, "column": 58, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 69}, {"ruleId": "1983", "severity": 1, "message": "2310", "line": 3, "column": 71, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 81}, {"ruleId": "1983", "severity": 1, "message": "2311", "line": 48, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 48, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2312", "line": 56, "column": 9, "nodeType": "1985", "messageId": "1986", "endLine": 56, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 37, "column": 30, "nodeType": "1985", "endLine": 37, "endColumn": 41}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 35, "column": 32, "nodeType": "1985", "endLine": 35, "endColumn": 43}, {"ruleId": "1983", "severity": 1, "message": "2099", "line": 3, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2067", "line": 3, "column": 20, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 25}, {"ruleId": "1983", "severity": 1, "message": "2313", "line": 3, "column": 27, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 38}, {"ruleId": "1983", "severity": 1, "message": "2119", "line": 3, "column": 40, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 52}, {"ruleId": "1983", "severity": 1, "message": "2309", "line": 3, "column": 54, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 65}, {"ruleId": "1983", "severity": 1, "message": "2314", "line": 6, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 6, "endColumn": 32}, {"ruleId": "1983", "severity": 1, "message": "2315", "line": 8, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2316", "line": 8, "column": 25, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 39}, {"ruleId": "1983", "severity": 1, "message": "1995", "line": 2, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2099", "line": 3, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2313", "line": 3, "column": 27, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 38}, {"ruleId": "1983", "severity": 1, "message": "2119", "line": 3, "column": 40, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 52}, {"ruleId": "1983", "severity": 1, "message": "2233", "line": 4, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 4, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2317", "line": 5, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 5, "endColumn": 30}, {"ruleId": "1983", "severity": 1, "message": "2318", "line": 7, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 21}, {"ruleId": "1983", "severity": 1, "message": "2315", "line": 8, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 8, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2319", "line": 9, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 9, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2266", "line": 15, "column": 39, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 46}, {"ruleId": "1983", "severity": 1, "message": "2166", "line": 17, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 17, "endColumn": 23}, {"ruleId": "1983", "severity": 1, "message": "2320", "line": 18, "column": 37, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 48}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 181, "column": 54, "nodeType": "2041", "messageId": "2042", "endLine": 181, "endColumn": 56}, {"ruleId": "1990", "severity": 1, "message": "2321", "line": 57, "column": 8, "nodeType": "1992", "endLine": 57, "endColumn": 38, "suggestions": "2322"}, {"ruleId": "1983", "severity": 1, "message": "2095", "line": 1, "column": 17, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2323", "line": 2, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 2, "endColumn": 26}, {"ruleId": "2039", "severity": 1, "message": "2151", "line": 186, "column": 50, "nodeType": "2041", "messageId": "2042", "endLine": 186, "endColumn": 52}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2324", "line": 277, "column": 5, "nodeType": null}, {"ruleId": "1983", "severity": 1, "message": "2313", "line": 1, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2325", "line": 53, "column": 8, "nodeType": "1992", "endLine": 53, "endColumn": 10, "suggestions": "2326"}, {"ruleId": "1983", "severity": 1, "message": "2095", "line": 1, "column": 38, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 41}, {"ruleId": "1983", "severity": 1, "message": "2327", "line": 14, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 14, "endColumn": 28}, {"ruleId": "1983", "severity": 1, "message": "2328", "line": 15, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 15, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2329", "line": 26, "column": 8, "nodeType": "1992", "endLine": 26, "endColumn": 36, "suggestions": "2330"}, {"ruleId": "1990", "severity": 1, "message": "2331", "line": 31, "column": 8, "nodeType": "1992", "endLine": 31, "endColumn": 26, "suggestions": "2332"}, {"ruleId": "1983", "severity": 1, "message": "2094", "line": 18, "column": 8, "nodeType": "1985", "messageId": "1986", "endLine": 18, "endColumn": 31}, {"ruleId": "1983", "severity": 1, "message": "2333", "line": 3, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 20}, {"ruleId": "1983", "severity": 1, "message": "2334", "line": 3, "column": 22, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 29}, {"ruleId": "1983", "severity": 1, "message": "2335", "line": 3, "column": 31, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 38}, {"ruleId": "1983", "severity": 1, "message": "2336", "line": 3, "column": 40, "nodeType": "1985", "messageId": "1986", "endLine": 3, "endColumn": 50}, {"ruleId": "1983", "severity": 1, "message": "2337", "line": 7, "column": 12, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 17}, {"ruleId": "1983", "severity": 1, "message": "2338", "line": 7, "column": 19, "nodeType": "1985", "messageId": "1986", "endLine": 7, "endColumn": 27}, {"ruleId": "1983", "severity": 1, "message": "1995", "line": 1, "column": 10, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 18}, {"ruleId": "1983", "severity": 1, "message": "2095", "line": 1, "column": 31, "nodeType": "1985", "messageId": "1986", "endLine": 1, "endColumn": 34}, {"ruleId": "1983", "severity": 1, "message": "2339", "line": 75, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 75, "endColumn": 28}, {"ruleId": "1983", "severity": 1, "message": "2266", "line": 75, "column": 30, "nodeType": "1985", "messageId": "1986", "endLine": 75, "endColumn": 37}, {"ruleId": "1983", "severity": 1, "message": "2255", "line": 75, "column": 39, "nodeType": "1985", "messageId": "1986", "endLine": 75, "endColumn": 49}, {"ruleId": "1983", "severity": 1, "message": "2050", "line": 13, "column": 45, "nodeType": "1985", "messageId": "1986", "endLine": 13, "endColumn": 54}, {"ruleId": "1983", "severity": 1, "message": "2340", "line": 66, "column": 13, "nodeType": "1985", "messageId": "1986", "endLine": 66, "endColumn": 25}, {"ruleId": "1983", "severity": 1, "message": "2029", "line": 67, "column": 26, "nodeType": "1985", "messageId": "1986", "endLine": 67, "endColumn": 36}, "no-unused-vars", "'AdminUserSearchPage' is defined but never used.", "Identifier", "unusedVar", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'BeeMathLogo' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.username'. Either include it or remove the dependency array.", "ArrayExpression", ["2341"], "'resultAction' is assigned a value but never used.", "'useState' is defined but never used.", "'isFilterVIew' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", ["2342"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'setIsFilterView' is defined but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "'loadingExam' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2343"], "'showAddStudent' is assigned a value but never used.", "'AdminLayout' is defined but never used.", "'setAttempts' is defined but never used.", "'ScoreDistributionChart' is defined but never used.", "'setLogs' is assigned a value but never used.", "'setAnswers' is assigned a value but never used.", "'prevRanks' is assigned a value but never used.", "'setRankChanges' is assigned a value but never used.", "'setUpdatedAttemptId' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'handleClickedDetail' is assigned a value but never used.", "'handleClickedPreviewExam' is assigned a value but never used.", "'handleClickedQuestions' is assigned a value but never used.", "'handleClickedTracking' is assigned a value but never used.", "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'errorMsg' is assigned a value but never used.", "'setExam' is defined but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'backgroundImage' is defined but never used.", "'shouldFloat' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2344"], "'useEffect' is defined but never used.", "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'setSortOrder' is defined but never used.", "'resetFilters' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2345"], "'processInputForUpdate' is defined but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'setDeleteMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2346"], "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2347"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2348"], "'setClass' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "'LatexRenderer' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2349"], "'compose' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["2350", "2351"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'use' is defined but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2352"], "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2353"], "'ExamDefaultImage' is defined but never used.", "'imageUrl' is assigned a value but never used.", "'ActionButton' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2354"], "'LoadingSpinner' is defined but never used.", "'ChevronRight' is defined but never used.", "'toggleDropdown' is defined but never used.", "'toggleExamDropdown' is defined but never used.", "'dropdown' is assigned a value but never used.", "'tuitionDropdown' is assigned a value but never used.", "'examDropdown' is assigned a value but never used.", "'icon5' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2355"], ["2356", "2357"], ["2358", "2359"], ["2360", "2361"], "Unnecessary escape character: \\).", ["2362", "2363"], ["2364", "2365"], ["2366", "2367"], ["2368", "2369"], ["2370", "2371"], ["2372", "2373"], ["2374", "2375"], ["2376", "2377"], ["2378", "2379"], ["2380", "2381"], ["2382", "2383"], "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2384"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2385"], "'InputSearch' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'menuOpen' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'toggleCloseSidebar' is defined but never used.", "'ArrowRight' is defined but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "'isHovered' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2386"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2387"], "'currentTime' is assigned a value but never used.", "'setSuccess' is defined but never used.", "'success' is assigned a value but never used.", "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchExams' and 'isClassroomExam'. Either include them or remove the dependency array.", ["2388"], ["2389"], "'isMobile' is assigned a value but never used.", "'currentQuestionType' is assigned a value but never used.", "The 'allQuestions' array makes the dependencies of useEffect Hook (at line 160) change on every render. To fix this, wrap the initialization of 'allQuestions' in its own useMemo() Hook.", "VariableDeclarator", "'isFullscreen' is defined but never used.", "React Hook useEffect has missing dependencies: 'examId' and 'navigate'. Either include them or remove the dependency array.", ["2390"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 668) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2391"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 679) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2392"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 691) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2393"], "'addErrorQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'flag'. Either include it or remove the dependency array.", ["2394"], "'result' is assigned a value but never used.", "no-sequences", "Unexpected use of comma operator.", "SequenceExpression", "unexpectedCommaExpression", "React Hook useEffect has a missing dependency: 'handleAutoSubmit'. Either include it or remove the dependency array.", ["2395"], "React Hook useEffect has missing dependencies: 'dispatch', 'exam?.isCheatingCheckEnabled', 'user.firstName', and 'user.lastName'. Either include them or remove the dependency array.", ["2396"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'ScoreBarChart' is defined but never used.", ["2397"], "'NoDataFound' is defined but never used.", "'BookMarked' is defined but never used.", "'ExternalLink' is defined but never used.", "'ButtonSidebar' is assigned a value but never used.", "'choice' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2398"], "'ChevronDown' is defined but never used.", "'ChevronUp' is defined but never used.", "'UserLayout' is defined but never used.", "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2399"], "'Filter' is defined but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2400"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2401"], ["2402"], ["2403"], "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'BarChart2' is defined but never used.", "'articles' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'formatNumberWithDots' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'loading' is assigned a value but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterGraduationYear', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2404"], "'handleBatchAdd' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tuitionPayments'. Either include it or remove the dependency array.", ["2405"], "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'Receipt' is defined but never used.", "'studentClassTuitions' is assigned a value but never used.", "'classTuitionsLoading' is assigned a value but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2406"], "React Hook useEffect has a missing dependency: 'user?.id'. Either include it or remove the dependency array.", ["2407"], "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2408"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", "'CheckCircle' is defined but never used.", "'DollarSign' is defined but never used.", "'getStatusBadge' is assigned a value but never used.", "'getPaymentStatusIcon' is assigned a value but never used.", "'ChevronLeft' is defined but never used.", "'getLearningItemWeekend' is defined but never used.", "'resetCalendar' is defined but never used.", "'setSelectedDay' is defined but never used.", "'fetchClassesOverview' is defined but never used.", "'CalendarMonth' is defined but never used.", "'SidebarCalendar' is defined but never used.", "'selectedDay' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'firstGridDate' and 'lastGridDate'. Either include them or remove the dependency array.", ["2409"], "'NavigateTimeButton' is defined but never used.", "Parsing error: Unexpected token (277:5)", "React Hook useEffect has a missing dependency: 'onClose'. Either include it or remove the dependency array. If 'onClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2410"], "'selectedQuestion' is assigned a value but never used.", "'editedText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isChanged'. Either include it or remove the dependency array.", ["2411"], "React Hook useEffect has missing dependencies: 'isChanged', 'questions', and 'questionsEdited'. Either include them or remove the dependency array.", ["2412"], "'TableAdmin' is defined but never used.", "'TdAdmin' is defined but never used.", "'ThAdmin' is defined but never used.", "'TheadAdmin' is defined but never used.", "'month' is assigned a value but never used.", "'setMonth' is assigned a value but never used.", "'tuitionPayments' is assigned a value but never used.", "'userAttempts' is assigned a value but never used.", {"desc": "2413", "fix": "2414"}, {"desc": "2415", "fix": "2416"}, {"desc": "2417", "fix": "2418"}, {"desc": "2419", "fix": "2420"}, {"desc": "2421", "fix": "2422"}, {"desc": "2423", "fix": "2424"}, {"desc": "2425", "fix": "2426"}, {"desc": "2427", "fix": "2428"}, {"desc": "2429", "fix": "2430"}, {"messageId": "2431", "fix": "2432", "desc": "2433"}, {"messageId": "2434", "fix": "2435", "desc": "2436"}, {"desc": "2437", "fix": "2438"}, {"desc": "2439", "fix": "2440"}, {"desc": "2441", "fix": "2442"}, {"desc": "2443", "fix": "2444"}, {"messageId": "2431", "fix": "2445", "desc": "2433"}, {"messageId": "2434", "fix": "2446", "desc": "2436"}, {"messageId": "2431", "fix": "2447", "desc": "2433"}, {"messageId": "2434", "fix": "2448", "desc": "2436"}, {"messageId": "2431", "fix": "2449", "desc": "2433"}, {"messageId": "2434", "fix": "2450", "desc": "2436"}, {"messageId": "2431", "fix": "2451", "desc": "2433"}, {"messageId": "2434", "fix": "2452", "desc": "2436"}, {"messageId": "2431", "fix": "2453", "desc": "2433"}, {"messageId": "2434", "fix": "2454", "desc": "2436"}, {"messageId": "2431", "fix": "2455", "desc": "2433"}, {"messageId": "2434", "fix": "2456", "desc": "2436"}, {"messageId": "2431", "fix": "2457", "desc": "2433"}, {"messageId": "2434", "fix": "2458", "desc": "2436"}, {"messageId": "2431", "fix": "2459", "desc": "2433"}, {"messageId": "2434", "fix": "2460", "desc": "2436"}, {"messageId": "2431", "fix": "2461", "desc": "2433"}, {"messageId": "2434", "fix": "2462", "desc": "2436"}, {"messageId": "2431", "fix": "2463", "desc": "2433"}, {"messageId": "2434", "fix": "2464", "desc": "2436"}, {"messageId": "2431", "fix": "2465", "desc": "2433"}, {"messageId": "2434", "fix": "2466", "desc": "2436"}, {"messageId": "2431", "fix": "2467", "desc": "2433"}, {"messageId": "2434", "fix": "2468", "desc": "2436"}, {"messageId": "2431", "fix": "2469", "desc": "2433"}, {"messageId": "2434", "fix": "2470", "desc": "2436"}, {"messageId": "2431", "fix": "2471", "desc": "2433"}, {"messageId": "2434", "fix": "2472", "desc": "2436"}, {"desc": "2473", "fix": "2474"}, {"desc": "2475", "fix": "2476"}, {"desc": "2477", "fix": "2478"}, {"desc": "2479", "fix": "2480"}, {"desc": "2481", "fix": "2482"}, {"desc": "2483", "fix": "2484"}, {"desc": "2485", "fix": "2486"}, {"desc": "2487", "fix": "2488"}, {"desc": "2487", "fix": "2489"}, {"desc": "2487", "fix": "2490"}, {"desc": "2491", "fix": "2492"}, {"desc": "2493", "fix": "2494"}, {"desc": "2495", "fix": "2496"}, {"desc": "2485", "fix": "2497"}, {"desc": "2498", "fix": "2499"}, {"desc": "2500", "fix": "2501"}, {"desc": "2502", "fix": "2503"}, {"desc": "2504", "fix": "2505"}, {"desc": "2481", "fix": "2506"}, {"desc": "2483", "fix": "2507"}, {"desc": "2508", "fix": "2509"}, {"desc": "2510", "fix": "2511"}, {"desc": "2512", "fix": "2513"}, {"desc": "2514", "fix": "2515"}, {"desc": "2516", "fix": "2517"}, {"desc": "2518", "fix": "2519"}, {"desc": "2520", "fix": "2521"}, {"desc": "2522", "fix": "2523"}, {"desc": "2524", "fix": "2525"}, "Update the dependencies array to be: [user, navigate, formData.username]", {"range": "2526", "text": "2527"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "2528", "text": "2529"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "2530", "text": "2531"}, "Update the dependencies array to be: [options, selected, type]", {"range": "2532", "text": "2533"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "2534", "text": "2535"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "2536", "text": "2537"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "2538", "text": "2539"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "2540", "text": "2541"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "2542", "text": "2543"}, "removeEscape", {"range": "2544", "text": "2545"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2546", "text": "2547"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "2548", "text": "2549"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "2550", "text": "2551"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "2552", "text": "2553"}, "Update the dependencies array to be: [maxLength]", {"range": "2554", "text": "2555"}, {"range": "2556", "text": "2545"}, {"range": "2557", "text": "2547"}, {"range": "2558", "text": "2545"}, {"range": "2559", "text": "2547"}, {"range": "2560", "text": "2545"}, {"range": "2561", "text": "2547"}, {"range": "2562", "text": "2545"}, {"range": "2563", "text": "2547"}, {"range": "2564", "text": "2545"}, {"range": "2565", "text": "2547"}, {"range": "2566", "text": "2545"}, {"range": "2567", "text": "2547"}, {"range": "2568", "text": "2545"}, {"range": "2569", "text": "2547"}, {"range": "2570", "text": "2545"}, {"range": "2571", "text": "2547"}, {"range": "2572", "text": "2545"}, {"range": "2573", "text": "2547"}, {"range": "2574", "text": "2545"}, {"range": "2575", "text": "2547"}, {"range": "2576", "text": "2545"}, {"range": "2577", "text": "2547"}, {"range": "2578", "text": "2545"}, {"range": "2579", "text": "2547"}, {"range": "2580", "text": "2545"}, {"range": "2581", "text": "2547"}, {"range": "2582", "text": "2545"}, {"range": "2583", "text": "2547"}, "Update the dependencies array to be: [handlePaste]", {"range": "2584", "text": "2585"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "2586", "text": "2587"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "2588", "text": "2589"}, "Update the dependencies array to be: [handleFile]", {"range": "2590", "text": "2591"}, "Update the dependencies array to be: [dispatch, fetchExams, isClassroomExam, isSearch]", {"range": "2592", "text": "2593"}, "Update the dependencies array to be: [currentPage, fetchExams, isClassroomExam]", {"range": "2594", "text": "2595"}, "Update the dependencies array to be: [exam, examId, navigate]", {"range": "2596", "text": "2597"}, "Wrap the definition of 'addQuestion' in its own useCallback() Hook.", {"range": "2598", "text": "2599"}, {"range": "2600", "text": "2599"}, {"range": "2601", "text": "2599"}, "Update the dependencies array to be: [flag, remainingTime]", {"range": "2602", "text": "2603"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", {"range": "2604", "text": "2605"}, "Update the dependencies array to be: [user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", {"range": "2606", "text": "2607"}, {"range": "2608", "text": "2597"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "2609", "text": "2610"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "2611", "text": "2612"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "2613", "text": "2614"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "2615", "text": "2616"}, {"range": "2617", "text": "2593"}, {"range": "2618", "text": "2595"}, "Update the dependencies array to be: [dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "2619", "text": "2620"}, "Update the dependencies array to be: [tuitionPayments, tuitionStatistics]", {"range": "2621", "text": "2622"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, monthTuition, dispatch]", {"range": "2623", "text": "2624"}, "Update the dependencies array to be: [dispatch, selectedYear, selectedMonth, user?.id]", {"range": "2625", "text": "2626"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "2627", "text": "2628"}, "Update the dependencies array to be: [dispatch, currentMonth, view, firstGridDate, lastGridDate]", {"range": "2629", "text": "2630"}, "Update the dependencies array to be: [onClose]", {"range": "2631", "text": "2632"}, "Update the dependencies array to be: [isChanged, questions, questionsEdited]", {"range": "2633", "text": "2634"}, "Update the dependencies array to be: [exam, editedExam, isChanged, questions, questionsEdited]", {"range": "2635", "text": "2636"}, [1911, 1927], "[user, navigate, formData.username]", [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [9548, 9558], "[dispatch, fetchPdfFiles]", [669, 671], "[options, selected, type]", [1909, 1927], "[dispatch, fetchQuestions, params]", [1518, 1563], "[dispatch, search, page, pageSize, sortOrder, classId]", [2625, 2647], "[inputValue, dispatch, setSearch]", [2124, 2148], "[codes, question, question.class]", [2225, 2245], "[codes, exam, exam?.class]", [10723, 10724], "", [10723, 10723], "\\", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [972, 974], "[maxLength]", [8880, 8881], [8880, 8880], [9345, 9346], [9345, 9345], [9541, 9542], [9541, 9541], [9543, 9544], [9543, 9543], [9613, 9614], [9613, 9613], [9615, 9616], [9615, 9615], [12161, 12162], [12161, 12161], [13081, 13082], [13081, 13081], [13248, 13249], [13248, 13248], [13250, 13251], [13250, 13250], [13320, 13321], [13320, 13320], [13322, 13323], [13322, 13322], [15926, 15927], [15926, 15926], [16620, 16621], [16620, 16620], [2448, 2450], "[handlePaste]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [2341, 2361], "[dispatch, fetchExams, isClassroomExam, isSearch]", [2439, 2452], "[currentPage, fetchExams, isClassroomExam]", [2482, 2488], "[exam, examId, navigate]", [4594, 4956], "useCallback((questionId) => {\r\n        // Only add to saveQuestions if not already there and not in errorQuestions\r\n        if (!saveQuestions.includes(questionId) && !errorQuestions.includes(questionId)) {\r\n            dispatch(setSaveQuestions([...saveQuestions, questionId]));\r\n        }\r\n        // DO NOT call removeErrorQuestion here - let Redux slice handle it\r\n    })", [4594, 4956], [4594, 4956], [9169, 9184], "[flag, remainingTime]", [31473, 31564], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", [35671, 35699], "[user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", [1496, 1502], [7568, 7578], "[dispatch, limit]", [6802, 6815], "[activeItem?.index, classDetail]", [6098, 6108], "[dispatch, location.search]", [1591, 1613], "[currentPage, didInit, loadReports]", [3871, 3891], [3965, 3978], [10387, 10413], "[dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [24202, 24221], "[tuitionPayments, tuitionStatistics]", [8386, 8416], "[<PERSON><PERSON><PERSON><PERSON>, monthTuition, dispatch]", [2133, 2172], "[dispatch, selected<PERSON>ear, selected<PERSON><PERSON><PERSON>, user?.id]", [3996, 3998], "[dispatch, userId]", [2456, 2486], "[dispatch, currentMonth, view, firstGridDate, lastGridDate]", [1949, 1951], "[onClose]", [1184, 1212], "[isChanged, questions, questionsEdited]", [1369, 1387], "[exam, editedExam, isChanged, questions, questionsEdited]"]