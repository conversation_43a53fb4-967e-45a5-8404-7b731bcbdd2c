[{"C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx": "5", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx": "7", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx": "8", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx": "9", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx": "10", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx": "11", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx": "12", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "13", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "14", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx": "15", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx": "16", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "17", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "18", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "19", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "20", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx": "21", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "22", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "23", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx": "24", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js": "25", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js": "26", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js": "27", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js": "28", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js": "29", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js": "30", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js": "31", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js": "32", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js": "33", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js": "34", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js": "35", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js": "36", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js": "37", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js": "38", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js": "39", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx": "40", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx": "41", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx": "42", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx": "43", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx": "44", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx": "45", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx": "46", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "47", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx": "48", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx": "49", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx": "50", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx": "51", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx": "52", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx": "53", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx": "55", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx": "56", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx": "57", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx": "58", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx": "59", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx": "60", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx": "61", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx": "63", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx": "64", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx": "65", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx": "66", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx": "67", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx": "68", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx": "69", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx": "70", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx": "71", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx": "72", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx": "73", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx": "74", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx": "75", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx": "76", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx": "77", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js": "78", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx": "79", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx": "80", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx": "81", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js": "82", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx": "83", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx": "84", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js": "85", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx": "86", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "87", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx": "88", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx": "89", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx": "90", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx": "91", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx": "92", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx": "93", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx": "94", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx": "95", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx": "96", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx": "97", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js": "98", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx": "99", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx": "100", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js": "101", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js": "102", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js": "103", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx": "104", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js": "105", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js": "106", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx": "107", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx": "108", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx": "109", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx": "110", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx": "111", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx": "112", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx": "113", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx": "114", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx": "115", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx": "116", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx": "117", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js": "118", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js": "119", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js": "120", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx": "121", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx": "122", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx": "123", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx": "124", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx": "125", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx": "126", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx": "127", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js": "128", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx": "129", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx": "130", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx": "131", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "132", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js": "133", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx": "134", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx": "135", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx": "136", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx": "137", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx": "138", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx": "139", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx": "140", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx": "141", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx": "142", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx": "143", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js": "144", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js": "145", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx": "146", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx": "147", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx": "148", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx": "149", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx": "150", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx": "151", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx": "152", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx": "153", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx": "154", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "155", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "156", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx": "157", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx": "158", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx": "159", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx": "160", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx": "161", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx": "162", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js": "163", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js": "164", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx": "165", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "166", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx": "167", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx": "168", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js": "169", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx": "170", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx": "171", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx": "172", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx": "173", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx": "174", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx": "175", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx": "176", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx": "177", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx": "178", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx": "179", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx": "180", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx": "181", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx": "182", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx": "183", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx": "184", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx": "185", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "186", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx": "187", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx": "188", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx": "189", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx": "190", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx": "191", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx": "192", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx": "193", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx": "194", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx": "195", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx": "196", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx": "197", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx": "198", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx": "199", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx": "200", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx": "201", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx": "202", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js": "203", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx": "204", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx": "205", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js": "206", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx": "207", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js": "208", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx": "209", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx": "210", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx": "211", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx": "212", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "213", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js": "214", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js": "215", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js": "216", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx": "217", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx": "218", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "219", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "220", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx": "221", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js": "222", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "223", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "224", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js": "225", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "226", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx": "227", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx": "228", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx": "229", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx": "230", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js": "231", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js": "232", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx": "233", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "234", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx": "235", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "236", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "237", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js": "238", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "239", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "240", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "241", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js": "242", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx": "243", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx": "244", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx": "245", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx": "246", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx": "247", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx": "248", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js": "249", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js": "250", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx": "251", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx": "252", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx": "253", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx": "254", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js": "255", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx": "256", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx": "257", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js": "258", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx": "259", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js": "260", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js": "261", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx": "262", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx": "263", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js": "264", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js": "265", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx": "266", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx": "267", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx": "268", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js": "269", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx": "270", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js": "271", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx": "272", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx": "273", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx": "274", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js": "275", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx": "276", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js": "277", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx": "278", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx": "279", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx": "280", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx": "281", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx": "282", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx": "283", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx": "284", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js": "285", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx": "286", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js": "287", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx": "288", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js": "289", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js": "290", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx": "291", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx": "292", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx": "293", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx": "294", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx": "295", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx": "296", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx": "297", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx": "298", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx": "299", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx": "300", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx": "301", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx": "302", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx": "303", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx": "304", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js": "305", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\ImageDropZone.jsx": "306", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SolutionEditor.jsx": "307", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StaffManagement.jsx": "308", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\dashboard\\dashboardSlice.js": "309", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\dashboardApi.js": "310", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\layout\\AdminLayout.jsx": "311", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AddExamAdmin.jsx": "312", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\RightContent.jsx": "313", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\LeftContent.jsx": "314", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\addExam\\addExamSlice.js": "315", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\CompactStepHeader.jsx": "316", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\NavigateBar.jsx": "317", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\hooks\\useDebouncedEffect.js": "318", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddImagesModal.jsx": "319", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\SolutionEditor.jsx": "320"}, {"size": 837, "mtime": 1748800674146, "results": "321", "hashOfConfig": "322"}, {"size": 375, "mtime": 1744531393988, "results": "323", "hashOfConfig": "322"}, {"size": 12682, "mtime": 1752000863635, "results": "324", "hashOfConfig": "322"}, {"size": 2728, "mtime": 1752004079776, "results": "325", "hashOfConfig": "322"}, {"size": 1183, "mtime": 1751787344260, "results": "326", "hashOfConfig": "322"}, {"size": 7439, "mtime": 1751975227633, "results": "327", "hashOfConfig": "322"}, {"size": 4828, "mtime": 1746378664900, "results": "328", "hashOfConfig": "322"}, {"size": 2152, "mtime": 1751276563455, "results": "329", "hashOfConfig": "322"}, {"size": 1271, "mtime": 1744531393978, "results": "330", "hashOfConfig": "322"}, {"size": 21516, "mtime": 1751827522381, "results": "331", "hashOfConfig": "322"}, {"size": 11683, "mtime": 1748250288653, "results": "332", "hashOfConfig": "322"}, {"size": 1979, "mtime": 1749722105422, "results": "333", "hashOfConfig": "322"}, {"size": 551, "mtime": 1744531393982, "results": "334", "hashOfConfig": "322"}, {"size": 275, "mtime": 1748215697376, "results": "335", "hashOfConfig": "322"}, {"size": 1739, "mtime": 1749721392840, "results": "336", "hashOfConfig": "322"}, {"size": 44964, "mtime": 1750904385044, "results": "337", "hashOfConfig": "322"}, {"size": 9223, "mtime": 1751743443365, "results": "338", "hashOfConfig": "322"}, {"size": 350, "mtime": 1750322483573, "results": "339", "hashOfConfig": "322"}, {"size": 19813, "mtime": 1750323348863, "results": "340", "hashOfConfig": "322"}, {"size": 3066, "mtime": 1751743443365, "results": "341", "hashOfConfig": "322"}, {"size": 2397, "mtime": 1752003962009, "results": "342", "hashOfConfig": "322"}, {"size": 1898, "mtime": 1750325540002, "results": "343", "hashOfConfig": "322"}, {"size": 348, "mtime": 1749202010110, "results": "344", "hashOfConfig": "322"}, {"size": 3322, "mtime": 1751743523736, "results": "345", "hashOfConfig": "322"}, {"size": 9893, "mtime": 1750849306726, "results": "346", "hashOfConfig": "322"}, {"size": 733, "mtime": 1751439773864, "results": "347", "hashOfConfig": "322"}, {"size": 7352, "mtime": 1751971764594, "results": "348", "hashOfConfig": "322"}, {"size": 3345, "mtime": 1751786228120, "results": "349", "hashOfConfig": "322"}, {"size": 3523, "mtime": 1751745797479, "results": "350", "hashOfConfig": "322"}, {"size": 7806, "mtime": 1750325495112, "results": "351", "hashOfConfig": "322"}, {"size": 17715, "mtime": 1751542931344, "results": "352", "hashOfConfig": "322"}, {"size": 14134, "mtime": 1752003896697, "results": "353", "hashOfConfig": "322"}, {"size": 1380, "mtime": 1744531393975, "results": "354", "hashOfConfig": "322"}, {"size": 2737, "mtime": 1750295163218, "results": "355", "hashOfConfig": "322"}, {"size": 5741, "mtime": 1751849172659, "results": "356", "hashOfConfig": "322"}, {"size": 5921, "mtime": 1751786223198, "results": "357", "hashOfConfig": "322"}, {"size": 2480, "mtime": 1747721626218, "results": "358", "hashOfConfig": "322"}, {"size": 5150, "mtime": 1748398556383, "results": "359", "hashOfConfig": "322"}, {"size": 1337, "mtime": 1747720637516, "results": "360", "hashOfConfig": "322"}, {"size": 690, "mtime": 1744531393950, "results": "361", "hashOfConfig": "322"}, {"size": 1339, "mtime": 1751554069606, "results": "362", "hashOfConfig": "322"}, {"size": 673, "mtime": 1744531393976, "results": "363", "hashOfConfig": "322"}, {"size": 1495, "mtime": 1751362330062, "results": "364", "hashOfConfig": "322"}, {"size": 2276, "mtime": 1751554069606, "results": "365", "hashOfConfig": "322"}, {"size": 1151, "mtime": 1749730250381, "results": "366", "hashOfConfig": "322"}, {"size": 3200, "mtime": 1744531393954, "results": "367", "hashOfConfig": "322"}, {"size": 3255, "mtime": 1752047065950, "results": "368", "hashOfConfig": "322"}, {"size": 635, "mtime": 1744531393961, "results": "369", "hashOfConfig": "322"}, {"size": 1228, "mtime": 1751361437565, "results": "370", "hashOfConfig": "322"}, {"size": 4288, "mtime": 1751977780456, "results": "371", "hashOfConfig": "322"}, {"size": 7391, "mtime": 1751977756680, "results": "372", "hashOfConfig": "322"}, {"size": 1709, "mtime": 1751982367850, "results": "373", "hashOfConfig": "322"}, {"size": 10125, "mtime": 1751977846438, "results": "374", "hashOfConfig": "322"}, {"size": 5752, "mtime": 1751276751884, "results": "375", "hashOfConfig": "322"}, {"size": 28691, "mtime": 1744958278477, "results": "376", "hashOfConfig": "322"}, {"size": 19936, "mtime": 1748984865157, "results": "377", "hashOfConfig": "322"}, {"size": 5905, "mtime": 1751977734164, "results": "378", "hashOfConfig": "322"}, {"size": 8616, "mtime": 1751977877203, "results": "379", "hashOfConfig": "322"}, {"size": 911, "mtime": 1744531393943, "results": "380", "hashOfConfig": "322"}, {"size": 3767, "mtime": 1752059450460, "results": "381", "hashOfConfig": "322"}, {"size": 31136, "mtime": 1751782971202, "results": "382", "hashOfConfig": "322"}, {"size": 9670, "mtime": 1752000747881, "results": "383", "hashOfConfig": "322"}, {"size": 1574, "mtime": 1744531393960, "results": "384", "hashOfConfig": "322"}, {"size": 7401, "mtime": 1747223318342, "results": "385", "hashOfConfig": "322"}, {"size": 2914, "mtime": 1747223318342, "results": "386", "hashOfConfig": "322"}, {"size": 28398, "mtime": 1749425223637, "results": "387", "hashOfConfig": "322"}, {"size": 17584, "mtime": 1750758162845, "results": "388", "hashOfConfig": "322"}, {"size": 19291, "mtime": 1751845914738, "results": "389", "hashOfConfig": "322"}, {"size": 1734, "mtime": 1744531393948, "results": "390", "hashOfConfig": "322"}, {"size": 56278, "mtime": 1751372627190, "results": "391", "hashOfConfig": "322"}, {"size": 6857, "mtime": 1751977816258, "results": "392", "hashOfConfig": "322"}, {"size": 5637, "mtime": 1747254491214, "results": "393", "hashOfConfig": "322"}, {"size": 21475, "mtime": 1750325928413, "results": "394", "hashOfConfig": "322"}, {"size": 10256, "mtime": 1744531393939, "results": "395", "hashOfConfig": "322"}, {"size": 1875, "mtime": 1750960485529, "results": "396", "hashOfConfig": "322"}, {"size": 19265, "mtime": 1751742523955, "results": "397", "hashOfConfig": "322"}, {"size": 6162, "mtime": 1748250288622, "results": "398", "hashOfConfig": "322"}, {"size": 3022, "mtime": 1747719253353, "results": "399", "hashOfConfig": "322"}, {"size": 7915, "mtime": 1748392811401, "results": "400", "hashOfConfig": "322"}, {"size": 6118, "mtime": 1751977901342, "results": "401", "hashOfConfig": "322"}, {"size": 3490, "mtime": 1749748056906, "results": "402", "hashOfConfig": "322"}, {"size": 935, "mtime": 1745405710864, "results": "403", "hashOfConfig": "322"}, {"size": 949, "mtime": 1747223318316, "results": "404", "hashOfConfig": "322"}, {"size": 3267, "mtime": 1747354362354, "results": "405", "hashOfConfig": "322"}, {"size": 3005, "mtime": 1750849360884, "results": "406", "hashOfConfig": "322"}, {"size": 4276, "mtime": 1751828500143, "results": "407", "hashOfConfig": "322"}, {"size": 2380, "mtime": 1744531393947, "results": "408", "hashOfConfig": "322"}, {"size": 2201, "mtime": 1744531393951, "results": "409", "hashOfConfig": "322"}, {"size": 14316, "mtime": 1749895693266, "results": "410", "hashOfConfig": "322"}, {"size": 1990, "mtime": 1744531393948, "results": "411", "hashOfConfig": "322"}, {"size": 841, "mtime": 1748984865154, "results": "412", "hashOfConfig": "322"}, {"size": 5565, "mtime": 1745690690469, "results": "413", "hashOfConfig": "322"}, {"size": 2295, "mtime": 1747223318353, "results": "414", "hashOfConfig": "322"}, {"size": 3146, "mtime": 1744531393940, "results": "415", "hashOfConfig": "322"}, {"size": 4074, "mtime": 1747223318326, "results": "416", "hashOfConfig": "322"}, {"size": 2787, "mtime": 1750173366905, "results": "417", "hashOfConfig": "322"}, {"size": 10634, "mtime": 1745483150534, "results": "418", "hashOfConfig": "419"}, {"size": 3707, "mtime": 1749722678323, "results": "420", "hashOfConfig": "322"}, {"size": 6034, "mtime": 1748364026312, "results": "421", "hashOfConfig": "322"}, {"size": 4147, "mtime": 1749731674278, "results": "422", "hashOfConfig": "322"}, {"size": 822, "mtime": 1751276455614, "results": "423", "hashOfConfig": "322"}, {"size": 4040, "mtime": 1750767145503, "results": "424", "hashOfConfig": "322"}, {"size": 297, "mtime": 1744531393989, "results": "425", "hashOfConfig": "322"}, {"size": 313, "mtime": 1744531393940, "results": "426", "hashOfConfig": "322"}, {"size": 4755, "mtime": 1751305471358, "results": "427", "hashOfConfig": "322"}, {"size": 1946, "mtime": 1751971622958, "results": "428", "hashOfConfig": "322"}, {"size": 993, "mtime": 1747223318349, "results": "429", "hashOfConfig": "322"}, {"size": 475, "mtime": 1748984865156, "results": "430", "hashOfConfig": "322"}, {"size": 902, "mtime": 1747223318353, "results": "431", "hashOfConfig": "322"}, {"size": 3053, "mtime": 1744531393946, "results": "432", "hashOfConfig": "322"}, {"size": 49554, "mtime": 1750767145500, "results": "433", "hashOfConfig": "322"}, {"size": 3402, "mtime": 1748781512174, "results": "434", "hashOfConfig": "322"}, {"size": 4872, "mtime": 1747223318349, "results": "435", "hashOfConfig": "322"}, {"size": 1641, "mtime": 1751827507517, "results": "436", "hashOfConfig": "322"}, {"size": 2297, "mtime": 1744531393945, "results": "437", "hashOfConfig": "322"}, {"size": 2193, "mtime": 1747223318349, "results": "438", "hashOfConfig": "322"}, {"size": 1359, "mtime": 1747223318349, "results": "439", "hashOfConfig": "322"}, {"size": 826, "mtime": 1748398318309, "results": "440", "hashOfConfig": "322"}, {"size": 2301, "mtime": 1751847942058, "results": "441", "hashOfConfig": "322"}, {"size": 1050, "mtime": 1751740167834, "results": "442", "hashOfConfig": "322"}, {"size": 4921, "mtime": 1747223318325, "results": "443", "hashOfConfig": "322"}, {"size": 12221, "mtime": 1751982174489, "results": "444", "hashOfConfig": "322"}, {"size": 411, "mtime": 1744531393940, "results": "445", "hashOfConfig": "322"}, {"size": 2290, "mtime": 1744531393963, "results": "446", "hashOfConfig": "322"}, {"size": 1219, "mtime": 1747467640276, "results": "447", "hashOfConfig": "322"}, {"size": 2003, "mtime": 1744531393970, "results": "448", "hashOfConfig": "322"}, {"size": 2166, "mtime": 1744531393969, "results": "449", "hashOfConfig": "322"}, {"size": 22383, "mtime": 1752066014351, "results": "450", "hashOfConfig": "322"}, {"size": 8372, "mtime": 1752047784140, "results": "451", "hashOfConfig": "322"}, {"size": 3094, "mtime": 1744531393970, "results": "452", "hashOfConfig": "322"}, {"size": 10222, "mtime": 1752047795777, "results": "453", "hashOfConfig": "322"}, {"size": 503, "mtime": 1744531393949, "results": "454", "hashOfConfig": "322"}, {"size": 394, "mtime": 1752000448524, "results": "455", "hashOfConfig": "322"}, {"size": 7876, "mtime": 1747223318342, "results": "456", "hashOfConfig": "322"}, {"size": 4922, "mtime": 1748329867180, "results": "457", "hashOfConfig": "322"}, {"size": 19965, "mtime": 1750900200879, "results": "458", "hashOfConfig": "322"}, {"size": 20555, "mtime": 1748250288625, "results": "459", "hashOfConfig": "322"}, {"size": 1337, "mtime": 1744531393967, "results": "460", "hashOfConfig": "419"}, {"size": 5412, "mtime": 1747223318349, "results": "461", "hashOfConfig": "322"}, {"size": 2938, "mtime": 1747223318353, "results": "462", "hashOfConfig": "322"}, {"size": 3182, "mtime": 1747223318353, "results": "463", "hashOfConfig": "322"}, {"size": 2928, "mtime": 1747223318349, "results": "464", "hashOfConfig": "322"}, {"size": 1885, "mtime": 1747354661883, "results": "465", "hashOfConfig": "322"}, {"size": 1345, "mtime": 1749697625937, "results": "466", "hashOfConfig": "322"}, {"size": 4099, "mtime": 1749731407409, "results": "467", "hashOfConfig": "322"}, {"size": 1576, "mtime": 1751811065450, "results": "468", "hashOfConfig": "322"}, {"size": 6380, "mtime": 1747361017417, "results": "469", "hashOfConfig": "322"}, {"size": 2600, "mtime": 1748876218633, "results": "470", "hashOfConfig": "322"}, {"size": 11541, "mtime": 1750900175948, "results": "471", "hashOfConfig": "322"}, {"size": 3297, "mtime": 1744531393957, "results": "472", "hashOfConfig": "322"}, {"size": 31011, "mtime": 1750902784209, "results": "473", "hashOfConfig": "322"}, {"size": 14565, "mtime": 1750819864802, "results": "474", "hashOfConfig": "322"}, {"size": 1205, "mtime": 1748984865161, "results": "475", "hashOfConfig": "322"}, {"size": 12148, "mtime": 1748250288636, "results": "476", "hashOfConfig": "322"}, {"size": 7688, "mtime": 1747223318312, "results": "477", "hashOfConfig": "322"}, {"size": 8344, "mtime": 1745507778499, "results": "478", "hashOfConfig": "322"}, {"size": 8025, "mtime": 1745507836095, "results": "479", "hashOfConfig": "322"}, {"size": 6988, "mtime": 1747223318344, "results": "480", "hashOfConfig": "322"}, {"size": 7719, "mtime": 1745499803667, "results": "481", "hashOfConfig": "322"}, {"size": 8378, "mtime": 1747223318344, "results": "482", "hashOfConfig": "322"}, {"size": 7254, "mtime": 1747223318344, "results": "483", "hashOfConfig": "322"}, {"size": 1770, "mtime": 1751850036982, "results": "484", "hashOfConfig": "322"}, {"size": 11464, "mtime": 1745500164258, "results": "485", "hashOfConfig": "322"}, {"size": 4650, "mtime": 1745508333678, "results": "486", "hashOfConfig": "322"}, {"size": 13822, "mtime": 1748250288625, "results": "487", "hashOfConfig": "322"}, {"size": 8599, "mtime": 1747223318326, "results": "488", "hashOfConfig": "322"}, {"size": 9774, "mtime": 1747223318326, "results": "489", "hashOfConfig": "322"}, {"size": 7914, "mtime": 1747223318326, "results": "490", "hashOfConfig": "322"}, {"size": 10728, "mtime": 1749548057374, "results": "491", "hashOfConfig": "322"}, {"size": 18582, "mtime": 1749747601919, "results": "492", "hashOfConfig": "322"}, {"size": 8954, "mtime": 1749810779685, "results": "493", "hashOfConfig": "322"}, {"size": 3429, "mtime": 1745682027607, "results": "494", "hashOfConfig": "322"}, {"size": 2298, "mtime": 1749802834779, "results": "495", "hashOfConfig": "322"}, {"size": 823, "mtime": 1748779533260, "results": "496", "hashOfConfig": "322"}, {"size": 1380, "mtime": 1745682047729, "results": "497", "hashOfConfig": "322"}, {"size": 1145, "mtime": 1747902858133, "results": "498", "hashOfConfig": "322"}, {"size": 1118, "mtime": 1745682069045, "results": "499", "hashOfConfig": "322"}, {"size": 978, "mtime": 1747902858130, "results": "500", "hashOfConfig": "322"}, {"size": 1781, "mtime": 1745682059432, "results": "501", "hashOfConfig": "419"}, {"size": 10563, "mtime": 1748585335221, "results": "502", "hashOfConfig": "322"}, {"size": 3574, "mtime": 1746378664904, "results": "503", "hashOfConfig": "322"}, {"size": 4389, "mtime": 1749980511142, "results": "504", "hashOfConfig": "322"}, {"size": 4914, "mtime": 1750326838086, "results": "505", "hashOfConfig": "322"}, {"size": 6418, "mtime": 1750326903479, "results": "506", "hashOfConfig": "322"}, {"size": 1020, "mtime": 1745682406063, "results": "507", "hashOfConfig": "322"}, {"size": 2723, "mtime": 1749810234131, "results": "508", "hashOfConfig": "322"}, {"size": 6116, "mtime": 1746378664905, "results": "509", "hashOfConfig": "322"}, {"size": 1565, "mtime": 1747223318344, "results": "510", "hashOfConfig": "322"}, {"size": 1624, "mtime": 1745689590880, "results": "511", "hashOfConfig": "322"}, {"size": 45152, "mtime": 1750295514405, "results": "512", "hashOfConfig": "322"}, {"size": 70430, "mtime": 1748984865165, "results": "513", "hashOfConfig": "322"}, {"size": 9519, "mtime": 1747354195259, "results": "514", "hashOfConfig": "322"}, {"size": 7408, "mtime": 1749894301204, "results": "515", "hashOfConfig": "322"}, {"size": 4905, "mtime": 1747354192845, "results": "516", "hashOfConfig": "322"}, {"size": 60115, "mtime": 1750947214848, "results": "517", "hashOfConfig": "322"}, {"size": 26487, "mtime": 1748278828957, "results": "518", "hashOfConfig": "322"}, {"size": 24147, "mtime": 1747354834297, "results": "519", "hashOfConfig": "322"}, {"size": 23053, "mtime": 1749730024729, "results": "520", "hashOfConfig": "322"}, {"size": 37412, "mtime": 1750487313341, "results": "521", "hashOfConfig": "322"}, {"size": 17071, "mtime": 1751543017144, "results": "522", "hashOfConfig": "322"}, {"size": 8060, "mtime": 1747223318310, "results": "523", "hashOfConfig": "322"}, {"size": 16767, "mtime": 1748876218633, "results": "524", "hashOfConfig": "322"}, {"size": 3160, "mtime": 1745731138150, "results": "525", "hashOfConfig": "322"}, {"size": 7136, "mtime": 1747223318325, "results": "526", "hashOfConfig": "322"}, {"size": 20572, "mtime": 1751978048828, "results": "527", "hashOfConfig": "322"}, {"size": 2129, "mtime": 1746378664905, "results": "528", "hashOfConfig": "322"}, {"size": 955, "mtime": 1746378664898, "results": "529", "hashOfConfig": "322"}, {"size": 1184, "mtime": 1746378664905, "results": "530", "hashOfConfig": "322"}, {"size": 13378, "mtime": 1748984865159, "results": "531", "hashOfConfig": "322"}, {"size": 1099, "mtime": 1748326442261, "results": "532", "hashOfConfig": "322"}, {"size": 15897, "mtime": 1749894770618, "results": "533", "hashOfConfig": "322"}, {"size": 11963, "mtime": 1750323324025, "results": "534", "hashOfConfig": "322"}, {"size": 12224, "mtime": 1748220515100, "results": "535", "hashOfConfig": "322"}, {"size": 10534, "mtime": 1748220627343, "results": "536", "hashOfConfig": "322"}, {"size": 4031, "mtime": 1747278525096, "results": "537", "hashOfConfig": "322"}, {"size": 2036, "mtime": 1747283717643, "results": "538", "hashOfConfig": "322"}, {"size": 61133, "mtime": 1751834903269, "results": "539", "hashOfConfig": "322"}, {"size": 3589, "mtime": 1747355350828, "results": "540", "hashOfConfig": "322"}, {"size": 7509, "mtime": 1747353152130, "results": "541", "hashOfConfig": "322"}, {"size": 6153, "mtime": 1747354063004, "results": "542", "hashOfConfig": "322"}, {"size": 16791, "mtime": 1748473697636, "results": "543", "hashOfConfig": "322"}, {"size": 12245, "mtime": 1750386603287, "results": "544", "hashOfConfig": "322"}, {"size": 13612, "mtime": 1750175198005, "results": "545", "hashOfConfig": "322"}, {"size": 73138, "mtime": 1750487274356, "results": "546", "hashOfConfig": "322"}, {"size": 5589, "mtime": 1750239106405, "results": "547", "hashOfConfig": "322"}, {"size": 9836, "mtime": 1750175833314, "results": "548", "hashOfConfig": "322"}, {"size": 7964, "mtime": 1750400179899, "results": "549", "hashOfConfig": "322"}, {"size": 4152, "mtime": 1749202010110, "results": "550", "hashOfConfig": "322"}, {"size": 4762, "mtime": 1748513292659, "results": "551", "hashOfConfig": "322"}, {"size": 2443, "mtime": 1747719362467, "results": "552", "hashOfConfig": "322"}, {"size": 16049, "mtime": 1751785268325, "results": "553", "hashOfConfig": "322"}, {"size": 2002, "mtime": 1751785248620, "results": "554", "hashOfConfig": "322"}, {"size": 55806, "mtime": 1751975191453, "results": "555", "hashOfConfig": "322"}, {"size": 16095, "mtime": 1751785298468, "results": "556", "hashOfConfig": "322"}, {"size": 8222, "mtime": 1748250288630, "results": "557", "hashOfConfig": "322"}, {"size": 11210, "mtime": 1748223444732, "results": "558", "hashOfConfig": "322"}, {"size": 41991, "mtime": 1751785335077, "results": "559", "hashOfConfig": "322"}, {"size": 8067, "mtime": 1750764769591, "results": "560", "hashOfConfig": "322"}, {"size": 28083, "mtime": 1748250288657, "results": "561", "hashOfConfig": "322"}, {"size": 29543, "mtime": 1748984865164, "results": "562", "hashOfConfig": "322"}, {"size": 36682, "mtime": 1748250768337, "results": "563", "hashOfConfig": "322"}, {"size": 1999, "mtime": 1750764523294, "results": "564", "hashOfConfig": "322"}, {"size": 9569, "mtime": 1749694485776, "results": "565", "hashOfConfig": "322"}, {"size": 13102, "mtime": 1748250288647, "results": "566", "hashOfConfig": "322"}, {"size": 16077, "mtime": 1748365756504, "results": "567", "hashOfConfig": "322"}, {"size": 3987, "mtime": 1748709335425, "results": "568", "hashOfConfig": "322"}, {"size": 3539, "mtime": 1748800991826, "results": "569", "hashOfConfig": "322"}, {"size": 1712, "mtime": 1748800656400, "results": "570", "hashOfConfig": "322"}, {"size": 1983, "mtime": 1751010202512, "results": "571", "hashOfConfig": "322"}, {"size": 3908, "mtime": 1748801325319, "results": "572", "hashOfConfig": "322"}, {"size": 839, "mtime": 1748801505979, "results": "573", "hashOfConfig": "322"}, {"size": 365, "mtime": 1748984865153, "results": "574", "hashOfConfig": "322"}, {"size": 1850, "mtime": 1752060590013, "results": "575", "hashOfConfig": "322"}, {"size": 8860, "mtime": 1749202010110, "results": "576", "hashOfConfig": "322"}, {"size": 5114, "mtime": 1750951954100, "results": "577", "hashOfConfig": "322"}, {"size": 8088, "mtime": 1751977681640, "results": "578", "hashOfConfig": "322"}, {"size": 4151, "mtime": 1751716037997, "results": "579", "hashOfConfig": "322"}, {"size": 958, "mtime": 1750951934365, "results": "580", "hashOfConfig": "322"}, {"size": 2836, "mtime": 1749722547776, "results": "581", "hashOfConfig": "322"}, {"size": 913, "mtime": 1751742948436, "results": "582", "hashOfConfig": "322"}, {"size": 388, "mtime": 1749720429395, "results": "583", "hashOfConfig": "322"}, {"size": 579, "mtime": 1749731593347, "results": "584", "hashOfConfig": "322"}, {"size": 1948, "mtime": 1751543127045, "results": "585", "hashOfConfig": "322"}, {"size": 10543, "mtime": 1750295311557, "results": "586", "hashOfConfig": "322"}, {"size": 1664, "mtime": 1749808930419, "results": "587", "hashOfConfig": "322"}, {"size": 2401, "mtime": 1750389237348, "results": "588", "hashOfConfig": "322"}, {"size": 7582, "mtime": 1750400196241, "results": "589", "hashOfConfig": "322"}, {"size": 3921, "mtime": 1750322224906, "results": "590", "hashOfConfig": "322"}, {"size": 2502, "mtime": 1751969779191, "results": "591", "hashOfConfig": "322"}, {"size": 6803, "mtime": 1751969853518, "results": "592", "hashOfConfig": "322"}, {"size": 200, "mtime": 1750487274358, "results": "593", "hashOfConfig": "322"}, {"size": 4832, "mtime": 1750574009240, "results": "594", "hashOfConfig": "322"}, {"size": 3066, "mtime": 1750766071078, "results": "595", "hashOfConfig": "322"}, {"size": 4807, "mtime": 1750765702772, "results": "596", "hashOfConfig": "322"}, {"size": 74, "mtime": 1748250288649, "results": "597", "hashOfConfig": "322"}, {"size": 4384, "mtime": 1750962443161, "results": "598", "hashOfConfig": "322"}, {"size": 4263, "mtime": 1750960348603, "results": "599", "hashOfConfig": "322"}, {"size": 4331, "mtime": 1750963155580, "results": "600", "hashOfConfig": "322"}, {"size": 3878, "mtime": 1750940043247, "results": "601", "hashOfConfig": "322"}, {"size": 23770, "mtime": 1750963670443, "results": "602", "hashOfConfig": "322"}, {"size": 17942, "mtime": 1750963374804, "results": "603", "hashOfConfig": "322"}, {"size": 22243, "mtime": 1750961198993, "results": "604", "hashOfConfig": "322"}, {"size": 1252, "mtime": 1750941383903, "results": "605", "hashOfConfig": "322"}, {"size": 846, "mtime": 1750944008903, "results": "606", "hashOfConfig": "322"}, {"size": 218, "mtime": 1751349395110, "results": "607", "hashOfConfig": "322"}, {"size": 17515, "mtime": 1751975558805, "results": "608", "hashOfConfig": "322"}, {"size": 1131, "mtime": 1751847779969, "results": "609", "hashOfConfig": "322"}, {"size": 11512, "mtime": 1751835083804, "results": "610", "hashOfConfig": "322"}, {"size": 11356, "mtime": 1751850006546, "results": "611", "hashOfConfig": "322"}, {"size": 449, "mtime": 1751515976272, "results": "612", "hashOfConfig": "322"}, {"size": 2775, "mtime": 1752061651073, "results": "613", "hashOfConfig": "322"}, {"size": 6786, "mtime": 1751849858966, "results": "614", "hashOfConfig": "322"}, {"size": 1127, "mtime": 1751843983335, "results": "615", "hashOfConfig": "322"}, {"size": 16832, "mtime": 1752061505047, "results": "616", "hashOfConfig": "322"}, {"size": 5733, "mtime": 1751847035611, "results": "617", "hashOfConfig": "322"}, {"size": 4358, "mtime": 1751835622757, "results": "618", "hashOfConfig": "322"}, {"size": 759, "mtime": 1751785941157, "results": "619", "hashOfConfig": "322"}, {"size": 4350, "mtime": 1751784560929, "results": "620", "hashOfConfig": "322"}, {"size": 4259, "mtime": 1751742649369, "results": "621", "hashOfConfig": "322"}, {"size": 851, "mtime": 1752000599842, "results": "622", "hashOfConfig": "322"}, {"size": 663, "mtime": 1751977976716, "results": "623", "hashOfConfig": "322"}, {"size": 1337, "mtime": 1751829986814, "results": "624", "hashOfConfig": "322"}, {"size": 5772, "mtime": 1751830627407, "results": "625", "hashOfConfig": "322"}, {"size": 2222, "mtime": 1751829103916, "results": "626", "hashOfConfig": "322"}, {"size": 1702, "mtime": 1751829247534, "results": "627", "hashOfConfig": "322"}, {"size": 4105, "mtime": 1751831884036, "results": "628", "hashOfConfig": "322"}, {"size": 4780, "mtime": 1752063286693, "results": "629", "hashOfConfig": "322"}, {"size": 8061, "mtime": 1751981788773, "results": "630", "hashOfConfig": "322"}, {"size": 6115, "mtime": 1751974570045, "results": "631", "hashOfConfig": "322"}, {"size": 1301, "mtime": 1751974544781, "results": "632", "hashOfConfig": "322"}, {"size": 1565, "mtime": 1751974714234, "results": "633", "hashOfConfig": "322"}, {"size": 5349, "mtime": 1752062161130, "results": "634", "hashOfConfig": "322"}, {"size": 17672, "mtime": 1752062132356, "results": "635", "hashOfConfig": "322"}, {"size": 36222, "mtime": 1752064212977, "results": "636", "hashOfConfig": "322"}, {"size": 5017, "mtime": 1752061979473, "results": "637", "hashOfConfig": "322"}, {"size": 2108, "mtime": 1752049805712, "results": "638", "hashOfConfig": "322"}, {"size": 593, "mtime": 1752051337091, "results": "639", "hashOfConfig": "322"}, {"size": 592, "mtime": 1752053261180, "results": "640", "hashOfConfig": "322"}, {"size": 12882, "mtime": 1752061763745, "results": "641", "hashOfConfig": "322"}, {"size": 4487, "mtime": 1752063476831, "results": "642", "hashOfConfig": "322"}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e07wfn", {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1op6h7j", {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1258", "messages": "1259", "suppressedMessages": "1260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1261", "messages": "1262", "suppressedMessages": "1263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1264", "messages": "1265", "suppressedMessages": "1266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1267", "messages": "1268", "suppressedMessages": "1269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1270", "messages": "1271", "suppressedMessages": "1272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1273", "messages": "1274", "suppressedMessages": "1275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1276", "messages": "1277", "suppressedMessages": "1278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1279", "messages": "1280", "suppressedMessages": "1281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1282", "messages": "1283", "suppressedMessages": "1284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1285", "messages": "1286", "suppressedMessages": "1287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1288", "messages": "1289", "suppressedMessages": "1290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1291", "messages": "1292", "suppressedMessages": "1293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1297", "messages": "1298", "suppressedMessages": "1299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1300", "messages": "1301", "suppressedMessages": "1302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1303", "messages": "1304", "suppressedMessages": "1305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1306", "messages": "1307", "suppressedMessages": "1308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1327", "messages": "1328", "suppressedMessages": "1329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1330", "messages": "1331", "suppressedMessages": "1332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1333", "messages": "1334", "suppressedMessages": "1335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1336", "messages": "1337", "suppressedMessages": "1338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1339", "messages": "1340", "suppressedMessages": "1341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1342", "messages": "1343", "suppressedMessages": "1344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1345", "messages": "1346", "suppressedMessages": "1347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1348", "messages": "1349", "suppressedMessages": "1350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1351", "messages": "1352", "suppressedMessages": "1353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1354", "messages": "1355", "suppressedMessages": "1356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1357", "messages": "1358", "suppressedMessages": "1359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1360", "messages": "1361", "suppressedMessages": "1362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1363", "messages": "1364", "suppressedMessages": "1365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1366", "messages": "1367", "suppressedMessages": "1368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1369", "messages": "1370", "suppressedMessages": "1371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1372", "messages": "1373", "suppressedMessages": "1374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1375", "messages": "1376", "suppressedMessages": "1377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1378", "messages": "1379", "suppressedMessages": "1380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1381", "messages": "1382", "suppressedMessages": "1383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1384", "messages": "1385", "suppressedMessages": "1386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1387", "messages": "1388", "suppressedMessages": "1389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1390", "messages": "1391", "suppressedMessages": "1392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1393", "messages": "1394", "suppressedMessages": "1395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1396", "messages": "1397", "suppressedMessages": "1398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1399", "messages": "1400", "suppressedMessages": "1401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1402", "messages": "1403", "suppressedMessages": "1404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1405", "messages": "1406", "suppressedMessages": "1407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1408", "messages": "1409", "suppressedMessages": "1410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1411", "messages": "1412", "suppressedMessages": "1413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1414", "messages": "1415", "suppressedMessages": "1416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1417", "messages": "1418", "suppressedMessages": "1419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1420", "messages": "1421", "suppressedMessages": "1422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1423", "messages": "1424", "suppressedMessages": "1425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1426", "messages": "1427", "suppressedMessages": "1428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1429", "messages": "1430", "suppressedMessages": "1431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1432", "messages": "1433", "suppressedMessages": "1434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1435", "messages": "1436", "suppressedMessages": "1437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1438", "messages": "1439", "suppressedMessages": "1440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1441", "messages": "1442", "suppressedMessages": "1443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1444", "messages": "1445", "suppressedMessages": "1446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1447", "messages": "1448", "suppressedMessages": "1449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1450", "messages": "1451", "suppressedMessages": "1452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1453", "messages": "1454", "suppressedMessages": "1455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1456", "messages": "1457", "suppressedMessages": "1458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1459", "messages": "1460", "suppressedMessages": "1461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1462", "messages": "1463", "suppressedMessages": "1464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1465", "messages": "1466", "suppressedMessages": "1467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1468", "messages": "1469", "suppressedMessages": "1470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1471", "messages": "1472", "suppressedMessages": "1473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1474", "messages": "1475", "suppressedMessages": "1476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1477", "messages": "1478", "suppressedMessages": "1479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1480", "messages": "1481", "suppressedMessages": "1482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1483", "messages": "1484", "suppressedMessages": "1485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1486", "messages": "1487", "suppressedMessages": "1488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1489", "messages": "1490", "suppressedMessages": "1491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1492", "messages": "1493", "suppressedMessages": "1494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1495", "messages": "1496", "suppressedMessages": "1497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1498", "messages": "1499", "suppressedMessages": "1500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1501", "messages": "1502", "suppressedMessages": "1503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1504", "messages": "1505", "suppressedMessages": "1506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1507", "messages": "1508", "suppressedMessages": "1509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1510", "messages": "1511", "suppressedMessages": "1512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1513", "messages": "1514", "suppressedMessages": "1515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1516", "messages": "1517", "suppressedMessages": "1518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1519", "messages": "1520", "suppressedMessages": "1521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1522", "messages": "1523", "suppressedMessages": "1524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1525", "messages": "1526", "suppressedMessages": "1527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1528", "messages": "1529", "suppressedMessages": "1530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1531", "messages": "1532", "suppressedMessages": "1533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1534", "messages": "1535", "suppressedMessages": "1536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1537", "messages": "1538", "suppressedMessages": "1539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1540", "messages": "1541", "suppressedMessages": "1542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1543", "messages": "1544", "suppressedMessages": "1545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1546", "messages": "1547", "suppressedMessages": "1548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1549", "messages": "1550", "suppressedMessages": "1551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1552", "messages": "1553", "suppressedMessages": "1554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1555", "messages": "1556", "suppressedMessages": "1557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1558", "messages": "1559", "suppressedMessages": "1560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1561", "messages": "1562", "suppressedMessages": "1563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1564", "messages": "1565", "suppressedMessages": "1566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1567", "messages": "1568", "suppressedMessages": "1569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1570", "messages": "1571", "suppressedMessages": "1572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1573", "messages": "1574", "suppressedMessages": "1575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1576", "messages": "1577", "suppressedMessages": "1578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1579", "messages": "1580", "suppressedMessages": "1581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1582", "messages": "1583", "suppressedMessages": "1584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1585", "messages": "1586", "suppressedMessages": "1587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1588", "messages": "1589", "suppressedMessages": "1590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1591", "messages": "1592", "suppressedMessages": "1593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1594", "messages": "1595", "suppressedMessages": "1596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1597", "messages": "1598", "suppressedMessages": "1599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1600", "messages": "1601", "suppressedMessages": "1602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js", ["1603"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx", ["1604", "1605", "1606", "1607", "1608"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx", ["1609", "1610"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx", ["1611"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx", ["1612"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx", ["1613", "1614", "1615", "1616", "1617"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx", ["1618", "1619"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1620", "1621"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1622"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1623", "1624", "1625", "1626", "1627", "1628", "1629"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1630", "1631", "1632", "1633", "1634"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", ["1635"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", ["1649", "1650", "1651", "1652"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1653", "1654"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1663"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js", ["1664"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js", ["1665"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js", ["1666"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx", ["1667"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx", ["1668"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1669"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx", ["1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx", ["1678", "1679"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx", ["1680", "1681", "1682", "1683"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx", ["1684"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx", ["1685"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx", ["1686", "1687", "1688"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx", ["1689", "1690", "1691"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx", ["1692", "1693", "1694"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx", ["1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx", ["1711", "1712"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx", ["1713", "1714", "1715", "1716", "1717"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx", ["1718", "1719", "1720", "1721", "1722", "1723", "1724"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx", ["1725", "1726"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx", ["1727"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx", ["1728"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx", ["1729", "1730", "1731", "1732", "1733"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx", ["1734"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx", ["1735", "1736", "1737", "1738", "1739", "1740"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx", ["1741", "1742", "1743"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx", ["1744"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx", ["1745", "1746", "1747", "1748", "1749", "1750", "1751"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx", ["1752", "1753", "1754", "1755", "1756"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1757", "1758", "1759"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx", ["1760", "1761"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx", ["1762"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx", ["1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx", ["1772"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx", ["1773"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js", ["1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx", ["1792"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx", ["1793"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx", ["1794"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx", ["1795"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx", ["1796"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx", ["1797", "1798", "1799"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx", ["1800"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1801", "1802", "1803", "1804", "1805", "1806"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx", ["1807"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx", ["1808", "1809", "1810", "1811"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx", ["1812", "1813"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx", ["1814", "1815", "1816", "1817"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx", ["1818", "1819"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx", ["1820", "1821", "1822", "1823"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1824"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx", ["1825", "1826"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1827"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx", ["1828", "1829"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx", ["1830"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx", ["1831"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx", ["1832", "1833", "1834", "1835", "1836"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx", ["1837", "1838"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx", ["1839", "1840", "1841"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx", ["1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx", ["1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868", "1869"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx", ["1870", "1871", "1872", "1873"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx", ["1874"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx", ["1875", "1876", "1877", "1878"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx", ["1879", "1880", "1881", "1882", "1883", "1884", "1885"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx", ["1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx", ["1896", "1897", "1898", "1899"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["1900", "1901"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx", ["1902", "1903", "1904", "1905", "1906", "1907", "1908"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx", ["1909", "1910", "1911", "1912", "1913", "1914", "1915"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx", ["1916", "1917", "1918"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js", ["1919"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx", ["1920", "1921", "1922"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx", ["1923", "1924", "1925", "1926"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js", ["1927"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx", ["1928", "1929", "1930"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx", ["1931", "1932", "1933", "1934", "1935", "1936", "1937"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js", ["1938"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["1939", "1940", "1941", "1942"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["1943", "1944", "1945", "1946", "1947", "1948", "1949", "1950", "1951", "1952", "1953", "1954", "1955", "1956", "1957", "1958", "1959", "1960", "1961", "1962", "1963", "1964", "1965", "1966", "1967", "1968", "1969", "1970", "1971", "1972", "1973", "1974"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["1975", "1976", "1977", "1978", "1979", "1980"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx", ["1981"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx", ["1982"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx", ["1983"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js", ["1984", "1985"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx", ["1986", "1987", "1988", "1989", "1990", "1991", "1992", "1993"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", ["1994"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["1995", "1996", "1997", "1998", "1999"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["2000", "2001", "2002"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["2003", "2004"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx", ["2005", "2006"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["2007", "2008", "2009"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx", ["2010"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx", ["2011", "2012", "2013", "2014"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx", ["2015"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx", ["2016"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx", ["2017", "2018", "2019", "2020", "2021", "2022", "2023", "2024"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx", ["2025"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx", ["2026", "2027", "2028", "2029", "2030", "2031", "2032", "2033", "2034", "2035", "2036", "2037"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx", ["2038"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx", ["2039", "2040", "2041"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx", ["2042", "2043"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx", ["2044", "2045"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx", ["2046", "2047"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx", ["2048", "2049", "2050", "2051", "2052", "2053", "2054"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx", ["2055"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx", ["2056"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx", ["2057", "2058"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx", ["2059", "2060", "2061", "2062", "2063", "2064"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx", ["2065", "2066", "2067", "2068", "2069"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx", ["2070", "2071", "2072"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx", ["2073"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\ImageDropZone.jsx", ["2074"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SolutionEditor.jsx", ["2075", "2076"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StaffManagement.jsx", ["2077", "2078", "2079"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\dashboard\\dashboardSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\dashboardApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\layout\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AddExamAdmin.jsx", ["2080", "2081", "2082", "2083", "2084", "2085", "2086", "2087", "2088", "2089", "2090", "2091", "2092", "2093", "2094", "2095", "2096", "2097", "2098", "2099", "2100", "2101", "2102", "2103", "2104", "2105", "2106", "2107"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\RightContent.jsx", ["2108", "2109", "2110", "2111", "2112", "2113"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\LeftContent.jsx", ["2114", "2115", "2116", "2117"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\addExam\\addExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\CompactStepHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\NavigateBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\hooks\\useDebouncedEffect.js", ["2118", "2119"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddImagesModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAddExam\\SolutionEditor.jsx", ["2120", "2121"], [], {"ruleId": "2122", "severity": 1, "message": "2123", "line": 51, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 51, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2126", "line": 4, "column": 23, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2127", "line": 8, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2128", "line": 11, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 11, "endColumn": 21}, {"ruleId": "2129", "severity": 1, "message": "2130", "line": 42, "column": 8, "nodeType": "2131", "endLine": 42, "endColumn": 24, "suggestions": "2132"}, {"ruleId": "2122", "severity": 1, "message": "2133", "line": 49, "column": 15, "nodeType": "2124", "messageId": "2125", "endLine": 49, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2134", "line": 10, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 10, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2135", "line": 18, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 36}, {"ruleId": "2129", "severity": 1, "message": "2136", "line": 26, "column": 8, "nodeType": "2131", "endLine": 26, "endColumn": 19, "suggestions": "2137"}, {"ruleId": "2138", "severity": 1, "message": "2139", "line": 393, "column": 45, "nodeType": "2140", "endLine": 398, "endColumn": 47}, {"ruleId": "2122", "severity": 1, "message": "2141", "line": 6, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 11}, {"ruleId": "2122", "severity": 1, "message": "2142", "line": 6, "column": 27, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 31}, {"ruleId": "2122", "severity": 1, "message": "2143", "line": 7, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2144", "line": 8, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 32}, {"ruleId": "2122", "severity": 1, "message": "2145", "line": 88, "column": 23, "nodeType": "2124", "messageId": "2125", "endLine": 88, "endColumn": 31}, {"ruleId": "2122", "severity": 1, "message": "2146", "line": 14, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2135", "line": 15, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 36}, {"ruleId": "2122", "severity": 1, "message": "2147", "line": 2, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2148", "line": 3, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2149", "line": 5, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 39}, {"ruleId": "2122", "severity": 1, "message": "2150", "line": 15, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2151", "line": 16, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 16, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2152", "line": 24, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 24, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2153", "line": 30, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 30, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2154", "line": 34, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 34, "endColumn": 23}, {"ruleId": "2129", "severity": 1, "message": "2155", "line": 73, "column": 31, "nodeType": "2124", "endLine": 73, "endColumn": 42}, {"ruleId": "2129", "severity": 1, "message": "2156", "line": 269, "column": 8, "nodeType": "2131", "endLine": 269, "endColumn": 18, "suggestions": "2157"}, {"ruleId": "2122", "severity": 1, "message": "2150", "line": 6, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2151", "line": 9, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 9, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2152", "line": 18, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2153", "line": 20, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 20, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2158", "line": 26, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 26}, {"ruleId": "2122", "severity": 1, "message": "2159", "line": 1, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2159", "line": 1, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2160", "line": 8, "column": 37, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 48}, {"ruleId": "2122", "severity": 1, "message": "2161", "line": 11, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 11, "endColumn": 30}, {"ruleId": "2122", "severity": 1, "message": "2162", "line": 22, "column": 18, "nodeType": "2124", "messageId": "2125", "endLine": 22, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2163", "line": 23, "column": 21, "nodeType": "2124", "messageId": "2125", "endLine": 23, "endColumn": 31}, {"ruleId": "2122", "severity": 1, "message": "2164", "line": 24, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 24, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2165", "line": 25, "column": 25, "nodeType": "2124", "messageId": "2125", "endLine": 25, "endColumn": 39}, {"ruleId": "2122", "severity": 1, "message": "2166", "line": 26, "column": 30, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 49}, {"ruleId": "2122", "severity": 1, "message": "2167", "line": 27, "column": 21, "nodeType": "2124", "messageId": "2125", "endLine": 27, "endColumn": 26}, {"ruleId": "2122", "severity": 1, "message": "2168", "line": 27, "column": 41, "nodeType": "2124", "messageId": "2125", "endLine": 27, "endColumn": 51}, {"ruleId": "2122", "severity": 1, "message": "2169", "line": 31, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 31, "endColumn": 30}, {"ruleId": "2122", "severity": 1, "message": "2170", "line": 34, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 34, "endColumn": 35}, {"ruleId": "2122", "severity": 1, "message": "2171", "line": 38, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 38, "endColumn": 33}, {"ruleId": "2122", "severity": 1, "message": "2159", "line": 1, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2169", "line": 17, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 17, "endColumn": 30}, {"ruleId": "2122", "severity": 1, "message": "2170", "line": 20, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 20, "endColumn": 35}, {"ruleId": "2122", "severity": 1, "message": "2172", "line": 23, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 23, "endColumn": 32}, {"ruleId": "2122", "severity": 1, "message": "2173", "line": 15, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2135", "line": 15, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 36}, {"ruleId": "2122", "severity": 1, "message": "2159", "line": 1, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2147", "line": 2, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2174", "line": 6, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2175", "line": 7, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2176", "line": 8, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2169", "line": 25, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 25, "endColumn": 30}, {"ruleId": "2122", "severity": 1, "message": "2171", "line": 28, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 28, "endColumn": 33}, {"ruleId": "2122", "severity": 1, "message": "2172", "line": 31, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 31, "endColumn": 32}, {"ruleId": "2122", "severity": 1, "message": "2159", "line": 1, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2177", "line": 37, "column": 19, "nodeType": "2124", "messageId": "2125", "endLine": 37, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2178", "line": 5, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 17}, {"ruleId": "2179", "severity": 1, "message": "2180", "line": 140, "column": 80, "nodeType": "2181", "messageId": "2182", "endLine": 140, "endColumn": 82}, {"ruleId": "2122", "severity": 1, "message": "2183", "line": 3, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2184", "line": 19, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 19, "endColumn": 22}, {"ruleId": "2129", "severity": 1, "message": "2185", "line": 18, "column": 8, "nodeType": "2131", "endLine": 18, "endColumn": 10, "suggestions": "2186"}, {"ruleId": "2122", "severity": 1, "message": "2187", "line": 1, "column": 20, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 3, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2189", "line": 4, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2190", "line": 12, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 12, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2191", "line": 13, "column": 36, "nodeType": "2124", "messageId": "2125", "endLine": 13, "endColumn": 45}, {"ruleId": "2122", "severity": 1, "message": "2192", "line": 14, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2193", "line": 14, "column": 16, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2194", "line": 29, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 29, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 3, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2195", "line": 5, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 3, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2196", "line": 11, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 11, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2197", "line": 19, "column": 36, "nodeType": "2124", "messageId": "2125", "endLine": 19, "endColumn": 46}, {"ruleId": "2129", "severity": 1, "message": "2198", "line": 44, "column": 8, "nodeType": "2131", "endLine": 44, "endColumn": 26, "suggestions": "2199"}, {"ruleId": "2122", "severity": 1, "message": "2187", "line": 2, "column": 20, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2200", "line": 7, "column": 47, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 68}, {"ruleId": "2122", "severity": 1, "message": "2201", "line": 17, "column": 7, "nodeType": "2124", "messageId": "2125", "endLine": 17, "endColumn": 16}, {"ruleId": "2122", "severity": 1, "message": "2202", "line": 29, "column": 7, "nodeType": "2124", "messageId": "2125", "endLine": 29, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2203", "line": 36, "column": 7, "nodeType": "2124", "messageId": "2125", "endLine": 36, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2196", "line": 6, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2197", "line": 15, "column": 36, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 46}, {"ruleId": "2122", "severity": 1, "message": "2204", "line": 19, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 19, "endColumn": 37}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 5, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2197", "line": 16, "column": 36, "nodeType": "2124", "messageId": "2125", "endLine": 16, "endColumn": 46}, {"ruleId": "2129", "severity": 1, "message": "2205", "line": 35, "column": 8, "nodeType": "2131", "endLine": 35, "endColumn": 53, "suggestions": "2206"}, {"ruleId": "2122", "severity": 1, "message": "2207", "line": 12, "column": 55, "nodeType": "2124", "messageId": "2125", "endLine": 12, "endColumn": 61}, {"ruleId": "2122", "severity": 1, "message": "2208", "line": 12, "column": 77, "nodeType": "2124", "messageId": "2125", "endLine": 12, "endColumn": 82}, {"ruleId": "2122", "severity": 1, "message": "2209", "line": 12, "column": 84, "nodeType": "2124", "messageId": "2125", "endLine": 12, "endColumn": 94}, {"ruleId": "2122", "severity": 1, "message": "2152", "line": 18, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2154", "line": 20, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 20, "endColumn": 23}, {"ruleId": "2129", "severity": 1, "message": "2155", "line": 39, "column": 31, "nodeType": "2124", "endLine": 39, "endColumn": 42}, {"ruleId": "2122", "severity": 1, "message": "2210", "line": 96, "column": 17, "nodeType": "2124", "messageId": "2125", "endLine": 96, "endColumn": 21}, {"ruleId": "2179", "severity": 1, "message": "2180", "line": 412, "column": 68, "nodeType": "2181", "messageId": "2182", "endLine": 412, "endColumn": 70}, {"ruleId": "2122", "severity": 1, "message": "2148", "line": 2, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2149", "line": 7, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 39}, {"ruleId": "2122", "severity": 1, "message": "2211", "line": 25, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 25, "endColumn": 26}, {"ruleId": "2122", "severity": 1, "message": "2212", "line": 26, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 30}, {"ruleId": "2122", "severity": 1, "message": "2213", "line": 48, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 48, "endColumn": 22}, {"ruleId": "2129", "severity": 1, "message": "2214", "line": 69, "column": 8, "nodeType": "2131", "endLine": 69, "endColumn": 30, "suggestions": "2215"}, {"ruleId": "2122", "severity": 1, "message": "2216", "line": 79, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 79, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2217", "line": 87, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 87, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2218", "line": 8, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 28}, {"ruleId": "2129", "severity": 1, "message": "2219", "line": 45, "column": 8, "nodeType": "2131", "endLine": 45, "endColumn": 32, "suggestions": "2220"}, {"ruleId": "2122", "severity": 1, "message": "2221", "line": 6, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2222", "line": 7, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2150", "line": 13, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 13, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2151", "line": 14, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2153", "line": 23, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 23, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2223", "line": 5, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2222", "line": 6, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2218", "line": 7, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 28}, {"ruleId": "2129", "severity": 1, "message": "2224", "line": 57, "column": 8, "nodeType": "2131", "endLine": 57, "endColumn": 28, "suggestions": "2225"}, {"ruleId": "2122", "severity": 1, "message": "2171", "line": 71, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 71, "endColumn": 33}, {"ruleId": "2122", "severity": 1, "message": "2170", "line": 75, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 75, "endColumn": 35}, {"ruleId": "2122", "severity": 1, "message": "2172", "line": 79, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 79, "endColumn": 32}, {"ruleId": "2122", "severity": 1, "message": "2226", "line": 10, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 10, "endColumn": 17}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 290, "column": 59, "nodeType": "2229", "messageId": "2230", "endLine": 290, "endColumn": 60, "suggestions": "2231"}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 3, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2197", "line": 11, "column": 41, "nodeType": "2124", "messageId": "2125", "endLine": 11, "endColumn": 51}, {"ruleId": "2122", "severity": 1, "message": "2232", "line": 1, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 28}, {"ruleId": "2122", "severity": 1, "message": "2233", "line": 2, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2187", "line": 3, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2234", "line": 7, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2235", "line": 12, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 12, "endColumn": 31}, {"ruleId": "2122", "severity": 1, "message": "2236", "line": 1, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2237", "line": 2, "column": 37, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 45}, {"ruleId": "2122", "severity": 1, "message": "2238", "line": 2, "column": 47, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 60}, {"ruleId": "2122", "severity": 1, "message": "2239", "line": 2, "column": 62, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 67}, {"ruleId": "2122", "severity": 1, "message": "2240", "line": 2, "column": 69, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 77}, {"ruleId": "2122", "severity": 1, "message": "2241", "line": 3, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 26}, {"ruleId": "2129", "severity": 1, "message": "2242", "line": 36, "column": 8, "nodeType": "2131", "endLine": 36, "endColumn": 43, "suggestions": "2243"}, {"ruleId": "2122", "severity": 1, "message": "2236", "line": 1, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 5, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2134", "line": 7, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 18}, {"ruleId": "2129", "severity": 1, "message": "2244", "line": 25, "column": 8, "nodeType": "2131", "endLine": 25, "endColumn": 20, "suggestions": "2245"}, {"ruleId": "2122", "severity": 1, "message": "2246", "line": 1, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2247", "line": 32, "column": 83, "nodeType": "2124", "messageId": "2125", "endLine": 32, "endColumn": 91}, {"ruleId": "2122", "severity": 1, "message": "2248", "line": 121, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 121, "endColumn": 23}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 159, "column": 22, "nodeType": "2251", "messageId": "2252", "endLine": 159, "endColumn": 24}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 159, "column": 91, "nodeType": "2251", "messageId": "2252", "endLine": 159, "endColumn": 93}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 304, "column": 40, "nodeType": "2251", "messageId": "2252", "endLine": 304, "endColumn": 42}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 304, "column": 109, "nodeType": "2251", "messageId": "2252", "endLine": 304, "endColumn": 111}, {"ruleId": "2122", "severity": 1, "message": "2253", "line": 14, "column": 38, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 47}, {"ruleId": "2122", "severity": 1, "message": "2254", "line": 14, "column": 49, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 56}, {"ruleId": "2122", "severity": 1, "message": "2255", "line": 14, "column": 58, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 70}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 34, "column": 40, "nodeType": "2251", "messageId": "2252", "endLine": 34, "endColumn": 42}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 34, "column": 109, "nodeType": "2251", "messageId": "2252", "endLine": 34, "endColumn": 111}, {"ruleId": "2122", "severity": 1, "message": "2167", "line": 15, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2256", "line": 15, "column": 44, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 52}, {"ruleId": "2129", "severity": 1, "message": "2257", "line": 42, "column": 8, "nodeType": "2131", "endLine": 42, "endColumn": 40, "suggestions": "2258"}, {"ruleId": "2122", "severity": 1, "message": "2134", "line": 1, "column": 25, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 33}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 4, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2259", "line": 4, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2128", "line": 1, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2126", "line": 2, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2260", "line": 4, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2261", "line": 4, "column": 26, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 44}, {"ruleId": "2122", "severity": 1, "message": "2262", "line": 9, "column": 166, "nodeType": "2124", "messageId": "2125", "endLine": 9, "endColumn": 170}, {"ruleId": "2122", "severity": 1, "message": "2263", "line": 47, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 47, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2264", "line": 48, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 48, "endColumn": 26}, {"ruleId": "2122", "severity": 1, "message": "2265", "line": 50, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 50, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2266", "line": 59, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 59, "endColumn": 16}, {"ruleId": "2129", "severity": 1, "message": "2267", "line": 29, "column": 8, "nodeType": "2131", "endLine": 29, "endColumn": 10, "suggestions": "2268"}, {"ruleId": "2138", "severity": 1, "message": "2139", "line": 44, "column": 29, "nodeType": "2140", "endLine": 44, "endColumn": 98}, {"ruleId": "2227", "severity": 1, "message": "2269", "line": 202, "column": 24, "nodeType": "2229", "messageId": "2230", "endLine": 202, "endColumn": 25, "suggestions": "2270"}, {"ruleId": "2227", "severity": 1, "message": "2271", "line": 202, "column": 26, "nodeType": "2229", "messageId": "2230", "endLine": 202, "endColumn": 27, "suggestions": "2272"}, {"ruleId": "2227", "severity": 1, "message": "2269", "line": 204, "column": 27, "nodeType": "2229", "messageId": "2230", "endLine": 204, "endColumn": 28, "suggestions": "2273"}, {"ruleId": "2227", "severity": 1, "message": "2271", "line": 204, "column": 29, "nodeType": "2229", "messageId": "2230", "endLine": 204, "endColumn": 30, "suggestions": "2274"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 248, "column": 30, "nodeType": "2229", "messageId": "2230", "endLine": 248, "endColumn": 31, "suggestions": "2275"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 267, "column": 61, "nodeType": "2229", "messageId": "2230", "endLine": 267, "endColumn": 62, "suggestions": "2276"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 275, "column": 29, "nodeType": "2229", "messageId": "2230", "endLine": 275, "endColumn": 30, "suggestions": "2277"}, {"ruleId": "2227", "severity": 1, "message": "2278", "line": 275, "column": 31, "nodeType": "2229", "messageId": "2230", "endLine": 275, "endColumn": 32, "suggestions": "2279"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 276, "column": 51, "nodeType": "2229", "messageId": "2230", "endLine": 276, "endColumn": 52, "suggestions": "2280"}, {"ruleId": "2227", "severity": 1, "message": "2278", "line": 276, "column": 53, "nodeType": "2229", "messageId": "2230", "endLine": 276, "endColumn": 54, "suggestions": "2281"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 385, "column": 30, "nodeType": "2229", "messageId": "2230", "endLine": 385, "endColumn": 31, "suggestions": "2282"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 412, "column": 61, "nodeType": "2229", "messageId": "2230", "endLine": 412, "endColumn": 62, "suggestions": "2283"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 420, "column": 29, "nodeType": "2229", "messageId": "2230", "endLine": 420, "endColumn": 30, "suggestions": "2284"}, {"ruleId": "2227", "severity": 1, "message": "2278", "line": 420, "column": 31, "nodeType": "2229", "messageId": "2230", "endLine": 420, "endColumn": 32, "suggestions": "2285"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 421, "column": 51, "nodeType": "2229", "messageId": "2230", "endLine": 421, "endColumn": 52, "suggestions": "2286"}, {"ruleId": "2227", "severity": 1, "message": "2278", "line": 421, "column": 53, "nodeType": "2229", "messageId": "2230", "endLine": 421, "endColumn": 54, "suggestions": "2287"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 520, "column": 30, "nodeType": "2229", "messageId": "2230", "endLine": 520, "endColumn": 31, "suggestions": "2288"}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 544, "column": 61, "nodeType": "2229", "messageId": "2230", "endLine": 544, "endColumn": 62, "suggestions": "2289"}, {"ruleId": "2129", "severity": 1, "message": "2290", "line": 89, "column": 8, "nodeType": "2131", "endLine": 89, "endColumn": 10, "suggestions": "2291"}, {"ruleId": "2138", "severity": 1, "message": "2139", "line": 56, "column": 37, "nodeType": "2140", "endLine": 56, "endColumn": 107}, {"ruleId": "2122", "severity": 1, "message": "2292", "line": 3, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 27}, {"ruleId": "2129", "severity": 1, "message": "2293", "line": 73, "column": 8, "nodeType": "2131", "endLine": 73, "endColumn": 25, "suggestions": "2294"}, {"ruleId": "2122", "severity": 1, "message": "2187", "line": 3, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2295", "line": 5, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2296", "line": 14, "column": 28, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 45}, {"ruleId": "2122", "severity": 1, "message": "2297", "line": 34, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 34, "endColumn": 20}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 116, "column": 56, "nodeType": "2181", "messageId": "2182", "endLine": 116, "endColumn": 58}, {"ruleId": "2122", "severity": 1, "message": "2233", "line": 2, "column": 23, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 34}, {"ruleId": "2122", "severity": 1, "message": "2299", "line": 4, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 28}, {"ruleId": "2122", "severity": 1, "message": "2300", "line": 5, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2301", "line": 5, "column": 33, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 40}, {"ruleId": "2122", "severity": 1, "message": "2302", "line": 5, "column": 42, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 51}, {"ruleId": "2122", "severity": 1, "message": "2303", "line": 8, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 21}, {"ruleId": "2304", "severity": 1, "message": "2305", "line": 12, "column": 25, "nodeType": "2140", "endLine": 12, "endColumn": 614}, {"ruleId": "2122", "severity": 1, "message": "2236", "line": 1, "column": 46, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 49}, {"ruleId": "2122", "severity": 1, "message": "2306", "line": 3, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2307", "line": 15, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2308", "line": 15, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 37}, {"ruleId": "2129", "severity": 1, "message": "2309", "line": 21, "column": 8, "nodeType": "2131", "endLine": 21, "endColumn": 26, "suggestions": "2310"}, {"ruleId": "2129", "severity": 1, "message": "2311", "line": 40, "column": 8, "nodeType": "2131", "endLine": 40, "endColumn": 10, "suggestions": "2312"}, {"ruleId": "2122", "severity": 1, "message": "2141", "line": 2, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 11}, {"ruleId": "2122", "severity": 1, "message": "2148", "line": 3, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2313", "line": 13, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 13, "endColumn": 23}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 290, "column": 56, "nodeType": "2181", "messageId": "2182", "endLine": 290, "endColumn": 58}, {"ruleId": "2122", "severity": 1, "message": "2314", "line": 15, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2315", "line": 19, "column": 22, "nodeType": "2124", "messageId": "2125", "endLine": 19, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2295", "line": 4, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2316", "line": 5, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 16}, {"ruleId": "2122", "severity": 1, "message": "2317", "line": 100, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 100, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2318", "line": 111, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 111, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2190", "line": 15, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2176", "line": 7, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 36}, {"ruleId": "2122", "severity": 1, "message": "2190", "line": 13, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 13, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2190", "line": 14, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2176", "line": 7, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 36}, {"ruleId": "2122", "severity": 1, "message": "2190", "line": 13, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 13, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2176", "line": 7, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 36}, {"ruleId": "2319", "severity": 1, "message": "2320", "line": 28, "column": 74, "nodeType": "2181", "messageId": "2321", "endLine": 28, "endColumn": 75}, {"ruleId": "2122", "severity": 1, "message": "2322", "line": 6, "column": 25, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2323", "line": 6, "column": 31, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 37}, {"ruleId": "2122", "severity": 1, "message": "2209", "line": 6, "column": 39, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 49}, {"ruleId": "2122", "severity": 1, "message": "2239", "line": 6, "column": 51, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 56}, {"ruleId": "2122", "severity": 1, "message": "2324", "line": 6, "column": 65, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 70}, {"ruleId": "2129", "severity": 1, "message": "2325", "line": 50, "column": 8, "nodeType": "2131", "endLine": 50, "endColumn": 28, "suggestions": "2326"}, {"ruleId": "2129", "severity": 1, "message": "2325", "line": 54, "column": 8, "nodeType": "2131", "endLine": 54, "endColumn": 21, "suggestions": "2327"}, {"ruleId": "2122", "severity": 1, "message": "2328", "line": 37, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 37, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2329", "line": 44, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 44, "endColumn": 29}, {"ruleId": "2129", "severity": 1, "message": "2330", "line": 47, "column": 9, "nodeType": "2331", "endLine": 51, "endColumn": 4}, {"ruleId": "2122", "severity": 1, "message": "2332", "line": 16, "column": 45, "nodeType": "2124", "messageId": "2125", "endLine": 16, "endColumn": 57}, {"ruleId": "2129", "severity": 1, "message": "2333", "line": 55, "column": 8, "nodeType": "2131", "endLine": 55, "endColumn": 14, "suggestions": "2334"}, {"ruleId": "2129", "severity": 1, "message": "2335", "line": 109, "column": 11, "nodeType": "2331", "endLine": 115, "endColumn": 6, "suggestions": "2336"}, {"ruleId": "2129", "severity": 1, "message": "2337", "line": 109, "column": 11, "nodeType": "2331", "endLine": 115, "endColumn": 6, "suggestions": "2338"}, {"ruleId": "2129", "severity": 1, "message": "2339", "line": 109, "column": 11, "nodeType": "2331", "endLine": 115, "endColumn": 6, "suggestions": "2340"}, {"ruleId": "2122", "severity": 1, "message": "2341", "line": 117, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 117, "endColumn": 27}, {"ruleId": "2129", "severity": 1, "message": "2342", "line": 232, "column": 8, "nodeType": "2131", "endLine": 232, "endColumn": 23, "suggestions": "2343"}, {"ruleId": "2122", "severity": 1, "message": "2344", "line": 283, "column": 19, "nodeType": "2124", "messageId": "2125", "endLine": 283, "endColumn": 25}, {"ruleId": "2129", "severity": 1, "message": "2155", "line": 371, "column": 37, "nodeType": "2124", "endLine": 371, "endColumn": 48}, {"ruleId": "2129", "severity": 1, "message": "2155", "line": 398, "column": 37, "nodeType": "2124", "endLine": 398, "endColumn": 48}, {"ruleId": "2345", "severity": 1, "message": "2346", "line": 629, "column": 60, "nodeType": "2347", "messageId": "2348", "endLine": 629, "endColumn": 61}, {"ruleId": "2129", "severity": 1, "message": "2349", "line": 785, "column": 8, "nodeType": "2131", "endLine": 785, "endColumn": 99, "suggestions": "2350"}, {"ruleId": "2129", "severity": 1, "message": "2351", "line": 893, "column": 8, "nodeType": "2131", "endLine": 893, "endColumn": 36, "suggestions": "2352"}, {"ruleId": "2122", "severity": 1, "message": "2353", "line": 2, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2354", "line": 4, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2355", "line": 18, "column": 25, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2356", "line": 18, "column": 137, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 146}, {"ruleId": "2122", "severity": 1, "message": "2357", "line": 18, "column": 148, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 161}, {"ruleId": "2122", "severity": 1, "message": "2358", "line": 18, "column": 163, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 173}, {"ruleId": "2122", "severity": 1, "message": "2359", "line": 18, "column": 175, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 182}, {"ruleId": "2122", "severity": 1, "message": "2360", "line": 18, "column": 184, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 189}, {"ruleId": "2122", "severity": 1, "message": "2361", "line": 18, "column": 191, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 205}, {"ruleId": "2122", "severity": 1, "message": "2362", "line": 34, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 34, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2363", "line": 39, "column": 7, "nodeType": "2124", "messageId": "2125", "endLine": 39, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2364", "line": 40, "column": 7, "nodeType": "2124", "messageId": "2125", "endLine": 40, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2365", "line": 64, "column": 7, "nodeType": "2124", "messageId": "2125", "endLine": 64, "endColumn": 17}, {"ruleId": "2122", "severity": 1, "message": "2366", "line": 65, "column": 7, "nodeType": "2124", "messageId": "2125", "endLine": 65, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2367", "line": 67, "column": 7, "nodeType": "2124", "messageId": "2125", "endLine": 67, "endColumn": 17}, {"ruleId": "2122", "severity": 1, "message": "2236", "line": 4, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2160", "line": 6, "column": 35, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 46}, {"ruleId": "2122", "severity": 1, "message": "2368", "line": 10, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 10, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2197", "line": 26, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 21}, {"ruleId": "2129", "severity": 1, "message": "2333", "line": 37, "column": 8, "nodeType": "2131", "endLine": 37, "endColumn": 14, "suggestions": "2369"}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 15, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2370", "line": 18, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2141", "line": 21, "column": 98, "nodeType": "2124", "messageId": "2125", "endLine": 21, "endColumn": 99}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 262, "column": 38, "nodeType": "2181", "messageId": "2182", "endLine": 262, "endColumn": 40}, {"ruleId": "2122", "severity": 1, "message": "2151", "line": 7, "column": 111, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 115}, {"ruleId": "2122", "severity": 1, "message": "2371", "line": 7, "column": 117, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 127}, {"ruleId": "2122", "severity": 1, "message": "2246", "line": 10, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 10, "endColumn": 24}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 178, "column": 123, "nodeType": "2251", "messageId": "2252", "endLine": 178, "endColumn": 125}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 178, "column": 197, "nodeType": "2251", "messageId": "2252", "endLine": 178, "endColumn": 199}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 187, "column": 120, "nodeType": "2251", "messageId": "2252", "endLine": 187, "endColumn": 122}, {"ruleId": "2249", "severity": 1, "message": "2250", "line": 187, "column": 189, "nodeType": "2251", "messageId": "2252", "endLine": 187, "endColumn": 191}, {"ruleId": "2122", "severity": 1, "message": "2237", "line": 15, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2207", "line": 17, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 17, "endColumn": 11}, {"ruleId": "2122", "severity": 1, "message": "2151", "line": 20, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 20, "endColumn": 9}, {"ruleId": "2122", "severity": 1, "message": "2372", "line": 21, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 21, "endColumn": 17}, {"ruleId": "2122", "severity": 1, "message": "2208", "line": 24, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 24, "endColumn": 10}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 28, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 28, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2373", "line": 30, "column": 7, "nodeType": "2124", "messageId": "2125", "endLine": 30, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2374", "line": 159, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 159, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2375", "line": 160, "column": 21, "nodeType": "2124", "messageId": "2125", "endLine": 160, "endColumn": 31}, {"ruleId": "2129", "severity": 1, "message": "2376", "line": 166, "column": 8, "nodeType": "2131", "endLine": 166, "endColumn": 18, "suggestions": "2377"}, {"ruleId": "2122", "severity": 1, "message": "2378", "line": 15, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 16}, {"ruleId": "2122", "severity": 1, "message": "2379", "line": 16, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 16, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2380", "line": 26, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 18}, {"ruleId": "2129", "severity": 1, "message": "2381", "line": 199, "column": 8, "nodeType": "2131", "endLine": 199, "endColumn": 21, "suggestions": "2382"}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 49, "column": 37, "nodeType": "2181", "messageId": "2182", "endLine": 49, "endColumn": 39}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 51, "column": 44, "nodeType": "2181", "messageId": "2182", "endLine": 51, "endColumn": 46}, {"ruleId": "2122", "severity": 1, "message": "2383", "line": 8, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 16}, {"ruleId": "2122", "severity": 1, "message": "2141", "line": 8, "column": 18, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2240", "line": 8, "column": 21, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2384", "line": 15, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 17}, {"ruleId": "2122", "severity": 1, "message": "2385", "line": 16, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 16, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2386", "line": 26, "column": 24, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 37}, {"ruleId": "2122", "severity": 1, "message": "2387", "line": 111, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 111, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 7, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2151", "line": 13, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 13, "endColumn": 9}, {"ruleId": "2122", "severity": 1, "message": "2237", "line": 17, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 17, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2388", "line": 19, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 19, "endColumn": 13}, {"ruleId": "2129", "severity": 1, "message": "2389", "line": 129, "column": 8, "nodeType": "2131", "endLine": 129, "endColumn": 18, "suggestions": "2390"}, {"ruleId": "2122", "severity": 1, "message": "2391", "line": 155, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 155, "endColumn": 32}, {"ruleId": "2122", "severity": 1, "message": "2392", "line": 232, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 232, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 7, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 22}, {"ruleId": "2129", "severity": 1, "message": "2393", "line": 40, "column": 8, "nodeType": "2131", "endLine": 40, "endColumn": 30, "suggestions": "2394"}, {"ruleId": "2138", "severity": 1, "message": "2139", "line": 223, "column": 69, "nodeType": "2140", "endLine": 227, "endColumn": 71}, {"ruleId": "2179", "severity": 1, "message": "2180", "line": 49, "column": 108, "nodeType": "2181", "messageId": "2182", "endLine": 49, "endColumn": 110}, {"ruleId": "2122", "severity": 1, "message": "2167", "line": 55, "column": 42, "nodeType": "2124", "messageId": "2125", "endLine": 55, "endColumn": 47}, {"ruleId": "2129", "severity": 1, "message": "2325", "line": 88, "column": 8, "nodeType": "2131", "endLine": 88, "endColumn": 28, "suggestions": "2395"}, {"ruleId": "2129", "severity": 1, "message": "2325", "line": 92, "column": 8, "nodeType": "2131", "endLine": 92, "endColumn": 21, "suggestions": "2396"}, {"ruleId": "2122", "severity": 1, "message": "2397", "line": 7, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2398", "line": 8, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 12}, {"ruleId": "2122", "severity": 1, "message": "2399", "line": 13, "column": 3, "nodeType": "2124", "messageId": "2125", "endLine": 13, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2296", "line": 14, "column": 3, "nodeType": "2124", "messageId": "2125", "endLine": 14, "endColumn": 20}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 285, "column": 65, "nodeType": "2181", "messageId": "2182", "endLine": 285, "endColumn": 67}, {"ruleId": "2122", "severity": 1, "message": "2400", "line": 28, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 28, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2209", "line": 29, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 29, "endColumn": 15}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 447, "column": 101, "nodeType": "2181", "messageId": "2182", "endLine": 447, "endColumn": 103}, {"ruleId": "2122", "severity": 1, "message": "2401", "line": 55, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 55, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2402", "line": 55, "column": 32, "nodeType": "2124", "messageId": "2125", "endLine": 55, "endColumn": 42}, {"ruleId": "2122", "severity": 1, "message": "2403", "line": 59, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 59, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2404", "line": 70, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 70, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2405", "line": 82, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 82, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2406", "line": 88, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 88, "endColumn": 30}, {"ruleId": "2122", "severity": 1, "message": "2391", "line": 94, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 94, "endColumn": 32}, {"ruleId": "2179", "severity": 1, "message": "2180", "line": 336, "column": 35, "nodeType": "2181", "messageId": "2182", "endLine": 336, "endColumn": 37}, {"ruleId": "2122", "severity": 1, "message": "2196", "line": 9, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 9, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2407", "line": 11, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 11, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2207", "line": 19, "column": 3, "nodeType": "2124", "messageId": "2125", "endLine": 19, "endColumn": 9}, {"ruleId": "2122", "severity": 1, "message": "2197", "line": 31, "column": 30, "nodeType": "2124", "messageId": "2125", "endLine": 31, "endColumn": 40}, {"ruleId": "2122", "severity": 1, "message": "2408", "line": 4, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2409", "line": 5, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 14}, {"ruleId": "2122", "severity": 1, "message": "2410", "line": 17, "column": 26, "nodeType": "2124", "messageId": "2125", "endLine": 17, "endColumn": 46}, {"ruleId": "2122", "severity": 1, "message": "2411", "line": 24, "column": 96, "nodeType": "2124", "messageId": "2125", "endLine": 24, "endColumn": 100}, {"ruleId": "2122", "severity": 1, "message": "2207", "line": 24, "column": 102, "nodeType": "2124", "messageId": "2125", "endLine": 24, "endColumn": 108}, {"ruleId": "2122", "severity": 1, "message": "2147", "line": 26, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2412", "line": 27, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 27, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2152", "line": 34, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 34, "endColumn": 17}, {"ruleId": "2122", "severity": 1, "message": "2413", "line": 35, "column": 47, "nodeType": "2124", "messageId": "2125", "endLine": 35, "endColumn": 54}, {"ruleId": "2122", "severity": 1, "message": "2197", "line": 37, "column": 17, "nodeType": "2124", "messageId": "2125", "endLine": 37, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2414", "line": 91, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 91, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2415", "line": 92, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 92, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2416", "line": 98, "column": 20, "nodeType": "2124", "messageId": "2125", "endLine": 98, "endColumn": 31}, {"ruleId": "2122", "severity": 1, "message": "2417", "line": 99, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 99, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2418", "line": 99, "column": 22, "nodeType": "2124", "messageId": "2125", "endLine": 99, "endColumn": 35}, {"ruleId": "2122", "severity": 1, "message": "2419", "line": 100, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 100, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2420", "line": 100, "column": 20, "nodeType": "2124", "messageId": "2125", "endLine": 100, "endColumn": 31}, {"ruleId": "2122", "severity": 1, "message": "2421", "line": 103, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 103, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2422", "line": 104, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 104, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2423", "line": 105, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 105, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2424", "line": 106, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 106, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2425", "line": 136, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 136, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2426", "line": 137, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 137, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2427", "line": 139, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 139, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2428", "line": 139, "column": 25, "nodeType": "2124", "messageId": "2125", "endLine": 139, "endColumn": 41}, {"ruleId": "2122", "severity": 1, "message": "2429", "line": 167, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 167, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2430", "line": 173, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 173, "endColumn": 33}, {"ruleId": "2129", "severity": 1, "message": "2431", "line": 286, "column": 6, "nodeType": "2131", "endLine": 286, "endColumn": 32, "suggestions": "2432"}, {"ruleId": "2122", "severity": 1, "message": "2433", "line": 364, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 364, "endColumn": 23}, {"ruleId": "2129", "severity": 1, "message": "2434", "line": 742, "column": 6, "nodeType": "2131", "endLine": 742, "endColumn": 25, "suggestions": "2435"}, {"ruleId": "2122", "severity": 1, "message": "2436", "line": 764, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 764, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2437", "line": 776, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 776, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2407", "line": 8, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2208", "line": 19, "column": 3, "nodeType": "2124", "messageId": "2125", "endLine": 19, "endColumn": 8}, {"ruleId": "2122", "severity": 1, "message": "2438", "line": 20, "column": 3, "nodeType": "2124", "messageId": "2125", "endLine": 20, "endColumn": 10}, {"ruleId": "2122", "severity": 1, "message": "2439", "line": 30, "column": 36, "nodeType": "2124", "messageId": "2125", "endLine": 30, "endColumn": 56}, {"ruleId": "2122", "severity": 1, "message": "2440", "line": 32, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 32, "endColumn": 30}, {"ruleId": "2122", "severity": 1, "message": "2441", "line": 32, "column": 32, "nodeType": "2124", "messageId": "2125", "endLine": 32, "endColumn": 55}, {"ruleId": "2122", "severity": 1, "message": "2442", "line": 2, "column": 26, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 32}, {"ruleId": "2129", "severity": 1, "message": "2155", "line": 35, "column": 29, "nodeType": "2124", "endLine": 35, "endColumn": 40}, {"ruleId": "2129", "severity": 1, "message": "2155", "line": 35, "column": 30, "nodeType": "2124", "endLine": 35, "endColumn": 41}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 347, "column": 68, "nodeType": "2181", "messageId": "2182", "endLine": 347, "endColumn": 70}, {"ruleId": "2179", "severity": 1, "message": "2180", "line": 360, "column": 64, "nodeType": "2181", "messageId": "2182", "endLine": 360, "endColumn": 66}, {"ruleId": "2122", "severity": 1, "message": "2236", "line": 1, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2443", "line": 12, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 12, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2150", "line": 24, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 24, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2151", "line": 26, "column": 119, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 123}, {"ruleId": "2122", "severity": 1, "message": "2444", "line": 26, "column": 135, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 140}, {"ruleId": "2122", "severity": 1, "message": "2152", "line": 99, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 99, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2197", "line": 107, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 107, "endColumn": 19}, {"ruleId": "2129", "severity": 1, "message": "2445", "line": 217, "column": 8, "nodeType": "2131", "endLine": 217, "endColumn": 38, "suggestions": "2446"}, {"ruleId": "2129", "severity": 1, "message": "2447", "line": 67, "column": 8, "nodeType": "2131", "endLine": 67, "endColumn": 47, "suggestions": "2448"}, {"ruleId": "2122", "severity": 1, "message": "2159", "line": 4, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2207", "line": 17, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 17, "endColumn": 11}, {"ruleId": "2122", "severity": 1, "message": "2142", "line": 20, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 20, "endColumn": 9}, {"ruleId": "2122", "severity": 1, "message": "2449", "line": 27, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 27, "endColumn": 32}, {"ruleId": "2129", "severity": 1, "message": "2450", "line": 122, "column": 8, "nodeType": "2131", "endLine": 122, "endColumn": 10, "suggestions": "2451"}, {"ruleId": "2122", "severity": 1, "message": "2240", "line": 6, "column": 56, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 64}, {"ruleId": "2122", "severity": 1, "message": "2323", "line": 6, "column": 73, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 79}, {"ruleId": "2122", "severity": 1, "message": "2209", "line": 6, "column": 81, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 91}, {"ruleId": "2122", "severity": 1, "message": "2452", "line": 5, "column": 60, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 66}, {"ruleId": "2122", "severity": 1, "message": "2383", "line": 5, "column": 68, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 74}, {"ruleId": "2122", "severity": 1, "message": "2453", "line": 2, "column": 42, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 51}, {"ruleId": "2122", "severity": 1, "message": "2454", "line": 5, "column": 11, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 8, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2413", "line": 16, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 16, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2455", "line": 17, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 17, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2134", "line": 1, "column": 21, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2456", "line": 3, "column": 58, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 69}, {"ruleId": "2122", "severity": 1, "message": "2457", "line": 3, "column": 71, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 81}, {"ruleId": "2122", "severity": 1, "message": "2458", "line": 48, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 48, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2459", "line": 56, "column": 9, "nodeType": "2124", "messageId": "2125", "endLine": 56, "endColumn": 29}, {"ruleId": "2129", "severity": 1, "message": "2155", "line": 37, "column": 30, "nodeType": "2124", "endLine": 37, "endColumn": 41}, {"ruleId": "2129", "severity": 1, "message": "2155", "line": 35, "column": 32, "nodeType": "2124", "endLine": 35, "endColumn": 43}, {"ruleId": "2122", "severity": 1, "message": "2240", "line": 3, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2208", "line": 3, "column": 20, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2460", "line": 3, "column": 27, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 38}, {"ruleId": "2122", "severity": 1, "message": "2259", "line": 3, "column": 40, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 52}, {"ruleId": "2122", "severity": 1, "message": "2456", "line": 3, "column": 54, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 65}, {"ruleId": "2122", "severity": 1, "message": "2461", "line": 6, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 32}, {"ruleId": "2122", "severity": 1, "message": "2462", "line": 8, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2463", "line": 8, "column": 25, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 39}, {"ruleId": "2122", "severity": 1, "message": "2134", "line": 2, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2240", "line": 3, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2460", "line": 3, "column": 27, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 38}, {"ruleId": "2122", "severity": 1, "message": "2259", "line": 3, "column": 40, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 52}, {"ruleId": "2122", "severity": 1, "message": "2380", "line": 4, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2464", "line": 5, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 30}, {"ruleId": "2122", "severity": 1, "message": "2465", "line": 7, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2462", "line": 8, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2466", "line": 9, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 9, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2413", "line": 15, "column": 39, "nodeType": "2124", "messageId": "2125", "endLine": 15, "endColumn": 46}, {"ruleId": "2122", "severity": 1, "message": "2313", "line": 17, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 17, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2467", "line": 18, "column": 37, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 48}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 181, "column": 54, "nodeType": "2181", "messageId": "2182", "endLine": 181, "endColumn": 56}, {"ruleId": "2129", "severity": 1, "message": "2468", "line": 57, "column": 8, "nodeType": "2131", "endLine": 57, "endColumn": 38, "suggestions": "2469"}, {"ruleId": "2122", "severity": 1, "message": "2236", "line": 1, "column": 17, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2470", "line": 2, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 26}, {"ruleId": "2179", "severity": 1, "message": "2298", "line": 186, "column": 50, "nodeType": "2181", "messageId": "2182", "endLine": 186, "endColumn": 52}, {"ruleId": "2122", "severity": 1, "message": "2471", "line": 79, "column": 29, "nodeType": "2124", "messageId": "2125", "endLine": 79, "endColumn": 45}, {"ruleId": "2122", "severity": 1, "message": "2472", "line": 79, "column": 47, "nodeType": "2124", "messageId": "2125", "endLine": 79, "endColumn": 59}, {"ruleId": "2122", "severity": 1, "message": "2460", "line": 1, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 21}, {"ruleId": "2129", "severity": 1, "message": "2473", "line": 51, "column": 8, "nodeType": "2131", "endLine": 51, "endColumn": 10, "suggestions": "2474"}, {"ruleId": "2122", "severity": 1, "message": "2134", "line": 1, "column": 17, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 25}, {"ruleId": "2129", "severity": 1, "message": "2475", "line": 33, "column": 8, "nodeType": "2131", "endLine": 33, "endColumn": 17, "suggestions": "2476"}, {"ruleId": "2122", "severity": 1, "message": "2134", "line": 6, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 6, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2477", "line": 7, "column": 46, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 56}, {"ruleId": "2122", "severity": 1, "message": "2478", "line": 7, "column": 72, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 82}, {"ruleId": "2122", "severity": 1, "message": "2479", "line": 8, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2480", "line": 66, "column": 107, "nodeType": "2124", "messageId": "2125", "endLine": 66, "endColumn": 114}, {"ruleId": "2129", "severity": 1, "message": "2481", "line": 73, "column": 8, "nodeType": "2131", "endLine": 73, "endColumn": 36, "suggestions": "2482"}, {"ruleId": "2129", "severity": 1, "message": "2481", "line": 78, "column": 8, "nodeType": "2131", "endLine": 78, "endColumn": 26, "suggestions": "2483"}, {"ruleId": "2122", "severity": 1, "message": "2484", "line": 7, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2235", "line": 18, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 31}, {"ruleId": "2122", "severity": 1, "message": "2235", "line": 5, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 5, "endColumn": 31}, {"ruleId": "2122", "severity": 1, "message": "2485", "line": 8, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 16}, {"ruleId": "2122", "severity": 1, "message": "2486", "line": 3, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 20}, {"ruleId": "2122", "severity": 1, "message": "2487", "line": 3, "column": 22, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 29}, {"ruleId": "2122", "severity": 1, "message": "2488", "line": 3, "column": 31, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 38}, {"ruleId": "2122", "severity": 1, "message": "2489", "line": 3, "column": 40, "nodeType": "2124", "messageId": "2125", "endLine": 3, "endColumn": 50}, {"ruleId": "2122", "severity": 1, "message": "2490", "line": 7, "column": 12, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 17}, {"ruleId": "2122", "severity": 1, "message": "2491", "line": 7, "column": 19, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2134", "line": 1, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2236", "line": 1, "column": 31, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 34}, {"ruleId": "2122", "severity": 1, "message": "2492", "line": 75, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 75, "endColumn": 28}, {"ruleId": "2122", "severity": 1, "message": "2413", "line": 75, "column": 30, "nodeType": "2124", "messageId": "2125", "endLine": 75, "endColumn": 37}, {"ruleId": "2122", "severity": 1, "message": "2402", "line": 75, "column": 39, "nodeType": "2124", "messageId": "2125", "endLine": 75, "endColumn": 49}, {"ruleId": "2122", "severity": 1, "message": "2191", "line": 13, "column": 45, "nodeType": "2124", "messageId": "2125", "endLine": 13, "endColumn": 54}, {"ruleId": "2122", "severity": 1, "message": "2493", "line": 66, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 66, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2168", "line": 67, "column": 26, "nodeType": "2124", "messageId": "2125", "endLine": 67, "endColumn": 36}, {"ruleId": "2122", "severity": 1, "message": "2485", "line": 20, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 20, "endColumn": 16}, {"ruleId": "2138", "severity": 1, "message": "2139", "line": 77, "column": 21, "nodeType": "2140", "endLine": 81, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2187", "line": 1, "column": 35, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 44}, {"ruleId": "2122", "severity": 1, "message": "2494", "line": 2, "column": 21, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 27}, {"ruleId": "2122", "severity": 1, "message": "2495", "line": 11, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 11, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2191", "line": 96, "column": 29, "nodeType": "2124", "messageId": "2125", "endLine": 96, "endColumn": 38}, {"ruleId": "2122", "severity": 1, "message": "2496", "line": 166, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 166, "endColumn": 18}, {"ruleId": "2122", "severity": 1, "message": "2134", "line": 1, "column": 17, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 25}, {"ruleId": "2122", "severity": 1, "message": "2497", "line": 7, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 17}, {"ruleId": "2122", "severity": 1, "message": "2498", "line": 7, "column": 19, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 30}, {"ruleId": "2122", "severity": 1, "message": "2499", "line": 7, "column": 32, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 40}, {"ruleId": "2122", "severity": 1, "message": "2500", "line": 7, "column": 42, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 50}, {"ruleId": "2122", "severity": 1, "message": "2501", "line": 7, "column": 52, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 60}, {"ruleId": "2122", "severity": 1, "message": "2222", "line": 8, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 24}, {"ruleId": "2122", "severity": 1, "message": "2218", "line": 9, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 9, "endColumn": 28}, {"ruleId": "2122", "severity": 1, "message": "2502", "line": 10, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 10, "endColumn": 19}, {"ruleId": "2122", "severity": 1, "message": "2503", "line": 11, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 11, "endColumn": 17}, {"ruleId": "2122", "severity": 1, "message": "2223", "line": 12, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 12, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2188", "line": 13, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 13, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2142", "line": 17, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 17, "endColumn": 9}, {"ruleId": "2122", "severity": 1, "message": "2504", "line": 18, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 18, "endColumn": 8}, {"ruleId": "2122", "severity": 1, "message": "2505", "line": 19, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 19, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2506", "line": 20, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 20, "endColumn": 9}, {"ruleId": "2122", "severity": 1, "message": "2485", "line": 21, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 21, "endColumn": 11}, {"ruleId": "2122", "severity": 1, "message": "2507", "line": 22, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 22, "endColumn": 9}, {"ruleId": "2122", "severity": 1, "message": "2456", "line": 23, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 23, "endColumn": 16}, {"ruleId": "2122", "severity": 1, "message": "2259", "line": 24, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 24, "endColumn": 17}, {"ruleId": "2122", "severity": 1, "message": "2460", "line": 25, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 25, "endColumn": 16}, {"ruleId": "2122", "severity": 1, "message": "2208", "line": 26, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 26, "endColumn": 10}, {"ruleId": "2122", "severity": 1, "message": "2239", "line": 27, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 27, "endColumn": 10}, {"ruleId": "2122", "severity": 1, "message": "2237", "line": 28, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 28, "endColumn": 13}, {"ruleId": "2122", "severity": 1, "message": "2508", "line": 29, "column": 14, "nodeType": "2124", "messageId": "2125", "endLine": 29, "endColumn": 23}, {"ruleId": "2122", "severity": 1, "message": "2494", "line": 30, "column": 5, "nodeType": "2124", "messageId": "2125", "endLine": 30, "endColumn": 11}, {"ruleId": "2129", "severity": 1, "message": "2509", "line": 49, "column": 8, "nodeType": "2131", "endLine": 49, "endColumn": 18, "suggestions": "2510"}, {"ruleId": "2122", "severity": 1, "message": "2146", "line": 51, "column": 123, "nodeType": "2124", "messageId": "2125", "endLine": 51, "endColumn": 132}, {"ruleId": "2122", "severity": 1, "message": "2511", "line": 7, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 7, "endColumn": 26}, {"ruleId": "2122", "severity": 1, "message": "2512", "line": 8, "column": 8, "nodeType": "2124", "messageId": "2125", "endLine": 8, "endColumn": 26}, {"ruleId": "2122", "severity": 1, "message": "2513", "line": 9, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 9, "endColumn": 22}, {"ruleId": "2122", "severity": 1, "message": "2514", "line": 97, "column": 23, "nodeType": "2124", "messageId": "2125", "endLine": 97, "endColumn": 32}, {"ruleId": "2122", "severity": 1, "message": "2515", "line": 97, "column": 34, "nodeType": "2124", "messageId": "2125", "endLine": 97, "endColumn": 42}, {"ruleId": "2122", "severity": 1, "message": "2516", "line": 332, "column": 13, "nodeType": "2124", "messageId": "2125", "endLine": 332, "endColumn": 21}, {"ruleId": "2122", "severity": 1, "message": "2517", "line": 4, "column": 10, "nodeType": "2124", "messageId": "2125", "endLine": 4, "endColumn": 26}, {"ruleId": "2122", "severity": 1, "message": "2515", "line": 34, "column": 34, "nodeType": "2124", "messageId": "2125", "endLine": 34, "endColumn": 42}, {"ruleId": "2129", "severity": 1, "message": "2518", "line": 379, "column": 8, "nodeType": "2131", "endLine": 379, "endColumn": 118, "suggestions": "2519"}, {"ruleId": "2138", "severity": 1, "message": "2139", "line": 522, "column": 21, "nodeType": "2140", "endLine": 526, "endColumn": 23}, {"ruleId": "2129", "severity": 1, "message": "2520", "line": 17, "column": 8, "nodeType": "2131", "endLine": 17, "endColumn": 24, "suggestions": "2521"}, {"ruleId": "2129", "severity": 1, "message": "2522", "line": 17, "column": 9, "nodeType": "2523", "endLine": 17, "endColumn": 16}, {"ruleId": "2122", "severity": 1, "message": "2187", "line": 1, "column": 35, "nodeType": "2124", "messageId": "2125", "endLine": 1, "endColumn": 44}, {"ruleId": "2122", "severity": 1, "message": "2494", "line": 2, "column": 21, "nodeType": "2124", "messageId": "2125", "endLine": 2, "endColumn": 27}, "no-unused-vars", "'AdminUserSearchPage' is defined but never used.", "Identifier", "unusedVar", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'BeeMathLogo' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.username'. Either include it or remove the dependency array.", "ArrayExpression", ["2524"], "'resultAction' is assigned a value but never used.", "'useState' is defined but never used.", "'isFilterVIew' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", ["2525"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'setIsFilterView' is defined but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "'loadingExam' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2526"], "'showAddStudent' is assigned a value but never used.", "'AdminLayout' is defined but never used.", "'setAttempts' is defined but never used.", "'ScoreDistributionChart' is defined but never used.", "'setLogs' is assigned a value but never used.", "'setAnswers' is assigned a value but never used.", "'prevRanks' is assigned a value but never used.", "'setRankChanges' is assigned a value but never used.", "'setUpdatedAttemptId' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'handleClickedDetail' is assigned a value but never used.", "'handleClickedPreviewExam' is assigned a value but never used.", "'handleClickedQuestions' is assigned a value but never used.", "'handleClickedTracking' is assigned a value but never used.", "'isAddView' is assigned a value but never used.", "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'errorMsg' is assigned a value but never used.", "'setExam' is defined but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'backgroundImage' is defined but never used.", "'shouldFloat' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2527"], "'useEffect' is defined but never used.", "'LoadingSpinner' is defined but never used.", "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'setSortOrder' is defined but never used.", "'resetFilters' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2528"], "'processInputForUpdate' is defined but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'setDeleteMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2529"], "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2530"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2531"], "'setClass' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "'LatexRenderer' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2532"], "'compose' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["2533", "2534"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'use' is defined but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2535"], "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2536"], "'ExamDefaultImage' is defined but never used.", "'imageUrl' is assigned a value but never used.", "'ActionButton' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2537"], "'ChevronRight' is defined but never used.", "'toggleDropdown' is defined but never used.", "'toggleExamDropdown' is defined but never used.", "'User' is defined but never used.", "'dropdown' is assigned a value but never used.", "'tuitionDropdown' is assigned a value but never used.", "'examDropdown' is assigned a value but never used.", "'icon5' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2538"], "Unnecessary escape character: \\}.", ["2539", "2540"], "Unnecessary escape character: \\{.", ["2541", "2542"], ["2543", "2544"], ["2545", "2546"], ["2547", "2548"], ["2549", "2550"], ["2551", "2552"], "Unnecessary escape character: \\).", ["2553", "2554"], ["2555", "2556"], ["2557", "2558"], ["2559", "2560"], ["2561", "2562"], ["2563", "2564"], ["2565", "2566"], ["2567", "2568"], ["2569", "2570"], ["2571", "2572"], ["2573", "2574"], "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2575"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2576"], "'InputSearch' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'menuOpen' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'toggleCloseSidebar' is defined but never used.", "'ArrowRight' is defined but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "'isHovered' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2577"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2578"], "'currentTime' is assigned a value but never used.", "'setSuccess' is defined but never used.", "'success' is assigned a value but never used.", "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchExams' and 'isClassroomExam'. Either include them or remove the dependency array.", ["2579"], ["2580"], "'isMobile' is assigned a value but never used.", "'currentQuestionType' is assigned a value but never used.", "The 'allQuestions' array makes the dependencies of useEffect Hook (at line 160) change on every render. To fix this, wrap the initialization of 'allQuestions' in its own useMemo() Hook.", "VariableDeclarator", "'isFullscreen' is defined but never used.", "React Hook useEffect has missing dependencies: 'examId' and 'navigate'. Either include them or remove the dependency array.", ["2581"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 668) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2582"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 679) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2583"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 691) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2584"], "'addErrorQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'flag'. Either include it or remove the dependency array.", ["2585"], "'result' is assigned a value but never used.", "no-sequences", "Unexpected use of comma operator.", "SequenceExpression", "unexpectedCommaExpression", "React Hook useEffect has a missing dependency: 'handleAutoSubmit'. Either include it or remove the dependency array.", ["2586"], "React Hook useEffect has missing dependencies: 'dispatch', 'exam?.isCheatingCheckEnabled', 'user.firstName', and 'user.lastName'. Either include them or remove the dependency array.", ["2587"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'ScoreBarChart' is defined but never used.", ["2588"], "'NoDataFound' is defined but never used.", "'BookMarked' is defined but never used.", "'ExternalLink' is defined but never used.", "'ButtonSidebar' is assigned a value but never used.", "'choice' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2589"], "'ChevronDown' is defined but never used.", "'ChevronUp' is defined but never used.", "'UserLayout' is defined but never used.", "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2590"], "'Filter' is defined but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2591"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2592"], ["2593"], ["2594"], "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'BarChart2' is defined but never used.", "'articles' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'formatNumberWithDots' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'loading' is assigned a value but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterGraduationYear', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2595"], "'handleBatchAdd' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tuitionPayments'. Either include it or remove the dependency array.", ["2596"], "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'Receipt' is defined but never used.", "'studentClassTuitions' is assigned a value but never used.", "'classTuitionsLoading' is assigned a value but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2597"], "React Hook useEffect has a missing dependency: 'user?.id'. Either include it or remove the dependency array.", ["2598"], "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2599"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", "'CheckCircle' is defined but never used.", "'DollarSign' is defined but never used.", "'getStatusBadge' is assigned a value but never used.", "'getPaymentStatusIcon' is assigned a value but never used.", "'ChevronLeft' is defined but never used.", "'getLearningItemWeekend' is defined but never used.", "'resetCalendar' is defined but never used.", "'setSelectedDay' is defined but never used.", "'fetchClassesOverview' is defined but never used.", "'CalendarMonth' is defined but never used.", "'SidebarCalendar' is defined but never used.", "'selectedDay' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'firstGridDate' and 'lastGridDate'. Either include them or remove the dependency array.", ["2600"], "'NavigateTimeButton' is defined but never used.", "'recentActivities' is assigned a value but never used.", "'systemStatus' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'onClose'. Either include it or remove the dependency array. If 'onClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2601"], "React Hook useEffect has missing dependencies: 'dispatch' and 'exam.id'. Either include them or remove the dependency array.", ["2602"], "'commitExam' is defined but never used.", "'setConfirm' is defined but never used.", "'ConfirmModal' is defined but never used.", "'confirm' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'isChange'. Either include them or remove the dependency array.", ["2603"], ["2604"], "'AddImagesModal' is defined but never used.", "'Trash2' is defined but never used.", "'TableAdmin' is defined but never used.", "'TdAdmin' is defined but never used.", "'ThAdmin' is defined but never used.", "'TheadAdmin' is defined but never used.", "'month' is assigned a value but never used.", "'setMonth' is assigned a value but never used.", "'tuitionPayments' is assigned a value but never used.", "'userAttempts' is assigned a value but never used.", "'Upload' is defined but never used.", "'Pagination' is defined but never used.", "'staff' is assigned a value but never used.", "'setStep' is defined but never used.", "'setExamData' is defined but never used.", "'postExam' is defined but never used.", "'nextStep' is defined but never used.", "'prevStep' is defined but never used.", "'ImageUpload' is defined but never used.", "'UploadPdf' is defined but never used.", "'Eye' is defined but never used.", "'FileText' is defined but never used.", "'Plus' is defined but never used.", "'Edit' is defined but never used.", "'ImageIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'folder'. Either include it or remove the dependency array.", ["2605"], "'questionUntil' is defined but never used.", "'useDebouncedEffect' is defined but never used.", "'setQuestions' is defined but never used.", "'examImage' is assigned a value but never used.", "'examFile' is assigned a value but never used.", "'examData' is assigned a value but never used.", "'fetchCodesByType' is defined but never used.", "React Hook useEffect has a missing dependency: 'isViewAdd'. Either include it or remove the dependency array.", ["2606"], "React Hook useEffect has a missing dependency: 'effect'. Either include it or remove the dependency array. If 'effect' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2607"], "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", {"desc": "2608", "fix": "2609"}, {"desc": "2610", "fix": "2611"}, {"desc": "2612", "fix": "2613"}, {"desc": "2614", "fix": "2615"}, {"desc": "2616", "fix": "2617"}, {"desc": "2618", "fix": "2619"}, {"desc": "2620", "fix": "2621"}, {"desc": "2622", "fix": "2623"}, {"desc": "2624", "fix": "2625"}, {"messageId": "2626", "fix": "2627", "desc": "2628"}, {"messageId": "2629", "fix": "2630", "desc": "2631"}, {"desc": "2632", "fix": "2633"}, {"desc": "2634", "fix": "2635"}, {"desc": "2636", "fix": "2637"}, {"desc": "2638", "fix": "2639"}, {"messageId": "2626", "fix": "2640", "desc": "2628"}, {"messageId": "2629", "fix": "2641", "desc": "2631"}, {"messageId": "2626", "fix": "2642", "desc": "2628"}, {"messageId": "2629", "fix": "2643", "desc": "2631"}, {"messageId": "2626", "fix": "2644", "desc": "2628"}, {"messageId": "2629", "fix": "2645", "desc": "2631"}, {"messageId": "2626", "fix": "2646", "desc": "2628"}, {"messageId": "2629", "fix": "2647", "desc": "2631"}, {"messageId": "2626", "fix": "2648", "desc": "2628"}, {"messageId": "2629", "fix": "2649", "desc": "2631"}, {"messageId": "2626", "fix": "2650", "desc": "2628"}, {"messageId": "2629", "fix": "2651", "desc": "2631"}, {"messageId": "2626", "fix": "2652", "desc": "2628"}, {"messageId": "2629", "fix": "2653", "desc": "2631"}, {"messageId": "2626", "fix": "2654", "desc": "2628"}, {"messageId": "2629", "fix": "2655", "desc": "2631"}, {"messageId": "2626", "fix": "2656", "desc": "2628"}, {"messageId": "2629", "fix": "2657", "desc": "2631"}, {"messageId": "2626", "fix": "2658", "desc": "2628"}, {"messageId": "2629", "fix": "2659", "desc": "2631"}, {"messageId": "2626", "fix": "2660", "desc": "2628"}, {"messageId": "2629", "fix": "2661", "desc": "2631"}, {"messageId": "2626", "fix": "2662", "desc": "2628"}, {"messageId": "2629", "fix": "2663", "desc": "2631"}, {"messageId": "2626", "fix": "2664", "desc": "2628"}, {"messageId": "2629", "fix": "2665", "desc": "2631"}, {"messageId": "2626", "fix": "2666", "desc": "2628"}, {"messageId": "2629", "fix": "2667", "desc": "2631"}, {"messageId": "2626", "fix": "2668", "desc": "2628"}, {"messageId": "2629", "fix": "2669", "desc": "2631"}, {"messageId": "2626", "fix": "2670", "desc": "2628"}, {"messageId": "2629", "fix": "2671", "desc": "2631"}, {"messageId": "2626", "fix": "2672", "desc": "2628"}, {"messageId": "2629", "fix": "2673", "desc": "2631"}, {"messageId": "2626", "fix": "2674", "desc": "2628"}, {"messageId": "2629", "fix": "2675", "desc": "2631"}, {"desc": "2676", "fix": "2677"}, {"desc": "2678", "fix": "2679"}, {"desc": "2680", "fix": "2681"}, {"desc": "2682", "fix": "2683"}, {"desc": "2684", "fix": "2685"}, {"desc": "2686", "fix": "2687"}, {"desc": "2688", "fix": "2689"}, {"desc": "2690", "fix": "2691"}, {"desc": "2690", "fix": "2692"}, {"desc": "2690", "fix": "2693"}, {"desc": "2694", "fix": "2695"}, {"desc": "2696", "fix": "2697"}, {"desc": "2698", "fix": "2699"}, {"desc": "2688", "fix": "2700"}, {"desc": "2701", "fix": "2702"}, {"desc": "2703", "fix": "2704"}, {"desc": "2705", "fix": "2706"}, {"desc": "2707", "fix": "2708"}, {"desc": "2684", "fix": "2709"}, {"desc": "2686", "fix": "2710"}, {"desc": "2711", "fix": "2712"}, {"desc": "2713", "fix": "2714"}, {"desc": "2715", "fix": "2716"}, {"desc": "2717", "fix": "2718"}, {"desc": "2719", "fix": "2720"}, {"desc": "2721", "fix": "2722"}, {"desc": "2723", "fix": "2724"}, {"desc": "2725", "fix": "2726"}, {"desc": "2727", "fix": "2728"}, {"desc": "2729", "fix": "2730"}, {"desc": "2731", "fix": "2732"}, {"desc": "2733", "fix": "2734"}, {"desc": "2735", "fix": "2736"}, "Update the dependencies array to be: [user, navigate, formData.username]", {"range": "2737", "text": "2738"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "2739", "text": "2740"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "2741", "text": "2742"}, "Update the dependencies array to be: [options, selected, type]", {"range": "2743", "text": "2744"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "2745", "text": "2746"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "2747", "text": "2748"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "2749", "text": "2750"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "2751", "text": "2752"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "2753", "text": "2754"}, "removeEscape", {"range": "2755", "text": "2756"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2757", "text": "2758"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "2759", "text": "2760"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "2761", "text": "2762"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "2763", "text": "2764"}, "Update the dependencies array to be: [maxLength]", {"range": "2765", "text": "2766"}, {"range": "2767", "text": "2756"}, {"range": "2768", "text": "2758"}, {"range": "2769", "text": "2756"}, {"range": "2770", "text": "2758"}, {"range": "2771", "text": "2756"}, {"range": "2772", "text": "2758"}, {"range": "2773", "text": "2756"}, {"range": "2774", "text": "2758"}, {"range": "2775", "text": "2756"}, {"range": "2776", "text": "2758"}, {"range": "2777", "text": "2756"}, {"range": "2778", "text": "2758"}, {"range": "2779", "text": "2756"}, {"range": "2780", "text": "2758"}, {"range": "2781", "text": "2756"}, {"range": "2782", "text": "2758"}, {"range": "2783", "text": "2756"}, {"range": "2784", "text": "2758"}, {"range": "2785", "text": "2756"}, {"range": "2786", "text": "2758"}, {"range": "2787", "text": "2756"}, {"range": "2788", "text": "2758"}, {"range": "2789", "text": "2756"}, {"range": "2790", "text": "2758"}, {"range": "2791", "text": "2756"}, {"range": "2792", "text": "2758"}, {"range": "2793", "text": "2756"}, {"range": "2794", "text": "2758"}, {"range": "2795", "text": "2756"}, {"range": "2796", "text": "2758"}, {"range": "2797", "text": "2756"}, {"range": "2798", "text": "2758"}, {"range": "2799", "text": "2756"}, {"range": "2800", "text": "2758"}, {"range": "2801", "text": "2756"}, {"range": "2802", "text": "2758"}, "Update the dependencies array to be: [handlePaste]", {"range": "2803", "text": "2804"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "2805", "text": "2806"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "2807", "text": "2808"}, "Update the dependencies array to be: [handleFile]", {"range": "2809", "text": "2810"}, "Update the dependencies array to be: [dispatch, fetchExams, isClassroomExam, isSearch]", {"range": "2811", "text": "2812"}, "Update the dependencies array to be: [currentPage, fetchExams, isClassroomExam]", {"range": "2813", "text": "2814"}, "Update the dependencies array to be: [exam, examId, navigate]", {"range": "2815", "text": "2816"}, "Wrap the definition of 'addQuestion' in its own useCallback() Hook.", {"range": "2817", "text": "2818"}, {"range": "2819", "text": "2818"}, {"range": "2820", "text": "2818"}, "Update the dependencies array to be: [flag, remainingTime]", {"range": "2821", "text": "2822"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", {"range": "2823", "text": "2824"}, "Update the dependencies array to be: [user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", {"range": "2825", "text": "2826"}, {"range": "2827", "text": "2816"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "2828", "text": "2829"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "2830", "text": "2831"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "2832", "text": "2833"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "2834", "text": "2835"}, {"range": "2836", "text": "2812"}, {"range": "2837", "text": "2814"}, "Update the dependencies array to be: [dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "2838", "text": "2839"}, "Update the dependencies array to be: [tuitionPayments, tuitionStatistics]", {"range": "2840", "text": "2841"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, monthTuition, dispatch]", {"range": "2842", "text": "2843"}, "Update the dependencies array to be: [dispatch, selectedYear, selectedMonth, user?.id]", {"range": "2844", "text": "2845"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "2846", "text": "2847"}, "Update the dependencies array to be: [dispatch, currentMonth, view, firstGridDate, lastGridDate]", {"range": "2848", "text": "2849"}, "Update the dependencies array to be: [onClose]", {"range": "2850", "text": "2851"}, "Update the dependencies array to be: [confirm, dispatch, exam.id]", {"range": "2852", "text": "2853"}, "Update the dependencies array to be: [dispatch, isChange, questions, questionsEdited]", {"range": "2854", "text": "2855"}, "Update the dependencies array to be: [exam, editedExam, isChange, dispatch]", {"range": "2856", "text": "2857"}, "Update the dependencies array to be: [dispatch, folder]", {"range": "2858", "text": "2859"}, "Update the dependencies array to be: [questionT<PERSON>ontent, correctAnswerTN, questionDS<PERSON>ontent, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", {"range": "2860", "text": "2861"}, "Update the dependencies array to be: [delay, effect]", {"range": "2862", "text": "2863"}, [1894, 1910], "[user, navigate, formData.username]", [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [9548, 9558], "[dispatch, fetchPdfFiles]", [669, 671], "[options, selected, type]", [1960, 1978], "[dispatch, fetchQuestions, params]", [1569, 1614], "[dispatch, search, page, pageSize, sortOrder, classId]", [2683, 2705], "[inputValue, dispatch, setSearch]", [2124, 2148], "[codes, question, question.class]", [2397, 2417], "[codes, exam, exam?.class]", [10723, 10724], "", [10723, 10723], "\\", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [972, 974], "[maxLength]", [7716, 7717], [7716, 7716], [7718, 7719], [7718, 7718], [7915, 7916], [7915, 7915], [7917, 7918], [7917, 7917], [9446, 9447], [9446, 9446], [10203, 10204], [10203, 10203], [10439, 10440], [10439, 10439], [10441, 10442], [10441, 10441], [10511, 10512], [10511, 10511], [10513, 10514], [10513, 10513], [13742, 13743], [13742, 13742], [14883, 14884], [14883, 14883], [15109, 15110], [15109, 15109], [15111, 15112], [15111, 15111], [15181, 15182], [15181, 15181], [15183, 15184], [15183, 15183], [18396, 18397], [18396, 18396], [19311, 19312], [19311, 19311], [2566, 2568], "[handlePaste]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [2341, 2361], "[dispatch, fetchExams, isClassroomExam, isSearch]", [2439, 2452], "[currentPage, fetchExams, isClassroomExam]", [2482, 2488], "[exam, examId, navigate]", [4594, 4956], "useCallback((questionId) => {\r\n        // Only add to saveQuestions if not already there and not in errorQuestions\r\n        if (!saveQuestions.includes(questionId) && !errorQuestions.includes(questionId)) {\r\n            dispatch(setSaveQuestions([...saveQuestions, questionId]));\r\n        }\r\n        // DO NOT call removeErrorQuestion here - let Redux slice handle it\r\n    })", [4594, 4956], [4594, 4956], [9169, 9184], "[flag, remainingTime]", [31473, 31564], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", [35671, 35699], "[user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", [1496, 1502], [7568, 7578], "[dispatch, limit]", [6802, 6815], "[activeItem?.index, classDetail]", [6098, 6108], "[dispatch, location.search]", [1654, 1676], "[currentPage, didInit, loadReports]", [3871, 3891], [3965, 3978], [10387, 10413], "[dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [24202, 24221], "[tuitionPayments, tuitionStatistics]", [8386, 8416], "[<PERSON><PERSON><PERSON><PERSON>, monthTuition, dispatch]", [2133, 2172], "[dispatch, selected<PERSON>ear, selected<PERSON><PERSON><PERSON>, user?.id]", [3996, 3998], "[dispatch, userId]", [2456, 2486], "[dispatch, currentMonth, view, firstGridDate, lastGridDate]", [2008, 2010], "[onClose]", [1519, 1528], "[confirm, dispatch, exam.id]", [3118, 3146], "[dispatch, isChange, questions, questionsEdited]", [3285, 3303], "[exam, editedExam, isChange, dispatch]", [2039, 2049], "[dispatch, folder]", [15769, 15879], "[question<PERSON><PERSON><PERSON><PERSON>, correctAnswerTN, question<PERSON><PERSON><PERSON>nt, correctAnswerDS, questionTLNContent, correctAnswerTLN, isViewAdd]", [511, 527], "[delay, effect]"]