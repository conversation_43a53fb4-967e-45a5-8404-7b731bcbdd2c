[{"C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx": "5", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx": "7", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx": "8", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx": "9", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx": "10", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx": "11", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx": "12", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "13", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "14", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx": "15", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx": "16", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "17", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "18", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "19", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "20", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx": "21", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "22", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "23", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx": "24", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js": "25", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js": "26", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js": "27", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js": "28", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js": "29", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js": "30", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js": "31", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js": "32", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js": "33", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js": "34", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js": "35", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js": "36", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js": "37", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js": "38", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js": "39", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx": "40", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx": "41", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx": "42", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx": "43", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx": "44", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx": "45", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx": "46", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "47", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx": "48", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx": "49", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx": "50", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx": "51", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx": "52", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx": "53", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx": "55", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx": "56", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx": "57", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx": "58", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx": "59", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx": "60", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx": "61", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx": "63", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx": "64", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx": "65", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx": "66", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx": "67", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx": "68", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx": "69", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx": "70", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx": "71", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx": "72", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx": "73", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx": "74", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx": "75", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx": "76", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx": "77", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js": "78", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx": "79", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx": "80", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx": "81", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js": "82", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx": "83", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx": "84", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js": "85", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx": "86", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "87", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx": "88", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx": "89", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx": "90", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx": "91", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx": "92", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx": "93", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx": "94", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx": "95", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx": "96", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx": "97", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js": "98", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx": "99", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx": "100", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js": "101", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js": "102", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js": "103", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx": "104", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js": "105", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js": "106", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx": "107", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx": "108", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx": "109", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx": "110", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx": "111", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx": "112", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx": "113", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx": "114", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx": "115", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx": "116", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx": "117", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js": "118", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js": "119", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js": "120", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx": "121", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx": "122", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx": "123", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx": "124", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx": "125", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx": "126", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx": "127", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js": "128", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx": "129", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx": "130", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx": "131", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "132", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js": "133", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx": "134", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx": "135", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx": "136", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx": "137", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx": "138", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx": "139", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx": "140", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx": "141", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx": "142", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx": "143", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js": "144", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js": "145", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx": "146", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx": "147", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx": "148", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx": "149", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx": "150", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx": "151", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx": "152", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx": "153", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx": "154", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "155", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "156", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx": "157", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx": "158", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx": "159", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx": "160", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx": "161", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx": "162", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js": "163", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js": "164", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx": "165", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "166", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx": "167", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx": "168", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js": "169", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx": "170", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx": "171", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx": "172", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx": "173", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx": "174", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx": "175", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx": "176", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx": "177", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx": "178", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx": "179", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx": "180", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx": "181", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx": "182", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx": "183", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx": "184", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx": "185", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "186", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx": "187", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx": "188", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx": "189", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx": "190", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx": "191", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx": "192", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx": "193", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx": "194", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx": "195", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx": "196", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx": "197", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx": "198", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx": "199", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx": "200", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx": "201", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx": "202", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js": "203", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx": "204", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx": "205", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js": "206", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx": "207", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js": "208", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx": "209", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx": "210", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx": "211", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx": "212", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "213", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js": "214", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js": "215", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js": "216", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx": "217", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx": "218", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "219", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "220", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx": "221", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js": "222", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "223", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "224", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js": "225", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "226", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx": "227", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx": "228", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx": "229", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx": "230", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js": "231", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js": "232", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx": "233", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "234", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx": "235", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "236", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "237", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js": "238", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "239", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "240", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "241", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js": "242", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx": "243", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx": "244", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx": "245", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx": "246", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx": "247", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx": "248", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js": "249", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js": "250", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx": "251", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx": "252", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx": "253", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx": "254", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js": "255", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx": "256", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx": "257", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js": "258", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx": "259", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js": "260", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js": "261", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx": "262", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx": "263", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js": "264", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js": "265", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx": "266", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx": "267", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx": "268", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js": "269", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx": "270", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js": "271", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx": "272", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx": "273", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx": "274", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js": "275", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx": "276", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js": "277", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx": "278", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx": "279", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx": "280", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx": "281", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx": "282", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx": "283", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx": "284", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js": "285", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx": "286", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal2.jsx": "287", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js": "288", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx": "289", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js": "290", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js": "291", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx": "292", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx": "293", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx": "294", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx": "295", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx": "296", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx": "297", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx": "298", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx": "299", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx": "300", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx": "301", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx": "302", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx": "303", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx": "304", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx": "305", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js": "306", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\ImageDropZone.jsx": "307", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SolutionEditor.jsx": "308"}, {"size": 837, "mtime": 1748800674146, "results": "309", "hashOfConfig": "310"}, {"size": 375, "mtime": 1744531393988, "results": "311", "hashOfConfig": "310"}, {"size": 12055, "mtime": 1751827979200, "results": "312", "hashOfConfig": "310"}, {"size": 2522, "mtime": 1751514283166, "results": "313", "hashOfConfig": "310"}, {"size": 1183, "mtime": 1751787344260, "results": "314", "hashOfConfig": "310"}, {"size": 7456, "mtime": 1751554069606, "results": "315", "hashOfConfig": "310"}, {"size": 4828, "mtime": 1746378664900, "results": "316", "hashOfConfig": "310"}, {"size": 2152, "mtime": 1751276563455, "results": "317", "hashOfConfig": "310"}, {"size": 1271, "mtime": 1744531393978, "results": "318", "hashOfConfig": "310"}, {"size": 21516, "mtime": 1751827522381, "results": "319", "hashOfConfig": "310"}, {"size": 11683, "mtime": 1748250288653, "results": "320", "hashOfConfig": "310"}, {"size": 1979, "mtime": 1749722105422, "results": "321", "hashOfConfig": "310"}, {"size": 551, "mtime": 1744531393982, "results": "322", "hashOfConfig": "310"}, {"size": 275, "mtime": 1748215697376, "results": "323", "hashOfConfig": "310"}, {"size": 1739, "mtime": 1749721392840, "results": "324", "hashOfConfig": "310"}, {"size": 44964, "mtime": 1750904385044, "results": "325", "hashOfConfig": "310"}, {"size": 9223, "mtime": 1751743443365, "results": "326", "hashOfConfig": "310"}, {"size": 350, "mtime": 1750322483573, "results": "327", "hashOfConfig": "310"}, {"size": 19813, "mtime": 1750323348863, "results": "328", "hashOfConfig": "310"}, {"size": 3066, "mtime": 1751743443365, "results": "329", "hashOfConfig": "310"}, {"size": 2158, "mtime": 1751439211245, "results": "330", "hashOfConfig": "310"}, {"size": 1898, "mtime": 1750325540002, "results": "331", "hashOfConfig": "310"}, {"size": 348, "mtime": 1749202010110, "results": "332", "hashOfConfig": "310"}, {"size": 3322, "mtime": 1751743523736, "results": "333", "hashOfConfig": "310"}, {"size": 9893, "mtime": 1750849306726, "results": "334", "hashOfConfig": "310"}, {"size": 733, "mtime": 1751439773864, "results": "335", "hashOfConfig": "310"}, {"size": 6445, "mtime": 1749721854651, "results": "336", "hashOfConfig": "310"}, {"size": 3345, "mtime": 1751786228120, "results": "337", "hashOfConfig": "310"}, {"size": 3523, "mtime": 1751745797479, "results": "338", "hashOfConfig": "310"}, {"size": 7806, "mtime": 1750325495112, "results": "339", "hashOfConfig": "310"}, {"size": 17715, "mtime": 1751542931344, "results": "340", "hashOfConfig": "310"}, {"size": 14519, "mtime": 1750767145502, "results": "341", "hashOfConfig": "310"}, {"size": 1380, "mtime": 1744531393975, "results": "342", "hashOfConfig": "310"}, {"size": 2737, "mtime": 1750295163218, "results": "343", "hashOfConfig": "310"}, {"size": 4309, "mtime": 1751822374362, "results": "344", "hashOfConfig": "310"}, {"size": 5921, "mtime": 1751786223198, "results": "345", "hashOfConfig": "310"}, {"size": 2480, "mtime": 1747721626218, "results": "346", "hashOfConfig": "310"}, {"size": 5150, "mtime": 1748398556383, "results": "347", "hashOfConfig": "310"}, {"size": 1337, "mtime": 1747720637516, "results": "348", "hashOfConfig": "310"}, {"size": 690, "mtime": 1744531393950, "results": "349", "hashOfConfig": "310"}, {"size": 1339, "mtime": 1751554069606, "results": "350", "hashOfConfig": "310"}, {"size": 673, "mtime": 1744531393976, "results": "351", "hashOfConfig": "310"}, {"size": 1495, "mtime": 1751362330062, "results": "352", "hashOfConfig": "310"}, {"size": 2276, "mtime": 1751554069606, "results": "353", "hashOfConfig": "310"}, {"size": 1151, "mtime": 1749730250381, "results": "354", "hashOfConfig": "310"}, {"size": 3200, "mtime": 1744531393954, "results": "355", "hashOfConfig": "310"}, {"size": 3578, "mtime": 1747223318325, "results": "356", "hashOfConfig": "310"}, {"size": 635, "mtime": 1744531393961, "results": "357", "hashOfConfig": "310"}, {"size": 1228, "mtime": 1751361437565, "results": "358", "hashOfConfig": "310"}, {"size": 4033, "mtime": 1751276343661, "results": "359", "hashOfConfig": "310"}, {"size": 7571, "mtime": 1747902858134, "results": "360", "hashOfConfig": "310"}, {"size": 1218, "mtime": 1744531393963, "results": "361", "hashOfConfig": "310"}, {"size": 9572, "mtime": 1750323036527, "results": "362", "hashOfConfig": "310"}, {"size": 5752, "mtime": 1751276751884, "results": "363", "hashOfConfig": "310"}, {"size": 28691, "mtime": 1744958278477, "results": "364", "hashOfConfig": "310"}, {"size": 19936, "mtime": 1748984865157, "results": "365", "hashOfConfig": "310"}, {"size": 5631, "mtime": 1749721260990, "results": "366", "hashOfConfig": "310"}, {"size": 8139, "mtime": 1749721464118, "results": "367", "hashOfConfig": "310"}, {"size": 911, "mtime": 1744531393943, "results": "368", "hashOfConfig": "310"}, {"size": 3827, "mtime": 1751811244412, "results": "369", "hashOfConfig": "310"}, {"size": 31136, "mtime": 1751782971202, "results": "370", "hashOfConfig": "310"}, {"size": 9572, "mtime": 1749693815954, "results": "371", "hashOfConfig": "310"}, {"size": 1574, "mtime": 1744531393960, "results": "372", "hashOfConfig": "310"}, {"size": 7401, "mtime": 1747223318342, "results": "373", "hashOfConfig": "310"}, {"size": 2914, "mtime": 1747223318342, "results": "374", "hashOfConfig": "310"}, {"size": 28398, "mtime": 1749425223637, "results": "375", "hashOfConfig": "310"}, {"size": 17584, "mtime": 1750758162845, "results": "376", "hashOfConfig": "310"}, {"size": 19291, "mtime": 1751845914738, "results": "377", "hashOfConfig": "310"}, {"size": 1734, "mtime": 1744531393948, "results": "378", "hashOfConfig": "310"}, {"size": 56278, "mtime": 1751372627190, "results": "379", "hashOfConfig": "310"}, {"size": 6448, "mtime": 1749729618233, "results": "380", "hashOfConfig": "310"}, {"size": 5637, "mtime": 1747254491214, "results": "381", "hashOfConfig": "310"}, {"size": 21475, "mtime": 1750325928413, "results": "382", "hashOfConfig": "310"}, {"size": 10256, "mtime": 1744531393939, "results": "383", "hashOfConfig": "310"}, {"size": 1875, "mtime": 1750960485529, "results": "384", "hashOfConfig": "310"}, {"size": 19265, "mtime": 1751742523955, "results": "385", "hashOfConfig": "310"}, {"size": 6162, "mtime": 1748250288622, "results": "386", "hashOfConfig": "310"}, {"size": 3022, "mtime": 1747719253353, "results": "387", "hashOfConfig": "310"}, {"size": 7915, "mtime": 1748392811401, "results": "388", "hashOfConfig": "310"}, {"size": 5844, "mtime": 1751743845208, "results": "389", "hashOfConfig": "310"}, {"size": 3490, "mtime": 1749748056906, "results": "390", "hashOfConfig": "310"}, {"size": 935, "mtime": 1745405710864, "results": "391", "hashOfConfig": "310"}, {"size": 949, "mtime": 1747223318316, "results": "392", "hashOfConfig": "310"}, {"size": 3267, "mtime": 1747354362354, "results": "393", "hashOfConfig": "310"}, {"size": 3005, "mtime": 1750849360884, "results": "394", "hashOfConfig": "310"}, {"size": 4276, "mtime": 1751828500143, "results": "395", "hashOfConfig": "310"}, {"size": 2380, "mtime": 1744531393947, "results": "396", "hashOfConfig": "310"}, {"size": 2201, "mtime": 1744531393951, "results": "397", "hashOfConfig": "310"}, {"size": 14316, "mtime": 1749895693266, "results": "398", "hashOfConfig": "310"}, {"size": 1990, "mtime": 1744531393948, "results": "399", "hashOfConfig": "310"}, {"size": 841, "mtime": 1748984865154, "results": "400", "hashOfConfig": "310"}, {"size": 5565, "mtime": 1745690690469, "results": "401", "hashOfConfig": "310"}, {"size": 2295, "mtime": 1747223318353, "results": "402", "hashOfConfig": "310"}, {"size": 3146, "mtime": 1744531393940, "results": "403", "hashOfConfig": "310"}, {"size": 4074, "mtime": 1747223318326, "results": "404", "hashOfConfig": "310"}, {"size": 2787, "mtime": 1750173366905, "results": "405", "hashOfConfig": "310"}, {"size": 10634, "mtime": 1745483150534, "results": "406", "hashOfConfig": "407"}, {"size": 3707, "mtime": 1749722678323, "results": "408", "hashOfConfig": "310"}, {"size": 6034, "mtime": 1748364026312, "results": "409", "hashOfConfig": "310"}, {"size": 4147, "mtime": 1749731674278, "results": "410", "hashOfConfig": "310"}, {"size": 822, "mtime": 1751276455614, "results": "411", "hashOfConfig": "310"}, {"size": 4040, "mtime": 1750767145503, "results": "412", "hashOfConfig": "310"}, {"size": 297, "mtime": 1744531393989, "results": "413", "hashOfConfig": "310"}, {"size": 313, "mtime": 1744531393940, "results": "414", "hashOfConfig": "310"}, {"size": 4755, "mtime": 1751305471358, "results": "415", "hashOfConfig": "310"}, {"size": 1652, "mtime": 1748250288663, "results": "416", "hashOfConfig": "310"}, {"size": 993, "mtime": 1747223318349, "results": "417", "hashOfConfig": "310"}, {"size": 475, "mtime": 1748984865156, "results": "418", "hashOfConfig": "310"}, {"size": 902, "mtime": 1747223318353, "results": "419", "hashOfConfig": "310"}, {"size": 3053, "mtime": 1744531393946, "results": "420", "hashOfConfig": "310"}, {"size": 49554, "mtime": 1750767145500, "results": "421", "hashOfConfig": "310"}, {"size": 3402, "mtime": 1748781512174, "results": "422", "hashOfConfig": "310"}, {"size": 4872, "mtime": 1747223318349, "results": "423", "hashOfConfig": "310"}, {"size": 1641, "mtime": 1751827507517, "results": "424", "hashOfConfig": "310"}, {"size": 2297, "mtime": 1744531393945, "results": "425", "hashOfConfig": "310"}, {"size": 2193, "mtime": 1747223318349, "results": "426", "hashOfConfig": "310"}, {"size": 1359, "mtime": 1747223318349, "results": "427", "hashOfConfig": "310"}, {"size": 826, "mtime": 1748398318309, "results": "428", "hashOfConfig": "310"}, {"size": 1769, "mtime": 1748709335429, "results": "429", "hashOfConfig": "310"}, {"size": 1050, "mtime": 1751740167834, "results": "430", "hashOfConfig": "310"}, {"size": 4921, "mtime": 1747223318325, "results": "431", "hashOfConfig": "310"}, {"size": 11407, "mtime": 1751822007149, "results": "432", "hashOfConfig": "310"}, {"size": 411, "mtime": 1744531393940, "results": "433", "hashOfConfig": "310"}, {"size": 2290, "mtime": 1744531393963, "results": "434", "hashOfConfig": "310"}, {"size": 1219, "mtime": 1747467640276, "results": "435", "hashOfConfig": "310"}, {"size": 2003, "mtime": 1744531393970, "results": "436", "hashOfConfig": "310"}, {"size": 2166, "mtime": 1744531393969, "results": "437", "hashOfConfig": "310"}, {"size": 18935, "mtime": 1751374977843, "results": "438", "hashOfConfig": "310"}, {"size": 6813, "mtime": 1747223318342, "results": "439", "hashOfConfig": "310"}, {"size": 3094, "mtime": 1744531393970, "results": "440", "hashOfConfig": "310"}, {"size": 6364, "mtime": 1750767145498, "results": "441", "hashOfConfig": "310"}, {"size": 503, "mtime": 1744531393949, "results": "442", "hashOfConfig": "310"}, {"size": 394, "mtime": 1751811266307, "results": "443", "hashOfConfig": "310"}, {"size": 7876, "mtime": 1747223318342, "results": "444", "hashOfConfig": "310"}, {"size": 4922, "mtime": 1748329867180, "results": "445", "hashOfConfig": "310"}, {"size": 19965, "mtime": 1750900200879, "results": "446", "hashOfConfig": "310"}, {"size": 20555, "mtime": 1748250288625, "results": "447", "hashOfConfig": "310"}, {"size": 1337, "mtime": 1744531393967, "results": "448", "hashOfConfig": "407"}, {"size": 5412, "mtime": 1747223318349, "results": "449", "hashOfConfig": "310"}, {"size": 2938, "mtime": 1747223318353, "results": "450", "hashOfConfig": "310"}, {"size": 3182, "mtime": 1747223318353, "results": "451", "hashOfConfig": "310"}, {"size": 2928, "mtime": 1747223318349, "results": "452", "hashOfConfig": "310"}, {"size": 1885, "mtime": 1747354661883, "results": "453", "hashOfConfig": "310"}, {"size": 1345, "mtime": 1749697625937, "results": "454", "hashOfConfig": "310"}, {"size": 4099, "mtime": 1749731407409, "results": "455", "hashOfConfig": "310"}, {"size": 1576, "mtime": 1751811065450, "results": "456", "hashOfConfig": "310"}, {"size": 6380, "mtime": 1747361017417, "results": "457", "hashOfConfig": "310"}, {"size": 2600, "mtime": 1748876218633, "results": "458", "hashOfConfig": "310"}, {"size": 11541, "mtime": 1750900175948, "results": "459", "hashOfConfig": "310"}, {"size": 3297, "mtime": 1744531393957, "results": "460", "hashOfConfig": "310"}, {"size": 31011, "mtime": 1750902784209, "results": "461", "hashOfConfig": "310"}, {"size": 14565, "mtime": 1750819864802, "results": "462", "hashOfConfig": "310"}, {"size": 1205, "mtime": 1748984865161, "results": "463", "hashOfConfig": "310"}, {"size": 12148, "mtime": 1748250288636, "results": "464", "hashOfConfig": "310"}, {"size": 7688, "mtime": 1747223318312, "results": "465", "hashOfConfig": "310"}, {"size": 8344, "mtime": 1745507778499, "results": "466", "hashOfConfig": "310"}, {"size": 8025, "mtime": 1745507836095, "results": "467", "hashOfConfig": "310"}, {"size": 6988, "mtime": 1747223318344, "results": "468", "hashOfConfig": "310"}, {"size": 7719, "mtime": 1745499803667, "results": "469", "hashOfConfig": "310"}, {"size": 8378, "mtime": 1747223318344, "results": "470", "hashOfConfig": "310"}, {"size": 7254, "mtime": 1747223318344, "results": "471", "hashOfConfig": "310"}, {"size": 1723, "mtime": 1751844661049, "results": "472", "hashOfConfig": "310"}, {"size": 11464, "mtime": 1745500164258, "results": "473", "hashOfConfig": "310"}, {"size": 4650, "mtime": 1745508333678, "results": "474", "hashOfConfig": "310"}, {"size": 13822, "mtime": 1748250288625, "results": "475", "hashOfConfig": "310"}, {"size": 8599, "mtime": 1747223318326, "results": "476", "hashOfConfig": "310"}, {"size": 9774, "mtime": 1747223318326, "results": "477", "hashOfConfig": "310"}, {"size": 7914, "mtime": 1747223318326, "results": "478", "hashOfConfig": "310"}, {"size": 10728, "mtime": 1749548057374, "results": "479", "hashOfConfig": "310"}, {"size": 18582, "mtime": 1749747601919, "results": "480", "hashOfConfig": "310"}, {"size": 8954, "mtime": 1749810779685, "results": "481", "hashOfConfig": "310"}, {"size": 3429, "mtime": 1745682027607, "results": "482", "hashOfConfig": "310"}, {"size": 2298, "mtime": 1749802834779, "results": "483", "hashOfConfig": "310"}, {"size": 823, "mtime": 1748779533260, "results": "484", "hashOfConfig": "310"}, {"size": 1380, "mtime": 1745682047729, "results": "485", "hashOfConfig": "310"}, {"size": 1145, "mtime": 1747902858133, "results": "486", "hashOfConfig": "310"}, {"size": 1118, "mtime": 1745682069045, "results": "487", "hashOfConfig": "310"}, {"size": 978, "mtime": 1747902858130, "results": "488", "hashOfConfig": "310"}, {"size": 1781, "mtime": 1745682059432, "results": "489", "hashOfConfig": "407"}, {"size": 10563, "mtime": 1748585335221, "results": "490", "hashOfConfig": "310"}, {"size": 3574, "mtime": 1746378664904, "results": "491", "hashOfConfig": "310"}, {"size": 4389, "mtime": 1749980511142, "results": "492", "hashOfConfig": "310"}, {"size": 4914, "mtime": 1750326838086, "results": "493", "hashOfConfig": "310"}, {"size": 6418, "mtime": 1750326903479, "results": "494", "hashOfConfig": "310"}, {"size": 1020, "mtime": 1745682406063, "results": "495", "hashOfConfig": "310"}, {"size": 2723, "mtime": 1749810234131, "results": "496", "hashOfConfig": "310"}, {"size": 6116, "mtime": 1746378664905, "results": "497", "hashOfConfig": "310"}, {"size": 1565, "mtime": 1747223318344, "results": "498", "hashOfConfig": "310"}, {"size": 1624, "mtime": 1745689590880, "results": "499", "hashOfConfig": "310"}, {"size": 45152, "mtime": 1750295514405, "results": "500", "hashOfConfig": "310"}, {"size": 70430, "mtime": 1748984865165, "results": "501", "hashOfConfig": "310"}, {"size": 9519, "mtime": 1747354195259, "results": "502", "hashOfConfig": "310"}, {"size": 7408, "mtime": 1749894301204, "results": "503", "hashOfConfig": "310"}, {"size": 4905, "mtime": 1747354192845, "results": "504", "hashOfConfig": "310"}, {"size": 60115, "mtime": 1750947214848, "results": "505", "hashOfConfig": "310"}, {"size": 26487, "mtime": 1748278828957, "results": "506", "hashOfConfig": "310"}, {"size": 24147, "mtime": 1747354834297, "results": "507", "hashOfConfig": "310"}, {"size": 23053, "mtime": 1749730024729, "results": "508", "hashOfConfig": "310"}, {"size": 37412, "mtime": 1750487313341, "results": "509", "hashOfConfig": "310"}, {"size": 17071, "mtime": 1751543017144, "results": "510", "hashOfConfig": "310"}, {"size": 8060, "mtime": 1747223318310, "results": "511", "hashOfConfig": "310"}, {"size": 16767, "mtime": 1748876218633, "results": "512", "hashOfConfig": "310"}, {"size": 3160, "mtime": 1745731138150, "results": "513", "hashOfConfig": "310"}, {"size": 7136, "mtime": 1747223318325, "results": "514", "hashOfConfig": "310"}, {"size": 20185, "mtime": 1747223318312, "results": "515", "hashOfConfig": "310"}, {"size": 2129, "mtime": 1746378664905, "results": "516", "hashOfConfig": "310"}, {"size": 955, "mtime": 1746378664898, "results": "517", "hashOfConfig": "310"}, {"size": 1184, "mtime": 1746378664905, "results": "518", "hashOfConfig": "310"}, {"size": 13378, "mtime": 1748984865159, "results": "519", "hashOfConfig": "310"}, {"size": 1099, "mtime": 1748326442261, "results": "520", "hashOfConfig": "310"}, {"size": 15897, "mtime": 1749894770618, "results": "521", "hashOfConfig": "310"}, {"size": 11963, "mtime": 1750323324025, "results": "522", "hashOfConfig": "310"}, {"size": 12224, "mtime": 1748220515100, "results": "523", "hashOfConfig": "310"}, {"size": 10534, "mtime": 1748220627343, "results": "524", "hashOfConfig": "310"}, {"size": 4031, "mtime": 1747278525096, "results": "525", "hashOfConfig": "310"}, {"size": 2036, "mtime": 1747283717643, "results": "526", "hashOfConfig": "310"}, {"size": 61133, "mtime": 1751834903269, "results": "527", "hashOfConfig": "310"}, {"size": 3589, "mtime": 1747355350828, "results": "528", "hashOfConfig": "310"}, {"size": 7509, "mtime": 1747353152130, "results": "529", "hashOfConfig": "310"}, {"size": 6153, "mtime": 1747354063004, "results": "530", "hashOfConfig": "310"}, {"size": 16791, "mtime": 1748473697636, "results": "531", "hashOfConfig": "310"}, {"size": 12245, "mtime": 1750386603287, "results": "532", "hashOfConfig": "310"}, {"size": 13612, "mtime": 1750175198005, "results": "533", "hashOfConfig": "310"}, {"size": 73138, "mtime": 1750487274356, "results": "534", "hashOfConfig": "310"}, {"size": 5589, "mtime": 1750239106405, "results": "535", "hashOfConfig": "310"}, {"size": 9836, "mtime": 1750175833314, "results": "536", "hashOfConfig": "310"}, {"size": 7964, "mtime": 1750400179899, "results": "537", "hashOfConfig": "310"}, {"size": 4152, "mtime": 1749202010110, "results": "538", "hashOfConfig": "310"}, {"size": 4762, "mtime": 1748513292659, "results": "539", "hashOfConfig": "310"}, {"size": 2443, "mtime": 1747719362467, "results": "540", "hashOfConfig": "310"}, {"size": 16049, "mtime": 1751785268325, "results": "541", "hashOfConfig": "310"}, {"size": 2002, "mtime": 1751785248620, "results": "542", "hashOfConfig": "310"}, {"size": 55792, "mtime": 1751616690844, "results": "543", "hashOfConfig": "310"}, {"size": 16095, "mtime": 1751785298468, "results": "544", "hashOfConfig": "310"}, {"size": 8222, "mtime": 1748250288630, "results": "545", "hashOfConfig": "310"}, {"size": 11210, "mtime": 1748223444732, "results": "546", "hashOfConfig": "310"}, {"size": 41991, "mtime": 1751785335077, "results": "547", "hashOfConfig": "310"}, {"size": 8067, "mtime": 1750764769591, "results": "548", "hashOfConfig": "310"}, {"size": 28083, "mtime": 1748250288657, "results": "549", "hashOfConfig": "310"}, {"size": 29543, "mtime": 1748984865164, "results": "550", "hashOfConfig": "310"}, {"size": 36682, "mtime": 1748250768337, "results": "551", "hashOfConfig": "310"}, {"size": 1999, "mtime": 1750764523294, "results": "552", "hashOfConfig": "310"}, {"size": 9569, "mtime": 1749694485776, "results": "553", "hashOfConfig": "310"}, {"size": 13102, "mtime": 1748250288647, "results": "554", "hashOfConfig": "310"}, {"size": 16077, "mtime": 1748365756504, "results": "555", "hashOfConfig": "310"}, {"size": 3987, "mtime": 1748709335425, "results": "556", "hashOfConfig": "310"}, {"size": 3539, "mtime": 1748800991826, "results": "557", "hashOfConfig": "310"}, {"size": 1712, "mtime": 1748800656400, "results": "558", "hashOfConfig": "310"}, {"size": 1983, "mtime": 1751010202512, "results": "559", "hashOfConfig": "310"}, {"size": 3908, "mtime": 1748801325319, "results": "560", "hashOfConfig": "310"}, {"size": 839, "mtime": 1748801505979, "results": "561", "hashOfConfig": "310"}, {"size": 365, "mtime": 1748984865153, "results": "562", "hashOfConfig": "310"}, {"size": 1800, "mtime": 1751834731248, "results": "563", "hashOfConfig": "310"}, {"size": 8860, "mtime": 1749202010110, "results": "564", "hashOfConfig": "310"}, {"size": 5114, "mtime": 1750951954100, "results": "565", "hashOfConfig": "310"}, {"size": 8088, "mtime": 1751742516897, "results": "566", "hashOfConfig": "310"}, {"size": 4151, "mtime": 1751716037997, "results": "567", "hashOfConfig": "310"}, {"size": 958, "mtime": 1750951934365, "results": "568", "hashOfConfig": "310"}, {"size": 2836, "mtime": 1749722547776, "results": "569", "hashOfConfig": "310"}, {"size": 913, "mtime": 1751742948436, "results": "570", "hashOfConfig": "310"}, {"size": 388, "mtime": 1749720429395, "results": "571", "hashOfConfig": "310"}, {"size": 579, "mtime": 1749731593347, "results": "572", "hashOfConfig": "310"}, {"size": 1948, "mtime": 1751543127045, "results": "573", "hashOfConfig": "310"}, {"size": 10543, "mtime": 1750295311557, "results": "574", "hashOfConfig": "310"}, {"size": 1664, "mtime": 1749808930419, "results": "575", "hashOfConfig": "310"}, {"size": 2401, "mtime": 1750389237348, "results": "576", "hashOfConfig": "310"}, {"size": 7582, "mtime": 1750400196241, "results": "577", "hashOfConfig": "310"}, {"size": 3921, "mtime": 1750322224906, "results": "578", "hashOfConfig": "310"}, {"size": 2025, "mtime": 1750487274354, "results": "579", "hashOfConfig": "310"}, {"size": 3458, "mtime": 1750487274353, "results": "580", "hashOfConfig": "310"}, {"size": 200, "mtime": 1750487274358, "results": "581", "hashOfConfig": "310"}, {"size": 4832, "mtime": 1750574009240, "results": "582", "hashOfConfig": "310"}, {"size": 3066, "mtime": 1750766071078, "results": "583", "hashOfConfig": "310"}, {"size": 4807, "mtime": 1750765702772, "results": "584", "hashOfConfig": "310"}, {"size": 74, "mtime": 1748250288649, "results": "585", "hashOfConfig": "310"}, {"size": 4384, "mtime": 1750962443161, "results": "586", "hashOfConfig": "310"}, {"size": 4263, "mtime": 1750960348603, "results": "587", "hashOfConfig": "310"}, {"size": 4331, "mtime": 1750963155580, "results": "588", "hashOfConfig": "310"}, {"size": 3878, "mtime": 1750940043247, "results": "589", "hashOfConfig": "310"}, {"size": 23770, "mtime": 1750963670443, "results": "590", "hashOfConfig": "310"}, {"size": 17942, "mtime": 1750963374804, "results": "591", "hashOfConfig": "310"}, {"size": 22243, "mtime": 1750961198993, "results": "592", "hashOfConfig": "310"}, {"size": 1252, "mtime": 1750941383903, "results": "593", "hashOfConfig": "310"}, {"size": 846, "mtime": 1750944008903, "results": "594", "hashOfConfig": "310"}, {"size": 218, "mtime": 1751349395110, "results": "595", "hashOfConfig": "310"}, {"size": 2323, "mtime": 1751350697129, "results": "596", "hashOfConfig": "310"}, {"size": 52491, "mtime": 1751375995915, "results": "597", "hashOfConfig": "310"}, {"size": 1133, "mtime": 1751842850227, "results": "598", "hashOfConfig": "310"}, {"size": 11512, "mtime": 1751835083804, "results": "599", "hashOfConfig": "310"}, {"size": 10352, "mtime": 1751843655468, "results": "600", "hashOfConfig": "310"}, {"size": 449, "mtime": 1751515976272, "results": "601", "hashOfConfig": "310"}, {"size": 2125, "mtime": 1751831578538, "results": "602", "hashOfConfig": "310"}, {"size": 7426, "mtime": 1751846803304, "results": "603", "hashOfConfig": "310"}, {"size": 1127, "mtime": 1751843983335, "results": "604", "hashOfConfig": "310"}, {"size": 16628, "mtime": 1751846575221, "results": "605", "hashOfConfig": "310"}, {"size": 5733, "mtime": 1751847035611, "results": "606", "hashOfConfig": "310"}, {"size": 4358, "mtime": 1751835622757, "results": "607", "hashOfConfig": "310"}, {"size": 759, "mtime": 1751785941157, "results": "608", "hashOfConfig": "310"}, {"size": 4350, "mtime": 1751784560929, "results": "609", "hashOfConfig": "310"}, {"size": 4259, "mtime": 1751742649369, "results": "610", "hashOfConfig": "310"}, {"size": 843, "mtime": 1751717594658, "results": "611", "hashOfConfig": "310"}, {"size": 663, "mtime": 1751742504805, "results": "612", "hashOfConfig": "310"}, {"size": 1337, "mtime": 1751829986814, "results": "613", "hashOfConfig": "310"}, {"size": 5772, "mtime": 1751830627407, "results": "614", "hashOfConfig": "310"}, {"size": 2222, "mtime": 1751829103916, "results": "615", "hashOfConfig": "310"}, {"size": 1702, "mtime": 1751829247534, "results": "616", "hashOfConfig": "310"}, {"size": 4105, "mtime": 1751831884036, "results": "617", "hashOfConfig": "310"}, {"size": 4793, "mtime": 1751833516713, "results": "618", "hashOfConfig": "310"}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e07wfn", {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1op6h7j", {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1258", "messages": "1259", "suppressedMessages": "1260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1261", "messages": "1262", "suppressedMessages": "1263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1264", "messages": "1265", "suppressedMessages": "1266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1267", "messages": "1268", "suppressedMessages": "1269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1270", "messages": "1271", "suppressedMessages": "1272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1273", "messages": "1274", "suppressedMessages": "1275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1276", "messages": "1277", "suppressedMessages": "1278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1279", "messages": "1280", "suppressedMessages": "1281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1282", "messages": "1283", "suppressedMessages": "1284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1285", "messages": "1286", "suppressedMessages": "1287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1288", "messages": "1289", "suppressedMessages": "1290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1291", "messages": "1292", "suppressedMessages": "1293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1297", "messages": "1298", "suppressedMessages": "1299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1300", "messages": "1301", "suppressedMessages": "1302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1303", "messages": "1304", "suppressedMessages": "1305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1306", "messages": "1307", "suppressedMessages": "1308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1327", "messages": "1328", "suppressedMessages": "1329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1330", "messages": "1331", "suppressedMessages": "1332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1333", "messages": "1334", "suppressedMessages": "1335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1336", "messages": "1337", "suppressedMessages": "1338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1339", "messages": "1340", "suppressedMessages": "1341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1342", "messages": "1343", "suppressedMessages": "1344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1345", "messages": "1346", "suppressedMessages": "1347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1348", "messages": "1349", "suppressedMessages": "1350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1351", "messages": "1352", "suppressedMessages": "1353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1354", "messages": "1355", "suppressedMessages": "1356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1357", "messages": "1358", "suppressedMessages": "1359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1360", "messages": "1361", "suppressedMessages": "1362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1363", "messages": "1364", "suppressedMessages": "1365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1366", "messages": "1367", "suppressedMessages": "1368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1369", "messages": "1370", "suppressedMessages": "1371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1372", "messages": "1373", "suppressedMessages": "1374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1375", "messages": "1376", "suppressedMessages": "1377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1378", "messages": "1379", "suppressedMessages": "1380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1381", "messages": "1382", "suppressedMessages": "1383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1384", "messages": "1385", "suppressedMessages": "1386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1387", "messages": "1388", "suppressedMessages": "1389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1390", "messages": "1391", "suppressedMessages": "1392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1393", "messages": "1394", "suppressedMessages": "1395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1396", "messages": "1397", "suppressedMessages": "1398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1399", "messages": "1400", "suppressedMessages": "1401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1402", "messages": "1403", "suppressedMessages": "1404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1405", "messages": "1406", "suppressedMessages": "1407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1408", "messages": "1409", "suppressedMessages": "1410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1411", "messages": "1412", "suppressedMessages": "1413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1414", "messages": "1415", "suppressedMessages": "1416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1417", "messages": "1418", "suppressedMessages": "1419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1420", "messages": "1421", "suppressedMessages": "1422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1423", "messages": "1424", "suppressedMessages": "1425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1426", "messages": "1427", "suppressedMessages": "1428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1429", "messages": "1430", "suppressedMessages": "1431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1432", "messages": "1433", "suppressedMessages": "1434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1435", "messages": "1436", "suppressedMessages": "1437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1438", "messages": "1439", "suppressedMessages": "1440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1441", "messages": "1442", "suppressedMessages": "1443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1444", "messages": "1445", "suppressedMessages": "1446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1447", "messages": "1448", "suppressedMessages": "1449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1450", "messages": "1451", "suppressedMessages": "1452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1453", "messages": "1454", "suppressedMessages": "1455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1456", "messages": "1457", "suppressedMessages": "1458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1459", "messages": "1460", "suppressedMessages": "1461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1462", "messages": "1463", "suppressedMessages": "1464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1465", "messages": "1466", "suppressedMessages": "1467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1468", "messages": "1469", "suppressedMessages": "1470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1471", "messages": "1472", "suppressedMessages": "1473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1474", "messages": "1475", "suppressedMessages": "1476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1477", "messages": "1478", "suppressedMessages": "1479", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1480", "messages": "1481", "suppressedMessages": "1482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1483", "messages": "1484", "suppressedMessages": "1485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1486", "messages": "1487", "suppressedMessages": "1488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1489", "messages": "1490", "suppressedMessages": "1491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1492", "messages": "1493", "suppressedMessages": "1494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1495", "messages": "1496", "suppressedMessages": "1497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1498", "messages": "1499", "suppressedMessages": "1500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1501", "messages": "1502", "suppressedMessages": "1503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1504", "messages": "1505", "suppressedMessages": "1506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1507", "messages": "1508", "suppressedMessages": "1509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1510", "messages": "1511", "suppressedMessages": "1512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1513", "messages": "1514", "suppressedMessages": "1515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1516", "messages": "1517", "suppressedMessages": "1518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1519", "messages": "1520", "suppressedMessages": "1521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1522", "messages": "1523", "suppressedMessages": "1524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1525", "messages": "1526", "suppressedMessages": "1527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1528", "messages": "1529", "suppressedMessages": "1530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1531", "messages": "1532", "suppressedMessages": "1533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1534", "messages": "1535", "suppressedMessages": "1536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1537", "messages": "1538", "suppressedMessages": "1539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1540", "messages": "1541", "suppressedMessages": "1542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js", ["1543"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx", ["1544", "1545", "1546", "1547", "1548"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx", ["1549", "1550"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx", ["1551"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx", ["1552"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx", ["1553", "1554", "1555", "1556", "1557"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx", ["1558", "1559"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1560", "1561"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1562"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1563", "1564", "1565", "1566", "1567", "1568", "1569"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1570", "1571", "1572", "1573", "1574"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", ["1575"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", ["1589", "1590", "1591", "1592"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1593", "1594"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1603"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js", ["1604"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js", ["1605"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js", ["1606"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx", ["1607"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx", ["1608"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1609"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx", ["1610", "1611", "1612", "1613", "1614", "1615", "1616"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx", ["1617"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx", ["1618", "1619", "1620"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx", ["1621"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx", ["1622"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx", ["1623", "1624", "1625"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx", ["1626", "1627", "1628"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx", ["1629", "1630"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx", ["1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx", ["1647", "1648"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx", ["1649", "1650", "1651", "1652", "1653"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx", ["1654", "1655", "1656", "1657", "1658", "1659", "1660"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx", ["1661", "1662"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx", ["1663"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx", ["1664", "1665", "1666", "1667", "1668"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx", ["1669"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx", ["1670", "1671", "1672", "1673", "1674", "1675"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx", ["1676", "1677"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx", ["1678"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx", ["1679", "1680", "1681", "1682", "1683", "1684", "1685"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx", ["1686", "1687", "1688", "1689", "1690"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1691", "1692", "1693"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx", ["1694", "1695"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx", ["1696"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx", ["1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx", ["1705"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx", ["1706"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js", ["1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx", ["1721"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx", ["1722"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx", ["1723"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx", ["1724"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx", ["1725"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx", ["1726", "1727", "1728"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx", ["1729"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1730", "1731", "1732", "1733", "1734", "1735"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx", ["1736"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx", ["1737", "1738", "1739", "1740"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx", ["1741", "1742"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx", ["1743", "1744", "1745", "1746"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx", ["1747", "1748"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx", ["1749", "1750", "1751", "1752"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1753"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx", ["1754", "1755"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1756"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx", ["1757", "1758"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx", ["1759"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx", ["1760"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx", ["1761", "1762", "1763", "1764", "1765"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx", ["1766", "1767"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx", ["1768", "1769", "1770"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx", ["1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx", ["1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx", ["1799", "1800", "1801", "1802"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx", ["1803"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx", ["1804", "1805", "1806", "1807"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx", ["1808", "1809", "1810", "1811", "1812", "1813", "1814"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx", ["1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx", ["1825", "1826", "1827", "1828"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["1829", "1830"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx", ["1831", "1832", "1833", "1834", "1835", "1836", "1837"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx", ["1838", "1839", "1840", "1841", "1842", "1843", "1844"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx", ["1845", "1846"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js", ["1847"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx", ["1848", "1849", "1850"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx", ["1851", "1852", "1853", "1854"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js", ["1855"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx", ["1856", "1857", "1858"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx", ["1859", "1860", "1861", "1862", "1863", "1864", "1865"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js", ["1866"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["1867", "1868", "1869", "1870"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["1903", "1904", "1905", "1906", "1907", "1908"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx", ["1909"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx", ["1910"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx", ["1911"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js", ["1912", "1913"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx", ["1914", "1915", "1916", "1917", "1918", "1919", "1920", "1921"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", ["1922"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["1923", "1924", "1925", "1926", "1927"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["1928", "1929", "1930"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["1931", "1932"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx", ["1933", "1934"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["1935", "1936", "1937"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx", ["1938"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx", ["1939", "1940", "1941", "1942"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx", ["1943"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx", ["1944"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx", ["1945", "1946", "1947", "1948", "1949", "1950", "1951", "1952"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx", ["1953"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx", ["1954", "1955", "1956", "1957", "1958", "1959", "1960", "1961", "1962", "1963", "1964", "1965"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx", ["1966"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx", ["1967", "1968", "1969"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal2.jsx", ["1970"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx", ["1971", "1972"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx", ["1973", "1974", "1975"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx", ["1976", "1977", "1978"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx", ["1979"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx", ["1980", "1981"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx", ["1982", "1983", "1984", "1985", "1986", "1987"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx", ["1988", "1989", "1990", "1991", "1992"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx", ["1993", "1994", "1995"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx", ["1996"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\ImageDropZone.jsx", ["1997"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SolutionEditor.jsx", ["1998", "1999"], [], {"ruleId": "2000", "severity": 1, "message": "2001", "line": 51, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 51, "endColumn": 27}, {"ruleId": "2000", "severity": 1, "message": "2004", "line": 4, "column": 23, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 27}, {"ruleId": "2000", "severity": 1, "message": "2005", "line": 8, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 25}, {"ruleId": "2000", "severity": 1, "message": "2006", "line": 11, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 11, "endColumn": 21}, {"ruleId": "2007", "severity": 1, "message": "2008", "line": 42, "column": 8, "nodeType": "2009", "endLine": 42, "endColumn": 24, "suggestions": "2010"}, {"ruleId": "2000", "severity": 1, "message": "2011", "line": 49, "column": 15, "nodeType": "2002", "messageId": "2003", "endLine": 49, "endColumn": 27}, {"ruleId": "2000", "severity": 1, "message": "2012", "line": 10, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 10, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2013", "line": 18, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 36}, {"ruleId": "2007", "severity": 1, "message": "2014", "line": 26, "column": 8, "nodeType": "2009", "endLine": 26, "endColumn": 19, "suggestions": "2015"}, {"ruleId": "2016", "severity": 1, "message": "2017", "line": 393, "column": 45, "nodeType": "2018", "endLine": 398, "endColumn": 47}, {"ruleId": "2000", "severity": 1, "message": "2019", "line": 6, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 11}, {"ruleId": "2000", "severity": 1, "message": "2020", "line": 6, "column": 27, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 31}, {"ruleId": "2000", "severity": 1, "message": "2021", "line": 7, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 25}, {"ruleId": "2000", "severity": 1, "message": "2022", "line": 8, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 32}, {"ruleId": "2000", "severity": 1, "message": "2023", "line": 88, "column": 23, "nodeType": "2002", "messageId": "2003", "endLine": 88, "endColumn": 31}, {"ruleId": "2000", "severity": 1, "message": "2024", "line": 14, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 14, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2013", "line": 15, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 36}, {"ruleId": "2000", "severity": 1, "message": "2025", "line": 2, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2026", "line": 3, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2027", "line": 5, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 39}, {"ruleId": "2000", "severity": 1, "message": "2028", "line": 15, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2029", "line": 16, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 16, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2030", "line": 24, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 24, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2031", "line": 30, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 30, "endColumn": 25}, {"ruleId": "2000", "severity": 1, "message": "2032", "line": 34, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 34, "endColumn": 23}, {"ruleId": "2007", "severity": 1, "message": "2033", "line": 73, "column": 31, "nodeType": "2002", "endLine": 73, "endColumn": 42}, {"ruleId": "2007", "severity": 1, "message": "2034", "line": 269, "column": 8, "nodeType": "2009", "endLine": 269, "endColumn": 18, "suggestions": "2035"}, {"ruleId": "2000", "severity": 1, "message": "2028", "line": 6, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2029", "line": 9, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 9, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2030", "line": 18, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2031", "line": 20, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 20, "endColumn": 25}, {"ruleId": "2000", "severity": 1, "message": "2036", "line": 26, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 26, "endColumn": 26}, {"ruleId": "2000", "severity": 1, "message": "2037", "line": 1, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2037", "line": 1, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2038", "line": 8, "column": 37, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 48}, {"ruleId": "2000", "severity": 1, "message": "2039", "line": 11, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 11, "endColumn": 30}, {"ruleId": "2000", "severity": 1, "message": "2040", "line": 22, "column": 18, "nodeType": "2002", "messageId": "2003", "endLine": 22, "endColumn": 25}, {"ruleId": "2000", "severity": 1, "message": "2041", "line": 23, "column": 21, "nodeType": "2002", "messageId": "2003", "endLine": 23, "endColumn": 31}, {"ruleId": "2000", "severity": 1, "message": "2042", "line": 24, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 24, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2043", "line": 25, "column": 25, "nodeType": "2002", "messageId": "2003", "endLine": 25, "endColumn": 39}, {"ruleId": "2000", "severity": 1, "message": "2044", "line": 26, "column": 30, "nodeType": "2002", "messageId": "2003", "endLine": 26, "endColumn": 49}, {"ruleId": "2000", "severity": 1, "message": "2045", "line": 27, "column": 21, "nodeType": "2002", "messageId": "2003", "endLine": 27, "endColumn": 26}, {"ruleId": "2000", "severity": 1, "message": "2046", "line": 27, "column": 41, "nodeType": "2002", "messageId": "2003", "endLine": 27, "endColumn": 51}, {"ruleId": "2000", "severity": 1, "message": "2047", "line": 31, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 31, "endColumn": 30}, {"ruleId": "2000", "severity": 1, "message": "2048", "line": 34, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 34, "endColumn": 35}, {"ruleId": "2000", "severity": 1, "message": "2049", "line": 38, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 38, "endColumn": 33}, {"ruleId": "2000", "severity": 1, "message": "2037", "line": 1, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2047", "line": 17, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 17, "endColumn": 30}, {"ruleId": "2000", "severity": 1, "message": "2048", "line": 20, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 20, "endColumn": 35}, {"ruleId": "2000", "severity": 1, "message": "2050", "line": 23, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 23, "endColumn": 32}, {"ruleId": "2000", "severity": 1, "message": "2027", "line": 6, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 39}, {"ruleId": "2000", "severity": 1, "message": "2013", "line": 15, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 36}, {"ruleId": "2000", "severity": 1, "message": "2037", "line": 1, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2025", "line": 2, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2051", "line": 6, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2052", "line": 7, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2053", "line": 8, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2047", "line": 25, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 25, "endColumn": 30}, {"ruleId": "2000", "severity": 1, "message": "2049", "line": 28, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 28, "endColumn": 33}, {"ruleId": "2000", "severity": 1, "message": "2050", "line": 31, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 31, "endColumn": 32}, {"ruleId": "2000", "severity": 1, "message": "2037", "line": 1, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2054", "line": 37, "column": 19, "nodeType": "2002", "messageId": "2003", "endLine": 37, "endColumn": 27}, {"ruleId": "2000", "severity": 1, "message": "2055", "line": 5, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 17}, {"ruleId": "2056", "severity": 1, "message": "2057", "line": 140, "column": 80, "nodeType": "2058", "messageId": "2059", "endLine": 140, "endColumn": 82}, {"ruleId": "2000", "severity": 1, "message": "2060", "line": 3, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2061", "line": 19, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 19, "endColumn": 22}, {"ruleId": "2007", "severity": 1, "message": "2062", "line": 18, "column": 8, "nodeType": "2009", "endLine": 18, "endColumn": 10, "suggestions": "2063"}, {"ruleId": "2000", "severity": 1, "message": "2064", "line": 1, "column": 20, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2065", "line": 4, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2066", "line": 11, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 11, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2067", "line": 12, "column": 36, "nodeType": "2002", "messageId": "2003", "endLine": 12, "endColumn": 45}, {"ruleId": "2000", "severity": 1, "message": "2068", "line": 13, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 13, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2069", "line": 13, "column": 16, "nodeType": "2002", "messageId": "2003", "endLine": 13, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2070", "line": 28, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 28, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2071", "line": 5, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2072", "line": 11, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 11, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2073", "line": 18, "column": 36, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 46}, {"ruleId": "2007", "severity": 1, "message": "2074", "line": 43, "column": 8, "nodeType": "2009", "endLine": 43, "endColumn": 26, "suggestions": "2075"}, {"ruleId": "2000", "severity": 1, "message": "2064", "line": 2, "column": 20, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2076", "line": 7, "column": 47, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 68}, {"ruleId": "2000", "severity": 1, "message": "2077", "line": 17, "column": 7, "nodeType": "2002", "messageId": "2003", "endLine": 17, "endColumn": 16}, {"ruleId": "2000", "severity": 1, "message": "2078", "line": 29, "column": 7, "nodeType": "2002", "messageId": "2003", "endLine": 29, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2079", "line": 36, "column": 7, "nodeType": "2002", "messageId": "2003", "endLine": 36, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2072", "line": 7, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2073", "line": 15, "column": 36, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 46}, {"ruleId": "2000", "severity": 1, "message": "2080", "line": 19, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 19, "endColumn": 37}, {"ruleId": "2000", "severity": 1, "message": "2073", "line": 15, "column": 36, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 46}, {"ruleId": "2007", "severity": 1, "message": "2081", "line": 34, "column": 8, "nodeType": "2009", "endLine": 34, "endColumn": 53, "suggestions": "2082"}, {"ruleId": "2000", "severity": 1, "message": "2083", "line": 12, "column": 55, "nodeType": "2002", "messageId": "2003", "endLine": 12, "endColumn": 61}, {"ruleId": "2000", "severity": 1, "message": "2084", "line": 12, "column": 77, "nodeType": "2002", "messageId": "2003", "endLine": 12, "endColumn": 82}, {"ruleId": "2000", "severity": 1, "message": "2085", "line": 12, "column": 84, "nodeType": "2002", "messageId": "2003", "endLine": 12, "endColumn": 94}, {"ruleId": "2000", "severity": 1, "message": "2030", "line": 18, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2032", "line": 20, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 20, "endColumn": 23}, {"ruleId": "2007", "severity": 1, "message": "2033", "line": 39, "column": 31, "nodeType": "2002", "endLine": 39, "endColumn": 42}, {"ruleId": "2000", "severity": 1, "message": "2086", "line": 96, "column": 17, "nodeType": "2002", "messageId": "2003", "endLine": 96, "endColumn": 21}, {"ruleId": "2056", "severity": 1, "message": "2057", "line": 412, "column": 68, "nodeType": "2058", "messageId": "2059", "endLine": 412, "endColumn": 70}, {"ruleId": "2000", "severity": 1, "message": "2026", "line": 2, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2027", "line": 7, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 39}, {"ruleId": "2000", "severity": 1, "message": "2087", "line": 24, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 24, "endColumn": 26}, {"ruleId": "2000", "severity": 1, "message": "2088", "line": 25, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 25, "endColumn": 30}, {"ruleId": "2000", "severity": 1, "message": "2089", "line": 47, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 47, "endColumn": 22}, {"ruleId": "2007", "severity": 1, "message": "2090", "line": 68, "column": 8, "nodeType": "2009", "endLine": 68, "endColumn": 30, "suggestions": "2091"}, {"ruleId": "2000", "severity": 1, "message": "2092", "line": 78, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 78, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2093", "line": 86, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 86, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2094", "line": 8, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 28}, {"ruleId": "2007", "severity": 1, "message": "2095", "line": 45, "column": 8, "nodeType": "2009", "endLine": 45, "endColumn": 32, "suggestions": "2096"}, {"ruleId": "2000", "severity": 1, "message": "2097", "line": 6, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2098", "line": 7, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2028", "line": 13, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 13, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2029", "line": 14, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 14, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2031", "line": 23, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 23, "endColumn": 25}, {"ruleId": "2000", "severity": 1, "message": "2099", "line": 5, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2098", "line": 6, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2094", "line": 7, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 28}, {"ruleId": "2007", "severity": 1, "message": "2100", "line": 57, "column": 8, "nodeType": "2009", "endLine": 57, "endColumn": 28, "suggestions": "2101"}, {"ruleId": "2000", "severity": 1, "message": "2049", "line": 71, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 71, "endColumn": 33}, {"ruleId": "2000", "severity": 1, "message": "2048", "line": 75, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 75, "endColumn": 35}, {"ruleId": "2000", "severity": 1, "message": "2050", "line": 79, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 79, "endColumn": 32}, {"ruleId": "2000", "severity": 1, "message": "2102", "line": 10, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 10, "endColumn": 17}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 290, "column": 59, "nodeType": "2105", "messageId": "2106", "endLine": 290, "endColumn": 60, "suggestions": "2107"}, {"ruleId": "2000", "severity": 1, "message": "2073", "line": 11, "column": 41, "nodeType": "2002", "messageId": "2003", "endLine": 11, "endColumn": 51}, {"ruleId": "2000", "severity": 1, "message": "2108", "line": 1, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 28}, {"ruleId": "2000", "severity": 1, "message": "2109", "line": 2, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2064", "line": 3, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2110", "line": 7, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2111", "line": 12, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 12, "endColumn": 31}, {"ruleId": "2000", "severity": 1, "message": "2112", "line": 1, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 13}, {"ruleId": "2000", "severity": 1, "message": "2113", "line": 2, "column": 37, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 45}, {"ruleId": "2000", "severity": 1, "message": "2114", "line": 2, "column": 47, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 60}, {"ruleId": "2000", "severity": 1, "message": "2115", "line": 2, "column": 62, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 67}, {"ruleId": "2000", "severity": 1, "message": "2116", "line": 2, "column": 69, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 77}, {"ruleId": "2000", "severity": 1, "message": "2117", "line": 3, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 26}, {"ruleId": "2007", "severity": 1, "message": "2118", "line": 36, "column": 8, "nodeType": "2009", "endLine": 36, "endColumn": 43, "suggestions": "2119"}, {"ruleId": "2000", "severity": 1, "message": "2112", "line": 1, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 13}, {"ruleId": "2000", "severity": 1, "message": "2012", "line": 7, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 18}, {"ruleId": "2007", "severity": 1, "message": "2120", "line": 25, "column": 8, "nodeType": "2009", "endLine": 25, "endColumn": 20, "suggestions": "2121"}, {"ruleId": "2000", "severity": 1, "message": "2122", "line": 1, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2123", "line": 32, "column": 83, "nodeType": "2002", "messageId": "2003", "endLine": 32, "endColumn": 91}, {"ruleId": "2000", "severity": 1, "message": "2124", "line": 121, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 121, "endColumn": 23}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 159, "column": 22, "nodeType": "2127", "messageId": "2128", "endLine": 159, "endColumn": 24}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 159, "column": 91, "nodeType": "2127", "messageId": "2128", "endLine": 159, "endColumn": 93}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 304, "column": 40, "nodeType": "2127", "messageId": "2128", "endLine": 304, "endColumn": 42}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 304, "column": 109, "nodeType": "2127", "messageId": "2128", "endLine": 304, "endColumn": 111}, {"ruleId": "2000", "severity": 1, "message": "2129", "line": 14, "column": 38, "nodeType": "2002", "messageId": "2003", "endLine": 14, "endColumn": 47}, {"ruleId": "2000", "severity": 1, "message": "2130", "line": 14, "column": 49, "nodeType": "2002", "messageId": "2003", "endLine": 14, "endColumn": 56}, {"ruleId": "2000", "severity": 1, "message": "2131", "line": 14, "column": 58, "nodeType": "2002", "messageId": "2003", "endLine": 14, "endColumn": 70}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 34, "column": 40, "nodeType": "2127", "messageId": "2128", "endLine": 34, "endColumn": 42}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 34, "column": 109, "nodeType": "2127", "messageId": "2128", "endLine": 34, "endColumn": 111}, {"ruleId": "2000", "severity": 1, "message": "2045", "line": 15, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2132", "line": 15, "column": 44, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 52}, {"ruleId": "2007", "severity": 1, "message": "2133", "line": 42, "column": 8, "nodeType": "2009", "endLine": 42, "endColumn": 40, "suggestions": "2134"}, {"ruleId": "2000", "severity": 1, "message": "2012", "line": 1, "column": 25, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 33}, {"ruleId": "2000", "severity": 1, "message": "2135", "line": 4, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2136", "line": 4, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2006", "line": 1, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2004", "line": 2, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2137", "line": 4, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2138", "line": 4, "column": 26, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 44}, {"ruleId": "2000", "severity": 1, "message": "2139", "line": 47, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 47, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2140", "line": 48, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 48, "endColumn": 26}, {"ruleId": "2000", "severity": 1, "message": "2141", "line": 50, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 50, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2142", "line": 59, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 59, "endColumn": 16}, {"ruleId": "2007", "severity": 1, "message": "2143", "line": 29, "column": 8, "nodeType": "2009", "endLine": 29, "endColumn": 10, "suggestions": "2144"}, {"ruleId": "2016", "severity": 1, "message": "2017", "line": 44, "column": 29, "nodeType": "2018", "endLine": 44, "endColumn": 98}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 238, "column": 30, "nodeType": "2105", "messageId": "2106", "endLine": 238, "endColumn": 31, "suggestions": "2145"}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 251, "column": 61, "nodeType": "2105", "messageId": "2106", "endLine": 251, "endColumn": 62, "suggestions": "2146"}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 257, "column": 29, "nodeType": "2105", "messageId": "2106", "endLine": 257, "endColumn": 30, "suggestions": "2147"}, {"ruleId": "2103", "severity": 1, "message": "2148", "line": 257, "column": 31, "nodeType": "2105", "messageId": "2106", "endLine": 257, "endColumn": 32, "suggestions": "2149"}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 258, "column": 51, "nodeType": "2105", "messageId": "2106", "endLine": 258, "endColumn": 52, "suggestions": "2150"}, {"ruleId": "2103", "severity": 1, "message": "2148", "line": 258, "column": 53, "nodeType": "2105", "messageId": "2106", "endLine": 258, "endColumn": 54, "suggestions": "2151"}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 346, "column": 30, "nodeType": "2105", "messageId": "2106", "endLine": 346, "endColumn": 31, "suggestions": "2152"}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 368, "column": 61, "nodeType": "2105", "messageId": "2106", "endLine": 368, "endColumn": 62, "suggestions": "2153"}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 374, "column": 29, "nodeType": "2105", "messageId": "2106", "endLine": 374, "endColumn": 30, "suggestions": "2154"}, {"ruleId": "2103", "severity": 1, "message": "2148", "line": 374, "column": 31, "nodeType": "2105", "messageId": "2106", "endLine": 374, "endColumn": 32, "suggestions": "2155"}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 375, "column": 51, "nodeType": "2105", "messageId": "2106", "endLine": 375, "endColumn": 52, "suggestions": "2156"}, {"ruleId": "2103", "severity": 1, "message": "2148", "line": 375, "column": 53, "nodeType": "2105", "messageId": "2106", "endLine": 375, "endColumn": 54, "suggestions": "2157"}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 456, "column": 30, "nodeType": "2105", "messageId": "2106", "endLine": 456, "endColumn": 31, "suggestions": "2158"}, {"ruleId": "2103", "severity": 1, "message": "2104", "line": 475, "column": 61, "nodeType": "2105", "messageId": "2106", "endLine": 475, "endColumn": 62, "suggestions": "2159"}, {"ruleId": "2007", "severity": 1, "message": "2160", "line": 80, "column": 8, "nodeType": "2009", "endLine": 80, "endColumn": 10, "suggestions": "2161"}, {"ruleId": "2016", "severity": 1, "message": "2017", "line": 56, "column": 37, "nodeType": "2018", "endLine": 56, "endColumn": 107}, {"ruleId": "2000", "severity": 1, "message": "2162", "line": 3, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 27}, {"ruleId": "2007", "severity": 1, "message": "2163", "line": 73, "column": 8, "nodeType": "2009", "endLine": 73, "endColumn": 25, "suggestions": "2164"}, {"ruleId": "2000", "severity": 1, "message": "2064", "line": 3, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2165", "line": 5, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2166", "line": 14, "column": 28, "nodeType": "2002", "messageId": "2003", "endLine": 14, "endColumn": 45}, {"ruleId": "2000", "severity": 1, "message": "2167", "line": 34, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 34, "endColumn": 20}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 116, "column": 56, "nodeType": "2058", "messageId": "2059", "endLine": 116, "endColumn": 58}, {"ruleId": "2000", "severity": 1, "message": "2109", "line": 2, "column": 23, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 34}, {"ruleId": "2000", "severity": 1, "message": "2169", "line": 4, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 28}, {"ruleId": "2000", "severity": 1, "message": "2170", "line": 5, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2171", "line": 5, "column": 33, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 40}, {"ruleId": "2000", "severity": 1, "message": "2172", "line": 5, "column": 42, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 51}, {"ruleId": "2000", "severity": 1, "message": "2173", "line": 8, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 21}, {"ruleId": "2174", "severity": 1, "message": "2175", "line": 12, "column": 25, "nodeType": "2018", "endLine": 12, "endColumn": 614}, {"ruleId": "2000", "severity": 1, "message": "2112", "line": 1, "column": 46, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 49}, {"ruleId": "2000", "severity": 1, "message": "2176", "line": 3, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2177", "line": 15, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2178", "line": 15, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 37}, {"ruleId": "2007", "severity": 1, "message": "2179", "line": 21, "column": 8, "nodeType": "2009", "endLine": 21, "endColumn": 26, "suggestions": "2180"}, {"ruleId": "2007", "severity": 1, "message": "2181", "line": 40, "column": 8, "nodeType": "2009", "endLine": 40, "endColumn": 10, "suggestions": "2182"}, {"ruleId": "2000", "severity": 1, "message": "2019", "line": 2, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 11}, {"ruleId": "2000", "severity": 1, "message": "2026", "line": 3, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2183", "line": 13, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 13, "endColumn": 23}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 290, "column": 56, "nodeType": "2058", "messageId": "2059", "endLine": 290, "endColumn": 58}, {"ruleId": "2000", "severity": 1, "message": "2184", "line": 15, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2185", "line": 19, "column": 22, "nodeType": "2002", "messageId": "2003", "endLine": 19, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2165", "line": 4, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2186", "line": 5, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 16}, {"ruleId": "2000", "severity": 1, "message": "2187", "line": 100, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 100, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2188", "line": 111, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 111, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2066", "line": 15, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2053", "line": 7, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 36}, {"ruleId": "2000", "severity": 1, "message": "2066", "line": 13, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 13, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2066", "line": 14, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 14, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2053", "line": 7, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 36}, {"ruleId": "2000", "severity": 1, "message": "2066", "line": 13, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 13, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2053", "line": 7, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 36}, {"ruleId": "2189", "severity": 1, "message": "2190", "line": 28, "column": 74, "nodeType": "2058", "messageId": "2191", "endLine": 28, "endColumn": 75}, {"ruleId": "2000", "severity": 1, "message": "2192", "line": 6, "column": 25, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2193", "line": 6, "column": 31, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 37}, {"ruleId": "2000", "severity": 1, "message": "2085", "line": 6, "column": 39, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 49}, {"ruleId": "2000", "severity": 1, "message": "2115", "line": 6, "column": 51, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 56}, {"ruleId": "2000", "severity": 1, "message": "2194", "line": 6, "column": 65, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 70}, {"ruleId": "2007", "severity": 1, "message": "2195", "line": 50, "column": 8, "nodeType": "2009", "endLine": 50, "endColumn": 28, "suggestions": "2196"}, {"ruleId": "2007", "severity": 1, "message": "2195", "line": 54, "column": 8, "nodeType": "2009", "endLine": 54, "endColumn": 21, "suggestions": "2197"}, {"ruleId": "2000", "severity": 1, "message": "2198", "line": 37, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 37, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2199", "line": 44, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 44, "endColumn": 29}, {"ruleId": "2007", "severity": 1, "message": "2200", "line": 47, "column": 9, "nodeType": "2201", "endLine": 51, "endColumn": 4}, {"ruleId": "2000", "severity": 1, "message": "2202", "line": 16, "column": 45, "nodeType": "2002", "messageId": "2003", "endLine": 16, "endColumn": 57}, {"ruleId": "2007", "severity": 1, "message": "2203", "line": 55, "column": 8, "nodeType": "2009", "endLine": 55, "endColumn": 14, "suggestions": "2204"}, {"ruleId": "2007", "severity": 1, "message": "2205", "line": 109, "column": 11, "nodeType": "2201", "endLine": 115, "endColumn": 6, "suggestions": "2206"}, {"ruleId": "2007", "severity": 1, "message": "2207", "line": 109, "column": 11, "nodeType": "2201", "endLine": 115, "endColumn": 6, "suggestions": "2208"}, {"ruleId": "2007", "severity": 1, "message": "2209", "line": 109, "column": 11, "nodeType": "2201", "endLine": 115, "endColumn": 6, "suggestions": "2210"}, {"ruleId": "2000", "severity": 1, "message": "2211", "line": 117, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 117, "endColumn": 27}, {"ruleId": "2007", "severity": 1, "message": "2212", "line": 232, "column": 8, "nodeType": "2009", "endLine": 232, "endColumn": 23, "suggestions": "2213"}, {"ruleId": "2000", "severity": 1, "message": "2214", "line": 283, "column": 19, "nodeType": "2002", "messageId": "2003", "endLine": 283, "endColumn": 25}, {"ruleId": "2007", "severity": 1, "message": "2033", "line": 371, "column": 37, "nodeType": "2002", "endLine": 371, "endColumn": 48}, {"ruleId": "2007", "severity": 1, "message": "2033", "line": 398, "column": 37, "nodeType": "2002", "endLine": 398, "endColumn": 48}, {"ruleId": "2215", "severity": 1, "message": "2216", "line": 629, "column": 60, "nodeType": "2217", "messageId": "2218", "endLine": 629, "endColumn": 61}, {"ruleId": "2007", "severity": 1, "message": "2219", "line": 785, "column": 8, "nodeType": "2009", "endLine": 785, "endColumn": 99, "suggestions": "2220"}, {"ruleId": "2007", "severity": 1, "message": "2221", "line": 893, "column": 8, "nodeType": "2009", "endLine": 893, "endColumn": 36, "suggestions": "2222"}, {"ruleId": "2000", "severity": 1, "message": "2223", "line": 2, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2224", "line": 4, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2225", "line": 18, "column": 25, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2226", "line": 18, "column": 137, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 146}, {"ruleId": "2000", "severity": 1, "message": "2227", "line": 18, "column": 148, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 161}, {"ruleId": "2000", "severity": 1, "message": "2228", "line": 18, "column": 163, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 173}, {"ruleId": "2000", "severity": 1, "message": "2229", "line": 18, "column": 175, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 182}, {"ruleId": "2000", "severity": 1, "message": "2230", "line": 18, "column": 184, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 189}, {"ruleId": "2000", "severity": 1, "message": "2231", "line": 18, "column": 191, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 205}, {"ruleId": "2000", "severity": 1, "message": "2232", "line": 34, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 34, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2233", "line": 39, "column": 7, "nodeType": "2002", "messageId": "2003", "endLine": 39, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2234", "line": 40, "column": 7, "nodeType": "2002", "messageId": "2003", "endLine": 40, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2235", "line": 64, "column": 7, "nodeType": "2002", "messageId": "2003", "endLine": 64, "endColumn": 17}, {"ruleId": "2000", "severity": 1, "message": "2236", "line": 65, "column": 7, "nodeType": "2002", "messageId": "2003", "endLine": 65, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2237", "line": 67, "column": 7, "nodeType": "2002", "messageId": "2003", "endLine": 67, "endColumn": 17}, {"ruleId": "2000", "severity": 1, "message": "2112", "line": 4, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 13}, {"ruleId": "2000", "severity": 1, "message": "2038", "line": 6, "column": 35, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 46}, {"ruleId": "2000", "severity": 1, "message": "2238", "line": 10, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 10, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2073", "line": 26, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 26, "endColumn": 21}, {"ruleId": "2007", "severity": 1, "message": "2203", "line": 37, "column": 8, "nodeType": "2009", "endLine": 37, "endColumn": 14, "suggestions": "2239"}, {"ruleId": "2000", "severity": 1, "message": "2135", "line": 15, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2240", "line": 18, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2019", "line": 21, "column": 98, "nodeType": "2002", "messageId": "2003", "endLine": 21, "endColumn": 99}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 262, "column": 38, "nodeType": "2058", "messageId": "2059", "endLine": 262, "endColumn": 40}, {"ruleId": "2000", "severity": 1, "message": "2029", "line": 7, "column": 111, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 115}, {"ruleId": "2000", "severity": 1, "message": "2241", "line": 7, "column": 117, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 127}, {"ruleId": "2000", "severity": 1, "message": "2122", "line": 10, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 10, "endColumn": 24}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 178, "column": 123, "nodeType": "2127", "messageId": "2128", "endLine": 178, "endColumn": 125}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 178, "column": 197, "nodeType": "2127", "messageId": "2128", "endLine": 178, "endColumn": 199}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 187, "column": 120, "nodeType": "2127", "messageId": "2128", "endLine": 187, "endColumn": 122}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 187, "column": 189, "nodeType": "2127", "messageId": "2128", "endLine": 187, "endColumn": 191}, {"ruleId": "2000", "severity": 1, "message": "2113", "line": 15, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 13}, {"ruleId": "2000", "severity": 1, "message": "2083", "line": 17, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 17, "endColumn": 11}, {"ruleId": "2000", "severity": 1, "message": "2029", "line": 20, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 20, "endColumn": 9}, {"ruleId": "2000", "severity": 1, "message": "2242", "line": 21, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 21, "endColumn": 17}, {"ruleId": "2000", "severity": 1, "message": "2084", "line": 24, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 24, "endColumn": 10}, {"ruleId": "2000", "severity": 1, "message": "2135", "line": 28, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 28, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2243", "line": 30, "column": 7, "nodeType": "2002", "messageId": "2003", "endLine": 30, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2244", "line": 159, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 159, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2245", "line": 160, "column": 21, "nodeType": "2002", "messageId": "2003", "endLine": 160, "endColumn": 31}, {"ruleId": "2007", "severity": 1, "message": "2246", "line": 166, "column": 8, "nodeType": "2009", "endLine": 166, "endColumn": 18, "suggestions": "2247"}, {"ruleId": "2000", "severity": 1, "message": "2248", "line": 15, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 16}, {"ruleId": "2000", "severity": 1, "message": "2249", "line": 16, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 16, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2250", "line": 26, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 26, "endColumn": 18}, {"ruleId": "2007", "severity": 1, "message": "2251", "line": 199, "column": 8, "nodeType": "2009", "endLine": 199, "endColumn": 21, "suggestions": "2252"}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 49, "column": 37, "nodeType": "2058", "messageId": "2059", "endLine": 49, "endColumn": 39}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 51, "column": 44, "nodeType": "2058", "messageId": "2059", "endLine": 51, "endColumn": 46}, {"ruleId": "2000", "severity": 1, "message": "2253", "line": 8, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 16}, {"ruleId": "2000", "severity": 1, "message": "2019", "line": 8, "column": 18, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2116", "line": 8, "column": 21, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2254", "line": 15, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 17}, {"ruleId": "2000", "severity": 1, "message": "2255", "line": 16, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 16, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2256", "line": 26, "column": 24, "nodeType": "2002", "messageId": "2003", "endLine": 26, "endColumn": 37}, {"ruleId": "2000", "severity": 1, "message": "2257", "line": 111, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 111, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2135", "line": 7, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2029", "line": 13, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 13, "endColumn": 9}, {"ruleId": "2000", "severity": 1, "message": "2113", "line": 17, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 17, "endColumn": 13}, {"ruleId": "2000", "severity": 1, "message": "2258", "line": 19, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 19, "endColumn": 13}, {"ruleId": "2007", "severity": 1, "message": "2259", "line": 129, "column": 8, "nodeType": "2009", "endLine": 129, "endColumn": 18, "suggestions": "2260"}, {"ruleId": "2000", "severity": 1, "message": "2261", "line": 155, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 155, "endColumn": 32}, {"ruleId": "2000", "severity": 1, "message": "2262", "line": 232, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 232, "endColumn": 29}, {"ruleId": "2007", "severity": 1, "message": "2263", "line": 39, "column": 8, "nodeType": "2009", "endLine": 39, "endColumn": 30, "suggestions": "2264"}, {"ruleId": "2016", "severity": 1, "message": "2017", "line": 226, "column": 69, "nodeType": "2018", "endLine": 230, "endColumn": 71}, {"ruleId": "2056", "severity": 1, "message": "2057", "line": 49, "column": 108, "nodeType": "2058", "messageId": "2059", "endLine": 49, "endColumn": 110}, {"ruleId": "2000", "severity": 1, "message": "2045", "line": 55, "column": 42, "nodeType": "2002", "messageId": "2003", "endLine": 55, "endColumn": 47}, {"ruleId": "2007", "severity": 1, "message": "2195", "line": 88, "column": 8, "nodeType": "2009", "endLine": 88, "endColumn": 28, "suggestions": "2265"}, {"ruleId": "2007", "severity": 1, "message": "2195", "line": 92, "column": 8, "nodeType": "2009", "endLine": 92, "endColumn": 21, "suggestions": "2266"}, {"ruleId": "2000", "severity": 1, "message": "2267", "line": 7, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2268", "line": 8, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 12}, {"ruleId": "2000", "severity": 1, "message": "2269", "line": 13, "column": 3, "nodeType": "2002", "messageId": "2003", "endLine": 13, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2166", "line": 14, "column": 3, "nodeType": "2002", "messageId": "2003", "endLine": 14, "endColumn": 20}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 285, "column": 65, "nodeType": "2058", "messageId": "2059", "endLine": 285, "endColumn": 67}, {"ruleId": "2000", "severity": 1, "message": "2270", "line": 28, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 28, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2085", "line": 29, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 29, "endColumn": 15}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 447, "column": 101, "nodeType": "2058", "messageId": "2059", "endLine": 447, "endColumn": 103}, {"ruleId": "2000", "severity": 1, "message": "2271", "line": 55, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 55, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2272", "line": 55, "column": 32, "nodeType": "2002", "messageId": "2003", "endLine": 55, "endColumn": 42}, {"ruleId": "2000", "severity": 1, "message": "2273", "line": 59, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 59, "endColumn": 27}, {"ruleId": "2000", "severity": 1, "message": "2274", "line": 70, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 70, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2275", "line": 82, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 82, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2276", "line": 88, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 88, "endColumn": 30}, {"ruleId": "2000", "severity": 1, "message": "2261", "line": 94, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 94, "endColumn": 32}, {"ruleId": "2056", "severity": 1, "message": "2057", "line": 336, "column": 35, "nodeType": "2058", "messageId": "2059", "endLine": 336, "endColumn": 37}, {"ruleId": "2000", "severity": 1, "message": "2072", "line": 9, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 9, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2277", "line": 11, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 11, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2083", "line": 19, "column": 3, "nodeType": "2002", "messageId": "2003", "endLine": 19, "endColumn": 9}, {"ruleId": "2000", "severity": 1, "message": "2073", "line": 31, "column": 30, "nodeType": "2002", "messageId": "2003", "endLine": 31, "endColumn": 40}, {"ruleId": "2000", "severity": 1, "message": "2278", "line": 4, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2279", "line": 5, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 14}, {"ruleId": "2000", "severity": 1, "message": "2280", "line": 17, "column": 26, "nodeType": "2002", "messageId": "2003", "endLine": 17, "endColumn": 46}, {"ruleId": "2000", "severity": 1, "message": "2281", "line": 24, "column": 96, "nodeType": "2002", "messageId": "2003", "endLine": 24, "endColumn": 100}, {"ruleId": "2000", "severity": 1, "message": "2083", "line": 24, "column": 102, "nodeType": "2002", "messageId": "2003", "endLine": 24, "endColumn": 108}, {"ruleId": "2000", "severity": 1, "message": "2025", "line": 26, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 26, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2282", "line": 27, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 27, "endColumn": 13}, {"ruleId": "2000", "severity": 1, "message": "2030", "line": 34, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 34, "endColumn": 17}, {"ruleId": "2000", "severity": 1, "message": "2283", "line": 35, "column": 47, "nodeType": "2002", "messageId": "2003", "endLine": 35, "endColumn": 54}, {"ruleId": "2000", "severity": 1, "message": "2073", "line": 37, "column": 17, "nodeType": "2002", "messageId": "2003", "endLine": 37, "endColumn": 27}, {"ruleId": "2000", "severity": 1, "message": "2284", "line": 91, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 91, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2285", "line": 92, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 92, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2286", "line": 98, "column": 20, "nodeType": "2002", "messageId": "2003", "endLine": 98, "endColumn": 31}, {"ruleId": "2000", "severity": 1, "message": "2287", "line": 99, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 99, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2288", "line": 99, "column": 22, "nodeType": "2002", "messageId": "2003", "endLine": 99, "endColumn": 35}, {"ruleId": "2000", "severity": 1, "message": "2289", "line": 100, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 100, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2290", "line": 100, "column": 20, "nodeType": "2002", "messageId": "2003", "endLine": 100, "endColumn": 31}, {"ruleId": "2000", "severity": 1, "message": "2291", "line": 103, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 103, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2292", "line": 104, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 104, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2293", "line": 105, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 105, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2294", "line": 106, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 106, "endColumn": 27}, {"ruleId": "2000", "severity": 1, "message": "2295", "line": 136, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 136, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2296", "line": 137, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 137, "endColumn": 27}, {"ruleId": "2000", "severity": 1, "message": "2297", "line": 139, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 139, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2298", "line": 139, "column": 25, "nodeType": "2002", "messageId": "2003", "endLine": 139, "endColumn": 41}, {"ruleId": "2000", "severity": 1, "message": "2299", "line": 167, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 167, "endColumn": 25}, {"ruleId": "2000", "severity": 1, "message": "2300", "line": 173, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 173, "endColumn": 33}, {"ruleId": "2007", "severity": 1, "message": "2301", "line": 286, "column": 6, "nodeType": "2009", "endLine": 286, "endColumn": 32, "suggestions": "2302"}, {"ruleId": "2000", "severity": 1, "message": "2303", "line": 364, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 364, "endColumn": 23}, {"ruleId": "2007", "severity": 1, "message": "2304", "line": 742, "column": 6, "nodeType": "2009", "endLine": 742, "endColumn": 25, "suggestions": "2305"}, {"ruleId": "2000", "severity": 1, "message": "2306", "line": 764, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 764, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2307", "line": 776, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 776, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2277", "line": 8, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2084", "line": 19, "column": 3, "nodeType": "2002", "messageId": "2003", "endLine": 19, "endColumn": 8}, {"ruleId": "2000", "severity": 1, "message": "2308", "line": 20, "column": 3, "nodeType": "2002", "messageId": "2003", "endLine": 20, "endColumn": 10}, {"ruleId": "2000", "severity": 1, "message": "2309", "line": 30, "column": 36, "nodeType": "2002", "messageId": "2003", "endLine": 30, "endColumn": 56}, {"ruleId": "2000", "severity": 1, "message": "2310", "line": 32, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 32, "endColumn": 30}, {"ruleId": "2000", "severity": 1, "message": "2311", "line": 32, "column": 32, "nodeType": "2002", "messageId": "2003", "endLine": 32, "endColumn": 55}, {"ruleId": "2000", "severity": 1, "message": "2312", "line": 2, "column": 26, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 32}, {"ruleId": "2007", "severity": 1, "message": "2033", "line": 35, "column": 29, "nodeType": "2002", "endLine": 35, "endColumn": 40}, {"ruleId": "2007", "severity": 1, "message": "2033", "line": 35, "column": 30, "nodeType": "2002", "endLine": 35, "endColumn": 41}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 347, "column": 68, "nodeType": "2058", "messageId": "2059", "endLine": 347, "endColumn": 70}, {"ruleId": "2056", "severity": 1, "message": "2057", "line": 360, "column": 64, "nodeType": "2058", "messageId": "2059", "endLine": 360, "endColumn": 66}, {"ruleId": "2000", "severity": 1, "message": "2112", "line": 1, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 13}, {"ruleId": "2000", "severity": 1, "message": "2313", "line": 12, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 12, "endColumn": 13}, {"ruleId": "2000", "severity": 1, "message": "2028", "line": 24, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 24, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2029", "line": 26, "column": 119, "nodeType": "2002", "messageId": "2003", "endLine": 26, "endColumn": 123}, {"ruleId": "2000", "severity": 1, "message": "2314", "line": 26, "column": 135, "nodeType": "2002", "messageId": "2003", "endLine": 26, "endColumn": 140}, {"ruleId": "2000", "severity": 1, "message": "2030", "line": 99, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 99, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2073", "line": 107, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 107, "endColumn": 19}, {"ruleId": "2007", "severity": 1, "message": "2315", "line": 217, "column": 8, "nodeType": "2009", "endLine": 217, "endColumn": 38, "suggestions": "2316"}, {"ruleId": "2007", "severity": 1, "message": "2317", "line": 67, "column": 8, "nodeType": "2009", "endLine": 67, "endColumn": 47, "suggestions": "2318"}, {"ruleId": "2000", "severity": 1, "message": "2037", "line": 4, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 19}, {"ruleId": "2000", "severity": 1, "message": "2083", "line": 17, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 17, "endColumn": 11}, {"ruleId": "2000", "severity": 1, "message": "2020", "line": 20, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 20, "endColumn": 9}, {"ruleId": "2000", "severity": 1, "message": "2319", "line": 27, "column": 5, "nodeType": "2002", "messageId": "2003", "endLine": 27, "endColumn": 32}, {"ruleId": "2007", "severity": 1, "message": "2320", "line": 122, "column": 8, "nodeType": "2009", "endLine": 122, "endColumn": 10, "suggestions": "2321"}, {"ruleId": "2000", "severity": 1, "message": "2116", "line": 6, "column": 56, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 64}, {"ruleId": "2000", "severity": 1, "message": "2193", "line": 6, "column": 73, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 79}, {"ruleId": "2000", "severity": 1, "message": "2085", "line": 6, "column": 81, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 91}, {"ruleId": "2000", "severity": 1, "message": "2322", "line": 5, "column": 60, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 66}, {"ruleId": "2000", "severity": 1, "message": "2253", "line": 5, "column": 68, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 74}, {"ruleId": "2000", "severity": 1, "message": "2323", "line": 2, "column": 42, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 51}, {"ruleId": "2000", "severity": 1, "message": "2324", "line": 5, "column": 11, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 24}, {"ruleId": "2000", "severity": 1, "message": "2135", "line": 8, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 22}, {"ruleId": "2000", "severity": 1, "message": "2283", "line": 16, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 16, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2325", "line": 17, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 17, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2012", "line": 1, "column": 21, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2326", "line": 3, "column": 58, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 69}, {"ruleId": "2000", "severity": 1, "message": "2327", "line": 3, "column": 71, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 81}, {"ruleId": "2000", "severity": 1, "message": "2328", "line": 48, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 48, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2329", "line": 56, "column": 9, "nodeType": "2002", "messageId": "2003", "endLine": 56, "endColumn": 29}, {"ruleId": "2007", "severity": 1, "message": "2033", "line": 37, "column": 30, "nodeType": "2002", "endLine": 37, "endColumn": 41}, {"ruleId": "2007", "severity": 1, "message": "2033", "line": 35, "column": 32, "nodeType": "2002", "endLine": 35, "endColumn": 43}, {"ruleId": "2000", "severity": 1, "message": "2116", "line": 3, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2084", "line": 3, "column": 20, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 25}, {"ruleId": "2000", "severity": 1, "message": "2330", "line": 3, "column": 27, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 38}, {"ruleId": "2000", "severity": 1, "message": "2136", "line": 3, "column": 40, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 52}, {"ruleId": "2000", "severity": 1, "message": "2326", "line": 3, "column": 54, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 65}, {"ruleId": "2000", "severity": 1, "message": "2331", "line": 6, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 6, "endColumn": 32}, {"ruleId": "2000", "severity": 1, "message": "2332", "line": 8, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2333", "line": 8, "column": 25, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 39}, {"ruleId": "2000", "severity": 1, "message": "2012", "line": 2, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2116", "line": 3, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2330", "line": 3, "column": 27, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 38}, {"ruleId": "2000", "severity": 1, "message": "2136", "line": 3, "column": 40, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 52}, {"ruleId": "2000", "severity": 1, "message": "2250", "line": 4, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 4, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2334", "line": 5, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 30}, {"ruleId": "2000", "severity": 1, "message": "2335", "line": 7, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 21}, {"ruleId": "2000", "severity": 1, "message": "2332", "line": 8, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2336", "line": 9, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 9, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2283", "line": 15, "column": 39, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 46}, {"ruleId": "2000", "severity": 1, "message": "2183", "line": 17, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 17, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2337", "line": 18, "column": 37, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 48}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 181, "column": 54, "nodeType": "2058", "messageId": "2059", "endLine": 181, "endColumn": 56}, {"ruleId": "2007", "severity": 1, "message": "2338", "line": 57, "column": 8, "nodeType": "2009", "endLine": 57, "endColumn": 38, "suggestions": "2339"}, {"ruleId": "2000", "severity": 1, "message": "2112", "line": 1, "column": 17, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2340", "line": 2, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 26}, {"ruleId": "2056", "severity": 1, "message": "2168", "line": 186, "column": 50, "nodeType": "2058", "messageId": "2059", "endLine": 186, "endColumn": 52}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2341", "line": 277, "column": 5, "nodeType": null}, {"ruleId": "2000", "severity": 1, "message": "2330", "line": 1, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 21}, {"ruleId": "2007", "severity": 1, "message": "2342", "line": 51, "column": 8, "nodeType": "2009", "endLine": 51, "endColumn": 10, "suggestions": "2343"}, {"ruleId": "2000", "severity": 1, "message": "2112", "line": 1, "column": 38, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 41}, {"ruleId": "2000", "severity": 1, "message": "2344", "line": 14, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 14, "endColumn": 28}, {"ruleId": "2000", "severity": 1, "message": "2345", "line": 15, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 15, "endColumn": 22}, {"ruleId": "2007", "severity": 1, "message": "2346", "line": 75, "column": 8, "nodeType": "2009", "endLine": 75, "endColumn": 36, "suggestions": "2347"}, {"ruleId": "2007", "severity": 1, "message": "2346", "line": 80, "column": 8, "nodeType": "2009", "endLine": 80, "endColumn": 26, "suggestions": "2348"}, {"ruleId": "2007", "severity": 1, "message": "2349", "line": 98, "column": 8, "nodeType": "2009", "endLine": 98, "endColumn": 17, "suggestions": "2350"}, {"ruleId": "2000", "severity": 1, "message": "2111", "line": 18, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 18, "endColumn": 31}, {"ruleId": "2000", "severity": 1, "message": "2111", "line": 5, "column": 8, "nodeType": "2002", "messageId": "2003", "endLine": 5, "endColumn": 31}, {"ruleId": "2000", "severity": 1, "message": "2351", "line": 8, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 8, "endColumn": 16}, {"ruleId": "2000", "severity": 1, "message": "2352", "line": 3, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 20}, {"ruleId": "2000", "severity": 1, "message": "2353", "line": 3, "column": 22, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 29}, {"ruleId": "2000", "severity": 1, "message": "2354", "line": 3, "column": 31, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 38}, {"ruleId": "2000", "severity": 1, "message": "2355", "line": 3, "column": 40, "nodeType": "2002", "messageId": "2003", "endLine": 3, "endColumn": 50}, {"ruleId": "2000", "severity": 1, "message": "2356", "line": 7, "column": 12, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 17}, {"ruleId": "2000", "severity": 1, "message": "2357", "line": 7, "column": 19, "nodeType": "2002", "messageId": "2003", "endLine": 7, "endColumn": 27}, {"ruleId": "2000", "severity": 1, "message": "2012", "line": 1, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 18}, {"ruleId": "2000", "severity": 1, "message": "2112", "line": 1, "column": 31, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 34}, {"ruleId": "2000", "severity": 1, "message": "2358", "line": 75, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 75, "endColumn": 28}, {"ruleId": "2000", "severity": 1, "message": "2283", "line": 75, "column": 30, "nodeType": "2002", "messageId": "2003", "endLine": 75, "endColumn": 37}, {"ruleId": "2000", "severity": 1, "message": "2272", "line": 75, "column": 39, "nodeType": "2002", "messageId": "2003", "endLine": 75, "endColumn": 49}, {"ruleId": "2000", "severity": 1, "message": "2067", "line": 13, "column": 45, "nodeType": "2002", "messageId": "2003", "endLine": 13, "endColumn": 54}, {"ruleId": "2000", "severity": 1, "message": "2359", "line": 66, "column": 13, "nodeType": "2002", "messageId": "2003", "endLine": 66, "endColumn": 25}, {"ruleId": "2000", "severity": 1, "message": "2046", "line": 67, "column": 26, "nodeType": "2002", "messageId": "2003", "endLine": 67, "endColumn": 36}, {"ruleId": "2000", "severity": 1, "message": "2351", "line": 20, "column": 10, "nodeType": "2002", "messageId": "2003", "endLine": 20, "endColumn": 16}, {"ruleId": "2016", "severity": 1, "message": "2017", "line": 77, "column": 21, "nodeType": "2018", "endLine": 81, "endColumn": 23}, {"ruleId": "2000", "severity": 1, "message": "2064", "line": 1, "column": 35, "nodeType": "2002", "messageId": "2003", "endLine": 1, "endColumn": 44}, {"ruleId": "2000", "severity": 1, "message": "2360", "line": 2, "column": 21, "nodeType": "2002", "messageId": "2003", "endLine": 2, "endColumn": 27}, "no-unused-vars", "'AdminUserSearchPage' is defined but never used.", "Identifier", "unusedVar", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'BeeMathLogo' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.username'. Either include it or remove the dependency array.", "ArrayExpression", ["2361"], "'resultAction' is assigned a value but never used.", "'useState' is defined but never used.", "'isFilterVIew' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", ["2362"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'setIsFilterView' is defined but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "'loadingExam' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2363"], "'showAddStudent' is assigned a value but never used.", "'AdminLayout' is defined but never used.", "'setAttempts' is defined but never used.", "'ScoreDistributionChart' is defined but never used.", "'setLogs' is assigned a value but never used.", "'setAnswers' is assigned a value but never used.", "'prevRanks' is assigned a value but never used.", "'setRankChanges' is assigned a value but never used.", "'setUpdatedAttemptId' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'handleClickedDetail' is assigned a value but never used.", "'handleClickedPreviewExam' is assigned a value but never used.", "'handleClickedQuestions' is assigned a value but never used.", "'handleClickedTracking' is assigned a value but never used.", "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'errorMsg' is assigned a value but never used.", "'setExam' is defined but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'backgroundImage' is defined but never used.", "'shouldFloat' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2364"], "'useEffect' is defined but never used.", "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'setSortOrder' is defined but never used.", "'resetFilters' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2365"], "'processInputForUpdate' is defined but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'setDeleteMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2366"], "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2367"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2368"], "'setClass' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "'LatexRenderer' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2369"], "'compose' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["2370", "2371"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'use' is defined but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2372"], "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2373"], "'ExamDefaultImage' is defined but never used.", "'imageUrl' is assigned a value but never used.", "'ActionButton' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2374"], "'LoadingSpinner' is defined but never used.", "'ChevronRight' is defined but never used.", "'toggleDropdown' is defined but never used.", "'toggleExamDropdown' is defined but never used.", "'dropdown' is assigned a value but never used.", "'tuitionDropdown' is assigned a value but never used.", "'examDropdown' is assigned a value but never used.", "'icon5' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2375"], ["2376", "2377"], ["2378", "2379"], ["2380", "2381"], "Unnecessary escape character: \\).", ["2382", "2383"], ["2384", "2385"], ["2386", "2387"], ["2388", "2389"], ["2390", "2391"], ["2392", "2393"], ["2394", "2395"], ["2396", "2397"], ["2398", "2399"], ["2400", "2401"], ["2402", "2403"], "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2404"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2405"], "'InputSearch' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'menuOpen' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'toggleCloseSidebar' is defined but never used.", "'ArrowRight' is defined but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "'isHovered' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2406"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2407"], "'currentTime' is assigned a value but never used.", "'setSuccess' is defined but never used.", "'success' is assigned a value but never used.", "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchExams' and 'isClassroomExam'. Either include them or remove the dependency array.", ["2408"], ["2409"], "'isMobile' is assigned a value but never used.", "'currentQuestionType' is assigned a value but never used.", "The 'allQuestions' array makes the dependencies of useEffect Hook (at line 160) change on every render. To fix this, wrap the initialization of 'allQuestions' in its own useMemo() Hook.", "VariableDeclarator", "'isFullscreen' is defined but never used.", "React Hook useEffect has missing dependencies: 'examId' and 'navigate'. Either include them or remove the dependency array.", ["2410"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 668) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2411"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 679) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2412"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 691) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2413"], "'addErrorQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'flag'. Either include it or remove the dependency array.", ["2414"], "'result' is assigned a value but never used.", "no-sequences", "Unexpected use of comma operator.", "SequenceExpression", "unexpectedCommaExpression", "React Hook useEffect has a missing dependency: 'handleAutoSubmit'. Either include it or remove the dependency array.", ["2415"], "React Hook useEffect has missing dependencies: 'dispatch', 'exam?.isCheatingCheckEnabled', 'user.firstName', and 'user.lastName'. Either include them or remove the dependency array.", ["2416"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'ScoreBarChart' is defined but never used.", ["2417"], "'NoDataFound' is defined but never used.", "'BookMarked' is defined but never used.", "'ExternalLink' is defined but never used.", "'ButtonSidebar' is assigned a value but never used.", "'choice' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2418"], "'ChevronDown' is defined but never used.", "'ChevronUp' is defined but never used.", "'UserLayout' is defined but never used.", "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2419"], "'Filter' is defined but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2420"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2421"], ["2422"], ["2423"], "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'BarChart2' is defined but never used.", "'articles' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'formatNumberWithDots' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'loading' is assigned a value but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterGraduationYear', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2424"], "'handleBatchAdd' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tuitionPayments'. Either include it or remove the dependency array.", ["2425"], "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'Receipt' is defined but never used.", "'studentClassTuitions' is assigned a value but never used.", "'classTuitionsLoading' is assigned a value but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2426"], "React Hook useEffect has a missing dependency: 'user?.id'. Either include it or remove the dependency array.", ["2427"], "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2428"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", "'CheckCircle' is defined but never used.", "'DollarSign' is defined but never used.", "'getStatusBadge' is assigned a value but never used.", "'getPaymentStatusIcon' is assigned a value but never used.", "'ChevronLeft' is defined but never used.", "'getLearningItemWeekend' is defined but never used.", "'resetCalendar' is defined but never used.", "'setSelectedDay' is defined but never used.", "'fetchClassesOverview' is defined but never used.", "'CalendarMonth' is defined but never used.", "'SidebarCalendar' is defined but never used.", "'selectedDay' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'firstGridDate' and 'lastGridDate'. Either include them or remove the dependency array.", ["2429"], "'NavigateTimeButton' is defined but never used.", "Parsing error: Unexpected token (277:5)", "React Hook useEffect has a missing dependency: 'onClose'. Either include it or remove the dependency array. If 'onClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2430"], "'selectedQuestion' is assigned a value but never used.", "'editedText' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'isChange'. Either include them or remove the dependency array.", ["2431"], ["2432"], "React Hook useEffect has missing dependencies: 'dispatch' and 'exam.id'. Either include them or remove the dependency array.", ["2433"], "'Trash2' is defined but never used.", "'TableAdmin' is defined but never used.", "'TdAdmin' is defined but never used.", "'ThAdmin' is defined but never used.", "'TheadAdmin' is defined but never used.", "'month' is assigned a value but never used.", "'setMonth' is assigned a value but never used.", "'tuitionPayments' is assigned a value but never used.", "'userAttempts' is assigned a value but never used.", "'Upload' is defined but never used.", {"desc": "2434", "fix": "2435"}, {"desc": "2436", "fix": "2437"}, {"desc": "2438", "fix": "2439"}, {"desc": "2440", "fix": "2441"}, {"desc": "2442", "fix": "2443"}, {"desc": "2444", "fix": "2445"}, {"desc": "2446", "fix": "2447"}, {"desc": "2448", "fix": "2449"}, {"desc": "2450", "fix": "2451"}, {"messageId": "2452", "fix": "2453", "desc": "2454"}, {"messageId": "2455", "fix": "2456", "desc": "2457"}, {"desc": "2458", "fix": "2459"}, {"desc": "2460", "fix": "2461"}, {"desc": "2462", "fix": "2463"}, {"desc": "2464", "fix": "2465"}, {"messageId": "2452", "fix": "2466", "desc": "2454"}, {"messageId": "2455", "fix": "2467", "desc": "2457"}, {"messageId": "2452", "fix": "2468", "desc": "2454"}, {"messageId": "2455", "fix": "2469", "desc": "2457"}, {"messageId": "2452", "fix": "2470", "desc": "2454"}, {"messageId": "2455", "fix": "2471", "desc": "2457"}, {"messageId": "2452", "fix": "2472", "desc": "2454"}, {"messageId": "2455", "fix": "2473", "desc": "2457"}, {"messageId": "2452", "fix": "2474", "desc": "2454"}, {"messageId": "2455", "fix": "2475", "desc": "2457"}, {"messageId": "2452", "fix": "2476", "desc": "2454"}, {"messageId": "2455", "fix": "2477", "desc": "2457"}, {"messageId": "2452", "fix": "2478", "desc": "2454"}, {"messageId": "2455", "fix": "2479", "desc": "2457"}, {"messageId": "2452", "fix": "2480", "desc": "2454"}, {"messageId": "2455", "fix": "2481", "desc": "2457"}, {"messageId": "2452", "fix": "2482", "desc": "2454"}, {"messageId": "2455", "fix": "2483", "desc": "2457"}, {"messageId": "2452", "fix": "2484", "desc": "2454"}, {"messageId": "2455", "fix": "2485", "desc": "2457"}, {"messageId": "2452", "fix": "2486", "desc": "2454"}, {"messageId": "2455", "fix": "2487", "desc": "2457"}, {"messageId": "2452", "fix": "2488", "desc": "2454"}, {"messageId": "2455", "fix": "2489", "desc": "2457"}, {"messageId": "2452", "fix": "2490", "desc": "2454"}, {"messageId": "2455", "fix": "2491", "desc": "2457"}, {"messageId": "2452", "fix": "2492", "desc": "2454"}, {"messageId": "2455", "fix": "2493", "desc": "2457"}, {"desc": "2494", "fix": "2495"}, {"desc": "2496", "fix": "2497"}, {"desc": "2498", "fix": "2499"}, {"desc": "2500", "fix": "2501"}, {"desc": "2502", "fix": "2503"}, {"desc": "2504", "fix": "2505"}, {"desc": "2506", "fix": "2507"}, {"desc": "2508", "fix": "2509"}, {"desc": "2508", "fix": "2510"}, {"desc": "2508", "fix": "2511"}, {"desc": "2512", "fix": "2513"}, {"desc": "2514", "fix": "2515"}, {"desc": "2516", "fix": "2517"}, {"desc": "2506", "fix": "2518"}, {"desc": "2519", "fix": "2520"}, {"desc": "2521", "fix": "2522"}, {"desc": "2523", "fix": "2524"}, {"desc": "2525", "fix": "2526"}, {"desc": "2502", "fix": "2527"}, {"desc": "2504", "fix": "2528"}, {"desc": "2529", "fix": "2530"}, {"desc": "2531", "fix": "2532"}, {"desc": "2533", "fix": "2534"}, {"desc": "2535", "fix": "2536"}, {"desc": "2537", "fix": "2538"}, {"desc": "2539", "fix": "2540"}, {"desc": "2541", "fix": "2542"}, {"desc": "2543", "fix": "2544"}, {"desc": "2545", "fix": "2546"}, {"desc": "2547", "fix": "2548"}, "Update the dependencies array to be: [user, navigate, formData.username]", {"range": "2549", "text": "2550"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "2551", "text": "2552"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "2553", "text": "2554"}, "Update the dependencies array to be: [options, selected, type]", {"range": "2555", "text": "2556"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "2557", "text": "2558"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "2559", "text": "2560"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "2561", "text": "2562"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "2563", "text": "2564"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "2565", "text": "2566"}, "removeEscape", {"range": "2567", "text": "2568"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2569", "text": "2570"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "2571", "text": "2572"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "2573", "text": "2574"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "2575", "text": "2576"}, "Update the dependencies array to be: [maxLength]", {"range": "2577", "text": "2578"}, {"range": "2579", "text": "2568"}, {"range": "2580", "text": "2570"}, {"range": "2581", "text": "2568"}, {"range": "2582", "text": "2570"}, {"range": "2583", "text": "2568"}, {"range": "2584", "text": "2570"}, {"range": "2585", "text": "2568"}, {"range": "2586", "text": "2570"}, {"range": "2587", "text": "2568"}, {"range": "2588", "text": "2570"}, {"range": "2589", "text": "2568"}, {"range": "2590", "text": "2570"}, {"range": "2591", "text": "2568"}, {"range": "2592", "text": "2570"}, {"range": "2593", "text": "2568"}, {"range": "2594", "text": "2570"}, {"range": "2595", "text": "2568"}, {"range": "2596", "text": "2570"}, {"range": "2597", "text": "2568"}, {"range": "2598", "text": "2570"}, {"range": "2599", "text": "2568"}, {"range": "2600", "text": "2570"}, {"range": "2601", "text": "2568"}, {"range": "2602", "text": "2570"}, {"range": "2603", "text": "2568"}, {"range": "2604", "text": "2570"}, {"range": "2605", "text": "2568"}, {"range": "2606", "text": "2570"}, "Update the dependencies array to be: [handlePaste]", {"range": "2607", "text": "2608"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "2609", "text": "2610"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "2611", "text": "2612"}, "Update the dependencies array to be: [handleFile]", {"range": "2613", "text": "2614"}, "Update the dependencies array to be: [dispatch, fetchExams, isClassroomExam, isSearch]", {"range": "2615", "text": "2616"}, "Update the dependencies array to be: [currentPage, fetchExams, isClassroomExam]", {"range": "2617", "text": "2618"}, "Update the dependencies array to be: [exam, examId, navigate]", {"range": "2619", "text": "2620"}, "Wrap the definition of 'addQuestion' in its own useCallback() Hook.", {"range": "2621", "text": "2622"}, {"range": "2623", "text": "2622"}, {"range": "2624", "text": "2622"}, "Update the dependencies array to be: [flag, remainingTime]", {"range": "2625", "text": "2626"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", {"range": "2627", "text": "2628"}, "Update the dependencies array to be: [user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", {"range": "2629", "text": "2630"}, {"range": "2631", "text": "2620"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "2632", "text": "2633"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "2634", "text": "2635"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "2636", "text": "2637"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "2638", "text": "2639"}, {"range": "2640", "text": "2616"}, {"range": "2641", "text": "2618"}, "Update the dependencies array to be: [dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "2642", "text": "2643"}, "Update the dependencies array to be: [tuitionPayments, tuitionStatistics]", {"range": "2644", "text": "2645"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, monthTuition, dispatch]", {"range": "2646", "text": "2647"}, "Update the dependencies array to be: [dispatch, selectedYear, selectedMonth, user?.id]", {"range": "2648", "text": "2649"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "2650", "text": "2651"}, "Update the dependencies array to be: [dispatch, currentMonth, view, firstGridDate, lastGridDate]", {"range": "2652", "text": "2653"}, "Update the dependencies array to be: [onClose]", {"range": "2654", "text": "2655"}, "Update the dependencies array to be: [dispatch, isChange, questions, questionsEdited]", {"range": "2656", "text": "2657"}, "Update the dependencies array to be: [exam, editedExam, isChange, dispatch]", {"range": "2658", "text": "2659"}, "Update the dependencies array to be: [confirm, dispatch, exam.id]", {"range": "2660", "text": "2661"}, [1911, 1927], "[user, navigate, formData.username]", [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [9548, 9558], "[dispatch, fetchPdfFiles]", [669, 671], "[options, selected, type]", [1909, 1927], "[dispatch, fetchQuestions, params]", [1518, 1563], "[dispatch, search, page, pageSize, sortOrder, classId]", [2625, 2647], "[inputValue, dispatch, setSearch]", [2124, 2148], "[codes, question, question.class]", [2397, 2417], "[codes, exam, exam?.class]", [10723, 10724], "", [10723, 10723], "\\", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [972, 974], "[maxLength]", [8880, 8881], [8880, 8880], [9345, 9346], [9345, 9345], [9541, 9542], [9541, 9541], [9543, 9544], [9543, 9543], [9613, 9614], [9613, 9613], [9615, 9616], [9615, 9615], [12161, 12162], [12161, 12161], [13081, 13082], [13081, 13081], [13248, 13249], [13248, 13248], [13250, 13251], [13250, 13250], [13320, 13321], [13320, 13320], [13322, 13323], [13322, 13322], [15926, 15927], [15926, 15926], [16620, 16621], [16620, 16620], [2448, 2450], "[handlePaste]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [2341, 2361], "[dispatch, fetchExams, isClassroomExam, isSearch]", [2439, 2452], "[currentPage, fetchExams, isClassroomExam]", [2482, 2488], "[exam, examId, navigate]", [4594, 4956], "useCallback((questionId) => {\r\n        // Only add to saveQuestions if not already there and not in errorQuestions\r\n        if (!saveQuestions.includes(questionId) && !errorQuestions.includes(questionId)) {\r\n            dispatch(setSaveQuestions([...saveQuestions, questionId]));\r\n        }\r\n        // DO NOT call removeErrorQuestion here - let Redux slice handle it\r\n    })", [4594, 4956], [4594, 4956], [9169, 9184], "[flag, remainingTime]", [31473, 31564], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", [35671, 35699], "[user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", [1496, 1502], [7568, 7578], "[dispatch, limit]", [6802, 6815], "[activeItem?.index, classDetail]", [6098, 6108], "[dispatch, location.search]", [1591, 1613], "[currentPage, didInit, loadReports]", [3871, 3891], [3965, 3978], [10387, 10413], "[dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [24202, 24221], "[tuitionPayments, tuitionStatistics]", [8386, 8416], "[<PERSON><PERSON><PERSON><PERSON>, monthTuition, dispatch]", [2133, 2172], "[dispatch, selected<PERSON>ear, selected<PERSON><PERSON><PERSON>, user?.id]", [3996, 3998], "[dispatch, userId]", [2456, 2486], "[dispatch, currentMonth, view, firstGridDate, lastGridDate]", [2008, 2010], "[onClose]", [3191, 3219], "[dispatch, isChange, questions, questionsEdited]", [3358, 3376], "[exam, editedExam, isChange, dispatch]", [3841, 3850], "[confirm, dispatch, exam.id]"]