[{"C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx": "5", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx": "7", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx": "8", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx": "9", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx": "10", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx": "11", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx": "12", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "13", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "14", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx": "15", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx": "16", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "17", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "18", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "19", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "20", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx": "21", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "22", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "23", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx": "24", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js": "25", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js": "26", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js": "27", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js": "28", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js": "29", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js": "30", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js": "31", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js": "32", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js": "33", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js": "34", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js": "35", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js": "36", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js": "37", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js": "38", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js": "39", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx": "40", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx": "41", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx": "42", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx": "43", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx": "44", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx": "45", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx": "46", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "47", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx": "48", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx": "49", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx": "50", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx": "51", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx": "52", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx": "53", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx": "55", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx": "56", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx": "57", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx": "58", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx": "59", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx": "60", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx": "61", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx": "63", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx": "64", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx": "65", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx": "66", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx": "67", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx": "68", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx": "69", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx": "70", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx": "71", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx": "72", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx": "73", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx": "74", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx": "75", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx": "76", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx": "77", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js": "78", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx": "79", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx": "80", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx": "81", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js": "82", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx": "83", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx": "84", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js": "85", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx": "86", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "87", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx": "88", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx": "89", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx": "90", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx": "91", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx": "92", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx": "93", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx": "94", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx": "95", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx": "96", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx": "97", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js": "98", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx": "99", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx": "100", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js": "101", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js": "102", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js": "103", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx": "104", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js": "105", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js": "106", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx": "107", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx": "108", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx": "109", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx": "110", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx": "111", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx": "112", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx": "113", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx": "114", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx": "115", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx": "116", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx": "117", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js": "118", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js": "119", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js": "120", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx": "121", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx": "122", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx": "123", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx": "124", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx": "125", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx": "126", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx": "127", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js": "128", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx": "129", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx": "130", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx": "131", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "132", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js": "133", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx": "134", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx": "135", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx": "136", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx": "137", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx": "138", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx": "139", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx": "140", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx": "141", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx": "142", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx": "143", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js": "144", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js": "145", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx": "146", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx": "147", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx": "148", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx": "149", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx": "150", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx": "151", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx": "152", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx": "153", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx": "154", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "155", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "156", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx": "157", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx": "158", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx": "159", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx": "160", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx": "161", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx": "162", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js": "163", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js": "164", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx": "165", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "166", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx": "167", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx": "168", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js": "169", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx": "170", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx": "171", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx": "172", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx": "173", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx": "174", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx": "175", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx": "176", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx": "177", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx": "178", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx": "179", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx": "180", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx": "181", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx": "182", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx": "183", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx": "184", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx": "185", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "186", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx": "187", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx": "188", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx": "189", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx": "190", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx": "191", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx": "192", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx": "193", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx": "194", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx": "195", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx": "196", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx": "197", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx": "198", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx": "199", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx": "200", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx": "201", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx": "202", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js": "203", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx": "204", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx": "205", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js": "206", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx": "207", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js": "208", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx": "209", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx": "210", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx": "211", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx": "212", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "213", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js": "214", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js": "215", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js": "216", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx": "217", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx": "218", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "219", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "220", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx": "221", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js": "222", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "223", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "224", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js": "225", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "226", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx": "227", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx": "228", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx": "229", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx": "230", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js": "231", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js": "232", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx": "233", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "234", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx": "235", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "236", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "237", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js": "238", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "239", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "240", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "241", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js": "242", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx": "243", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx": "244", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx": "245", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx": "246", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx": "247", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx": "248", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js": "249", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js": "250", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx": "251", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx": "252", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx": "253", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx": "254", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js": "255", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx": "256", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx": "257", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js": "258", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx": "259", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js": "260", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js": "261", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx": "262", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx": "263", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js": "264", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js": "265", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx": "266", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx": "267", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx": "268", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js": "269", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx": "270", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js": "271", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx": "272", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx": "273", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx": "274", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js": "275", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx": "276", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js": "277", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx": "278", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx": "279", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx": "280", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx": "281", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx": "282", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx": "283", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx": "284", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js": "285", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx": "286", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal2.jsx": "287", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js": "288", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx": "289", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js": "290", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js": "291", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx": "292", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx": "293", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx": "294", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx": "295", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx": "296", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx": "297", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx": "298", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx": "299", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx": "300", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx": "301", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx": "302", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx": "303", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx": "304", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx": "305", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js": "306", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\ImageDropZone.jsx": "307"}, {"size": 837, "mtime": 1748800674146, "results": "308", "hashOfConfig": "309"}, {"size": 375, "mtime": 1744531393988, "results": "310", "hashOfConfig": "309"}, {"size": 12055, "mtime": 1751827979200, "results": "311", "hashOfConfig": "309"}, {"size": 2522, "mtime": 1751514283166, "results": "312", "hashOfConfig": "309"}, {"size": 1183, "mtime": 1751787344260, "results": "313", "hashOfConfig": "309"}, {"size": 7456, "mtime": 1751554069606, "results": "314", "hashOfConfig": "309"}, {"size": 4828, "mtime": 1746378664900, "results": "315", "hashOfConfig": "309"}, {"size": 2152, "mtime": 1751276563455, "results": "316", "hashOfConfig": "309"}, {"size": 1271, "mtime": 1744531393978, "results": "317", "hashOfConfig": "309"}, {"size": 21516, "mtime": 1751827522381, "results": "318", "hashOfConfig": "309"}, {"size": 11683, "mtime": 1748250288653, "results": "319", "hashOfConfig": "309"}, {"size": 1979, "mtime": 1749722105422, "results": "320", "hashOfConfig": "309"}, {"size": 551, "mtime": 1744531393982, "results": "321", "hashOfConfig": "309"}, {"size": 275, "mtime": 1748215697376, "results": "322", "hashOfConfig": "309"}, {"size": 1739, "mtime": 1749721392840, "results": "323", "hashOfConfig": "309"}, {"size": 44964, "mtime": 1750904385044, "results": "324", "hashOfConfig": "309"}, {"size": 9223, "mtime": 1751743443365, "results": "325", "hashOfConfig": "309"}, {"size": 350, "mtime": 1750322483573, "results": "326", "hashOfConfig": "309"}, {"size": 19813, "mtime": 1750323348863, "results": "327", "hashOfConfig": "309"}, {"size": 3066, "mtime": 1751743443365, "results": "328", "hashOfConfig": "309"}, {"size": 2158, "mtime": 1751439211245, "results": "329", "hashOfConfig": "309"}, {"size": 1898, "mtime": 1750325540002, "results": "330", "hashOfConfig": "309"}, {"size": 348, "mtime": 1749202010110, "results": "331", "hashOfConfig": "309"}, {"size": 3322, "mtime": 1751743523736, "results": "332", "hashOfConfig": "309"}, {"size": 9893, "mtime": 1750849306726, "results": "333", "hashOfConfig": "309"}, {"size": 733, "mtime": 1751439773864, "results": "334", "hashOfConfig": "309"}, {"size": 6445, "mtime": 1749721854651, "results": "335", "hashOfConfig": "309"}, {"size": 3345, "mtime": 1751786228120, "results": "336", "hashOfConfig": "309"}, {"size": 3523, "mtime": 1751745797479, "results": "337", "hashOfConfig": "309"}, {"size": 7806, "mtime": 1750325495112, "results": "338", "hashOfConfig": "309"}, {"size": 17715, "mtime": 1751542931344, "results": "339", "hashOfConfig": "309"}, {"size": 14519, "mtime": 1750767145502, "results": "340", "hashOfConfig": "309"}, {"size": 1380, "mtime": 1744531393975, "results": "341", "hashOfConfig": "309"}, {"size": 2737, "mtime": 1750295163218, "results": "342", "hashOfConfig": "309"}, {"size": 4309, "mtime": 1751822374362, "results": "343", "hashOfConfig": "309"}, {"size": 5921, "mtime": 1751786223198, "results": "344", "hashOfConfig": "309"}, {"size": 2480, "mtime": 1747721626218, "results": "345", "hashOfConfig": "309"}, {"size": 5150, "mtime": 1748398556383, "results": "346", "hashOfConfig": "309"}, {"size": 1337, "mtime": 1747720637516, "results": "347", "hashOfConfig": "309"}, {"size": 690, "mtime": 1744531393950, "results": "348", "hashOfConfig": "309"}, {"size": 1339, "mtime": 1751554069606, "results": "349", "hashOfConfig": "309"}, {"size": 673, "mtime": 1744531393976, "results": "350", "hashOfConfig": "309"}, {"size": 1495, "mtime": 1751362330062, "results": "351", "hashOfConfig": "309"}, {"size": 2276, "mtime": 1751554069606, "results": "352", "hashOfConfig": "309"}, {"size": 1151, "mtime": 1749730250381, "results": "353", "hashOfConfig": "309"}, {"size": 3200, "mtime": 1744531393954, "results": "354", "hashOfConfig": "309"}, {"size": 3578, "mtime": 1747223318325, "results": "355", "hashOfConfig": "309"}, {"size": 635, "mtime": 1744531393961, "results": "356", "hashOfConfig": "309"}, {"size": 1228, "mtime": 1751361437565, "results": "357", "hashOfConfig": "309"}, {"size": 4033, "mtime": 1751276343661, "results": "358", "hashOfConfig": "309"}, {"size": 7571, "mtime": 1747902858134, "results": "359", "hashOfConfig": "309"}, {"size": 1218, "mtime": 1744531393963, "results": "360", "hashOfConfig": "309"}, {"size": 9572, "mtime": 1750323036527, "results": "361", "hashOfConfig": "309"}, {"size": 5752, "mtime": 1751276751884, "results": "362", "hashOfConfig": "309"}, {"size": 28691, "mtime": 1744958278477, "results": "363", "hashOfConfig": "309"}, {"size": 19936, "mtime": 1748984865157, "results": "364", "hashOfConfig": "309"}, {"size": 5631, "mtime": 1749721260990, "results": "365", "hashOfConfig": "309"}, {"size": 8139, "mtime": 1749721464118, "results": "366", "hashOfConfig": "309"}, {"size": 911, "mtime": 1744531393943, "results": "367", "hashOfConfig": "309"}, {"size": 3827, "mtime": 1751811244412, "results": "368", "hashOfConfig": "309"}, {"size": 31136, "mtime": 1751782971202, "results": "369", "hashOfConfig": "309"}, {"size": 9572, "mtime": 1749693815954, "results": "370", "hashOfConfig": "309"}, {"size": 1574, "mtime": 1744531393960, "results": "371", "hashOfConfig": "309"}, {"size": 7401, "mtime": 1747223318342, "results": "372", "hashOfConfig": "309"}, {"size": 2914, "mtime": 1747223318342, "results": "373", "hashOfConfig": "309"}, {"size": 28398, "mtime": 1749425223637, "results": "374", "hashOfConfig": "309"}, {"size": 17584, "mtime": 1750758162845, "results": "375", "hashOfConfig": "309"}, {"size": 19119, "mtime": 1750767145501, "results": "376", "hashOfConfig": "309"}, {"size": 1734, "mtime": 1744531393948, "results": "377", "hashOfConfig": "309"}, {"size": 56278, "mtime": 1751372627190, "results": "378", "hashOfConfig": "309"}, {"size": 6448, "mtime": 1749729618233, "results": "379", "hashOfConfig": "309"}, {"size": 5637, "mtime": 1747254491214, "results": "380", "hashOfConfig": "309"}, {"size": 21475, "mtime": 1750325928413, "results": "381", "hashOfConfig": "309"}, {"size": 10256, "mtime": 1744531393939, "results": "382", "hashOfConfig": "309"}, {"size": 1875, "mtime": 1750960485529, "results": "383", "hashOfConfig": "309"}, {"size": 19265, "mtime": 1751742523955, "results": "384", "hashOfConfig": "309"}, {"size": 6162, "mtime": 1748250288622, "results": "385", "hashOfConfig": "309"}, {"size": 3022, "mtime": 1747719253353, "results": "386", "hashOfConfig": "309"}, {"size": 7915, "mtime": 1748392811401, "results": "387", "hashOfConfig": "309"}, {"size": 5844, "mtime": 1751743845208, "results": "388", "hashOfConfig": "309"}, {"size": 3490, "mtime": 1749748056906, "results": "389", "hashOfConfig": "309"}, {"size": 935, "mtime": 1745405710864, "results": "390", "hashOfConfig": "309"}, {"size": 949, "mtime": 1747223318316, "results": "391", "hashOfConfig": "309"}, {"size": 3267, "mtime": 1747354362354, "results": "392", "hashOfConfig": "309"}, {"size": 3005, "mtime": 1750849360884, "results": "393", "hashOfConfig": "309"}, {"size": 4276, "mtime": 1751828500143, "results": "394", "hashOfConfig": "309"}, {"size": 2380, "mtime": 1744531393947, "results": "395", "hashOfConfig": "309"}, {"size": 2201, "mtime": 1744531393951, "results": "396", "hashOfConfig": "309"}, {"size": 14316, "mtime": 1749895693266, "results": "397", "hashOfConfig": "309"}, {"size": 1990, "mtime": 1744531393948, "results": "398", "hashOfConfig": "309"}, {"size": 841, "mtime": 1748984865154, "results": "399", "hashOfConfig": "309"}, {"size": 5565, "mtime": 1745690690469, "results": "400", "hashOfConfig": "309"}, {"size": 2295, "mtime": 1747223318353, "results": "401", "hashOfConfig": "309"}, {"size": 3146, "mtime": 1744531393940, "results": "402", "hashOfConfig": "309"}, {"size": 4074, "mtime": 1747223318326, "results": "403", "hashOfConfig": "309"}, {"size": 2787, "mtime": 1750173366905, "results": "404", "hashOfConfig": "309"}, {"size": 10634, "mtime": 1745483150534, "results": "405", "hashOfConfig": "406"}, {"size": 3707, "mtime": 1749722678323, "results": "407", "hashOfConfig": "309"}, {"size": 6034, "mtime": 1748364026312, "results": "408", "hashOfConfig": "309"}, {"size": 4147, "mtime": 1749731674278, "results": "409", "hashOfConfig": "309"}, {"size": 822, "mtime": 1751276455614, "results": "410", "hashOfConfig": "309"}, {"size": 4040, "mtime": 1750767145503, "results": "411", "hashOfConfig": "309"}, {"size": 297, "mtime": 1744531393989, "results": "412", "hashOfConfig": "309"}, {"size": 313, "mtime": 1744531393940, "results": "413", "hashOfConfig": "309"}, {"size": 4755, "mtime": 1751305471358, "results": "414", "hashOfConfig": "309"}, {"size": 1652, "mtime": 1748250288663, "results": "415", "hashOfConfig": "309"}, {"size": 993, "mtime": 1747223318349, "results": "416", "hashOfConfig": "309"}, {"size": 475, "mtime": 1748984865156, "results": "417", "hashOfConfig": "309"}, {"size": 902, "mtime": 1747223318353, "results": "418", "hashOfConfig": "309"}, {"size": 3053, "mtime": 1744531393946, "results": "419", "hashOfConfig": "309"}, {"size": 49554, "mtime": 1750767145500, "results": "420", "hashOfConfig": "309"}, {"size": 3402, "mtime": 1748781512174, "results": "421", "hashOfConfig": "309"}, {"size": 4872, "mtime": 1747223318349, "results": "422", "hashOfConfig": "309"}, {"size": 1641, "mtime": 1751827507517, "results": "423", "hashOfConfig": "309"}, {"size": 2297, "mtime": 1744531393945, "results": "424", "hashOfConfig": "309"}, {"size": 2193, "mtime": 1747223318349, "results": "425", "hashOfConfig": "309"}, {"size": 1359, "mtime": 1747223318349, "results": "426", "hashOfConfig": "309"}, {"size": 826, "mtime": 1748398318309, "results": "427", "hashOfConfig": "309"}, {"size": 1769, "mtime": 1748709335429, "results": "428", "hashOfConfig": "309"}, {"size": 1050, "mtime": 1751740167834, "results": "429", "hashOfConfig": "309"}, {"size": 4921, "mtime": 1747223318325, "results": "430", "hashOfConfig": "309"}, {"size": 11407, "mtime": 1751822007149, "results": "431", "hashOfConfig": "309"}, {"size": 411, "mtime": 1744531393940, "results": "432", "hashOfConfig": "309"}, {"size": 2290, "mtime": 1744531393963, "results": "433", "hashOfConfig": "309"}, {"size": 1219, "mtime": 1747467640276, "results": "434", "hashOfConfig": "309"}, {"size": 2003, "mtime": 1744531393970, "results": "435", "hashOfConfig": "309"}, {"size": 2166, "mtime": 1744531393969, "results": "436", "hashOfConfig": "309"}, {"size": 18935, "mtime": 1751374977843, "results": "437", "hashOfConfig": "309"}, {"size": 6813, "mtime": 1747223318342, "results": "438", "hashOfConfig": "309"}, {"size": 3094, "mtime": 1744531393970, "results": "439", "hashOfConfig": "309"}, {"size": 6364, "mtime": 1750767145498, "results": "440", "hashOfConfig": "309"}, {"size": 503, "mtime": 1744531393949, "results": "441", "hashOfConfig": "309"}, {"size": 394, "mtime": 1751811266307, "results": "442", "hashOfConfig": "309"}, {"size": 7876, "mtime": 1747223318342, "results": "443", "hashOfConfig": "309"}, {"size": 4922, "mtime": 1748329867180, "results": "444", "hashOfConfig": "309"}, {"size": 19965, "mtime": 1750900200879, "results": "445", "hashOfConfig": "309"}, {"size": 20555, "mtime": 1748250288625, "results": "446", "hashOfConfig": "309"}, {"size": 1337, "mtime": 1744531393967, "results": "447", "hashOfConfig": "406"}, {"size": 5412, "mtime": 1747223318349, "results": "448", "hashOfConfig": "309"}, {"size": 2938, "mtime": 1747223318353, "results": "449", "hashOfConfig": "309"}, {"size": 3182, "mtime": 1747223318353, "results": "450", "hashOfConfig": "309"}, {"size": 2928, "mtime": 1747223318349, "results": "451", "hashOfConfig": "309"}, {"size": 1885, "mtime": 1747354661883, "results": "452", "hashOfConfig": "309"}, {"size": 1345, "mtime": 1749697625937, "results": "453", "hashOfConfig": "309"}, {"size": 4099, "mtime": 1749731407409, "results": "454", "hashOfConfig": "309"}, {"size": 1576, "mtime": 1751811065450, "results": "455", "hashOfConfig": "309"}, {"size": 6380, "mtime": 1747361017417, "results": "456", "hashOfConfig": "309"}, {"size": 2600, "mtime": 1748876218633, "results": "457", "hashOfConfig": "309"}, {"size": 11541, "mtime": 1750900175948, "results": "458", "hashOfConfig": "309"}, {"size": 3297, "mtime": 1744531393957, "results": "459", "hashOfConfig": "309"}, {"size": 31011, "mtime": 1750902784209, "results": "460", "hashOfConfig": "309"}, {"size": 14565, "mtime": 1750819864802, "results": "461", "hashOfConfig": "309"}, {"size": 1205, "mtime": 1748984865161, "results": "462", "hashOfConfig": "309"}, {"size": 12148, "mtime": 1748250288636, "results": "463", "hashOfConfig": "309"}, {"size": 7688, "mtime": 1747223318312, "results": "464", "hashOfConfig": "309"}, {"size": 8344, "mtime": 1745507778499, "results": "465", "hashOfConfig": "309"}, {"size": 8025, "mtime": 1745507836095, "results": "466", "hashOfConfig": "309"}, {"size": 6988, "mtime": 1747223318344, "results": "467", "hashOfConfig": "309"}, {"size": 7719, "mtime": 1745499803667, "results": "468", "hashOfConfig": "309"}, {"size": 8378, "mtime": 1747223318344, "results": "469", "hashOfConfig": "309"}, {"size": 7254, "mtime": 1747223318344, "results": "470", "hashOfConfig": "309"}, {"size": 1721, "mtime": 1749553291593, "results": "471", "hashOfConfig": "309"}, {"size": 11464, "mtime": 1745500164258, "results": "472", "hashOfConfig": "309"}, {"size": 4650, "mtime": 1745508333678, "results": "473", "hashOfConfig": "309"}, {"size": 13822, "mtime": 1748250288625, "results": "474", "hashOfConfig": "309"}, {"size": 8599, "mtime": 1747223318326, "results": "475", "hashOfConfig": "309"}, {"size": 9774, "mtime": 1747223318326, "results": "476", "hashOfConfig": "309"}, {"size": 7914, "mtime": 1747223318326, "results": "477", "hashOfConfig": "309"}, {"size": 10728, "mtime": 1749548057374, "results": "478", "hashOfConfig": "309"}, {"size": 18582, "mtime": 1749747601919, "results": "479", "hashOfConfig": "309"}, {"size": 8954, "mtime": 1749810779685, "results": "480", "hashOfConfig": "309"}, {"size": 3429, "mtime": 1745682027607, "results": "481", "hashOfConfig": "309"}, {"size": 2298, "mtime": 1749802834779, "results": "482", "hashOfConfig": "309"}, {"size": 823, "mtime": 1748779533260, "results": "483", "hashOfConfig": "309"}, {"size": 1380, "mtime": 1745682047729, "results": "484", "hashOfConfig": "309"}, {"size": 1145, "mtime": 1747902858133, "results": "485", "hashOfConfig": "309"}, {"size": 1118, "mtime": 1745682069045, "results": "486", "hashOfConfig": "309"}, {"size": 978, "mtime": 1747902858130, "results": "487", "hashOfConfig": "309"}, {"size": 1781, "mtime": 1745682059432, "results": "488", "hashOfConfig": "406"}, {"size": 10563, "mtime": 1748585335221, "results": "489", "hashOfConfig": "309"}, {"size": 3574, "mtime": 1746378664904, "results": "490", "hashOfConfig": "309"}, {"size": 4389, "mtime": 1749980511142, "results": "491", "hashOfConfig": "309"}, {"size": 4914, "mtime": 1750326838086, "results": "492", "hashOfConfig": "309"}, {"size": 6418, "mtime": 1750326903479, "results": "493", "hashOfConfig": "309"}, {"size": 1020, "mtime": 1745682406063, "results": "494", "hashOfConfig": "309"}, {"size": 2723, "mtime": 1749810234131, "results": "495", "hashOfConfig": "309"}, {"size": 6116, "mtime": 1746378664905, "results": "496", "hashOfConfig": "309"}, {"size": 1565, "mtime": 1747223318344, "results": "497", "hashOfConfig": "309"}, {"size": 1624, "mtime": 1745689590880, "results": "498", "hashOfConfig": "309"}, {"size": 45152, "mtime": 1750295514405, "results": "499", "hashOfConfig": "309"}, {"size": 70430, "mtime": 1748984865165, "results": "500", "hashOfConfig": "309"}, {"size": 9519, "mtime": 1747354195259, "results": "501", "hashOfConfig": "309"}, {"size": 7408, "mtime": 1749894301204, "results": "502", "hashOfConfig": "309"}, {"size": 4905, "mtime": 1747354192845, "results": "503", "hashOfConfig": "309"}, {"size": 60115, "mtime": 1750947214848, "results": "504", "hashOfConfig": "309"}, {"size": 26487, "mtime": 1748278828957, "results": "505", "hashOfConfig": "309"}, {"size": 24147, "mtime": 1747354834297, "results": "506", "hashOfConfig": "309"}, {"size": 23053, "mtime": 1749730024729, "results": "507", "hashOfConfig": "309"}, {"size": 37412, "mtime": 1750487313341, "results": "508", "hashOfConfig": "309"}, {"size": 17071, "mtime": 1751543017144, "results": "509", "hashOfConfig": "309"}, {"size": 8060, "mtime": 1747223318310, "results": "510", "hashOfConfig": "309"}, {"size": 16767, "mtime": 1748876218633, "results": "511", "hashOfConfig": "309"}, {"size": 3160, "mtime": 1745731138150, "results": "512", "hashOfConfig": "309"}, {"size": 7136, "mtime": 1747223318325, "results": "513", "hashOfConfig": "309"}, {"size": 20185, "mtime": 1747223318312, "results": "514", "hashOfConfig": "309"}, {"size": 2129, "mtime": 1746378664905, "results": "515", "hashOfConfig": "309"}, {"size": 955, "mtime": 1746378664898, "results": "516", "hashOfConfig": "309"}, {"size": 1184, "mtime": 1746378664905, "results": "517", "hashOfConfig": "309"}, {"size": 13378, "mtime": 1748984865159, "results": "518", "hashOfConfig": "309"}, {"size": 1099, "mtime": 1748326442261, "results": "519", "hashOfConfig": "309"}, {"size": 15897, "mtime": 1749894770618, "results": "520", "hashOfConfig": "309"}, {"size": 11963, "mtime": 1750323324025, "results": "521", "hashOfConfig": "309"}, {"size": 12224, "mtime": 1748220515100, "results": "522", "hashOfConfig": "309"}, {"size": 10534, "mtime": 1748220627343, "results": "523", "hashOfConfig": "309"}, {"size": 4031, "mtime": 1747278525096, "results": "524", "hashOfConfig": "309"}, {"size": 2036, "mtime": 1747283717643, "results": "525", "hashOfConfig": "309"}, {"size": 72412, "mtime": 1750397113436, "results": "526", "hashOfConfig": "309"}, {"size": 3589, "mtime": 1747355350828, "results": "527", "hashOfConfig": "309"}, {"size": 7509, "mtime": 1747353152130, "results": "528", "hashOfConfig": "309"}, {"size": 6153, "mtime": 1747354063004, "results": "529", "hashOfConfig": "309"}, {"size": 16791, "mtime": 1748473697636, "results": "530", "hashOfConfig": "309"}, {"size": 12245, "mtime": 1750386603287, "results": "531", "hashOfConfig": "309"}, {"size": 13612, "mtime": 1750175198005, "results": "532", "hashOfConfig": "309"}, {"size": 73138, "mtime": 1750487274356, "results": "533", "hashOfConfig": "309"}, {"size": 5589, "mtime": 1750239106405, "results": "534", "hashOfConfig": "309"}, {"size": 9836, "mtime": 1750175833314, "results": "535", "hashOfConfig": "309"}, {"size": 7964, "mtime": 1750400179899, "results": "536", "hashOfConfig": "309"}, {"size": 4152, "mtime": 1749202010110, "results": "537", "hashOfConfig": "309"}, {"size": 4762, "mtime": 1748513292659, "results": "538", "hashOfConfig": "309"}, {"size": 2443, "mtime": 1747719362467, "results": "539", "hashOfConfig": "309"}, {"size": 16049, "mtime": 1751785268325, "results": "540", "hashOfConfig": "309"}, {"size": 2002, "mtime": 1751785248620, "results": "541", "hashOfConfig": "309"}, {"size": 55792, "mtime": 1751616690844, "results": "542", "hashOfConfig": "309"}, {"size": 16095, "mtime": 1751785298468, "results": "543", "hashOfConfig": "309"}, {"size": 8222, "mtime": 1748250288630, "results": "544", "hashOfConfig": "309"}, {"size": 11210, "mtime": 1748223444732, "results": "545", "hashOfConfig": "309"}, {"size": 41991, "mtime": 1751785335077, "results": "546", "hashOfConfig": "309"}, {"size": 8067, "mtime": 1750764769591, "results": "547", "hashOfConfig": "309"}, {"size": 28083, "mtime": 1748250288657, "results": "548", "hashOfConfig": "309"}, {"size": 29543, "mtime": 1748984865164, "results": "549", "hashOfConfig": "309"}, {"size": 36682, "mtime": 1748250768337, "results": "550", "hashOfConfig": "309"}, {"size": 1999, "mtime": 1750764523294, "results": "551", "hashOfConfig": "309"}, {"size": 9569, "mtime": 1749694485776, "results": "552", "hashOfConfig": "309"}, {"size": 13102, "mtime": 1748250288647, "results": "553", "hashOfConfig": "309"}, {"size": 16077, "mtime": 1748365756504, "results": "554", "hashOfConfig": "309"}, {"size": 3987, "mtime": 1748709335425, "results": "555", "hashOfConfig": "309"}, {"size": 3539, "mtime": 1748800991826, "results": "556", "hashOfConfig": "309"}, {"size": 1712, "mtime": 1748800656400, "results": "557", "hashOfConfig": "309"}, {"size": 1983, "mtime": 1751010202512, "results": "558", "hashOfConfig": "309"}, {"size": 3908, "mtime": 1748801325319, "results": "559", "hashOfConfig": "309"}, {"size": 839, "mtime": 1748801505979, "results": "560", "hashOfConfig": "309"}, {"size": 365, "mtime": 1748984865153, "results": "561", "hashOfConfig": "309"}, {"size": 1800, "mtime": 1751828247066, "results": "562", "hashOfConfig": "309"}, {"size": 8860, "mtime": 1749202010110, "results": "563", "hashOfConfig": "309"}, {"size": 5114, "mtime": 1750951954100, "results": "564", "hashOfConfig": "309"}, {"size": 8088, "mtime": 1751742516897, "results": "565", "hashOfConfig": "309"}, {"size": 4151, "mtime": 1751716037997, "results": "566", "hashOfConfig": "309"}, {"size": 958, "mtime": 1750951934365, "results": "567", "hashOfConfig": "309"}, {"size": 2836, "mtime": 1749722547776, "results": "568", "hashOfConfig": "309"}, {"size": 913, "mtime": 1751742948436, "results": "569", "hashOfConfig": "309"}, {"size": 388, "mtime": 1749720429395, "results": "570", "hashOfConfig": "309"}, {"size": 579, "mtime": 1749731593347, "results": "571", "hashOfConfig": "309"}, {"size": 1948, "mtime": 1751543127045, "results": "572", "hashOfConfig": "309"}, {"size": 10543, "mtime": 1750295311557, "results": "573", "hashOfConfig": "309"}, {"size": 1664, "mtime": 1749808930419, "results": "574", "hashOfConfig": "309"}, {"size": 2401, "mtime": 1750389237348, "results": "575", "hashOfConfig": "309"}, {"size": 7582, "mtime": 1750400196241, "results": "576", "hashOfConfig": "309"}, {"size": 3921, "mtime": 1750322224906, "results": "577", "hashOfConfig": "309"}, {"size": 2025, "mtime": 1750487274354, "results": "578", "hashOfConfig": "309"}, {"size": 3458, "mtime": 1750487274353, "results": "579", "hashOfConfig": "309"}, {"size": 200, "mtime": 1750487274358, "results": "580", "hashOfConfig": "309"}, {"size": 4832, "mtime": 1750574009240, "results": "581", "hashOfConfig": "309"}, {"size": 3066, "mtime": 1750766071078, "results": "582", "hashOfConfig": "309"}, {"size": 4807, "mtime": 1750765702772, "results": "583", "hashOfConfig": "309"}, {"size": 74, "mtime": 1748250288649, "results": "584", "hashOfConfig": "309"}, {"size": 4384, "mtime": 1750962443161, "results": "585", "hashOfConfig": "309"}, {"size": 4263, "mtime": 1750960348603, "results": "586", "hashOfConfig": "309"}, {"size": 4331, "mtime": 1750963155580, "results": "587", "hashOfConfig": "309"}, {"size": 3878, "mtime": 1750940043247, "results": "588", "hashOfConfig": "309"}, {"size": 23770, "mtime": 1750963670443, "results": "589", "hashOfConfig": "309"}, {"size": 17942, "mtime": 1750963374804, "results": "590", "hashOfConfig": "309"}, {"size": 22243, "mtime": 1750961198993, "results": "591", "hashOfConfig": "309"}, {"size": 1252, "mtime": 1750941383903, "results": "592", "hashOfConfig": "309"}, {"size": 846, "mtime": 1750944008903, "results": "593", "hashOfConfig": "309"}, {"size": 218, "mtime": 1751349395110, "results": "594", "hashOfConfig": "309"}, {"size": 2323, "mtime": 1751350697129, "results": "595", "hashOfConfig": "309"}, {"size": 52491, "mtime": 1751375995915, "results": "596", "hashOfConfig": "309"}, {"size": 815, "mtime": 1751617408262, "results": "597", "hashOfConfig": "309"}, {"size": 10987, "mtime": 1751631444481, "results": "598", "hashOfConfig": "309"}, {"size": 8700, "mtime": 1751822765291, "results": "599", "hashOfConfig": "309"}, {"size": 449, "mtime": 1751515976272, "results": "600", "hashOfConfig": "309"}, {"size": 2125, "mtime": 1751831578538, "results": "601", "hashOfConfig": "309"}, {"size": 3401, "mtime": 1751822255115, "results": "602", "hashOfConfig": "309"}, {"size": 719, "mtime": 1751617844711, "results": "603", "hashOfConfig": "309"}, {"size": 14778, "mtime": 1751826579778, "results": "604", "hashOfConfig": "309"}, {"size": 5735, "mtime": 1751826157062, "results": "605", "hashOfConfig": "309"}, {"size": 5981, "mtime": 1751832446273, "results": "606", "hashOfConfig": "309"}, {"size": 759, "mtime": 1751785941157, "results": "607", "hashOfConfig": "309"}, {"size": 4350, "mtime": 1751784560929, "results": "608", "hashOfConfig": "309"}, {"size": 4259, "mtime": 1751742649369, "results": "609", "hashOfConfig": "309"}, {"size": 843, "mtime": 1751717594658, "results": "610", "hashOfConfig": "309"}, {"size": 663, "mtime": 1751742504805, "results": "611", "hashOfConfig": "309"}, {"size": 1337, "mtime": 1751829986814, "results": "612", "hashOfConfig": "309"}, {"size": 5772, "mtime": 1751830627407, "results": "613", "hashOfConfig": "309"}, {"size": 2222, "mtime": 1751829103916, "results": "614", "hashOfConfig": "309"}, {"size": 1702, "mtime": 1751829247534, "results": "615", "hashOfConfig": "309"}, {"size": 4105, "mtime": 1751831884036, "results": "616", "hashOfConfig": "309"}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e07wfn", {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1op6h7j", {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1337", "messages": "1338", "suppressedMessages": "1339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1340", "messages": "1341", "suppressedMessages": "1342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1343", "messages": "1344", "suppressedMessages": "1345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1346", "messages": "1347", "suppressedMessages": "1348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1349", "messages": "1350", "suppressedMessages": "1351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1352", "messages": "1353", "suppressedMessages": "1354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1355", "messages": "1356", "suppressedMessages": "1357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1358", "messages": "1359", "suppressedMessages": "1360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1361", "messages": "1362", "suppressedMessages": "1363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1364", "messages": "1365", "suppressedMessages": "1366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1367", "messages": "1368", "suppressedMessages": "1369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1370", "messages": "1371", "suppressedMessages": "1372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1373", "messages": "1374", "suppressedMessages": "1375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1376", "messages": "1377", "suppressedMessages": "1378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1379", "messages": "1380", "suppressedMessages": "1381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1382", "messages": "1383", "suppressedMessages": "1384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1385", "messages": "1386", "suppressedMessages": "1387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1388", "messages": "1389", "suppressedMessages": "1390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1391", "messages": "1392", "suppressedMessages": "1393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1394", "messages": "1395", "suppressedMessages": "1396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1397", "messages": "1398", "suppressedMessages": "1399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1400", "messages": "1401", "suppressedMessages": "1402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1403", "messages": "1404", "suppressedMessages": "1405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1406", "messages": "1407", "suppressedMessages": "1408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1409", "messages": "1410", "suppressedMessages": "1411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1412", "messages": "1413", "suppressedMessages": "1414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1415", "messages": "1416", "suppressedMessages": "1417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1418", "messages": "1419", "suppressedMessages": "1420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1421", "messages": "1422", "suppressedMessages": "1423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1427", "messages": "1428", "suppressedMessages": "1429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1430", "messages": "1431", "suppressedMessages": "1432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1433", "messages": "1434", "suppressedMessages": "1435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1436", "messages": "1437", "suppressedMessages": "1438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1439", "messages": "1440", "suppressedMessages": "1441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1442", "messages": "1443", "suppressedMessages": "1444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1445", "messages": "1446", "suppressedMessages": "1447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1448", "messages": "1449", "suppressedMessages": "1450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1451", "messages": "1452", "suppressedMessages": "1453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1454", "messages": "1455", "suppressedMessages": "1456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1457", "messages": "1458", "suppressedMessages": "1459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1460", "messages": "1461", "suppressedMessages": "1462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1463", "messages": "1464", "suppressedMessages": "1465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1484", "messages": "1485", "suppressedMessages": "1486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1487", "messages": "1488", "suppressedMessages": "1489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1490", "messages": "1491", "suppressedMessages": "1492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1493", "messages": "1494", "suppressedMessages": "1495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1496", "messages": "1497", "suppressedMessages": "1498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1499", "messages": "1500", "suppressedMessages": "1501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1502", "messages": "1503", "suppressedMessages": "1504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1505", "messages": "1506", "suppressedMessages": "1507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1508", "messages": "1509", "suppressedMessages": "1510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1511", "messages": "1512", "suppressedMessages": "1513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1514", "messages": "1515", "suppressedMessages": "1516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1517", "messages": "1518", "suppressedMessages": "1519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1520", "messages": "1521", "suppressedMessages": "1522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1523", "messages": "1524", "suppressedMessages": "1525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1526", "messages": "1527", "suppressedMessages": "1528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1529", "messages": "1530", "suppressedMessages": "1531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1532", "messages": "1533", "suppressedMessages": "1534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1535", "messages": "1536", "suppressedMessages": "1537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js", ["1538"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx", ["1539", "1540", "1541", "1542", "1543"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx", ["1544", "1545"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx", ["1546"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx", ["1547"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx", ["1548", "1549", "1550", "1551", "1552"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx", ["1553", "1554"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1555", "1556"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1557"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1558", "1559", "1560", "1561", "1562", "1563", "1564"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1565", "1566", "1567", "1568", "1569"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", ["1570"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", ["1584", "1585", "1586", "1587"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1588", "1589"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1598"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js", ["1599"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js", ["1600"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js", ["1601"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx", ["1602"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx", ["1603"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1604"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx", ["1605", "1606", "1607", "1608", "1609", "1610", "1611"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx", ["1612"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx", ["1613", "1614", "1615"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx", ["1616"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx", ["1617"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx", ["1618", "1619", "1620"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx", ["1621", "1622", "1623"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx", ["1624", "1625"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx", ["1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx", ["1642", "1643"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx", ["1644", "1645", "1646", "1647", "1648"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx", ["1649", "1650", "1651", "1652", "1653", "1654", "1655"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx", ["1656", "1657"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx", ["1658"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx", ["1659", "1660", "1661", "1662", "1663"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx", ["1664"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx", ["1665", "1666", "1667", "1668", "1669", "1670"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx", ["1671", "1672"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx", ["1673"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx", ["1674", "1675", "1676", "1677", "1678", "1679", "1680"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx", ["1681", "1682", "1683", "1684", "1685"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1686", "1687", "1688"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx", ["1689", "1690"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx", ["1691"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx", ["1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx", ["1700"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx", ["1701"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js", ["1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx", ["1716"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx", ["1717"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx", ["1718"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx", ["1719"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx", ["1720"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx", ["1721", "1722", "1723"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx", ["1724"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1725", "1726", "1727", "1728", "1729", "1730"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx", ["1731"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx", ["1732", "1733", "1734", "1735"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx", ["1736", "1737"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx", ["1738", "1739", "1740", "1741"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx", ["1742", "1743"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx", ["1744", "1745", "1746", "1747"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1748"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx", ["1749", "1750"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1751"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx", ["1752", "1753"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx", ["1754"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx", ["1755"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx", ["1756", "1757", "1758", "1759", "1760"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx", ["1761", "1762"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx", ["1763", "1764", "1765"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx", ["1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx", ["1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx", ["1794", "1795", "1796", "1797"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx", ["1798"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx", ["1799", "1800", "1801", "1802"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx", ["1803", "1804", "1805", "1806", "1807", "1808", "1809"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx", ["1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx", ["1820", "1821", "1822", "1823"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["1824", "1825"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx", ["1826", "1827", "1828", "1829", "1830", "1831", "1832"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx", ["1833", "1834", "1835", "1836", "1837", "1838", "1839"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx", ["1840", "1841"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js", ["1842"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx", ["1843", "1844", "1845"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx", ["1846", "1847", "1848", "1849"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js", ["1850"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx", ["1851", "1852", "1853"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx", ["1854", "1855", "1856", "1857", "1858", "1859", "1860"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js", ["1861"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["1862", "1863", "1864", "1865"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["1898", "1899", "1900", "1901", "1902", "1903"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx", ["1904"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx", ["1905"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx", ["1906"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js", ["1907", "1908"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx", ["1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", ["1917"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["1918", "1919", "1920", "1921", "1922"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["1923", "1924", "1925"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["1926", "1927"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx", ["1928", "1929"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["1930", "1931", "1932"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx", ["1933"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx", ["1934", "1935", "1936", "1937"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ExamAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sheet\\sheetSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\UpdateTuitionSheetModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\sheetApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ExamSearchInput.jsx", ["1938"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiLessonSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\LessonSearchInput.jsx", ["1939"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\FullSchedulePage.jsx", ["1940", "1941", "1942", "1943", "1944", "1945", "1946", "1947"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\calendar\\calendarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\SidebarCalender.jsx", ["1948"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\CalenderMonth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\WeekView.jsx", ["1949", "1950", "1951", "1952", "1953", "1954", "1955", "1956", "1957", "1958", "1959", "1960"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\MonthView.jsx", ["1961"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\DayView.jsx", ["1962", "1963", "1964"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\NavigateTimeButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\calendar\\TimeSlots.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\constants\\UserType.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal2.jsx", ["1965"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\ocrExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AIExamManagement.jsx", ["1966", "1967"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\examAI\\examAISlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\apin8n.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\AiExamDetailAdmin.jsx", ["1968", "1969", "1970"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Header.jsx", ["1971", "1972"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\Button.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\RightContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\LeftContent.jsx", ["1973"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\QuestionContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentAttendanceAdmin.jsx", ["1974", "1975", "1976", "1977", "1978", "1979"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentTuitionAdmin.jsx", ["1980", "1981", "1982", "1983", "1984"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentHistoryAdmin.jsx", ["1985", "1986", "1987"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TableAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableQuestionItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementsContainer.jsx", ["1988"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\SortableStatementItem.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\setupKatexWarningFilter.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PageAIexam\\ImageDropZone.jsx", ["1989"], [], {"ruleId": "1990", "severity": 1, "message": "1991", "line": 51, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 51, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "1994", "line": 4, "column": 23, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "1995", "line": 8, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "1996", "line": 11, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 11, "endColumn": 21}, {"ruleId": "1997", "severity": 1, "message": "1998", "line": 42, "column": 8, "nodeType": "1999", "endLine": 42, "endColumn": 24, "suggestions": "2000"}, {"ruleId": "1990", "severity": 1, "message": "2001", "line": 49, "column": 15, "nodeType": "1992", "messageId": "1993", "endLine": 49, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "2002", "line": 10, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 10, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2003", "line": 18, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 36}, {"ruleId": "1997", "severity": 1, "message": "2004", "line": 26, "column": 8, "nodeType": "1999", "endLine": 26, "endColumn": 19, "suggestions": "2005"}, {"ruleId": "2006", "severity": 1, "message": "2007", "line": 393, "column": 45, "nodeType": "2008", "endLine": 398, "endColumn": 47}, {"ruleId": "1990", "severity": 1, "message": "2009", "line": 6, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 11}, {"ruleId": "1990", "severity": 1, "message": "2010", "line": 6, "column": 27, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 31}, {"ruleId": "1990", "severity": 1, "message": "2011", "line": 7, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "2012", "line": 8, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 32}, {"ruleId": "1990", "severity": 1, "message": "2013", "line": 88, "column": 23, "nodeType": "1992", "messageId": "1993", "endLine": 88, "endColumn": 31}, {"ruleId": "1990", "severity": 1, "message": "2014", "line": 14, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 14, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2003", "line": 15, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 36}, {"ruleId": "1990", "severity": 1, "message": "2015", "line": 2, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 3, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2017", "line": 5, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 39}, {"ruleId": "1990", "severity": 1, "message": "2018", "line": 15, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2019", "line": 16, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 16, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2020", "line": 24, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 24, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2021", "line": 30, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 30, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "2022", "line": 34, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 34, "endColumn": 23}, {"ruleId": "1997", "severity": 1, "message": "2023", "line": 73, "column": 31, "nodeType": "1992", "endLine": 73, "endColumn": 42}, {"ruleId": "1997", "severity": 1, "message": "2024", "line": 269, "column": 8, "nodeType": "1999", "endLine": 269, "endColumn": 18, "suggestions": "2025"}, {"ruleId": "1990", "severity": 1, "message": "2018", "line": 6, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2019", "line": 9, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 9, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2020", "line": 18, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2021", "line": 20, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 20, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "2026", "line": 26, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 26, "endColumn": 26}, {"ruleId": "1990", "severity": 1, "message": "2027", "line": 1, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2027", "line": 1, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2028", "line": 8, "column": 37, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 48}, {"ruleId": "1990", "severity": 1, "message": "2029", "line": 11, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 11, "endColumn": 30}, {"ruleId": "1990", "severity": 1, "message": "2030", "line": 22, "column": 18, "nodeType": "1992", "messageId": "1993", "endLine": 22, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "2031", "line": 23, "column": 21, "nodeType": "1992", "messageId": "1993", "endLine": 23, "endColumn": 31}, {"ruleId": "1990", "severity": 1, "message": "2032", "line": 24, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 24, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2033", "line": 25, "column": 25, "nodeType": "1992", "messageId": "1993", "endLine": 25, "endColumn": 39}, {"ruleId": "1990", "severity": 1, "message": "2034", "line": 26, "column": 30, "nodeType": "1992", "messageId": "1993", "endLine": 26, "endColumn": 49}, {"ruleId": "1990", "severity": 1, "message": "2035", "line": 27, "column": 21, "nodeType": "1992", "messageId": "1993", "endLine": 27, "endColumn": 26}, {"ruleId": "1990", "severity": 1, "message": "2036", "line": 27, "column": 41, "nodeType": "1992", "messageId": "1993", "endLine": 27, "endColumn": 51}, {"ruleId": "1990", "severity": 1, "message": "2037", "line": 31, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 31, "endColumn": 30}, {"ruleId": "1990", "severity": 1, "message": "2038", "line": 34, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 34, "endColumn": 35}, {"ruleId": "1990", "severity": 1, "message": "2039", "line": 38, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 38, "endColumn": 33}, {"ruleId": "1990", "severity": 1, "message": "2027", "line": 1, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2037", "line": 17, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 17, "endColumn": 30}, {"ruleId": "1990", "severity": 1, "message": "2038", "line": 20, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 20, "endColumn": 35}, {"ruleId": "1990", "severity": 1, "message": "2040", "line": 23, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 23, "endColumn": 32}, {"ruleId": "1990", "severity": 1, "message": "2017", "line": 6, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 39}, {"ruleId": "1990", "severity": 1, "message": "2003", "line": 15, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 36}, {"ruleId": "1990", "severity": 1, "message": "2027", "line": 1, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2015", "line": 2, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2041", "line": 6, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2042", "line": 7, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2043", "line": 8, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2037", "line": 25, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 25, "endColumn": 30}, {"ruleId": "1990", "severity": 1, "message": "2039", "line": 28, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 28, "endColumn": 33}, {"ruleId": "1990", "severity": 1, "message": "2040", "line": 31, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 31, "endColumn": 32}, {"ruleId": "1990", "severity": 1, "message": "2027", "line": 1, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2044", "line": 37, "column": 19, "nodeType": "1992", "messageId": "1993", "endLine": 37, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "2045", "line": 5, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 17}, {"ruleId": "2046", "severity": 1, "message": "2047", "line": 140, "column": 80, "nodeType": "2048", "messageId": "2049", "endLine": 140, "endColumn": 82}, {"ruleId": "1990", "severity": 1, "message": "2050", "line": 3, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2051", "line": 19, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 19, "endColumn": 22}, {"ruleId": "1997", "severity": 1, "message": "2052", "line": 18, "column": 8, "nodeType": "1999", "endLine": 18, "endColumn": 10, "suggestions": "2053"}, {"ruleId": "1990", "severity": 1, "message": "2054", "line": 1, "column": 20, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2055", "line": 4, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2056", "line": 11, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 11, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2057", "line": 12, "column": 36, "nodeType": "1992", "messageId": "1993", "endLine": 12, "endColumn": 45}, {"ruleId": "1990", "severity": 1, "message": "2058", "line": 13, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 13, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2059", "line": 13, "column": 16, "nodeType": "1992", "messageId": "1993", "endLine": 13, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2060", "line": 28, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 28, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2061", "line": 5, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2062", "line": 11, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 11, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2063", "line": 18, "column": 36, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 46}, {"ruleId": "1997", "severity": 1, "message": "2064", "line": 43, "column": 8, "nodeType": "1999", "endLine": 43, "endColumn": 26, "suggestions": "2065"}, {"ruleId": "1990", "severity": 1, "message": "2054", "line": 2, "column": 20, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2066", "line": 7, "column": 47, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 68}, {"ruleId": "1990", "severity": 1, "message": "2067", "line": 17, "column": 7, "nodeType": "1992", "messageId": "1993", "endLine": 17, "endColumn": 16}, {"ruleId": "1990", "severity": 1, "message": "2068", "line": 29, "column": 7, "nodeType": "1992", "messageId": "1993", "endLine": 29, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2069", "line": 36, "column": 7, "nodeType": "1992", "messageId": "1993", "endLine": 36, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2062", "line": 7, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2063", "line": 15, "column": 36, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 46}, {"ruleId": "1990", "severity": 1, "message": "2070", "line": 19, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 19, "endColumn": 37}, {"ruleId": "1990", "severity": 1, "message": "2063", "line": 15, "column": 36, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 46}, {"ruleId": "1997", "severity": 1, "message": "2071", "line": 34, "column": 8, "nodeType": "1999", "endLine": 34, "endColumn": 53, "suggestions": "2072"}, {"ruleId": "1990", "severity": 1, "message": "2073", "line": 12, "column": 55, "nodeType": "1992", "messageId": "1993", "endLine": 12, "endColumn": 61}, {"ruleId": "1990", "severity": 1, "message": "2074", "line": 12, "column": 77, "nodeType": "1992", "messageId": "1993", "endLine": 12, "endColumn": 82}, {"ruleId": "1990", "severity": 1, "message": "2075", "line": 12, "column": 84, "nodeType": "1992", "messageId": "1993", "endLine": 12, "endColumn": 94}, {"ruleId": "1990", "severity": 1, "message": "2020", "line": 18, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2022", "line": 20, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 20, "endColumn": 23}, {"ruleId": "1997", "severity": 1, "message": "2023", "line": 39, "column": 31, "nodeType": "1992", "endLine": 39, "endColumn": 42}, {"ruleId": "1990", "severity": 1, "message": "2076", "line": 96, "column": 17, "nodeType": "1992", "messageId": "1993", "endLine": 96, "endColumn": 21}, {"ruleId": "2046", "severity": 1, "message": "2047", "line": 412, "column": 68, "nodeType": "2048", "messageId": "2049", "endLine": 412, "endColumn": 70}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 2, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2017", "line": 7, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 39}, {"ruleId": "1990", "severity": 1, "message": "2077", "line": 24, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 24, "endColumn": 26}, {"ruleId": "1990", "severity": 1, "message": "2078", "line": 25, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 25, "endColumn": 30}, {"ruleId": "1990", "severity": 1, "message": "2079", "line": 47, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 47, "endColumn": 22}, {"ruleId": "1997", "severity": 1, "message": "2080", "line": 68, "column": 8, "nodeType": "1999", "endLine": 68, "endColumn": 30, "suggestions": "2081"}, {"ruleId": "1990", "severity": 1, "message": "2082", "line": 78, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 78, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2083", "line": 86, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 86, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2084", "line": 8, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 28}, {"ruleId": "1997", "severity": 1, "message": "2085", "line": 45, "column": 8, "nodeType": "1999", "endLine": 45, "endColumn": 32, "suggestions": "2086"}, {"ruleId": "1990", "severity": 1, "message": "2087", "line": 6, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2088", "line": 7, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2018", "line": 13, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 13, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2019", "line": 14, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 14, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2021", "line": 23, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 23, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "2089", "line": 5, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2088", "line": 6, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2084", "line": 7, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 28}, {"ruleId": "1997", "severity": 1, "message": "2090", "line": 51, "column": 8, "nodeType": "1999", "endLine": 51, "endColumn": 28, "suggestions": "2091"}, {"ruleId": "1990", "severity": 1, "message": "2039", "line": 65, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 65, "endColumn": 33}, {"ruleId": "1990", "severity": 1, "message": "2038", "line": 69, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 69, "endColumn": 35}, {"ruleId": "1990", "severity": 1, "message": "2040", "line": 73, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 73, "endColumn": 32}, {"ruleId": "1990", "severity": 1, "message": "2092", "line": 10, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 10, "endColumn": 17}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 290, "column": 59, "nodeType": "2095", "messageId": "2096", "endLine": 290, "endColumn": 60, "suggestions": "2097"}, {"ruleId": "1990", "severity": 1, "message": "2063", "line": 11, "column": 41, "nodeType": "1992", "messageId": "1993", "endLine": 11, "endColumn": 51}, {"ruleId": "1990", "severity": 1, "message": "2098", "line": 1, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 28}, {"ruleId": "1990", "severity": 1, "message": "2099", "line": 2, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2054", "line": 3, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2100", "line": 7, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2101", "line": 12, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 12, "endColumn": 31}, {"ruleId": "1990", "severity": 1, "message": "2102", "line": 1, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 13}, {"ruleId": "1990", "severity": 1, "message": "2103", "line": 2, "column": 37, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 45}, {"ruleId": "1990", "severity": 1, "message": "2104", "line": 2, "column": 47, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 60}, {"ruleId": "1990", "severity": 1, "message": "2105", "line": 2, "column": 62, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 67}, {"ruleId": "1990", "severity": 1, "message": "2106", "line": 2, "column": 69, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 77}, {"ruleId": "1990", "severity": 1, "message": "2107", "line": 3, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 26}, {"ruleId": "1997", "severity": 1, "message": "2108", "line": 36, "column": 8, "nodeType": "1999", "endLine": 36, "endColumn": 43, "suggestions": "2109"}, {"ruleId": "1990", "severity": 1, "message": "2102", "line": 1, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 13}, {"ruleId": "1990", "severity": 1, "message": "2002", "line": 7, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 18}, {"ruleId": "1997", "severity": 1, "message": "2110", "line": 25, "column": 8, "nodeType": "1999", "endLine": 25, "endColumn": 20, "suggestions": "2111"}, {"ruleId": "1990", "severity": 1, "message": "2112", "line": 1, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2113", "line": 32, "column": 83, "nodeType": "1992", "messageId": "1993", "endLine": 32, "endColumn": 91}, {"ruleId": "1990", "severity": 1, "message": "2114", "line": 121, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 121, "endColumn": 23}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 159, "column": 22, "nodeType": "2117", "messageId": "2118", "endLine": 159, "endColumn": 24}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 159, "column": 91, "nodeType": "2117", "messageId": "2118", "endLine": 159, "endColumn": 93}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 304, "column": 40, "nodeType": "2117", "messageId": "2118", "endLine": 304, "endColumn": 42}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 304, "column": 109, "nodeType": "2117", "messageId": "2118", "endLine": 304, "endColumn": 111}, {"ruleId": "1990", "severity": 1, "message": "2119", "line": 14, "column": 38, "nodeType": "1992", "messageId": "1993", "endLine": 14, "endColumn": 47}, {"ruleId": "1990", "severity": 1, "message": "2120", "line": 14, "column": 49, "nodeType": "1992", "messageId": "1993", "endLine": 14, "endColumn": 56}, {"ruleId": "1990", "severity": 1, "message": "2121", "line": 14, "column": 58, "nodeType": "1992", "messageId": "1993", "endLine": 14, "endColumn": 70}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 34, "column": 40, "nodeType": "2117", "messageId": "2118", "endLine": 34, "endColumn": 42}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 34, "column": 109, "nodeType": "2117", "messageId": "2118", "endLine": 34, "endColumn": 111}, {"ruleId": "1990", "severity": 1, "message": "2035", "line": 15, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2122", "line": 15, "column": 44, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 52}, {"ruleId": "1997", "severity": 1, "message": "2123", "line": 42, "column": 8, "nodeType": "1999", "endLine": 42, "endColumn": 40, "suggestions": "2124"}, {"ruleId": "1990", "severity": 1, "message": "2002", "line": 1, "column": 25, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 33}, {"ruleId": "1990", "severity": 1, "message": "2125", "line": 4, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2126", "line": 4, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "1996", "line": 1, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "1994", "line": 2, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2127", "line": 4, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2128", "line": 4, "column": 26, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 44}, {"ruleId": "1990", "severity": 1, "message": "2129", "line": 47, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 47, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2130", "line": 48, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 48, "endColumn": 26}, {"ruleId": "1990", "severity": 1, "message": "2131", "line": 50, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 50, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2132", "line": 59, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 59, "endColumn": 16}, {"ruleId": "1997", "severity": 1, "message": "2133", "line": 29, "column": 8, "nodeType": "1999", "endLine": 29, "endColumn": 10, "suggestions": "2134"}, {"ruleId": "2006", "severity": 1, "message": "2007", "line": 44, "column": 29, "nodeType": "2008", "endLine": 44, "endColumn": 98}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 238, "column": 30, "nodeType": "2095", "messageId": "2096", "endLine": 238, "endColumn": 31, "suggestions": "2135"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 251, "column": 61, "nodeType": "2095", "messageId": "2096", "endLine": 251, "endColumn": 62, "suggestions": "2136"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 257, "column": 29, "nodeType": "2095", "messageId": "2096", "endLine": 257, "endColumn": 30, "suggestions": "2137"}, {"ruleId": "2093", "severity": 1, "message": "2138", "line": 257, "column": 31, "nodeType": "2095", "messageId": "2096", "endLine": 257, "endColumn": 32, "suggestions": "2139"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 258, "column": 51, "nodeType": "2095", "messageId": "2096", "endLine": 258, "endColumn": 52, "suggestions": "2140"}, {"ruleId": "2093", "severity": 1, "message": "2138", "line": 258, "column": 53, "nodeType": "2095", "messageId": "2096", "endLine": 258, "endColumn": 54, "suggestions": "2141"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 346, "column": 30, "nodeType": "2095", "messageId": "2096", "endLine": 346, "endColumn": 31, "suggestions": "2142"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 368, "column": 61, "nodeType": "2095", "messageId": "2096", "endLine": 368, "endColumn": 62, "suggestions": "2143"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 374, "column": 29, "nodeType": "2095", "messageId": "2096", "endLine": 374, "endColumn": 30, "suggestions": "2144"}, {"ruleId": "2093", "severity": 1, "message": "2138", "line": 374, "column": 31, "nodeType": "2095", "messageId": "2096", "endLine": 374, "endColumn": 32, "suggestions": "2145"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 375, "column": 51, "nodeType": "2095", "messageId": "2096", "endLine": 375, "endColumn": 52, "suggestions": "2146"}, {"ruleId": "2093", "severity": 1, "message": "2138", "line": 375, "column": 53, "nodeType": "2095", "messageId": "2096", "endLine": 375, "endColumn": 54, "suggestions": "2147"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 456, "column": 30, "nodeType": "2095", "messageId": "2096", "endLine": 456, "endColumn": 31, "suggestions": "2148"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 475, "column": 61, "nodeType": "2095", "messageId": "2096", "endLine": 475, "endColumn": 62, "suggestions": "2149"}, {"ruleId": "1997", "severity": 1, "message": "2150", "line": 80, "column": 8, "nodeType": "1999", "endLine": 80, "endColumn": 10, "suggestions": "2151"}, {"ruleId": "2006", "severity": 1, "message": "2007", "line": 56, "column": 37, "nodeType": "2008", "endLine": 56, "endColumn": 107}, {"ruleId": "1990", "severity": 1, "message": "2152", "line": 3, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 27}, {"ruleId": "1997", "severity": 1, "message": "2153", "line": 73, "column": 8, "nodeType": "1999", "endLine": 73, "endColumn": 25, "suggestions": "2154"}, {"ruleId": "1990", "severity": 1, "message": "2054", "line": 3, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2155", "line": 5, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2156", "line": 14, "column": 28, "nodeType": "1992", "messageId": "1993", "endLine": 14, "endColumn": 45}, {"ruleId": "1990", "severity": 1, "message": "2157", "line": 34, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 34, "endColumn": 20}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 116, "column": 56, "nodeType": "2048", "messageId": "2049", "endLine": 116, "endColumn": 58}, {"ruleId": "1990", "severity": 1, "message": "2099", "line": 2, "column": 23, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 34}, {"ruleId": "1990", "severity": 1, "message": "2159", "line": 4, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 28}, {"ruleId": "1990", "severity": 1, "message": "2160", "line": 5, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2161", "line": 5, "column": 33, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 40}, {"ruleId": "1990", "severity": 1, "message": "2162", "line": 5, "column": 42, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 51}, {"ruleId": "1990", "severity": 1, "message": "2163", "line": 8, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 21}, {"ruleId": "2164", "severity": 1, "message": "2165", "line": 12, "column": 25, "nodeType": "2008", "endLine": 12, "endColumn": 614}, {"ruleId": "1990", "severity": 1, "message": "2102", "line": 1, "column": 46, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 49}, {"ruleId": "1990", "severity": 1, "message": "2166", "line": 3, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2167", "line": 15, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2168", "line": 15, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 37}, {"ruleId": "1997", "severity": 1, "message": "2169", "line": 21, "column": 8, "nodeType": "1999", "endLine": 21, "endColumn": 26, "suggestions": "2170"}, {"ruleId": "1997", "severity": 1, "message": "2171", "line": 40, "column": 8, "nodeType": "1999", "endLine": 40, "endColumn": 10, "suggestions": "2172"}, {"ruleId": "1990", "severity": 1, "message": "2009", "line": 2, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 11}, {"ruleId": "1990", "severity": 1, "message": "2016", "line": 3, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2173", "line": 13, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 13, "endColumn": 23}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 290, "column": 56, "nodeType": "2048", "messageId": "2049", "endLine": 290, "endColumn": 58}, {"ruleId": "1990", "severity": 1, "message": "2174", "line": 15, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2175", "line": 19, "column": 22, "nodeType": "1992", "messageId": "1993", "endLine": 19, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2155", "line": 4, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2176", "line": 5, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 16}, {"ruleId": "1990", "severity": 1, "message": "2177", "line": 100, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 100, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2178", "line": 111, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 111, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2056", "line": 15, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2043", "line": 7, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 36}, {"ruleId": "1990", "severity": 1, "message": "2056", "line": 13, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 13, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2056", "line": 14, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 14, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2043", "line": 7, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 36}, {"ruleId": "1990", "severity": 1, "message": "2056", "line": 13, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 13, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2043", "line": 7, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 36}, {"ruleId": "2179", "severity": 1, "message": "2180", "line": 28, "column": 74, "nodeType": "2048", "messageId": "2181", "endLine": 28, "endColumn": 75}, {"ruleId": "1990", "severity": 1, "message": "2182", "line": 6, "column": 25, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2183", "line": 6, "column": 31, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 37}, {"ruleId": "1990", "severity": 1, "message": "2075", "line": 6, "column": 39, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 49}, {"ruleId": "1990", "severity": 1, "message": "2105", "line": 6, "column": 51, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 56}, {"ruleId": "1990", "severity": 1, "message": "2184", "line": 6, "column": 65, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 70}, {"ruleId": "1997", "severity": 1, "message": "2185", "line": 50, "column": 8, "nodeType": "1999", "endLine": 50, "endColumn": 28, "suggestions": "2186"}, {"ruleId": "1997", "severity": 1, "message": "2185", "line": 54, "column": 8, "nodeType": "1999", "endLine": 54, "endColumn": 21, "suggestions": "2187"}, {"ruleId": "1990", "severity": 1, "message": "2188", "line": 37, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 37, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2189", "line": 44, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 44, "endColumn": 29}, {"ruleId": "1997", "severity": 1, "message": "2190", "line": 47, "column": 9, "nodeType": "2191", "endLine": 51, "endColumn": 4}, {"ruleId": "1990", "severity": 1, "message": "2192", "line": 16, "column": 45, "nodeType": "1992", "messageId": "1993", "endLine": 16, "endColumn": 57}, {"ruleId": "1997", "severity": 1, "message": "2193", "line": 55, "column": 8, "nodeType": "1999", "endLine": 55, "endColumn": 14, "suggestions": "2194"}, {"ruleId": "1997", "severity": 1, "message": "2195", "line": 109, "column": 11, "nodeType": "2191", "endLine": 115, "endColumn": 6, "suggestions": "2196"}, {"ruleId": "1997", "severity": 1, "message": "2197", "line": 109, "column": 11, "nodeType": "2191", "endLine": 115, "endColumn": 6, "suggestions": "2198"}, {"ruleId": "1997", "severity": 1, "message": "2199", "line": 109, "column": 11, "nodeType": "2191", "endLine": 115, "endColumn": 6, "suggestions": "2200"}, {"ruleId": "1990", "severity": 1, "message": "2201", "line": 117, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 117, "endColumn": 27}, {"ruleId": "1997", "severity": 1, "message": "2202", "line": 232, "column": 8, "nodeType": "1999", "endLine": 232, "endColumn": 23, "suggestions": "2203"}, {"ruleId": "1990", "severity": 1, "message": "2204", "line": 283, "column": 19, "nodeType": "1992", "messageId": "1993", "endLine": 283, "endColumn": 25}, {"ruleId": "1997", "severity": 1, "message": "2023", "line": 371, "column": 37, "nodeType": "1992", "endLine": 371, "endColumn": 48}, {"ruleId": "1997", "severity": 1, "message": "2023", "line": 398, "column": 37, "nodeType": "1992", "endLine": 398, "endColumn": 48}, {"ruleId": "2205", "severity": 1, "message": "2206", "line": 629, "column": 60, "nodeType": "2207", "messageId": "2208", "endLine": 629, "endColumn": 61}, {"ruleId": "1997", "severity": 1, "message": "2209", "line": 785, "column": 8, "nodeType": "1999", "endLine": 785, "endColumn": 99, "suggestions": "2210"}, {"ruleId": "1997", "severity": 1, "message": "2211", "line": 893, "column": 8, "nodeType": "1999", "endLine": 893, "endColumn": 36, "suggestions": "2212"}, {"ruleId": "1990", "severity": 1, "message": "2213", "line": 2, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2214", "line": 4, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2215", "line": 18, "column": 25, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2216", "line": 18, "column": 137, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 146}, {"ruleId": "1990", "severity": 1, "message": "2217", "line": 18, "column": 148, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 161}, {"ruleId": "1990", "severity": 1, "message": "2218", "line": 18, "column": 163, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 173}, {"ruleId": "1990", "severity": 1, "message": "2219", "line": 18, "column": 175, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 182}, {"ruleId": "1990", "severity": 1, "message": "2220", "line": 18, "column": 184, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 189}, {"ruleId": "1990", "severity": 1, "message": "2221", "line": 18, "column": 191, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 205}, {"ruleId": "1990", "severity": 1, "message": "2222", "line": 34, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 34, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2223", "line": 39, "column": 7, "nodeType": "1992", "messageId": "1993", "endLine": 39, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2224", "line": 40, "column": 7, "nodeType": "1992", "messageId": "1993", "endLine": 40, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2225", "line": 64, "column": 7, "nodeType": "1992", "messageId": "1993", "endLine": 64, "endColumn": 17}, {"ruleId": "1990", "severity": 1, "message": "2226", "line": 65, "column": 7, "nodeType": "1992", "messageId": "1993", "endLine": 65, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2227", "line": 67, "column": 7, "nodeType": "1992", "messageId": "1993", "endLine": 67, "endColumn": 17}, {"ruleId": "1990", "severity": 1, "message": "2102", "line": 4, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 13}, {"ruleId": "1990", "severity": 1, "message": "2028", "line": 6, "column": 35, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 46}, {"ruleId": "1990", "severity": 1, "message": "2228", "line": 10, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 10, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2063", "line": 26, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 26, "endColumn": 21}, {"ruleId": "1997", "severity": 1, "message": "2193", "line": 37, "column": 8, "nodeType": "1999", "endLine": 37, "endColumn": 14, "suggestions": "2229"}, {"ruleId": "1990", "severity": 1, "message": "2125", "line": 15, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2230", "line": 18, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2009", "line": 21, "column": 98, "nodeType": "1992", "messageId": "1993", "endLine": 21, "endColumn": 99}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 262, "column": 38, "nodeType": "2048", "messageId": "2049", "endLine": 262, "endColumn": 40}, {"ruleId": "1990", "severity": 1, "message": "2019", "line": 7, "column": 111, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 115}, {"ruleId": "1990", "severity": 1, "message": "2231", "line": 7, "column": 117, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 127}, {"ruleId": "1990", "severity": 1, "message": "2112", "line": 10, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 10, "endColumn": 24}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 178, "column": 123, "nodeType": "2117", "messageId": "2118", "endLine": 178, "endColumn": 125}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 178, "column": 197, "nodeType": "2117", "messageId": "2118", "endLine": 178, "endColumn": 199}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 187, "column": 120, "nodeType": "2117", "messageId": "2118", "endLine": 187, "endColumn": 122}, {"ruleId": "2115", "severity": 1, "message": "2116", "line": 187, "column": 189, "nodeType": "2117", "messageId": "2118", "endLine": 187, "endColumn": 191}, {"ruleId": "1990", "severity": 1, "message": "2103", "line": 15, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 13}, {"ruleId": "1990", "severity": 1, "message": "2073", "line": 17, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 17, "endColumn": 11}, {"ruleId": "1990", "severity": 1, "message": "2019", "line": 20, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 20, "endColumn": 9}, {"ruleId": "1990", "severity": 1, "message": "2232", "line": 21, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 21, "endColumn": 17}, {"ruleId": "1990", "severity": 1, "message": "2074", "line": 24, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 24, "endColumn": 10}, {"ruleId": "1990", "severity": 1, "message": "2125", "line": 28, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 28, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2233", "line": 30, "column": 7, "nodeType": "1992", "messageId": "1993", "endLine": 30, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2234", "line": 159, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 159, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2235", "line": 160, "column": 21, "nodeType": "1992", "messageId": "1993", "endLine": 160, "endColumn": 31}, {"ruleId": "1997", "severity": 1, "message": "2236", "line": 166, "column": 8, "nodeType": "1999", "endLine": 166, "endColumn": 18, "suggestions": "2237"}, {"ruleId": "1990", "severity": 1, "message": "2238", "line": 15, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 16}, {"ruleId": "1990", "severity": 1, "message": "2239", "line": 16, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 16, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2240", "line": 26, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 26, "endColumn": 18}, {"ruleId": "1997", "severity": 1, "message": "2241", "line": 199, "column": 8, "nodeType": "1999", "endLine": 199, "endColumn": 21, "suggestions": "2242"}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 49, "column": 37, "nodeType": "2048", "messageId": "2049", "endLine": 49, "endColumn": 39}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 51, "column": 44, "nodeType": "2048", "messageId": "2049", "endLine": 51, "endColumn": 46}, {"ruleId": "1990", "severity": 1, "message": "2243", "line": 8, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 16}, {"ruleId": "1990", "severity": 1, "message": "2009", "line": 8, "column": 18, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2106", "line": 8, "column": 21, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2244", "line": 15, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 17}, {"ruleId": "1990", "severity": 1, "message": "2245", "line": 16, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 16, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2246", "line": 26, "column": 24, "nodeType": "1992", "messageId": "1993", "endLine": 26, "endColumn": 37}, {"ruleId": "1990", "severity": 1, "message": "2247", "line": 111, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 111, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2125", "line": 7, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2019", "line": 13, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 13, "endColumn": 9}, {"ruleId": "1990", "severity": 1, "message": "2103", "line": 17, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 17, "endColumn": 13}, {"ruleId": "1990", "severity": 1, "message": "2248", "line": 19, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 19, "endColumn": 13}, {"ruleId": "1997", "severity": 1, "message": "2249", "line": 129, "column": 8, "nodeType": "1999", "endLine": 129, "endColumn": 18, "suggestions": "2250"}, {"ruleId": "1990", "severity": 1, "message": "2251", "line": 155, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 155, "endColumn": 32}, {"ruleId": "1990", "severity": 1, "message": "2252", "line": 232, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 232, "endColumn": 29}, {"ruleId": "1997", "severity": 1, "message": "2253", "line": 39, "column": 8, "nodeType": "1999", "endLine": 39, "endColumn": 30, "suggestions": "2254"}, {"ruleId": "2006", "severity": 1, "message": "2007", "line": 226, "column": 69, "nodeType": "2008", "endLine": 230, "endColumn": 71}, {"ruleId": "2046", "severity": 1, "message": "2047", "line": 49, "column": 108, "nodeType": "2048", "messageId": "2049", "endLine": 49, "endColumn": 110}, {"ruleId": "1990", "severity": 1, "message": "2035", "line": 55, "column": 42, "nodeType": "1992", "messageId": "1993", "endLine": 55, "endColumn": 47}, {"ruleId": "1997", "severity": 1, "message": "2185", "line": 88, "column": 8, "nodeType": "1999", "endLine": 88, "endColumn": 28, "suggestions": "2255"}, {"ruleId": "1997", "severity": 1, "message": "2185", "line": 92, "column": 8, "nodeType": "1999", "endLine": 92, "endColumn": 21, "suggestions": "2256"}, {"ruleId": "1990", "severity": 1, "message": "2257", "line": 7, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2258", "line": 8, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 12}, {"ruleId": "1990", "severity": 1, "message": "2259", "line": 13, "column": 3, "nodeType": "1992", "messageId": "1993", "endLine": 13, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2156", "line": 14, "column": 3, "nodeType": "1992", "messageId": "1993", "endLine": 14, "endColumn": 20}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 285, "column": 65, "nodeType": "2048", "messageId": "2049", "endLine": 285, "endColumn": 67}, {"ruleId": "1990", "severity": 1, "message": "2260", "line": 28, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 28, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2075", "line": 29, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 29, "endColumn": 15}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 406, "column": 101, "nodeType": "2048", "messageId": "2049", "endLine": 406, "endColumn": 103}, {"ruleId": "1990", "severity": 1, "message": "2261", "line": 55, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 55, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2262", "line": 55, "column": 32, "nodeType": "1992", "messageId": "1993", "endLine": 55, "endColumn": 42}, {"ruleId": "1990", "severity": 1, "message": "2263", "line": 59, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 59, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "2264", "line": 70, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 70, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2265", "line": 82, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 82, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2266", "line": 88, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 88, "endColumn": 30}, {"ruleId": "1990", "severity": 1, "message": "2251", "line": 94, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 94, "endColumn": 32}, {"ruleId": "2046", "severity": 1, "message": "2047", "line": 336, "column": 35, "nodeType": "2048", "messageId": "2049", "endLine": 336, "endColumn": 37}, {"ruleId": "1990", "severity": 1, "message": "2062", "line": 9, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 9, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2267", "line": 11, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 11, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2073", "line": 19, "column": 3, "nodeType": "1992", "messageId": "1993", "endLine": 19, "endColumn": 9}, {"ruleId": "1990", "severity": 1, "message": "2063", "line": 31, "column": 30, "nodeType": "1992", "messageId": "1993", "endLine": 31, "endColumn": 40}, {"ruleId": "1990", "severity": 1, "message": "2268", "line": 4, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2269", "line": 5, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 14}, {"ruleId": "1990", "severity": 1, "message": "2270", "line": 17, "column": 26, "nodeType": "1992", "messageId": "1993", "endLine": 17, "endColumn": 46}, {"ruleId": "1990", "severity": 1, "message": "2271", "line": 24, "column": 96, "nodeType": "1992", "messageId": "1993", "endLine": 24, "endColumn": 100}, {"ruleId": "1990", "severity": 1, "message": "2073", "line": 24, "column": 102, "nodeType": "1992", "messageId": "1993", "endLine": 24, "endColumn": 108}, {"ruleId": "1990", "severity": 1, "message": "2015", "line": 26, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 26, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2272", "line": 27, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 27, "endColumn": 13}, {"ruleId": "1990", "severity": 1, "message": "2020", "line": 34, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 34, "endColumn": 17}, {"ruleId": "1990", "severity": 1, "message": "2273", "line": 35, "column": 47, "nodeType": "1992", "messageId": "1993", "endLine": 35, "endColumn": 54}, {"ruleId": "1990", "severity": 1, "message": "2063", "line": 37, "column": 17, "nodeType": "1992", "messageId": "1993", "endLine": 37, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "2274", "line": 91, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 91, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2275", "line": 92, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 92, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2276", "line": 98, "column": 20, "nodeType": "1992", "messageId": "1993", "endLine": 98, "endColumn": 31}, {"ruleId": "1990", "severity": 1, "message": "2277", "line": 99, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 99, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2278", "line": 99, "column": 22, "nodeType": "1992", "messageId": "1993", "endLine": 99, "endColumn": 35}, {"ruleId": "1990", "severity": 1, "message": "2279", "line": 100, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 100, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2280", "line": 100, "column": 20, "nodeType": "1992", "messageId": "1993", "endLine": 100, "endColumn": 31}, {"ruleId": "1990", "severity": 1, "message": "2281", "line": 103, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 103, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2282", "line": 104, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 104, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2283", "line": 105, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 105, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2284", "line": 106, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 106, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "2285", "line": 136, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 136, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2286", "line": 137, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 137, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "2287", "line": 139, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 139, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2288", "line": 139, "column": 25, "nodeType": "1992", "messageId": "1993", "endLine": 139, "endColumn": 41}, {"ruleId": "1990", "severity": 1, "message": "2289", "line": 167, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 167, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "2290", "line": 173, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 173, "endColumn": 33}, {"ruleId": "1997", "severity": 1, "message": "2291", "line": 286, "column": 6, "nodeType": "1999", "endLine": 286, "endColumn": 32, "suggestions": "2292"}, {"ruleId": "1990", "severity": 1, "message": "2293", "line": 364, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 364, "endColumn": 23}, {"ruleId": "1997", "severity": 1, "message": "2294", "line": 742, "column": 6, "nodeType": "1999", "endLine": 742, "endColumn": 25, "suggestions": "2295"}, {"ruleId": "1990", "severity": 1, "message": "2296", "line": 764, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 764, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2297", "line": 776, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 776, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2267", "line": 8, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2074", "line": 19, "column": 3, "nodeType": "1992", "messageId": "1993", "endLine": 19, "endColumn": 8}, {"ruleId": "1990", "severity": 1, "message": "2298", "line": 20, "column": 3, "nodeType": "1992", "messageId": "1993", "endLine": 20, "endColumn": 10}, {"ruleId": "1990", "severity": 1, "message": "2299", "line": 30, "column": 36, "nodeType": "1992", "messageId": "1993", "endLine": 30, "endColumn": 56}, {"ruleId": "1990", "severity": 1, "message": "2300", "line": 32, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 32, "endColumn": 30}, {"ruleId": "1990", "severity": 1, "message": "2301", "line": 32, "column": 32, "nodeType": "1992", "messageId": "1993", "endLine": 32, "endColumn": 55}, {"ruleId": "1990", "severity": 1, "message": "2302", "line": 2, "column": 26, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 32}, {"ruleId": "1997", "severity": 1, "message": "2023", "line": 35, "column": 29, "nodeType": "1992", "endLine": 35, "endColumn": 40}, {"ruleId": "1997", "severity": 1, "message": "2023", "line": 35, "column": 30, "nodeType": "1992", "endLine": 35, "endColumn": 41}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 347, "column": 68, "nodeType": "2048", "messageId": "2049", "endLine": 347, "endColumn": 70}, {"ruleId": "2046", "severity": 1, "message": "2047", "line": 360, "column": 64, "nodeType": "2048", "messageId": "2049", "endLine": 360, "endColumn": 66}, {"ruleId": "1990", "severity": 1, "message": "2102", "line": 1, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 13}, {"ruleId": "1990", "severity": 1, "message": "2303", "line": 12, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 12, "endColumn": 13}, {"ruleId": "1990", "severity": 1, "message": "2018", "line": 24, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 24, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2019", "line": 26, "column": 119, "nodeType": "1992", "messageId": "1993", "endLine": 26, "endColumn": 123}, {"ruleId": "1990", "severity": 1, "message": "2304", "line": 26, "column": 135, "nodeType": "1992", "messageId": "1993", "endLine": 26, "endColumn": 140}, {"ruleId": "1990", "severity": 1, "message": "2020", "line": 99, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 99, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2063", "line": 107, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 107, "endColumn": 19}, {"ruleId": "1997", "severity": 1, "message": "2305", "line": 217, "column": 8, "nodeType": "1999", "endLine": 217, "endColumn": 38, "suggestions": "2306"}, {"ruleId": "1997", "severity": 1, "message": "2307", "line": 67, "column": 8, "nodeType": "1999", "endLine": 67, "endColumn": 47, "suggestions": "2308"}, {"ruleId": "1990", "severity": 1, "message": "2027", "line": 4, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 19}, {"ruleId": "1990", "severity": 1, "message": "2073", "line": 17, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 17, "endColumn": 11}, {"ruleId": "1990", "severity": 1, "message": "2010", "line": 20, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 20, "endColumn": 9}, {"ruleId": "1990", "severity": 1, "message": "2309", "line": 27, "column": 5, "nodeType": "1992", "messageId": "1993", "endLine": 27, "endColumn": 32}, {"ruleId": "1997", "severity": 1, "message": "2310", "line": 122, "column": 8, "nodeType": "1999", "endLine": 122, "endColumn": 10, "suggestions": "2311"}, {"ruleId": "1990", "severity": 1, "message": "2106", "line": 6, "column": 56, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 64}, {"ruleId": "1990", "severity": 1, "message": "2183", "line": 6, "column": 73, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 79}, {"ruleId": "1990", "severity": 1, "message": "2075", "line": 6, "column": 81, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 91}, {"ruleId": "1990", "severity": 1, "message": "2312", "line": 5, "column": 60, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 66}, {"ruleId": "1990", "severity": 1, "message": "2243", "line": 5, "column": 68, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 74}, {"ruleId": "1990", "severity": 1, "message": "2313", "line": 2, "column": 42, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 51}, {"ruleId": "1990", "severity": 1, "message": "2314", "line": 5, "column": 11, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 24}, {"ruleId": "1990", "severity": 1, "message": "2125", "line": 8, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 22}, {"ruleId": "1990", "severity": 1, "message": "2273", "line": 16, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 16, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2315", "line": 17, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 17, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2002", "line": 1, "column": 21, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2316", "line": 3, "column": 58, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 69}, {"ruleId": "1990", "severity": 1, "message": "2317", "line": 3, "column": 71, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 81}, {"ruleId": "1990", "severity": 1, "message": "2318", "line": 48, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 48, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2319", "line": 56, "column": 9, "nodeType": "1992", "messageId": "1993", "endLine": 56, "endColumn": 29}, {"ruleId": "1997", "severity": 1, "message": "2023", "line": 37, "column": 30, "nodeType": "1992", "endLine": 37, "endColumn": 41}, {"ruleId": "1997", "severity": 1, "message": "2023", "line": 35, "column": 32, "nodeType": "1992", "endLine": 35, "endColumn": 43}, {"ruleId": "1990", "severity": 1, "message": "2106", "line": 3, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2074", "line": 3, "column": 20, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "2320", "line": 3, "column": 27, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 38}, {"ruleId": "1990", "severity": 1, "message": "2126", "line": 3, "column": 40, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 52}, {"ruleId": "1990", "severity": 1, "message": "2316", "line": 3, "column": 54, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 65}, {"ruleId": "1990", "severity": 1, "message": "2321", "line": 6, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 6, "endColumn": 32}, {"ruleId": "1990", "severity": 1, "message": "2322", "line": 8, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2323", "line": 8, "column": 25, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 39}, {"ruleId": "1990", "severity": 1, "message": "2002", "line": 2, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2106", "line": 3, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2320", "line": 3, "column": 27, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 38}, {"ruleId": "1990", "severity": 1, "message": "2126", "line": 3, "column": 40, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 52}, {"ruleId": "1990", "severity": 1, "message": "2240", "line": 4, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 4, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2324", "line": 5, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 5, "endColumn": 30}, {"ruleId": "1990", "severity": 1, "message": "2325", "line": 7, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 21}, {"ruleId": "1990", "severity": 1, "message": "2322", "line": 8, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 8, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2326", "line": 9, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 9, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2273", "line": 15, "column": 39, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 46}, {"ruleId": "1990", "severity": 1, "message": "2173", "line": 17, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 17, "endColumn": 23}, {"ruleId": "1990", "severity": 1, "message": "2327", "line": 18, "column": 37, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 48}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 181, "column": 54, "nodeType": "2048", "messageId": "2049", "endLine": 181, "endColumn": 56}, {"ruleId": "1997", "severity": 1, "message": "2328", "line": 57, "column": 8, "nodeType": "1999", "endLine": 57, "endColumn": 38, "suggestions": "2329"}, {"ruleId": "1990", "severity": 1, "message": "2102", "line": 1, "column": 17, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2330", "line": 2, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 2, "endColumn": 26}, {"ruleId": "2046", "severity": 1, "message": "2158", "line": 186, "column": 50, "nodeType": "2048", "messageId": "2049", "endLine": 186, "endColumn": 52}, {"ruleId": null, "fatal": true, "severity": 2, "message": "2331", "line": 277, "column": 5, "nodeType": null}, {"ruleId": "1990", "severity": 1, "message": "2320", "line": 1, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 21}, {"ruleId": "1997", "severity": 1, "message": "2332", "line": 53, "column": 8, "nodeType": "1999", "endLine": 53, "endColumn": 10, "suggestions": "2333"}, {"ruleId": "1990", "severity": 1, "message": "2102", "line": 1, "column": 38, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 41}, {"ruleId": "1990", "severity": 1, "message": "2334", "line": 14, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 14, "endColumn": 28}, {"ruleId": "1990", "severity": 1, "message": "2335", "line": 15, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 15, "endColumn": 22}, {"ruleId": "1997", "severity": 1, "message": "2336", "line": 26, "column": 8, "nodeType": "1999", "endLine": 26, "endColumn": 36, "suggestions": "2337"}, {"ruleId": "1997", "severity": 1, "message": "2338", "line": 31, "column": 8, "nodeType": "1999", "endLine": 31, "endColumn": 26, "suggestions": "2339"}, {"ruleId": "1990", "severity": 1, "message": "2101", "line": 18, "column": 8, "nodeType": "1992", "messageId": "1993", "endLine": 18, "endColumn": 31}, {"ruleId": "1990", "severity": 1, "message": "2340", "line": 3, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 20}, {"ruleId": "1990", "severity": 1, "message": "2341", "line": 3, "column": 22, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 29}, {"ruleId": "1990", "severity": 1, "message": "2342", "line": 3, "column": 31, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 38}, {"ruleId": "1990", "severity": 1, "message": "2343", "line": 3, "column": 40, "nodeType": "1992", "messageId": "1993", "endLine": 3, "endColumn": 50}, {"ruleId": "1990", "severity": 1, "message": "2344", "line": 7, "column": 12, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 17}, {"ruleId": "1990", "severity": 1, "message": "2345", "line": 7, "column": 19, "nodeType": "1992", "messageId": "1993", "endLine": 7, "endColumn": 27}, {"ruleId": "1990", "severity": 1, "message": "2002", "line": 1, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 18}, {"ruleId": "1990", "severity": 1, "message": "2102", "line": 1, "column": 31, "nodeType": "1992", "messageId": "1993", "endLine": 1, "endColumn": 34}, {"ruleId": "1990", "severity": 1, "message": "2346", "line": 75, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 75, "endColumn": 28}, {"ruleId": "1990", "severity": 1, "message": "2273", "line": 75, "column": 30, "nodeType": "1992", "messageId": "1993", "endLine": 75, "endColumn": 37}, {"ruleId": "1990", "severity": 1, "message": "2262", "line": 75, "column": 39, "nodeType": "1992", "messageId": "1993", "endLine": 75, "endColumn": 49}, {"ruleId": "1990", "severity": 1, "message": "2057", "line": 13, "column": 45, "nodeType": "1992", "messageId": "1993", "endLine": 13, "endColumn": 54}, {"ruleId": "1990", "severity": 1, "message": "2347", "line": 66, "column": 13, "nodeType": "1992", "messageId": "1993", "endLine": 66, "endColumn": 25}, {"ruleId": "1990", "severity": 1, "message": "2036", "line": 67, "column": 26, "nodeType": "1992", "messageId": "1993", "endLine": 67, "endColumn": 36}, {"ruleId": "1990", "severity": 1, "message": "2348", "line": 20, "column": 10, "nodeType": "1992", "messageId": "1993", "endLine": 20, "endColumn": 16}, {"ruleId": "2006", "severity": 1, "message": "2007", "line": 77, "column": 21, "nodeType": "2008", "endLine": 81, "endColumn": 23}, "no-unused-vars", "'AdminUserSearchPage' is defined but never used.", "Identifier", "unusedVar", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'BeeMathLogo' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.username'. Either include it or remove the dependency array.", "ArrayExpression", ["2349"], "'resultAction' is assigned a value but never used.", "'useState' is defined but never used.", "'isFilterVIew' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", ["2350"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'setIsFilterView' is defined but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "'loadingExam' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2351"], "'showAddStudent' is assigned a value but never used.", "'AdminLayout' is defined but never used.", "'setAttempts' is defined but never used.", "'ScoreDistributionChart' is defined but never used.", "'setLogs' is assigned a value but never used.", "'setAnswers' is assigned a value but never used.", "'prevRanks' is assigned a value but never used.", "'setRankChanges' is assigned a value but never used.", "'setUpdatedAttemptId' is assigned a value but never used.", "'limit' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'handleClickedDetail' is assigned a value but never used.", "'handleClickedPreviewExam' is assigned a value but never used.", "'handleClickedQuestions' is assigned a value but never used.", "'handleClickedTracking' is assigned a value but never used.", "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'errorMsg' is assigned a value but never used.", "'setExam' is defined but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'backgroundImage' is defined but never used.", "'shouldFloat' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2352"], "'useEffect' is defined but never used.", "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'setSortOrder' is defined but never used.", "'resetFilters' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2353"], "'processInputForUpdate' is defined but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'setDeleteMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2354"], "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2355"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2356"], "'setClass' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "'LatexRenderer' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2357"], "'compose' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["2358", "2359"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'use' is defined but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2360"], "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2361"], "'ExamDefaultImage' is defined but never used.", "'imageUrl' is assigned a value but never used.", "'ActionButton' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2362"], "'LoadingSpinner' is defined but never used.", "'ChevronRight' is defined but never used.", "'toggleDropdown' is defined but never used.", "'toggleExamDropdown' is defined but never used.", "'dropdown' is assigned a value but never used.", "'tuitionDropdown' is assigned a value but never used.", "'examDropdown' is assigned a value but never used.", "'icon5' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2363"], ["2364", "2365"], ["2366", "2367"], ["2368", "2369"], "Unnecessary escape character: \\).", ["2370", "2371"], ["2372", "2373"], ["2374", "2375"], ["2376", "2377"], ["2378", "2379"], ["2380", "2381"], ["2382", "2383"], ["2384", "2385"], ["2386", "2387"], ["2388", "2389"], ["2390", "2391"], "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2392"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2393"], "'InputSearch' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'menuOpen' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'toggleCloseSidebar' is defined but never used.", "'ArrowRight' is defined but never used.", "'ArrowUp' is defined but never used.", "'ArrowDown' is defined but never used.", "'isHovered' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2394"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2395"], "'currentTime' is assigned a value but never used.", "'setSuccess' is defined but never used.", "'success' is assigned a value but never used.", "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchExams' and 'isClassroomExam'. Either include them or remove the dependency array.", ["2396"], ["2397"], "'isMobile' is assigned a value but never used.", "'currentQuestionType' is assigned a value but never used.", "The 'allQuestions' array makes the dependencies of useEffect Hook (at line 160) change on every render. To fix this, wrap the initialization of 'allQuestions' in its own useMemo() Hook.", "VariableDeclarator", "'isFullscreen' is defined but never used.", "React Hook useEffect has missing dependencies: 'examId' and 'navigate'. Either include them or remove the dependency array.", ["2398"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 668) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2399"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 679) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2400"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 691) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2401"], "'addErrorQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'flag'. Either include it or remove the dependency array.", ["2402"], "'result' is assigned a value but never used.", "no-sequences", "Unexpected use of comma operator.", "SequenceExpression", "unexpectedCommaExpression", "React Hook useEffect has a missing dependency: 'handleAutoSubmit'. Either include it or remove the dependency array.", ["2403"], "React Hook useEffect has missing dependencies: 'dispatch', 'exam?.isCheatingCheckEnabled', 'user.firstName', and 'user.lastName'. Either include them or remove the dependency array.", ["2404"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'ScoreBarChart' is defined but never used.", ["2405"], "'NoDataFound' is defined but never used.", "'BookMarked' is defined but never used.", "'ExternalLink' is defined but never used.", "'ButtonSidebar' is assigned a value but never used.", "'choice' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2406"], "'ChevronDown' is defined but never used.", "'ChevronUp' is defined but never used.", "'UserLayout' is defined but never used.", "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2407"], "'Filter' is defined but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2408"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2409"], ["2410"], ["2411"], "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'BarChart2' is defined but never used.", "'articles' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'formatNumberWithDots' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'loading' is assigned a value but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterGraduationYear', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2412"], "'handleBatchAdd' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tuitionPayments'. Either include it or remove the dependency array.", ["2413"], "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'Receipt' is defined but never used.", "'studentClassTuitions' is assigned a value but never used.", "'classTuitionsLoading' is assigned a value but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2414"], "React Hook useEffect has a missing dependency: 'user?.id'. Either include it or remove the dependency array.", ["2415"], "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2416"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", "'CheckCircle' is defined but never used.", "'DollarSign' is defined but never used.", "'getStatusBadge' is assigned a value but never used.", "'getPaymentStatusIcon' is assigned a value but never used.", "'ChevronLeft' is defined but never used.", "'getLearningItemWeekend' is defined but never used.", "'resetCalendar' is defined but never used.", "'setSelectedDay' is defined but never used.", "'fetchClassesOverview' is defined but never used.", "'CalendarMonth' is defined but never used.", "'SidebarCalendar' is defined but never used.", "'selectedDay' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'firstGridDate' and 'lastGridDate'. Either include them or remove the dependency array.", ["2417"], "'NavigateTimeButton' is defined but never used.", "Parsing error: Unexpected token (277:5)", "React Hook useEffect has a missing dependency: 'onClose'. Either include it or remove the dependency array. If 'onClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2418"], "'selectedQuestion' is assigned a value but never used.", "'editedText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isChanged'. Either include it or remove the dependency array.", ["2419"], "React Hook useEffect has missing dependencies: 'isChanged', 'questions', and 'questionsEdited'. Either include them or remove the dependency array.", ["2420"], "'TableAdmin' is defined but never used.", "'TdAdmin' is defined but never used.", "'ThAdmin' is defined but never used.", "'TheadAdmin' is defined but never used.", "'month' is assigned a value but never used.", "'setMonth' is assigned a value but never used.", "'tuitionPayments' is assigned a value but never used.", "'userAttempts' is assigned a value but never used.", "'Trash2' is defined but never used.", {"desc": "2421", "fix": "2422"}, {"desc": "2423", "fix": "2424"}, {"desc": "2425", "fix": "2426"}, {"desc": "2427", "fix": "2428"}, {"desc": "2429", "fix": "2430"}, {"desc": "2431", "fix": "2432"}, {"desc": "2433", "fix": "2434"}, {"desc": "2435", "fix": "2436"}, {"desc": "2437", "fix": "2438"}, {"messageId": "2439", "fix": "2440", "desc": "2441"}, {"messageId": "2442", "fix": "2443", "desc": "2444"}, {"desc": "2445", "fix": "2446"}, {"desc": "2447", "fix": "2448"}, {"desc": "2449", "fix": "2450"}, {"desc": "2451", "fix": "2452"}, {"messageId": "2439", "fix": "2453", "desc": "2441"}, {"messageId": "2442", "fix": "2454", "desc": "2444"}, {"messageId": "2439", "fix": "2455", "desc": "2441"}, {"messageId": "2442", "fix": "2456", "desc": "2444"}, {"messageId": "2439", "fix": "2457", "desc": "2441"}, {"messageId": "2442", "fix": "2458", "desc": "2444"}, {"messageId": "2439", "fix": "2459", "desc": "2441"}, {"messageId": "2442", "fix": "2460", "desc": "2444"}, {"messageId": "2439", "fix": "2461", "desc": "2441"}, {"messageId": "2442", "fix": "2462", "desc": "2444"}, {"messageId": "2439", "fix": "2463", "desc": "2441"}, {"messageId": "2442", "fix": "2464", "desc": "2444"}, {"messageId": "2439", "fix": "2465", "desc": "2441"}, {"messageId": "2442", "fix": "2466", "desc": "2444"}, {"messageId": "2439", "fix": "2467", "desc": "2441"}, {"messageId": "2442", "fix": "2468", "desc": "2444"}, {"messageId": "2439", "fix": "2469", "desc": "2441"}, {"messageId": "2442", "fix": "2470", "desc": "2444"}, {"messageId": "2439", "fix": "2471", "desc": "2441"}, {"messageId": "2442", "fix": "2472", "desc": "2444"}, {"messageId": "2439", "fix": "2473", "desc": "2441"}, {"messageId": "2442", "fix": "2474", "desc": "2444"}, {"messageId": "2439", "fix": "2475", "desc": "2441"}, {"messageId": "2442", "fix": "2476", "desc": "2444"}, {"messageId": "2439", "fix": "2477", "desc": "2441"}, {"messageId": "2442", "fix": "2478", "desc": "2444"}, {"messageId": "2439", "fix": "2479", "desc": "2441"}, {"messageId": "2442", "fix": "2480", "desc": "2444"}, {"desc": "2481", "fix": "2482"}, {"desc": "2483", "fix": "2484"}, {"desc": "2485", "fix": "2486"}, {"desc": "2487", "fix": "2488"}, {"desc": "2489", "fix": "2490"}, {"desc": "2491", "fix": "2492"}, {"desc": "2493", "fix": "2494"}, {"desc": "2495", "fix": "2496"}, {"desc": "2495", "fix": "2497"}, {"desc": "2495", "fix": "2498"}, {"desc": "2499", "fix": "2500"}, {"desc": "2501", "fix": "2502"}, {"desc": "2503", "fix": "2504"}, {"desc": "2493", "fix": "2505"}, {"desc": "2506", "fix": "2507"}, {"desc": "2508", "fix": "2509"}, {"desc": "2510", "fix": "2511"}, {"desc": "2512", "fix": "2513"}, {"desc": "2489", "fix": "2514"}, {"desc": "2491", "fix": "2515"}, {"desc": "2516", "fix": "2517"}, {"desc": "2518", "fix": "2519"}, {"desc": "2520", "fix": "2521"}, {"desc": "2522", "fix": "2523"}, {"desc": "2524", "fix": "2525"}, {"desc": "2526", "fix": "2527"}, {"desc": "2528", "fix": "2529"}, {"desc": "2530", "fix": "2531"}, {"desc": "2532", "fix": "2533"}, "Update the dependencies array to be: [user, navigate, formData.username]", {"range": "2534", "text": "2535"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "2536", "text": "2537"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "2538", "text": "2539"}, "Update the dependencies array to be: [options, selected, type]", {"range": "2540", "text": "2541"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "2542", "text": "2543"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "2544", "text": "2545"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "2546", "text": "2547"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "2548", "text": "2549"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "2550", "text": "2551"}, "removeEscape", {"range": "2552", "text": "2553"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2554", "text": "2555"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "2556", "text": "2557"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "2558", "text": "2559"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "2560", "text": "2561"}, "Update the dependencies array to be: [maxLength]", {"range": "2562", "text": "2563"}, {"range": "2564", "text": "2553"}, {"range": "2565", "text": "2555"}, {"range": "2566", "text": "2553"}, {"range": "2567", "text": "2555"}, {"range": "2568", "text": "2553"}, {"range": "2569", "text": "2555"}, {"range": "2570", "text": "2553"}, {"range": "2571", "text": "2555"}, {"range": "2572", "text": "2553"}, {"range": "2573", "text": "2555"}, {"range": "2574", "text": "2553"}, {"range": "2575", "text": "2555"}, {"range": "2576", "text": "2553"}, {"range": "2577", "text": "2555"}, {"range": "2578", "text": "2553"}, {"range": "2579", "text": "2555"}, {"range": "2580", "text": "2553"}, {"range": "2581", "text": "2555"}, {"range": "2582", "text": "2553"}, {"range": "2583", "text": "2555"}, {"range": "2584", "text": "2553"}, {"range": "2585", "text": "2555"}, {"range": "2586", "text": "2553"}, {"range": "2587", "text": "2555"}, {"range": "2588", "text": "2553"}, {"range": "2589", "text": "2555"}, {"range": "2590", "text": "2553"}, {"range": "2591", "text": "2555"}, "Update the dependencies array to be: [handlePaste]", {"range": "2592", "text": "2593"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "2594", "text": "2595"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "2596", "text": "2597"}, "Update the dependencies array to be: [handleFile]", {"range": "2598", "text": "2599"}, "Update the dependencies array to be: [dispatch, fetchExams, isClassroomExam, isSearch]", {"range": "2600", "text": "2601"}, "Update the dependencies array to be: [currentPage, fetchExams, isClassroomExam]", {"range": "2602", "text": "2603"}, "Update the dependencies array to be: [exam, examId, navigate]", {"range": "2604", "text": "2605"}, "Wrap the definition of 'addQuestion' in its own useCallback() Hook.", {"range": "2606", "text": "2607"}, {"range": "2608", "text": "2607"}, {"range": "2609", "text": "2607"}, "Update the dependencies array to be: [flag, remainingTime]", {"range": "2610", "text": "2611"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", {"range": "2612", "text": "2613"}, "Update the dependencies array to be: [user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", {"range": "2614", "text": "2615"}, {"range": "2616", "text": "2605"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "2617", "text": "2618"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "2619", "text": "2620"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "2621", "text": "2622"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "2623", "text": "2624"}, {"range": "2625", "text": "2601"}, {"range": "2626", "text": "2603"}, "Update the dependencies array to be: [dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "2627", "text": "2628"}, "Update the dependencies array to be: [tuitionPayments, tuitionStatistics]", {"range": "2629", "text": "2630"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, monthTuition, dispatch]", {"range": "2631", "text": "2632"}, "Update the dependencies array to be: [dispatch, selectedYear, selectedMonth, user?.id]", {"range": "2633", "text": "2634"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "2635", "text": "2636"}, "Update the dependencies array to be: [dispatch, currentMonth, view, firstGridDate, lastGridDate]", {"range": "2637", "text": "2638"}, "Update the dependencies array to be: [onClose]", {"range": "2639", "text": "2640"}, "Update the dependencies array to be: [isChanged, questions, questionsEdited]", {"range": "2641", "text": "2642"}, "Update the dependencies array to be: [exam, editedExam, isChanged, questions, questionsEdited]", {"range": "2643", "text": "2644"}, [1911, 1927], "[user, navigate, formData.username]", [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [9548, 9558], "[dispatch, fetchPdfFiles]", [669, 671], "[options, selected, type]", [1909, 1927], "[dispatch, fetchQuestions, params]", [1518, 1563], "[dispatch, search, page, pageSize, sortOrder, classId]", [2625, 2647], "[inputValue, dispatch, setSearch]", [2124, 2148], "[codes, question, question.class]", [2225, 2245], "[codes, exam, exam?.class]", [10723, 10724], "", [10723, 10723], "\\", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [972, 974], "[maxLength]", [8880, 8881], [8880, 8880], [9345, 9346], [9345, 9345], [9541, 9542], [9541, 9541], [9543, 9544], [9543, 9543], [9613, 9614], [9613, 9613], [9615, 9616], [9615, 9615], [12161, 12162], [12161, 12161], [13081, 13082], [13081, 13081], [13248, 13249], [13248, 13248], [13250, 13251], [13250, 13250], [13320, 13321], [13320, 13320], [13322, 13323], [13322, 13322], [15926, 15927], [15926, 15926], [16620, 16621], [16620, 16620], [2448, 2450], "[handlePaste]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [2341, 2361], "[dispatch, fetchExams, isClassroomExam, isSearch]", [2439, 2452], "[currentPage, fetchExams, isClassroomExam]", [2482, 2488], "[exam, examId, navigate]", [4594, 4956], "useCallback((questionId) => {\r\n        // Only add to saveQuestions if not already there and not in errorQuestions\r\n        if (!saveQuestions.includes(questionId) && !errorQuestions.includes(questionId)) {\r\n            dispatch(setSaveQuestions([...saveQuestions, questionId]));\r\n        }\r\n        // DO NOT call removeErrorQuestion here - let Redux slice handle it\r\n    })", [4594, 4956], [4594, 4956], [9169, 9184], "[flag, remainingTime]", [31473, 31564], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", [35671, 35699], "[user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", [1496, 1502], [7568, 7578], "[dispatch, limit]", [6802, 6815], "[activeItem?.index, classDetail]", [6098, 6108], "[dispatch, location.search]", [1591, 1613], "[currentPage, didInit, loadReports]", [3871, 3891], [3965, 3978], [10387, 10413], "[dispatch, filterClass, filterGraduationYear, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [24202, 24221], "[tuitionPayments, tuitionStatistics]", [8386, 8416], "[<PERSON><PERSON><PERSON><PERSON>, monthTuition, dispatch]", [2133, 2172], "[dispatch, selected<PERSON>ear, selected<PERSON><PERSON><PERSON>, user?.id]", [3996, 3998], "[dispatch, userId]", [2456, 2486], "[dispatch, currentMonth, view, firstGridDate, lastGridDate]", [1949, 1951], "[onClose]", [1184, 1212], "[isChanged, questions, questionsEdited]", [1369, 1387], "[exam, editedExam, isChanged, questions, questionsEdited]"]