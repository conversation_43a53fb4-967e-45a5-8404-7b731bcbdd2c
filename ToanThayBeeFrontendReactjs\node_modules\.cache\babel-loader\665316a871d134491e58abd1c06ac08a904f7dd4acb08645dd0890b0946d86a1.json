{"ast": null, "code": "import React, { useMemo, useRef, useEffect, useState, useContext } from 'react';\nimport { useDndContext, getClientRect, useDroppable, useDraggable, closestCorners, getFirstCollision, getScrollableAncestors, KeyboardCode } from '@dnd-kit/core';\nimport { useUniqueId, useIsomorphicLayoutEffect, CSS, useCombinedRefs, isKeyboardEvent, subtract } from '@dnd-kit/utilities';\n\n/**\r\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\r\n */\nfunction arrayMove(array, from, to) {\n  const newArray = array.slice();\n  newArray.splice(to < 0 ? newArray.length + to : to, 0, newArray.splice(from, 1)[0]);\n  return newArray;\n}\n\n/**\r\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\r\n */\nfunction arraySwap(array, from, to) {\n  const newArray = array.slice();\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n  return newArray;\n}\nfunction getSortedRects(items, rects) {\n  return items.reduce((accumulator, id, index) => {\n    const rect = rects.get(id);\n    if (rect) {\n      accumulator[index] = rect;\n    }\n    return accumulator;\n  }, Array(items.length));\n}\nfunction isValidIndex(index) {\n  return index !== null && index >= 0;\n}\nfunction itemsEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (a.length !== b.length) {\n    return false;\n  }\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction normalizeDisabled(disabled) {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled\n    };\n  }\n  return disabled;\n}\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst horizontalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n  let {\n    rects,\n    activeNodeRect: fallbackActiveRect,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n  if (!activeNodeRect) {\n    return null;\n  }\n  const itemGap = getItemGap(rects, index, activeIndex);\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n    if (!newIndexRect) {\n      return null;\n    }\n    return {\n      x: activeIndex < overIndex ? newIndexRect.left + newIndexRect.width - (activeNodeRect.left + activeNodeRect.width) : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale\n    };\n  }\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale\n  };\n};\nfunction getItemGap(rects, index, activeIndex) {\n  const currentRect = rects[index];\n  const previousRect = rects[index - 1];\n  const nextRect = rects[index + 1];\n  if (!currentRect || !previousRect && !nextRect) {\n    return 0;\n  }\n  if (activeIndex < index) {\n    return previousRect ? currentRect.left - (previousRect.left + previousRect.width) : nextRect.left - (currentRect.left + currentRect.width);\n  }\n  return nextRect ? nextRect.left - (currentRect.left + currentRect.width) : currentRect.left - (previousRect.left + previousRect.width);\n}\nconst rectSortingStrategy = _ref => {\n  let {\n    rects,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n  if (!newRect || !oldRect) {\n    return null;\n  }\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\nconst rectSwappingStrategy = _ref => {\n  let {\n    activeIndex,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  let oldRect;\n  let newRect;\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n  if (!newRect || !oldRect) {\n    return null;\n  }\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\n// To-do: We should be calculating scale transformation\nconst defaultScale$1 = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst verticalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n  let {\n    activeIndex,\n    activeNodeRect: fallbackActiveRect,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n  if (!activeNodeRect) {\n    return null;\n  }\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n    if (!overIndexRect) {\n      return null;\n    }\n    return {\n      x: 0,\n      y: activeIndex < overIndex ? overIndexRect.top + overIndexRect.height - (activeNodeRect.top + activeNodeRect.height) : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale$1\n    };\n  }\n  const itemGap = getItemGap$1(rects, index, activeIndex);\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale$1\n    };\n  }\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale$1\n    };\n  }\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale$1\n  };\n};\nfunction getItemGap$1(clientRects, index, activeIndex) {\n  const currentRect = clientRects[index];\n  const previousRect = clientRects[index - 1];\n  const nextRect = clientRects[index + 1];\n  if (!currentRect) {\n    return 0;\n  }\n  if (activeIndex < index) {\n    return previousRect ? currentRect.top - (previousRect.top + previousRect.height) : nextRect ? nextRect.top - (currentRect.top + currentRect.height) : 0;\n  }\n  return nextRect ? nextRect.top - (currentRect.top + currentRect.height) : previousRect ? currentRect.top - (previousRect.top + previousRect.height) : 0;\n}\nconst ID_PREFIX = 'Sortable';\nconst Context = /*#__PURE__*/React.createContext({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false\n  }\n});\nfunction SortableContext(_ref) {\n  let {\n    children,\n    id,\n    items: userDefinedItems,\n    strategy = rectSortingStrategy,\n    disabled: disabledProp = false\n  } = _ref;\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers\n  } = useDndContext();\n  const containerId = useUniqueId(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = useMemo(() => userDefinedItems.map(item => typeof item === 'object' && 'id' in item ? item.id : item), [userDefinedItems]);\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = useRef(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms = overIndex !== -1 && activeIndex === -1 || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n  useIsomorphicLayoutEffect(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n  useEffect(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n  const contextValue = useMemo(() => ({\n    activeIndex,\n    containerId,\n    disabled,\n    disableTransforms,\n    items,\n    overIndex,\n    useDragOverlay,\n    sortedRects: getSortedRects(items, droppableRects),\n    strategy\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [activeIndex, containerId, disabled.draggable, disabled.droppable, disableTransforms, items, overIndex, droppableRects, useDragOverlay, strategy]);\n  return React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\nconst defaultNewIndexGetter = _ref => {\n  let {\n    id,\n    items,\n    activeIndex,\n    overIndex\n  } = _ref;\n  return arrayMove(items, activeIndex, overIndex).indexOf(id);\n};\nconst defaultAnimateLayoutChanges = _ref2 => {\n  let {\n    containerId,\n    isSorting,\n    wasDragging,\n    index,\n    items,\n    newIndex,\n    previousItems,\n    previousContainerId,\n    transition\n  } = _ref2;\n  if (!transition || !wasDragging) {\n    return false;\n  }\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n  if (isSorting) {\n    return true;\n  }\n  return newIndex !== index && containerId === previousContainerId;\n};\nconst defaultTransition = {\n  duration: 200,\n  easing: 'ease'\n};\nconst transitionProperty = 'transform';\nconst disabledTransition = /*#__PURE__*/CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear'\n});\nconst defaultAttributes = {\n  roleDescription: 'sortable'\n};\n\n/*\r\n * When the index of an item changes while sorting,\r\n * we need to temporarily disable the transforms\r\n */\n\nfunction useDerivedTransform(_ref) {\n  let {\n    disabled,\n    index,\n    node,\n    rect\n  } = _ref;\n  const [derivedTransform, setDerivedtransform] = useState(null);\n  const previousIndex = useRef(index);\n  useIsomorphicLayoutEffect(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n      if (initial) {\n        const current = getClientRect(node.current, {\n          ignoreTransform: true\n        });\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height\n        };\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n  useEffect(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n  return derivedTransform;\n}\nfunction useSortable(_ref) {\n  let {\n    animateLayoutChanges = defaultAnimateLayoutChanges,\n    attributes: userDefinedAttributes,\n    disabled: localDisabled,\n    data: customData,\n    getNewIndex = defaultNewIndexGetter,\n    id,\n    strategy: localStrategy,\n    resizeObserverConfig,\n    transition = defaultTransition\n  } = _ref;\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy\n  } = useContext(Context);\n  const disabled = normalizeLocalDisabled(localDisabled, globalDisabled);\n  const index = items.indexOf(id);\n  const data = useMemo(() => ({\n    sortable: {\n      containerId,\n      index,\n      items\n    },\n    ...customData\n  }), [containerId, customData, index, items]);\n  const itemsAfterCurrentSortable = useMemo(() => items.slice(items.indexOf(id)), [items, id]);\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef\n  } = useDroppable({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig\n    }\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform\n  } = useDraggable({\n    id,\n    data,\n    attributes: {\n      ...defaultAttributes,\n      ...userDefinedAttributes\n    },\n    disabled: disabled.draggable\n  });\n  const setNodeRef = useCombinedRefs(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem = isSorting && !disableTransforms && isValidIndex(activeIndex) && isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement = shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy != null ? localStrategy : globalStrategy;\n  const finalTransform = displaceItem ? dragSourceDisplacement != null ? dragSourceDisplacement : strategy({\n    rects: sortedRects,\n    activeNodeRect,\n    activeIndex,\n    overIndex,\n    index\n  }) : null;\n  const newIndex = isValidIndex(activeIndex) && isValidIndex(overIndex) ? getNewIndex({\n    id,\n    items,\n    activeIndex,\n    overIndex\n  }) : index;\n  const activeId = active == null ? void 0 : active.id;\n  const previous = useRef({\n    activeId,\n    items,\n    newIndex,\n    containerId\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null\n  });\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect\n  });\n  useEffect(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n  useEffect(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n    if (activeId != null && previous.current.activeId == null) {\n      previous.current.activeId = activeId;\n      return;\n    }\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform != null ? derivedTransform : finalTransform,\n    transition: getTransition()\n  };\n  function getTransition() {\n    if (\n    // Temporarily disable transitions for a single frame to set up derived transforms\n    derivedTransform ||\n    // Or to prevent items jumping to back to their \"new\" position when items change\n    itemsHaveChanged && previous.current.newIndex === index) {\n      return disabledTransition;\n    }\n    if (shouldDisplaceDragSource && !isKeyboardEvent(activatorEvent) || !transition) {\n      return undefined;\n    }\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return CSS.Transition.toString({\n        ...transition,\n        property: transitionProperty\n      });\n    }\n    return undefined;\n  }\n}\nfunction normalizeLocalDisabled(localDisabled, globalDisabled) {\n  var _localDisabled$dragga, _localDisabled$droppa;\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false\n    };\n  }\n  return {\n    draggable: (_localDisabled$dragga = localDisabled == null ? void 0 : localDisabled.draggable) != null ? _localDisabled$dragga : globalDisabled.draggable,\n    droppable: (_localDisabled$droppa = localDisabled == null ? void 0 : localDisabled.droppable) != null ? _localDisabled$droppa : globalDisabled.droppable\n  };\n}\nfunction hasSortableData(entry) {\n  if (!entry) {\n    return false;\n  }\n  const data = entry.data.current;\n  if (data && 'sortable' in data && typeof data.sortable === 'object' && 'containerId' in data.sortable && 'items' in data.sortable && 'index' in data.sortable) {\n    return true;\n  }\n  return false;\n}\nconst directions = [KeyboardCode.Down, KeyboardCode.Right, KeyboardCode.Up, KeyboardCode.Left];\nconst sortableKeyboardCoordinates = (event, _ref) => {\n  let {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors\n    }\n  } = _ref;\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n    if (!active || !collisionRect) {\n      return;\n    }\n    const filteredContainers = [];\n    droppableContainers.getEnabled().forEach(entry => {\n      if (!entry || entry != null && entry.disabled) {\n        return;\n      }\n      const rect = droppableRects.get(entry.id);\n      if (!rect) {\n        return;\n      }\n      switch (event.code) {\n        case KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n      }\n    });\n    const collisions = closestCorners({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null\n    });\n    let closestId = getFirstCollision(collisions, 'id');\n    if (closestId === (over == null ? void 0 : over.id) && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable == null ? void 0 : newDroppable.node.current;\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = getScrollableAncestors(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some((element, index) => scrollableAncestors[index] !== element);\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset = hasDifferentScrollAncestors || !hasSameContainer ? {\n          x: 0,\n          y: 0\n        } : {\n          x: isAfterActive ? collisionRect.width - newRect.width : 0,\n          y: isAfterActive ? collisionRect.height - newRect.height : 0\n        };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top\n        };\n        const newCoordinates = offset.x && offset.y ? rectCoordinates : subtract(rectCoordinates, offset);\n        return newCoordinates;\n      }\n    }\n  }\n  return undefined;\n};\nfunction isSameContainer(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n  return a.data.current.sortable.containerId === b.data.current.sortable.containerId;\n}\nfunction isAfter(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\nexport { SortableContext, arrayMove, arraySwap, defaultAnimateLayoutChanges, defaultNewIndexGetter, hasSortableData, horizontalListSortingStrategy, rectSortingStrategy, rectSwappingStrategy, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy };", "map": {"version": 3, "names": ["arrayMove", "array", "from", "to", "newArray", "slice", "splice", "length", "arraySwap", "getSortedRects", "items", "rects", "reduce", "accumulator", "id", "index", "rect", "get", "Array", "isValidIndex", "itemsEqual", "a", "b", "i", "normalizeDisabled", "disabled", "draggable", "droppable", "defaultScale", "scaleX", "scaleY", "horizontalListSortingStrategy", "_ref", "activeNodeRect", "fallbackActiveRect", "activeIndex", "overIndex", "_rects$activeIndex", "itemGap", "getItemGap", "newIndexRect", "x", "left", "width", "y", "currentRect", "previousRect", "nextRect", "rectSortingStrategy", "newRects", "oldRect", "newRect", "top", "height", "rectSwappingStrategy", "defaultScale$1", "verticalListSortingStrategy", "overIndexRect", "getItemGap$1", "clientRects", "ID_PREFIX", "Context", "React", "createContext", "containerId", "disableTransforms", "useDragOverlay", "sortedRects", "strategy", "SortableContext", "children", "userDefinedItems", "disabledProp", "active", "dragOverlay", "droppableRects", "over", "measureDroppableContainers", "useDndContext", "useUniqueId", "Boolean", "useMemo", "map", "item", "isDragging", "indexOf", "previousItemsRef", "useRef", "itemsHaveChanged", "current", "useIsomorphicLayoutEffect", "useEffect", "contextValue", "createElement", "Provider", "value", "defaultNewIndexGetter", "defaultAnimateLayoutChanges", "_ref2", "isSorting", "wasDragging", "newIndex", "previousItems", "previousContainerId", "transition", "defaultTransition", "duration", "easing", "transitionProperty", "disabledTransition", "CSS", "Transition", "toString", "property", "defaultAttributes", "roleDescription", "useDerivedTransform", "node", "derivedTransform", "setDerivedtransform", "useState", "previousIndex", "initial", "getClientRect", "ignoreTransform", "delta", "useSortable", "animateLayoutChanges", "attributes", "userDefinedAttributes", "localDisabled", "data", "customData", "getNewIndex", "localStrategy", "resizeObserverConfig", "globalDisabled", "globalStrategy", "useContext", "normalizeLocalDisabled", "sortable", "itemsAfterCurrentSortable", "isOver", "setNodeRef", "setDroppableNodeRef", "useDroppable", "updateMeasurementsFor", "activatorEvent", "setDraggableNodeRef", "listeners", "setActivatorNodeRef", "transform", "useDraggable", "useCombinedRefs", "displaceItem", "shouldDisplaceDragSource", "dragSourceDisplacement", "finalTransform", "activeId", "previous", "shouldAnimateLayoutChanges", "timeoutId", "setTimeout", "clearTimeout", "getTransition", "isKeyboardEvent", "undefined", "_localDisabled$dragga", "_localDisabled$droppa", "hasSortableData", "entry", "directions", "KeyboardCode", "Down", "Right", "Up", "Left", "sortableKeyboardCoordinates", "event", "context", "collisionRect", "droppableContainers", "scrollableAncestors", "includes", "code", "preventDefault", "filteredContainers", "getEnabled", "for<PERSON>ach", "push", "collisions", "closestCorners", "pointerCoordinates", "closestId", "getFirstCollision", "activeDroppable", "newDroppable", "newNode", "newScrollAncestors", "getScrollableAncestors", "hasDifferentScrollAncestors", "some", "element", "hasSameContainer", "isSameContainer", "isAfterActive", "isAfter", "offset", "rectCoordinates", "newCoordinates", "subtract"], "sources": ["C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\utilities\\arrayMove.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\utilities\\arraySwap.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\utilities\\getSortedRects.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\utilities\\isValidIndex.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\utilities\\itemsEqual.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\utilities\\normalizeDisabled.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\strategies\\horizontalListSorting.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\strategies\\rectSorting.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\strategies\\rectSwapping.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\strategies\\verticalListSorting.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\components\\SortableContext.tsx", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\hooks\\defaults.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\hooks\\utilities\\useDerivedTransform.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\hooks\\useSortable.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\types\\type-guard.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\sortable\\src\\sensors\\keyboard\\sortableKeyboardCoordinates.ts"], "sourcesContent": ["/**\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\n */\nexport function arrayMove<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n  newArray.splice(\n    to < 0 ? newArray.length + to : to,\n    0,\n    newArray.splice(from, 1)[0]\n  );\n\n  return newArray;\n}\n", "/**\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\n */\nexport function arraySwap<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n\n  return newArray;\n}\n", "import type {\n  ClientRect,\n  UniqueIdentifier,\n  UseDndContextReturnValue,\n} from '@dnd-kit/core';\n\nexport function getSortedRects(\n  items: UniqueIdentifier[],\n  rects: UseDndContextReturnValue['droppableRects']\n) {\n  return items.reduce<ClientRect[]>((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n", "export function isValidIndex(index: number | null): index is number {\n  return index !== null && index >= 0;\n}\n", "import type {UniqueIdentifier} from '@dnd-kit/core';\n\nexport function itemsEqual(a: UniqueIdentifier[], b: UniqueIdentifier[]) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import type {Disabled} from '../types';\n\nexport function normalizeDisabled(disabled: boolean | Disabled): Disabled {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled,\n    };\n  }\n\n  return disabled;\n}\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const horizontalListSortingStrategy: SortingStrategy = ({\n  rects,\n  activeNodeRect: fallbackActiveRect,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x:\n        activeIndex < overIndex\n          ? newIndexRect.left +\n            newIndexRect.width -\n            (activeNodeRect.left + activeNodeRect.width)\n          : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(rects: ClientRect[], index: number, activeIndex: number) {\n  const currentRect: ClientRect | undefined = rects[index];\n  const previousRect: ClientRect | undefined = rects[index - 1];\n  const nextRect: ClientRect | undefined = rects[index + 1];\n\n  if (!currentRect || (!previousRect && !nextRect)) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.left - (previousRect.left + previousRect.width)\n      : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect\n    ? nextRect.left - (currentRect.left + currentRect.width)\n    : currentRect.left - (previousRect.left + previousRect.width);\n}\n", "import {arrayMove} from '../utilities';\nimport type {SortingStrategy} from '../types';\n\nexport const rectSortingStrategy: SortingStrategy = ({\n  rects,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {SortingStrategy} from '../types';\n\nexport const rectSwappingStrategy: SortingStrategy = ({\n  activeIndex,\n  index,\n  rects,\n  overIndex,\n}) => {\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const verticalListSortingStrategy: SortingStrategy = ({\n  activeIndex,\n  activeNodeRect: fallbackActiveRect,\n  index,\n  rects,\n  overIndex,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y:\n        activeIndex < overIndex\n          ? overIndexRect.top +\n            overIndexRect.height -\n            (activeNodeRect.top + activeNodeRect.height)\n          : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale,\n    };\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(\n  clientRects: ClientRect[],\n  index: number,\n  activeIndex: number\n) {\n  const currentRect: ClientRect | undefined = clientRects[index];\n  const previousRect: ClientRect | undefined = clientRects[index - 1];\n  const nextRect: ClientRect | undefined = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.top - (previousRect.top + previousRect.height)\n      : nextRect\n      ? nextRect.top - (currentRect.top + currentRect.height)\n      : 0;\n  }\n\n  return nextRect\n    ? nextRect.top - (currentRect.top + currentRect.height)\n    : previousRect\n    ? currentRect.top - (previousRect.top + previousRect.height)\n    : 0;\n}\n", "import React, {useEffect, useMemo, useRef} from 'react';\nimport {useDndContext, ClientRect, UniqueIdentifier} from '@dnd-kit/core';\nimport {useIsomorphicLayoutEffect, useUniqueId} from '@dnd-kit/utilities';\n\nimport type {Disabled, SortingStrategy} from '../types';\nimport {getSortedRects, itemsEqual, normalizeDisabled} from '../utilities';\nimport {rectSortingStrategy} from '../strategies';\n\nexport interface Props {\n  children: React.ReactNode;\n  items: (UniqueIdentifier | {id: UniqueIdentifier})[];\n  strategy?: SortingStrategy;\n  id?: string;\n  disabled?: boolean | Disabled;\n}\n\nconst ID_PREFIX = 'Sortable';\n\ninterface ContextDescriptor {\n  activeIndex: number;\n  containerId: string;\n  disabled: Disabled;\n  disableTransforms: boolean;\n  items: UniqueIdentifier[];\n  overIndex: number;\n  useDragOverlay: boolean;\n  sortedRects: ClientRect[];\n  strategy: SortingStrategy;\n}\n\nexport const Context = React.createContext<ContextDescriptor>({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false,\n  },\n});\n\nexport function SortableContext({\n  children,\n  id,\n  items: userDefinedItems,\n  strategy = rectSortingStrategy,\n  disabled: disabledProp = false,\n}: Props) {\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n  } = useDndContext();\n  const containerId = useUniqueId(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = useMemo<UniqueIdentifier[]>(\n    () =>\n      userDefinedItems.map((item) =>\n        typeof item === 'object' && 'id' in item ? item.id : item\n      ),\n    [userDefinedItems]\n  );\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = useRef(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms =\n    (overIndex !== -1 && activeIndex === -1) || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n\n  useIsomorphicLayoutEffect(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n\n  useEffect(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n\n  const contextValue = useMemo(\n    (): ContextDescriptor => ({\n      activeIndex,\n      containerId,\n      disabled,\n      disableTransforms,\n      items,\n      overIndex,\n      useDragOverlay,\n      sortedRects: getSortedRects(items, droppableRects),\n      strategy,\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      activeIndex,\n      containerId,\n      disabled.draggable,\n      disabled.droppable,\n      disableTransforms,\n      items,\n      overIndex,\n      droppableRects,\n      useDragOverlay,\n      strategy,\n    ]\n  );\n\n  return <Context.Provider value={contextValue}>{children}</Context.Provider>;\n}\n", "import {CSS} from '@dnd-kit/utilities';\n\nimport {arrayMove} from '../utilities';\n\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\n\nexport const defaultNewIndexGetter: NewIndexGetter = ({\n  id,\n  items,\n  activeIndex,\n  overIndex,\n}) => arrayMove(items, activeIndex, overIndex).indexOf(id);\n\nexport const defaultAnimateLayoutChanges: AnimateLayoutChanges = ({\n  containerId,\n  isSorting,\n  wasDragging,\n  index,\n  items,\n  newIndex,\n  previousItems,\n  previousContainerId,\n  transition,\n}) => {\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\n\nexport const defaultTransition: SortableTransition = {\n  duration: 200,\n  easing: 'ease',\n};\n\nexport const transitionProperty = 'transform';\n\nexport const disabledTransition = CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear',\n});\n\nexport const defaultAttributes = {\n  roleDescription: 'sortable',\n};\n", "import {useEffect, useRef, useState} from 'react';\nimport {getClientRect, ClientRect} from '@dnd-kit/core';\nimport {Transform, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  rect: React.MutableRefObject<ClientRect | null>;\n  disabled: boolean;\n  index: number;\n  node: React.MutableRefObject<HTMLElement | null>;\n}\n\n/*\n * When the index of an item changes while sorting,\n * we need to temporarily disable the transforms\n */\nexport function useDerivedTransform({disabled, index, node, rect}: Arguments) {\n  const [derivedTransform, setDerivedtransform] = useState<Transform | null>(\n    null\n  );\n  const previousIndex = useRef(index);\n\n  useIsomorphicLayoutEffect(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = getClientRect(node.current, {\n          ignoreTransform: true,\n        });\n\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height,\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n\n  useEffect(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n\n  return derivedTransform;\n}\n", "import {useContext, useEffect, useMemo, useRef} from 'react';\nimport {\n  useDraggable,\n  useDroppable,\n  UseDraggableArguments,\n  UseDroppableArguments,\n} from '@dnd-kit/core';\nimport type {Data} from '@dnd-kit/core';\nimport {CSS, isKeyboardEvent, useCombinedRefs} from '@dnd-kit/utilities';\n\nimport {Context} from '../components';\nimport type {Disabled, SortableData, SortingStrategy} from '../types';\nimport {isValidIndex} from '../utilities';\nimport {\n  defaultAnimateLayoutChanges,\n  defaultAttributes,\n  defaultNewIndexGetter,\n  defaultTransition,\n  disabledTransition,\n  transitionProperty,\n} from './defaults';\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\nimport {useDerivedTransform} from './utilities';\n\nexport interface Arguments\n  extends Omit<UseDraggableArguments, 'disabled'>,\n    Pick<UseDroppableArguments, 'resizeObserverConfig'> {\n  animateLayoutChanges?: AnimateLayoutChanges;\n  disabled?: boolean | Disabled;\n  getNewIndex?: NewIndexGetter;\n  strategy?: SortingStrategy;\n  transition?: SortableTransition | null;\n}\n\nexport function useSortable({\n  animateLayoutChanges = defaultAnimateLayoutChanges,\n  attributes: userDefinedAttributes,\n  disabled: localDisabled,\n  data: customData,\n  getNewIndex = defaultNewIndexGetter,\n  id,\n  strategy: localStrategy,\n  resizeObserverConfig,\n  transition = defaultTransition,\n}: Arguments) {\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy,\n  } = useContext(Context);\n  const disabled: Disabled = normalizeLocalDisabled(\n    localDisabled,\n    globalDisabled\n  );\n  const index = items.indexOf(id);\n  const data = useMemo<SortableData & Data>(\n    () => ({sortable: {containerId, index, items}, ...customData}),\n    [containerId, customData, index, items]\n  );\n  const itemsAfterCurrentSortable = useMemo(\n    () => items.slice(items.indexOf(id)),\n    [items, id]\n  );\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef,\n  } = useDroppable({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig,\n    },\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform,\n  } = useDraggable({\n    id,\n    data,\n    attributes: {\n      ...defaultAttributes,\n      ...userDefinedAttributes,\n    },\n    disabled: disabled.draggable,\n  });\n  const setNodeRef = useCombinedRefs(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem =\n    isSorting &&\n    !disableTransforms &&\n    isValidIndex(activeIndex) &&\n    isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement =\n    shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy ?? globalStrategy;\n  const finalTransform = displaceItem\n    ? dragSourceDisplacement ??\n      strategy({\n        rects: sortedRects,\n        activeNodeRect,\n        activeIndex,\n        overIndex,\n        index,\n      })\n    : null;\n  const newIndex =\n    isValidIndex(activeIndex) && isValidIndex(overIndex)\n      ? getNewIndex({id, items, activeIndex, overIndex})\n      : index;\n  const activeId = active?.id;\n  const previous = useRef({\n    activeId,\n    items,\n    newIndex,\n    containerId,\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null,\n  });\n\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect,\n  });\n\n  useEffect(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n\n  useEffect(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId != null && previous.current.activeId == null) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform ?? finalTransform,\n    transition: getTransition(),\n  };\n\n  function getTransition() {\n    if (\n      // Temporarily disable transitions for a single frame to set up derived transforms\n      derivedTransform ||\n      // Or to prevent items jumping to back to their \"new\" position when items change\n      (itemsHaveChanged && previous.current.newIndex === index)\n    ) {\n      return disabledTransition;\n    }\n\n    if (\n      (shouldDisplaceDragSource && !isKeyboardEvent(activatorEvent)) ||\n      !transition\n    ) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return CSS.Transition.toString({\n        ...transition,\n        property: transitionProperty,\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(\n  localDisabled: Arguments['disabled'],\n  globalDisabled: Disabled\n) {\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false,\n    };\n  }\n\n  return {\n    draggable: localDisabled?.draggable ?? globalDisabled.draggable,\n    droppable: localDisabled?.droppable ?? globalDisabled.droppable,\n  };\n}\n", "import type {\n  Active,\n  Data,\n  DroppableContainer,\n  DraggableNode,\n  Over,\n} from '@dnd-kit/core';\n\nimport type {SortableData} from './data';\n\nexport function hasSortableData<\n  T extends Active | Over | DraggableNode | DroppableContainer\n>(\n  entry: T | null | undefined\n): entry is T & {data: {current: Data<SortableData>}} {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (\n    data &&\n    'sortable' in data &&\n    typeof data.sortable === 'object' &&\n    'containerId' in data.sortable &&\n    'items' in data.sortable &&\n    'index' in data.sortable\n  ) {\n    return true;\n  }\n\n  return false;\n}\n", "import {\n  closestCorners,\n  getScrollableAncestors,\n  getFirstCollision,\n  KeyboardCode,\n  DroppableContainer,\n  KeyboardCoordinateGetter,\n} from '@dnd-kit/core';\nimport {subtract} from '@dnd-kit/utilities';\n\nimport {hasSortableData} from '../../types';\n\nconst directions: string[] = [\n  KeyboardCode.Down,\n  KeyboardCode.Right,\n  KeyboardCode.Up,\n  KeyboardCode.Left,\n];\n\nexport const sortableKeyboardCoordinates: KeyboardCoordinateGetter = (\n  event,\n  {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n    },\n  }\n) => {\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers: DroppableContainer[] = [];\n\n    droppableContainers.getEnabled().forEach((entry) => {\n      if (!entry || entry?.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n      }\n    });\n\n    const collisions = closestCorners({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null,\n    });\n    let closestId = getFirstCollision(collisions, 'id');\n\n    if (closestId === over?.id && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable?.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = getScrollableAncestors(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some(\n          (element, index) => scrollableAncestors[index] !== element\n        );\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset =\n          hasDifferentScrollAncestors || !hasSameContainer\n            ? {\n                x: 0,\n                y: 0,\n              }\n            : {\n                x: isAfterActive ? collisionRect.width - newRect.width : 0,\n                y: isAfterActive ? collisionRect.height - newRect.height : 0,\n              };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top,\n        };\n\n        const newCoordinates =\n          offset.x && offset.y\n            ? rectCoordinates\n            : subtract(rectCoordinates, offset);\n\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return (\n    a.data.current.sortable.containerId === b.data.current.sortable.containerId\n  );\n}\n\nfunction isAfter(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n"], "mappings": ";;;;AAAA;;;SAGgBA,UAAaC,KAAA,EAAYC,IAAA,EAAcC,EAAA;EACrD,MAAMC,QAAQ,GAAGH,KAAK,CAACI,KAAN,EAAjB;EACAD,QAAQ,CAACE,MAAT,CACEH,EAAE,GAAG,CAAL,GAASC,QAAQ,CAACG,MAAT,GAAkBJ,EAA3B,GAAgCA,EADlC,EAEE,CAFF,EAGEC,QAAQ,CAACE,MAAT,CAAgBJ,IAAhB,EAAsB,CAAtB,EAAyB,CAAzB,CAHF;EAMA,OAAOE,QAAP;AACD;;ACZD;;;AAGA,SAAgBI,UAAaP,KAAA,EAAYC,IAAA,EAAcC,EAAA;EACrD,MAAMC,QAAQ,GAAGH,KAAK,CAACI,KAAN,EAAjB;EAEAD,QAAQ,CAACF,IAAD,CAAR,GAAiBD,KAAK,CAACE,EAAD,CAAtB;EACAC,QAAQ,CAACD,EAAD,CAAR,GAAeF,KAAK,CAACC,IAAD,CAApB;EAEA,OAAOE,QAAP;AACD;SCJeK,eACdC,KAAA,EACAC,KAAA;EAEA,OAAOD,KAAK,CAACE,MAAN,CAA2B,CAACC,WAAD,EAAcC,EAAd,EAAkBC,KAAlB;IAChC,MAAMC,IAAI,GAAGL,KAAK,CAACM,GAAN,CAAUH,EAAV,CAAb;IAEA,IAAIE,IAAJ,EAAU;MACRH,WAAW,CAACE,KAAD,CAAX,GAAqBC,IAArB;;IAGF,OAAOH,WAAP;GAPK,EAQJK,KAAK,CAACR,KAAK,CAACH,MAAP,CARD,CAAP;AASD;SCnBeY,aAAaJ,KAAA;EAC3B,OAAOA,KAAK,KAAK,IAAV,IAAkBA,KAAK,IAAI,CAAlC;AACD;SCAeK,WAAWC,CAAA,EAAuBC,CAAA;EAChD,IAAID,CAAC,KAAKC,CAAV,EAAa;IACX,OAAO,IAAP;;EAGF,IAAID,CAAC,CAACd,MAAF,KAAae,CAAC,CAACf,MAAnB,EAA2B;IACzB,OAAO,KAAP;;EAGF,KAAK,IAAIgB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,CAAC,CAACd,MAAtB,EAA8BgB,CAAC,EAA/B,EAAmC;IACjC,IAAIF,CAAC,CAACE,CAAD,CAAD,KAASD,CAAC,CAACC,CAAD,CAAd,EAAmB;MACjB,OAAO,KAAP;;;EAIJ,OAAO,IAAP;AACD;SChBeC,kBAAkBC,QAAA;EAChC,IAAI,OAAOA,QAAP,KAAoB,SAAxB,EAAmC;IACjC,OAAO;MACLC,SAAS,EAAED,QADN;MAELE,SAAS,EAAEF;KAFb;;EAMF,OAAOA,QAAP;AACD;;ACRD;AACA,MAAMG,YAAY,GAAG;EACnBC,MAAM,EAAE,CADW;EAEnBC,MAAM,EAAE;AAFW,CAArB;AAKA,MAAaC,6BAA6B,GAAoBC,IAAA;;MAAC;IAC7DrB,KAD6D;IAE7DsB,cAAc,EAAEC,kBAF6C;IAG7DC,WAH6D;IAI7DC,SAJ6D;IAK7DrB;;EAEA,MAAMkB,cAAc,IAAAI,kBAAA,GAAG1B,KAAK,CAACwB,WAAD,CAAR,YAAAE,kBAAA,GAAyBH,kBAA7C;EAEA,IAAI,CAACD,cAAL,EAAqB;IACnB,OAAO,IAAP;;EAGF,MAAMK,OAAO,GAAGC,UAAU,CAAC5B,KAAD,EAAQI,KAAR,EAAeoB,WAAf,CAA1B;EAEA,IAAIpB,KAAK,KAAKoB,WAAd,EAA2B;IACzB,MAAMK,YAAY,GAAG7B,KAAK,CAACyB,SAAD,CAA1B;IAEA,IAAI,CAACI,YAAL,EAAmB;MACjB,OAAO,IAAP;;IAGF,OAAO;MACLC,CAAC,EACCN,WAAW,GAAGC,SAAd,GACII,YAAY,CAACE,IAAb,GACAF,YAAY,CAACG,KADb,IAECV,cAAc,CAACS,IAAf,GAAsBT,cAAc,CAACU,KAFtC,CADJ,GAIIH,YAAY,CAACE,IAAb,GAAoBT,cAAc,CAACS,IANpC;MAOLE,CAAC,EAAE,CAPE;MAQL,GAAGhB;KARL;;EAYF,IAAIb,KAAK,GAAGoB,WAAR,IAAuBpB,KAAK,IAAIqB,SAApC,EAA+C;IAC7C,OAAO;MACLK,CAAC,EAAE,CAACR,cAAc,CAACU,KAAhB,GAAwBL,OADtB;MAELM,CAAC,EAAE,CAFE;MAGL,GAAGhB;KAHL;;EAOF,IAAIb,KAAK,GAAGoB,WAAR,IAAuBpB,KAAK,IAAIqB,SAApC,EAA+C;IAC7C,OAAO;MACLK,CAAC,EAAER,cAAc,CAACU,KAAf,GAAuBL,OADrB;MAELM,CAAC,EAAE,CAFE;MAGL,GAAGhB;KAHL;;EAOF,OAAO;IACLa,CAAC,EAAE,CADE;IAELG,CAAC,EAAE,CAFE;IAGL,GAAGhB;GAHL;AAKD,CAvDM;AAyDP,SAASW,UAATA,CAAoB5B,KAApB,EAAyCI,KAAzC,EAAwDoB,WAAxD;EACE,MAAMU,WAAW,GAA2BlC,KAAK,CAACI,KAAD,CAAjD;EACA,MAAM+B,YAAY,GAA2BnC,KAAK,CAACI,KAAK,GAAG,CAAT,CAAlD;EACA,MAAMgC,QAAQ,GAA2BpC,KAAK,CAACI,KAAK,GAAG,CAAT,CAA9C;EAEA,IAAI,CAAC8B,WAAD,IAAiB,CAACC,YAAD,IAAiB,CAACC,QAAvC,EAAkD;IAChD,OAAO,CAAP;;EAGF,IAAIZ,WAAW,GAAGpB,KAAlB,EAAyB;IACvB,OAAO+B,YAAY,GACfD,WAAW,CAACH,IAAZ,IAAoBI,YAAY,CAACJ,IAAb,GAAoBI,YAAY,CAACH,KAArD,CADe,GAEfI,QAAQ,CAACL,IAAT,IAAiBG,WAAW,CAACH,IAAZ,GAAmBG,WAAW,CAACF,KAAhD,CAFJ;;EAKF,OAAOI,QAAQ,GACXA,QAAQ,CAACL,IAAT,IAAiBG,WAAW,CAACH,IAAZ,GAAmBG,WAAW,CAACF,KAAhD,CADW,GAEXE,WAAW,CAACH,IAAZ,IAAoBI,YAAY,CAACJ,IAAb,GAAoBI,YAAY,CAACH,KAArD,CAFJ;AAGD;MCjFYK,mBAAmB,GAAoBhB,IAAA;MAAC;IACnDrB,KADmD;IAEnDwB,WAFmD;IAGnDC,SAHmD;IAInDrB;;EAEA,MAAMkC,QAAQ,GAAGjD,SAAS,CAACW,KAAD,EAAQyB,SAAR,EAAmBD,WAAnB,CAA1B;EAEA,MAAMe,OAAO,GAAGvC,KAAK,CAACI,KAAD,CAArB;EACA,MAAMoC,OAAO,GAAGF,QAAQ,CAAClC,KAAD,CAAxB;EAEA,IAAI,CAACoC,OAAD,IAAY,CAACD,OAAjB,EAA0B;IACxB,OAAO,IAAP;;EAGF,OAAO;IACLT,CAAC,EAAEU,OAAO,CAACT,IAAR,GAAeQ,OAAO,CAACR,IADrB;IAELE,CAAC,EAAEO,OAAO,CAACC,GAAR,GAAcF,OAAO,CAACE,GAFpB;IAGLvB,MAAM,EAAEsB,OAAO,CAACR,KAAR,GAAgBO,OAAO,CAACP,KAH3B;IAILb,MAAM,EAAEqB,OAAO,CAACE,MAAR,GAAiBH,OAAO,CAACG;GAJnC;AAMD,CArBM;MCDMC,oBAAoB,GAAoBtB,IAAA;MAAC;IACpDG,WADoD;IAEpDpB,KAFoD;IAGpDJ,KAHoD;IAIpDyB;;EAEA,IAAIc,OAAJ;EACA,IAAIC,OAAJ;EAEA,IAAIpC,KAAK,KAAKoB,WAAd,EAA2B;IACzBe,OAAO,GAAGvC,KAAK,CAACI,KAAD,CAAf;IACAoC,OAAO,GAAGxC,KAAK,CAACyB,SAAD,CAAf;;EAGF,IAAIrB,KAAK,KAAKqB,SAAd,EAAyB;IACvBc,OAAO,GAAGvC,KAAK,CAACI,KAAD,CAAf;IACAoC,OAAO,GAAGxC,KAAK,CAACwB,WAAD,CAAf;;EAGF,IAAI,CAACgB,OAAD,IAAY,CAACD,OAAjB,EAA0B;IACxB,OAAO,IAAP;;EAGF,OAAO;IACLT,CAAC,EAAEU,OAAO,CAACT,IAAR,GAAeQ,OAAO,CAACR,IADrB;IAELE,CAAC,EAAEO,OAAO,CAACC,GAAR,GAAcF,OAAO,CAACE,GAFpB;IAGLvB,MAAM,EAAEsB,OAAO,CAACR,KAAR,GAAgBO,OAAO,CAACP,KAH3B;IAILb,MAAM,EAAEqB,OAAO,CAACE,MAAR,GAAiBH,OAAO,CAACG;GAJnC;AAMD,CA7BM;;ACCP;AACA,MAAME,cAAY,GAAG;EACnB1B,MAAM,EAAE,CADW;EAEnBC,MAAM,EAAE;AAFW,CAArB;AAKA,MAAa0B,2BAA2B,GAAoBxB,IAAA;;MAAC;IAC3DG,WAD2D;IAE3DF,cAAc,EAAEC,kBAF2C;IAG3DnB,KAH2D;IAI3DJ,KAJ2D;IAK3DyB;;EAEA,MAAMH,cAAc,IAAAI,kBAAA,GAAG1B,KAAK,CAACwB,WAAD,CAAR,YAAAE,kBAAA,GAAyBH,kBAA7C;EAEA,IAAI,CAACD,cAAL,EAAqB;IACnB,OAAO,IAAP;;EAGF,IAAIlB,KAAK,KAAKoB,WAAd,EAA2B;IACzB,MAAMsB,aAAa,GAAG9C,KAAK,CAACyB,SAAD,CAA3B;IAEA,IAAI,CAACqB,aAAL,EAAoB;MAClB,OAAO,IAAP;;IAGF,OAAO;MACLhB,CAAC,EAAE,CADE;MAELG,CAAC,EACCT,WAAW,GAAGC,SAAd,GACIqB,aAAa,CAACL,GAAd,GACAK,aAAa,CAACJ,MADd,IAECpB,cAAc,CAACmB,GAAf,GAAqBnB,cAAc,CAACoB,MAFrC,CADJ,GAIII,aAAa,CAACL,GAAd,GAAoBnB,cAAc,CAACmB,GAPpC;MAQL,GAAGG;KARL;;EAYF,MAAMjB,OAAO,GAAGoB,YAAU,CAAC/C,KAAD,EAAQI,KAAR,EAAeoB,WAAf,CAA1B;EAEA,IAAIpB,KAAK,GAAGoB,WAAR,IAAuBpB,KAAK,IAAIqB,SAApC,EAA+C;IAC7C,OAAO;MACLK,CAAC,EAAE,CADE;MAELG,CAAC,EAAE,CAACX,cAAc,CAACoB,MAAhB,GAAyBf,OAFvB;MAGL,GAAGiB;KAHL;;EAOF,IAAIxC,KAAK,GAAGoB,WAAR,IAAuBpB,KAAK,IAAIqB,SAApC,EAA+C;IAC7C,OAAO;MACLK,CAAC,EAAE,CADE;MAELG,CAAC,EAAEX,cAAc,CAACoB,MAAf,GAAwBf,OAFtB;MAGL,GAAGiB;KAHL;;EAOF,OAAO;IACLd,CAAC,EAAE,CADE;IAELG,CAAC,EAAE,CAFE;IAGL,GAAGW;GAHL;AAKD,CAvDM;AAyDP,SAASG,YAATnB,CACEoB,WADF,EAEE5C,KAFF,EAGEoB,WAHF;EAKE,MAAMU,WAAW,GAA2Bc,WAAW,CAAC5C,KAAD,CAAvD;EACA,MAAM+B,YAAY,GAA2Ba,WAAW,CAAC5C,KAAK,GAAG,CAAT,CAAxD;EACA,MAAMgC,QAAQ,GAA2BY,WAAW,CAAC5C,KAAK,GAAG,CAAT,CAApD;EAEA,IAAI,CAAC8B,WAAL,EAAkB;IAChB,OAAO,CAAP;;EAGF,IAAIV,WAAW,GAAGpB,KAAlB,EAAyB;IACvB,OAAO+B,YAAY,GACfD,WAAW,CAACO,GAAZ,IAAmBN,YAAY,CAACM,GAAb,GAAmBN,YAAY,CAACO,MAAnD,CADe,GAEfN,QAAQ,GACRA,QAAQ,CAACK,GAAT,IAAgBP,WAAW,CAACO,GAAZ,GAAkBP,WAAW,CAACQ,MAA9C,CADQ,GAER,CAJJ;;EAOF,OAAON,QAAQ,GACXA,QAAQ,CAACK,GAAT,IAAgBP,WAAW,CAACO,GAAZ,GAAkBP,WAAW,CAACQ,MAA9C,CADW,GAEXP,YAAY,GACZD,WAAW,CAACO,GAAZ,IAAmBN,YAAY,CAACM,GAAb,GAAmBN,YAAY,CAACO,MAAnD,CADY,GAEZ,CAJJ;AAKD;AC5ED,MAAMO,SAAS,GAAG,UAAlB;AAcA,MAAaC,OAAO,gBAAGC,KAAK,CAACC,aAAN,CAAuC;EAC5D5B,WAAW,EAAE,CAAC,CAD8C;EAE5D6B,WAAW,EAAEJ,SAF+C;EAG5DK,iBAAiB,EAAE,KAHyC;EAI5DvD,KAAK,EAAE,EAJqD;EAK5D0B,SAAS,EAAE,CAAC,CALgD;EAM5D8B,cAAc,EAAE,KAN4C;EAO5DC,WAAW,EAAE,EAP+C;EAQ5DC,QAAQ,EAAEpB,mBARkD;EAS5DvB,QAAQ,EAAE;IACRC,SAAS,EAAE,KADH;IAERC,SAAS,EAAE;;AAX+C,CAAvC,CAAhB;AAeP,SAAgB0C,gBAAArC,IAAA;MAAgB;IAC9BsC,QAD8B;IAE9BxD,EAF8B;IAG9BJ,KAAK,EAAE6D,gBAHuB;IAI9BH,QAAQ,GAAGpB,mBAJmB;IAK9BvB,QAAQ,EAAE+C,YAAY,GAAG;;EAEzB,MAAM;IACJC,MADI;IAEJC,WAFI;IAGJC,cAHI;IAIJC,IAJI;IAKJC;MACEC,aAAa,EANjB;EAOA,MAAMd,WAAW,GAAGe,WAAW,CAACnB,SAAD,EAAY9C,EAAZ,CAA/B;EACA,MAAMoD,cAAc,GAAGc,OAAO,CAACN,WAAW,CAAC1D,IAAZ,KAAqB,IAAtB,CAA9B;EACA,MAAMN,KAAK,GAAGuE,OAAO,CACnB,MACEV,gBAAgB,CAACW,GAAjB,CAAsBC,IAAD,IACnB,OAAOA,IAAP,KAAgB,QAAhB,IAA4B,QAAQA,IAApC,GAA2CA,IAAI,CAACrE,EAAhD,GAAqDqE,IADvD,CAFiB,EAKnB,CAACZ,gBAAD,CALmB,CAArB;EAOA,MAAMa,UAAU,GAAGX,MAAM,IAAI,IAA7B;EACA,MAAMtC,WAAW,GAAGsC,MAAM,GAAG/D,KAAK,CAAC2E,OAAN,CAAcZ,MAAM,CAAC3D,EAArB,CAAH,GAA8B,CAAC,CAAzD;EACA,MAAMsB,SAAS,GAAGwC,IAAI,GAAGlE,KAAK,CAAC2E,OAAN,CAAcT,IAAI,CAAC9D,EAAnB,CAAH,GAA4B,CAAC,CAAnD;EACA,MAAMwE,gBAAgB,GAAGC,MAAM,CAAC7E,KAAD,CAA/B;EACA,MAAM8E,gBAAgB,GAAG,CAACpE,UAAU,CAACV,KAAD,EAAQ4E,gBAAgB,CAACG,OAAzB,CAApC;EACA,MAAMxB,iBAAiB,GACpB7B,SAAS,KAAK,CAAC,CAAf,IAAoBD,WAAW,KAAK,CAAC,CAAtC,IAA4CqD,gBAD9C;EAEA,MAAM/D,QAAQ,GAAGD,iBAAiB,CAACgD,YAAD,CAAlC;EAEAkB,yBAAyB,CAAC;IACxB,IAAIF,gBAAgB,IAAIJ,UAAxB,EAAoC;MAClCP,0BAA0B,CAACnE,KAAD,CAA1B;;GAFqB,EAItB,CAAC8E,gBAAD,EAAmB9E,KAAnB,EAA0B0E,UAA1B,EAAsCP,0BAAtC,CAJsB,CAAzB;EAMAc,SAAS,CAAC;IACRL,gBAAgB,CAACG,OAAjB,GAA2B/E,KAA3B;GADO,EAEN,CAACA,KAAD,CAFM,CAAT;EAIA,MAAMkF,YAAY,GAAGX,OAAO,CAC1B,OAA0B;IACxB9C,WADwB;IAExB6B,WAFwB;IAGxBvC,QAHwB;IAIxBwC,iBAJwB;IAKxBvD,KALwB;IAMxB0B,SANwB;IAOxB8B,cAPwB;IAQxBC,WAAW,EAAE1D,cAAc,CAACC,KAAD,EAAQiE,cAAR,CARH;IASxBP;GATF,CAD0B;EAAA;EAa1B,CACEjC,WADF,EAEE6B,WAFF,EAGEvC,QAAQ,CAACC,SAHX,EAIED,QAAQ,CAACE,SAJX,EAKEsC,iBALF,EAMEvD,KANF,EAOE0B,SAPF,EAQEuC,cARF,EASET,cATF,EAUEE,QAVF,CAb0B,CAA5B;EA2BA,OAAON,KAAA,CAAA+B,aAAA,CAAChC,OAAO,CAACiC,QAAT;IAAkBC,KAAK,EAAEH;GAAzB,EAAwCtB,QAAxC,CAAP;AACD;MCzGY0B,qBAAqB,GAAmBhE,IAAA;EAAA,IAAC;IACpDlB,EADoD;IAEpDJ,KAFoD;IAGpDyB,WAHoD;IAIpDC;GAJmD,GAAAJ,IAAA;EAAA,OAK/ChC,SAAS,CAACU,KAAD,EAAQyB,WAAR,EAAqBC,SAArB,CAAT,CAAyCiD,OAAzC,CAAiDvE,EAAjD,CAL+C;AAAA,CAA9C;AAOP,MAAamF,2BAA2B,GAAyBC,KAAA;MAAC;IAChElC,WADgE;IAEhEmC,SAFgE;IAGhEC,WAHgE;IAIhErF,KAJgE;IAKhEL,KALgE;IAMhE2F,QANgE;IAOhEC,aAPgE;IAQhEC,mBARgE;IAShEC;;EAEA,IAAI,CAACA,UAAD,IAAe,CAACJ,WAApB,EAAiC;IAC/B,OAAO,KAAP;;EAGF,IAAIE,aAAa,KAAK5F,KAAlB,IAA2BK,KAAK,KAAKsF,QAAzC,EAAmD;IACjD,OAAO,KAAP;;EAGF,IAAIF,SAAJ,EAAe;IACb,OAAO,IAAP;;EAGF,OAAOE,QAAQ,KAAKtF,KAAb,IAAsBiD,WAAW,KAAKuC,mBAA7C;AACD,CAxBM;AA0BP,MAAaE,iBAAiB,GAAuB;EACnDC,QAAQ,EAAE,GADyC;EAEnDC,MAAM,EAAE;AAF2C,CAA9C;AAKP,MAAaC,kBAAkB,GAAG,WAA3B;AAEP,MAAaC,kBAAkB,gBAAGC,GAAG,CAACC,UAAJ,CAAeC,QAAf,CAAwB;EACxDC,QAAQ,EAAEL,kBAD8C;EAExDF,QAAQ,EAAE,CAF8C;EAGxDC,MAAM,EAAE;AAHgD,CAAxB,CAA3B;AAMP,MAAaO,iBAAiB,GAAG;EAC/BC,eAAe,EAAE;AADc,CAA1B;;AC7CP;;;;;AAIA,SAAgBC,oBAAApF,IAAA;MAAoB;IAACP,QAAD;IAAWV,KAAX;IAAkBsG,IAAlB;IAAwBrG;;EAC1D,MAAM,CAACsG,gBAAD,EAAmBC,mBAAnB,IAA0CC,QAAQ,CACtD,IADsD,CAAxD;EAGA,MAAMC,aAAa,GAAGlC,MAAM,CAACxE,KAAD,CAA5B;EAEA2E,yBAAyB,CAAC;IACxB,IAAI,CAACjE,QAAD,IAAaV,KAAK,KAAK0G,aAAa,CAAChC,OAArC,IAAgD4B,IAAI,CAAC5B,OAAzD,EAAkE;MAChE,MAAMiC,OAAO,GAAG1G,IAAI,CAACyE,OAArB;MAEA,IAAIiC,OAAJ,EAAa;QACX,MAAMjC,OAAO,GAAGkC,aAAa,CAACN,IAAI,CAAC5B,OAAN,EAAe;UAC1CmC,eAAe,EAAE;SADU,CAA7B;QAIA,MAAMC,KAAK,GAAG;UACZpF,CAAC,EAAEiF,OAAO,CAAChF,IAAR,GAAe+C,OAAO,CAAC/C,IADd;UAEZE,CAAC,EAAE8E,OAAO,CAACtE,GAAR,GAAcqC,OAAO,CAACrC,GAFb;UAGZvB,MAAM,EAAE6F,OAAO,CAAC/E,KAAR,GAAgB8C,OAAO,CAAC9C,KAHpB;UAIZb,MAAM,EAAE4F,OAAO,CAACrE,MAAR,GAAiBoC,OAAO,CAACpC;SAJnC;QAOA,IAAIwE,KAAK,CAACpF,CAAN,IAAWoF,KAAK,CAACjF,CAArB,EAAwB;UACtB2E,mBAAmB,CAACM,KAAD,CAAnB;;;;IAKN,IAAI9G,KAAK,KAAK0G,aAAa,CAAChC,OAA5B,EAAqC;MACnCgC,aAAa,CAAChC,OAAd,GAAwB1E,KAAxB;;GAvBqB,EAyBtB,CAACU,QAAD,EAAWV,KAAX,EAAkBsG,IAAlB,EAAwBrG,IAAxB,CAzBsB,CAAzB;EA2BA2E,SAAS,CAAC;IACR,IAAI2B,gBAAJ,EAAsB;MACpBC,mBAAmB,CAAC,IAAD,CAAnB;;GAFK,EAIN,CAACD,gBAAD,CAJM,CAAT;EAMA,OAAOA,gBAAP;AACD;SCjBeQ,YAAA9F,IAAA;MAAY;IAC1B+F,oBAAoB,GAAG9B,2BADG;IAE1B+B,UAAU,EAAEC,qBAFc;IAG1BxG,QAAQ,EAAEyG,aAHgB;IAI1BC,IAAI,EAAEC,UAJoB;IAK1BC,WAAW,GAAGrC,qBALY;IAM1BlF,EAN0B;IAO1BsD,QAAQ,EAAEkE,aAPgB;IAQ1BC,oBAR0B;IAS1B/B,UAAU,GAAGC;;EAEb,MAAM;IACJ/F,KADI;IAEJsD,WAFI;IAGJ7B,WAHI;IAIJV,QAAQ,EAAE+G,cAJN;IAKJvE,iBALI;IAMJE,WANI;IAOJ/B,SAPI;IAQJ8B,cARI;IASJE,QAAQ,EAAEqE;MACRC,UAAU,CAAC7E,OAAD,CAVd;EAWA,MAAMpC,QAAQ,GAAakH,sBAAsB,CAC/CT,aAD+C,EAE/CM,cAF+C,CAAjD;EAIA,MAAMzH,KAAK,GAAGL,KAAK,CAAC2E,OAAN,CAAcvE,EAAd,CAAd;EACA,MAAMqH,IAAI,GAAGlD,OAAO,CAClB,OAAO;IAAC2D,QAAQ,EAAE;MAAC5E,WAAD;MAAcjD,KAAd;MAAqBL;KAAhC;IAAwC,GAAG0H;GAAlD,CADkB,EAElB,CAACpE,WAAD,EAAcoE,UAAd,EAA0BrH,KAA1B,EAAiCL,KAAjC,CAFkB,CAApB;EAIA,MAAMmI,yBAAyB,GAAG5D,OAAO,CACvC,MAAMvE,KAAK,CAACL,KAAN,CAAYK,KAAK,CAAC2E,OAAN,CAAcvE,EAAd,CAAZ,CADiC,EAEvC,CAACJ,KAAD,EAAQI,EAAR,CAFuC,CAAzC;EAIA,MAAM;IACJE,IADI;IAEJqG,IAFI;IAGJyB,MAHI;IAIJC,UAAU,EAAEC;MACVC,YAAY,CAAC;IACfnI,EADe;IAEfqH,IAFe;IAGf1G,QAAQ,EAAEA,QAAQ,CAACE,SAHJ;IAIf4G,oBAAoB,EAAE;MACpBW,qBAAqB,EAAEL,yBADH;MAEpB,GAAGN;;GANS,CALhB;EAcA,MAAM;IACJ9D,MADI;IAEJ0E,cAFI;IAGJlH,cAHI;IAIJ+F,UAJI;IAKJe,UAAU,EAAEK,mBALR;IAMJC,SANI;IAOJjE,UAPI;IAQJR,IARI;IASJ0E,mBATI;IAUJC;MACEC,YAAY,CAAC;IACf1I,EADe;IAEfqH,IAFe;IAGfH,UAAU,EAAE;MACV,GAAGd,iBADO;MAEV,GAAGe;KALU;IAOfxG,QAAQ,EAAEA,QAAQ,CAACC;GAPL,CAXhB;EAoBA,MAAMqH,UAAU,GAAGU,eAAe,CAACT,mBAAD,EAAsBI,mBAAtB,CAAlC;EACA,MAAMjD,SAAS,GAAGnB,OAAO,CAACP,MAAD,CAAzB;EACA,MAAMiF,YAAY,GAChBvD,SAAS,IACT,CAAClC,iBADD,IAEA9C,YAAY,CAACgB,WAAD,CAFZ,IAGAhB,YAAY,CAACiB,SAAD,CAJd;EAKA,MAAMuH,wBAAwB,GAAG,CAACzF,cAAD,IAAmBkB,UAApD;EACA,MAAMwE,sBAAsB,GAC1BD,wBAAwB,IAAID,YAA5B,GAA2CH,SAA3C,GAAuD,IADzD;EAEA,MAAMnF,QAAQ,GAAGkE,aAAH,WAAGA,aAAH,GAAoBG,cAAlC;EACA,MAAMoB,cAAc,GAAGH,YAAY,GAC/BE,sBAD+B,WAC/BA,sBAD+B,GAE/BxF,QAAQ,CAAC;IACPzD,KAAK,EAAEwD,WADA;IAEPlC,cAFO;IAGPE,WAHO;IAIPC,SAJO;IAKPrB;GALM,CAFuB,GAS/B,IATJ;EAUA,MAAMsF,QAAQ,GACZlF,YAAY,CAACgB,WAAD,CAAZ,IAA6BhB,YAAY,CAACiB,SAAD,CAAzC,GACIiG,WAAW,CAAC;IAACvH,EAAD;IAAKJ,KAAL;IAAYyB,WAAZ;IAAyBC;GAA1B,CADf,GAEIrB,KAHN;EAIA,MAAM+I,QAAQ,GAAGrF,MAAH,oBAAGA,MAAM,CAAE3D,EAAzB;EACA,MAAMiJ,QAAQ,GAAGxE,MAAM,CAAC;IACtBuE,QADsB;IAEtBpJ,KAFsB;IAGtB2F,QAHsB;IAItBrC;GAJqB,CAAvB;EAMA,MAAMwB,gBAAgB,GAAG9E,KAAK,KAAKqJ,QAAQ,CAACtE,OAAT,CAAiB/E,KAApD;EACA,MAAMsJ,0BAA0B,GAAGjC,oBAAoB,CAAC;IACtDtD,MADsD;IAEtDT,WAFsD;IAGtDoB,UAHsD;IAItDe,SAJsD;IAKtDrF,EALsD;IAMtDC,KANsD;IAOtDL,KAPsD;IAQtD2F,QAAQ,EAAE0D,QAAQ,CAACtE,OAAT,CAAiBY,QAR2B;IAStDC,aAAa,EAAEyD,QAAQ,CAACtE,OAAT,CAAiB/E,KATsB;IAUtD6F,mBAAmB,EAAEwD,QAAQ,CAACtE,OAAT,CAAiBzB,WAVgB;IAWtDwC,UAXsD;IAYtDJ,WAAW,EAAE2D,QAAQ,CAACtE,OAAT,CAAiBqE,QAAjB,IAA6B;GAZW,CAAvD;EAeA,MAAMxC,gBAAgB,GAAGF,mBAAmB,CAAC;IAC3C3F,QAAQ,EAAE,CAACuI,0BADgC;IAE3CjJ,KAF2C;IAG3CsG,IAH2C;IAI3CrG;GAJ0C,CAA5C;EAOA2E,SAAS,CAAC;IACR,IAAIQ,SAAS,IAAI4D,QAAQ,CAACtE,OAAT,CAAiBY,QAAjB,KAA8BA,QAA/C,EAAyD;MACvD0D,QAAQ,CAACtE,OAAT,CAAiBY,QAAjB,GAA4BA,QAA5B;;IAGF,IAAIrC,WAAW,KAAK+F,QAAQ,CAACtE,OAAT,CAAiBzB,WAArC,EAAkD;MAChD+F,QAAQ,CAACtE,OAAT,CAAiBzB,WAAjB,GAA+BA,WAA/B;;IAGF,IAAItD,KAAK,KAAKqJ,QAAQ,CAACtE,OAAT,CAAiB/E,KAA/B,EAAsC;MACpCqJ,QAAQ,CAACtE,OAAT,CAAiB/E,KAAjB,GAAyBA,KAAzB;;GAVK,EAYN,CAACyF,SAAD,EAAYE,QAAZ,EAAsBrC,WAAtB,EAAmCtD,KAAnC,CAZM,CAAT;EAcAiF,SAAS,CAAC;IACR,IAAImE,QAAQ,KAAKC,QAAQ,CAACtE,OAAT,CAAiBqE,QAAlC,EAA4C;MAC1C;;IAGF,IAAIA,QAAQ,IAAI,IAAZ,IAAoBC,QAAQ,CAACtE,OAAT,CAAiBqE,QAAjB,IAA6B,IAArD,EAA2D;MACzDC,QAAQ,CAACtE,OAAT,CAAiBqE,QAAjB,GAA4BA,QAA5B;MACA;;IAGF,MAAMG,SAAS,GAAGC,UAAU,CAAC;MAC3BH,QAAQ,CAACtE,OAAT,CAAiBqE,QAAjB,GAA4BA,QAA5B;KAD0B,EAEzB,EAFyB,CAA5B;IAIA,OAAO,MAAMK,YAAY,CAACF,SAAD,CAAzB;GAdO,EAeN,CAACH,QAAD,CAfM,CAAT;EAiBA,OAAO;IACLrF,MADK;IAELtC,WAFK;IAGL6F,UAHK;IAILG,IAJK;IAKLnH,IALK;IAMLD,KANK;IAOLsF,QAPK;IAQL3F,KARK;IASLoI,MATK;IAUL3C,SAVK;IAWLf,UAXK;IAYLiE,SAZK;IAaLhC,IAbK;IAcLjF,SAdK;IAeLwC,IAfK;IAgBLmE,UAhBK;IAiBLO,mBAjBK;IAkBLN,mBAlBK;IAmBLI,mBAnBK;IAoBLG,SAAS,EAAEjC,gBAAF,WAAEA,gBAAF,GAAsBuC,cApB1B;IAqBLrD,UAAU,EAAE4D,aAAa;GArB3B;EAwBA,SAASA,aAATA,CAAA;IACE;IAAA;IAEE9C,gBAAgB;IAAA;IAEf9B,gBAAgB,IAAIuE,QAAQ,CAACtE,OAAT,CAAiBY,QAAjB,KAA8BtF,KAJrD,EAKE;MACA,OAAO8F,kBAAP;;IAGF,IACG8C,wBAAwB,IAAI,CAACU,eAAe,CAAClB,cAAD,CAA7C,IACA,CAAC3C,UAFH,EAGE;MACA,OAAO8D,SAAP;;IAGF,IAAInE,SAAS,IAAI6D,0BAAjB,EAA6C;MAC3C,OAAOlD,GAAG,CAACC,UAAJ,CAAeC,QAAf,CAAwB;QAC7B,GAAGR,UAD0B;QAE7BS,QAAQ,EAAEL;OAFL,CAAP;;IAMF,OAAO0D,SAAP;;AAEH;AAED,SAAS3B,sBAATA,CACET,aADF,EAEEM,cAFF;;EAIE,IAAI,OAAON,aAAP,KAAyB,SAA7B,EAAwC;IACtC,OAAO;MACLxG,SAAS,EAAEwG,aADN;;MAGLvG,SAAS,EAAE;KAHb;;EAOF,OAAO;IACLD,SAAS,GAAA6I,qBAAA,GAAErC,aAAF,oBAAEA,aAAa,CAAExG,SAAjB,YAAA6I,qBAAA,GAA8B/B,cAAc,CAAC9G,SADjD;IAELC,SAAS,GAAA6I,qBAAA,GAAEtC,aAAF,oBAAEA,aAAa,CAAEvG,SAAjB,YAAA6I,qBAAA,GAA8BhC,cAAc,CAAC7G;GAFxD;AAID;SC3Pe8I,gBAGdC,KAAA;EAEA,IAAI,CAACA,KAAL,EAAY;IACV,OAAO,KAAP;;EAGF,MAAMvC,IAAI,GAAGuC,KAAK,CAACvC,IAAN,CAAW1C,OAAxB;EAEA,IACE0C,IAAI,IACJ,cAAcA,IADd,IAEA,OAAOA,IAAI,CAACS,QAAZ,KAAyB,QAFzB,IAGA,iBAAiBT,IAAI,CAACS,QAHtB,IAIA,WAAWT,IAAI,CAACS,QAJhB,IAKA,WAAWT,IAAI,CAACS,QANlB,EAOE;IACA,OAAO,IAAP;;EAGF,OAAO,KAAP;AACD;ACrBD,MAAM+B,UAAU,GAAa,CAC3BC,YAAY,CAACC,IADc,EAE3BD,YAAY,CAACE,KAFc,EAG3BF,YAAY,CAACG,EAHc,EAI3BH,YAAY,CAACI,IAJc,CAA7B;AAOA,MAAaC,2BAA2B,GAA6BA,CACnEC,KADmE,EAAAlJ,IAAA;MAEnE;IACEmJ,OAAO,EAAE;MACP1G,MADO;MAEP2G,aAFO;MAGPzG,cAHO;MAIP0G,mBAJO;MAKPzG,IALO;MAMP0G;;;EAIJ,IAAIX,UAAU,CAACY,QAAX,CAAoBL,KAAK,CAACM,IAA1B,CAAJ,EAAqC;IACnCN,KAAK,CAACO,cAAN;IAEA,IAAI,CAAChH,MAAD,IAAW,CAAC2G,aAAhB,EAA+B;MAC7B;;IAGF,MAAMM,kBAAkB,GAAyB,EAAjD;IAEAL,mBAAmB,CAACM,UAApB,GAAiCC,OAAjC,CAA0ClB,KAAD;MACvC,IAAI,CAACA,KAAD,IAAUA,KAAV,YAAUA,KAAK,CAAEjJ,QAArB,EAA+B;QAC7B;;MAGF,MAAMT,IAAI,GAAG2D,cAAc,CAAC1D,GAAf,CAAmByJ,KAAK,CAAC5J,EAAzB,CAAb;MAEA,IAAI,CAACE,IAAL,EAAW;QACT;;MAGF,QAAQkK,KAAK,CAACM,IAAd;QACE,KAAKZ,YAAY,CAACC,IAAlB;UACE,IAAIO,aAAa,CAAChI,GAAd,GAAoBpC,IAAI,CAACoC,GAA7B,EAAkC;YAChCsI,kBAAkB,CAACG,IAAnB,CAAwBnB,KAAxB;;UAEF;QACF,KAAKE,YAAY,CAACG,EAAlB;UACE,IAAIK,aAAa,CAAChI,GAAd,GAAoBpC,IAAI,CAACoC,GAA7B,EAAkC;YAChCsI,kBAAkB,CAACG,IAAnB,CAAwBnB,KAAxB;;UAEF;QACF,KAAKE,YAAY,CAACI,IAAlB;UACE,IAAII,aAAa,CAAC1I,IAAd,GAAqB1B,IAAI,CAAC0B,IAA9B,EAAoC;YAClCgJ,kBAAkB,CAACG,IAAnB,CAAwBnB,KAAxB;;UAEF;QACF,KAAKE,YAAY,CAACE,KAAlB;UACE,IAAIM,aAAa,CAAC1I,IAAd,GAAqB1B,IAAI,CAAC0B,IAA9B,EAAoC;YAClCgJ,kBAAkB,CAACG,IAAnB,CAAwBnB,KAAxB;;UAEF;;KA/BN;IAmCA,MAAMoB,UAAU,GAAGC,cAAc,CAAC;MAChCtH,MADgC;MAEhC2G,aAAa,EAAEA,aAFiB;MAGhCzG,cAHgC;MAIhC0G,mBAAmB,EAAEK,kBAJW;MAKhCM,kBAAkB,EAAE;KALW,CAAjC;IAOA,IAAIC,SAAS,GAAGC,iBAAiB,CAACJ,UAAD,EAAa,IAAb,CAAjC;IAEA,IAAIG,SAAS,MAAKrH,IAAL,oBAAKA,IAAI,CAAE9D,EAAX,CAAT,IAA0BgL,UAAU,CAACvL,MAAX,GAAoB,CAAlD,EAAqD;MACnD0L,SAAS,GAAGH,UAAU,CAAC,CAAD,CAAV,CAAchL,EAA1B;;IAGF,IAAImL,SAAS,IAAI,IAAjB,EAAuB;MACrB,MAAME,eAAe,GAAGd,mBAAmB,CAACpK,GAApB,CAAwBwD,MAAM,CAAC3D,EAA/B,CAAxB;MACA,MAAMsL,YAAY,GAAGf,mBAAmB,CAACpK,GAApB,CAAwBgL,SAAxB,CAArB;MACA,MAAM9I,OAAO,GAAGiJ,YAAY,GAAGzH,cAAc,CAAC1D,GAAf,CAAmBmL,YAAY,CAACtL,EAAhC,CAAH,GAAyC,IAArE;MACA,MAAMuL,OAAO,GAAGD,YAAH,oBAAGA,YAAY,CAAE/E,IAAd,CAAmB5B,OAAnC;MAEA,IAAI4G,OAAO,IAAIlJ,OAAX,IAAsBgJ,eAAtB,IAAyCC,YAA7C,EAA2D;QACzD,MAAME,kBAAkB,GAAGC,sBAAsB,CAACF,OAAD,CAAjD;QACA,MAAMG,2BAA2B,GAAGF,kBAAkB,CAACG,IAAnB,CAClC,CAACC,OAAD,EAAU3L,KAAV,KAAoBuK,mBAAmB,CAACvK,KAAD,CAAnB,KAA+B2L,OADjB,CAApC;QAGA,MAAMC,gBAAgB,GAAGC,eAAe,CAACT,eAAD,EAAkBC,YAAlB,CAAxC;QACA,MAAMS,aAAa,GAAGC,OAAO,CAACX,eAAD,EAAkBC,YAAlB,CAA7B;QACA,MAAMW,MAAM,GACVP,2BAA2B,IAAI,CAACG,gBAAhC,GACI;UACElK,CAAC,EAAE,CADL;UAEEG,CAAC,EAAE;SAHT,GAKI;UACEH,CAAC,EAAEoK,aAAa,GAAGzB,aAAa,CAACzI,KAAd,GAAsBQ,OAAO,CAACR,KAAjC,GAAyC,CAD3D;UAEEC,CAAC,EAAEiK,aAAa,GAAGzB,aAAa,CAAC/H,MAAd,GAAuBF,OAAO,CAACE,MAAlC,GAA2C;SARnE;QAUA,MAAM2J,eAAe,GAAG;UACtBvK,CAAC,EAAEU,OAAO,CAACT,IADW;UAEtBE,CAAC,EAAEO,OAAO,CAACC;SAFb;QAKA,MAAM6J,cAAc,GAClBF,MAAM,CAACtK,CAAP,IAAYsK,MAAM,CAACnK,CAAnB,GACIoK,eADJ,GAEIE,QAAQ,CAACF,eAAD,EAAkBD,MAAlB,CAHd;QAKA,OAAOE,cAAP;;;;EAKN,OAAO3C,SAAP;AACD,CA7GM;AA+GP,SAASsC,eAATA,CAAyBvL,CAAzB,EAAgDC,CAAhD;EACE,IAAI,CAACmJ,eAAe,CAACpJ,CAAD,CAAhB,IAAuB,CAACoJ,eAAe,CAACnJ,CAAD,CAA3C,EAAgD;IAC9C,OAAO,KAAP;;EAGF,OACED,CAAC,CAAC8G,IAAF,CAAO1C,OAAP,CAAemD,QAAf,CAAwB5E,WAAxB,KAAwC1C,CAAC,CAAC6G,IAAF,CAAO1C,OAAP,CAAemD,QAAf,CAAwB5E,WADlE;AAGD;AAED,SAAS8I,OAATA,CAAiBzL,CAAjB,EAAwCC,CAAxC;EACE,IAAI,CAACmJ,eAAe,CAACpJ,CAAD,CAAhB,IAAuB,CAACoJ,eAAe,CAACnJ,CAAD,CAA3C,EAAgD;IAC9C,OAAO,KAAP;;EAGF,IAAI,CAACsL,eAAe,CAACvL,CAAD,EAAIC,CAAJ,CAApB,EAA4B;IAC1B,OAAO,KAAP;;EAGF,OAAOD,CAAC,CAAC8G,IAAF,CAAO1C,OAAP,CAAemD,QAAf,CAAwB7H,KAAxB,GAAgCO,CAAC,CAAC6G,IAAF,CAAO1C,OAAP,CAAemD,QAAf,CAAwB7H,KAA/D;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}