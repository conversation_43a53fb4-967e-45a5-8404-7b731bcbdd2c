{"ast": null, "code": "/**\n * Utility to suppress KaTeX character metrics warnings\n * These warnings occur when KaTeX encounters Unicode characters (like Vietnamese)\n * that don't have font metrics defined\n */\n\nexport const suppressKatexWarnings = callback => {\n  // Store original console.warn\n  const originalWarn = console.warn;\n\n  // Create a filtered warn function that ignores KaTeX character metrics warnings\n  console.warn = function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    const message = args.join(' ');\n\n    // Filter out KaTeX character metrics warnings\n    if (message.includes('No character metrics for') || message.includes('Unrecognized Unicode character') || message.includes('LaTeX-incompatible input')) {\n      return; // Ignore these warnings\n    }\n\n    // Allow other warnings through\n    originalWarn.apply(console, args);\n  };\n  try {\n    // Execute the callback\n    const result = callback();\n\n    // If it's a Promise, handle it properly\n    if (result && typeof result.then === 'function') {\n      return result.finally(() => {\n        console.warn = originalWarn;\n      });\n    }\n    return result;\n  } finally {\n    // Always restore original console.warn for synchronous operations\n    if (!result || typeof result.then !== 'function') {\n      console.warn = originalWarn;\n    }\n  }\n};\nexport const withSuppressedWarnings = Component => {\n  return props => {\n    return suppressKatexWarnings(() => Component(props));\n  };\n};", "map": {"version": 3, "names": ["suppressKatexWarnings", "callback", "originalWarn", "console", "warn", "_len", "arguments", "length", "args", "Array", "_key", "message", "join", "includes", "apply", "result", "then", "finally", "withSuppressed<PERSON><PERSON>nings", "Component", "props"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/utils/suppressKatexWarnings.js"], "sourcesContent": ["/**\n * Utility to suppress KaTeX character metrics warnings\n * These warnings occur when KaTeX encounters Unicode characters (like Vietnamese)\n * that don't have font metrics defined\n */\n\nexport const suppressKatexWarnings = (callback) => {\n    // Store original console.warn\n    const originalWarn = console.warn;\n    \n    // Create a filtered warn function that ignores KaTeX character metrics warnings\n    console.warn = (...args) => {\n        const message = args.join(' ');\n        \n        // Filter out KaTeX character metrics warnings\n        if (message.includes('No character metrics for') || \n            message.includes('Unrecognized Unicode character') ||\n            message.includes('LaTeX-incompatible input')) {\n            return; // Ignore these warnings\n        }\n        \n        // Allow other warnings through\n        originalWarn.apply(console, args);\n    };\n    \n    try {\n        // Execute the callback\n        const result = callback();\n        \n        // If it's a Promise, handle it properly\n        if (result && typeof result.then === 'function') {\n            return result.finally(() => {\n                console.warn = originalWarn;\n            });\n        }\n        \n        return result;\n    } finally {\n        // Always restore original console.warn for synchronous operations\n        if (!result || typeof result.then !== 'function') {\n            console.warn = originalWarn;\n        }\n    }\n};\n\nexport const withSuppressedWarnings = (Component) => {\n    return (props) => {\n        return suppressKatexWarnings(() => Component(props));\n    };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,qBAAqB,GAAIC,QAAQ,IAAK;EAC/C;EACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,IAAI;;EAEjC;EACAD,OAAO,CAACC,IAAI,GAAG,YAAa;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IACnB,MAAMC,OAAO,GAAGH,IAAI,CAACI,IAAI,CAAC,GAAG,CAAC;;IAE9B;IACA,IAAID,OAAO,CAACE,QAAQ,CAAC,0BAA0B,CAAC,IAC5CF,OAAO,CAACE,QAAQ,CAAC,gCAAgC,CAAC,IAClDF,OAAO,CAACE,QAAQ,CAAC,0BAA0B,CAAC,EAAE;MAC9C,OAAO,CAAC;IACZ;;IAEA;IACAX,YAAY,CAACY,KAAK,CAACX,OAAO,EAAEK,IAAI,CAAC;EACrC,CAAC;EAED,IAAI;IACA;IACA,MAAMO,MAAM,GAAGd,QAAQ,CAAC,CAAC;;IAEzB;IACA,IAAIc,MAAM,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU,EAAE;MAC7C,OAAOD,MAAM,CAACE,OAAO,CAAC,MAAM;QACxBd,OAAO,CAACC,IAAI,GAAGF,YAAY;MAC/B,CAAC,CAAC;IACN;IAEA,OAAOa,MAAM;EACjB,CAAC,SAAS;IACN;IACA,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU,EAAE;MAC9Cb,OAAO,CAACC,IAAI,GAAGF,YAAY;IAC/B;EACJ;AACJ,CAAC;AAED,OAAO,MAAMgB,sBAAsB,GAAIC,SAAS,IAAK;EACjD,OAAQC,KAAK,IAAK;IACd,OAAOpB,qBAAqB,CAAC,MAAMmB,SAAS,CAACC,KAAK,CAAC,CAAC;EACxD,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}