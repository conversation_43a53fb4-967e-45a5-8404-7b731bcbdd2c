{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\QuestionContent.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setSelectedId } from \"src/features/questionsExam/questionsExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\nimport { deleteQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { Trash2 } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionContent = _ref => {\n  _s();\n  var _codes$chapter, _codes$chapter$find;\n  let {\n    question,\n    index\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    selectedId\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\\n            flex flex-col gap-3 p-3 rounded-lg border transition cursor-pointer\\n            \".concat(selectedId === question.id ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\"),\n    onClick: () => dispatch(setSelectedId(question.id)),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        div: true,\n        className: \"flex flex-wrap items-center text-xs text-gray-500 gap-x-4 gap-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-700\",\n          children: [\"C\\xE2u \", index + 1, \" (\", question.id, \") :\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this), question.isNewQuestion && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-[0.2rem] rounded-full text-xs bg-green-500 text-white\",\n          children: \"new\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 48\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"L\\u1EDBp: \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-700\",\n            children: question.class || \"Chưa xác định\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 32\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u0110\\u1ED9 kh\\xF3: \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-700\",\n            children: question.difficulty || \"Chưa xác định\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 35\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Ch\\u01B0\\u01A1ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-700\",\n            children: ((_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === question.chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || \"Chưa xác định\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => dispatch(deleteQuestion(question.id)),\n          className: \"p-1 text-red-600 hover:bg-red-50 rounded\",\n          title: \"X\\xF3a\",\n          children: /*#__PURE__*/_jsxDEV(Trash2, {\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-base text-gray-800 leading-relaxed\",\n        children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          className: \"text-gray-800 text-xs\",\n          text: question.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 17\n      }, this), question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center w-full h-[10rem] mt-1\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: question.imageUrl,\n          alt: \"question\",\n          className: \"object-contain w-full h-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(SortableStatementsContainer, {\n      question: question\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"text-xs font-bold\",\n        children: \"L\\u1EDDi gi\\u1EA3i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: question.solution,\n        className: \" w-full\",\n        style: {\n          fontSize: '12px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 17\n    }, this)]\n  }, question.id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionContent, \"AfjN6IGtVYmfigb2x41Czu0K54o=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = QuestionContent;\nexport default QuestionContent;\nvar _c;\n$RefreshReg$(_c, \"QuestionContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "setSelectedId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "SortableStatementsContainer", "deleteQuestion", "Trash2", "jsxDEV", "_jsxDEV", "QuestionContent", "_ref", "_s", "_codes$chapter", "_codes$chapter$find", "question", "index", "dispatch", "selectedId", "state", "questionsExam", "codes", "className", "concat", "id", "onClick", "children", "div", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isNewQuestion", "class", "difficulty", "find", "c", "code", "chapter", "description", "title", "size", "text", "content", "imageUrl", "src", "alt", "solution", "style", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/QuestionContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { setSelectedId } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\r\nimport { deleteQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { Trash2 } from \"lucide-react\";\r\nconst QuestionContent = ({ question, index }) => {\r\n    const dispatch = useDispatch();\r\n    const { selectedId } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    return (\r\n        <div\r\n            key={question.id}\r\n            className={`\r\n            flex flex-col gap-3 p-3 rounded-lg border transition cursor-pointer\r\n            ${selectedId === question.id ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\"}`}\r\n            onClick={() => dispatch(setSelectedId(question.id))}\r\n        >\r\n            {/* Thông tin câu hỏi */}\r\n            <div className=\"flex justify-between items-center\">\r\n                <div div className=\"flex flex-wrap items-center text-xs text-gray-500 gap-x-4 gap-y-1\" >\r\n                    <span className=\"font-medium text-gray-700\">Câu {index + 1} ({question.id}) :</span>\r\n                    {question.isNewQuestion && <div className=\"p-[0.2rem] rounded-full text-xs bg-green-500 text-white\">new</div>}\r\n                    <span>Lớp: <span className=\"text-gray-700\">{question.class || \"Chưa xác định\"}</span></span>\r\n                    <span>Độ khó: <span className=\"text-gray-700\">{question.difficulty || \"Chưa xác định\"}</span></span>\r\n                    <span>\r\n                        Chương:{\" \"}\r\n                        <span className=\"text-gray-700\">\r\n                            {codes[\"chapter\"]?.find(c => c.code === question.chapter)?.description || \"Chưa xác định\"}\r\n                        </span>\r\n                    </span>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                    <button\r\n                        onClick={() => dispatch(deleteQuestion(question.id))}\r\n                        className=\"p-1 text-red-600 hover:bg-red-50 rounded\"\r\n                        title=\"Xóa\"\r\n                    >\r\n                        <Trash2 size={14} />\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n\r\n            <div className=\"flex flex-col gap-2\">\r\n                <div className=\"text-base text-gray-800 leading-relaxed\">\r\n                    <LatexRenderer className=\"text-gray-800 text-xs\" text={question.content} />\r\n                </div>\r\n                {question.imageUrl && (\r\n                    <div className=\"flex flex-col items-center justify-center w-full h-[10rem] mt-1\">\r\n                        <img\r\n                            src={question.imageUrl}\r\n                            alt=\"question\"\r\n                            className=\"object-contain w-full h-full\"\r\n                        />\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Statements with drag and drop */}\r\n            <SortableStatementsContainer question={question} />\r\n\r\n            {question.solution && (\r\n                <div className=\"mt-2\">\r\n                    <h6 className=\"text-xs font-bold\">Lời giải</h6>\r\n                    <MarkdownPreviewWithMath content={question.solution} className=\" w-full\" style={{ fontSize: '12px' }} />\r\n                </div>\r\n            )}\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default QuestionContent;"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,aAAa,QAAQ,+CAA+C;AAC7E,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAC9D,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACtC,MAAMC,eAAe,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,mBAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAL,IAAA;EACxC,MAAMM,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB;EAAW,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EAClE,MAAM;IAAEC;EAAM,CAAC,GAAGrB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,oBACIZ,OAAA;IAEIa,SAAS,oGAAAC,MAAA,CAEPL,UAAU,KAAKH,QAAQ,CAACS,EAAE,GAAG,4BAA4B,GAAG,2CAA2C,CAAG;IAC5GC,OAAO,EAAEA,CAAA,KAAMR,QAAQ,CAACf,aAAa,CAACa,QAAQ,CAACS,EAAE,CAAC,CAAE;IAAAE,QAAA,gBAGpDjB,OAAA;MAAKa,SAAS,EAAC,mCAAmC;MAAAI,QAAA,gBAC9CjB,OAAA;QAAKkB,GAAG;QAACL,SAAS,EAAC,mEAAmE;QAAAI,QAAA,gBAClFjB,OAAA;UAAMa,SAAS,EAAC,2BAA2B;UAAAI,QAAA,GAAC,SAAI,EAACV,KAAK,GAAG,CAAC,EAAC,IAAE,EAACD,QAAQ,CAACS,EAAE,EAAC,KAAG;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACnFhB,QAAQ,CAACiB,aAAa,iBAAIvB,OAAA;UAAKa,SAAS,EAAC,yDAAyD;UAAAI,QAAA,EAAC;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7GtB,OAAA;UAAAiB,QAAA,GAAM,YAAK,eAAAjB,OAAA;YAAMa,SAAS,EAAC,eAAe;YAAAI,QAAA,EAAEX,QAAQ,CAACkB,KAAK,IAAI;UAAe;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5FtB,OAAA;UAAAiB,QAAA,GAAM,uBAAQ,eAAAjB,OAAA;YAAMa,SAAS,EAAC,eAAe;YAAAI,QAAA,EAAEX,QAAQ,CAACmB,UAAU,IAAI;UAAe;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpGtB,OAAA;UAAAiB,QAAA,GAAM,mBACK,EAAC,GAAG,eACXjB,OAAA;YAAMa,SAAS,EAAC,eAAe;YAAAI,QAAA,EAC1B,EAAAb,cAAA,GAAAQ,KAAK,CAAC,SAAS,CAAC,cAAAR,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKtB,QAAQ,CAACuB,OAAO,CAAC,cAAAxB,mBAAA,uBAAxDA,mBAAA,CAA0DyB,WAAW,KAAI;UAAe;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNtB,OAAA;QAAKa,SAAS,EAAC,yBAAyB;QAAAI,QAAA,eACpCjB,OAAA;UACIgB,OAAO,EAAEA,CAAA,KAAMR,QAAQ,CAACX,cAAc,CAACS,QAAQ,CAACS,EAAE,CAAC,CAAE;UACrDF,SAAS,EAAC,0CAA0C;UACpDkB,KAAK,EAAC,QAAK;UAAAd,QAAA,eAEXjB,OAAA,CAACF,MAAM;YAACkC,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNtB,OAAA;MAAKa,SAAS,EAAC,qBAAqB;MAAAI,QAAA,gBAChCjB,OAAA;QAAKa,SAAS,EAAC,yCAAyC;QAAAI,QAAA,eACpDjB,OAAA,CAACN,aAAa;UAACmB,SAAS,EAAC,uBAAuB;UAACoB,IAAI,EAAE3B,QAAQ,CAAC4B;QAAQ;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,EACLhB,QAAQ,CAAC6B,QAAQ,iBACdnC,OAAA;QAAKa,SAAS,EAAC,iEAAiE;QAAAI,QAAA,eAC5EjB,OAAA;UACIoC,GAAG,EAAE9B,QAAQ,CAAC6B,QAAS;UACvBE,GAAG,EAAC,UAAU;UACdxB,SAAS,EAAC;QAA8B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNtB,OAAA,CAACJ,2BAA2B;MAACU,QAAQ,EAAEA;IAAS;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAElDhB,QAAQ,CAACgC,QAAQ,iBACdtC,OAAA;MAAKa,SAAS,EAAC,MAAM;MAAAI,QAAA,gBACjBjB,OAAA;QAAIa,SAAS,EAAC,mBAAmB;QAAAI,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/CtB,OAAA,CAACL,uBAAuB;QAACuC,OAAO,EAAE5B,QAAQ,CAACgC,QAAS;QAACzB,SAAS,EAAC,SAAS;QAAC0B,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CACR;EAAA,GAvDIhB,QAAQ,CAACS,EAAE;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAyDf,CAAC;AAEd,CAAC;AAAAnB,EAAA,CAlEKF,eAAe;EAAA,QACAT,WAAW,EACLD,WAAW,EAChBA,WAAW;AAAA;AAAAkD,EAAA,GAH3BxC,eAAe;AAoErB,eAAeA,eAAe;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}