{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\latex\\\\RenderLatex.jsx\";\nimport React from \"react\";\nimport \"katex/dist/katex.min.css\";\nimport { BlockMath, InlineMath } from \"react-katex\";\nimport NoTranslate from \"../utils/NoTranslate\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LatexRenderer = _ref => {\n  let {\n    text,\n    className = '',\n    style\n  } = _ref;\n  if (text === null || text === undefined) return null;\n\n  // Chuyển \\(...\\) => $...$, \\[...\\] => $$...$$\n  const formattedText = text.replace(/\\\\\\(/g, \"$\").replace(/\\\\\\)/g, \"$\").replace(/\\\\\\[/g, \"$$\").replace(/\\\\\\]/g, \"$$\");\n\n  // Regex phân chia nội dung theo LaTeX inline/block\n  const parts = formattedText.split(/(\\$\\$.*?\\$\\$|\\$.*?\\$)/gs); // 's' để nhận nhiều dòng\n\n  const elements = parts.map((part, index) => {\n    try {\n      if (part.startsWith(\"$$\") && part.endsWith(\"$$\")) {\n        return /*#__PURE__*/_jsxDEV(BlockMath, {\n          children: part.slice(2, -2)\n        }, \"block-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 24\n        }, this);\n      } else if (part.startsWith(\"$\") && part.endsWith(\"$\")) {\n        return /*#__PURE__*/_jsxDEV(InlineMath, {\n          children: part.slice(1, -1)\n        }, \"inline-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 24\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          children: part\n        }, \"text-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 24\n        }, this);\n      }\n    } catch (error) {\n      console.warn(\"KaTeX render error:\", error);\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        children: part\n      }, \"fallback-\".concat(index), false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 20\n      }, this); // fallback hiển thị text\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(NoTranslate, {\n    as: \"div\",\n    className: className,\n    style: style,\n    children: elements\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 9\n  }, this);\n};\n_c = LatexRenderer;\nexport default LatexRenderer;\nvar _c;\n$RefreshReg$(_c, \"LatexRenderer\");", "map": {"version": 3, "names": ["React", "BlockMath", "InlineMath", "NoTranslate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "text", "className", "style", "undefined", "formattedText", "replace", "parts", "split", "elements", "map", "part", "index", "startsWith", "endsWith", "children", "slice", "concat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "error", "console", "warn", "as", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/latex/RenderLatex.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport \"katex/dist/katex.min.css\";\r\nimport { BlockMath, InlineMath } from \"react-katex\";\r\nimport NoTranslate from \"../utils/NoTranslate\";\r\n\r\nconst LatexRenderer = ({ text, className = '', style }) => {\r\n    if (text === null || text === undefined) return null;\r\n\r\n    // Chuyển \\(...\\) => $...$, \\[...\\] => $$...$$\r\n    const formattedText = text\r\n        .replace(/\\\\\\(/g, \"$\")\r\n        .replace(/\\\\\\)/g, \"$\")\r\n        .replace(/\\\\\\[/g, \"$$\")\r\n        .replace(/\\\\\\]/g, \"$$\");\r\n\r\n    // Regex phân chia nội dung theo LaTeX inline/block\r\n    const parts = formattedText.split(/(\\$\\$.*?\\$\\$|\\$.*?\\$)/gs); // 's' để nhận nhiều dòng\r\n\r\n    const elements = parts.map((part, index) => {\r\n        try {\r\n            if (part.startsWith(\"$$\") && part.endsWith(\"$$\")) {\r\n                return <BlockMath key={`block-${index}`}>{part.slice(2, -2)}</BlockMath>;\r\n            } else if (part.startsWith(\"$\") && part.endsWith(\"$\")) {\r\n                return <InlineMath key={`inline-${index}`}>{part.slice(1, -1)}</InlineMath>;\r\n            } else {\r\n                return <span key={`text-${index}`}>{part}</span>;\r\n            }\r\n        } catch (error) {\r\n            console.warn(\"KaTeX render error:\", error);\r\n            return <span key={`fallback-${index}`}>{part}</span>; // fallback hiển thị text\r\n        }\r\n    });\r\n\r\n\r\n    return (\r\n        <NoTranslate as=\"div\" className={className} style={style}>\r\n            {elements}\r\n        </NoTranslate>\r\n    );\r\n};\r\n\r\nexport default LatexRenderer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,SAASC,SAAS,EAAEC,UAAU,QAAQ,aAAa;AACnD,OAAOC,WAAW,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,aAAa,GAAGC,IAAA,IAAqC;EAAA,IAApC;IAAEC,IAAI;IAAEC,SAAS,GAAG,EAAE;IAAEC;EAAM,CAAC,GAAAH,IAAA;EAClD,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKG,SAAS,EAAE,OAAO,IAAI;;EAEpD;EACA,MAAMC,aAAa,GAAGJ,IAAI,CACrBK,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;;EAE3B;EACA,MAAMC,KAAK,GAAGF,aAAa,CAACG,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAC;;EAE9D,MAAMC,QAAQ,GAAGF,KAAK,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IACxC,IAAI;MACA,IAAID,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9C,oBAAOhB,OAAA,CAACJ,SAAS;UAAAqB,QAAA,EAAyBJ,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAAC,YAAAC,MAAA,CAA3BL,KAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkC,CAAC;MAC5E,CAAC,MAAM,IAAIV,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;QACnD,oBAAOhB,OAAA,CAACH,UAAU;UAAAoB,QAAA,EAA0BJ,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAAC,aAAAC,MAAA,CAA3BL,KAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmC,CAAC;MAC/E,CAAC,MAAM;QACH,oBAAOvB,OAAA;UAAAiB,QAAA,EAA6BJ;QAAI,WAAAM,MAAA,CAAdL,KAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MACpD;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACC,IAAI,CAAC,qBAAqB,EAAEF,KAAK,CAAC;MAC1C,oBAAOxB,OAAA;QAAAiB,QAAA,EAAiCJ;MAAI,eAAAM,MAAA,CAAdL,KAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC,CAAC,CAAC;IAC1D;EACJ,CAAC,CAAC;EAGF,oBACIvB,OAAA,CAACF,WAAW;IAAC6B,EAAE,EAAC,KAAK;IAACvB,SAAS,EAAEA,SAAU;IAACC,KAAK,EAAEA,KAAM;IAAAY,QAAA,EACpDN;EAAQ;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEtB,CAAC;AAACK,EAAA,GAlCI3B,aAAa;AAoCnB,eAAeA,aAAa;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}