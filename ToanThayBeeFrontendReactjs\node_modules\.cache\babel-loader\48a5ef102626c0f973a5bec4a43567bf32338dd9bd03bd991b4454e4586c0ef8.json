{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\CompactStepHeader.jsx\",\n  _s = $RefreshSig$();\n// Compact Step Header Component\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setStep } from \"src/features/addExam/addExamSlice\";\nimport { Edit, FileText, CheckCircle, ChevronRight } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CompactStepHeader = () => {\n  _s();\n  const {\n    step\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  const steps = [{\n    id: 1,\n    label: \"Thông tin\",\n    icon: Edit\n  }, {\n    id: 2,\n    label: \"Câu hỏi\",\n    icon: FileText\n  }, {\n    id: 3,\n    label: \"<PERSON>à<PERSON> tất\",\n    icon: CheckCircle\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2\",\n    children: steps.map((stepItem, index) => {\n      const Icon = stepItem.icon;\n      const isActive = step === stepItem.id;\n      const isCompleted = step > stepItem.id;\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => dispatch(setStep(stepItem.id)),\n          className: \"flex items-center gap-1 px-2 py-1 rounded text-xs transition-all \".concat(isActive ? 'bg-blue-100 text-blue-700 font-medium' : isCompleted ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'),\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: stepItem.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 29\n          }, this), isCompleted && /*#__PURE__*/_jsxDEV(CheckCircle, {\n            className: \"w-3 h-3 text-green-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 45\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 25\n        }, this), index < steps.length - 1 && /*#__PURE__*/_jsxDEV(ChevronRight, {\n          className: \"w-3 h-3 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 29\n        }, this)]\n      }, stepItem.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 21\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_s(CompactStepHeader, \"Do6+eAfHfz9TFWVN6PM5K31DPYU=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = CompactStepHeader;\nexport default CompactStepHeader;\nvar _c;\n$RefreshReg$(_c, \"CompactStepHeader\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "setStep", "Edit", "FileText", "CheckCircle", "ChevronRight", "jsxDEV", "_jsxDEV", "CompactStepHeader", "_s", "step", "state", "addExam", "dispatch", "steps", "id", "label", "icon", "className", "children", "map", "stepItem", "index", "Icon", "isActive", "isCompleted", "React", "Fragment", "onClick", "concat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/CompactStepHeader.jsx"], "sourcesContent": ["// Compact Step Header Component\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setStep } from \"src/features/addExam/addExamSlice\";\r\nimport { Edit, FileText, CheckCircle, ChevronRight } from \"lucide-react\";\r\n\r\n\r\nconst CompactStepHeader = () => {\r\n    const { step } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n\r\n    const steps = [\r\n        { id: 1, label: \"Thông tin\", icon: Edit },\r\n        { id: 2, label: \"Câu hỏi\", icon: FileText },\r\n        { id: 3, label: \"Hoàn tất\", icon: CheckCircle }\r\n    ];\r\n\r\n    return (\r\n        <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2\">\r\n            {steps.map((stepItem, index) => {\r\n                const Icon = stepItem.icon;\r\n                const isActive = step === stepItem.id;\r\n                const isCompleted = step > stepItem.id;\r\n\r\n                return (\r\n                    <React.Fragment key={stepItem.id}>\r\n                        <button\r\n                            onClick={() => dispatch(setStep(stepItem.id))}\r\n                            className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-all ${isActive\r\n                                ? 'bg-blue-100 text-blue-700 font-medium'\r\n                                : isCompleted\r\n                                    ? 'bg-green-100 text-green-700'\r\n                                    : 'text-gray-500 hover:bg-gray-100'\r\n                                }`}\r\n                        >\r\n                            <Icon className=\"w-3 h-3\" />\r\n                            <span>{stepItem.label}</span>\r\n                            {isCompleted && <CheckCircle className=\"w-3 h-3 text-green-600\" />}\r\n                        </button>\r\n                        {index < steps.length - 1 && (\r\n                            <ChevronRight className=\"w-3 h-3 text-gray-400\" />\r\n                        )}\r\n                    </React.Fragment>\r\n                );\r\n            })}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CompactStepHeader;"], "mappings": ";;AAAA;AACA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,IAAI,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAK,CAAC,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EACtD,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAMc,KAAK,GAAG,CACV;IAAEC,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAEf;EAAK,CAAC,EACzC;IAAEa,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEd;EAAS,CAAC,EAC3C;IAAEY,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAEb;EAAY,CAAC,CAClD;EAED,oBACIG,OAAA;IAAKW,SAAS,EAAC,+EAA+E;IAAAC,QAAA,EACzFL,KAAK,CAACM,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MAC5B,MAAMC,IAAI,GAAGF,QAAQ,CAACJ,IAAI;MAC1B,MAAMO,QAAQ,GAAGd,IAAI,KAAKW,QAAQ,CAACN,EAAE;MACrC,MAAMU,WAAW,GAAGf,IAAI,GAAGW,QAAQ,CAACN,EAAE;MAEtC,oBACIR,OAAA,CAACmB,KAAK,CAACC,QAAQ;QAAAR,QAAA,gBACXZ,OAAA;UACIqB,OAAO,EAAEA,CAAA,KAAMf,QAAQ,CAACZ,OAAO,CAACoB,QAAQ,CAACN,EAAE,CAAC,CAAE;UAC9CG,SAAS,sEAAAW,MAAA,CAAsEL,QAAQ,GACjF,uCAAuC,GACvCC,WAAW,GACP,6BAA6B,GAC7B,iCAAiC,CACpC;UAAAN,QAAA,gBAEPZ,OAAA,CAACgB,IAAI;YAACL,SAAS,EAAC;UAAS;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B1B,OAAA;YAAAY,QAAA,EAAOE,QAAQ,CAACL;UAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC5BR,WAAW,iBAAIlB,OAAA,CAACH,WAAW;YAACc,SAAS,EAAC;UAAwB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,EACRX,KAAK,GAAGR,KAAK,CAACoB,MAAM,GAAG,CAAC,iBACrB3B,OAAA,CAACF,YAAY;UAACa,SAAS,EAAC;QAAuB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACpD;MAAA,GAhBgBZ,QAAQ,CAACN,EAAE;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBhB,CAAC;IAEzB,CAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACxB,EAAA,CAxCID,iBAAiB;EAAA,QACFT,WAAW,EACXC,WAAW;AAAA;AAAAmC,EAAA,GAF1B3B,iBAAiB;AA0CvB,eAAeA,iBAAiB;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}