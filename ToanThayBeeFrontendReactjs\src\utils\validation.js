import { setErrorMessage } from "../features/state/stateApiSlice";


export const validateRegister = (data, dispatch) => {
    console.log(data);
    if (!data.lastName) {
        dispatch(setErrorMessage("Họ và tên đệm không được để trống."))
        return false;
    };
    if (!data.firstName) {
        dispatch(setErrorMessage("Tên không được để trống."));
        return false;
    }
    // Gender validation
    if (data.gender === undefined || data.gender === null || data.gender === -1) {
        dispatch(setErrorMessage("Vui lòng chọn giới tính."));
        return false;
    }
    // Grade validation
    if (!data.class) {
        dispatch(setErrorMessage("Vui lòng chọn khối lớp."));
        return false;
    }
    // School validation
    if (!data.highSchool) {
        dispatch(setErrorMessage("<PERSON>ui lòng nhập trường học."));
        return false;
    }
    // // Class validation - check if classIds array exists and has at least one item
    // if (!data.classIds || !Array.isArray(data.classIds) || data.classIds.length === 0) {
    //     dispatch(setErrorMessage("Vui lòng chọn ít nhất một lớp học."));
    //     return false;
    // }
    // Username and password validation
    if (!data.username) {
        dispatch(setErrorMessage("Tên đăng nhập không được để trống."));
        return false;
    }
    if (!data.password) {
        dispatch(setErrorMessage("Mật khẩu không được để trống."));
        return false;
    }
    // Parent phone validation
    if (!data.phone) {
        dispatch(setErrorMessage("Số điện thoại phụ huynh không được để trống."));
        return false;
    }
    if (data.phone && !/^(0|\+84)\d{9}$/.test(data.phone)) {
        dispatch(setErrorMessage("Số điện thoại phụ huynh không hợp lệ"));
        return false;
    }
    return true;
};

export const validationUser = (user, dispatch) => {
    if (!user.lastName) {
        dispatch(setErrorMessage("Họ và tên đệm không được để trống."));
        return false;
    }
    if (!user.firstName) {
        dispatch(setErrorMessage("Tên không được để trống."));
        return false;
    }

    if (!user.phone) {
        dispatch(setErrorMessage("Số điện thoại không được để trống."));
        return false;
    }
    return true;
}

