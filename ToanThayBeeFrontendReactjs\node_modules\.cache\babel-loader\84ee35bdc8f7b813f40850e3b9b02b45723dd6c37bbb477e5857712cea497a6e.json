{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport TextArea from \"src/components/input/TextArea\";\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { CheckCircle } from \"lucide-react\";\nimport { setQuestions, setNewQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DetailQuestionView = () => {\n  _s();\n  const {\n    questionsExam,\n    selectedId,\n    view\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const dispatch = useDispatch();\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [question, setQuestion] = useState(null);\n  useEffect(() => {\n    // console.log(selectedId);\n\n    if (selectedId && (questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.length) > 0) {\n      const selectedQuestion = questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.find(q => q.id === selectedId);\n      setQuestion(selectedQuestion);\n    }\n  }, [selectedId, questionsExam]);\n  const handleQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...question,\n      [field]: e.target.value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleStatementChange = (index, value, field) => {\n    const updatedStatements = [...question.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...question,\n      statements: updatedStatements\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleSolutionQuestionChange = value => {\n    const updatedQuestion = {\n      ...question,\n      solution: value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const [optionChapter, setOptionChapter] = useState([]);\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (question !== null && question !== void 0 && question.class && (question === null || question === void 0 ? void 0 : question.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(question === null || question === void 0 ? void 0 : question.class) && code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, question === null || question === void 0 ? void 0 : question.class]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: question && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Ph\\xE2n lo\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.class,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'class'),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: question.chapter,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'chapter'),\n            options: optionChapter,\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.difficulty,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'difficulty'),\n            options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextArea, {\n            value: question.content,\n            onChange: e => handleQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 29\n          }, this), (view === 'image' || question.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: question.imageUrl,\n            onImageDrop: image => handleQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 33\n          }, this), question.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n                  value: statement.content,\n                  onChange: e => handleStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 45\n              }, this), (view === 'image' || statement.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 49\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 41\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 33\n          }, this), question.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: question.correctAnswer,\n              onChange: e => handleQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n            solution: question.solution,\n            onSolutionChange: handleSolutionQuestionChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 9\n  }, this);\n};\n_s(DetailQuestionView, \"AtX1aa+NXfezjHwEa8coxZtMvMs=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = DetailQuestionView;\nconst AddQuestionView = () => {\n  _s2();\n  const dispatch = useDispatch();\n  const {\n    newQuestion\n  } = useSelector(state => state.questionsExam);\n  const handleNewQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...newQuestion,\n      [field]: e.target.value\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  const handleNewStatementChange = (index, value, field) => {\n    const updatedStatements = [...newQuestion.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...newQuestion,\n      statements: updatedStatements\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-900 mb-1\",\n        children: \"Ph\\xE2n lo\\u1EA1i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: newQuestion.class,\n        onChange: option => handleNewQuestionChange({\n          target: {\n            value: option\n          }\n        }, 'class'),\n        options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n        className: \"text-xs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n        selectedOption: newQuestion.chapter,\n        onChange: option => handleNewQuestionChange({\n          target: {\n            value: option\n          }\n        }, 'chapter'),\n        options: optionChapter,\n        className: \"text-xs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: newQuestion.difficulty,\n        onChange: option => handleNewQuestionChange({\n          target: {\n            value: option\n          }\n        }, 'difficulty'),\n        options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n        className: \"text-xs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 9\n  }, this);\n};\n_s2(AddQuestionView, \"27UGbxbm3AL1dy+pPbAmxtPMMAQ=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c2 = AddQuestionView;\nconst LeftContent = () => {\n  _s3();\n  const [view, setView] = useState('questionDetail');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Chi ti\\u1EBFt c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: view === 'questionDetail' ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setView('addQuestion'),\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white\",\n          children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setView('questionDetail'),\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white\",\n            children: \"L\\u01B0u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setView('questionDetail'),\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white\",\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 13\n    }, this), view === 'questionDetail' && /*#__PURE__*/_jsxDEV(DetailQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 43\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 9\n  }, this);\n};\n_s3(LeftContent, \"wSdNEyYiwjPO5HRovtTjK+yTCo8=\");\n_c3 = LeftContent;\nexport default LeftContent;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"DetailQuestionView\");\n$RefreshReg$(_c2, \"AddQuestionView\");\n$RefreshReg$(_c3, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useState", "useSelector", "useDispatch", "DropMenuBarAdmin", "SuggestInputBarAdmin", "TextArea", "ImageDropZone", "SolutionEditor", "setQuestion", "CheckCircle", "setQuestions", "setNewQuestion", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DetailQuestionView", "_s", "questionsExam", "selectedId", "view", "state", "codes", "dispatch", "prefixTN", "prefixDS", "question", "length", "selectedQuestion", "find", "q", "id", "handleQuestionChange", "e", "field", "updatedQuestion", "target", "value", "handleStatementChange", "index", "updatedStatements", "statements", "handleSolutionQuestionChange", "solution", "optionChapter", "setOptionChapter", "Array", "isArray", "class", "trim", "filter", "code", "startsWith", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selectedOption", "onChange", "option", "options", "chapter", "difficulty", "content", "placeholder", "label", "imageUrl", "onImageDrop", "image", "onImageRemove", "typeOfQuestion", "map", "statement", "<PERSON><PERSON><PERSON><PERSON>", "Icon", "onSolutionChange", "_c", "AddQuestionView", "_s2", "newQuestion", "handleNewQuestionChange", "handleNewStatementChange", "_c2", "LeftContent", "_s3", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/LeftContent.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport TextArea from \"src/components/input/TextArea\";\r\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\r\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\r\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { CheckCircle } from \"lucide-react\";\r\nimport { setQuestions, setNewQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\n\r\n\r\nconst DetailQuestionView = () => {\r\n    const { questionsExam, selectedId, view } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const dispatch = useDispatch();\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n\r\n    const [question, setQuestion] = useState(null);\r\n\r\n    useEffect(() => {\r\n        // console.log(selectedId);\r\n\r\n        if (selectedId && questionsExam?.length > 0) {\r\n            const selectedQuestion = questionsExam?.find(q => q.id === selectedId);\r\n            setQuestion(selectedQuestion);\r\n        }\r\n    }, [selectedId, questionsExam]);\r\n\r\n    const handleQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...question, [field]: e.target.value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...question.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...question, statements: updatedStatements };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleSolutionQuestionChange = (value) => {\r\n        const updatedQuestion = { ...question, solution: value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (question?.class && question?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(question?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, question?.class]);\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            {question && (\r\n                <div className=\"space-y-3 w-full\">\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                        <div className=\"flex flex-row gap-2\">\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.class}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <SuggestInputBarAdmin\r\n                                selectedOption={question.chapter}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}\r\n                                options={optionChapter}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.difficulty}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                                options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\" bg-gray-200\"></hr>\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                        <div className=\"space-y-2\">\r\n                            <TextArea\r\n                                value={question.content}\r\n                                onChange={(e) => handleQuestionChange(e, 'content')}\r\n                                placeholder=\"Nhập nội dung câu hỏi\"\r\n                                label=\"Câu hỏi\"\r\n                            />\r\n                            {(view === 'image' || question.imageUrl) && (\r\n                                <ImageDropZone\r\n                                    imageUrl={question.imageUrl}\r\n                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                                />\r\n                            )}\r\n                            {question.typeOfQuestion !== 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    {question.statements.map((statement, index) => (\r\n                                        <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                            <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                                <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                    {question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                                </p>\r\n                                                <TextArea\r\n                                                    value={statement.content}\r\n                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}\r\n                                                    placeholder=\"Nhập nội dung mệnh đề\"\r\n                                                />\r\n                                            </div>\r\n                                            {(view === 'image' || statement.imageUrl) && (\r\n                                                <ImageDropZone\r\n                                                    imageUrl={statement.imageUrl}\r\n                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}\r\n                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}\r\n                                                />\r\n                                            )}\r\n\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            )}\r\n                            {question.typeOfQuestion === 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    <TextArea\r\n                                        value={question.correctAnswer}\r\n                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}\r\n                                        placeholder=\"Nhập đáp án\"\r\n                                        label=\"Đáp án\"\r\n                                        Icon={CheckCircle}\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            {/* <TextArea\r\n                                        value={question.solution}\r\n                                        onChange={(e) => handleQuestionChange(e, 'solution')}\r\n                                        placeholder=\"Nhập lời giải\"\r\n                                        label=\"Lời giải\"\r\n                                        Icon={CheckCircle}\r\n                                    /> */}\r\n                            <SolutionEditor\r\n                                solution={question.solution}\r\n                                onSolutionChange={handleSolutionQuestionChange}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst AddQuestionView = () => {\r\n    const dispatch = useDispatch();\r\n    const { newQuestion } = useSelector((state) => state.questionsExam);\r\n\r\n    const handleNewQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...newQuestion, [field]: e.target.value };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    const handleNewStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...newQuestion.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...newQuestion, statements: updatedStatements };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-3\">\r\n            <div className=\"flex flex-col gap-2\">\r\n                <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                <DropMenuBarAdmin\r\n                    selectedOption={newQuestion.class}\r\n                    onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'class')}\r\n                    options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                    className=\"text-xs\"\r\n                />\r\n                <SuggestInputBarAdmin\r\n                    selectedOption={newQuestion.chapter}\r\n                    onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'chapter')}\r\n                    options={optionChapter}\r\n                    className=\"text-xs\"\r\n                />\r\n                <DropMenuBarAdmin\r\n                    selectedOption={newQuestion.difficulty}\r\n                    onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                    options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                    className=\"text-xs\"\r\n                />\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst LeftContent = () => {\r\n    const [view, setView] = useState('questionDetail');\r\n\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Chi tiết câu hỏi</h2>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                    {\r\n                        view === 'questionDetail' ? (\r\n                            <button\r\n                                onClick={() => setView('addQuestion')}\r\n                                className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white`}\r\n                            >\r\n                                Thêm câu hỏi\r\n                            </button>\r\n                        ) : (\r\n                            <>\r\n                                <button\r\n                                    onClick={() => setView('questionDetail')}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white`}\r\n                                >\r\n                                    Lưu\r\n                                </button>\r\n                                <button\r\n                                    onClick={() => setView('questionDetail')}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white`}\r\n                                >\r\n                                    Hủy\r\n                                </button>\r\n\r\n                            </>\r\n                        )\r\n                    }\r\n\r\n                </div>\r\n            </div>\r\n            {view === 'questionDetail' && <DetailQuestionView />}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nexport default LeftContent;"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SAASC,WAAW,QAAQ,+CAA+C;AAC3E,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,YAAY,EAAEC,cAAc,QAAQ,+CAA+C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG7F,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC,aAAa;IAAEC,UAAU;IAAEC;EAAK,CAAC,GAAGnB,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACvF,MAAM;IAAEI;EAAM,CAAC,GAAGrB,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAE7E,MAAM,CAACC,QAAQ,EAAElB,WAAW,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACZ;;IAEA,IAAIoB,UAAU,IAAI,CAAAD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAES,MAAM,IAAG,CAAC,EAAE;MACzC,MAAMC,gBAAgB,GAAGV,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKZ,UAAU,CAAC;MACtEX,WAAW,CAACoB,gBAAgB,CAAC;IACjC;EACJ,CAAC,EAAE,CAACT,UAAU,EAAED,aAAa,CAAC,CAAC;EAE/B,MAAMc,oBAAoB,GAAGA,CAACC,CAAC,EAAEC,KAAK,KAAK;IACvC,MAAMC,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAE,CAACQ,KAAK,GAAGD,CAAC,CAACG,MAAM,CAACC;IAAM,CAAC;IAChEd,QAAQ,CAACb,YAAY,CAACyB,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAACC,KAAK,EAAEF,KAAK,EAAEH,KAAK,KAAK;IACnD,MAAMM,iBAAiB,GAAG,CAAC,GAAGd,QAAQ,CAACe,UAAU,CAAC;IAClDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACL,KAAK,GAAGG;IAAM,CAAC;IAC1E,MAAMF,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEe,UAAU,EAAED;IAAkB,CAAC;IACtEjB,QAAQ,CAACb,YAAY,CAACyB,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMO,4BAA4B,GAAIL,KAAK,IAAK;IAC5C,MAAMF,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEiB,QAAQ,EAAEN;IAAM,CAAC;IACxDd,QAAQ,CAACb,YAAY,CAACyB,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAEtDD,SAAS,CAAC,MAAM;IACZ,IAAI+C,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAII,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEsB,KAAK,IAAI,CAAAtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,KAAK,CAACC,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QAClDJ,gBAAgB,CACZvB,KAAK,CAAC,SAAS,CAAC,CAAC4B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC1B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,KAAK,CAAC,IAAIG,IAAI,CAACA,IAAI,CAACxB,MAAM,KAAK,CAAC,CACrG,CAAC;MACL,CAAC,MAAM;QACHkB,gBAAgB,CAACvB,KAAK,CAAC,SAAS,CAAC,CAAC4B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACxB,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHkB,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACvB,KAAK,EAAEI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,KAAK,CAAC,CAAC;EAE5B,oBACInC,OAAA;IAAKwC,SAAS,EAAC,sBAAsB;IAAAC,QAAA,EAChC5B,QAAQ,iBACLb,OAAA;MAAKwC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BzC,OAAA;QAAKwC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCzC,OAAA;UAAIwC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE7C,OAAA;UAAKwC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChCzC,OAAA,CAACV,gBAAgB;YACbwD,cAAc,EAAEjC,QAAQ,CAACsB,KAAM;YAC/BY,QAAQ,EAAGC,MAAM,IAAK7B,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEwB;cAAO;YAAE,CAAC,EAAE,OAAO,CAAE;YACnFC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;YAC7D+B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF7C,OAAA,CAACT,oBAAoB;YACjBuD,cAAc,EAAEjC,QAAQ,CAACqC,OAAQ;YACjCH,QAAQ,EAAGC,MAAM,IAAK7B,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEwB;cAAO;YAAE,CAAC,EAAE,SAAS,CAAE;YACrFC,OAAO,EAAElB,aAAc;YACvBS,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF7C,OAAA,CAACV,gBAAgB;YACbwD,cAAc,EAAEjC,QAAQ,CAACsC,UAAW;YACpCJ,QAAQ,EAAGC,MAAM,IAAK7B,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEwB;cAAO;YAAE,CAAC,EAAE,YAAY,CAAE;YACxFC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;YACvE+B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN7C,OAAA;QAAIwC,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClC7C,OAAA;QAAKwC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCzC,OAAA;UAAIwC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E7C,OAAA;UAAKwC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBzC,OAAA,CAACR,QAAQ;YACLgC,KAAK,EAAEX,QAAQ,CAACuC,OAAQ;YACxBL,QAAQ,EAAG3B,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,SAAS,CAAE;YACpDiC,WAAW,EAAC,yCAAuB;YACnCC,KAAK,EAAC;UAAS;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EACD,CAACtC,IAAI,KAAK,OAAO,IAAIM,QAAQ,CAAC0C,QAAQ,kBACnCvD,OAAA,CAACP,aAAa;YACV8D,QAAQ,EAAE1C,QAAQ,CAAC0C,QAAS;YAC5BC,WAAW,EAAGC,KAAK,IAAKtC,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEiC;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YACvFC,aAAa,EAAEA,CAAA,KAAMvC,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CACJ,EACAhC,QAAQ,CAAC8C,cAAc,KAAK,KAAK,iBAC9B3D,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrB5B,QAAQ,CAACe,UAAU,CAACgC,GAAG,CAAC,CAACC,SAAS,EAAEnC,KAAK,kBACtC1B,OAAA;cAAiBwC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChEzC,OAAA;gBAAKwC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpDzC,OAAA;kBAAGwC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7C5B,QAAQ,CAAC8C,cAAc,KAAK,IAAI,GAAGhD,QAAQ,CAACe,KAAK,CAAC,GAAGd,QAAQ,CAACc,KAAK;gBAAC;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACJ7C,OAAA,CAACR,QAAQ;kBACLgC,KAAK,EAAEqC,SAAS,CAACT,OAAQ;kBACzBL,QAAQ,EAAG3B,CAAC,IAAKK,qBAAqB,CAACC,KAAK,EAAEN,CAAC,CAACG,MAAM,CAACC,KAAK,EAAE,SAAS,CAAE;kBACzE6B,WAAW,EAAC;gBAAuB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACL,CAACtC,IAAI,KAAK,OAAO,IAAIsD,SAAS,CAACN,QAAQ,kBACpCvD,OAAA,CAACP,aAAa;gBACV8D,QAAQ,EAAEM,SAAS,CAACN,QAAS;gBAC7BC,WAAW,EAAGC,KAAK,IAAKhC,qBAAqB,CAACC,KAAK,EAAE+B,KAAK,EAAE,UAAU,CAAE;gBACxEC,aAAa,EAAEA,CAAA,KAAMjC,qBAAqB,CAACC,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACJ;YAAA,GAjBKnB,KAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAhC,QAAQ,CAAC8C,cAAc,KAAK,KAAK,iBAC9B3D,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtBzC,OAAA,CAACR,QAAQ;cACLgC,KAAK,EAAEX,QAAQ,CAACiD,aAAc;cAC9Bf,QAAQ,EAAG3B,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,eAAe,CAAE;cAC1DiC,WAAW,EAAC,6BAAa;cACzBC,KAAK,EAAC,mBAAQ;cACdS,IAAI,EAAEnE;YAAY;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAQD7C,OAAA,CAACN,cAAc;YACXoC,QAAQ,EAAEjB,QAAQ,CAACiB,QAAS;YAC5BkC,gBAAgB,EAAEnC;UAA6B;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAzC,EAAA,CAtJKD,kBAAkB;EAAA,QACwBf,WAAW,EACrCA,WAAW,EACZC,WAAW;AAAA;AAAA4E,EAAA,GAH1B9D,kBAAkB;AAwJxB,MAAM+D,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAMzD,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+E;EAAY,CAAC,GAAGhF,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EAEnE,MAAMgE,uBAAuB,GAAGA,CAACjD,CAAC,EAAEC,KAAK,KAAK;IAC1C,MAAMC,eAAe,GAAG;MAAE,GAAG8C,WAAW;MAAE,CAAC/C,KAAK,GAAGD,CAAC,CAACG,MAAM,CAACC;IAAM,CAAC;IACnEd,QAAQ,CAACZ,cAAc,CAACwB,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMgD,wBAAwB,GAAGA,CAAC5C,KAAK,EAAEF,KAAK,EAAEH,KAAK,KAAK;IACtD,MAAMM,iBAAiB,GAAG,CAAC,GAAGyC,WAAW,CAACxC,UAAU,CAAC;IACrDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACL,KAAK,GAAGG;IAAM,CAAC;IAC1E,MAAMF,eAAe,GAAG;MAAE,GAAG8C,WAAW;MAAExC,UAAU,EAAED;IAAkB,CAAC;IACzEjB,QAAQ,CAACZ,cAAc,CAACwB,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,oBACItB,OAAA;IAAKwC,SAAS,EAAC,WAAW;IAAAC,QAAA,eACtBzC,OAAA;MAAKwC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChCzC,OAAA;QAAIwC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrE7C,OAAA,CAACV,gBAAgB;QACbwD,cAAc,EAAEsB,WAAW,CAACjC,KAAM;QAClCY,QAAQ,EAAGC,MAAM,IAAKqB,uBAAuB,CAAC;UAAE9C,MAAM,EAAE;YAAEC,KAAK,EAAEwB;UAAO;QAAE,CAAC,EAAE,OAAO,CAAE;QACtFC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;QAC7D+B,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACF7C,OAAA,CAACT,oBAAoB;QACjBuD,cAAc,EAAEsB,WAAW,CAAClB,OAAQ;QACpCH,QAAQ,EAAGC,MAAM,IAAKqB,uBAAuB,CAAC;UAAE9C,MAAM,EAAE;YAAEC,KAAK,EAAEwB;UAAO;QAAE,CAAC,EAAE,SAAS,CAAE;QACxFC,OAAO,EAAElB,aAAc;QACvBS,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACF7C,OAAA,CAACV,gBAAgB;QACbwD,cAAc,EAAEsB,WAAW,CAACjB,UAAW;QACvCJ,QAAQ,EAAGC,MAAM,IAAKqB,uBAAuB,CAAC;UAAE9C,MAAM,EAAE;YAAEC,KAAK,EAAEwB;UAAO;QAAE,CAAC,EAAE,YAAY,CAAE;QAC3FC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;QACvE+B,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAsB,GAAA,CAzCKD,eAAe;EAAA,QACA7E,WAAW,EACJD,WAAW;AAAA;AAAAmF,GAAA,GAFjCL,eAAe;AA4CrB,MAAMM,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM,CAAClE,IAAI,EAAEmE,OAAO,CAAC,GAAGvF,QAAQ,CAAC,gBAAgB,CAAC;EAGlD,oBACIa,OAAA;IAAKwC,SAAS,EAAC,uDAAuD;IAAAC,QAAA,gBAElEzC,OAAA;MAAKwC,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBAC/FzC,OAAA;QAAKwC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCzC,OAAA;UAAIwC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAEhClC,IAAI,KAAK,gBAAgB,gBACrBP,OAAA;UACI2E,OAAO,EAAEA,CAAA,KAAMD,OAAO,CAAC,aAAa,CAAE;UACtClC,SAAS,+FAAgG;UAAAC,QAAA,EAC5G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAET7C,OAAA,CAAAE,SAAA;UAAAuC,QAAA,gBACIzC,OAAA;YACI2E,OAAO,EAAEA,CAAA,KAAMD,OAAO,CAAC,gBAAgB,CAAE;YACzClC,SAAS,uGAAwG;YAAAC,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7C,OAAA;YACI2E,OAAO,EAAEA,CAAA,KAAMD,OAAO,CAAC,gBAAgB,CAAE;YACzClC,SAAS,+FAAgG;YAAAC,QAAA,EAC5G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eAEX;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACLtC,IAAI,KAAK,gBAAgB,iBAAIP,OAAA,CAACG,kBAAkB;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnD,CAAC;AAEd,CAAC;AAAA4B,GAAA,CA5CKD,WAAW;AAAAI,GAAA,GAAXJ,WAAW;AA+CjB,eAAeA,WAAW;AAAC,IAAAP,EAAA,EAAAM,GAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}