import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as examApi from "../../services/examApi";
// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";

export const postExam = createAsyncThunk(
    "addExam/postExam",
    async ({ examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, { dispatch }) => {
        return await apiHandler(dispatch, examApi.postExamAPI, { examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, () => { }, true, false);
    }
);

const addExamSlice = createSlice({
    name: "addExam",
    initialState: {
        loading: false,
        step: 1,
        selectedIndex: 0,
        questionTNContent: "",
        questionDSContent: "",
        questionTLNContent: "",
        folder: "questionImage",
        correctAnswerTN: "",
        correctAnswerDS: "",
        correctAnswerTLN: "",
        showAddImagesModal: false,
        examData:
        {
            name: "",
            typeOfExam: null,
            class: null,
            chapter: null,
            year: null,
            description: "",
            testDuration: null,
            passRate: null,
            solutionUrl: "",
            imageUrl: "",
            public: false,
            isClassroomExam: false,
        },
        examImage: null,
        questions: [],
        questionImages: [],
        statementImages: [],
        solutionImages: [],
        examFile: null,
    },
    reducers: {
        nextStep: (state) => {
            state.step++;
        },
        prevStep: (state) => {
            state.step--;
        },
        setSelectedIndex: (state, action) => {
            state.selectedIndex = action.payload;
        },
        setLoading: (state, action) => {
            state.loading = action.payload;
        },
        setQuestionTNContent: (state, action) => {
            state.questionTNContent = action.payload;
        },
        setQuestionDSContent: (state, action) => {
            state.questionDSContent = action.payload;
        },
        setQuestionTLNContent: (state, action) => {
            state.questionTLNContent = action.payload;
        },
        setCorrectAnswerTN: (state, action) => {
            state.correctAnswerTN = action.payload;
        },
        setCorrectAnswerDS: (state, action) => {
            state.correctAnswerDS = action.payload;
        },
        setCorrectAnswerTLN: (state, action) => {
            state.correctAnswerTLN = action.payload;
        },

        setExamData: (state, action) => {
            const { field, value } = action.payload;
            // console.log('Setting exam data in slice:', field, value);
            state.examData[field] = value;
        },
        setExamImage: (state, action) => {
            state.examImage = action.payload;
        },
        setExamFile: (state, action) => {
            state.examFile = action.payload;
        },
        setStep: (state, action) => {
            state.step = action.payload;
        },
        resetData: (state) => {
            state.examData = {
                name: "",
                typeOfExam: null,
                class: null,
                chapter: null,
                year: null,
                description: "",
                testDuration: null,
                passRate: null,
                solutionUrl: "",
                imageUrl: "",
                public: false,
                isClassroomExam: false,
            };
            state.examImage = null;
            state.questions = [];
            state.questionImages = [];
            state.statementImages = [];
            state.solutionImages = [];
            state.examFile = null;
        },
        setQuestions: (state, action) => {
            state.questions = action.payload;
        },
        setShowAddImagesModal: (state, action) => {
            state.showAddImagesModal = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(postExam.pending, (state) => {
                state.loading = true;
            })
            .addCase(postExam.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(postExam.rejected, (state) => {
                state.loading = false;
            })
    }
});

export const {
    setLoading,
    resetData,
    nextStep,
    prevStep,
    setExamData,
    setStep,
    setExamImage,
    setExamFile,
    setQuestionTNContent,
    setQuestionDSContent,
    setQuestionTLNContent,
    setCorrectAnswerTN,
    setCorrectAnswerDS,
    setCorrectAnswerTLN,
    setQuestions,
    setSelectedIndex,
    setShowAddImagesModal,
} = addExamSlice.actions;
export default addExamSlice.reducer;