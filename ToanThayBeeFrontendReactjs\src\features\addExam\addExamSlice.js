import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as examApi from "../../services/examApi";
// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";

export const postExam = createAsyncThunk(
    "addExam/postExam",
    async ({ examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, { dispatch }) => {
        return await apiHandler(dispatch, examApi.postExamAPI, { examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, () => { }, true, false);
    }
);

const addExamSlice = createSlice({
    name: "addExam",
    initialState: {
        loading: false,
        step: 1,
        examData:
        {
            name: "",
            typeOfExam: null,
            class: null,
            chapter: null,
            year: null,
            description: "",
            testDuration: null,
            passRate: null,
            solutionUrl: "",
            imageUrl: "",
            public: false,
            isClassroomExam: false,
        },
        examImage: null,
        questions: [],
        questionImages: [],
        statementImages: [],
        solutionImages: [],
        examFile: null,
    },
    reducers: {
        nextStep: (state) => {
            state.step++;
        },
        prevStep: (state) => {
            state.step--;
        },
        setLoading: (state, action) => {
            state.loading = action.payload;
        },
        setExamData: (state, action) => {
            const { field, value } = action.payload;
            // console.log('Setting exam data in slice:', field, value);
            state.examData[field] = value;
        },
        setStep: (state, action) => {
            state.step = action.payload;
        },
        resetData: (state) => {
            state.examData = {
                name: "",
                typeOfExam: null,
                class: null,
                chapter: null,
                year: null,
                description: "",
                testDuration: null,
                passRate: null,
                solutionUrl: "",
                imageUrl: "",
                public: false,
                isClassroomExam: false,
            };
            state.examImage = null;
            state.questions = [];
            state.questionImages = [];
            state.statementImages = [];
            state.solutionImages = [];
            state.examFile = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(postExam.pending, (state) => {
                state.loading = true;
            })
            .addCase(postExam.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(postExam.rejected, (state) => {
                state.loading = false;
            })
    }
});

export const { setLoading, resetData, nextStep, prevStep, setExamData, setStep } = addExamSlice.actions;
export default addExamSlice.reducer;