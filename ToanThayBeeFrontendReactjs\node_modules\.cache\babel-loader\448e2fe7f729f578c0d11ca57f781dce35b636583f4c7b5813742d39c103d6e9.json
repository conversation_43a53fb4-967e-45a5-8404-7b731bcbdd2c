{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\LeftContent.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LeftContent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 overflow-y-auto p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold text-gray-800 mb-4\",\n      children: \"Danh s\\xE1ch c\\xE2u h\\u1ECFi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = LeftContent;\nexport default LeftContent;\nvar _c;\n$RefreshReg$(_c, \"LeftContent\");", "map": {"version": 3, "names": ["LeftContent", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/LeftContent.jsx"], "sourcesContent": ["const LeftContent = () => {\r\n    return (\r\n        <div className=\"flex-1 overflow-y-auto p-4\">\r\n            <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Danh sách câu hỏi</h2>\r\n            <div className=\"space-y-3\">\r\n                {/* Hiển thị danh sách câu hỏi ở đây */}\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nexport default LeftContent;"], "mappings": ";;AAAA,MAAMA,WAAW,GAAGA,CAAA,KAAM;EACtB,oBACIC,OAAA;IAAKC,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACvCF,OAAA;MAAIC,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/EN,OAAA;MAAKC,SAAS,EAAC;IAAW;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAErB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAC,EAAA,GATKR,WAAW;AAYjB,eAAeA,WAAW;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}