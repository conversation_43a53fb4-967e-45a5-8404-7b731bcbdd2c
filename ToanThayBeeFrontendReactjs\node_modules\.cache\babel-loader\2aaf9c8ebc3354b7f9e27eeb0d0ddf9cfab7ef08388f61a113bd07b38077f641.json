{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\modal\\\\AddImagesModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { X, Upload, Image as ImageIcon, Trash2 } from \"lucide-react\";\nimport { setShowAddImagesModal } from \"../../features/examAI/examAISlice\";\nimport { uploadMultipleImages } from \"../../features/image/imageSlice\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddImagesModal = _ref => {\n  _s();\n  var _images$folder;\n  let {\n    showAddImagesModal,\n    folder = 'ImageFormN8N'\n  } = _ref;\n  const dispatch = useDispatch();\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [previewUrls, setPreviewUrls] = useState([]);\n  const [pasteIndicator, setPasteIndicator] = useState(false);\n  const fileInputRef = useRef(null);\n  const {\n    images,\n    loadingUploadImages\n  } = useSelector(state => state.images);\n  const handleClose = () => {\n    dispatch(setShowAddImagesModal(false));\n    setSelectedFiles([]);\n    setPreviewUrls([]);\n  };\n  const handleFileSelect = e => {\n    const files = Array.from(e.target.files);\n\n    // Limit to 10 files\n    const limitedFiles = files.slice(0, 10);\n    setSelectedFiles(limitedFiles);\n\n    // Create preview URLs\n    const urls = limitedFiles.map(file => URL.createObjectURL(file));\n    setPreviewUrls(urls);\n  };\n  const handleRemoveFile = index => {\n    const newFiles = selectedFiles.filter((_, i) => i !== index);\n    const newUrls = previewUrls.filter((_, i) => i !== index);\n\n    // Revoke the removed URL to prevent memory leaks\n    URL.revokeObjectURL(previewUrls[index]);\n    setSelectedFiles(newFiles);\n    setPreviewUrls(newUrls);\n  };\n  const handleUpload = async () => {\n    if (selectedFiles.length === 0) return;\n    try {\n      await dispatch(uploadMultipleImages({\n        files: selectedFiles,\n        folder\n      })).unwrap();\n      // Clear selected files after successful upload\n      setSelectedFiles([]);\n      setPreviewUrls([]);\n      // Reset file input\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    } catch (error) {\n      console.error('Upload failed:', error);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    const files = Array.from(e.dataTransfer.files);\n    const imageFiles = files.filter(file => file.type.startsWith('image/'));\n    const limitedFiles = imageFiles.slice(0, 10);\n    setSelectedFiles(limitedFiles);\n    const urls = limitedFiles.map(file => URL.createObjectURL(file));\n    setPreviewUrls(urls);\n  };\n  const handlePaste = e => {\n    e.preventDefault();\n    const items = Array.from(e.clipboardData.items);\n    const imageItems = items.filter(item => item.type.startsWith('image/'));\n    if (imageItems.length === 0) return;\n    const files = [];\n    const urls = [];\n    imageItems.forEach((item, index) => {\n      const file = item.getAsFile();\n      if (file) {\n        // Create a new file with a proper name\n        const timestamp = Date.now();\n        const newFile = new File([file], \"pasted-image-\".concat(timestamp, \"-\").concat(index, \".\").concat(file.type.split('/')[1]), {\n          type: file.type\n        });\n        files.push(newFile);\n        urls.push(URL.createObjectURL(newFile));\n      }\n    });\n    if (files.length > 0) {\n      // Limit to 10 files total\n      const limitedFiles = files.slice(0, 10);\n      const limitedUrls = urls.slice(0, 10);\n      setSelectedFiles(prev => [...prev, ...limitedFiles].slice(0, 10));\n      setPreviewUrls(prev => [...prev, ...limitedUrls].slice(0, 10));\n\n      // Show paste indicator\n      setPasteIndicator(true);\n      setTimeout(() => setPasteIndicator(false), 2000);\n    }\n  };\n\n  // Add paste event listener when modal is open\n  useEffect(() => {\n    if (showAddImagesModal) {\n      const handleGlobalPaste = e => handlePaste(e);\n      document.addEventListener('paste', handleGlobalPaste);\n      return () => {\n        document.removeEventListener('paste', handleGlobalPaste);\n      };\n    }\n  }, [showAddImagesModal]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[61]\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-800\",\n            children: \"Th\\xEAm \\u1EA3nh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 25\n          }, this), pasteIndicator && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium animate-pulse\",\n            children: \"\\u2713 \\u0110\\xE3 paste \\u1EA3nh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClose,\n          className: \"p-2 hover:bg-gray-100 rounded-full transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 overflow-y-auto max-h-[calc(90vh-160px)]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors\",\n            onDragOver: handleDragOver,\n            onDragEnter: handleDragEnter,\n            onDragLeave: handleDragLeave,\n            onDrop: handleDrop,\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-medium text-gray-700 mb-2\",\n              children: \"K\\xE9o th\\u1EA3, paste (Ctrl+V) ho\\u1EB7c click \\u0111\\u1EC3 ch\\u1ECDn \\u1EA3nh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 mb-4\",\n              children: \"T\\u1ED1i \\u0111a 10 \\u1EA3nh, \\u0111\\u1ECBnh d\\u1EA1ng JPG, PNG, GIF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ref: fileInputRef,\n              type: \"file\",\n              multiple: true,\n              accept: \"image/*\",\n              onChange: handleFileSelect,\n              className: \"hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _fileInputRef$current;\n                return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n              },\n              className: \"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors\",\n              children: \"Ch\\u1ECDn \\u1EA3nh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 21\n        }, this), selectedFiles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-800 mb-3\",\n            children: [\"\\u1EA2nh \\u0111\\xE3 ch\\u1ECDn (\", selectedFiles.length, \"/10)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n            children: previewUrls.map((url, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: url,\n                alt: \"Preview \".concat(index + 1),\n                className: \"w-full h-32 object-cover rounded-lg border\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleRemoveFile(index),\n                className: \"absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\",\n                children: selectedFiles[index].name.length > 15 ? selectedFiles[index].name.substring(0, 15) + '...' : selectedFiles[index].name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 41\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-800 mb-3\",\n            children: [\"\\u1EA2nh \\u0111\\xE3 t\\u1EA3i l\\xEAn (\", (_images$folder = images[folder]) === null || _images$folder === void 0 ? void 0 : _images$folder.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 25\n          }, this), images[folder].length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n            children: images[folder].map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: image[folder] || image.url || image,\n                alt: \"Uploaded \".concat(index + 1),\n                className: \"w-full h-32 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 41\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(ImageIcon, {\n              className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Ch\\u01B0a c\\xF3 \\u1EA3nh n\\xE0o \\u0111\\u01B0\\u1EE3c t\\u1EA3i l\\xEAn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-t bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: selectedFiles.length > 0 && \"\".concat(selectedFiles.length, \" \\u1EA3nh \\u0111\\xE3 ch\\u1ECDn\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n            children: \"\\u0110\\xF3ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 25\n          }, this), selectedFiles.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleUpload,\n            disabled: loadingUploadImages,\n            className: \"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-lg transition-colors flex items-center gap-2\",\n            children: loadingUploadImages ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 41\n              }, this), \"\\u0110ang t\\u1EA3i l\\xEAn...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Upload, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 41\n              }, this), \"T\\u1EA3i l\\xEAn\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 9\n  }, this);\n};\n_s(AddImagesModal, \"E/G6UYoEjUKwuJPq6Ap1aM/Ws1w=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = AddImagesModal;\nexport default AddImagesModal;\nvar _c;\n$RefreshReg$(_c, \"AddImagesModal\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDispatch", "useSelector", "X", "Upload", "Image", "ImageIcon", "Trash2", "setShowAddImagesModal", "uploadMultipleImages", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddImagesModal", "_ref", "_s", "_images$folder", "showAddImagesModal", "folder", "dispatch", "selectedFiles", "setSelectedFiles", "previewUrls", "setPreviewUrls", "pasteIndicator", "setPasteIndicator", "fileInputRef", "images", "loadingUploadImages", "state", "handleClose", "handleFileSelect", "e", "files", "Array", "from", "target", "limitedFiles", "slice", "urls", "map", "file", "URL", "createObjectURL", "handleRemoveFile", "index", "newFiles", "filter", "_", "i", "newUrls", "revokeObjectURL", "handleUpload", "length", "unwrap", "current", "value", "error", "console", "handleDragOver", "preventDefault", "stopPropagation", "handleDragEnter", "handleDragLeave", "handleDrop", "dataTransfer", "imageFiles", "type", "startsWith", "handlePaste", "items", "clipboardData", "imageItems", "item", "for<PERSON>ach", "getAsFile", "timestamp", "Date", "now", "newFile", "File", "concat", "split", "push", "limitedUrls", "prev", "setTimeout", "handleGlobalPaste", "document", "addEventListener", "removeEventListener", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "ref", "multiple", "accept", "onChange", "_fileInputRef$current", "click", "url", "src", "alt", "name", "substring", "image", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/modal/AddImagesModal.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { X, Upload, Image as ImageIcon, Trash2 } from \"lucide-react\";\nimport { setShowAddImagesModal } from \"../../features/examAI/examAISlice\";\nimport { uploadMultipleImages } from \"../../features/image/imageSlice\";\n\nconst AddImagesModal = ({ showAddImagesModal, folder = 'ImageFormN8N' }) => {\n    const dispatch = useDispatch();\n    const [selectedFiles, setSelectedFiles] = useState([]);\n    const [previewUrls, setPreviewUrls] = useState([]);\n    const [pasteIndicator, setPasteIndicator] = useState(false);\n    const fileInputRef = useRef(null);\n    const { images, loadingUploadImages } = useSelector(state => state.images);\n\n    const handleClose = () => {\n        dispatch(setShowAddImagesModal(false));\n        setSelectedFiles([]);\n        setPreviewUrls([]);\n    };\n\n    const handleFileSelect = (e) => {\n        const files = Array.from(e.target.files);\n\n        // Limit to 10 files\n        const limitedFiles = files.slice(0, 10);\n\n        setSelectedFiles(limitedFiles);\n\n        // Create preview URLs\n        const urls = limitedFiles.map(file => URL.createObjectURL(file));\n        setPreviewUrls(urls);\n    };\n\n    const handleRemoveFile = (index) => {\n        const newFiles = selectedFiles.filter((_, i) => i !== index);\n        const newUrls = previewUrls.filter((_, i) => i !== index);\n\n        // Revoke the removed URL to prevent memory leaks\n        URL.revokeObjectURL(previewUrls[index]);\n\n        setSelectedFiles(newFiles);\n        setPreviewUrls(newUrls);\n    };\n\n    const handleUpload = async () => {\n        if (selectedFiles.length === 0) return;\n\n        try {\n            await dispatch(uploadMultipleImages({ files: selectedFiles, folder })).unwrap();\n            // Clear selected files after successful upload\n            setSelectedFiles([]);\n            setPreviewUrls([]);\n            // Reset file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n        } catch (error) {\n            console.error('Upload failed:', error);\n        }\n    };\n\n    const handleDragOver = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n    };\n\n    const handleDragEnter = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n    };\n\n    const handleDragLeave = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n    };\n\n    const handleDrop = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n\n        const files = Array.from(e.dataTransfer.files);\n        const imageFiles = files.filter(file => file.type.startsWith('image/'));\n        const limitedFiles = imageFiles.slice(0, 10);\n\n        setSelectedFiles(limitedFiles);\n\n        const urls = limitedFiles.map(file => URL.createObjectURL(file));\n        setPreviewUrls(urls);\n    };\n\n    const handlePaste = (e) => {\n        e.preventDefault();\n\n        const items = Array.from(e.clipboardData.items);\n        const imageItems = items.filter(item => item.type.startsWith('image/'));\n\n        if (imageItems.length === 0) return;\n\n        const files = [];\n        const urls = [];\n\n        imageItems.forEach((item, index) => {\n            const file = item.getAsFile();\n            if (file) {\n                // Create a new file with a proper name\n                const timestamp = Date.now();\n                const newFile = new File([file], `pasted-image-${timestamp}-${index}.${file.type.split('/')[1]}`, {\n                    type: file.type\n                });\n\n                files.push(newFile);\n                urls.push(URL.createObjectURL(newFile));\n            }\n        });\n\n        if (files.length > 0) {\n            // Limit to 10 files total\n            const limitedFiles = files.slice(0, 10);\n            const limitedUrls = urls.slice(0, 10);\n\n            setSelectedFiles(prev => [...prev, ...limitedFiles].slice(0, 10));\n            setPreviewUrls(prev => [...prev, ...limitedUrls].slice(0, 10));\n\n            // Show paste indicator\n            setPasteIndicator(true);\n            setTimeout(() => setPasteIndicator(false), 2000);\n        }\n    };\n\n    // Add paste event listener when modal is open\n    useEffect(() => {\n        if (showAddImagesModal) {\n            const handleGlobalPaste = (e) => handlePaste(e);\n            document.addEventListener('paste', handleGlobalPaste);\n\n            return () => {\n                document.removeEventListener('paste', handleGlobalPaste);\n            };\n        }\n    }, [showAddImagesModal]);\n\n    return (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[61]\">\n            <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\">\n                {/* Header */}\n                <div className=\"flex items-center justify-between p-6 border-b\">\n                    <div className=\"flex items-center gap-3\">\n                        <h2 className=\"text-xl font-semibold text-gray-800\">Thêm ảnh</h2>\n                        {pasteIndicator && (\n                            <div className=\"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium animate-pulse\">\n                                ✓ Đã paste ảnh\n                            </div>\n                        )}\n                    </div>\n                    <button\n                        onClick={handleClose}\n                        className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n                    >\n                        <X className=\"w-5 h-5\" />\n                    </button>\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-160px)]\">\n                    {/* Upload Area */}\n                    <div className=\"mb-6\">\n                        <div\n                            className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors\"\n                            onDragOver={handleDragOver}\n                            onDragEnter={handleDragEnter}\n                            onDragLeave={handleDragLeave}\n                            onDrop={handleDrop}\n                        >\n                            <Upload className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                            <p className=\"text-lg font-medium text-gray-700 mb-2\">\n                                Kéo thả, paste (Ctrl+V) hoặc click để chọn ảnh\n                            </p>\n                            <p className=\"text-sm text-gray-500 mb-4\">\n                                Tối đa 10 ảnh, định dạng JPG, PNG, GIF\n                            </p>\n                            <input\n                                ref={fileInputRef}\n                                type=\"file\"\n                                multiple\n                                accept=\"image/*\"\n                                onChange={handleFileSelect}\n                                className=\"hidden\"\n                            />\n                            <button\n                                onClick={() => fileInputRef.current?.click()}\n                                className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors\"\n                            >\n                                Chọn ảnh\n                            </button>\n                        </div>\n                    </div>\n\n                    {/* Selected Files Preview */}\n                    {selectedFiles.length > 0 && (\n                        <div className=\"mb-6\">\n                            <h3 className=\"text-lg font-medium text-gray-800 mb-3\">\n                                Ảnh đã chọn ({selectedFiles.length}/10)\n                            </h3>\n                            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n                                {previewUrls.map((url, index) => (\n                                    <div key={index} className=\"relative group\">\n                                        <img\n                                            src={url}\n                                            alt={`Preview ${index + 1}`}\n                                            className=\"w-full h-32 object-cover rounded-lg border\"\n                                        />\n                                        <button\n                                            onClick={() => handleRemoveFile(index)}\n                                            className=\"absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\"\n                                        >\n                                            <Trash2 className=\"w-4 h-4\" />\n                                        </button>\n                                        <div className=\"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\">\n                                            {selectedFiles[index].name.length > 15\n                                                ? selectedFiles[index].name.substring(0, 15) + '...'\n                                                : selectedFiles[index].name\n                                            }\n                                        </div>\n                                    </div>\n                                ))}\n                            </div>\n                        </div>\n                    )}\n\n                    {/* Existing Images */}\n                    <div>\n                        <h3 className=\"text-lg font-medium text-gray-800 mb-3\">\n                            Ảnh đã tải lên ({images[folder]?.length})\n                        </h3>\n                        {images[folder].length > 0 ? (\n                            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n                                {images[folder].map((image, index) => (\n                                    <div key={index} className=\"relative group\">\n                                        <img\n                                            src={image[folder] || image.url || image}\n                                            alt={`Uploaded ${index + 1}`}\n                                            className=\"w-full h-32 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity\"\n                                        />\n                                    </div>\n                                ))}\n                            </div>\n                        ) : (\n                            <div className=\"text-center py-8 text-gray-500\">\n                                <ImageIcon className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                                <p>Chưa có ảnh nào được tải lên</p>\n                            </div>\n                        )}\n                    </div>\n                </div>\n\n                {/* Footer */}\n                <div className=\"flex items-center justify-between p-6 border-t bg-gray-50\">\n                    <div className=\"text-sm text-gray-600\">\n                        {selectedFiles.length > 0 && `${selectedFiles.length} ảnh đã chọn`}\n                    </div>\n                    <div className=\"flex gap-3\">\n                        <button\n                            onClick={handleClose}\n                            className=\"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n                        >\n                            Đóng\n                        </button>\n                        {selectedFiles.length > 0 && (\n                            <button\n                                onClick={handleUpload}\n                                disabled={loadingUploadImages}\n                                className=\"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-lg transition-colors flex items-center gap-2\"\n                            >\n                                {loadingUploadImages ? (\n                                    <>\n                                        <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                                        Đang tải lên...\n                                    </>\n                                ) : (\n                                    <>\n                                        <Upload className=\"w-4 h-4\" />\n                                        Tải lên\n                                    </>\n                                )}\n                            </button>\n                        )}\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default AddImagesModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,CAAC,EAAEC,MAAM,EAAEC,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,cAAc;AACpE,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,oBAAoB,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,cAAc,GAAGC,IAAA,IAAqD;EAAAC,EAAA;EAAA,IAAAC,cAAA;EAAA,IAApD;IAAEC,kBAAkB;IAAEC,MAAM,GAAG;EAAe,CAAC,GAAAJ,IAAA;EACnE,MAAMK,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM6B,YAAY,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAE6B,MAAM;IAAEC;EAAoB,CAAC,GAAG3B,WAAW,CAAC4B,KAAK,IAAIA,KAAK,CAACF,MAAM,CAAC;EAE1E,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACtBX,QAAQ,CAACZ,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtCc,gBAAgB,CAAC,EAAE,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAMQ,gBAAgB,GAAIC,CAAC,IAAK;IAC5B,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;;IAExC;IACA,MAAMI,YAAY,GAAGJ,KAAK,CAACK,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAEvCjB,gBAAgB,CAACgB,YAAY,CAAC;;IAE9B;IACA,MAAME,IAAI,GAAGF,YAAY,CAACG,GAAG,CAACC,IAAI,IAAIC,GAAG,CAACC,eAAe,CAACF,IAAI,CAAC,CAAC;IAChElB,cAAc,CAACgB,IAAI,CAAC;EACxB,CAAC;EAED,MAAMK,gBAAgB,GAAIC,KAAK,IAAK;IAChC,MAAMC,QAAQ,GAAG1B,aAAa,CAAC2B,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAC5D,MAAMK,OAAO,GAAG5B,WAAW,CAACyB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;;IAEzD;IACAH,GAAG,CAACS,eAAe,CAAC7B,WAAW,CAACuB,KAAK,CAAC,CAAC;IAEvCxB,gBAAgB,CAACyB,QAAQ,CAAC;IAC1BvB,cAAc,CAAC2B,OAAO,CAAC;EAC3B,CAAC;EAED,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAIhC,aAAa,CAACiC,MAAM,KAAK,CAAC,EAAE;IAEhC,IAAI;MACA,MAAMlC,QAAQ,CAACX,oBAAoB,CAAC;QAAEyB,KAAK,EAAEb,aAAa;QAAEF;MAAO,CAAC,CAAC,CAAC,CAACoC,MAAM,CAAC,CAAC;MAC/E;MACAjC,gBAAgB,CAAC,EAAE,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClB;MACA,IAAIG,YAAY,CAAC6B,OAAO,EAAE;QACtB7B,YAAY,CAAC6B,OAAO,CAACC,KAAK,GAAG,EAAE;MACnC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IAC1C;EACJ,CAAC;EAED,MAAME,cAAc,GAAI3B,CAAC,IAAK;IAC1BA,CAAC,CAAC4B,cAAc,CAAC,CAAC;IAClB5B,CAAC,CAAC6B,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAMC,eAAe,GAAI9B,CAAC,IAAK;IAC3BA,CAAC,CAAC4B,cAAc,CAAC,CAAC;IAClB5B,CAAC,CAAC6B,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAME,eAAe,GAAI/B,CAAC,IAAK;IAC3BA,CAAC,CAAC4B,cAAc,CAAC,CAAC;IAClB5B,CAAC,CAAC6B,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAMG,UAAU,GAAIhC,CAAC,IAAK;IACtBA,CAAC,CAAC4B,cAAc,CAAC,CAAC;IAClB5B,CAAC,CAAC6B,eAAe,CAAC,CAAC;IAEnB,MAAM5B,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACiC,YAAY,CAAChC,KAAK,CAAC;IAC9C,MAAMiC,UAAU,GAAGjC,KAAK,CAACc,MAAM,CAACN,IAAI,IAAIA,IAAI,CAAC0B,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACvE,MAAM/B,YAAY,GAAG6B,UAAU,CAAC5B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAE5CjB,gBAAgB,CAACgB,YAAY,CAAC;IAE9B,MAAME,IAAI,GAAGF,YAAY,CAACG,GAAG,CAACC,IAAI,IAAIC,GAAG,CAACC,eAAe,CAACF,IAAI,CAAC,CAAC;IAChElB,cAAc,CAACgB,IAAI,CAAC;EACxB,CAAC;EAED,MAAM8B,WAAW,GAAIrC,CAAC,IAAK;IACvBA,CAAC,CAAC4B,cAAc,CAAC,CAAC;IAElB,MAAMU,KAAK,GAAGpC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACuC,aAAa,CAACD,KAAK,CAAC;IAC/C,MAAME,UAAU,GAAGF,KAAK,CAACvB,MAAM,CAAC0B,IAAI,IAAIA,IAAI,CAACN,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAEvE,IAAII,UAAU,CAACnB,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAMpB,KAAK,GAAG,EAAE;IAChB,MAAMM,IAAI,GAAG,EAAE;IAEfiC,UAAU,CAACE,OAAO,CAAC,CAACD,IAAI,EAAE5B,KAAK,KAAK;MAChC,MAAMJ,IAAI,GAAGgC,IAAI,CAACE,SAAS,CAAC,CAAC;MAC7B,IAAIlC,IAAI,EAAE;QACN;QACA,MAAMmC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC5B,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACvC,IAAI,CAAC,kBAAAwC,MAAA,CAAkBL,SAAS,OAAAK,MAAA,CAAIpC,KAAK,OAAAoC,MAAA,CAAIxC,IAAI,CAAC0B,IAAI,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAI;UAC9Ff,IAAI,EAAE1B,IAAI,CAAC0B;QACf,CAAC,CAAC;QAEFlC,KAAK,CAACkD,IAAI,CAACJ,OAAO,CAAC;QACnBxC,IAAI,CAAC4C,IAAI,CAACzC,GAAG,CAACC,eAAe,CAACoC,OAAO,CAAC,CAAC;MAC3C;IACJ,CAAC,CAAC;IAEF,IAAI9C,KAAK,CAACoB,MAAM,GAAG,CAAC,EAAE;MAClB;MACA,MAAMhB,YAAY,GAAGJ,KAAK,CAACK,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MACvC,MAAM8C,WAAW,GAAG7C,IAAI,CAACD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAErCjB,gBAAgB,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGhD,YAAY,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACjEf,cAAc,CAAC8D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,WAAW,CAAC,CAAC9C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAE9D;MACAb,iBAAiB,CAAC,IAAI,CAAC;MACvB6D,UAAU,CAAC,MAAM7D,iBAAiB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACpD;EACJ,CAAC;;EAED;EACA1B,SAAS,CAAC,MAAM;IACZ,IAAIkB,kBAAkB,EAAE;MACpB,MAAMsE,iBAAiB,GAAIvD,CAAC,IAAKqC,WAAW,CAACrC,CAAC,CAAC;MAC/CwD,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;MAErD,OAAO,MAAM;QACTC,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEH,iBAAiB,CAAC;MAC5D,CAAC;IACL;EACJ,CAAC,EAAE,CAACtE,kBAAkB,CAAC,CAAC;EAExB,oBACIP,OAAA;IAAKiF,SAAS,EAAC,8EAA8E;IAAAC,QAAA,eACzFlF,OAAA;MAAKiF,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBAE7FlF,OAAA;QAAKiF,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC3DlF,OAAA;UAAKiF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpClF,OAAA;YAAIiF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAChExE,cAAc,iBACXd,OAAA;YAAKiF,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EAAC;UAEtG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACNtF,OAAA;UACIuF,OAAO,EAAEnE,WAAY;UACrB6D,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eAEhElF,OAAA,CAACR,CAAC;YAACyF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAEzDlF,OAAA;UAAKiF,SAAS,EAAC,MAAM;UAAAC,QAAA,eACjBlF,OAAA;YACIiF,SAAS,EAAC,2GAA2G;YACrHO,UAAU,EAAEvC,cAAe;YAC3BwC,WAAW,EAAErC,eAAgB;YAC7BsC,WAAW,EAAErC,eAAgB;YAC7BsC,MAAM,EAAErC,UAAW;YAAA4B,QAAA,gBAEnBlF,OAAA,CAACP,MAAM;cAACwF,SAAS,EAAC;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DtF,OAAA;cAAGiF,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtF,OAAA;cAAGiF,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtF,OAAA;cACI4F,GAAG,EAAE5E,YAAa;cAClByC,IAAI,EAAC,MAAM;cACXoC,QAAQ;cACRC,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAE1E,gBAAiB;cAC3B4D,SAAS,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFtF,OAAA;cACIuF,OAAO,EAAEA,CAAA;gBAAA,IAAAS,qBAAA;gBAAA,QAAAA,qBAAA,GAAMhF,YAAY,CAAC6B,OAAO,cAAAmD,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;cAAA,CAAC;cAC7ChB,SAAS,EAAC,iFAAiF;cAAAC,QAAA,EAC9F;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGL5E,aAAa,CAACiC,MAAM,GAAG,CAAC,iBACrB3C,OAAA;UAAKiF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACjBlF,OAAA;YAAIiF,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,iCACtC,EAACxE,aAAa,CAACiC,MAAM,EAAC,MACvC;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtF,OAAA;YAAKiF,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAChEtE,WAAW,CAACkB,GAAG,CAAC,CAACoE,GAAG,EAAE/D,KAAK,kBACxBnC,OAAA;cAAiBiF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBACvClF,OAAA;gBACImG,GAAG,EAAED,GAAI;gBACTE,GAAG,aAAA7B,MAAA,CAAapC,KAAK,GAAG,CAAC,CAAG;gBAC5B8C,SAAS,EAAC;cAA4C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACFtF,OAAA;gBACIuF,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACC,KAAK,CAAE;gBACvC8C,SAAS,EAAC,uIAAuI;gBAAAC,QAAA,eAEjJlF,OAAA,CAACJ,MAAM;kBAACqF,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACTtF,OAAA;gBAAKiF,SAAS,EAAC,sFAAsF;gBAAAC,QAAA,EAChGxE,aAAa,CAACyB,KAAK,CAAC,CAACkE,IAAI,CAAC1D,MAAM,GAAG,EAAE,GAChCjC,aAAa,CAACyB,KAAK,CAAC,CAACkE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAClD5F,aAAa,CAACyB,KAAK,CAAC,CAACkE;cAAI;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE9B,CAAC;YAAA,GAjBAnD,KAAK;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,eAGDtF,OAAA;UAAAkF,QAAA,gBACIlF,OAAA;YAAIiF,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,uCACnC,GAAA5E,cAAA,GAACW,MAAM,CAACT,MAAM,CAAC,cAAAF,cAAA,uBAAdA,cAAA,CAAgBqC,MAAM,EAAC,GAC5C;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACJrE,MAAM,CAACT,MAAM,CAAC,CAACmC,MAAM,GAAG,CAAC,gBACtB3C,OAAA;YAAKiF,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAChEjE,MAAM,CAACT,MAAM,CAAC,CAACsB,GAAG,CAAC,CAACyE,KAAK,EAAEpE,KAAK,kBAC7BnC,OAAA;cAAiBiF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eACvClF,OAAA;gBACImG,GAAG,EAAEI,KAAK,CAAC/F,MAAM,CAAC,IAAI+F,KAAK,CAACL,GAAG,IAAIK,KAAM;gBACzCH,GAAG,cAAA7B,MAAA,CAAcpC,KAAK,GAAG,CAAC,CAAG;gBAC7B8C,SAAS,EAAC;cAA+F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5G;YAAC,GALInD,KAAK;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,gBAENtF,OAAA;YAAKiF,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC3ClF,OAAA,CAACL,SAAS;cAACsF,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DtF,OAAA;cAAAkF,QAAA,EAAG;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACtElF,OAAA;UAAKiF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACjCxE,aAAa,CAACiC,MAAM,GAAG,CAAC,OAAA4B,MAAA,CAAO7D,aAAa,CAACiC,MAAM;QAAc;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACNtF,OAAA;UAAKiF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBlF,OAAA;YACIuF,OAAO,EAAEnE,WAAY;YACrB6D,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAC5E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACR5E,aAAa,CAACiC,MAAM,GAAG,CAAC,iBACrB3C,OAAA;YACIuF,OAAO,EAAE7C,YAAa;YACtB8D,QAAQ,EAAEtF,mBAAoB;YAC9B+D,SAAS,EAAC,8HAA8H;YAAAC,QAAA,EAEvIhE,mBAAmB,gBAChBlB,OAAA,CAAAE,SAAA;cAAAgF,QAAA,gBACIlF,OAAA;gBAAKiF,SAAS,EAAC;cAA8E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gCAExG;YAAA,eAAE,CAAC,gBAEHtF,OAAA,CAAAE,SAAA;cAAAgF,QAAA,gBACIlF,OAAA,CAACP,MAAM;gBAACwF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAElC;YAAA,eAAE;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACjF,EAAA,CA7RIF,cAAc;EAAA,QACCb,WAAW,EAKYC,WAAW;AAAA;AAAAkH,EAAA,GANjDtG,cAAc;AA+RpB,eAAeA,cAAc;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}