{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\QuestionContent.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { selectQuestion } from \"src/features/examAI/examAISlice\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const RenderStatement = _ref => {\n  let {\n    content,\n    prefix,\n    index,\n    isCorrect\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-gray-800 flex flex-row gap-1 \".concat(isCorrect ? \"text-green-600\" : \"text-red-600\"),\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"font-semibold\",\n      children: [prefix, \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n      text: content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this)]\n  }, index, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 9\n  }, this);\n};\n_c = RenderStatement;\nexport const QuestionContent = _ref2 => {\n  _s();\n  var _codes$chapter, _codes$chapter$find;\n  let {\n    question,\n    index\n  } = _ref2;\n  const dispatch = useDispatch();\n  const {\n    selectedQuestion\n  } = useSelector(state => state.examAI);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n  const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n  const renderStatement = question => {\n    if (question.typeOfQuestion === \"TN\") {\n      return question.statement1s.map((item, idx) => /*#__PURE__*/_jsxDEV(RenderStatement, {\n        index: idx,\n        content: item.content,\n        prefix: prefixTN[idx],\n        isCorrect: item.isCorrect\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 21\n      }, this));\n    } else if (question.typeOfQuestion === \"DS\") {\n      return question.statement1s.map((item, idx) => /*#__PURE__*/_jsxDEV(RenderStatement, {\n        index: idx,\n        content: item.content,\n        prefix: prefixDS[idx],\n        isCorrect: item.isCorrect\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 21\n      }, this));\n    } else if (question.typeOfQuestion === \"TLN\") {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold\",\n          children: \"\\u0110\\xE1p \\xE1n: \"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: question.correctAnswer\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\\n            \".concat((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.id) === question.id ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\", \"\\n        \"),\n    onClick: () => dispatch(selectQuestion(question)),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-gray-700\",\n        children: [\"C\\xE2u \", index + 1]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\u0110\\u1ED9 kh\\xF3: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: question.difficulty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Ch\\u01B0\\u01A1ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: ((_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === question.chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || \"Chưa xác định\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-base text-gray-800 leading-relaxed\",\n      children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        className: \"text-gray-800\",\n        text: question.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(SortableStatementsContainer, {\n      question: question\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"L\\u1EDDi gi\\u1EA3i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 21\n      }, this), \" \", /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: question.solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 17\n    }, this)]\n  }, question.id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionContent, \"SeAVc7soWB6uWcGv4aVyZGOqKtU=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c2 = QuestionContent;\nexport default QuestionContent;\nvar _c, _c2;\n$RefreshReg$(_c, \"RenderStatement\");\n$RefreshReg$(_c2, \"QuestionContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "selectQuestion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "SortableStatementsContainer", "jsxDEV", "_jsxDEV", "RenderStatement", "_ref", "content", "prefix", "index", "isCorrect", "className", "concat", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "_c", "QuestionContent", "_ref2", "_s", "_codes$chapter", "_codes$chapter$find", "question", "dispatch", "selectedQuestion", "state", "examAI", "codes", "prefixTN", "prefixDS", "renderStatement", "typeOfQuestion", "statement1s", "map", "item", "idx", "<PERSON><PERSON><PERSON><PERSON>", "id", "onClick", "difficulty", "find", "c", "code", "chapter", "description", "solution", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/QuestionContent.jsx"], "sourcesContent": ["\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { selectQuestion } from \"src/features/examAI/examAISlice\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\r\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\r\n\r\nexport const RenderStatement = ({ content, prefix, index, isCorrect }) => {\r\n    return (\r\n        <div key={index} className={`text-gray-800 flex flex-row gap-1 ${isCorrect ? \"text-green-600\" : \"text-red-600\"}`}>\r\n            <span className=\"font-semibold\">{prefix} </span>\r\n            <LatexRenderer text={content} />\r\n        </div>\r\n    )\r\n}\r\n\r\nexport const QuestionContent = ({ question, index }) => {\r\n    const dispatch = useDispatch();\r\n    const { selectedQuestion } = useSelector((state) => state.examAI);\r\n\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\r\n    const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\r\n\r\n    const renderStatement = (question) => {\r\n        if (question.typeOfQuestion === \"TN\") {\r\n            return (\r\n                question.statement1s.map((item, idx) => (\r\n                    <RenderStatement index={idx} content={item.content} prefix={prefixTN[idx]} isCorrect={item.isCorrect} />\r\n                ))\r\n            );\r\n        } else if (question.typeOfQuestion === \"DS\") {\r\n            return (\r\n                question.statement1s.map((item, idx) => (\r\n                    <RenderStatement index={idx} content={item.content} prefix={prefixDS[idx]} isCorrect={item.isCorrect} />\r\n                ))\r\n            );\r\n        } else if (question.typeOfQuestion === \"TLN\") {\r\n            return (\r\n                <div className=\"text-gray-800\">\r\n                    <span className=\"font-semibold\">Đáp án: </span>\r\n                    <span>{question.correctAnswer}</span>\r\n                </div>\r\n            );\r\n        }\r\n\r\n    }\r\n\r\n    return (\r\n        <div\r\n            key={question.id}\r\n            className={`\r\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\r\n            ${selectedQuestion?.id === question.id\r\n                    ? \"bg-blue-50 border-blue-500\"\r\n                    : \"bg-white hover:bg-gray-50 border-gray-300\"\r\n                }\r\n        `}\r\n            onClick={() => dispatch(selectQuestion(question))}\r\n        >\r\n            {/* Thông tin câu hỏi */}\r\n            <div className=\"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\">\r\n                <span className=\"font-medium text-gray-700\">Câu {index + 1}</span>\r\n                <span>Độ khó: <span className=\"text-gray-700\">{question.difficulty}</span></span>\r\n                <span>\r\n                    Chương:{\" \"}\r\n                    <span className=\"text-gray-700\">\r\n                        {codes[\"chapter\"]?.find(c => c.code === question.chapter)?.description || \"Chưa xác định\"}\r\n                    </span>\r\n                </span>\r\n            </div>\r\n\r\n            {/* Nội dung câu hỏi */}\r\n            <div className=\"text-base text-gray-800 leading-relaxed\">\r\n                <LatexRenderer className=\"text-gray-800\" text={question.content} />\r\n            </div>\r\n\r\n            {/* Statement: A, B, C,... */}\r\n            <SortableStatementsContainer question={question} />\r\n\r\n            {/* Lời giải (nếu có) */}\r\n            {question.solution && (\r\n                <div className=\"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\">\r\n                    <span className=\"font-semibold\">Lời giải:</span>{\" \"}\r\n                    <MarkdownPreviewWithMath content={question.solution} />\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default QuestionContent;"], "mappings": ";;AACA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,2BAA2B,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,OAAO,MAAMC,eAAe,GAAGC,IAAA,IAA2C;EAAA,IAA1C;IAAEC,OAAO;IAAEC,MAAM;IAAEC,KAAK;IAAEC;EAAU,CAAC,GAAAJ,IAAA;EACjE,oBACIF,OAAA;IAAiBO,SAAS,uCAAAC,MAAA,CAAuCF,SAAS,GAAG,gBAAgB,GAAG,cAAc,CAAG;IAAAG,QAAA,gBAC7GT,OAAA;MAAMO,SAAS,EAAC,eAAe;MAAAE,QAAA,GAAEL,MAAM,EAAC,GAAC;IAAA;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAChDb,OAAA,CAACJ,aAAa;MAACkB,IAAI,EAAEX;IAAQ;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,GAF1BR,KAAK;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGV,CAAC;AAEd,CAAC;AAAAE,EAAA,GAPYd,eAAe;AAS5B,OAAO,MAAMe,eAAe,GAAGC,KAAA,IAAyB;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,mBAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEhB;EAAM,CAAC,GAAAY,KAAA;EAC/C,MAAMK,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B;EAAiB,CAAC,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEjE,MAAM;IAAEC;EAAM,CAAC,GAAGjC,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,eAAe,GAAIR,QAAQ,IAAK;IAClC,IAAIA,QAAQ,CAACS,cAAc,KAAK,IAAI,EAAE;MAClC,OACIT,QAAQ,CAACU,WAAW,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBAC/BlC,OAAA,CAACC,eAAe;QAACI,KAAK,EAAE6B,GAAI;QAAC/B,OAAO,EAAE8B,IAAI,CAAC9B,OAAQ;QAACC,MAAM,EAAEuB,QAAQ,CAACO,GAAG,CAAE;QAAC5B,SAAS,EAAE2B,IAAI,CAAC3B;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC1G,CAAC;IAEV,CAAC,MAAM,IAAIQ,QAAQ,CAACS,cAAc,KAAK,IAAI,EAAE;MACzC,OACIT,QAAQ,CAACU,WAAW,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBAC/BlC,OAAA,CAACC,eAAe;QAACI,KAAK,EAAE6B,GAAI;QAAC/B,OAAO,EAAE8B,IAAI,CAAC9B,OAAQ;QAACC,MAAM,EAAEwB,QAAQ,CAACM,GAAG,CAAE;QAAC5B,SAAS,EAAE2B,IAAI,CAAC3B;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC1G,CAAC;IAEV,CAAC,MAAM,IAAIQ,QAAQ,CAACS,cAAc,KAAK,KAAK,EAAE;MAC1C,oBACI9B,OAAA;QAAKO,SAAS,EAAC,eAAe;QAAAE,QAAA,gBAC1BT,OAAA;UAAMO,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/Cb,OAAA;UAAAS,QAAA,EAAOY,QAAQ,CAACc;QAAa;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAEd;EAEJ,CAAC;EAED,oBACIb,OAAA;IAEIO,SAAS,oGAAAC,MAAA,CAEP,CAAAe,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEa,EAAE,MAAKf,QAAQ,CAACe,EAAE,GAC5B,4BAA4B,GAC5B,2CAA2C,eAEvD;IACEC,OAAO,EAAEA,CAAA,KAAMf,QAAQ,CAAC3B,cAAc,CAAC0B,QAAQ,CAAC,CAAE;IAAAZ,QAAA,gBAGlDT,OAAA;MAAKO,SAAS,EAAC,mEAAmE;MAAAE,QAAA,gBAC9ET,OAAA;QAAMO,SAAS,EAAC,2BAA2B;QAAAE,QAAA,GAAC,SAAI,EAACJ,KAAK,GAAG,CAAC;MAAA;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEb,OAAA;QAAAS,QAAA,GAAM,uBAAQ,eAAAT,OAAA;UAAMO,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAEY,QAAQ,CAACiB;QAAU;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjFb,OAAA;QAAAS,QAAA,GAAM,mBACK,EAAC,GAAG,eACXT,OAAA;UAAMO,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC1B,EAAAU,cAAA,GAAAO,KAAK,CAAC,SAAS,CAAC,cAAAP,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKpB,QAAQ,CAACqB,OAAO,CAAC,cAAAtB,mBAAA,uBAAxDA,mBAAA,CAA0DuB,WAAW,KAAI;QAAe;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNb,OAAA;MAAKO,SAAS,EAAC,yCAAyC;MAAAE,QAAA,eACpDT,OAAA,CAACJ,aAAa;QAACW,SAAS,EAAC,eAAe;QAACO,IAAI,EAAEO,QAAQ,CAAClB;MAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAGNb,OAAA,CAACF,2BAA2B;MAACuB,QAAQ,EAAEA;IAAS;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGlDQ,QAAQ,CAACuB,QAAQ,iBACd5C,OAAA;MAAKO,SAAS,EAAC,uFAAuF;MAAAE,QAAA,gBAClGT,OAAA;QAAMO,SAAS,EAAC,eAAe;QAAAE,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAG,eACpDb,OAAA,CAACH,uBAAuB;QAACM,OAAO,EAAEkB,QAAQ,CAACuB;MAAS;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CACR;EAAA,GApCIQ,QAAQ,CAACe,EAAE;IAAA1B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAqCf,CAAC;AAEd,CAAC;AAAAK,EAAA,CA1EYF,eAAe;EAAA,QACPtB,WAAW,EACCD,WAAW,EAEtBA,WAAW;AAAA;AAAAoD,GAAA,GAJpB7B,eAAe;AA4E5B,eAAeA,eAAe;AAAC,IAAAD,EAAA,EAAA8B,GAAA;AAAAC,YAAA,CAAA/B,EAAA;AAAA+B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}