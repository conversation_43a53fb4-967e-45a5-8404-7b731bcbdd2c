{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\QuestionOfExamAdmin.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport { fetchExamQuestionsWithoutPagination } from \"src/features/questionsExam/questionsExamSlice\";\nimport { useNavigate } from \"react-router-dom\";\nimport ExamAdminLayout from \"src/layouts/ExamAdminLayout\";\nimport { useEffect } from \"react\";\nimport LeftContent from \"src/components/PageQuestionsExam/LeftContent\";\nimport RightContent from \"src/components/PageQuestionsExam/RightContent\";\nimport LoadingData from \"src/components/loading/LoadingData\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionOfExamAdmin = () => {\n  _s();\n  const {\n    examId\n  } = useParams();\n  const {\n    questionsExam,\n    loading\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    dispatch(fetchExamQuestionsWithoutPagination({\n      id: examId\n    }));\n  }, [dispatch, examId]);\n  return /*#__PURE__*/_jsxDEV(ExamAdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(LoadingData, {\n      loading: loading,\n      loadText: \"\\u0110ang t\\u1EA3i danh s\\xE1ch c\\xE2u h\\u1ECFi\",\n      noDataText: \"Kh\\xF4ng c\\xF3 c\\xE2u h\\u1ECFi n\\xE0o.\",\n      isNoData: questionsExam.length > 0 ? false : true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-1 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-1/2 border-r border-gray-200 bg-white\",\n          children: /*#__PURE__*/_jsxDEV(LeftContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-1/2\",\n          children: /*#__PURE__*/_jsxDEV(RightContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionOfExamAdmin, \"zxnbXYsJdnsHMNAjJx8Z3WvuKMg=\", false, function () {\n  return [useParams, useSelector, useDispatch];\n});\n_c = QuestionOfExamAdmin;\nexport default QuestionOfExamAdmin;\nvar _c;\n$RefreshReg$(_c, \"QuestionOfExamAdmin\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "useParams", "fetchExamQuestionsWithoutPagination", "useNavigate", "ExamAdminLayout", "useEffect", "LeftContent", "RightContent", "LoadingData", "jsxDEV", "_jsxDEV", "QuestionOfExamAdmin", "_s", "examId", "questionsExam", "loading", "state", "dispatch", "id", "children", "loadText", "noDataText", "isNoData", "length", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/QuestionOfExamAdmin.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { fetchExamQuestionsWithoutPagination } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ExamAdminLayout from \"src/layouts/ExamAdminLayout\";\r\nimport { useEffect } from \"react\";\r\nimport LeftContent from \"src/components/PageQuestionsExam/LeftContent\";\r\nimport RightContent from \"src/components/PageQuestionsExam/RightContent\";\r\nimport LoadingData from \"src/components/loading/LoadingData\";\r\n\r\nconst QuestionOfExamAdmin = () => {\r\n    const { examId } = useParams();\r\n    const { questionsExam, loading } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        dispatch(fetchExamQuestionsWithoutPagination({ id: examId }));\r\n    }, [dispatch, examId]);\r\n\r\n    return (\r\n        <ExamAdminLayout>\r\n            <LoadingData\r\n                loading={loading}\r\n                loadText=\"Đang tải danh sách câu hỏi\"\r\n                noDataText=\"Không có câu hỏi nào.\"\r\n                isNoData={questionsExam.length > 0 ? false : true}\r\n            >\r\n                <div className=\"flex flex-1 overflow-hidden\">\r\n                    {/* Left Panel - Form */}\r\n                    <div className=\"w-1/2 border-r border-gray-200 bg-white\">\r\n                        <LeftContent />\r\n                    </div>\r\n\r\n                    {/* Right Panel - Preview */}\r\n                    <div className=\"w-1/2\">\r\n                        <RightContent />\r\n                    </div>\r\n                </div>\r\n            </LoadingData>\r\n\r\n        </ExamAdminLayout>\r\n\r\n    )\r\n}\r\n\r\nexport default QuestionOfExamAdmin;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,mCAAmC,QAAQ,+CAA+C;AACnG,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,8CAA8C;AACtE,OAAOC,YAAY,MAAM,+CAA+C;AACxE,OAAOC,WAAW,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAO,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAC9B,MAAM;IAAEa,aAAa;IAAEC;EAAQ,CAAC,GAAGhB,WAAW,CAAEiB,KAAK,IAAKA,KAAK,CAACF,aAAa,CAAC;EAC9E,MAAMG,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9BK,SAAS,CAAC,MAAM;IACZY,QAAQ,CAACf,mCAAmC,CAAC;MAAEgB,EAAE,EAAEL;IAAO,CAAC,CAAC,CAAC;EACjE,CAAC,EAAE,CAACI,QAAQ,EAAEJ,MAAM,CAAC,CAAC;EAEtB,oBACIH,OAAA,CAACN,eAAe;IAAAe,QAAA,eACZT,OAAA,CAACF,WAAW;MACRO,OAAO,EAAEA,OAAQ;MACjBK,QAAQ,EAAC,iDAA4B;MACrCC,UAAU,EAAC,wCAAuB;MAClCC,QAAQ,EAAER,aAAa,CAACS,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,IAAK;MAAAJ,QAAA,eAElDT,OAAA;QAAKc,SAAS,EAAC,6BAA6B;QAAAL,QAAA,gBAExCT,OAAA;UAAKc,SAAS,EAAC,yCAAyC;UAAAL,QAAA,eACpDT,OAAA,CAACJ,WAAW;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eAGNlB,OAAA;UAAKc,SAAS,EAAC,OAAO;UAAAL,QAAA,eAClBT,OAAA,CAACH,YAAY;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAED,CAAC;AAG1B,CAAC;AAAAhB,EAAA,CAjCKD,mBAAmB;EAAA,QACFV,SAAS,EACOF,WAAW,EAC7BC,WAAW;AAAA;AAAA6B,EAAA,GAH1BlB,mBAAmB;AAmCzB,eAAeA,mBAAmB;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}