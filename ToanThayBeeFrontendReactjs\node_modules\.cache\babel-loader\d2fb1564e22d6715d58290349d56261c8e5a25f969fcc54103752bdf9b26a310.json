{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\Button.jsx\";\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = _ref => {\n  let {\n    onClick,\n    children,\n    type,\n    p,\n    loading,\n    disabled\n  } = _ref;\n  const colorClasses = {\n    primary: \"bg-slate-600 hover:bg-slate-700\",\n    secondary: \"bg-gray-600 hover:bg-gray-700\",\n    danger: \"bg-red-600 hover:bg-red-700\",\n    none: \"bg-transparent hover:bg-gray-100 text-gray-800\",\n    save: \"bg-emerald-500 hover:bg-emerald-700\",\n    commit: \"bg-orange-500 hover:bg-orange-700\"\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onClick,\n    className: \"flex text-sm items-center gap-2 \".concat(p ? p : \"p-1\", \" rounded-[0.25rem] text-white \").concat(colorClasses[type] || \"bg-blue-600 hover:bg-blue-700\", \" transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"),\n    disabled: disabled,\n    children: [loading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      minHeight: \"min-h-0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 17\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 9\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["LoadingSpinner", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_ref", "onClick", "children", "type", "p", "loading", "disabled", "colorClasses", "primary", "secondary", "danger", "none", "save", "commit", "className", "concat", "minHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/Button.jsx"], "sourcesContent": ["import LoadingSpinner from \"../loading/LoadingSpinner\";\r\n\r\nconst Button = ({ onClick, children, type, p, loading, disabled }) => {\r\n    const colorClasses = {\r\n        primary: \"bg-slate-600 hover:bg-slate-700\",\r\n        secondary: \"bg-gray-600 hover:bg-gray-700\",\r\n        danger: \"bg-red-600 hover:bg-red-700\",\r\n        none: \"bg-transparent hover:bg-gray-100 text-gray-800\",\r\n        save: \"bg-emerald-500 hover:bg-emerald-700\",\r\n        commit: \"bg-orange-500 hover:bg-orange-700\",\r\n    };\r\n    return (\r\n        <button\r\n            onClick={onClick}\r\n            className={`flex text-sm items-center gap-2 ${p ? p : \"p-1\"} rounded-[0.25rem] text-white ${colorClasses[type] || \"bg-blue-600 hover:bg-blue-700\"} transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}\r\n            disabled={disabled}\r\n        >\r\n            {loading && (\r\n                <LoadingSpinner\r\n                    minHeight=\"min-h-0\"\r\n                />\r\n            )}\r\n            {children}\r\n        </button>\r\n    );\r\n};\r\n\r\nexport default Button;"], "mappings": ";AAAA,OAAOA,cAAc,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,MAAM,GAAGC,IAAA,IAAuD;EAAA,IAAtD;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,IAAI;IAAEC,CAAC;IAAEC,OAAO;IAAEC;EAAS,CAAC,GAAAN,IAAA;EAC7D,MAAMO,YAAY,GAAG;IACjBC,OAAO,EAAE,iCAAiC;IAC1CC,SAAS,EAAE,+BAA+B;IAC1CC,MAAM,EAAE,6BAA6B;IACrCC,IAAI,EAAE,gDAAgD;IACtDC,IAAI,EAAE,qCAAqC;IAC3CC,MAAM,EAAE;EACZ,CAAC;EACD,oBACIf,OAAA;IACIG,OAAO,EAAEA,OAAQ;IACjBa,SAAS,qCAAAC,MAAA,CAAqCX,CAAC,GAAGA,CAAC,GAAG,KAAK,oCAAAW,MAAA,CAAiCR,YAAY,CAACJ,IAAI,CAAC,IAAI,+BAA+B,qGAAmG;IACpPG,QAAQ,EAAEA,QAAS;IAAAJ,QAAA,GAElBG,OAAO,iBACJP,OAAA,CAACF,cAAc;MACXoB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACJ,EACAlB,QAAQ;EAAA;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEjB,CAAC;AAACC,EAAA,GAvBItB,MAAM;AAyBZ,eAAeA,MAAM;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}