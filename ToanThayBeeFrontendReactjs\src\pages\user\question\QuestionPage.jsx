import React from 'react';
import { useSelector, useDispatch } from "react-redux";
import { fetchPublicQuestionById } from 'src/features/question/questionSlice';
import { useParams } from "react-router-dom";
import { useEffect } from 'react';

const QuestionPage = () => {
    const { questionId } = useParams();
    const dispatch = useDispatch();
    const { question, loading } = useSelector((state) => state.questions);
    
    useEffect(() => {
        dispatch(fetchPublicQuestionById(questionId));
    }, [dispatch, questionId]);
    return (
        <div>
            <h1>Question Page</h1>
        </div>
    );
};

export default QuestionPage;
