import React from "react";
import "katex/dist/katex.min.css";
import { BlockMath, InlineMath } from "react-katex";
import NoTranslate from "../utils/NoTranslate";

const SafeLatexRenderer = ({ text, className = '', style }) => {
    if (text === null || text === undefined) return null;

    // Chuyển \(...\) => $...$, \[...\] => $$...$$
    const formattedText = text
        .replace(/\\\(/g, "$")
        .replace(/\\\)/g, "$")
        .replace(/\\\[/g, "$$")
        .replace(/\\\]/g, "$$");

    // Kiểm tra xem có chứa LaTeX không
    const hasLatex = /\$\$.*?\$\$|\$.*?\$/gs.test(formattedText);
    
    // Nếu không có LaTeX, tr<PERSON> về text thuần để tránh warning
    if (!hasLatex) {
        return (
            <NoTranslate as="span" className={className} style={style}>
                {text}
            </NoTranslate>
        );
    }

    // Cấu hình KaTeX để tắt strict mode và warnings
    const katexOptions = {
        strict: false, // Tắt strict mode
        throwOnError: false, // Không throw error
        errorColor: '#cc0000',
        trust: false,
        macros: {
            "\\RR": "\\mathbb{R}",
            "\\NN": "\\mathbb{N}",
            "\\ZZ": "\\mathbb{Z}",
            "\\QQ": "\\mathbb{Q}",
            "\\CC": "\\mathbb{C}"
        }
    };

    // Regex phân chia nội dung theo LaTeX inline/block
    const parts = formattedText.split(/(\$\$.*?\$\$|\$.*?\$)/gs);

    const elements = parts.map((part, index) => {
        try {
            if (part.startsWith("$$") && part.endsWith("$$")) {
                const mathContent = part.slice(2, -2);
                // Kiểm tra nếu content có ký tự tiếng Việt thì không render LaTeX
                if (/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i.test(mathContent)) {
                    return <span key={`text-${index}`}>{part}</span>;
                }
                return <BlockMath key={`block-${index}`} settings={katexOptions}>{mathContent}</BlockMath>;
            } else if (part.startsWith("$") && part.endsWith("$")) {
                const mathContent = part.slice(1, -1);
                // Kiểm tra nếu content có ký tự tiếng Việt thì không render LaTeX
                if (/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i.test(mathContent)) {
                    return <span key={`text-${index}`}>{part}</span>;
                }
                return <InlineMath key={`inline-${index}`} settings={katexOptions}>{mathContent}</InlineMath>;
            } else {
                return <span key={`text-${index}`}>{part}</span>;
            }
        } catch (error) {
            // Fallback về text thuần nếu có lỗi
            return <span key={`fallback-${index}`}>{part}</span>;
        }
    });

    return (
        <NoTranslate as="div" className={className} style={style}>
            {elements}
        </NoTranslate>
    );
};

export default SafeLatexRenderer;
