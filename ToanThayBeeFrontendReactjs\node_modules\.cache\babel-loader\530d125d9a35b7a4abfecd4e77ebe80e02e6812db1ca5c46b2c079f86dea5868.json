{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\UploadPdf.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { setSuccessMessage, setErrorMessage } from \"../features/state/stateApiSlice\";\nimport { Upload, Trash2, FileText, X } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UploadPdfForm = _ref => {\n  _s();\n  let {\n    id,\n    onSubmit,\n    loading,\n    setPdf = () => {},\n    deleteButton = true,\n    compact = false,\n    className = '',\n    showSubmitButton = true\n  } = _ref;\n  const dispatch = useDispatch();\n  const [pdfFile, setPdfFile] = useState(null);\n  const [isDragging, setIsDragging] = useState(false);\n\n  // Generate unique input id for each instance\n  const inputId = \"pdf-input-\".concat(id || 'default', \"-\").concat(Math.random().toString(36).slice(2, 11));\n  const handleFile = file => {\n    if (file && file.type === \"application/pdf\") {\n      setPdfFile(file);\n    } else {\n      dispatch(setErrorMessage(\"❌ Vui lòng chọn một file PDF hợp lệ.\"));\n    }\n  };\n  useEffect(() => {\n    setPdf(pdfFile);\n  }, [pdfFile, setPdf]);\n  const handleFileChange = e => {\n    handleFile(e.target.files[0]);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(false);\n    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n      handleFile(e.dataTransfer.files[0]);\n      e.dataTransfer.clearData();\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(false);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Gọi hàm onSubmit từ prop và truyền vào dữ liệu cần thiết\n    if (onSubmit) {\n      await onSubmit({\n        id,\n        pdfFile\n      });\n    }\n  };\n  const handleRemoveFile = () => {\n    setPdfFile(null);\n  };\n\n  // Compact mode for space-optimized layouts\n  if (compact) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative \".concat(className),\n      children: [!pdfFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => document.getElementById(inputId).click(),\n        onDrop: handleDrop,\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        className: \"w-full h-full p-4 min-h-[3rem] flex flex-col items-center justify-center cursor-pointer rounded border-2 border-dashed transition-all duration-200 \".concat(isDragging ? \"border-blue-400 bg-blue-50\" : \"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100\"),\n        children: [/*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4 text-gray-400 mb-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-600\",\n          children: \"Ch\\u1ECDn PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative group\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 p-2 bg-white border border-gray-200 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-4 h-4 text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-700 truncate flex-1\",\n            children: pdfFile.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleRemoveFile,\n          className: \"absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        id: inputId,\n        type: \"file\",\n        accept: \"application/pdf\",\n        onChange: handleFileChange,\n        className: \"hidden\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Standard mode\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"space-y-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      onDrop: handleDrop,\n      onDragOver: handleDragOver,\n      onDragLeave: handleDragLeave,\n      onClick: () => document.getElementById(inputId).click(),\n      className: \"flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 \".concat(className || 'w-full min-h-[8rem]', \" \").concat(isDragging ? \"border-blue-400 bg-blue-50\" : \"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100\"),\n      children: [pdfFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative group w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center space-x-3 p-4 bg-white border border-gray-200 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-8 h-8 text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-900 truncate\",\n              children: pdfFile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: [(pdfFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: e => {\n            e.stopPropagation();\n            handleRemoveFile();\n          },\n          className: \"absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md\",\n          children: /*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center text-center space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2 bg-gray-200 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-6 h-6 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"Ch\\u1ECDn file PDF ho\\u1EB7c k\\xE9o th\\u1EA3 v\\xE0o \\u0111\\xE2y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Ch\\u1EC9 h\\u1ED7 tr\\u1EE3 file PDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"mt-3 px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200\",\n          children: \"Ch\\u1ECDn file PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(\"input\", {\n        id: inputId,\n        type: \"file\",\n        accept: \"application/pdf\",\n        onChange: handleFileChange,\n        className: \"hidden\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this), showSubmitButton && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end gap-3\",\n      children: pdfFile ? /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\",\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 37\n          }, this), \"\\u0110ang t\\u1EA3i...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Upload, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 37\n          }, this), \"T\\u1EA3i l\\xEAn\"]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 25\n      }, this) : deleteButton && /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\",\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 37\n          }, this), \"\\u0110ang x\\xF3a...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 37\n          }, this), \"X\\xF3a\"]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 9\n  }, this);\n};\n_s(UploadPdfForm, \"1H/WsLpeEBLQb59BNrbiVzzBLJk=\", false, function () {\n  return [useDispatch];\n});\n_c = UploadPdfForm;\nexport default UploadPdfForm;\nvar _c;\n$RefreshReg$(_c, \"UploadPdfForm\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "setSuccessMessage", "setErrorMessage", "Upload", "Trash2", "FileText", "X", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UploadPdfForm", "_ref", "_s", "id", "onSubmit", "loading", "setPdf", "deleteButton", "compact", "className", "showSubmitButton", "dispatch", "pdfFile", "setPdfFile", "isDragging", "setIsDragging", "inputId", "concat", "Math", "random", "toString", "slice", "handleFile", "file", "type", "handleFileChange", "e", "target", "files", "handleDrop", "preventDefault", "stopPropagation", "dataTransfer", "length", "clearData", "handleDragOver", "handleDragLeave", "handleSubmit", "handleRemoveFile", "children", "onClick", "document", "getElementById", "click", "onDrop", "onDragOver", "onDragLeave", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "accept", "onChange", "size", "toFixed", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/UploadPdf.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { setSuccessMessage, setErrorMessage } from \"../features/state/stateApiSlice\";\r\nimport { Upload, Trash2, FileText, X } from \"lucide-react\";\r\n\r\nconst UploadPdfForm = ({\r\n    id,\r\n    onSubmit,\r\n    loading,\r\n    setPdf = () => { },\r\n    deleteButton = true,\r\n    compact = false,\r\n    className = '',\r\n    showSubmitButton = true\r\n}) => {\r\n    const dispatch = useDispatch();\r\n    const [pdfFile, setPdfFile] = useState(null);\r\n    const [isDragging, setIsDragging] = useState(false);\r\n\r\n    // Generate unique input id for each instance\r\n    const inputId = `pdf-input-${id || 'default'}-${Math.random().toString(36).slice(2, 11)}`;\r\n\r\n    const handleFile = (file) => {\r\n        if (file && file.type === \"application/pdf\") {\r\n            setPdfFile(file);\r\n        } else {\r\n            dispatch(setErrorMessage(\"❌ Vui lòng chọn một file PDF hợp lệ.\"));\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        setPdf(pdfFile);\r\n    }, [pdfFile, setPdf]);\r\n\r\n    const handleFileChange = (e) => {\r\n        handleFile(e.target.files[0]);\r\n    };\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        setIsDragging(false);\r\n        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\r\n            handleFile(e.dataTransfer.files[0]);\r\n            e.dataTransfer.clearData();\r\n        }\r\n    };\r\n\r\n    const handleDragOver = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        setIsDragging(true);\r\n    };\r\n\r\n    const handleDragLeave = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        setIsDragging(false);\r\n    };\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n\r\n        // Gọi hàm onSubmit từ prop và truyền vào dữ liệu cần thiết\r\n        if (onSubmit) {\r\n            await onSubmit({ id, pdfFile });\r\n        }\r\n    };\r\n\r\n    const handleRemoveFile = () => {\r\n        setPdfFile(null);\r\n    };\r\n\r\n    // Compact mode for space-optimized layouts\r\n    if (compact) {\r\n        return (\r\n            <div className={`relative ${className}`}>\r\n                {!pdfFile ? (\r\n                    <div\r\n                        onClick={() => document.getElementById(inputId).click()}\r\n                        onDrop={handleDrop}\r\n                        onDragOver={handleDragOver}\r\n                        onDragLeave={handleDragLeave}\r\n                        className={`w-full h-full p-4 min-h-[3rem] flex flex-col items-center justify-center cursor-pointer rounded border-2 border-dashed transition-all duration-200 ${\r\n                            isDragging\r\n                                ? \"border-blue-400 bg-blue-50\"\r\n                                : \"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100\"\r\n                        }`}\r\n                    >\r\n                        <FileText className=\"w-4 h-4 text-gray-400 mb-1\" />\r\n                        <span className=\"text-xs text-gray-600\">Chọn PDF</span>\r\n                    </div>\r\n                ) : (\r\n                    <div className=\"relative group\">\r\n                        <div className=\"flex items-center space-x-2 p-2 bg-white border border-gray-200 rounded\">\r\n                            <FileText className=\"w-4 h-4 text-red-600\" />\r\n                            <span className=\"text-xs text-gray-700 truncate flex-1\">\r\n                                {pdfFile.name}\r\n                            </span>\r\n                        </div>\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={handleRemoveFile}\r\n                            className=\"absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\r\n                        >\r\n                            <X className=\"w-3 h-3\" />\r\n                        </button>\r\n                    </div>\r\n                )}\r\n                <input\r\n                    id={inputId}\r\n                    type=\"file\"\r\n                    accept=\"application/pdf\"\r\n                    onChange={handleFileChange}\r\n                    className=\"hidden\"\r\n                />\r\n            </div>\r\n        );\r\n    }\r\n\r\n    // Standard mode\r\n    return (\r\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n            <div\r\n                onDrop={handleDrop}\r\n                onDragOver={handleDragOver}\r\n                onDragLeave={handleDragLeave}\r\n                onClick={() => document.getElementById(inputId).click()}\r\n                className={`flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 ${\r\n                    className || 'w-full min-h-[8rem]'\r\n                } ${\r\n                    isDragging\r\n                        ? \"border-blue-400 bg-blue-50\"\r\n                        : \"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100\"\r\n                }`}\r\n            >\r\n                {pdfFile ? (\r\n                    <div className=\"relative group w-full\">\r\n                        <div className=\"flex items-center justify-center space-x-3 p-4 bg-white border border-gray-200 rounded-lg\">\r\n                            <FileText className=\"w-8 h-8 text-red-600\" />\r\n                            <div className=\"flex-1 min-w-0\">\r\n                                <p className=\"text-sm font-medium text-gray-900 truncate\">\r\n                                    {pdfFile.name}\r\n                                </p>\r\n                                <p className=\"text-xs text-gray-500\">\r\n                                    {(pdfFile.size / 1024 / 1024).toFixed(2)} MB\r\n                                </p>\r\n                            </div>\r\n                        </div>\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={(e) => {\r\n                                e.stopPropagation();\r\n                                handleRemoveFile();\r\n                            }}\r\n                            className=\"absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md\"\r\n                        >\r\n                            <Trash2 className=\"w-4 h-4\" />\r\n                        </button>\r\n                    </div>\r\n                ) : (\r\n                    <>\r\n                        <div className=\"flex flex-col items-center text-center space-y-2\">\r\n                            <div className=\"p-2 bg-gray-200 rounded-full\">\r\n                                <FileText className=\"w-6 h-6 text-gray-600\" />\r\n                            </div>\r\n                            <div className=\"space-y-1\">\r\n                                <p className=\"text-sm font-medium text-gray-700\">\r\n                                    Chọn file PDF hoặc kéo thả vào đây\r\n                                </p>\r\n                                <p className=\"text-xs text-gray-500\">\r\n                                    Chỉ hỗ trợ file PDF\r\n                                </p>\r\n                            </div>\r\n                        </div>\r\n                        <button\r\n                            type=\"button\"\r\n                            className=\"mt-3 px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200\"\r\n                        >\r\n                            Chọn file PDF\r\n                        </button>\r\n                    </>\r\n                )}\r\n                <input\r\n                    id={inputId}\r\n                    type=\"file\"\r\n                    accept=\"application/pdf\"\r\n                    onChange={handleFileChange}\r\n                    className=\"hidden\"\r\n                />\r\n            </div>\r\n\r\n            {showSubmitButton && (\r\n                <div className=\"flex justify-end gap-3\">\r\n                    {pdfFile ? (\r\n                        <button\r\n                            type=\"submit\"\r\n                            disabled={loading}\r\n                            className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\r\n                        >\r\n                            {loading ? (\r\n                                <>\r\n                                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n                                    Đang tải...\r\n                                </>\r\n                            ) : (\r\n                                <>\r\n                                    <Upload className=\"w-4 h-4\" />\r\n                                    Tải lên\r\n                                </>\r\n                            )}\r\n                        </button>\r\n                    ) : (deleteButton && (\r\n                        <button\r\n                            type=\"submit\"\r\n                            disabled={loading}\r\n                            className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\r\n                        >\r\n                            {loading ? (\r\n                                <>\r\n                                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n                                    Đang xóa...\r\n                                </>\r\n                            ) : (\r\n                                <>\r\n                                    <Trash2 className=\"w-4 h-4\" />\r\n                                    Xóa\r\n                                </>\r\n                            )}\r\n                        </button>\r\n                    ))}\r\n                </div>\r\n            )}\r\n        </form>\r\n    );\r\n};\r\n\r\nexport default UploadPdfForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,iCAAiC;AACpF,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,CAAC,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,aAAa,GAAGC,IAAA,IAShB;EAAAC,EAAA;EAAA,IATiB;IACnBC,EAAE;IACFC,QAAQ;IACRC,OAAO;IACPC,MAAM,GAAGA,CAAA,KAAM,CAAE,CAAC;IAClBC,YAAY,GAAG,IAAI;IACnBC,OAAO,GAAG,KAAK;IACfC,SAAS,GAAG,EAAE;IACdC,gBAAgB,GAAG;EACvB,CAAC,GAAAT,IAAA;EACG,MAAMU,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM4B,OAAO,gBAAAC,MAAA,CAAgBd,EAAE,IAAI,SAAS,OAAAc,MAAA,CAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAE;EAEzF,MAAMC,UAAU,GAAIC,IAAI,IAAK;IACzB,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,EAAE;MACzCX,UAAU,CAACU,IAAI,CAAC;IACpB,CAAC,MAAM;MACHZ,QAAQ,CAACpB,eAAe,CAAC,sCAAsC,CAAC,CAAC;IACrE;EACJ,CAAC;EAEDJ,SAAS,CAAC,MAAM;IACZmB,MAAM,CAACM,OAAO,CAAC;EACnB,CAAC,EAAE,CAACA,OAAO,EAAEN,MAAM,CAAC,CAAC;EAErB,MAAMmB,gBAAgB,GAAIC,CAAC,IAAK;IAC5BJ,UAAU,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC;EAED,MAAMC,UAAU,GAAIH,CAAC,IAAK;IACtBA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBJ,CAAC,CAACK,eAAe,CAAC,CAAC;IACnBhB,aAAa,CAAC,KAAK,CAAC;IACpB,IAAIW,CAAC,CAACM,YAAY,CAACJ,KAAK,IAAIF,CAAC,CAACM,YAAY,CAACJ,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;MACzDX,UAAU,CAACI,CAAC,CAACM,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;MACnCF,CAAC,CAACM,YAAY,CAACE,SAAS,CAAC,CAAC;IAC9B;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIT,CAAC,IAAK;IAC1BA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBJ,CAAC,CAACK,eAAe,CAAC,CAAC;IACnBhB,aAAa,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqB,eAAe,GAAIV,CAAC,IAAK;IAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBJ,CAAC,CAACK,eAAe,CAAC,CAAC;IACnBhB,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMsB,YAAY,GAAG,MAAOX,CAAC,IAAK;IAC9BA,CAAC,CAACI,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI1B,QAAQ,EAAE;MACV,MAAMA,QAAQ,CAAC;QAAED,EAAE;QAAES;MAAQ,CAAC,CAAC;IACnC;EACJ,CAAC;EAED,MAAM0B,gBAAgB,GAAGA,CAAA,KAAM;IAC3BzB,UAAU,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,IAAIL,OAAO,EAAE;IACT,oBACIX,OAAA;MAAKY,SAAS,cAAAQ,MAAA,CAAcR,SAAS,CAAG;MAAA8B,QAAA,GACnC,CAAC3B,OAAO,gBACLf,OAAA;QACI2C,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC1B,OAAO,CAAC,CAAC2B,KAAK,CAAC,CAAE;QACxDC,MAAM,EAAEf,UAAW;QACnBgB,UAAU,EAAEV,cAAe;QAC3BW,WAAW,EAAEV,eAAgB;QAC7B3B,SAAS,wJAAAQ,MAAA,CACLH,UAAU,GACJ,4BAA4B,GAC5B,oEAAoE,CAC3E;QAAAyB,QAAA,gBAEH1C,OAAA,CAACH,QAAQ;UAACe,SAAS,EAAC;QAA4B;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDrD,OAAA;UAAMY,SAAS,EAAC,uBAAuB;UAAA8B,QAAA,EAAC;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,gBAENrD,OAAA;QAAKY,SAAS,EAAC,gBAAgB;QAAA8B,QAAA,gBAC3B1C,OAAA;UAAKY,SAAS,EAAC,yEAAyE;UAAA8B,QAAA,gBACpF1C,OAAA,CAACH,QAAQ;YAACe,SAAS,EAAC;UAAsB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CrD,OAAA;YAAMY,SAAS,EAAC,uCAAuC;YAAA8B,QAAA,EAClD3B,OAAO,CAACuC;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNrD,OAAA;UACI2B,IAAI,EAAC,QAAQ;UACbgB,OAAO,EAAEF,gBAAiB;UAC1B7B,SAAS,EAAC,iIAAiI;UAAA8B,QAAA,eAE3I1C,OAAA,CAACF,CAAC;YAACc,SAAS,EAAC;UAAS;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eACDrD,OAAA;QACIM,EAAE,EAAEa,OAAQ;QACZQ,IAAI,EAAC,MAAM;QACX4B,MAAM,EAAC,iBAAiB;QACxBC,QAAQ,EAAE5B,gBAAiB;QAC3BhB,SAAS,EAAC;MAAQ;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEd;;EAEA;EACA,oBACIrD,OAAA;IAAMO,QAAQ,EAAEiC,YAAa;IAAC5B,SAAS,EAAC,WAAW;IAAA8B,QAAA,gBAC/C1C,OAAA;MACI+C,MAAM,EAAEf,UAAW;MACnBgB,UAAU,EAAEV,cAAe;MAC3BW,WAAW,EAAEV,eAAgB;MAC7BI,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC1B,OAAO,CAAC,CAAC2B,KAAK,CAAC,CAAE;MACxDlC,SAAS,gIAAAQ,MAAA,CACLR,SAAS,IAAI,qBAAqB,OAAAQ,MAAA,CAElCH,UAAU,GACJ,4BAA4B,GAC5B,oEAAoE,CAC3E;MAAAyB,QAAA,GAEF3B,OAAO,gBACJf,OAAA;QAAKY,SAAS,EAAC,uBAAuB;QAAA8B,QAAA,gBAClC1C,OAAA;UAAKY,SAAS,EAAC,2FAA2F;UAAA8B,QAAA,gBACtG1C,OAAA,CAACH,QAAQ;YAACe,SAAS,EAAC;UAAsB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CrD,OAAA;YAAKY,SAAS,EAAC,gBAAgB;YAAA8B,QAAA,gBAC3B1C,OAAA;cAAGY,SAAS,EAAC,4CAA4C;cAAA8B,QAAA,EACpD3B,OAAO,CAACuC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACJrD,OAAA;cAAGY,SAAS,EAAC,uBAAuB;cAAA8B,QAAA,GAC/B,CAAC3B,OAAO,CAAC0C,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC7C;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNrD,OAAA;UACI2B,IAAI,EAAC,QAAQ;UACbgB,OAAO,EAAGd,CAAC,IAAK;YACZA,CAAC,CAACK,eAAe,CAAC,CAAC;YACnBO,gBAAgB,CAAC,CAAC;UACtB,CAAE;UACF7B,SAAS,EAAC,6IAA6I;UAAA8B,QAAA,eAEvJ1C,OAAA,CAACJ,MAAM;YAACgB,SAAS,EAAC;UAAS;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,gBAENrD,OAAA,CAAAE,SAAA;QAAAwC,QAAA,gBACI1C,OAAA;UAAKY,SAAS,EAAC,kDAAkD;UAAA8B,QAAA,gBAC7D1C,OAAA;YAAKY,SAAS,EAAC,8BAA8B;YAAA8B,QAAA,eACzC1C,OAAA,CAACH,QAAQ;cAACe,SAAS,EAAC;YAAuB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNrD,OAAA;YAAKY,SAAS,EAAC,WAAW;YAAA8B,QAAA,gBACtB1C,OAAA;cAAGY,SAAS,EAAC,mCAAmC;cAAA8B,QAAA,EAAC;YAEjD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJrD,OAAA;cAAGY,SAAS,EAAC,uBAAuB;cAAA8B,QAAA,EAAC;YAErC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNrD,OAAA;UACI2B,IAAI,EAAC,QAAQ;UACbf,SAAS,EAAC,mKAAmK;UAAA8B,QAAA,EAChL;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACX,CACL,eACDrD,OAAA;QACIM,EAAE,EAAEa,OAAQ;QACZQ,IAAI,EAAC,MAAM;QACX4B,MAAM,EAAC,iBAAiB;QACxBC,QAAQ,EAAE5B,gBAAiB;QAC3BhB,SAAS,EAAC;MAAQ;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAELxC,gBAAgB,iBACbb,OAAA;MAAKY,SAAS,EAAC,wBAAwB;MAAA8B,QAAA,EAClC3B,OAAO,gBACJf,OAAA;QACI2B,IAAI,EAAC,QAAQ;QACbgC,QAAQ,EAAEnD,OAAQ;QAClBI,SAAS,EAAC,0KAA0K;QAAA8B,QAAA,EAEnLlC,OAAO,gBACJR,OAAA,CAAAE,SAAA;UAAAwC,QAAA,gBACI1C,OAAA;YAAKY,SAAS,EAAC;UAA2D;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,yBAErF;QAAA,eAAE,CAAC,gBAEHrD,OAAA,CAAAE,SAAA;UAAAwC,QAAA,gBACI1C,OAAA,CAACL,MAAM;YAACiB,SAAS,EAAC;UAAS;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAElC;QAAA,eAAE;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,GACR3C,YAAY,iBACbV,OAAA;QACI2B,IAAI,EAAC,QAAQ;QACbgC,QAAQ,EAAEnD,OAAQ;QAClBI,SAAS,EAAC,wKAAwK;QAAA8B,QAAA,EAEjLlC,OAAO,gBACJR,OAAA,CAAAE,SAAA;UAAAwC,QAAA,gBACI1C,OAAA;YAAKY,SAAS,EAAC;UAA2D;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,uBAErF;QAAA,eAAE,CAAC,gBAEHrD,OAAA,CAAAE,SAAA;UAAAwC,QAAA,gBACI1C,OAAA,CAACJ,MAAM;YAACgB,SAAS,EAAC;UAAS;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAElC;QAAA,eAAE;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IACV;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEf,CAAC;AAAChD,EAAA,CAtOIF,aAAa;EAAA,QAUEX,WAAW;AAAA;AAAAoE,EAAA,GAV1BzD,aAAa;AAwOnB,eAAeA,aAAa;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}