{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\QuestionContent.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { selectQuestion } from \"src/features/examAI/examAISlice\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\nimport { setQuestionsEdited } from \"src/features/examAI/examAISlice\";\nimport { Trash2 } from \"lucide-react\";\nimport ImageDropZone from \"./ImageDropZone\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const QuestionContent = _ref => {\n  _s();\n  var _codes$chapter, _codes$chapter$find;\n  let {\n    question,\n    index\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    selectedQuestion,\n    isAddImage\n  } = useSelector(state => state.examAI);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\\n            \".concat((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.id) === question.id && !isAddImage ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\", \"\\n        \"),\n    onClick: () => {\n      if (isAddImage) return;\n      dispatch(selectQuestion(question));\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      div: true,\n      className: \"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-gray-700\",\n        children: [\"C\\xE2u \", index + 1]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\u0110\\u1ED9 kh\\xF3: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: question.difficulty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Ch\\u01B0\\u01A1ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: ((_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === question.chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || \"Chưa xác định\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this), isAddImage ? /*#__PURE__*/_jsxDEV(ImageDropZone, {\n      imageUrl: question.imageUrl,\n      onImageDrop: newUrl => {\n        dispatch(setQuestionsEdited({\n          ...question,\n          imageUrl: newUrl\n        }));\n      },\n      onImageRemove: () => {\n        dispatch(setQuestionsEdited({\n          ...question,\n          imageUrl: null\n        }));\n      },\n      content: question.content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 21\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-base text-gray-800 leading-relaxed\",\n      children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        className: \"text-gray-800\",\n        text: question.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 21\n    }, this), /*#__PURE__*/_jsxDEV(SortableStatementsContainer, {\n      question: question\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\",\n      children: isAddImage ? /*#__PURE__*/_jsxDEV(ImageDropZone, {\n        imageUrl: question.solutionImageUrl,\n        onImageDrop: newUrl => {\n          dispatch(setQuestionsEdited({\n            ...question,\n            solutionImageUrl: newUrl\n          }));\n        },\n        onImageRemove: () => {\n          dispatch(setQuestionsEdited({\n            ...question,\n            solutionImageUrl: null\n          }));\n        },\n        content: question.solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 29\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold\",\n          children: \"L\\u1EDDi gi\\u1EA3i:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 33\n        }, this), \" \", /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n          content: question.solution\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this), question.solutionImageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 relative group w-fit\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: question.solutionImageUrl,\n        alt: \"statement\",\n        className: \"max-h-32 object-contain rounded-md border border-gray-200 transition-all group-hover:brightness-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          dispatch(setQuestionsEdited({\n            ...question,\n            solutionImageUrl: null\n          }));\n        },\n        className: \"absolute inset-0 flex items-center justify-center bg-transparent text-gray-100 hover:text-white transition-opacity opacity-0 group-hover:opacity-100\",\n        title: \"X\\xF3a \\u1EA3nh\",\n        children: /*#__PURE__*/_jsxDEV(Trash2, {\n          className: \"w-8 h-8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 33\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 29\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 25\n    }, this)]\n  }, question.id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionContent, \"MTz42/6xoRQRjp3Dnj9r7syqXC4=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = QuestionContent;\nexport default QuestionContent;\nvar _c;\n$RefreshReg$(_c, \"QuestionContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "selectQuestion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "SortableStatementsContainer", "setQuestionsEdited", "Trash2", "ImageDropZone", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuestionContent", "_ref", "_s", "_codes$chapter", "_codes$chapter$find", "question", "index", "dispatch", "selectedQuestion", "isAddImage", "state", "examAI", "codes", "className", "concat", "id", "onClick", "children", "div", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "difficulty", "find", "c", "code", "chapter", "description", "imageUrl", "onImageDrop", "newUrl", "onImageRemove", "content", "text", "solutionImageUrl", "solution", "src", "alt", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/QuestionContent.jsx"], "sourcesContent": ["\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { selectQuestion } from \"src/features/examAI/examAISlice\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\r\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\r\nimport { setQuestionsEdited } from \"src/features/examAI/examAISlice\";\r\nimport { Trash2 } from \"lucide-react\";\r\nimport ImageDropZone from \"./ImageDropZone\";\r\n\r\nexport const QuestionContent = ({ question, index }) => {\r\n    const dispatch = useDispatch();\r\n    const { selectedQuestion, isAddImage } = useSelector((state) => state.examAI);\r\n\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    return (\r\n        <div\r\n            key={question.id}\r\n            className={`\r\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\r\n            ${selectedQuestion?.id === question.id && !isAddImage\r\n                    ? \"bg-blue-50 border-blue-500\"\r\n                    : \"bg-white hover:bg-gray-50 border-gray-300\"\r\n                }\r\n        `}\r\n            onClick={() => {\r\n                if (isAddImage) return\r\n                dispatch(selectQuestion(question))\r\n            }}\r\n        >\r\n            {/* Thông tin câu hỏi */}\r\n            <div div className=\"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\" >\r\n                <span className=\"font-medium text-gray-700\">Câu {index + 1}</span>\r\n                <span>Độ khó: <span className=\"text-gray-700\">{question.difficulty}</span></span>\r\n                <span>\r\n                    Chương:{\" \"}\r\n                    <span className=\"text-gray-700\">\r\n                        {codes[\"chapter\"]?.find(c => c.code === question.chapter)?.description || \"Chưa xác định\"}\r\n                    </span>\r\n                </span>\r\n            </div>\r\n\r\n            {/* Nội dung câu hỏi */}\r\n\r\n            {\r\n                isAddImage ? (\r\n                    <ImageDropZone\r\n                        imageUrl={question.imageUrl}\r\n                        onImageDrop={(newUrl) => {\r\n                            dispatch(\r\n                                setQuestionsEdited({\r\n                                    ...question,\r\n                                    imageUrl: newUrl\r\n                                })\r\n                            );\r\n                        }}\r\n                        onImageRemove={() => {\r\n                            dispatch(\r\n                                setQuestionsEdited({\r\n                                    ...question,\r\n                                    imageUrl: null\r\n                                })\r\n                            );\r\n                        }}\r\n                        content={question.content}\r\n                    />\r\n                ) : (\r\n                    <div className=\"text-base text-gray-800 leading-relaxed\">\r\n                        <LatexRenderer className=\"text-gray-800\" text={question.content} />\r\n                    </div>\r\n                )\r\n            }\r\n\r\n\r\n            <SortableStatementsContainer question={question} />\r\n            {/* Statement: A, B, C,... */}\r\n\r\n            {/* Lời giải (nếu có) */}\r\n            <div className=\"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\">\r\n\r\n                {\r\n                    isAddImage ? (\r\n                            <ImageDropZone\r\n                                imageUrl={question.solutionImageUrl}\r\n                                onImageDrop={(newUrl) => {\r\n                                    dispatch(\r\n                                        setQuestionsEdited({\r\n                                            ...question,\r\n                                            solutionImageUrl: newUrl\r\n                                        })\r\n                                    );\r\n                                }}\r\n                                onImageRemove={() => {\r\n                                    dispatch(\r\n                                        setQuestionsEdited({\r\n                                            ...question,\r\n                                            solutionImageUrl: null\r\n                                        })\r\n                                    );\r\n                                }}\r\n                                content={question.solution}\r\n                            />\r\n                            ) : (\r\n                            <>\r\n                                <span className=\"font-semibold\">Lời giải:</span>{\" \"}\r\n                                <MarkdownPreviewWithMath content={question.solution} />\r\n                            </>\r\n\r\n\r\n                            )\r\n            }\r\n                        </div>\r\n            {\r\n                    question.solutionImageUrl && (\r\n                        <div className=\"mt-2 relative group w-fit\">\r\n                            {/* Hình ảnh */}\r\n                            <img\r\n                                src={question.solutionImageUrl}\r\n                                alt=\"statement\"\r\n                                className=\"max-h-32 object-contain rounded-md border border-gray-200 transition-all group-hover:brightness-50\"\r\n                            />\r\n\r\n                            {/* Nút xoá */}\r\n                            <button\r\n                                onClick={() => {\r\n                                    dispatch(\r\n                                        setQuestionsEdited({\r\n                                            ...question,\r\n                                            solutionImageUrl: null\r\n                                        })\r\n                                    );\r\n                                }}\r\n                                className=\"absolute inset-0 flex items-center justify-center bg-transparent text-gray-100 hover:text-white transition-opacity opacity-0 group-hover:opacity-100\"\r\n                                title=\"Xóa ảnh\"\r\n                            >\r\n                                <Trash2 className=\"w-8 h-8\" />\r\n                            </button>\r\n                        </div>\r\n                    )\r\n                }\r\n            </div >\r\n            );\r\n}\r\n\r\n            export default QuestionContent;"], "mappings": ";;AACA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,OAAO,MAAMC,eAAe,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,mBAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAL,IAAA;EAC/C,MAAMM,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoB,gBAAgB;IAAEC;EAAW,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAE7E,MAAM;IAAEC;EAAM,CAAC,GAAGzB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,oBACIf,OAAA;IAEIgB,SAAS,oGAAAC,MAAA,CAEP,CAAAN,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEO,EAAE,MAAKV,QAAQ,CAACU,EAAE,IAAI,CAACN,UAAU,GAC3C,4BAA4B,GAC5B,2CAA2C,eAEvD;IACEO,OAAO,EAAEA,CAAA,KAAM;MACX,IAAIP,UAAU,EAAE;MAChBF,QAAQ,CAAClB,cAAc,CAACgB,QAAQ,CAAC,CAAC;IACtC,CAAE;IAAAY,QAAA,gBAGFpB,OAAA;MAAKqB,GAAG;MAACL,SAAS,EAAC,mEAAmE;MAAAI,QAAA,gBAClFpB,OAAA;QAAMgB,SAAS,EAAC,2BAA2B;QAAAI,QAAA,GAAC,SAAI,EAACX,KAAK,GAAG,CAAC;MAAA;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEzB,OAAA;QAAAoB,QAAA,GAAM,uBAAQ,eAAApB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAI,QAAA,EAAEZ,QAAQ,CAACkB;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjFzB,OAAA;QAAAoB,QAAA,GAAM,mBACK,EAAC,GAAG,eACXpB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAI,QAAA,EAC1B,EAAAd,cAAA,GAAAS,KAAK,CAAC,SAAS,CAAC,cAAAT,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKrB,QAAQ,CAACsB,OAAO,CAAC,cAAAvB,mBAAA,uBAAxDA,mBAAA,CAA0DwB,WAAW,KAAI;QAAe;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAKFb,UAAU,gBACNZ,OAAA,CAACF,aAAa;MACVkC,QAAQ,EAAExB,QAAQ,CAACwB,QAAS;MAC5BC,WAAW,EAAGC,MAAM,IAAK;QACrBxB,QAAQ,CACJd,kBAAkB,CAAC;UACf,GAAGY,QAAQ;UACXwB,QAAQ,EAAEE;QACd,CAAC,CACL,CAAC;MACL,CAAE;MACFC,aAAa,EAAEA,CAAA,KAAM;QACjBzB,QAAQ,CACJd,kBAAkB,CAAC;UACf,GAAGY,QAAQ;UACXwB,QAAQ,EAAE;QACd,CAAC,CACL,CAAC;MACL,CAAE;MACFI,OAAO,EAAE5B,QAAQ,CAAC4B;IAAQ;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,gBAEFzB,OAAA;MAAKgB,SAAS,EAAC,yCAAyC;MAAAI,QAAA,eACpDpB,OAAA,CAACP,aAAa;QAACuB,SAAS,EAAC,eAAe;QAACqB,IAAI,EAAE7B,QAAQ,CAAC4B;MAAQ;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CACR,eAILzB,OAAA,CAACL,2BAA2B;MAACa,QAAQ,EAAEA;IAAS;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAInDzB,OAAA;MAAKgB,SAAS,EAAC,uFAAuF;MAAAI,QAAA,EAG9FR,UAAU,gBACFZ,OAAA,CAACF,aAAa;QACVkC,QAAQ,EAAExB,QAAQ,CAAC8B,gBAAiB;QACpCL,WAAW,EAAGC,MAAM,IAAK;UACrBxB,QAAQ,CACJd,kBAAkB,CAAC;YACf,GAAGY,QAAQ;YACX8B,gBAAgB,EAAEJ;UACtB,CAAC,CACL,CAAC;QACL,CAAE;QACFC,aAAa,EAAEA,CAAA,KAAM;UACjBzB,QAAQ,CACJd,kBAAkB,CAAC;YACf,GAAGY,QAAQ;YACX8B,gBAAgB,EAAE;UACtB,CAAC,CACL,CAAC;QACL,CAAE;QACFF,OAAO,EAAE5B,QAAQ,CAAC+B;MAAS;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,gBAEFzB,OAAA,CAAAE,SAAA;QAAAkB,QAAA,gBACIpB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAI,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAC,GAAG,eACpDzB,OAAA,CAACN,uBAAuB;UAAC0C,OAAO,EAAE5B,QAAQ,CAAC+B;QAAS;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eACzD;IAGD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEA,CAAC,EAEVjB,QAAQ,CAAC8B,gBAAgB,iBACrBtC,OAAA;MAAKgB,SAAS,EAAC,2BAA2B;MAAAI,QAAA,gBAEtCpB,OAAA;QACIwC,GAAG,EAAEhC,QAAQ,CAAC8B,gBAAiB;QAC/BG,GAAG,EAAC,WAAW;QACfzB,SAAS,EAAC;MAAoG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CAAC,eAGFzB,OAAA;QACImB,OAAO,EAAEA,CAAA,KAAM;UACXT,QAAQ,CACJd,kBAAkB,CAAC;YACf,GAAGY,QAAQ;YACX8B,gBAAgB,EAAE;UACtB,CAAC,CACL,CAAC;QACL,CAAE;QACFtB,SAAS,EAAC,sJAAsJ;QAChK0B,KAAK,EAAC,iBAAS;QAAAtB,QAAA,eAEfpB,OAAA,CAACH,MAAM;UAACmB,SAAS,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR;EAAA,GAzHJjB,QAAQ,CAACU,EAAE;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA2HV,CAAC;AAEnB,CAAC;AAAApB,EAAA,CArIYF,eAAe;EAAA,QACPZ,WAAW,EACaD,WAAW,EAElCA,WAAW;AAAA;AAAAqD,EAAA,GAJpBxC,eAAe;AAuIhB,eAAeA,eAAe;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}