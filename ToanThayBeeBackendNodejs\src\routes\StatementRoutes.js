import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as StatementController from '../controllers/StatementController.js'
import uploadGoogleImageMiddleware from '../middlewares/imageGoogleUpload.js'

const router = express.Router()

router.get('/v1/statement/question/:questionId',
    requireRoles(Roles.JustClassManagement),
    asyncHandler(StatementController.getStatementByQuestionId)
)
router.get('/v1/statement/:id',
    requireRoles(Roles.JustClassManagement),
    asyncHandler(StatementController.getStatementById)
)
router.post('/v1/statement',
    requireRoles(Roles.JustClassManagement),
    async<PERSON>and<PERSON>(StatementController.postStatement)
)
router.put('/v1/statement/:id',
    requireRoles(Roles.JustClassManagement),
    async<PERSON>and<PERSON>(StatementController.putStatement)
)
router.delete('/v1/statement/:id',
    requireRoles(Roles.JustClassManagement),
    asyncHandler(StatementController.deleteStatement)
)

router.put('/v1/admin/statement/:id/image',
    requireRoles(Roles.JustClassManagement),
    uploadGoogleImageMiddleware.single('statementImage'),
    asyncHandler(StatementController.putStatementImage)
)

export default router