import { useState, useEffect } from "react";
import { Square, SquareCheck } from "lucide-react";


export const AuthCheckbox = () => {
    // ✅ Thêm state để theo dõi trạng thái
    const [isChecked, setIsChecked] = useState(localStorage.getItem("rememberMe") === "true");

    // ✅ Cập nhật `isChecked` khi component mount
    useEffect(() => {
        setIsChecked(localStorage.getItem("rememberMe") === "true");
    }, []);

    // ✅ Khi click vào checkbox, cập nhật state & localStorage
    const handleClick = () => {
        const newChecked = !isChecked;
        setIsChecked(newChecked);
        localStorage.setItem("rememberMe", newChecked.toString());
    };

    return (
        <div
            className="w-[1.25rem] h-[1.25rem] cursor-pointer select-none flex items-center justify-center"
            onClick={handleClick}
        >
            {isChecked ? (
                <SquareCheck className="w-full h-full text-sky-600 transition-transform duration-200 active:scale-90" />
            ) : (
                <Square className="w-full h-full text-gray-400 transition-transform duration-200 active:scale-90" />
            )}
        </div>
    );
};
