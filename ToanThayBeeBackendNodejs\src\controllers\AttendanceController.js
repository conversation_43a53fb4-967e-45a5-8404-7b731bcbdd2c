import * as AttendanceService from '../services/attendance.service.js';
import { findBTVN } from '../services/lesson.service.js';
/**
 * [GET] /api/attendances
 * Query: ?search=&page=&limit=&sortOrder=
 */
export const getAllAttendances = async (req, res) => {
    try {
        const { lessonId, status, tuition } = req.query;
        const { search = '', page = 1, limit = 10, sortOrder = 'DESC' } = req.query;
        const { month } = req.query;
        const btvn = await findBTVN(lessonId);
        // console.log(lessonId)
        const result = await AttendanceService.getAllAttendances(lessonId, {
            search,
            status,
            isPaid: tuition,
            page: parseInt(page),
            limit: parseInt(limit),
            sortOrder: sortOrder.toUpperCase()
        }, btvn, month);

        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({ message: error.message });
    }
};

/**
 * [POST] /api/attendances
 * Body: { userId, lessonId, status }
 */
export const createAttendance = async (req, res) => {
    try {
        const { userId, lessonId, classId, status, note } = req.body;

        const attendance = await AttendanceService.createAttendance(userId, lessonId, classId, status, note);

        return res.status(201).json({
            message: 'Tạo điểm danh thành công',
            data: attendance
        });
    } catch (error) {
        return res.status(400).json({ message: error.message });
    }
};

/**
 * [POST] /api/attendances/bulk
 * Body: { classId, lessonId }
 */
export const createAllAttendanceInClass = async (req, res) => {
    try {
        const { classId, lessonId } = req.body;

        const result = await AttendanceService.createAllAttendanceInClass(classId, lessonId);

        return res.status(201).json({
            message: `Tạo điểm danh cho ${result.length} học sinh thành công`,
            data: result,
            count: result.length
        });
    } catch (error) {
        return res.status(400).json({ message: error.message });
    }
};

/**
 * [PUT] /api/attendances/:id
 * Body: { status, note }
 */
export const updateAttendance = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        const updated = await AttendanceService.updateAttendance(id, updateData);

        return res.status(200).json({
            message: 'Cập nhật điểm danh thành công',
            data: updated
        });
    } catch (error) {
        return res.status(400).json({ message: error.message });
    }
};

/**
 * [DELETE] /api/attendances/:id
 */
export const deleteAttendance = async (req, res) => {
    try {
        const { id } = req.params;

        const deletedId = await AttendanceService.deleteAttendance(id);

        return res.status(200).json({
            message: 'Xóa điểm danh thành công',
            id: deletedId
        });
    } catch (error) {
        return res.status(400).json({ message: error.message });
    }
};

/**
 * [GET] /api/attendances/user/:userId
 * Get all attendances for a user grouped by month
 * Query: ?year=2024&month=1 (optional filters)
 */
export const getUserAttendances = async (req, res) => {
    try {
        const { id } = req.user;
        const { year, month } = req.query;

        const result = await AttendanceService.getUserAttendances(id, { year, month });

        return res.status(200).json({
            message: 'Lấy danh sách điểm danh thành công',
            data: result
        });
    } catch (error) {
        return res.status(500).json({ message: error.message });
    }
};

export const getUserAttendancesByAdmin = async (req, res) => {
    try {
        const { userId } = req.params;
        const { year, month } = req.query;

        const result = await AttendanceService.getUserAttendances(userId, { year, month });

        // console.log('result', result);

        return res.status(200).json({
            message: 'Lấy danh sách điểm danh thành công',
            data: result
        });
    } catch (error) {
        return res.status(500).json({ message: error.message });
    }
}

/**
 * [GET] /api/attendances/statistics/lesson/:lessonId
 * Get attendance statistics for a specific lesson
 */
export const getLessonAttendanceStatistics = async (req, res) => {
    try {
        const { lessonId } = req.params;

        const result = await AttendanceService.getLessonAttendanceStatistics(lessonId);

        return res.status(200).json({
            message: 'Lấy thống kê điểm danh buổi học thành công',
            data: result
        });
    } catch (error) {
        return res.status(500).json({ message: error.message });
    }
};

/**
 * [GET] /api/attendances/statistics/class/:classId
 * Get attendance statistics for a specific class
 * Query: ?startDate=2024-01-01&endDate=2024-12-31 (optional filters)
 */
export const getClassAttendanceStatistics = async (req, res) => {
    try {
        const { classId } = req.params;
        const { startDate, endDate } = req.query;

        const result = await AttendanceService.getClassAttendanceStatistics(classId, { startDate, endDate });

        return res.status(200).json({
            message: 'Lấy thống kê điểm danh lớp học thành công',
            data: result
        });
    } catch (error) {
        return res.status(500).json({ message: error.message });
    }
};

/**
 * [GET] /api/attendances/statistics/overall
 * Get overall attendance statistics
 * Query: ?startDate=2024-01-01&endDate=2024-12-31&classId=1 (optional filters)
 */
export const getOverallAttendanceStatistics = async (req, res) => {
    try {
        const { startDate, endDate, classId } = req.query;

        const result = await AttendanceService.getOverallAttendanceStatistics({ startDate, endDate, classId });

        return res.status(200).json({
            message: 'Lấy thống kê điểm danh tổng quan thành công',
            data: result
        });
    } catch (error) {
        return res.status(500).json({ message: error.message });
    }
};

/**
 * [PUT] /api/attendances/bulk-update/:lessonId
 * Bulk update attendance status for multiple students in a lesson
 * Body: { updates: [{ userId, status, note? }] }
 */
export const bulkUpdateAttendanceStatus = async (req, res) => {
    try {
        const { lessonId } = req.params;
        const { updates } = req.body;

        const result = await AttendanceService.bulkUpdateAttendanceStatus(lessonId, updates);

        return res.status(200).json({
            message: `Cập nhật hàng loạt thành công. ${result.successCount} bản ghi được xử lý${result.errorCount > 0 ? `, ${result.errorCount} lỗi xảy ra` : ''}`,
            data: result
        });
    } catch (error) {
        return res.status(400).json({ message: error.message });
    }
};

/**
 * [PUT] /api/attendances/mark-all/:lessonId
 * Mark all students in a lesson with the same status
 * Body: { status, note? }
 */
export const markAllAttendanceStatus = async (req, res) => {
    try {
        const { lessonId } = req.params;
        const { status, note } = req.body;
        console.log('lessonId', lessonId, 'status', status, 'note', note);
        const result = await AttendanceService.markAllAttendanceStatus(lessonId, status, note);

        return res.status(200).json({
            message: `Đánh dấu tất cả học sinh là ${status} thành công. ${result.successCount} bản ghi được xử lý${result.errorCount > 0 ? `, ${result.errorCount} lỗi xảy ra` : ''}`,
            data: result
        });
    } catch (error) {
        return res.status(400).json({ message: error.message });
    }
};


