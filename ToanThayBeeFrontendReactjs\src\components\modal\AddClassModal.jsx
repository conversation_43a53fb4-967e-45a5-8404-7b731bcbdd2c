import { useDispatch, useSelector } from "react-redux";
import LoadingSpinner from "../loading/LoadingSpinner";
import { postClass } from "../../features/class/classSlice";
import { useState, useEffect } from "react";
import { fetchCodesByType } from "../../features/code/codeSlice";
import DropMenuBarAdmin from "../dropMenu/OptionBarAdmin";
import { fetchClasses } from "../../features/class/classSlice";
import { setSuccessMessage } from "../../features/state/stateApiSlice";

// Icons components
const InfoIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

const ClockIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

const CalendarIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
);

const AcademicCapIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
    </svg>
);

const EyeIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
    </svg>
);

const AddClassModal = ({ onClose }) => {
    const dispatch = useDispatch();
    const { loading } = useSelector(state => state.states);
    const { codes } = useSelector(state => state.codes);
    const [validationError, setValidationError] = useState("");
    const [classData, setClassData] = useState({
        name: "",
        grade: null,
        description: "",
        dayOfWeek1: null,
        dayOfWeek2: null,
        status: "LHD",
        academicYear: "",
        startTime1: null,
        endTime1: null,
        startTime2: null,
        endTime2: null,
        public: true
    })
    const { search, currentPage, limit, sortOrder } = useSelector(state => state.filter);

    useEffect(() => {
        dispatch(fetchCodesByType(["dow", "year", "grade"]))
    }, [dispatch])

    const handleSubmit = (e) => {
        e.preventDefault()

        // Validate session 1 times
        if (classData.startTime1 && classData.endTime1) {
            if (classData.startTime1 >= classData.endTime1) {
                setValidationError("Giờ bắt đầu buổi 1 phải nhỏ hơn giờ kết thúc buổi 1");
                alert("Giờ bắt đầu buổi 1 phải nhỏ hơn giờ kết thúc buổi 1");
                return;
            }
        }

        // Validate session 2 times
        if (classData.startTime2 && classData.endTime2) {
            if (classData.startTime2 >= classData.endTime2) {
                setValidationError("Giờ bắt đầu buổi 2 phải nhỏ hơn giờ kết thúc buổi 2");
                alert("Giờ bắt đầu buổi 2 phải nhỏ hơn giờ kết thúc buổi 2");
                return;
            }
        }

        // Clear any previous validation errors
        setValidationError("");

        dispatch(postClass(classData))
            .unwrap()
            .then(() => {
                dispatch(setSuccessMessage("Tạo lớp học thành công"));
                onClose();
                dispatch(fetchClasses({ search, currentPage, limit, sortOrder })).unwrap();
            })
            .catch((error) => {
                console.error("Error creating class:", error);
            })
    }

    if (loading) return (
        <div className="flex items-center justify-center h-full w-full">
            <LoadingSpinner
                size="4rem"
                showText={true}
                text="Đang tải..."
            />
        </div>
    )


    return (
        <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-2">Tạo lớp học mới</h2>
                <p className="text-gray-600">Điền thông tin để tạo lớp học mới trong hệ thống</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
                {/* Thông tin cơ bản */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <InfoIcon />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900">Thông tin cơ bản</h3>
                            <p className="text-sm text-gray-500">Tên lớp, năm học và khối lớp</p>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Tên lớp */}
                        <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                                Tên lớp <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                required
                                value={classData.name}
                                onChange={(e) => setClassData({ ...classData, name: e.target.value })}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400"
                                placeholder="Ví dụ: 10A1, 11B2..."
                            />
                        </div>

                        {/* Năm học */}
                        <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                                Năm học <span className="text-red-500">*</span>
                            </label>
                            <DropMenuBarAdmin
                                selectedOption={classData.academicYear}
                                onChange={(option) => setClassData({ ...classData, academicYear: option })}
                                options={Array.isArray(codes.year) ? codes.year : []}
                            />
                        </div>

                        {/* Khối lớp */}
                        <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                                Khối lớp <span className="text-red-500">*</span>
                            </label>
                            <DropMenuBarAdmin
                                selectedOption={classData.grade}
                                onChange={(option) => setClassData({ ...classData, grade: option })}
                                options={[
                                    { code: "10", description: "Lớp 10" },
                                    { code: "11", description: "Lớp 11" },
                                    { code: "12", description: "Lớp 12" }
                                ]}
                            />
                        </div>

                        {/* Công khai */}
                        <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                                Quyền truy cập
                            </label>
                            <DropMenuBarAdmin
                                selectedOption={classData.public}
                                onChange={(option) => setClassData({ ...classData, public: option })}
                                options={[
                                    { code: true, description: "Công khai" },
                                    { code: false, description: "Riêng tư" }
                                ]}
                            />
                        </div>
                    </div>
                </div>

                {/* Lịch học */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <CalendarIcon />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900">Lịch học</h3>
                            <p className="text-sm text-gray-500">Thời gian và ngày học trong tuần</p>
                        </div>
                    </div>

                    {/* Buổi 1 */}
                    <div className="mb-6">
                        <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center gap-2">
                            <span className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</span>
                            Buổi học chính
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="space-y-2">
                                <label className="block text-sm font-medium text-gray-700">
                                    Thứ trong tuần <span className="text-red-500">*</span>
                                </label>
                                <DropMenuBarAdmin
                                    selectedOption={classData.dayOfWeek1}
                                    onChange={(option) => setClassData({ ...classData, dayOfWeek1: option })}
                                    options={Array.isArray(codes.dow) ? codes.dow : []}
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="block text-sm font-medium text-gray-700">
                                    Giờ bắt đầu <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="time"
                                    required
                                    value={classData.startTime1}
                                    onChange={(e) => {
                                        setClassData({ ...classData, startTime1: e.target.value });
                                        setValidationError("");
                                    }}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="block text-sm font-medium text-gray-700">
                                    Giờ kết thúc <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="time"
                                    required
                                    value={classData.endTime1}
                                    onChange={(e) => {
                                        setClassData({ ...classData, endTime1: e.target.value });
                                        setValidationError("");
                                    }}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Buổi 2 (Tùy chọn) */}
                    <div className="border-t border-gray-200 pt-6">
                        <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center gap-2">
                            <span className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</span>
                            Buổi học phụ (tùy chọn)
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="space-y-2">
                                <label className="block text-sm font-medium text-gray-700">
                                    Thứ trong tuần
                                </label>
                                <DropMenuBarAdmin
                                    selectedOption={classData.dayOfWeek2}
                                    onChange={(option) => setClassData({ ...classData, dayOfWeek2: option })}
                                    options={Array.isArray(codes.dow) ? codes.dow : []}
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="block text-sm font-medium text-gray-700">
                                    Giờ bắt đầu
                                </label>
                                <input
                                    type="time"
                                    value={classData.startTime2}
                                    onChange={(e) => {
                                        setClassData({ ...classData, startTime2: e.target.value });
                                        setValidationError("");
                                    }}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="block text-sm font-medium text-gray-700">
                                    Giờ kết thúc
                                </label>
                                <input
                                    type="time"
                                    value={classData.endTime2}
                                    onChange={(e) => {
                                        setClassData({ ...classData, endTime2: e.target.value });
                                        setValidationError("");
                                    }}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Thông tin bổ sung */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <InfoIcon />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900">Thông tin bổ sung</h3>
                            <p className="text-sm text-gray-500">Mô tả và ghi chú về lớp học</p>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                            Mô tả lớp học
                        </label>
                        <textarea
                            value={classData.description}
                            onChange={(e) => setClassData({ ...classData, description: e.target.value })}
                            rows={4}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400 resize-none"
                            placeholder="Nhập mô tả về lớp học, mục tiêu, yêu cầu..."
                        />
                    </div>
                </div>

                {/* Error message */}
                {validationError && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex items-center gap-3">
                            <div className="w-5 h-5 text-red-500">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <p className="text-red-700 font-medium">{validationError}</p>
                        </div>
                    </div>
                )}

                {/* Action buttons */}
                <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
                    <button
                        type="button"
                        onClick={onClose}
                        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium"
                    >
                        Hủy bỏ
                    </button>
                    <button
                        type="submit"
                        className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium shadow-sm"
                    >
                        Tạo lớp học
                    </button>
                </div>
            </form>
        </div>
    );
};

export default AddClassModal;