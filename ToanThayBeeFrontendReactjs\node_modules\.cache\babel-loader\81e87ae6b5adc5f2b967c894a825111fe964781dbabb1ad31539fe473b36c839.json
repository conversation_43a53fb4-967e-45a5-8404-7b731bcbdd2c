{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\ImageDropZone.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Trash2, ImagePlus, Upload } from \"lucide-react\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDropZone = _ref => {\n  _s();\n  let {\n    imageUrl,\n    onImageDrop,\n    onImageRemove,\n    content\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\");\n    if (draggedImage && onImageDrop) {\n      onImageDrop(draggedImage);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    // Chỉ tắt khi thực sự rời khỏi drop zone\n    const rect = e.currentTarget.getBoundingClientRect();\n    const x = e.clientX;\n    const y = e.clientY;\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      setIsDraggingOver(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-2 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative flex items-center  rounded-md p-4 cursor-pointer\\n        \".concat(isDraggingOver || isHovering ? \"border border-dashed border-sky-500 bg-sky-50 justify-center\" : \"justify-start\"),\n      onDragOver: e => e.preventDefault(),\n      onDragEnter: () => setIsDraggingOver(true),\n      onDragLeave: () => setIsDraggingOver(false),\n      onDrop: e => {\n        setIsDraggingOver(false); // reset lại trạng thái sau khi drop\n        handleDrop(e);\n      },\n      onMouseEnter: () => setIsHovering(true),\n      onMouseLeave: () => setIsHovering(false),\n      children: isDraggingOver || isHovering ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500 text-sm text-center\",\n        children: \"Th\\xEAm \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        text: content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this), imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-fit group/image\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: \"dropped\",\n        className: \"rounded-md max-h-40 object-contain w-full border transition-all group-hover/image:brightness-75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20 rounded-md\",\n        onClick: e => {\n          e.stopPropagation();\n          onImageRemove === null || onImageRemove === void 0 ? void 0 : onImageRemove();\n        },\n        title: \"X\\xF3a \\u1EA3nh\",\n        children: /*#__PURE__*/_jsxDEV(Trash2, {\n          className: \"w-6 h-6 text-white bg-red-500 p-1 rounded-full group-hover/image:opacity-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 9\n  }, this);\n};\n_s(ImageDropZone, \"VKW1ZLDpPe+zo2/qAoOWVX5YkVs=\");\n_c = ImageDropZone;\nexport default ImageDropZone;\nvar _c;\n$RefreshReg$(_c, \"ImageDropZone\");", "map": {"version": 3, "names": ["React", "useState", "Trash2", "ImagePlus", "Upload", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ImageDropZone", "_ref", "_s", "imageUrl", "onImageDrop", "onImageRemove", "content", "isDraggingOver", "setIsDraggingOver", "handleDrop", "e", "preventDefault", "stopPropagation", "draggedImage", "dataTransfer", "getData", "handleDragOver", "handleDragEnter", "types", "includes", "handleDragLeave", "rect", "currentTarget", "getBoundingClientRect", "x", "clientX", "y", "clientY", "left", "right", "top", "bottom", "className", "children", "concat", "isHovering", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "onMouseEnter", "setIsHovering", "onMouseLeave", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "src", "alt", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/ImageDropZone.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Trash2, ImagePlus, Upload } from \"lucide-react\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\n\r\nconst ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove, content }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\");\r\n        if (draggedImage && onImageDrop) {\r\n            onImageDrop(draggedImage);\r\n        }\r\n    };\r\n\r\n    const handleDragOver = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n    };\r\n\r\n    const handleDragEnter = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (e.dataTransfer.types.includes('text/plain')) {\r\n            setIsDraggingOver(true);\r\n        }\r\n    };\r\n\r\n    const handleDragLeave = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        // Chỉ tắt khi thực sự rời khỏi drop zone\r\n        const rect = e.currentTarget.getBoundingClientRect();\r\n        const x = e.clientX;\r\n        const y = e.clientY;\r\n\r\n        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\r\n            setIsDraggingOver(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-2 w-full\">\r\n            <div\r\n                className={`relative flex items-center  rounded-md p-4 cursor-pointer\r\n        ${isDraggingOver || isHovering ? \"border border-dashed border-sky-500 bg-sky-50 justify-center\" : \"justify-start\"}`}\r\n                onDragOver={(e) => e.preventDefault()}\r\n                onDragEnter={() => setIsDraggingOver(true)}\r\n                onDragLeave={() => setIsDraggingOver(false)}\r\n                onDrop={(e) => {\r\n                    setIsDraggingOver(false); // reset lại trạng thái sau khi drop\r\n                    handleDrop(e);\r\n                }}\r\n                onMouseEnter={() => setIsHovering(true)}\r\n                onMouseLeave={() => setIsHovering(false)}\r\n            >\r\n                {isDraggingOver || isHovering ? (\r\n                    <p className=\"text-gray-500 text-sm text-center\">Thêm ảnh</p>\r\n                ) : (\r\n                    <LatexRenderer text={content} />\r\n                )}\r\n            </div>\r\n            {imageUrl && (\r\n                <div className=\"relative w-fit group/image\">\r\n                    <img\r\n                        src={imageUrl}\r\n                        alt=\"dropped\"\r\n                        className=\"rounded-md max-h-40 object-contain w-full border transition-all group-hover/image:brightness-75\"\r\n                    />\r\n                    <button\r\n                        className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20 rounded-md\"\r\n                        onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            onImageRemove?.();\r\n                        }}\r\n                        title=\"Xóa ảnh\"\r\n                    >\r\n                        <Trash2 className=\"w-6 h-6 text-white bg-red-500 p-1 rounded-full group-hover/image:opacity-100\" />\r\n                    </button>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ImageDropZone;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,SAAS,EAAEC,MAAM,QAAQ,cAAc;AACxD,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,aAAa,GAAGC,IAAA,IAAuD;EAAAC,EAAA;EAAA,IAAtD;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,aAAa;IAAEC;EAAQ,CAAC,GAAAL,IAAA;EACpE,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMgB,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBJ,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMK,YAAY,GAAGH,CAAC,CAACI,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,IAAIF,YAAY,IAAIT,WAAW,EAAE;MAC7BA,WAAW,CAACS,YAAY,CAAC;IAC7B;EACJ,CAAC;EAED,MAAMG,cAAc,GAAIN,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAMK,eAAe,GAAIP,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIF,CAAC,CAACI,YAAY,CAACI,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC7CX,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMY,eAAe,GAAIV,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB;IACA,MAAMS,IAAI,GAAGX,CAAC,CAACY,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACpD,MAAMC,CAAC,GAAGd,CAAC,CAACe,OAAO;IACnB,MAAMC,CAAC,GAAGhB,CAAC,CAACiB,OAAO;IAEnB,IAAIH,CAAC,GAAGH,IAAI,CAACO,IAAI,IAAIJ,CAAC,GAAGH,IAAI,CAACQ,KAAK,IAAIH,CAAC,GAAGL,IAAI,CAACS,GAAG,IAAIJ,CAAC,GAAGL,IAAI,CAACU,MAAM,EAAE;MACpEvB,iBAAiB,CAAC,KAAK,CAAC;IAC5B;EACJ,CAAC;EAED,oBACIT,OAAA;IAAKiC,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACvClC,OAAA;MACIiC,SAAS,wEAAAE,MAAA,CACf3B,cAAc,IAAI4B,UAAU,GAAG,8DAA8D,GAAG,eAAe,CAAG;MAC5GC,UAAU,EAAG1B,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;MACtC0B,WAAW,EAAEA,CAAA,KAAM7B,iBAAiB,CAAC,IAAI,CAAE;MAC3C8B,WAAW,EAAEA,CAAA,KAAM9B,iBAAiB,CAAC,KAAK,CAAE;MAC5C+B,MAAM,EAAG7B,CAAC,IAAK;QACXF,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1BC,UAAU,CAACC,CAAC,CAAC;MACjB,CAAE;MACF8B,YAAY,EAAEA,CAAA,KAAMC,aAAa,CAAC,IAAI,CAAE;MACxCC,YAAY,EAAEA,CAAA,KAAMD,aAAa,CAAC,KAAK,CAAE;MAAAR,QAAA,EAExC1B,cAAc,IAAI4B,UAAU,gBACzBpC,OAAA;QAAGiC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAAQ;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAE7D/C,OAAA,CAACF,aAAa;QAACkD,IAAI,EAAEzC;MAAQ;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAClC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EACL3C,QAAQ,iBACLJ,OAAA;MAAKiC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACvClC,OAAA;QACIiD,GAAG,EAAE7C,QAAS;QACd8C,GAAG,EAAC,SAAS;QACbjB,SAAS,EAAC;MAAiG;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9G,CAAC,eACF/C,OAAA;QACIiC,SAAS,EAAC,6JAA6J;QACvKkB,OAAO,EAAGxC,CAAC,IAAK;UACZA,CAAC,CAACE,eAAe,CAAC,CAAC;UACnBP,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAG,CAAC;QACrB,CAAE;QACF8C,KAAK,EAAC,iBAAS;QAAAlB,QAAA,eAEflC,OAAA,CAACL,MAAM;UAACsC,SAAS,EAAC;QAA8E;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC5C,EAAA,CAlFIF,aAAa;AAAAoD,EAAA,GAAbpD,aAAa;AAoFnB,eAAeA,aAAa;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}