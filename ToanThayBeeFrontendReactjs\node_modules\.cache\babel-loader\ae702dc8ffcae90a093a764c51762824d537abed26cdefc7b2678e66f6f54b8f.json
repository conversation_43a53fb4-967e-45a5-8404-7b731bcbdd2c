{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\QuestionOfExamAdmin.jsx\",\n  _s = $RefreshSig$();\nimport AdminLayout from \"../../../layouts/AdminLayout\";\nimport FunctionBarAdmin from \"../../../components/bar/FunctionBarAdmin\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport QuestionTable from \"../../../components/table/QuestionTable\";\nimport { fetchExamQuestionsWithoutPagination } from \"../../../features/question/questionSlice\";\nimport { useNavigate } from \"react-router-dom\";\nimport AdminModal from \"../../../components/modal/AdminModal\";\nimport AddQuestionModal from \"../../../components/modal/AddQuestionModal\";\nimport { setIsAddView } from \"../../../features/filter/filterSlice\";\nimport { setCurrentPage, setLimit, setSearch } from \"src/features/question/questionSlice\";\nimport ExamAdminLayout from \"src/layouts/ExamAdminLayout\";\nimport { useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionOfExamAdmin = () => {\n  _s();\n  const {\n    examId\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    isAddView\n  } = useSelector(state => state.filter);\n  const {\n    pagination\n  } = useSelector(state => state.questions);\n  useEffect(() => {\n    dispatch(fetchExamQuestionsWithoutPagination({\n      id: examId\n    }));\n  }, [dispatch, examId]);\n  return /*#__PURE__*/_jsxDEV(ExamAdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-1 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-1/2 border-r border-gray-200 bg-white\",\n        children: /*#__PURE__*/_jsxDEV(LeftContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-1/2\",\n        children: /*#__PURE__*/_jsxDEV(RightContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionOfExamAdmin, \"OhyMxrWQV8ELO5i0Wpm1qk1D6NU=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector, useSelector];\n});\n_c = QuestionOfExamAdmin;\nexport default QuestionOfExamAdmin;\nvar _c;\n$RefreshReg$(_c, \"QuestionOfExamAdmin\");", "map": {"version": 3, "names": ["AdminLayout", "FunctionBarAdmin", "useSelector", "useDispatch", "useParams", "QuestionTable", "fetchExamQuestionsWithoutPagination", "useNavigate", "AdminModal", "AddQuestionModal", "setIsAddView", "setCurrentPage", "setLimit", "setSearch", "ExamAdminLayout", "useEffect", "jsxDEV", "_jsxDEV", "QuestionOfExamAdmin", "_s", "examId", "navigate", "dispatch", "isAddView", "state", "filter", "pagination", "questions", "id", "children", "className", "LeftContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "RightContent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/QuestionOfExamAdmin.jsx"], "sourcesContent": ["import AdminLayout from \"../../../layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"../../../components/bar/FunctionBarAdmin\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport QuestionTable from \"../../../components/table/QuestionTable\";\r\nimport { fetchExamQuestionsWithoutPagination } from \"../../../features/question/questionSlice\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AdminModal from \"../../../components/modal/AdminModal\";\r\nimport AddQuestionModal from \"../../../components/modal/AddQuestionModal\";\r\nimport { setIsAddView } from \"../../../features/filter/filterSlice\";\r\nimport { setCurrentPage, setLimit, setSearch } from \"src/features/question/questionSlice\";\r\nimport ExamAdminLayout from \"src/layouts/ExamAdminLayout\";\r\nimport { useEffect } from \"react\";\r\n\r\nconst QuestionOfExamAdmin = () => {\r\n    const { examId } = useParams();\r\n    const navigate = useNavigate();\r\n    const dispatch = useDispatch();\r\n    const { isAddView } = useSelector(state => state.filter);\r\n    const { pagination } = useSelector(state => state.questions);\r\n\r\n    useEffect(() => {\r\n        dispatch(fetchExamQuestionsWithoutPagination({ id: examId }));\r\n    }, [dispatch, examId]);\r\n\r\n    return (\r\n        <ExamAdminLayout>\r\n            <div className=\"flex flex-1 overflow-hidden\">\r\n                {/* Left Panel - Form */}\r\n                <div className=\"w-1/2 border-r border-gray-200 bg-white\">\r\n                    <LeftContent />\r\n                </div>\r\n\r\n                {/* Right Panel - Preview */}\r\n                <div className=\"w-1/2\">\r\n                    <RightContent />\r\n                </div>\r\n            </div>\r\n        </ExamAdminLayout>\r\n\r\n    )\r\n}\r\n\r\nexport default QuestionOfExamAdmin;\r\n"], "mappings": ";;AAAA,OAAOA,WAAW,MAAM,8BAA8B;AACtD,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,aAAa,MAAM,yCAAyC;AACnE,SAASC,mCAAmC,QAAQ,0CAA0C;AAC9F,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,SAASC,YAAY,QAAQ,sCAAsC;AACnE,SAASC,cAAc,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,qCAAqC;AACzF,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAO,CAAC,GAAGhB,SAAS,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoB;EAAU,CAAC,GAAGrB,WAAW,CAACsB,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EACxD,MAAM;IAAEC;EAAW,CAAC,GAAGxB,WAAW,CAACsB,KAAK,IAAIA,KAAK,CAACG,SAAS,CAAC;EAE5DZ,SAAS,CAAC,MAAM;IACZO,QAAQ,CAAChB,mCAAmC,CAAC;MAAEsB,EAAE,EAAER;IAAO,CAAC,CAAC,CAAC;EACjE,CAAC,EAAE,CAACE,QAAQ,EAAEF,MAAM,CAAC,CAAC;EAEtB,oBACIH,OAAA,CAACH,eAAe;IAAAe,QAAA,eACZZ,OAAA;MAAKa,SAAS,EAAC,6BAA6B;MAAAD,QAAA,gBAExCZ,OAAA;QAAKa,SAAS,EAAC,yCAAyC;QAAAD,QAAA,eACpDZ,OAAA,CAACc,WAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAGNlB,OAAA;QAAKa,SAAS,EAAC,OAAO;QAAAD,QAAA,eAClBZ,OAAA,CAACmB,YAAY;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAG1B,CAAC;AAAAhB,EAAA,CA3BKD,mBAAmB;EAAA,QACFd,SAAS,EACXG,WAAW,EACXJ,WAAW,EACND,WAAW,EACVA,WAAW;AAAA;AAAAmC,EAAA,GALhCnB,mBAAmB;AA6BzB,eAAeA,mBAAmB;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}