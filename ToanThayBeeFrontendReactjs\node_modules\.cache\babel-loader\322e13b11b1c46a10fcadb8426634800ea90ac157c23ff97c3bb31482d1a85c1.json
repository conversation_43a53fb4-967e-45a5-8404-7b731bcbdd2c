{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\LeftContent.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LeftContent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_42px)]\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Danh s\\xE1ch c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 7,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 6,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded border border-gray-200 \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = LeftContent;\nexport default LeftContent;\nvar _c;\n$RefreshReg$(_c, \"LeftContent\");", "map": {"version": 3, "names": ["LeftContent", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/LeftContent.jsx"], "sourcesContent": ["const LeftContent = () => {\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_42px)]\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Danh sách câu hỏi</h2>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Scrollable Preview Content */}\r\n            <div className=\"flex-1 overflow-y-auto p-3\">\r\n                <div className=\"bg-white rounded border border-gray-200 \">\r\n                    {/* Hiển thị danh sách câu hỏi ở đây */}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nexport default LeftContent;"], "mappings": ";;AAAA,MAAMA,WAAW,GAAGA,CAAA,KAAM;EACtB,oBACIC,OAAA;IAAKC,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEjDF,OAAA;MAAKC,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAC7FF,OAAA;QAAKC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCF,OAAA;UAAIC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNN,OAAA;MAAKC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACvCF,OAAA;QAAKC,SAAS,EAAC;MAA0C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAC,EAAA,GAlBKR,WAAW;AAqBjB,eAAeA,WAAW;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}