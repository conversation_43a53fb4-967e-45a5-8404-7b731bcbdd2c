{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport TextArea from \"src/components/input/TextArea\";\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { CheckCircle } from \"lucide-react\";\nimport { setQuestions, setNewQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DetailQuestionView = _ref => {\n  _s();\n  let {\n    optionChapter\n  } = _ref;\n  const {\n    questionsExam,\n    selectedId,\n    view\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const dispatch = useDispatch();\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [question, setQuestion] = useState(null);\n  useEffect(() => {\n    // console.log(selectedId);\n\n    if (selectedId && (questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.length) > 0) {\n      const selectedQuestion = questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.find(q => q.id === selectedId);\n      setQuestion(selectedQuestion);\n    }\n  }, [selectedId, questionsExam]);\n  const handleQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...question,\n      [field]: e.target.value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleStatementChange = (index, value, field) => {\n    const updatedStatements = [...question.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...question,\n      statements: updatedStatements\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleSolutionQuestionChange = value => {\n    const updatedQuestion = {\n      ...question,\n      solution: value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: question && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Ph\\xE2n lo\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.class,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'class'),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: question.chapter,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'chapter'),\n            options: optionChapter,\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.difficulty,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'difficulty'),\n            options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextArea, {\n            value: question.content,\n            onChange: e => handleQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 29\n          }, this), (view === 'image' || question.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: question.imageUrl,\n            onImageDrop: image => handleQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 33\n          }, this), question.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n                  value: statement.content,\n                  onChange: e => handleStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 45\n              }, this), (view === 'image' || statement.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 49\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 41\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 33\n          }, this), question.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: question.correctAnswer,\n              onChange: e => handleQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n            solution: question.solution,\n            onSolutionChange: handleSolutionQuestionChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 9\n  }, this);\n};\n_s(DetailQuestionView, \"a0FxRXwBAN6AAun+FPFt2MHanok=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = DetailQuestionView;\nconst AddQuestionView = _ref2 => {\n  _s2();\n  let {\n    optionChapter\n  } = _ref2;\n  const dispatch = useDispatch();\n  const {\n    newQuestion\n  } = useSelector(state => state.questionsExam);\n  const handleNewQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...newQuestion,\n      [field]: e.target.value\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  const handleNewStatementChange = (index, value, field) => {\n    const updatedStatements = [...newQuestion.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...newQuestion,\n      statements: updatedStatements\n    };\n    dispatch(setNewQuestion(updatedQuestion));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-900 mb-1\",\n        children: \"Ph\\xE2n lo\\u1EA1i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: newQuestion.class,\n        onChange: option => handleNewQuestionChange({\n          target: {\n            value: option\n          }\n        }, 'class'),\n        options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n        className: \"text-xs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n        selectedOption: newQuestion.chapter,\n        onChange: option => handleNewQuestionChange({\n          target: {\n            value: option\n          }\n        }, 'chapter'),\n        options: optionChapter,\n        className: \"text-xs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: newQuestion.difficulty,\n        onChange: option => handleNewQuestionChange({\n          target: {\n            value: option\n          }\n        }, 'difficulty'),\n        options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n        className: \"text-xs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 9\n  }, this);\n};\n_s2(AddQuestionView, \"27UGbxbm3AL1dy+pPbAmxtPMMAQ=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c2 = AddQuestionView;\nconst LeftContent = () => {\n  _s3();\n  const [view, setView] = useState('questionDetail');\n  const {\n    question\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const [optionChapter, setOptionChapter] = useState([]);\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (question !== null && question !== void 0 && question.class && (question === null || question === void 0 ? void 0 : question.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(question === null || question === void 0 ? void 0 : question.class) && code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, question === null || question === void 0 ? void 0 : question.class]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Chi ti\\u1EBFt c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: view === 'questionDetail' ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setView('addQuestion'),\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white\",\n          children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setView('questionDetail'),\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white\",\n            children: \"L\\u01B0u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setView('questionDetail'),\n            className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white\",\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 13\n    }, this), view === 'questionDetail' && /*#__PURE__*/_jsxDEV(DetailQuestionView, {\n      optionChapter: optionChapter\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 43\n    }, this), view === 'addQuestion' && /*#__PURE__*/_jsxDEV(AddQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 40\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 9\n  }, this);\n};\n_s3(LeftContent, \"nYqA78G1IWWTKt90rJ5RX1E3/Bw=\", false, function () {\n  return [useSelector, useSelector];\n});\n_c3 = LeftContent;\nexport default LeftContent;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"DetailQuestionView\");\n$RefreshReg$(_c2, \"AddQuestionView\");\n$RefreshReg$(_c3, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useState", "useSelector", "useDispatch", "DropMenuBarAdmin", "SuggestInputBarAdmin", "TextArea", "ImageDropZone", "SolutionEditor", "setQuestion", "CheckCircle", "setQuestions", "setNewQuestion", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DetailQuestionView", "_ref", "_s", "optionChapter", "questionsExam", "selectedId", "view", "state", "codes", "dispatch", "prefixTN", "prefixDS", "question", "length", "selectedQuestion", "find", "q", "id", "handleQuestionChange", "e", "field", "updatedQuestion", "target", "value", "handleStatementChange", "index", "updatedStatements", "statements", "handleSolutionQuestionChange", "solution", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selectedOption", "class", "onChange", "option", "options", "Array", "isArray", "chapter", "difficulty", "content", "placeholder", "label", "imageUrl", "onImageDrop", "image", "onImageRemove", "typeOfQuestion", "map", "statement", "<PERSON><PERSON><PERSON><PERSON>", "Icon", "onSolutionChange", "_c", "AddQuestionView", "_ref2", "_s2", "newQuestion", "handleNewQuestionChange", "handleNewStatementChange", "_c2", "LeftContent", "_s3", "<PERSON><PERSON><PERSON><PERSON>", "setOptionChapter", "trim", "filter", "code", "startsWith", "onClick", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/LeftContent.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport TextArea from \"src/components/input/TextArea\";\r\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\r\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\r\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { CheckCircle } from \"lucide-react\";\r\nimport { setQuestions, setNewQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\n\r\n\r\nconst DetailQuestionView = ({ optionChapter }) => {\r\n    const { questionsExam, selectedId, view } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const dispatch = useDispatch();\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n\r\n    const [question, setQuestion] = useState(null);\r\n\r\n    useEffect(() => {\r\n        // console.log(selectedId);\r\n\r\n        if (selectedId && questionsExam?.length > 0) {\r\n            const selectedQuestion = questionsExam?.find(q => q.id === selectedId);\r\n            setQuestion(selectedQuestion);\r\n        }\r\n    }, [selectedId, questionsExam]);\r\n\r\n    const handleQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...question, [field]: e.target.value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...question.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...question, statements: updatedStatements };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleSolutionQuestionChange = (value) => {\r\n        const updatedQuestion = { ...question, solution: value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n\r\n\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            {question && (\r\n                <div className=\"space-y-3 w-full\">\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                        <div className=\"flex flex-row gap-2\">\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.class}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <SuggestInputBarAdmin\r\n                                selectedOption={question.chapter}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}\r\n                                options={optionChapter}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.difficulty}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                                options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\" bg-gray-200\"></hr>\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                        <div className=\"space-y-2\">\r\n                            <TextArea\r\n                                value={question.content}\r\n                                onChange={(e) => handleQuestionChange(e, 'content')}\r\n                                placeholder=\"Nhập nội dung câu hỏi\"\r\n                                label=\"Câu hỏi\"\r\n                            />\r\n                            {(view === 'image' || question.imageUrl) && (\r\n                                <ImageDropZone\r\n                                    imageUrl={question.imageUrl}\r\n                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                                />\r\n                            )}\r\n                            {question.typeOfQuestion !== 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    {question.statements.map((statement, index) => (\r\n                                        <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                            <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                                <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                    {question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                                </p>\r\n                                                <TextArea\r\n                                                    value={statement.content}\r\n                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}\r\n                                                    placeholder=\"Nhập nội dung mệnh đề\"\r\n                                                />\r\n                                            </div>\r\n                                            {(view === 'image' || statement.imageUrl) && (\r\n                                                <ImageDropZone\r\n                                                    imageUrl={statement.imageUrl}\r\n                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}\r\n                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}\r\n                                                />\r\n                                            )}\r\n\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            )}\r\n                            {question.typeOfQuestion === 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    <TextArea\r\n                                        value={question.correctAnswer}\r\n                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}\r\n                                        placeholder=\"Nhập đáp án\"\r\n                                        label=\"Đáp án\"\r\n                                        Icon={CheckCircle}\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            {/* <TextArea\r\n                                        value={question.solution}\r\n                                        onChange={(e) => handleQuestionChange(e, 'solution')}\r\n                                        placeholder=\"Nhập lời giải\"\r\n                                        label=\"Lời giải\"\r\n                                        Icon={CheckCircle}\r\n                                    /> */}\r\n                            <SolutionEditor\r\n                                solution={question.solution}\r\n                                onSolutionChange={handleSolutionQuestionChange}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst AddQuestionView = ({ optionChapter }) => {\r\n    const dispatch = useDispatch();\r\n    const { newQuestion } = useSelector((state) => state.questionsExam);\r\n\r\n\r\n    const handleNewQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...newQuestion, [field]: e.target.value };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    const handleNewStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...newQuestion.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...newQuestion, statements: updatedStatements };\r\n        dispatch(setNewQuestion(updatedQuestion));\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-3\">\r\n            <div className=\"flex flex-col gap-2\">\r\n                <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                <DropMenuBarAdmin\r\n                    selectedOption={newQuestion.class}\r\n                    onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'class')}\r\n                    options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                    className=\"text-xs\"\r\n                />\r\n                <SuggestInputBarAdmin\r\n                    selectedOption={newQuestion.chapter}\r\n                    onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'chapter')}\r\n                    options={optionChapter}\r\n                    className=\"text-xs\"\r\n                />\r\n                <DropMenuBarAdmin\r\n                    selectedOption={newQuestion.difficulty}\r\n                    onChange={(option) => handleNewQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                    options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                    className=\"text-xs\"\r\n                />\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst LeftContent = () => {\r\n    const [view, setView] = useState('questionDetail');\r\n    const { question } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (question?.class && question?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(question?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, question?.class]);\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Chi tiết câu hỏi</h2>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                    {\r\n                        view === 'questionDetail' ? (\r\n                            <button\r\n                                onClick={() => setView('addQuestion')}\r\n                                className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-sky-600 hover:bg-sky-700 text-white`}\r\n                            >\r\n                                Thêm câu hỏi\r\n                            </button>\r\n                        ) : (\r\n                            <>\r\n                                <button\r\n                                    onClick={() => setView('questionDetail')}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-emerald-600 hover:bg-emerald-700 text-white`}\r\n                                >\r\n                                    Lưu\r\n                                </button>\r\n                                <button\r\n                                    onClick={() => setView('questionDetail')}\r\n                                    className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white`}\r\n                                >\r\n                                    Hủy\r\n                                </button>\r\n\r\n                            </>\r\n                        )\r\n                    }\r\n\r\n                </div>\r\n            </div>\r\n            {view === 'questionDetail' && <DetailQuestionView optionChapter={optionChapter} />}\r\n            {view === 'addQuestion' && <AddQuestionView />}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nexport default LeftContent;"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SAASC,WAAW,QAAQ,+CAA+C;AAC3E,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,YAAY,EAAEC,cAAc,QAAQ,+CAA+C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG7F,MAAMC,kBAAkB,GAAGC,IAAA,IAAuB;EAAAC,EAAA;EAAA,IAAtB;IAAEC;EAAc,CAAC,GAAAF,IAAA;EACzC,MAAM;IAAEG,aAAa;IAAEC,UAAU;IAAEC;EAAK,CAAC,GAAGrB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACvF,MAAM;IAAEI;EAAM,CAAC,GAAGvB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAE7E,MAAM,CAACC,QAAQ,EAAEpB,WAAW,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACZ;;IAEA,IAAIsB,UAAU,IAAI,CAAAD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAES,MAAM,IAAG,CAAC,EAAE;MACzC,MAAMC,gBAAgB,GAAGV,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKZ,UAAU,CAAC;MACtEb,WAAW,CAACsB,gBAAgB,CAAC;IACjC;EACJ,CAAC,EAAE,CAACT,UAAU,EAAED,aAAa,CAAC,CAAC;EAE/B,MAAMc,oBAAoB,GAAGA,CAACC,CAAC,EAAEC,KAAK,KAAK;IACvC,MAAMC,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAE,CAACQ,KAAK,GAAGD,CAAC,CAACG,MAAM,CAACC;IAAM,CAAC;IAChEd,QAAQ,CAACf,YAAY,CAAC2B,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAACC,KAAK,EAAEF,KAAK,EAAEH,KAAK,KAAK;IACnD,MAAMM,iBAAiB,GAAG,CAAC,GAAGd,QAAQ,CAACe,UAAU,CAAC;IAClDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACL,KAAK,GAAGG;IAAM,CAAC;IAC1E,MAAMF,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEe,UAAU,EAAED;IAAkB,CAAC;IACtEjB,QAAQ,CAACf,YAAY,CAAC2B,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMO,4BAA4B,GAAIL,KAAK,IAAK;IAC5C,MAAMF,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEiB,QAAQ,EAAEN;IAAM,CAAC;IACxDd,QAAQ,CAACf,YAAY,CAAC2B,eAAe,CAAC,CAAC;EAC3C,CAAC;EAKD,oBACIxB,OAAA;IAAKiC,SAAS,EAAC,sBAAsB;IAAAC,QAAA,EAChCnB,QAAQ,iBACLf,OAAA;MAAKiC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BlC,OAAA;QAAKiC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChClC,OAAA;UAAIiC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEtC,OAAA;UAAKiC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChClC,OAAA,CAACV,gBAAgB;YACbiD,cAAc,EAAExB,QAAQ,CAACyB,KAAM;YAC/BC,QAAQ,EAAGC,MAAM,IAAKrB,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEgB;cAAO;YAAE,CAAC,EAAE,OAAO,CAAE;YACnFC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAAClC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;YAC7DsB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACFtC,OAAA,CAACT,oBAAoB;YACjBgD,cAAc,EAAExB,QAAQ,CAAC+B,OAAQ;YACjCL,QAAQ,EAAGC,MAAM,IAAKrB,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEgB;cAAO;YAAE,CAAC,EAAE,SAAS,CAAE;YACrFC,OAAO,EAAErC,aAAc;YACvB2B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACFtC,OAAA,CAACV,gBAAgB;YACbiD,cAAc,EAAExB,QAAQ,CAACgC,UAAW;YACpCN,QAAQ,EAAGC,MAAM,IAAKrB,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEgB;cAAO;YAAE,CAAC,EAAE,YAAY,CAAE;YACxFC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAAClC,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;YACvEsB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtC,OAAA;QAAIiC,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClCtC,OAAA;QAAKiC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChClC,OAAA;UAAIiC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EtC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBlC,OAAA,CAACR,QAAQ;YACLkC,KAAK,EAAEX,QAAQ,CAACiC,OAAQ;YACxBP,QAAQ,EAAGnB,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,SAAS,CAAE;YACpD2B,WAAW,EAAC,yCAAuB;YACnCC,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EACD,CAAC7B,IAAI,KAAK,OAAO,IAAIM,QAAQ,CAACoC,QAAQ,kBACnCnD,OAAA,CAACP,aAAa;YACV0D,QAAQ,EAAEpC,QAAQ,CAACoC,QAAS;YAC5BC,WAAW,EAAGC,KAAK,IAAKhC,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAE2B;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YACvFC,aAAa,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CACJ,EACAvB,QAAQ,CAACwC,cAAc,KAAK,KAAK,iBAC9BvD,OAAA;YAAKiC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrBnB,QAAQ,CAACe,UAAU,CAAC0B,GAAG,CAAC,CAACC,SAAS,EAAE7B,KAAK,kBACtC5B,OAAA;cAAiBiC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChElC,OAAA;gBAAKiC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpDlC,OAAA;kBAAGiC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7CnB,QAAQ,CAACwC,cAAc,KAAK,IAAI,GAAG1C,QAAQ,CAACe,KAAK,CAAC,GAAGd,QAAQ,CAACc,KAAK;gBAAC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACJtC,OAAA,CAACR,QAAQ;kBACLkC,KAAK,EAAE+B,SAAS,CAACT,OAAQ;kBACzBP,QAAQ,EAAGnB,CAAC,IAAKK,qBAAqB,CAACC,KAAK,EAAEN,CAAC,CAACG,MAAM,CAACC,KAAK,EAAE,SAAS,CAAE;kBACzEuB,WAAW,EAAC;gBAAuB;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACL,CAAC7B,IAAI,KAAK,OAAO,IAAIgD,SAAS,CAACN,QAAQ,kBACpCnD,OAAA,CAACP,aAAa;gBACV0D,QAAQ,EAAEM,SAAS,CAACN,QAAS;gBAC7BC,WAAW,EAAGC,KAAK,IAAK1B,qBAAqB,CAACC,KAAK,EAAEyB,KAAK,EAAE,UAAU,CAAE;gBACxEC,aAAa,EAAEA,CAAA,KAAM3B,qBAAqB,CAACC,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACJ;YAAA,GAjBKV,KAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAvB,QAAQ,CAACwC,cAAc,KAAK,KAAK,iBAC9BvD,OAAA;YAAKiC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtBlC,OAAA,CAACR,QAAQ;cACLkC,KAAK,EAAEX,QAAQ,CAAC2C,aAAc;cAC9BjB,QAAQ,EAAGnB,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,eAAe,CAAE;cAC1D2B,WAAW,EAAC,6BAAa;cACzBC,KAAK,EAAC,mBAAQ;cACdS,IAAI,EAAE/D;YAAY;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAQDtC,OAAA,CAACN,cAAc;YACXsC,QAAQ,EAAEjB,QAAQ,CAACiB,QAAS;YAC5B4B,gBAAgB,EAAE7B;UAA6B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAjC,EAAA,CAzIKF,kBAAkB;EAAA,QACwBf,WAAW,EACrCA,WAAW,EACZC,WAAW;AAAA;AAAAwE,EAAA,GAH1B1D,kBAAkB;AA2IxB,MAAM2D,eAAe,GAAGC,KAAA,IAAuB;EAAAC,GAAA;EAAA,IAAtB;IAAE1D;EAAc,CAAC,GAAAyD,KAAA;EACtC,MAAMnD,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4E;EAAY,CAAC,GAAG7E,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EAGnE,MAAM2D,uBAAuB,GAAGA,CAAC5C,CAAC,EAAEC,KAAK,KAAK;IAC1C,MAAMC,eAAe,GAAG;MAAE,GAAGyC,WAAW;MAAE,CAAC1C,KAAK,GAAGD,CAAC,CAACG,MAAM,CAACC;IAAM,CAAC;IACnEd,QAAQ,CAACd,cAAc,CAAC0B,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,MAAM2C,wBAAwB,GAAGA,CAACvC,KAAK,EAAEF,KAAK,EAAEH,KAAK,KAAK;IACtD,MAAMM,iBAAiB,GAAG,CAAC,GAAGoC,WAAW,CAACnC,UAAU,CAAC;IACrDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACL,KAAK,GAAGG;IAAM,CAAC;IAC1E,MAAMF,eAAe,GAAG;MAAE,GAAGyC,WAAW;MAAEnC,UAAU,EAAED;IAAkB,CAAC;IACzEjB,QAAQ,CAACd,cAAc,CAAC0B,eAAe,CAAC,CAAC;EAC7C,CAAC;EAED,oBACIxB,OAAA;IAAKiC,SAAS,EAAC,WAAW;IAAAC,QAAA,eACtBlC,OAAA;MAAKiC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChClC,OAAA;QAAIiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEtC,OAAA,CAACV,gBAAgB;QACbiD,cAAc,EAAE0B,WAAW,CAACzB,KAAM;QAClCC,QAAQ,EAAGC,MAAM,IAAKwB,uBAAuB,CAAC;UAAEzC,MAAM,EAAE;YAAEC,KAAK,EAAEgB;UAAO;QAAE,CAAC,EAAE,OAAO,CAAE;QACtFC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAAClC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;QAC7DsB,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACFtC,OAAA,CAACT,oBAAoB;QACjBgD,cAAc,EAAE0B,WAAW,CAACnB,OAAQ;QACpCL,QAAQ,EAAGC,MAAM,IAAKwB,uBAAuB,CAAC;UAAEzC,MAAM,EAAE;YAAEC,KAAK,EAAEgB;UAAO;QAAE,CAAC,EAAE,SAAS,CAAE;QACxFC,OAAO,EAAErC,aAAc;QACvB2B,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACFtC,OAAA,CAACV,gBAAgB;QACbiD,cAAc,EAAE0B,WAAW,CAAClB,UAAW;QACvCN,QAAQ,EAAGC,MAAM,IAAKwB,uBAAuB,CAAC;UAAEzC,MAAM,EAAE;YAAEC,KAAK,EAAEgB;UAAO;QAAE,CAAC,EAAE,YAAY,CAAE;QAC3FC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAAClC,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;QACvEsB,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAA0B,GAAA,CA1CKF,eAAe;EAAA,QACAzE,WAAW,EACJD,WAAW;AAAA;AAAAgF,GAAA,GAFjCN,eAAe;AA6CrB,MAAMO,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM,CAAC7D,IAAI,EAAE8D,OAAO,CAAC,GAAGpF,QAAQ,CAAC,gBAAgB,CAAC;EAClD,MAAM;IAAE4B;EAAS,CAAC,GAAG3B,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EAChE,MAAM;IAAEI;EAAM,CAAC,GAAGvB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAM,CAACL,aAAa,EAAEkE,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAEtDD,SAAS,CAAC,MAAM;IACZ,IAAI0D,KAAK,CAACC,OAAO,CAAClC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAII,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEyB,KAAK,IAAI,CAAAzB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyB,KAAK,CAACiC,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QAClDD,gBAAgB,CACZ7D,KAAK,CAAC,SAAS,CAAC,CAAC+D,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC7D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyB,KAAK,CAAC,IAAImC,IAAI,CAACA,IAAI,CAAC3D,MAAM,KAAK,CAAC,CACrG,CAAC;MACL,CAAC,MAAM;QACHwD,gBAAgB,CAAC7D,KAAK,CAAC,SAAS,CAAC,CAAC+D,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAAC3D,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHwD,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAAC7D,KAAK,EAAEI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyB,KAAK,CAAC,CAAC;EAE5B,oBACIxC,OAAA;IAAKiC,SAAS,EAAC,uDAAuD;IAAAC,QAAA,gBAElElC,OAAA;MAAKiC,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBAC/FlC,OAAA;QAAKiC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpClC,OAAA;UAAIiC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACNtC,OAAA;QAAKiC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAEhCzB,IAAI,KAAK,gBAAgB,gBACrBT,OAAA;UACI6E,OAAO,EAAEA,CAAA,KAAMN,OAAO,CAAC,aAAa,CAAE;UACtCtC,SAAS,+FAAgG;UAAAC,QAAA,EAC5G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETtC,OAAA,CAAAE,SAAA;UAAAgC,QAAA,gBACIlC,OAAA;YACI6E,OAAO,EAAEA,CAAA,KAAMN,OAAO,CAAC,gBAAgB,CAAE;YACzCtC,SAAS,uGAAwG;YAAAC,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtC,OAAA;YACI6E,OAAO,EAAEA,CAAA,KAAMN,OAAO,CAAC,gBAAgB,CAAE;YACzCtC,SAAS,+FAAgG;YAAAC,QAAA,EAC5G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eAEX;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACL7B,IAAI,KAAK,gBAAgB,iBAAIT,OAAA,CAACG,kBAAkB;MAACG,aAAa,EAAEA;IAAc;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACjF7B,IAAI,KAAK,aAAa,iBAAIT,OAAA,CAAC8D,eAAe;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7C,CAAC;AAEd,CAAC;AAAAgC,GAAA,CA7DKD,WAAW;EAAA,QAEQjF,WAAW,EACdA,WAAW;AAAA;AAAA0F,GAAA,GAH3BT,WAAW;AAgEjB,eAAeA,WAAW;AAAC,IAAAR,EAAA,EAAAO,GAAA,EAAAU,GAAA;AAAAC,YAAA,CAAAlB,EAAA;AAAAkB,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}