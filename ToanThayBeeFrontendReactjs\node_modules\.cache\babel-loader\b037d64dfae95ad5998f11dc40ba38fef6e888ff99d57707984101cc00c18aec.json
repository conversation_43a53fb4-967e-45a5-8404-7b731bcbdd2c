{"ast": null, "code": "// src/app/store.js\nimport { configureStore } from '@reduxjs/toolkit';\nimport authReducer from '../features/auth/authSlice';\nimport sidebarReducer from '../features/sidebar/sidebarSlice';\nimport usersReducer from '../features/user/userSlice';\nimport filterReducer from '../features/filter/filterSlice';\nimport questionReducer from '../features/question/questionSlice';\nimport codeReducer from '../features/code/codeSlice';\nimport stateReducer from '../features/state/stateApiSlice';\nimport examReducer from '../features/exam/examSlice';\nimport classReducer from '../features/class/classSlice';\nimport answerReducer from '../features/answer/answerSlice';\nimport attemptReducer from '../features/attempt/attemptSlice';\nimport articleReducer from '../features/article/articleSlice';\nimport imageReducer from '../features/image/imageSlice';\nimport achievementReducer from '../features/achievement/achievementSlice';\nimport questionReportReducer from '../features/questionReport/questionReportSlice';\nimport notificationReducer from '../features/notification/notificationSlice';\nimport tuitionReducer from '../features/tuition/tuitionSlice';\nimport attendanceReducer from '../features/attendance/attendanceSlice';\nimport lessonReducer from '../features/lesson/lessonSlice';\nimport learningItemReducer from '../features/learningItem/learningItemSlice';\nimport doExamReducer from '../features/doExam/doExamSlice';\nimport sheetReducer from '../features/sheet/sheetSlice';\nimport calendarReducer from '../features/calendar/calendarSlice';\nimport examAIReducer from '../features/examAI/examAISlice';\nimport dashboardReducer from '../features/dashboard/dashboardSlice';\nimport addExamReducer from '../features/addExam/addExamSlice';\nimport questionsExamReducer from '../features/questionsExam/questionsExamSlice';\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    sidebar: sidebarReducer,\n    users: usersReducer,\n    filter: filterReducer,\n    questions: questionReducer,\n    codes: codeReducer,\n    states: stateReducer,\n    exams: examReducer,\n    classes: classReducer,\n    answers: answerReducer,\n    attempts: attemptReducer,\n    articles: articleReducer,\n    images: imageReducer,\n    achievements: achievementReducer,\n    questionReports: questionReportReducer,\n    notifications: notificationReducer,\n    tuition: tuitionReducer,\n    attendances: attendanceReducer,\n    lessons: lessonReducer,\n    learningItems: learningItemReducer,\n    doExam: doExamReducer,\n    sheet: sheetReducer,\n    calendar: calendarReducer,\n    examAI: examAIReducer,\n    dashboard: dashboardReducer,\n    addExam: addExamReducer,\n    questionsExam: questionsExamReducer\n  }\n});", "map": {"version": 3, "names": ["configureStore", "authReducer", "sidebarReducer", "usersReducer", "filterReducer", "questionReducer", "codeReducer", "stateReducer", "examReducer", "classReducer", "answerReducer", "attemptReducer", "articleReducer", "imageReducer", "achievementReducer", "questionReportReducer", "notificationReducer", "tuitionReducer", "attendanceReducer", "lessonReducer", "learningItemReducer", "doExamReducer", "sheetReducer", "calendarReducer", "examAIReducer", "dashboardReducer", "addExamReducer", "questionsExamReducer", "store", "reducer", "auth", "sidebar", "users", "filter", "questions", "codes", "states", "exams", "classes", "answers", "attempts", "articles", "images", "achievements", "questionReports", "notifications", "tuition", "attendances", "lessons", "learningItems", "doExam", "sheet", "calendar", "examAI", "dashboard", "addExam", "questionsExam"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/redux/store.js"], "sourcesContent": ["// src/app/store.js\r\nimport { configureStore } from '@reduxjs/toolkit';\r\nimport authReducer from '../features/auth/authSlice';\r\nimport sidebarReducer from '../features/sidebar/sidebarSlice';\r\nimport usersReducer from '../features/user/userSlice';\r\nimport filterReducer from '../features/filter/filterSlice';\r\nimport questionReducer from '../features/question/questionSlice';\r\nimport codeReducer from '../features/code/codeSlice';\r\nimport stateReducer from '../features/state/stateApiSlice';\r\nimport examReducer from '../features/exam/examSlice';\r\nimport classReducer from '../features/class/classSlice';\r\nimport answerReducer from '../features/answer/answerSlice';\r\nimport attemptReducer from '../features/attempt/attemptSlice';\r\nimport articleReducer from '../features/article/articleSlice';\r\nimport imageReducer from '../features/image/imageSlice';\r\nimport achievementReducer from '../features/achievement/achievementSlice';\r\nimport questionReportReducer from '../features/questionReport/questionReportSlice';\r\nimport notificationReducer from '../features/notification/notificationSlice';\r\nimport tuitionReducer from '../features/tuition/tuitionSlice';\r\nimport attendanceReducer from '../features/attendance/attendanceSlice';\r\nimport lessonReducer from '../features/lesson/lessonSlice';\r\nimport learningItemReducer from '../features/learningItem/learningItemSlice';\r\nimport doExamReducer from '../features/doExam/doExamSlice';\r\nimport sheetReducer from '../features/sheet/sheetSlice';\r\nimport calendarReducer from '../features/calendar/calendarSlice';\r\nimport examAIReducer from '../features/examAI/examAISlice';\r\nimport dashboardReducer from '../features/dashboard/dashboardSlice';\r\nimport addExamReducer from '../features/addExam/addExamSlice';\r\nimport questionsExamReducer from '../features/questionsExam/questionsExamSlice';\r\n\r\nexport const store = configureStore({\r\n    reducer: {\r\n        auth: authReducer,\r\n        sidebar: sidebarReducer,\r\n        users: usersReducer,\r\n        filter: filterReducer,\r\n        questions: questionReducer,\r\n        codes: codeReducer,\r\n        states: stateReducer,\r\n        exams: examReducer,\r\n        classes: classReducer,\r\n        answers: answerReducer,\r\n        attempts: attemptReducer,\r\n        articles: articleReducer,\r\n        images: imageReducer,\r\n        achievements: achievementReducer,\r\n        questionReports: questionReportReducer,\r\n        notifications: notificationReducer,\r\n        tuition: tuitionReducer,\r\n        attendances: attendanceReducer,\r\n        lessons: lessonReducer,\r\n        learningItems: learningItemReducer,\r\n        doExam: doExamReducer,\r\n        sheet: sheetReducer,\r\n        calendar: calendarReducer,\r\n        examAI: examAIReducer,\r\n        dashboard: dashboardReducer,\r\n        addExam: addExamReducer,\r\n        questionsExam: questionsExamReducer,\r\n    },\r\n});\r\n"], "mappings": "AAAA;AACA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAOC,qBAAqB,MAAM,gDAAgD;AAClF,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,iBAAiB,MAAM,wCAAwC;AACtE,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,oBAAoB,MAAM,8CAA8C;AAE/E,OAAO,MAAMC,KAAK,GAAG5B,cAAc,CAAC;EAChC6B,OAAO,EAAE;IACLC,IAAI,EAAE7B,WAAW;IACjB8B,OAAO,EAAE7B,cAAc;IACvB8B,KAAK,EAAE7B,YAAY;IACnB8B,MAAM,EAAE7B,aAAa;IACrB8B,SAAS,EAAE7B,eAAe;IAC1B8B,KAAK,EAAE7B,WAAW;IAClB8B,MAAM,EAAE7B,YAAY;IACpB8B,KAAK,EAAE7B,WAAW;IAClB8B,OAAO,EAAE7B,YAAY;IACrB8B,OAAO,EAAE7B,aAAa;IACtB8B,QAAQ,EAAE7B,cAAc;IACxB8B,QAAQ,EAAE7B,cAAc;IACxB8B,MAAM,EAAE7B,YAAY;IACpB8B,YAAY,EAAE7B,kBAAkB;IAChC8B,eAAe,EAAE7B,qBAAqB;IACtC8B,aAAa,EAAE7B,mBAAmB;IAClC8B,OAAO,EAAE7B,cAAc;IACvB8B,WAAW,EAAE7B,iBAAiB;IAC9B8B,OAAO,EAAE7B,aAAa;IACtB8B,aAAa,EAAE7B,mBAAmB;IAClC8B,MAAM,EAAE7B,aAAa;IACrB8B,KAAK,EAAE7B,YAAY;IACnB8B,QAAQ,EAAE7B,eAAe;IACzB8B,MAAM,EAAE7B,aAAa;IACrB8B,SAAS,EAAE7B,gBAAgB;IAC3B8B,OAAO,EAAE7B,cAAc;IACvB8B,aAAa,EAAE7B;EACnB;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}