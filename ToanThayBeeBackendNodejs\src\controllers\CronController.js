import { 
    manualUserDeactivation, 
    getUserDeactivationStats 
} from '../services/cronJobs.js';

/**
 * Controller để quản lý cron jobs
 */
class CronController {
    
    /**
     * Trigger manual user deactivation
     * POST /api/cron/deactivate-users
     */
    static async triggerUserDeactivation(req, res) {
        try {
            console.log('🔧 Manual user deactivation triggered by admin');
            
            const result = await manualUserDeactivation();
            
            return res.status(200).json({
                success: true,
                message: 'User deactivation completed successfully',
                data: result
            });
            
        } catch (error) {
            console.error('❌ Error in triggerUserDeactivation:', error);
            
            return res.status(500).json({
                success: false,
                message: 'Failed to deactivate users',
                error: error.message
            });
        }
    }
    
    /**
     * Get user deactivation statistics
     * GET /api/cron/deactivation-stats
     */
    static async getDeactivationStats(req, res) {
        try {
            console.log('📊 Getting user deactivation statistics');
            
            const stats = await getUserDeactivationStats();
            
            return res.status(200).json({
                success: true,
                message: 'User deactivation statistics retrieved successfully',
                data: stats
            });
            
        } catch (error) {
            console.error('❌ Error in getDeactivationStats:', error);
            
            return res.status(500).json({
                success: false,
                message: 'Failed to get deactivation statistics',
                error: error.message
            });
        }
    }
    
    /**
     * Get cron job status and information
     * GET /api/cron/status
     */
    static async getCronStatus(req, res) {
        try {
            const currentYear = new Date().getFullYear();
            const nextJuly = new Date(currentYear, 6, 1); // July 1st of current year
            
            // If July 1st has passed this year, next run is July 1st next year
            if (nextJuly < new Date()) {
                nextJuly.setFullYear(currentYear + 1);
            }
            
            const cronInfo = {
                status: 'active',
                description: 'Annual user deactivation cron job',
                schedule: '0 0 1 7 *', // Cron pattern
                scheduleDescription: 'Runs at 00:00 on July 1st every year',
                timezone: 'Asia/Ho_Chi_Minh',
                nextRun: nextJuly.toISOString(),
                currentYear: currentYear,
                criteria: `graduationYear = ${currentYear} OR graduationYear IS NULL`,
                action: 'Set isActive = false for matching users'
            };
            
            return res.status(200).json({
                success: true,
                message: 'Cron job status retrieved successfully',
                data: cronInfo
            });
            
        } catch (error) {
            console.error('❌ Error in getCronStatus:', error);
            
            return res.status(500).json({
                success: false,
                message: 'Failed to get cron status',
                error: error.message
            });
        }
    }
    
    /**
     * Preview users that will be deactivated (without actually deactivating)
     * GET /api/cron/preview-deactivation
     */
    static async previewDeactivation(req, res) {
        try {
            console.log('👀 Previewing users that will be deactivated');
            
            const stats = await getUserDeactivationStats();
            
            const preview = {
                currentYear: stats.currentYear,
                totalUsersToDeactivate: stats.totalUsersToDeactivate,
                criteria: stats.criteria,
                users: stats.users.map(user => ({
                    id: user.id,
                    username: user.username,
                    graduationYear: user.graduationYear,
                    isActive: user.isActive,
                    createdAt: user.createdAt
                }))
            };
            
            return res.status(200).json({
                success: true,
                message: 'Deactivation preview retrieved successfully',
                data: preview
            });
            
        } catch (error) {
            console.error('❌ Error in previewDeactivation:', error);
            
            return res.status(500).json({
                success: false,
                message: 'Failed to preview deactivation',
                error: error.message
            });
        }
    }
}

export default CronController;
