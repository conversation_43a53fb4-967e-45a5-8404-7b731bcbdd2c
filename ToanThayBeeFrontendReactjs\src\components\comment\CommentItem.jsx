import { useEffect, useState } from "react";
import { Send } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";
import { useSelector } from "react-redux";
import EmojiPicker from "./EmojiPicker";
import CommentInput from "./CommentInput";
import { fetchRepliesByCommentId } from "src/features/comments/ExamCommentsSlice";
import { useDispatch } from "react-redux";

const CommentItem = ({ comment, user, codes, onUpdate, onDelete, onReply, isChild }) => {
    const [editingId, setEditingId] = useState(null);
    const [editContent, setEditContent] = useState("");
    const [showReply, setShowReply] = useState(false);
    const [replyContent, setReplyContent] = useState("");

    const [showReplies, setShowReplies] = useState(false);
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const { replies: repliesState } = useSelector((state) => state.comments);
    const [replies, setReplies] = useState([]);

    const handleShowReplies = async () => {
        setShowReplies(true);
        setLoading(true);
        await dispatch(fetchRepliesByCommentId({ commentId: comment.id }))
            .unwrap()
            .then((data) => {
                setLoading(false);
            })
            .catch((error) => {
                setLoading(false);
            });
    };

    useEffect(() => {
        if (Array.isArray(repliesState[comment.id])) {
            setReplies(repliesState[comment.id]);
        }
    }, [repliesState[comment.id]]);

    const startEditing = () => {
        setEditingId(comment.id);
        setEditContent(comment.content);
    };

    const startReplying = () => {
        setShowReply(true);
        setReplyContent("");
    };

    const handleUpdate = (content) => {
        if (content.trim() === "") return;
        onUpdate?.(comment.id, content);
        setEditingId(null);
        setEditContent("");
    };

    const handleCancel = () => {
        setEditingId(null);
        setEditContent("");
    };

    const handleOnReply = (content, parentCommentId) => {
        if (content.trim() === "") return;
        onReply?.(content, parentCommentId);
        setShowReply(false);
        setReplyContent("");
    };

    const isOwner = user?.id === comment.userId;
    const userInfo = comment.user || {};

    return (
        <div className="flex flex-col w-full">
            <div className="flex items-start gap-3 w-full ">
                <div className={`relative ${isChild ? "w-7" : "w-8"} flex flex-col items-center h-full`}>
                    {/* Avatar */}
                    <div className={`${isChild ? "w-7 h-7" : "w-8 h-8"} rounded-full bg-gray-300 flex items-center justify-center text-white font-bold text-sm z-10`}>
                        {userInfo?.avatarUrl ? (
                            <img src={userInfo?.avatarUrl} alt="avatar" className="w-full h-full object-cover rounded-full" />
                        ) : (userInfo?.firstName?.[0] ?? "") + (userInfo?.lastName?.[0] ?? "")}
                    </div>

                    {/* Đường kẻ kéo xuống */}
                    <div className={`absolute top-0 h-full bg-gray-300 w-[2px] ${comment.replyCount > 0 || showReply ? "block" : "hidden"}`} />

                </div>


                <div className="flex-1">
                    <div className="flex flex-row gap-2 group">
                        <div className={`bg-gray-100 px-3 py-2 rounded-2xl relative ${editingId === comment.id ? "w-full" : "w-fit"}`}>
                            <p className={`text-xs font-semibold text-gray-800 ${editingId === comment.id ? "hidden" : ""}`}>
                                {userInfo?.firstName} {userInfo?.lastName + " "}
                                <span className="text-xs text-gray-500">
                                    {userInfo?.userType !== "HS1" && codes?.["user type"]?.find(c => c.code === userInfo?.userType)?.description}
                                </span>
                            </p>

                            {editingId === comment.id ? (
                                <CommentInput onSend={handleUpdate} value={editContent} />
                            ) : (
                                <p className="text-sm text-gray-800 whitespace-pre-wrap">{comment.content}</p>
                            )}
                        </div>

                        {isOwner && editingId !== comment.id && (
                            <div className="hidden group-hover:flex gap-2 text-xs text-gray-500">
                                <button onClick={startEditing} className="hover:underline">Sửa</button>
                                <button onClick={() => onDelete?.(comment.id)} className="hover:underline">Xoá</button>
                            </div>
                        )}

                        {isOwner && editingId === comment.id && (
                            <div className="flex gap-2 text-xs text-gray-500">
                                <button onClick={handleCancel} className="hover:underline">Hủy</button>
                            </div>
                        )}
                    </div>

                    <div className="flex items-center gap-3 text-xs text-gray-500 mt-0.5 pl-2">
                        <span>{formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true, locale: vi })}</span>
                        <button onClick={() => startReplying()} className={`hover:underline ${isChild ? "hidden" : "block"}`}>Trả lời</button>
                    </div>

                </div>
            </div>
            {!showReplies && (
                <div className={`relative flex-row py-2 items-center ${comment.replyCount > 0 ? "flex" : "hidden"}`}>
                    <div className=" h-full w-[29px] pb-[8px]">
                        <div className="h-full ml-[15px] w-full border-l-2 border-b-2 border-gray-300 rounded-bl-xl" >
                        </div>
                    </div>
                    <div className={`absolute left-[15px] top-0 bottom-0 bg-gray-300 w-[2px] ${showReply ? "h-full" : "h-1/4"}`} />
                    <div
                        onClick={handleShowReplies}
                        className="text-sm text-gray-500 pl-6 cursor-pointer">{comment.replyCount} phản hồi</div>
                </div>
            )}
            {replies.length > 0 && showReplies && (
                replies.map((reply, index) => (
                    <div className={`relative flex-row py-2 items-center ${comment.replyCount > 0 ? "flex" : "hidden"}`}>
                        <div className=" h-full w-[29px] pb-[50px]">
                            <div className="h-full ml-[15px] w-full border-l-2 border-b-2 border-gray-300 rounded-bl-xl" >
                            </div>

                        </div>
                        <div className={`${replies.length === index + 1 && !showReply ? "h-1/5" : ""} absolute left-[15px] top-0 bottom-0 bg-gray-300 w-[2px]`} />
                        <div className="pl-6 w-full">
                            <CommentItem
                                key={reply.id}
                                comment={reply}
                                user={user}
                                codes={codes}
                                onUpdate={onUpdate}
                                onDelete={onDelete}
                                onReply={onReply}
                                isChild={true}
                            />
                        </div>
                    </div>
                ))
            )}
            {showReply && (
                <div className="relative flex-row py-2 items-center flex">
                    <div className="relative h-full w-[29px] pb-[15px]">
                        <div className="h-full ml-[15px] w-full border-l-2 border-b-2 border-gray-300 rounded-bl-xl" >
                        </div>
                    </div>
                    <div className="absolute left-[15px] top-0 h-1/3 bg-gray-300 w-[2px]" />

                    <div className="pl-6 w-full">
                        <CommentInput onReply={handleOnReply} parentCommentId={comment.id} value={replyContent} />
                    </div>
                </div>
            )}
        </div>
    );
};

export default CommentItem;
