import db from "../models/index.js"
import UserType from "../constants/UserType.js"
import { uploadImage, cleanupUploadedFiles } from "../utils/imageUpload.js"
import { Op, or, where } from "sequelize";
import { uploadPdfToFirebase, deletePdfFromFirebase } from "../utils/pdfUpload.js"
import { sendUserNotification } from "../utils/notificationUtils.js"
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js"

const Exam = db.Exam

export const getExamsWithFilter = async ({ sortOrder = 'DESC', search = '', page = 1, limit = 10 }) => {
    const offset = (page - 1) * limit

    let whereClause = {}
    if (search.trim() !== '') {
        whereClause = {
            [Op.or]: [
                { name: { [Op.like]: `%${search}%` } },
                { description: { [Op.like]: `%${search}%` } },
                { chapter: { [Op.like]: `%${search}%` } },
                { year: { [Op.like]: `%${search}%` } },
                { class: { [Op.like]: `%${search}%` } },
                { typeOfExam: { [Op.like]: `%${search}%` } }
            ]
        }
    }

    const [examList, total] = await Promise.all([
        db.Exam.findAll({
            where: whereClause,
            offset,
            limit,
            order: [['createdAt', sortOrder]]
        }),
        db.Exam.count({
            where: whereClause
        })
    ])

    return new ResponseDataPagination(examList, {
        total,
        page,
        pageSize: limit,
        totalPages: Math.ceil(total / limit),
        sortOrder,
    });
}