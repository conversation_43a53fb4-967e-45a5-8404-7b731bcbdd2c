import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical } from 'lucide-react';
import QuestionContent from './QuestionContent';

const SortableQuestionItem = ({ question, index }) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({ id: question.id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };

    return (
        <div ref={setNodeRef} style={style} className="relative">
            {/* Drag Handle */}
            <div
                {...attributes}
                {...listeners}
                className="absolute left-2 top-4 z-10 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-200 transition-colors"
                title="Kéo để sắp xếp lại thứ tự"
            >
                <GripVertical size={16} className="text-gray-400" />
            </div>

            {/* Question Content with left padding for drag handle */}
            <div className="pl-8">
                <QuestionContent question={question} index={index} />
            </div>
        </div>
    );
};

export default SortableQuestionItem;
