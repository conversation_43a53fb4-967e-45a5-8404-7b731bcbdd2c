{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\loading\\\\LoadingText.jsx\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LoadingText = _ref => {\n  let {\n    children,\n    loading,\n    color = 'bg-gray-300',\n    w = 'w-48',\n    h = 'h-4'\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"rounded-full animate-pulse \".concat(color, \" \").concat(w, \" \").concat(h)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 17\n    }, this) : children\n  }, void 0, false);\n};\n_c = LoadingText;\nexport default LoadingText;\nvar _c;\n$RefreshReg$(_c, \"LoadingText\");", "map": {"version": 3, "names": ["LoadingText", "_ref", "children", "loading", "color", "w", "h", "_jsxDEV", "_Fragment", "className", "concat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/loading/LoadingText.jsx"], "sourcesContent": ["const LoadingText = ({ children, loading, color = 'bg-gray-300', w = 'w-48', h = 'h-4' }) => {\r\n    return (\r\n        <>\r\n            {loading ? (\r\n                <div className={`rounded-full animate-pulse ${color} ${w} ${h}`}></div>\r\n            ) : (\r\n                children\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\nexport default LoadingText;"], "mappings": ";;AAAA,MAAMA,WAAW,GAAGC,IAAA,IAAyE;EAAA,IAAxE;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,KAAK,GAAG,aAAa;IAAEC,CAAC,GAAG,MAAM;IAAEC,CAAC,GAAG;EAAM,CAAC,GAAAL,IAAA;EACpF,oBACIM,OAAA,CAAAC,SAAA;IAAAN,QAAA,EACKC,OAAO,gBACJI,OAAA;MAAKE,SAAS,gCAAAC,MAAA,CAAgCN,KAAK,OAAAM,MAAA,CAAIL,CAAC,OAAAK,MAAA,CAAIJ,CAAC;IAAG;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,GAEvEZ;EACH,gBACH,CAAC;AAEX,CAAC;AAACa,EAAA,GAVIf,WAAW;AAYjB,eAAeA,WAAW;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}