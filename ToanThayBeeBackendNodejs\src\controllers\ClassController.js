import db from "../models/index.js"
import ResponseClass from "../dtos/responses/class/ClassResponse.js"
const { Class } = db
import StudentClassStatus from "../constants/StudentClassStatus.js"
import { Op } from "sequelize"
import { cleanupUploadedFiles } from "../utils/imageUpload.js"
import * as classService from "../services/class.service.js"
import * as slideImageService from "../services/slideImage.service.js"
import * as lessonService from "../services/lesson.service.js"
import * as learningItemService from "../services/learningItem.service.js"
import * as notificationService from "../services/notification.service.js"
import { sendUserNotification } from "../utils/notificationUtils.js"
import { updateUserClassInAllSheets, removeUserClassFromAllSheets } from "../services/googleSheets.service.js"
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js"

const { sequelize } = db;

export const getPublicClass = async (req, res) => {
    const classes = await Class.findAll({
        where: {
            status: 'LHD',
            public: true,
        },
        order: [['createdAt', 'DESC']],
    })

    return res.status(200).json({
        message: 'Lấy danh sách lớp thành công!',
        data: classes,
        totalItems: classes.length,
    })
}

export const getAllClass = async (req, res) => {
    const search = req.query.search || ''
    const page = parseInt(req.query.page, 10) || 1
    const limit = parseInt(req.query.limit, 10) || 10
    const sortOrder = req.query.sortOrder || 'DESC'

    const result = await classService.getAllClasses({ search, page, limit, sortOrder });

    const formattedClasses = await Promise.all(
        result.classes.map(async (classRecord) => {
            // Tính số học sinh đã tham gia lớp
            const studentCount = await classService.getStudentCount(classRecord.id);

            return {
                ...new ResponseClass(classRecord),
                studentCount, // Vẫn trả về studentCount cho client nhưng tính toán động
            };
        })
    );

    res.status(200).json({
        message: 'Lấy danh sách lớp thành công!',
        data: new ResponseDataPagination(formattedClasses, {
            total: result.total,
            page: result.page,
            pageSize: result.pageSize,
            totalPages: result.totalPages,
            sortOrder: sortOrder,
        }),
    });
}

export const getDetailClassByAdmin = async (req, res) => {
    const id = req.params.id;

    const classItem = await classService.getClassDetailById(id);

    if (!classItem) {
        return res.status(404).json({
            message: `Không tìm thấy lớp với ID: ${id}!`
        });
    }

    return res.status(200).json({
        message: 'Lấy thông tin lớp thành công!',
        data: classItem
    });
};


export const getDetailClassByUser = async (req, res) => {
    const userId = req.user.id;
    const { classCode } = req.params;

    const classItem = await classService.getClassDetailByClassCode(classCode);

    if (!classItem) {
        return res.status(404).json({
            message: `Không tìm thấy lớp với mã: ${classCode}!`
        });
    }

    const status = await classService.getStatusByUserIdAndClassId(userId, classItem.id);

    return res.status(200).json({
        message: 'Lấy thông tin lớp thành công!',
        data: classItem,
        studentClassStatus: status
    });
};

export const getClassByUser = async (req, res) => {
    const userId = req.user.id;
    // Lấy tất cả lớp người dùng đã tham gia
    const classes = await classService.getClassesByUserId(userId);

    // Format lại danh sách + tính sĩ số từng lớp
    const formattedClasses = await Promise.all(
        classes.map(async (classRecord) => {
            const lop = classRecord.class;
            const status = classRecord.status;

            // Tính số học sinh đã tham gia lớp
            const studentCount = await classService.getStudentCount(lop.id);

            return {
                ...new ResponseClass(lop, status),
                studentCount, // Vẫn trả về studentCount cho client nhưng tính toán động
            };
        })
    );

    return res.status(200).json({
        message: 'Lấy danh sách lớp theo người dùng thành công!',
        data: formattedClasses,
        totalItems: formattedClasses.length,
    });
};

export const getClassByUserId = async (req, res) => {
    const userId = req.params.userId;
    // Lấy tất cả lớp người dùng đã tham gia
    const classes = await classService.getClassesByUserId(userId);

    // Format lại danh sách + tính sĩ số từng lớp
    const formattedClasses = await Promise.all(
        classes.map(async (classRecord) => {
            const lop = classRecord.class;
            const status = classRecord.status;

            // Tính số học sinh đã tham gia lớp
            const studentCount = await classService.getStudentCount(lop.id);

            return {
                ...new ResponseClass(lop, status),
                studentCount, // Vẫn trả về studentCount cho client nhưng tính toán động
            };
        })
    );

    return res.status(200).json({
        message: 'Lấy danh sách lớp theo người dùng thành công!',
        data: formattedClasses,
        totalItems: formattedClasses.length,
    });
};


export const findClasses = async (req, res) => {
    try {
        const { search } = req.query;
        const limit = 20; // Giới hạn 20 kết quả

        // Nếu không có tham số search, trả về 20 lớp được cập nhật gần đây nhất
        if (!search || search.trim() === '') {
            const recentClasses = await db.Class.findAll({
                order: [['updatedAt', 'DESC']],
                limit,

                where: {
                    status: 'LHD',
                },
            });

            return res.status(200).json({
                message: 'Lấy danh sách lớp gần đây thành công',
                data: recentClasses,
            });
        }
        // Tìm kiếm theo nhiều tiêu chí
        const searchTerm = search.trim();
        console.log(searchTerm);
        // Tạo điều kiện tìm kiếm
        const whereCondition = {
            status: 'LHD',
            [Op.or]: [
                { id: { [Op.like]: `%${searchTerm}%` } },
                { class_code: { [Op.like]: `%${searchTerm}%` } },
                { name: { [Op.like]: `%${searchTerm}%` } },
                { description: { [Op.like]: `%${searchTerm}%` } },
                { status: { [Op.like]: `%${searchTerm}%` } },
            ],
        };

        // Thực hiện tìm kiếm
        const classes = await db.Class.findAll({
            where: whereCondition,
            limit,
            order: [['updatedAt', 'DESC']],

        });

        return res.status(200).json({
            message: 'Tìm kiếm lớp thành công',
            data: classes,
            totalItems: classes.length,
        });
    } catch (error) {
        console.error('Lỗi khi tìm kiếm lớp:', error);
        return res.status(500).json({
            message: 'Lỗi server khi tìm kiếm lớp',
            error: error.message,
        });
    }
}

export const findClassByCode = async (req, res) => {
    const { classCode } = req.params;
    // Tìm kiếm lớp học theo mã lớp
    const classItem = await db.Class.findOne({
        where: {
            class_code: classCode,
            public: true, // Chỉ tìm lớp công khai
        },
        attributes: ['id'],
    });

    if (!classItem) {
        return res.status(404).json({
            message: `Không tìm thấy lớp học với mã: ${classCode}!`,
            data: false,
        });
    }
    return res.status(200).json({
        message: 'Lấy thông tin lớp học thành công',
        data: true,
    });
}



export const getOverviewClass = async (req, res) => {
    const { id } = req.user

    const classes = await classService.getOverviewClasses(id);

    res.status(200).json({
        message: "Lấy lớp học hôm nay thành công",
        data: classes,
    });
}

export const getDetailLessonLearningItemByClassId = async (req, res) => {
    const { classCode } = req.params;
    const userId = req.user?.id; // 👈 user hiện tại từ middleware xác thực

    const foundClass = await classService.getDetailLessonLearningItemByClassId(classCode);

    if (!foundClass) {
        return res.status(404).json({
            message: 'Không tìm thấy lớp học với classId đã cung cấp!',
            data: null,
        });
    }

    foundClass.lessons = foundClass.lessons.sort((a, b) => {
        // So sánh ngày theo thứ tự giảm dần (DESC)
        return new Date(a.day) - new Date(b.day);
    });

    // Đếm số học sinh đã tham gia lớp (status === 'JS')
    const studentCount = await classService.getStudentCount(foundClass.id);

    // Lấy trạng thái của người dùng hiện tại trong lớp
    let userStatus = null;
    if (userId) {
        userStatus = await classService.getStatusByUserIdAndClassId(userId, foundClass.id);
    }

    return res.status(200).json({
        message: 'Lấy thông tin lớp, bài học, mục học tập và trạng thái người dùng thành công!',
        data: {
            ...foundClass.toJSON(),
            joinedStudentCount: studentCount,
            userStatus
        },
    });
};

export const getFullLessonLearningItemByClassCode = async (req, res) => {
    const { classCode } = req.params;
    const userId = req.user?.id; // 👈 user hiện tại từ middleware xác thực

    const foundClass = await classService.getFullLessonLearningItemByClassCode(classCode, userId);

    if (!foundClass) {
        return res.status(404).json({
            message: 'Không tìm thấy lớp học với classCode đã cung cấp!',
            data: null,
        });
    }

    // Lấy trạng thái của người dùng trong lớp học
    let userStatus = null;
    if (userId) {
        userStatus = await classService.getStatusByUserIdAndClassId(userId, foundClass.id);
    }

    if (userStatus !== 'JS') {
        return res.status(403).json({
            message: 'Bạn không thể xem nội dung này vì chưa tham gia lớp học!',
            data: null,
        });
    }

    foundClass.lessons = foundClass.lessons.sort((a, b) => {
        return new Date(a.day) - new Date(b.day);
    });

    return res.status(200).json({
        message: 'Lấy thông tin lớp, bài học, mục học tập và trạng thái người dùng thành công!',
        data: {
            ...foundClass.toJSON(),
            userStatus
        },
    });
};


export const getFullLessonByClassID = async (req, res) => {
    const { id } = req.params;

    const foundClass = await classService.getFullLessonByClassID(id);

    if (!foundClass) {
        return res.status(404).json({
            message: 'Không tìm thấy lớp học với classId đã cung cấp!',
            data: null,
        });
    }

    return res.status(200).json({
        message: 'Lấy thông tin lớp, bài học, mục học tập thành công!',
        data: {
            ...foundClass.toJSON(),
        },
    });
};

export const postClass = async (req, res) => {
    const newClass = await classService.postClass(req.body);

    return res.status(201).json({
        message: 'Tạo lớp học thành công',
        newClass,
    });
};

export const putClass = async (req, res) => {
    const { id } = req.params

    const forbiddenFields = ['id', 'createdAt', 'updatedAt']

    const updatedData = Object.keys(req.body)
        .filter(key => !forbiddenFields.includes(key))
        .reduce((obj, key) => {
            obj[key] = req.body[key]
            return obj
        }, {})

    if (Object.keys(updatedData).length === 0) {
        return res.status(400).json({ message: 'Không có trường hợp lệ để cập nhật.' })
    }

    const updatedClass = await classService.putClass(id, updatedData)

    return res.status(200).json({ message: 'Cập nhật lớp học thành công', data: new ResponseClass(updatedClass) })
}

export const deleteClass = async (req, res) => {
    const { id } = req.params

    // Bắt đầu transaction
    const transaction = await sequelize.transaction();

    try {
        // 0. Tìm lớp học
        const classItem = await classService.getClassById(id, { transaction });
        if (!classItem) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Lớp học không tồn tại' });
        }

        // 1. Tìm Slide gắn với Class (dựa vào classItem.slideId)
        if (classItem.slideId) {
            try {
                // Lấy thông tin slide và các ảnh liên quan
                const slide = await slideImageService.getSlideById(classItem.slideId, { transaction });
                if (slide) {
                    const slideImages = await slideImageService.getSlideImageBySlideId(classItem.slideId, { transaction });

                    // Lưu lại URLs của ảnh để xóa sau khi transaction thành công
                    const imageUrls = slideImages.map(img => img.imageUrl).filter(url => url);

                    // Xóa slide và các ảnh liên quan
                    await slideImageService.deleteSlideImage(classItem.slideId, imageUrls, transaction);

                }
            } catch (slideError) {
                console.error('Lỗi khi xóa slide:', slideError);
                // Tiếp tục xử lý, không throw lỗi ở đây
            }
        }

        // 2. Xóa các bản ghi StudentClassStatus liên quan đến lớp học
        // await classService.deleteAllStudentInClass(id, transaction);


        // 3. Tìm và xóa tất cả các bài học và mục học tập liên quan
        const lessons = await lessonService.getLessonByClassId(id, transaction);

        const lessonIds = lessons.map(lesson => lesson.id);

        if (lessonIds.length > 0) {
            // 4. Xóa tất cả các mục học tập liên quan đến các bài học
            await learningItemService.deleteAllLearningItemByLessonId(lessonIds, transaction);

            // 5. Xóa tất cả các bài học
            await lessonService.deleteLessons(lessonIds, transaction);
        }

        // 6. Xóa Class - sử dụng service đã được cập nhật để hỗ trợ transaction
        await classService.deleteClass(id, transaction);

        // Commit transaction
        await transaction.commit();

        return res.status(200).json({ message: 'Xóa lớp học thành công' });
    } catch (error) {
        // Rollback transaction nếu có lỗi
        await transaction.rollback();
        console.error('Lỗi khi xóa lớp học:', error);
        return res.status(500).json({ message: 'Lỗi server khi xóa lớp học', error: error.message });
    }
}

export const putSlideImagesForClass = async (req, res) => {
    const transaction = await sequelize.transaction();
    const { id: classId } = req.params;
    const slideId = req.body.slideId || null;
    const files = req.files?.images || [];
    const keepImageIds = req.body.keepImageIds ? JSON.parse(req.body.keepImageIds) : [];

    try {
        // Sử dụng service để xử lý cập nhật slide
        const result = await slideImageService.processSlideImagesUpdate(
            classId,
            slideId,
            files,
            keepImageIds,
            transaction
        );

        if (!result.success) {
            throw result.error;
        }

        await transaction.commit();
        return res.status(200).json({ message: 'Cập nhật slide thành công.' });
    } catch (error) {
        console.error("Lỗi khi cập nhật slide:", error);

        // Xóa ảnh đã upload thành công nếu có
        if (error.uploadedUrls && error.uploadedUrls.length > 0) {
            await cleanupUploadedFiles(error.uploadedUrls);
        }

        await transaction.rollback();
        return res.status(500).json({
            message: 'Lỗi server khi cập nhật slide.',
            error: error.message || 'Lỗi không xác định'
        });
    }
};

export const joinClass = async (req, res) => {
    const userId = req.user.id;
    const { classCode } = req.params; // 👈 lấy mã lớp từ URL

    // Mở transaction
    const transaction = await db.sequelize.transaction();

    try {
        // 1. Tìm lớp theo mã lớp
        const classInfo = await classService.getClassByClassCode(classCode);
        // Không tìm thấy hoặc lớp không công khai
        if (!classInfo || !classInfo.public) {
            await transaction.rollback();
            return res.status(400).json({ message: "Không thể tham gia lớp học này!" });
        }

        // 2. Thêm học sinh vào bảng StudentClassStatus
        const insert = await classService.createStudentInClass(userId, classInfo.id, StudentClassStatus.WAITED, transaction);

        if (!insert) {
            await transaction.rollback();
            return res.status(500).json({ message: "Tham gia lớp học không thành công!" });
        }

        await notificationService.createNotification({
            userId,
            title: "Gửi yêu cầu tham gia lớp học thành công",
            content: `Bạn đã gửi yêu cầu tham gia lớp học "${classInfo.name}" thành công.`,
            type: "CLASS",
            relatedId: classInfo.id,
            relatedType: "CLASS",
            actionUrl: `/class/${classInfo.class_code}`,
            isRead: false
        });

        // try {
        //     const io = req.app.get('io');
        //     if (io) {
        //         await sendUserNotification(
        //             io,
        //             userId,
        //             "Tham gia lớp học thành công",
        //             `Bạn đã tham gia lớp học "${classInfo.name}" thành công`,
        //             "CLASS",
        //             classInfo.id,
        //             "CLASS",
        //             `/class/${classInfo.class_code}`
        //         );
        //     }
        // } catch (notificationError) {
        //     // Không ảnh hưởng đến kết quả trả về
        // }

        // 3. Commit transaction
        await transaction.commit();

        return res.status(200).json({ message: "Tham gia lớp học thành công!" });

    } catch (error) {
        await transaction.rollback();
        console.error("Lỗi khi tham gia lớp học:", error);
        return res.status(500).json({ message: "Lỗi server khi tham gia lớp học!" });
    }
};

export const addStudentToClass = async (req, res) => {
    const { studentId, classId } = req.params;

    const transaction = await db.sequelize.transaction();

    try {
        // Kiểm tra xem học sinh đã có trong lớp chưa
        const existingStatus = await classService.getStatusByUserIdAndClassId(studentId, classId);
        if (existingStatus !== StudentClassStatus.NOT_JOINED) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Học sinh đã có trong lớp học này!' });
        }

        // Kiểm tra xem học sinh có tồn tại không
        const student = await db.User.findByPk(studentId, { transaction });
        if (!student) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Không tìm thấy học sinh!' });
        }

        // Lấy thông tin lớp học
        const classInfo = await classService.getClassById(classId, { transaction });
        if (!classInfo) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Không tìm thấy thông tin lớp học!' });
        }

        // Thêm học sinh vào lớp với trạng thái JOINED
        await classService.createStudentInClass(studentId, classId, StudentClassStatus.JOINED, transaction);

        await learningItemService.createStatusesForStudent(studentId, classId, transaction)

        // Commit transaction trước khi gửi thông báo
        await transaction.commit();

        // Cập nhật Google Sheets (nếu có)
        try {
            const sheetsResult = await updateUserClassInAllSheets(studentId, classInfo.name);
            console.log('Kết quả cập nhật Google Sheets:', sheetsResult);
        } catch (sheetsError) {
            console.error('Lỗi khi cập nhật Google Sheets:', sheetsError);
            // Không ảnh hưởng đến kết quả trả về nếu cập nhật Google Sheets thất bại
        }

        await notificationService.createNotification({
            userId: studentId,
            title: "Đã được thêm vào lớp học",
            content: `Bạn đã được thêm vào lớp học "${classInfo.name}". Bây giờ bạn có thể xem đầy đủ nội dung của lớp học.`,
            type: "CLASS",
            relatedId: classId,
            relatedType: "CLASS",
            actionUrl: `/class/${classInfo.class_code}`,
            isRead: false
        });

        // // Gửi thông báo cho học sinh
        // try {
        //     const io = req.app.get('io');
        //     if (io) {
        //         await sendUserNotification(
        //             io,
        //             studentId,
        //             "Đã được thêm vào lớp học",
        //             `Bạn đã được thêm vào lớp học "${classInfo.name}". Bây giờ bạn có thể xem đầy đủ nội dung của lớp học.`,
        //             "CLASS",
        //             classId,
        //             "CLASS",
        //             `/class/${classInfo.class_code}`
        //         );
        //     }
        // } catch (notificationError) {
        //     console.error('Lỗi khi gửi thông báo:', notificationError);
        //     // Không ảnh hưởng đến kết quả trả về nếu gửi thông báo thất bại
        // }

        return res.status(200).json({
            message: 'Thêm học sinh vào lớp học thành công!',
            data: {
                studentId,
                classId,
                className: classInfo.name,
                studentName: student.fullName
            }
        });

    } catch (error) {
        console.error('Error adding student to class:', error);
        await transaction.rollback();
        return res.status(500).json({ message: 'Có lỗi xảy ra khi thêm học sinh vào lớp học.' });
    }
};

export const acceptStudentJoinClass = async (req, res) => {
    const { studentId, classId } = req.params;

    const transaction = await db.sequelize.transaction();

    try {
        // Cập nhật trạng thái học sinh trong lớp học
        await classService.updateStudentStatusInClass(studentId, classId, StudentClassStatus.JOINED, transaction);

        // Lấy thông tin lớp học để hiển thị trong thông báo
        const classInfo = await classService.getClassById(classId, { transaction });
        if (!classInfo) {
            throw new Error('Không tìm thấy thông tin lớp học');
        }

        await learningItemService.createStatusesForStudent(studentId, classId, transaction)

        // Commit transaction trước khi gửi thông báo
        await transaction.commit();

        // Cập nhật Google Sheets (nếu có)
        try {
            const sheetsResult = await updateUserClassInAllSheets(studentId, classInfo.name);
            console.log('Kết quả cập nhật Google Sheets:', sheetsResult);
        } catch (sheetsError) {
            console.error('Lỗi khi cập nhật Google Sheets:', sheetsError);
            // Không ảnh hưởng đến kết quả trả về nếu cập nhật Google Sheets thất bại
        }

        await notificationService.createNotification({
            userId: studentId,
            title: "Đã được chấp nhận vào lớp học",
            content: `Bạn đã được chấp nhận vào lớp học "${classInfo.name}". Bây giờ bạn có thể xem đầy đủ nội dung của lớp học.`,
            type: "CLASS",
            relatedId: classId,
            relatedType: "CLASS",
            actionUrl: `/class/${classInfo.class_code}`,
            isRead: false
        });

        // // Gửi thông báo cho học sinh
        // try {
        //     const io = req.app.get('io');
        //     if (io) {
        //         await sendUserNotification(
        //             io,
        //             studentId,
        //             "Đã được chấp nhận vào lớp học",
        //             `Bạn đã được chấp nhận vào lớp học "${classInfo.name}". Bây giờ bạn có thể xem đầy đủ nội dung của lớp học.`,
        //             "CLASS",
        //             classId,
        //             "CLASS",
        //             `/class/${classInfo.class_code}`
        //         );
        //     }
        // } catch (notificationError) {
        //     console.error('Lỗi khi gửi thông báo:', notificationError);
        //     // Không ảnh hưởng đến kết quả trả về nếu gửi thông báo thất bại
        // }

        return res.status(200).json({ message: 'Chấp nhận học viên tham gia lớp học thành công!' });

    } catch (error) {
        console.error('Error accepting student into class:', error);
        await transaction.rollback();
        return res.status(500).json({ message: 'Có lỗi xảy ra khi chấp nhận học viên tham gia lớp học.' });
    }
};

export const kickStudentFromClass = async (req, res) => {
    const { studentId, classId } = req.params;

    // Bắt đầu transaction
    const transaction = await db.sequelize.transaction();

    try {
        // Lấy thông tin lớp học trước khi xóa học sinh
        const classInfo = await classService.getClassById(classId, { transaction });
        if (!classInfo) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Không tìm thấy thông tin lớp học' });
        }

        // Xóa học sinh khỏi lớp học
        const deleteRecord = await classService.deleteStudenInClass(studentId, classId, transaction);

        // Xóa trạng thái học tập
        const learningItemIds = await learningItemService.getAllLearningItemIdByClassId(classId, transaction);
        await db.StudentStudyStatus.destroy({
            where: {
                studentId,
                learningItemId: {
                    [db.Sequelize.Op.in]: learningItemIds
                }
            },
            transaction
        });

        if (!deleteRecord) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Không tìm thấy học viên trong lớp học!' });
        }

        // ✅ Commit trước khi xử lý phần phụ (Google Sheets, socket)
        await transaction.commit();

        // Xóa tên lớp khỏi Google Sheets (không blocking)
        try {
            const sheetsResult = await removeUserClassFromAllSheets(studentId, classInfo.name);
            console.log('Kết quả xóa lớp khỏi Google Sheets:', sheetsResult);
        } catch (sheetsError) {
            console.error('Lỗi khi xóa lớp khỏi Google Sheets:', sheetsError);
        }

        // Gửi thông báo cho học sinh
        await notificationService.createNotification({
            userId: studentId,
            title: "Đã bị xóa khỏi lớp học",
            content: `Bạn đã bị xóa khỏi lớp học "${classInfo.name}".`,
            type: "CLASS",
            relatedId: classId,
            relatedType: "CLASS",
            actionUrl: `/class`,
            isRead: false
        });
        // try {
        //     const io = req.app.get('io');
        //     if (io) {
        //         await sendUserNotification(
        //             io,
        //             studentId,
        //             "Đã bị xóa khỏi lớp học",
        //             `Bạn đã bị xóa khỏi lớp học "${classInfo.name}".`,
        //             "CLASS",
        //             classId,
        //             "CLASS",
        //             `/class`
        //         );
        //     }
        // } catch (notificationError) {
        //     console.error('Lỗi khi gửi thông báo:', notificationError);
        // }

        return res.status(200).json({ message: 'Xóa học viên khỏi lớp học thành công!' });

    } catch (error) {
        console.error('Error kicking student from class:', error);

        // Chỉ rollback nếu transaction chưa hoàn tất
        if (!transaction.finished) {
            await transaction.rollback();
        }

        return res.status(500).json({ message: 'Có lỗi xảy ra khi xóa học viên khỏi lớp học.' });
    }
};
