import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useEffect, useState, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import { getFullLessonLearningItemByClassId, postLesson, deleteLesson, postLearningItem, deleteLearningItem } from "../../../features/class/classSlice";
import LearningItemIcon from "../../../components/image/LearningItemIcon";
import ViewDetail from "../../../components/ViewDetail";
import { setErrorMessage } from "../../../features/state/stateApiSlice";
import { fetchCodesByType } from "../../../features/code/codeSlice";
import DropMenuBarAdmin from "../../../components/dropMenu/OptionBarAdmin";
import { fetchPublicExamById } from "../../../features/exam/examSlice";
import { fetchPdfs } from "../../../features/image/imageSlice";
import LoadingSpinner from "../../../components/loading/LoadingSpinner";
import YouTubePlayer from "../../../components/YouTubePlayer";
import SuggestInputBarAdmin from "../../../components/input/suggestInputBarAdmin";
import AdminSidebar from "../../../components/sidebar/AdminSidebar";
import { Home, Plus, Calendar, BookOpen, Video, FileText, Target } from 'lucide-react';
import ClassAdminLayout from "src/layouts/ClassAdminLayout";
import PdfViewer from "src/components/ViewPdf";
import ExamSearchInput from "src/components/ExamSearchInput";
import { findLessons } from "src/features/lesson";

const LessonManagement = () => {
    const { classId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { lessonClass } = useSelector((state) => state.classes);
    const { codes } = useSelector((state) => state.codes);
    const [exam, setExam] = useState({});
    const { pdfs } = useSelector((state) => state.images);
    const { closeSidebar } = useSelector((state) => state.sidebar);
    const [openLessons, setOpenLessons] = useState([]);
    const [isAddViewLesson, setIsAddViewLesson] = useState(false);
    const [isAddViewLearningItem, setIsAddViewLearningItem] = useState(false);
    const [loadingExam, setLoadingExam] = useState(false);
    const [loadingPdfs, setLoadingPdfs] = useState(false);
    const [loading, setLoading] = useState(false);

    const [newLesson, setNewLesson] = useState({
        name: '',
        day: '',
        description: '',
        chapter: null,
    });

    const [newLearningItem, setNewLearningItem] = useState({
        name: '',
        typeOfLearningItem: '',
        url: '',
        deadline: '',
        description: '',
        lessonId: '',
    });

    const handleChange = (e) => {
        setNewLesson({
            ...newLesson,
            [e.target.name]: e.target.value,
        });
    };

    const findExam = async (examCode) => {
        if (examCode && examCode.trim()) {
            setLoadingExam(true);
            await dispatch(fetchPublicExamById(examCode.trim()));
            setLoadingExam(false);
        } else {
            // Clear exam if no code
            setExam({})
        }
    };

    // Debounced search function
    const debouncedFindExam = useCallback(
        (() => {
            let timeoutId;
            return (examCode) => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    findExam(examCode);
                }, 500); // 500ms delay
            };
        })(),
        [dispatch]
    );

    const handleChangeLearningItem = (e) => {
        const { name, value } = e.target;

        setNewLearningItem({
            ...newLearningItem,
            [name]: value,
        });

        // Auto search for exam when url (exam code) changes
        if (name === 'url' && newLearningItem.typeOfLearningItem === 'BTVN') {
            debouncedFindExam(value);
        }

        // // Fetch PDFs when type changes to DOC
        // if (name === 'typeOfLearningItem' && value === 'DOC') {
        //     fetchPdfFiles();
        // }
    };

    const fetchPdfFiles = async () => {
        setLoadingPdfs(true);
        console.log('Fetching PDFs for learning items...');
        try {
            await dispatch(fetchPdfs('learningItemsPdf'));
        } catch (error) {
            console.error('Error fetching PDFs:', error);
        } finally {
            setLoadingPdfs(false);
        }
    };

    const toggleLesson = (index) => {
        setOpenLessons((prev) =>
            prev.includes(index)
                ? prev.filter((i) => i !== index) // nếu đang mở thì đóng lại
                : [...prev, index]               // nếu đang đóng thì mở ra
        );
    };
    const [activeItem, setActiveItem] = useState({
        type: null,
        index: null,
        item: null,
    });

    const handleAddLesson = () => {
        setIsAddViewLesson(true);
        setIsAddViewLearningItem(false);
        setNewLesson({
            name: '',
            day: '',
            description: '',
            chapter: null,
        });
    }

    const handleAddLearningItem = (lessonId) => {
        // console.log("lessonId", lessonId);
        setIsAddViewLearningItem(true);
        setIsAddViewLesson(false);
        setNewLearningItem({
            name: '',
            typeOfLearningItem: '',
            url: '',
            deadline: '',
            description: '',
            lessonId,
        });
        // Clear exam when opening new form
        setExam({})
    }

    const handleSubmitLesson = async () => {

        if (!newLesson.name || !newLesson.day) {
            dispatch(setErrorMessage("Vui lòng điền đầy đủ thông tin"));
            return;
        }

        if (newLesson.description.length > 500) {
            dispatch(setErrorMessage("Mô tả không được quá 500 ký tự"));
            return;
        }

        // Dispatch action to add lesson
        await dispatch(postLesson({ data: { ...newLesson, classId } })); // Gọi API để thêm lesson mới

        setIsAddViewLesson(false); // Đóng form thêm buổi học sau khi đã lưu
        setNewLesson({ name: '', day: '', description: '', chapter: null }); // Reset form

        // Cập nhật lại danh sách lessons
        dispatch(getFullLessonLearningItemByClassId({ classId }));
    };

    const handleSubmitLearningItem = async () => {
        if (!newLearningItem.name || !newLearningItem.typeOfLearningItem) {
            dispatch(setErrorMessage("Vui lòng điền đầy đủ thông tin"));
            return;
        }

        if (newLearningItem.typeOfLearningItem === 'BTVN' && !newLearningItem.url) {
            dispatch(setErrorMessage("Vui lòng nhập mã đề"));
            return;
        }

        if (newLearningItem.description.length > 500) {
            dispatch(setErrorMessage("Mô tả không được quá 500 ký tự"));
            return;
        }
        const filteredItem = Object.fromEntries(
            Object.entries(newLearningItem).filter(
                ([_, value]) => value !== null && value !== ''
            )
        );
        // Dispatch action to add learning item
        await dispatch(postLearningItem({
            data: {
                ...filteredItem,
            }
        }));
        setIsAddViewLearningItem(false); // Đóng form thêm mục học tập sau khi đã lưu
        setNewLearningItem({ name: '', typeOfLearningItem: '', url: '', deadline: '', description: '' }); // Reset form
        setExam({})

        // Cập nhật lại danh sách learning items
        dispatch(getFullLessonLearningItemByClassId({ classId }));
    };

    const handleDeleteLesson = async (lessonId) => {
        await dispatch(deleteLesson({ lessonId }));
        dispatch(getFullLessonLearningItemByClassId({ classId }));
    };

    const handleDeleteLearningItem = async (learningItemId) => {
        await dispatch(deleteLearningItem({ learningItemId }));
        dispatch(getFullLessonLearningItemByClassId({ classId }));
    };

    useEffect(() => {
        setLoading(true);
        dispatch(fetchCodesByType(['study item type', 'chapter']))
            .then(() => setLoading(false))
            .catch(() => setLoading(false));
    }, [dispatch]);

    useEffect(() => {
        setIsAddViewLearningItem(false);
        setIsAddViewLesson(false);
    }, [activeItem]);

    useEffect(() => {
        if (classId) {
            setLoading(true);
            dispatch(getFullLessonLearningItemByClassId({ classId }))
                .then(() => setLoading(false))
                .catch(() => setLoading(false));

        }
    }, [dispatch, classId]);

    useEffect(() => {
        if (lessonClass && lessonClass?.lessons?.length > 0) {
            setActiveItem({
                type: "lesson",
                index: lessonClass?.lessons[0]?.id || null,
                item: lessonClass?.lessons[0] || null,
            });
        }
    }, [lessonClass]);

    useEffect(() => {
        const handleSelectItem = (e) => {
            const item = e.detail;
            setActiveItem({ type: 'learningItem', index: item.id, item });
        };

        window.addEventListener("selectLearningItem", handleSelectItem);
        return () => window.removeEventListener("selectLearningItem", handleSelectItem);
    }, []);

    // Fetch PDFs when component mounts
    useEffect(() => {
        fetchPdfFiles();
        dispatch(findLessons(''))
    }, [dispatch]);

    // useEffect(() => {
    //     console.log("newLesson", newLesson);
    // }, [newLesson]);

    return (
        <ClassAdminLayout>


            {/* Content */}
            <div className="flex-1 overflow-hidden p-6">
                <div className="flex gap-6 h-full">
                    {/* Sidebar - Lessons List */}
                    <div className="w-1/3 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col">
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Danh sách buổi học</h3>

                            {/* Add Lesson Button */}
                            <button
                                onClick={handleAddLesson}
                                className="w-full flex items-center justify-center gap-2 px-4 py-3 border-2 border-dashed border-sky-300 text-sky-600 hover:border-sky-400 hover:bg-sky-50 rounded-lg transition-colors"
                            >
                                <Plus size={20} />
                                <span className="font-medium">Thêm buổi học</span>
                            </button>
                        </div>

                        {loading ? (
                            <div className="flex-1 flex items-center justify-center">
                                <LoadingSpinner
                                    size="2rem"
                                    showText={true}
                                    text="Đang tải..."
                                />
                            </div>
                        ) : (
                            <div className="flex-1 overflow-y-auto p-4">
                                {lessonClass?.lessons?.length === 0 ? (
                                    <div className="flex flex-col items-center justify-center h-full text-gray-500">
                                        <Calendar size={48} className="mb-4 text-gray-300" />
                                        <p className="text-lg font-medium">Chưa có buổi học nào</p>
                                        <p className="text-sm">Nhấn "Thêm buổi học" để bắt đầu</p>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        {lessonClass?.lessons?.map((lesson, index) => (
                                            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                                                {/* Lesson Header */}
                                                <div
                                                    onClick={() => toggleLesson(index)}
                                                    className={`p-3 cursor-pointer transition-colors ${activeItem.type === 'lesson' && activeItem.index === lesson.id
                                                        ? 'bg-sky-100 border-sky-200'
                                                        : 'hover:bg-gray-50'
                                                        }`}
                                                >
                                                    <div className="flex items-center gap-3">
                                                        {lesson?.learningItems?.length > 0 ? (

                                                            <svg
                                                                className={`w-4 h-4 transition-transform ${openLessons.includes(index) ? 'rotate-90' : ''
                                                                    }`}
                                                                fill="none"
                                                                stroke="currentColor"
                                                                viewBox="0 0 24 24"
                                                            >
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                                            </svg>
                                                        ) : (
                                                            <div className="w-6 h-6 flex items-center justify-center">
                                                                <BookOpen size={16} className="text-gray-400" />
                                                            </div>
                                                        )}

                                                        <div
                                                            className="flex-1 min-w-0"
                                                            onClick={() => setActiveItem({ type: 'lesson', index: lesson.id, item: lesson })}
                                                        >
                                                            <h4 className="font-medium text-gray-900 truncate">{lesson.name}</h4>
                                                            <p className="text-sm text-gray-500">
                                                                {new Date(lesson.day).toLocaleDateString('vi-VN')}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Learning Items */}
                                                {(openLessons.includes(index) || lesson.learningItems.length === 0) && (
                                                    <div className="border-t border-gray-200 bg-gray-50">
                                                        {lesson.learningItems?.map((learningItem, i) => (
                                                            <div
                                                                key={i}
                                                                onClick={() => setActiveItem({ type: 'learningItem', index: learningItem.id, item: learningItem })}
                                                                className={`px-6 py-2 cursor-pointer transition-colors flex items-center gap-3 ${activeItem.type === 'learningItem' && activeItem.index === learningItem.id
                                                                    ? 'bg-sky-100 text-sky-900'
                                                                    : 'hover:bg-gray-100'
                                                                    }`}
                                                            >
                                                                <LearningItemIcon type={learningItem.typeOfLearningItem} />
                                                                <span className="text-sm font-medium truncate">{learningItem.name}</span>
                                                            </div>
                                                        ))}

                                                        {/* Add Learning Item Button */}
                                                        <div className="px-6 py-2">
                                                            <button
                                                                onClick={() => handleAddLearningItem(lesson.id)}
                                                                className="w-full flex items-center justify-center gap-2 px-3 py-2 border border-dashed border-purple-300 text-purple-600 hover:border-purple-400 hover:bg-purple-50 rounded text-sm transition-colors"
                                                            >
                                                                <Plus size={16} />
                                                                <span>Thêm mục học tập</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    {/* Main Content Area */}
                    <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col">
                        {isAddViewLesson ? (
                            <div className="flex-1 p-6">
                                <div className="max-w-2xl mx-auto">
                                    <div className="flex items-center gap-3 mb-6">
                                        <div className="p-2 bg-sky-100 rounded-lg">
                                            <Calendar size={24} className="text-sky-600" />
                                        </div>
                                        <div>
                                            <h2 className="text-xl font-semibold text-gray-900">Thêm buổi học mới</h2>
                                            <p className="text-sm text-gray-500">Tạo buổi học mới cho lớp học</p>
                                        </div>
                                    </div>

                                    <div className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Tên buổi học <span className="text-red-500">*</span>
                                                </label>
                                                <input
                                                    type="text"
                                                    name="name"
                                                    value={newLesson.name}
                                                    onChange={handleChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                                                    placeholder="Nhập tên buổi học"
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Ngày học <span className="text-red-500">*</span>
                                                </label>
                                                <input
                                                    type="date"
                                                    name="day"
                                                    value={newLesson.day}
                                                    onChange={handleChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                                                />
                                            </div>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Chương học
                                                <span className="text-gray-400 text-sm ml-2">
                                                    {newLesson.chapter ? `(${newLesson.chapter})` : "(Không có)"}
                                                </span>
                                            </label>
                                            <SuggestInputBarAdmin
                                                options={codes['chapter'] ? codes['chapter'].filter((c) => c.code.length === 4) : []}
                                                selectedOption={newLesson.chapter}
                                                onChange={(option) => setNewLesson({ ...newLesson, chapter: option })}
                                                placeholder="Chọn chương học"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Mô tả buổi học
                                            </label>
                                            <textarea
                                                name="description"
                                                value={newLesson.description}
                                                onChange={handleChange}
                                                rows={4}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 resize-none"
                                                placeholder="Nhập mô tả cho buổi học này..."
                                            />
                                            <p className="text-xs text-gray-500 mt-1">
                                                {newLesson.description.length}/500 ký tự
                                            </p>
                                        </div>

                                        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                                            <button
                                                onClick={() => setIsAddViewLesson(false)}
                                                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                                            >
                                                Hủy
                                            </button>
                                            <button
                                                onClick={handleSubmitLesson}
                                                className="px-6 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-lg transition-colors font-medium"
                                            >
                                                Tạo buổi học
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ) : isAddViewLearningItem ? (
                            <div className="flex-1 flex flex-col overflow-hidden">
                                {/* Header - Fixed */}
                                <div className="flex-shrink-0 p-6 border-b border-gray-200">
                                    <div className="max-w-2xl mx-auto">
                                        <div className="flex items-center gap-3">
                                            <div className="p-2 bg-purple-100 rounded-lg">
                                                <BookOpen size={24} className="text-purple-600" />
                                            </div>
                                            <div>
                                                <h2 className="text-xl font-semibold text-gray-900">Thêm mục học tập</h2>
                                                <p className="text-sm text-gray-500">Tạo mục học tập mới cho buổi học</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Scrollable Content */}
                                <div className="flex-1 overflow-y-auto p-6">
                                    <div className="max-w-2xl mx-auto space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Tên mục học tập <span className="text-red-500">*</span>
                                                </label>
                                                <input
                                                    type="text"
                                                    name="name"
                                                    value={newLearningItem.name}
                                                    onChange={handleChangeLearningItem}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                                    placeholder="Nhập tên mục học tập"
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Buổi học
                                                </label>
                                                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-700">
                                                    {lessonClass?.lessons?.find((lesson) => lesson.id === newLearningItem.lessonId)?.name}
                                                </div>
                                            </div>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Loại mục học tập <span className="text-red-500">*</span>
                                            </label>
                                            <DropMenuBarAdmin
                                                selectedOption={newLearningItem.typeOfLearningItem}
                                                onChange={(option) => setNewLearningItem({ ...newLearningItem, typeOfLearningItem: option })}
                                                options={codes['study item type'] ? codes['study item type'] : []}
                                            />
                                        </div>

                                        {/* Type-specific content */}
                                        {newLearningItem.typeOfLearningItem === 'BTVN' && (
                                            <div className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                                <h4 className="font-medium text-blue-900 flex items-center gap-2">
                                                    <Target size={16} />
                                                    Thông tin bài tập về nhà
                                                </h4>
                                                <div className="flex items-center gap-3">
                                                    <label className="text-sm font-medium text-gray-700 w-20">Mã đề:</label>
                                                    <ExamSearchInput
                                                        value={newLearningItem.url}
                                                        selectedExamId={newLearningItem.url}
                                                        onChange={(value) => setNewLearningItem({ ...newLearningItem, url: value })}
                                                        onSelect={(examItem) => setNewLearningItem({ ...newLearningItem, url: examItem.id })}
                                                        onClear={() => setNewLearningItem({ ...newLearningItem, url: '' })}
                                                        className="flex-1"
                                                        setExam={setExam}
                                                        clearExam={() => setExam({})}
                                                    />
                                                </div>

                                                {(newLearningItem.url && exam) ? (
                                                    <div className="space-y-3 bg-white p-3 rounded border">
                                                        <div className="flex justify-between">
                                                            <span className="text-sm font-medium text-gray-600">Tên đề:</span>
                                                            <span className="text-sm text-gray-900">{exam?.name}</span>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <span className="text-sm font-medium text-gray-600">Khối:</span>
                                                            <span className="text-sm text-gray-900">{exam?.class}</span>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <span className="text-sm font-medium text-gray-600">Năm:</span>
                                                            <span className="text-sm text-gray-900">{exam?.year}</span>
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <p className="text-sm text-red-600">Chưa chọn đề thi</p>
                                                )}
                                            </div>
                                        )}

                                        {newLearningItem.typeOfLearningItem === 'VID' && (
                                            <div className="space-y-4 p-4 bg-green-50 rounded-lg border border-green-200">
                                                <h4 className="font-medium text-green-900 flex items-center gap-2">
                                                    <Video size={16} />
                                                    Video học tập
                                                </h4>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">Link video</label>
                                                    <input
                                                        type="text"
                                                        name="url"
                                                        value={newLearningItem.url}
                                                        onChange={handleChangeLearningItem}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                                        placeholder="Nhập link YouTube"
                                                    />
                                                </div>
                                                {newLearningItem.url && (
                                                    <div className="mt-4">
                                                        <YouTubePlayer url={newLearningItem.url} />
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        {newLearningItem.typeOfLearningItem === 'DOC' && (
                                            <div className="space-y-4 p-4 bg-orange-50 rounded-lg border border-orange-200">
                                                <h4 className="font-medium text-orange-900 flex items-center gap-2">
                                                    <FileText size={16} />
                                                    Chọn tài liệu PDF
                                                </h4>

                                                {loadingPdfs ? (
                                                    <div className="flex justify-center py-4">
                                                        <LoadingSpinner size="1.5rem" />
                                                    </div>
                                                ) : pdfs && pdfs.length > 0 ? (
                                                    <div className="space-y-2 max-h-60 overflow-y-auto">
                                                        <p className="text-sm text-gray-600 mb-3">
                                                            Chọn một file PDF từ danh sách bên dưới:
                                                        </p>
                                                        {pdfs.map((pdfUrl, index) => {
                                                            const fileName = pdfUrl.split('/').pop().split('?')[0];
                                                            const displayName = decodeURIComponent(fileName.split('-').slice(1).join('-')) || fileName;
                                                            const isSelected = newLearningItem.url === pdfUrl;

                                                            return (
                                                                <div
                                                                    key={index}
                                                                    onClick={() => setNewLearningItem({ ...newLearningItem, url: pdfUrl })}
                                                                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${isSelected
                                                                        ? 'border-orange-500 bg-orange-100 text-orange-900'
                                                                        : 'border-gray-300 bg-white hover:border-orange-300 hover:bg-orange-50'
                                                                        }`}
                                                                >
                                                                    <div className="flex items-center gap-2">
                                                                        <FileText size={16} className={isSelected ? 'text-orange-600' : 'text-gray-500'} />
                                                                        <span className="text-sm font-medium truncate">{displayName}</span>
                                                                        {isSelected && (
                                                                            <div className="ml-auto">
                                                                                <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                ) : (
                                                    <div className="text-center py-4 text-gray-500">
                                                        <FileText size={24} className="mx-auto mb-2 text-gray-300" />
                                                        <p className="text-sm">Không có file PDF nào trong thư mục</p>
                                                    </div>
                                                )}

                                                {newLearningItem.url && (
                                                    <>
                                                        <div className="mt-3 p-3 bg-white border border-orange-200 rounded-lg">
                                                            <p className="text-sm font-medium text-gray-700 mb-1">File đã chọn:</p>
                                                            <p className="text-sm text-gray-600 truncate">{
                                                                newLearningItem.url
                                                            }</p>
                                                        </div>
                                                        <PdfViewer url={newLearningItem.url} />
                                                    </>

                                                )}
                                            </div>
                                        )}

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Ngày hết hạn <span className="text-gray-500 text-xs">(Nếu là BTVN sẽ tự động cập nhật nếu không truyền vào)</span>
                                                </label>
                                                <input
                                                    type="date"
                                                    name="deadline"
                                                    value={newLearningItem.deadline}
                                                    onChange={handleChangeLearningItem}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                                />
                                            </div>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Mô tả
                                            </label>
                                            <textarea
                                                name="description"
                                                value={newLearningItem.description}
                                                onChange={handleChangeLearningItem}
                                                rows={4}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
                                                placeholder="Nhập mô tả cho mục học tập này..."
                                            />
                                            <p className="text-xs text-gray-500 mt-1">
                                                {newLearningItem.description.length}/500 ký tự
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Footer - Fixed */}
                                <div className="flex-shrink-0 p-6 border-t border-gray-200 bg-gray-50">
                                    <div className="max-w-2xl mx-auto">
                                        <div className="flex justify-end gap-3">
                                            <button
                                                onClick={() => setIsAddViewLearningItem(false)}
                                                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors"
                                            >
                                                Hủy
                                            </button>
                                            <button
                                                onClick={handleSubmitLearningItem}
                                                className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium"
                                            >
                                                Tạo mục học tập
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            lessonClass?.lessons?.length > 0 && (
                                <ViewDetail
                                    activeItem={activeItem}
                                    deleteLearningItem={handleDeleteLearningItem}
                                    deleteLesson={handleDeleteLesson}
                                />
                            )
                        )}
                    </div>
                </div>
            </div>
        </ClassAdminLayout>

    )
}

export default LessonManagement;
