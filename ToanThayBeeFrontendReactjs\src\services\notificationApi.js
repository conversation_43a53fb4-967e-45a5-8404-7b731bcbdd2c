import api from "./api";

/**
 * <PERSON><PERSON><PERSON> danh sách thông báo của người dùng hiện tại
 * @param {Object} params - <PERSON><PERSON><PERSON> tham số truy vấn
 * @param {number} params.limit - <PERSON><PERSON> lượng thông báo tối đa (mặc định: 10)
 * @param {number} params.offset - Vị trí bắt đầu (mặc định: 0)
 * @param {boolean} params.isRead - Lọc theo trạng thái đã đọc (tùy chọn)
 * @returns {Promise} - Promise chứa kết quả API
 */
export const getNotificationsAPI = (params = { limit: 10, offset: 0 }) => {
  return api.get("/v1/notifications", { params });
};

/**
 * Lấy số lượng thông báo chưa đọc của người dùng hiện tại
 * @returns {Promise} - Promise chứa kết quả API
 */
export const getUnreadCountAPI = () => {
  return api.get("/v1/notifications/unread-count");
};

/**
 * <PERSON><PERSON>h dấu một hoặc nhiều thông báo đã đọc
 * @param {Array} notificationIds - Mảng các ID thông báo cần đánh dấu đã đọc
 * @returns {Promise} - Promise chứa kết quả API
 */
export const markAsReadAPI = (notificationIds) => {
  return api.post("/v1/notifications/mark-as-read", { notificationIds });
};

/**
 * Đánh dấu tất cả thông báo của người dùng hiện tại đã đọc
 * @returns {Promise} - Promise chứa kết quả API
 */
export const markAllAsReadAPI = () => {
  return api.post("/v1/notifications/mark-all-as-read");
};

/**
 * Xóa một thông báo
 * @param {number} id - ID của thông báo cần xóa
 * @returns {Promise} - Promise chứa kết quả API
 */
export const deleteNotificationAPI = (id) => {
  return api.delete(`/v1/notifications/${id}`);
};

/**
 * Xóa nhiều thông báo
 * @param {Array} notificationIds - Mảng các ID thông báo cần xóa
 * @returns {Promise} - Promise chứa kết quả API
 */
export const deleteNotificationsAPI = (notificationIds) => {
  return api.delete("/v1/notifications", { data: { notificationIds } });
};

/**
 * Gửi thông báo đến một người dùng cụ thể (chỉ dành cho admin)
 * @param {Object} data - Dữ liệu thông báo
 * @param {number} data.userId - ID của người dùng nhận thông báo
 * @param {string} data.title - Tiêu đề thông báo
 * @param {string} data.content - Nội dung thông báo
 * @param {string} data.type - Loại thông báo (mặc định: SYSTEM)
 * @param {number} data.relatedId - ID của đối tượng liên quan (tùy chọn)
 * @param {string} data.relatedType - Loại đối tượng liên quan (tùy chọn)
 * @param {string} data.actionUrl - URL chuyển hướng khi nhấp vào thông báo (tùy chọn)
 * @returns {Promise} - Promise chứa kết quả API
 */
export const sendNotificationToUserAPI = (data) => {
  return api.post("/v1/admin/notifications/send-to-user", data);
};

/**
 * Gửi thông báo đến tất cả người dùng trong một lớp (chỉ dành cho admin)
 * @param {Object} data - Dữ liệu thông báo
 * @param {number} data.classId - ID của lớp
 * @param {string} data.title - Tiêu đề thông báo
 * @param {string} data.content - Nội dung thông báo
 * @param {string} data.actionUrl - URL chuyển hướng khi nhấp vào thông báo (tùy chọn)
 * @returns {Promise} - Promise chứa kết quả API
 */
export const sendNotificationToClassAPI = (data) => {
  return api.post("/v1/admin/notifications/send-to-class", data);
};

/**
 * Gửi thông báo đến tất cả người dùng đang làm một bài thi (chỉ dành cho admin)
 * @param {Object} data - Dữ liệu thông báo
 * @param {number} data.examId - ID của bài thi
 * @param {string} data.title - Tiêu đề thông báo
 * @param {string} data.content - Nội dung thông báo
 * @param {string} data.actionUrl - URL chuyển hướng khi nhấp vào thông báo (tùy chọn)
 * @returns {Promise} - Promise chứa kết quả API
 */
export const sendNotificationToExamAPI = (data) => {
  return api.post("/v1/admin/notifications/send-to-exam", data);
};
