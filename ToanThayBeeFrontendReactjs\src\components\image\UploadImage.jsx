import React, { useState, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { setErrorMessage } from "../../features/state/stateApiSlice";
import { Upload, Image as ImageIcon, X, Trash2 } from "lucide-react";

const ImageUpload = ({
    image,
    setImage,
    question = true,
    inputId,
    className = '',
    compact = false,
    showPreview = true
}) => {
    const dispatch = useDispatch();
    const [preview, setPreview] = useState(null);
    const [isDragging, setIsDragging] = useState(false);
    const uploadRef = useRef(null);

    const validateAndSetImage = (file) => {
        if (!["image/jpeg", "image/png"].includes(file.type)) {
            dispatch(setErrorMessage("Chỉ cho phép định dạng JPEG hoặc PNG!"));
            return;
        }
        if (file.size > 5 * 1024 * 1024) {
            dispatch(setErrorMessage("<PERSON><PERSON><PERSON> thước <PERSON>nh vượt quá 5MB!"));
            return;
        }
        setImage(file);
        setPreview(URL.createObjectURL(file));
    };

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            validateAndSetImage(file);
        }
    };

    useEffect(() => {
        if (image) {
            setPreview(URL.createObjectURL(image));
        }
    }, [image]);

    const handleDragOver = (event) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = () => {
        setIsDragging(false);
    };

    const handleDrop = (event) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);
        const file = event.dataTransfer.files[0];
        if (file) {
            validateAndSetImage(file);
        }
    };

    const handlePaste = (event) => {
        const items = event.clipboardData?.items;
        if (items) {
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                if (item.type.indexOf("image") !== -1) {
                    const file = item.getAsFile();
                    if (file) {
                        validateAndSetImage(file);
                    }
                }
            }
        }
    };

    useEffect(() => {
        const el = uploadRef.current;
        if (!el) return;

        el.addEventListener("paste", handlePaste);
        return () => {
            el.removeEventListener("paste", handlePaste);
        };
    }, []);

    const handleUploadClick = () => {
        document.getElementById(inputId).click();
    };

    const handleDelete = () => {
        setImage(null);
        setPreview(null);
    };

    // Compact mode for space-optimized layouts
    if (compact) {
        return (
            <div
                ref={uploadRef}
                tabIndex={0}
                className={`relative ${className} transition-all duration-200`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
            >
                {!preview ? (
                    <div
                        onClick={handleUploadClick}
                        className={`w-full h-full p-4 min-h-[3rem] flex flex-col items-center justify-center cursor-pointer rounded border-2 border-dashed transition-all duration-200 ${
                            isDragging
                                ? "border-blue-400 bg-blue-50"
                                : "border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"
                        }`}
                    >
                        <ImageIcon className="w-4 h-4 text-gray-400 mb-1" />
                        <span className="text-xs text-gray-600">Chọn ảnh</span>
                    </div>
                ) : (
                    <div className="relative group">
                        <img
                            src={preview}
                            alt="Preview"
                            className="w-full h-full object-cover rounded border border-gray-200"
                        />
                        <button
                            type="button"
                            onClick={handleDelete}
                            className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        >
                            <X className="w-3 h-3" />
                        </button>
                    </div>
                )}
                <input
                    id={inputId}
                    type="file"
                    accept="image/jpeg,image/png"
                    onChange={handleFileChange}
                    className="hidden"
                />
            </div>
        );
    }

    // Standard mode
    return (
        <div
            ref={uploadRef}
            tabIndex={0}
            className={`flex flex-col items-center justify-center p-4 border-2 border-dashed rounded-lg transition-all duration-200 ${
                className || 'w-full min-h-[8rem]'
            } ${
                isDragging
                    ? "border-blue-400 bg-blue-50"
                    : "border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
        >
            {!preview ? (
                <>
                    <div className="flex flex-col items-center text-center space-y-2">
                        <div className="p-2 bg-gray-200 rounded-full">
                            <Upload className="w-6 h-6 text-gray-600" />
                        </div>
                        <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-700">
                                Chọn ảnh hoặc kéo thả vào đây
                            </p>
                            <p className="text-xs text-gray-500">
                                JPEG, PNG • Tối đa 5MB • Ctrl+V để dán
                            </p>
                        </div>
                    </div>
                    <button
                        type="button"
                        onClick={handleUploadClick}
                        className="mt-3 px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
                    >
                        Chọn ảnh
                    </button>
                    <input
                        id={inputId}
                        type="file"
                        accept="image/jpeg,image/png"
                        onChange={handleFileChange}
                        className="hidden"
                    />
                </>
            ) : (
                <div className="relative group">
                    {showPreview ? (
                        <img
                            src={preview}
                            alt="Preview"
                            className="max-w-full max-h-32 object-contain rounded border border-gray-200"
                        />
                    ) : (
                        <div className="flex items-center space-x-2 p-3 bg-white border border-gray-200 rounded">
                            <ImageIcon className="w-5 h-5 text-gray-600" />
                            <span className="text-sm text-gray-700">Ảnh đã chọn</span>
                        </div>
                    )}
                    <button
                        type="button"
                        onClick={handleDelete}
                        className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md"
                    >
                        <Trash2 className="w-4 h-4" />
                    </button>
                </div>
            )}
        </div>
    );
};

export default ImageUpload;
