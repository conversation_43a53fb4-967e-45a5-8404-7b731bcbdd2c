import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import PostLessonRequest from '../dtos/requests/lesson/PostLessonRequest.js'
import PutLessonRequest from '../dtos/requests/lesson/PutLessonRequest.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as LessonController from '../controllers/LessonController.js'

const router = express.Router()

router.get('/v1/user/lesson/:id',
    requireRoles(Roles.JustStudent),
    asyncHandler(LessonController.getLessonById)
)

router.get('/v1/admin/lesson/class/:classId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(LessonController.getLessonByClassId)
)

router.get('/v1/admin/lesson/attended/:userId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(LessonController.getUserAttendedLessons)
)

router.get('/v1/admin/lesson/search',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(LessonController.findLessons)
)

router.post('/v1/admin/lesson',
    validate(PostLessonRequest),
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(LessonController.insertLesson)
)
router.put('/v1/admin/lesson/:id',
    validate(PutLessonRequest),
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(LessonController.changeLesson)
)
router.delete('/v1/admin/lesson/:lessonId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(LessonController.deleteLesson)
)

export default router