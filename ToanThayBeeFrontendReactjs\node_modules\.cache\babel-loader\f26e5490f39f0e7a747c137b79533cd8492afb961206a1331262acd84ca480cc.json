{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setSelectedQuestion, selectQuestion, reorderQuestions } from \"src/features/examAI/examAISlice\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\nimport QuestionContent from \"./QuestionContent\";\nimport LoadingData from \"../loading/LoadingData\";\nimport SortableQuestionItem from \"./SortableQuestionItem\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const EditQuestionView = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    questionsEdited,\n    selectedQuestion,\n    loading\n  } = useSelector(state => state.examAI);\n  return /*#__PURE__*/_jsxDEV(LoadingData, {\n    loading: loading,\n    loadText: \"\\u0110ang t\\u1EA3i danh s\\xE1ch c\\xE2u h\\u1ECFi...\",\n    isNoData: questionsEdited.length > 0 ? false : true,\n    noDataText: \"Kh\\xF4ng c\\xF3 c\\xE2u h\\u1ECFi n\\xE0o.\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold text-gray-800 mb-4\",\n      children: \"Danh s\\xE1ch c\\xE2u h\\u1ECFi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: questionsEdited.length > 0 ? questionsEdited.map((q, index) => /*#__PURE__*/_jsxDEV(QuestionContent, {\n        question: q,\n        index: index\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 25\n      }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 c\\xE2u h\\u1ECFi n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n};\n_s(EditQuestionView, \"4EGQ57iV1QCln1FUEsD7zEt1pv8=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = EditQuestionView;\nexport const LeftContent = () => {\n  _s2();\n  const {\n    viewEdit\n  } = useSelector(state => state.examAI);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-2/3 border-r border-gray-300 overflow-y-auto p-4\",\n    children: viewEdit === 'question' && /*#__PURE__*/_jsxDEV(EditQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 41\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 9\n  }, this);\n};\n_s2(LeftContent, \"plqPiIWI8H/U9YD7djE1+JqN1rI=\", false, function () {\n  return [useSelector];\n});\n_c2 = LeftContent;\nexport default LeftContent;\nvar _c, _c2;\n$RefreshReg$(_c, \"EditQuestionView\");\n$RefreshReg$(_c2, \"LeftContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "setSelectedQuestion", "selectQuestion", "reorderQuestions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "LoadingSpinner", "QuestionContent", "LoadingData", "SortableQuestionItem", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "arrayMove", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "jsxDEV", "_jsxDEV", "EditQuestionView", "_s", "dispatch", "questionsEdited", "selectedQuestion", "loading", "state", "examAI", "loadText", "isNoData", "length", "noDataText", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "q", "index", "question", "_c", "LeftContent", "_s2", "viewEdit", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/LeftContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { setSelectedQuestion, selectQuestion, reorderQuestions } from \"src/features/examAI/examAISlice\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\r\nimport QuestionContent from \"./QuestionContent\";\r\nimport LoadingData from \"../loading/LoadingData\";\r\nimport SortableQuestionItem from \"./SortableQuestionItem\";\r\nimport {\r\n    DndContext,\r\n    closestCenter,\r\n    KeyboardSensor,\r\n    PointerSensor,\r\n    useSensor,\r\n    useSensors,\r\n} from '@dnd-kit/core';\r\nimport {\r\n    arrayMove,\r\n    SortableContext,\r\n    sortableKeyboardCoordinates,\r\n    verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\n\r\nexport const EditQuestionView = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionsEdited, selectedQuestion, loading } = useSelector((state) => state.examAI);\r\n\r\n    return (\r\n        <LoadingData loading={loading} loadText=\"Đang tải danh sách câu hỏi...\" isNoData={questionsEdited.length > 0 ? false : true} noDataText=\"Không có câu hỏi nào.\">\r\n            <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Danh sách câu hỏi</h2>\r\n            <div className=\"space-y-3\">\r\n                {questionsEdited.length > 0 ? (\r\n                    questionsEdited.map((q, index) => (\r\n                        <QuestionContent\r\n                            question={q}\r\n                            index={index}\r\n                        />\r\n                    ))\r\n                ) : (\r\n                    <p className=\"text-gray-500\">Không có câu hỏi nào.</p>\r\n                )}\r\n            </div>\r\n        </LoadingData>\r\n    )\r\n}\r\n\r\nexport const LeftContent = () => {\r\n    const { viewEdit } = useSelector((state) => state.examAI);\r\n\r\n    return (\r\n        <div className=\"w-2/3 border-r border-gray-300 overflow-y-auto p-4\">\r\n            {viewEdit === 'question' && <EditQuestionView />}\r\n        </div>\r\n    )\r\n\r\n}\r\nexport default LeftContent;"], "mappings": ";;;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,mBAAmB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,iCAAiC;AACvG,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,uBAAuB,MAAM,0BAA0B;AAC9D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,SAAS,EACTC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB,eAAe;IAAEC,gBAAgB;IAAEC;EAAQ,CAAC,GAAG5B,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAE3F,oBACIR,OAAA,CAACb,WAAW;IAACmB,OAAO,EAAEA,OAAQ;IAACG,QAAQ,EAAC,oDAA+B;IAACC,QAAQ,EAAEN,eAAe,CAACO,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,IAAK;IAACC,UAAU,EAAC,wCAAuB;IAAAC,QAAA,gBAC3Jb,OAAA;MAAIc,SAAS,EAAC,0CAA0C;MAAAD,QAAA,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/ElB,OAAA;MAAKc,SAAS,EAAC,WAAW;MAAAD,QAAA,EACrBT,eAAe,CAACO,MAAM,GAAG,CAAC,GACvBP,eAAe,CAACe,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACzBrB,OAAA,CAACd,eAAe;QACZoC,QAAQ,EAAEF,CAAE;QACZC,KAAK,EAAEA;MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACJ,CAAC,gBAEFlB,OAAA;QAAGc,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IACxD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAAhB,EAAA,CArBYD,gBAAgB;EAAA,QACRtB,WAAW,EAC2BD,WAAW;AAAA;AAAA6C,EAAA,GAFzDtB,gBAAgB;AAuB7B,OAAO,MAAMuB,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAM;IAAEC;EAAS,CAAC,GAAGhD,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEzD,oBACIR,OAAA;IAAKc,SAAS,EAAC,oDAAoD;IAAAD,QAAA,EAC9Da,QAAQ,KAAK,UAAU,iBAAI1B,OAAA,CAACC,gBAAgB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC;AAGd,CAAC;AAAAO,GAAA,CATYD,WAAW;EAAA,QACC9C,WAAW;AAAA;AAAAiD,GAAA,GADvBH,WAAW;AAUxB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}