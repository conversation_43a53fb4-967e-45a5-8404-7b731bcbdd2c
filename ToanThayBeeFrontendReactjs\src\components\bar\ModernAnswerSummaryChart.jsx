import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, ResponsiveContainer } from 'recharts';
import { CheckCircle, XCircle, HelpCircle, BarChart2 } from 'lucide-react';

/**
 * Modern Answer Summary Chart Component
 * 
 * @param {Object} props - Component props
 * @param {number} props.correct - Number of correct answers
 * @param {number} props.incorrect - Number of incorrect answers
 * @param {number} props.unanswered - Number of unanswered questions
 */
const ModernAnswerSummaryChart = ({ correct = 0, incorrect = 0, unanswered = 0 }) => {
    // Data for the pie chart
    const data = [
        { name: 'Đúng', value: correct, color: '#10b981' }, // green-500
        { name: '<PERSON>', value: incorrect, color: '#ef4444' }, // red-500
        { name: 'Chưa làm', value: unanswered, color: '#f59e0b' }, // amber-500
    ];
    
    // Calculate total questions and percentages
    const total = correct + incorrect + unanswered;
    const correctPercent = total > 0 ? Math.round((correct / total) * 100) : 0;
    const incorrectPercent = total > 0 ? Math.round((incorrect / total) * 100) : 0;
    const unansweredPercent = total > 0 ? Math.round((unanswered / total) * 100) : 0;
    
    // Custom tooltip for the pie chart
    const CustomTooltip = ({ active, payload }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <div className="bg-white p-3 shadow-lg rounded-lg border border-gray-200">
                    <p className="font-medium text-gray-800">{data.name}</p>
                    <p className="text-sm text-gray-600">{data.value} câu ({Math.round((data.value / total) * 100)}%)</p>
                </div>
            );
        }
        return null;
    };
    
    return (
        <div className="w-full">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <BarChart2 className="text-sky-600" size={20} />
                    <span>Tổng quan kết quả</span>
                </h3>
                <div className="text-sm text-gray-500">Tổng số: <span className="font-semibold">{total} câu</span></div>
            </div>
            
            <div className="flex flex-col md:flex-row items-center gap-6">
                {/* Pie chart */}
                <div className="w-full md:w-1/2">
                    <ResponsiveContainer width="100%" height={200}>
                        <PieChart>
                            <Pie
                                data={data}
                                cx="50%"
                                cy="50%"
                                innerRadius={50}
                                outerRadius={70}
                                paddingAngle={2}
                                dataKey="value"
                            >
                                {data.map((entry, index) => (
                                    <Cell 
                                        key={`cell-${index}`} 
                                        fill={entry.color} 
                                        stroke="white"
                                        strokeWidth={2}
                                    />
                                ))}
                            </Pie>
                            <Tooltip content={<CustomTooltip />} />
                        </PieChart>
                    </ResponsiveContainer>
                </div>
                
                {/* Stats cards */}
                <div className="w-full md:w-1/2 grid grid-cols-1 gap-3">
                    <div className="bg-gradient-to-r from-green-50 to-white rounded-lg p-3 border border-green-100 shadow-sm">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                                <div className="p-1.5 bg-green-100 rounded-full">
                                    <CheckCircle size={16} className="text-green-600" />
                                </div>
                                <span className="font-medium text-gray-800">Đúng</span>
                            </div>
                            <div className="text-sm font-semibold text-green-600">{correctPercent}%</div>
                        </div>
                        <div className="mt-2 h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                            <div 
                                className="bg-green-500 h-full rounded-full" 
                                style={{ width: `${correctPercent}%` }}
                            ></div>
                        </div>
                        <div className="mt-1 text-xs text-gray-500 text-right">{correct} câu</div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-red-50 to-white rounded-lg p-3 border border-red-100 shadow-sm">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                                <div className="p-1.5 bg-red-100 rounded-full">
                                    <XCircle size={16} className="text-red-600" />
                                </div>
                                <span className="font-medium text-gray-800">Sai</span>
                            </div>
                            <div className="text-sm font-semibold text-red-600">{incorrectPercent}%</div>
                        </div>
                        <div className="mt-2 h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                            <div 
                                className="bg-red-500 h-full rounded-full" 
                                style={{ width: `${incorrectPercent}%` }}
                            ></div>
                        </div>
                        <div className="mt-1 text-xs text-gray-500 text-right">{incorrect} câu</div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-amber-50 to-white rounded-lg p-3 border border-amber-100 shadow-sm">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                                <div className="p-1.5 bg-amber-100 rounded-full">
                                    <HelpCircle size={16} className="text-amber-600" />
                                </div>
                                <span className="font-medium text-gray-800">Chưa làm</span>
                            </div>
                            <div className="text-sm font-semibold text-amber-600">{unansweredPercent}%</div>
                        </div>
                        <div className="mt-2 h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                            <div 
                                className="bg-amber-500 h-full rounded-full" 
                                style={{ width: `${unansweredPercent}%` }}
                            ></div>
                        </div>
                        <div className="mt-1 text-xs text-gray-500 text-right">{unanswered} câu</div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ModernAnswerSummaryChart;
