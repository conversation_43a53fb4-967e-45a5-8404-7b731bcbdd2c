{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\question\\\\QuestionPage.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { fetchPublicQuestionById } from 'src/features/question/questionSlice';\nimport { useParams } from \"react-router-dom\";\nimport { useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionPage = () => {\n  _s();\n  const {\n    questionId\n  } = useParams();\n  const dispatch = useDispatch();\n  const {\n    question,\n    loading\n  } = useSelector(state => state.questions);\n  useEffect(() => {\n    dispatch(fetchPublicQuestionById(questionId));\n  }, [dispatch, questionId]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Question Page\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionPage, \"1rm/CiVvlmXOlmbJsGMePFGSdhY=\", false, function () {\n  return [useParams, useDispatch, useSelector];\n});\n_c = QuestionPage;\nexport default QuestionPage;\nvar _c;\n$RefreshReg$(_c, \"QuestionPage\");", "map": {"version": 3, "names": ["React", "useSelector", "useDispatch", "fetchPublicQuestionById", "useParams", "useEffect", "jsxDEV", "_jsxDEV", "QuestionPage", "_s", "questionId", "dispatch", "question", "loading", "state", "questions", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/question/QuestionPage.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { fetchPublicQuestionById } from 'src/features/question/questionSlice';\r\nimport { useParams } from \"react-router-dom\";\r\nimport { useEffect } from 'react';\r\n\r\nconst QuestionPage = () => {\r\n    const { questionId } = useParams();\r\n    const dispatch = useDispatch();\r\n    const { question, loading } = useSelector((state) => state.questions);\r\n    \r\n    useEffect(() => {\r\n        dispatch(fetchPublicQuestionById(questionId));\r\n    }, [dispatch, questionId]);\r\n    return (\r\n        <div>\r\n            <h1>Question Page</h1>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default QuestionPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,uBAAuB,QAAQ,qCAAqC;AAC7E,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAW,CAAC,GAAGN,SAAS,CAAC,CAAC;EAClC,MAAMO,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU,QAAQ;IAAEC;EAAQ,CAAC,GAAGZ,WAAW,CAAEa,KAAK,IAAKA,KAAK,CAACC,SAAS,CAAC;EAErEV,SAAS,CAAC,MAAM;IACZM,QAAQ,CAACR,uBAAuB,CAACO,UAAU,CAAC,CAAC;EACjD,CAAC,EAAE,CAACC,QAAQ,EAAED,UAAU,CAAC,CAAC;EAC1B,oBACIH,OAAA;IAAAS,QAAA,eACIT,OAAA;MAAAS,QAAA,EAAI;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrB,CAAC;AAEd,CAAC;AAACX,EAAA,CAbID,YAAY;EAAA,QACSJ,SAAS,EACfF,WAAW,EACED,WAAW;AAAA;AAAAoB,EAAA,GAHvCb,YAAY;AAelB,eAAeA,YAAY;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}