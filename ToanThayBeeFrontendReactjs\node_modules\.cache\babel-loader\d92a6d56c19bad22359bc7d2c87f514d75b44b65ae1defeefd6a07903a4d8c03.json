{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\NavigateBar.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavigateBar = _ref => {\n  let {\n    list,\n    active,\n    setActive\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-row text-xs w-full\",\n    children: list.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setActive(item),\n      className: \"flex-1 p-2 text-center \".concat(active === item ? 'font-bold' : ' border-b ', \" first:border-r last:border-l border-gray-200 cursor-pointer \"),\n      children: item\n    }, item, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = NavigateBar;\nexport default NavigateBar;\nvar _c;\n$RefreshReg$(_c, \"NavigateBar\");", "map": {"version": 3, "names": ["NavigateBar", "_ref", "list", "active", "setActive", "_jsxDEV", "className", "children", "map", "item", "onClick", "concat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/NavigateBar.jsx"], "sourcesContent": ["const NavigateBar = ({ list, active, setActive }) => {\r\n    return (\r\n        <div className=\"flex flex-row text-xs w-full\">\r\n            {list.map((item) => (\r\n                <button\r\n                    key={item}\r\n                    onClick={() => setActive(item)}\r\n                    className={`flex-1 p-2 text-center ${active === item ? 'font-bold' : ' border-b '} first:border-r last:border-l border-gray-200 cursor-pointer `}>\r\n                    {item}\r\n                </button>\r\n\r\n            ))}\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default NavigateBar;"], "mappings": ";;AAAA,MAAMA,WAAW,GAAGC,IAAA,IAAiC;EAAA,IAAhC;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAU,CAAC,GAAAH,IAAA;EAC5C,oBACII,OAAA;IAAKC,SAAS,EAAC,8BAA8B;IAAAC,QAAA,EACxCL,IAAI,CAACM,GAAG,CAAEC,IAAI,iBACXJ,OAAA;MAEIK,OAAO,EAAEA,CAAA,KAAMN,SAAS,CAACK,IAAI,CAAE;MAC/BH,SAAS,4BAAAK,MAAA,CAA4BR,MAAM,KAAKM,IAAI,GAAG,WAAW,GAAG,YAAY,kEAAgE;MAAAF,QAAA,EAChJE;IAAI,GAHAA,IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIL,CAEX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAAC,EAAA,GAdKhB,WAAW;AAgBjB,eAAeA,WAAW;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}