{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\nexport const fetchPublicExamById = createAsyncThunk(\"examDetail/fetchPublicExamById\", async (id, _ref) => {\n  let {\n    dispatch\n  } = _ref;\n  return await apiHandler(dispatch, examApi.getExamPublic, id, () => {}, false, false);\n});\nexport const saveExamForUser = createAsyncThunk(\"examDetail/saveExamForUser\", async (_ref2, _ref3) => {\n  let {\n    examId\n  } = _ref2;\n  let {\n    dispatch\n  } = _ref3;\n  return await apiHandler(dispatch, examApi.saveExamForUserAPI, {\n    examId\n  }, () => {}, true, false, false, false);\n});\nexport const fetchRelatedExams = createAsyncThunk(\"exams/fetchRelatedExams\", async (id, _ref4) => {\n  let {\n    dispatch\n  } = _ref4;\n  return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => {}, false, false);\n});\n\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\"exams/fetchRelatedExamsIfNeeded\", async (id, _ref5) => {\n  let {\n    dispatch,\n    getState\n  } = _ref5;\n  const state = getState();\n  const {\n    relatedExams,\n    lastFetchedRelatedExams\n  } = state.exams;\n\n  // Kiểm tra xem đã có dữ liệu trong cache chưa\n  const hasData = relatedExams[id] && relatedExams[id].length > 0;\n\n  // Kiểm tra thời gian cache (5 phút = 300000ms)\n  const now = Date.now();\n  const lastFetched = lastFetchedRelatedExams[id] || 0;\n  const isCacheValid = now - lastFetched < 300000;\n\n  // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache\n  if (hasData && isCacheValid) {\n    // Cập nhật state.exams để hiển thị dữ liệu cache\n    return {\n      data: relatedExams[id]\n    };\n  }\n\n  // Nếu không có dữ liệu hoặc cache hết hạn, gọi API\n  return await dispatch(fetchRelatedExams(id)).unwrap();\n});\nconst examDetailSlice = createSlice({\n  name: \"examDetail\",\n  initialState: {\n    exam: null,\n    loading: false,\n    relatedExams: [],\n    loadingRelatedExams: false,\n    view: 'detail'\n  },\n  reducers: {\n    setView: (state, action) => {\n      state.view = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchPublicExamById.pending, state => {\n      state.exam = null;\n      state.loading = true;\n    }).addCase(fetchPublicExamById.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exam = action.payload.data;\n      }\n      state.loading = false;\n    }).addCase(fetchPublicExamById.rejected, state => {\n      state.exam = null;\n      state.loading = false;\n    }).addCase(saveExamForUser.fulfilled, (state, action) => {\n      if (action.payload) {\n        const {\n          examId,\n          isSave\n        } = action.payload;\n        if (state.exam) {\n          state.exam.isSave = isSave;\n        }\n      }\n    }).addCase(fetchRelatedExams.pending, state => {\n      state.loadingRelatedExams = true;\n    }).addCase(fetchRelatedExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.relatedExams = action.payload.data;\n      }\n      state.loadingRelatedExams = false;\n    }).addCase(fetchRelatedExams.rejected, state => {\n      state.loadingRelatedExams = false;\n    }).addCase(fetchRelatedExamsIfNeeded.pending, state => {\n      state.loadingRelatedExams = true;\n    }).addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.relatedExams = action.payload.data;\n      }\n      state.loadingRelatedExams = false;\n    }).addCase(fetchRelatedExamsIfNeeded.rejected, state => {\n      state.loadingRelatedExams = false;\n    });\n  }\n});\nexport const {\n  setView\n} = examDetailSlice.actions;\nexport default examDetailSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialPaginationState", "paginationReducers", "initialFilterState", "filterReducers", "fetchPublicExamById", "id", "_ref", "dispatch", "getExamPublic", "saveExamForUser", "_ref2", "_ref3", "examId", "saveExamForUserAPI", "fetchRelatedExams", "_ref4", "getRelatedExamAPI", "fetchRelatedExamsIfNeeded", "_ref5", "getState", "state", "relatedExams", "lastFetchedRelatedExams", "exams", "hasData", "length", "now", "Date", "lastFetched", "isCache<PERSON><PERSON>d", "data", "unwrap", "examDetailSlice", "name", "initialState", "exam", "loading", "loadingRelatedExams", "view", "reducers", "<PERSON><PERSON><PERSON><PERSON>", "action", "payload", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "isSave", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/exam/examDetailSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\r\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\r\n\r\nexport const fetchPublicExamById = createAsyncThunk(\r\n    \"examDetail/fetchPublicExamById\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamPublic, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const saveExamForUser = createAsyncThunk(\r\n    \"examDetail/saveExamForUser\",\r\n    async ({ examId }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.saveExamForUserAPI, { examId }, () => { }, true, false, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchRelatedExams = createAsyncThunk(\r\n    \"exams/fetchRelatedExams\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\r\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\r\n    \"exams/fetchRelatedExamsIfNeeded\",\r\n    async (id, { dispatch, getState }) => {\r\n        const state = getState();\r\n        const { relatedExams, lastFetchedRelatedExams } = state.exams;\r\n\r\n        // Kiểm tra xem đã có dữ liệu trong cache chưa\r\n        const hasData = relatedExams[id] && relatedExams[id].length > 0;\r\n\r\n        // Kiểm tra thời gian cache (5 phút = 300000ms)\r\n        const now = Date.now();\r\n        const lastFetched = lastFetchedRelatedExams[id] || 0;\r\n        const isCacheValid = now - lastFetched < 300000;\r\n\r\n        // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache\r\n        if (hasData && isCacheValid) {\r\n            // Cập nhật state.exams để hiển thị dữ liệu cache\r\n            return { data: relatedExams[id] };\r\n        }\r\n\r\n        // Nếu không có dữ liệu hoặc cache hết hạn, gọi API\r\n        return await dispatch(fetchRelatedExams(id)).unwrap();\r\n    }\r\n);\r\n\r\nconst examDetailSlice = createSlice({\r\n    name: \"examDetail\",\r\n    initialState: {\r\n        exam: null,\r\n        loading: false,\r\n        relatedExams: [],\r\n        loadingRelatedExams: false,\r\n        view: 'detail',\r\n    },\r\n    reducers: {\r\n        setView: (state, action) => {\r\n            state.view = action.payload;\r\n        },\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchPublicExamById.pending, (state) => {\r\n                state.exam = null;\r\n                state.loading = true;\r\n            })\r\n            .addCase(fetchPublicExamById.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exam = action.payload.data;\r\n                }\r\n                state.loading = false;\r\n            })\r\n            .addCase(fetchPublicExamById.rejected, (state) => {\r\n                state.exam = null;\r\n                state.loading = false;\r\n            })\r\n            .addCase(saveExamForUser.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    const { examId, isSave } = action.payload;\r\n\r\n                    if (state.exam) {\r\n                        state.exam.isSave = isSave;\r\n                    }\r\n                }\r\n            })\r\n            .addCase(fetchRelatedExams.pending, (state) => {\r\n                state.loadingRelatedExams = true;\r\n            })\r\n            .addCase(fetchRelatedExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.relatedExams = action.payload.data;\r\n                }\r\n                state.loadingRelatedExams = false;\r\n            })\r\n            .addCase(fetchRelatedExams.rejected, (state) => {\r\n                state.loadingRelatedExams = false;\r\n            })\r\n            .addCase(fetchRelatedExamsIfNeeded.pending, (state) => {\r\n                state.loadingRelatedExams = true;\r\n            })\r\n            .addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.relatedExams = action.payload.data;\r\n                }\r\n                state.loadingRelatedExams = false;\r\n            })\r\n            .addCase(fetchRelatedExamsIfNeeded.rejected, (state) => {\r\n                state.loadingRelatedExams = false;\r\n            })\r\n    },\r\n});\r\n\r\nexport const { setView } = examDetailSlice.actions;\r\nexport default examDetailSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAE5E,OAAO,MAAMC,mBAAmB,GAAGP,gBAAgB,CAC/C,gCAAgC,EAChC,OAAOQ,EAAE,EAAAC,IAAA,KAAmB;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAD,IAAA;EACnB,OAAO,MAAMP,UAAU,CAACQ,QAAQ,EAAET,OAAO,CAACU,aAAa,EAAEH,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACzF,CACJ,CAAC;AAED,OAAO,MAAMI,eAAe,GAAGZ,gBAAgB,CAC3C,4BAA4B,EAC5B,OAAAa,KAAA,EAAAC,KAAA,KAAoC;EAAA,IAA7B;IAAEC;EAAO,CAAC,GAAAF,KAAA;EAAA,IAAE;IAAEH;EAAS,CAAC,GAAAI,KAAA;EAC3B,OAAO,MAAMZ,UAAU,CAACQ,QAAQ,EAAET,OAAO,CAACe,kBAAkB,EAAE;IAAED;EAAO,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACnH,CACJ,CAAC;AAED,OAAO,MAAME,iBAAiB,GAAGjB,gBAAgB,CAC7C,yBAAyB,EACzB,OAAOQ,EAAE,EAAAU,KAAA,KAAmB;EAAA,IAAjB;IAAER;EAAS,CAAC,GAAAQ,KAAA;EACnB,OAAO,MAAMhB,UAAU,CAACQ,QAAQ,EAAET,OAAO,CAACkB,iBAAiB,EAAEX,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC7F,CACJ,CAAC;;AAED;AACA,OAAO,MAAMY,yBAAyB,GAAGpB,gBAAgB,CACrD,iCAAiC,EACjC,OAAOQ,EAAE,EAAAa,KAAA,KAA6B;EAAA,IAA3B;IAAEX,QAAQ;IAAEY;EAAS,CAAC,GAAAD,KAAA;EAC7B,MAAME,KAAK,GAAGD,QAAQ,CAAC,CAAC;EACxB,MAAM;IAAEE,YAAY;IAAEC;EAAwB,CAAC,GAAGF,KAAK,CAACG,KAAK;;EAE7D;EACA,MAAMC,OAAO,GAAGH,YAAY,CAAChB,EAAE,CAAC,IAAIgB,YAAY,CAAChB,EAAE,CAAC,CAACoB,MAAM,GAAG,CAAC;;EAE/D;EACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;EACtB,MAAME,WAAW,GAAGN,uBAAuB,CAACjB,EAAE,CAAC,IAAI,CAAC;EACpD,MAAMwB,YAAY,GAAGH,GAAG,GAAGE,WAAW,GAAG,MAAM;;EAE/C;EACA,IAAIJ,OAAO,IAAIK,YAAY,EAAE;IACzB;IACA,OAAO;MAAEC,IAAI,EAAET,YAAY,CAAChB,EAAE;IAAE,CAAC;EACrC;;EAEA;EACA,OAAO,MAAME,QAAQ,CAACO,iBAAiB,CAACT,EAAE,CAAC,CAAC,CAAC0B,MAAM,CAAC,CAAC;AACzD,CACJ,CAAC;AAED,MAAMC,eAAe,GAAGpC,WAAW,CAAC;EAChCqC,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAE;IACVC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,KAAK;IACdf,YAAY,EAAE,EAAE;IAChBgB,mBAAmB,EAAE,KAAK;IAC1BC,IAAI,EAAE;EACV,CAAC;EACDC,QAAQ,EAAE;IACNC,OAAO,EAAEA,CAACpB,KAAK,EAAEqB,MAAM,KAAK;MACxBrB,KAAK,CAACkB,IAAI,GAAGG,MAAM,CAACC,OAAO;IAC/B;EACJ,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAACzC,mBAAmB,CAAC0C,OAAO,EAAG1B,KAAK,IAAK;MAC7CA,KAAK,CAACe,IAAI,GAAG,IAAI;MACjBf,KAAK,CAACgB,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDS,OAAO,CAACzC,mBAAmB,CAAC2C,SAAS,EAAE,CAAC3B,KAAK,EAAEqB,MAAM,KAAK;MACvD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBtB,KAAK,CAACe,IAAI,GAAGM,MAAM,CAACC,OAAO,CAACZ,IAAI;MACpC;MACAV,KAAK,CAACgB,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDS,OAAO,CAACzC,mBAAmB,CAAC4C,QAAQ,EAAG5B,KAAK,IAAK;MAC9CA,KAAK,CAACe,IAAI,GAAG,IAAI;MACjBf,KAAK,CAACgB,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDS,OAAO,CAACpC,eAAe,CAACsC,SAAS,EAAE,CAAC3B,KAAK,EAAEqB,MAAM,KAAK;MACnD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB,MAAM;UAAE9B,MAAM;UAAEqC;QAAO,CAAC,GAAGR,MAAM,CAACC,OAAO;QAEzC,IAAItB,KAAK,CAACe,IAAI,EAAE;UACZf,KAAK,CAACe,IAAI,CAACc,MAAM,GAAGA,MAAM;QAC9B;MACJ;IACJ,CAAC,CAAC,CACDJ,OAAO,CAAC/B,iBAAiB,CAACgC,OAAO,EAAG1B,KAAK,IAAK;MAC3CA,KAAK,CAACiB,mBAAmB,GAAG,IAAI;IACpC,CAAC,CAAC,CACDQ,OAAO,CAAC/B,iBAAiB,CAACiC,SAAS,EAAE,CAAC3B,KAAK,EAAEqB,MAAM,KAAK;MACrD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBtB,KAAK,CAACC,YAAY,GAAGoB,MAAM,CAACC,OAAO,CAACZ,IAAI;MAC5C;MACAV,KAAK,CAACiB,mBAAmB,GAAG,KAAK;IACrC,CAAC,CAAC,CACDQ,OAAO,CAAC/B,iBAAiB,CAACkC,QAAQ,EAAG5B,KAAK,IAAK;MAC5CA,KAAK,CAACiB,mBAAmB,GAAG,KAAK;IACrC,CAAC,CAAC,CACDQ,OAAO,CAAC5B,yBAAyB,CAAC6B,OAAO,EAAG1B,KAAK,IAAK;MACnDA,KAAK,CAACiB,mBAAmB,GAAG,IAAI;IACpC,CAAC,CAAC,CACDQ,OAAO,CAAC5B,yBAAyB,CAAC8B,SAAS,EAAE,CAAC3B,KAAK,EAAEqB,MAAM,KAAK;MAC7D,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBtB,KAAK,CAACC,YAAY,GAAGoB,MAAM,CAACC,OAAO,CAACZ,IAAI;MAC5C;MACAV,KAAK,CAACiB,mBAAmB,GAAG,KAAK;IACrC,CAAC,CAAC,CACDQ,OAAO,CAAC5B,yBAAyB,CAAC+B,QAAQ,EAAG5B,KAAK,IAAK;MACpDA,KAAK,CAACiB,mBAAmB,GAAG,KAAK;IACrC,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEG;AAAQ,CAAC,GAAGR,eAAe,CAACkB,OAAO;AAClD,eAAelB,eAAe,CAACmB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}