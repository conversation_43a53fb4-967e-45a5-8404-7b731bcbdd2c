import api from "./api";

export const getAllExamAPI = ({ search = "", currentPage = 1, limit = 10, sortOrder = 'asc' }) => {
    return api.get("/v1/admin/exam", {
        params: {
            search,
            page: currentPage,
            limit,
            sortOrder,
        }
    });
};

export const getNewestExamAPI = () => {
    return api.get("/v1/user/exam/newest");
}

export const getRelatedExamAPI = (id) => {
    return api.get(`/v1/user/exam/${id}/related`);
}

export const getExamsSavedAPI = () => {
    return api.get("/v1/user/exam/saved");
}

export const getAllPublicExamAPI = (data) => {
    return api.get("/v1/user/exam", {
        params: data
    });
}

export const getExamByIdAPI = (id) => {
    return api.get(`/v1/admin/exam/${id}`);
}

export const findExamsAPI = (search) => {
    return api.get("/v1/admin/exam/search", {
        params: {
            search,
        }
    });
}

export const getExamPublic = (id) => {
    return api.get(`/v1/user/exam/${id}`);
}

export const reExamination = (id) => {
    return api.get(`/v1/user/answer/re-examination/${id}`);
}

export const putExamAPI = async ({ examId, examData }) => {
    const response = await api.put(`/v1/admin/exam/${examId}`, examData);
    return response.data;
}



export const uploadSolutionPdfAPI = async ({ examId, pdfFile }) => {
    const formData = new FormData();

    formData.append("pdf", pdfFile);

    const response = await api.post(`/v1/admin/exam/${examId}/upload-pdf/solution`, formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });

    return response.data;
};

export const uploadExamPdfAPI = async ({ examId, pdfFile }) => {
    const formData = new FormData();

    formData.append("pdf", pdfFile);

    const response = await api.post(`/v1/admin/exam/${examId}/upload-pdf/examFile`, formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });

    return response.data;
}

export const putImageExamAPI = async ({ examId, examImage }) => {
    const formData = new FormData();
    formData.append("examImage", examImage);
    const response = await api.put(`/v1/admin/exam/${examId}/image`, formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
}

export const postExamAPI = async ({ examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }) => {
    const formData = new FormData();

    if (questions.length === 0) {
        formData.append("data", JSON.stringify({ examData }));
    } else {
        formData.append("data", JSON.stringify({ examData, questions }));
    }

    if (examImage) {
        formData.append("examImage", examImage);
    }

    if (examFile) {
        formData.append("pdf", examFile);
    }

    if (questionImages && questionImages.length > 0) {
        questionImages.forEach((file) => {
            if (file !== null) formData.append("questionImages", file);
        });
    }

    if (statementImages && statementImages.length > 0) {
        statementImages.forEach((file) => {
            if (file !== null) formData.append("statementImages", file);
        });
    }

    if (solutionImages && solutionImages.length > 0) {
        solutionImages.forEach((file) => {
            if (file !== null) formData.append("solutionImages", file);
        });
    }

    const response = await api.post("/v1/admin/exam", formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
}

export const saveExamForUserAPI = async ({ examId }) => {
    const response = await api.post('/v1/user/save-exam', { examId });
    return response.data;
}

export const deleteExamAPI = async (id) => {
    const response = await api.delete(`/v1/admin/exam/${id}`);
    return response.data;
}