{"ast": null, "code": "// helpers/fetchWithCacheIfNeeded.js\nexport const fetchWithCacheIfNeeded = async _ref => {\n  var _dataCache$id;\n  let {\n    id,\n    stateKey,\n    cacheKey,\n    dispatch,\n    getState,\n    fetchAction,\n    cacheDuration = 300000 // default 5 phút\n  } = _ref;\n  const state = getState();\n  const dataCache = state[stateKey][cacheKey]; // ví dụ: state.exams.relatedExams\n  const timeCache = state[stateKey][\"lastFetched\".concat(capitalizeFirst(cacheKey))];\n  const hasData = (dataCache === null || dataCache === void 0 ? void 0 : (_dataCache$id = dataCache[id]) === null || _dataCache$id === void 0 ? void 0 : _dataCache$id.length) > 0;\n  const lastFetched = (timeCache === null || timeCache === void 0 ? void 0 : timeCache[id]) || 0;\n  const isCacheValid = Date.now() - lastFetched < cacheDuration;\n  if (hasData && isCacheValid) {\n    return {\n      data: dataCache[id]\n    };\n  }\n  return await dispatch(fetchAction(id)).unwrap();\n};\nconst capitalizeFirst = str => str.charAt(0).toUpperCase() + str.slice(1);", "map": {"version": 3, "names": ["fetchWithCacheIfNeeded", "_ref", "_dataCache$id", "id", "stateKey", "cache<PERSON>ey", "dispatch", "getState", "fetchAction", "cacheDuration", "state", "dataCache", "timeCache", "concat", "capitalizeFirst", "hasData", "length", "lastFetched", "isCache<PERSON><PERSON>d", "Date", "now", "data", "unwrap", "str", "char<PERSON>t", "toUpperCase", "slice"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/helpers/fetchWithCacheIfNeeded.js"], "sourcesContent": ["// helpers/fetchWithCacheIfNeeded.js\r\nexport const fetchWithCacheIfNeeded = async ({\r\n    id,\r\n    stateKey,\r\n    cacheKey,\r\n    dispatch,\r\n    getState,\r\n    fetchAction,\r\n    cacheDuration = 300000, // default 5 phút\r\n}) => {\r\n    const state = getState();\r\n    const dataCache = state[stateKey][cacheKey]; // ví dụ: state.exams.relatedExams\r\n    const timeCache = state[stateKey][`lastFetched${capitalizeFirst(cacheKey)}`];\r\n\r\n    const hasData = dataCache?.[id]?.length > 0;\r\n    const lastFetched = timeCache?.[id] || 0;\r\n    const isCacheValid = Date.now() - lastFetched < cacheDuration;\r\n\r\n    if (hasData && isCacheValid) {\r\n        return { data: dataCache[id] };\r\n    }\r\n\r\n    return await dispatch(fetchAction(id)).unwrap();\r\n};\r\n\r\nconst capitalizeFirst = (str) => str.charAt(0).toUpperCase() + str.slice(1);\r\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,sBAAsB,GAAG,MAAAC,IAAA,IAQhC;EAAA,IAAAC,aAAA;EAAA,IARuC;IACzCC,EAAE;IACFC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,WAAW;IACXC,aAAa,GAAG,MAAM,CAAE;EAC5B,CAAC,GAAAR,IAAA;EACG,MAAMS,KAAK,GAAGH,QAAQ,CAAC,CAAC;EACxB,MAAMI,SAAS,GAAGD,KAAK,CAACN,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAMO,SAAS,GAAGF,KAAK,CAACN,QAAQ,CAAC,eAAAS,MAAA,CAAeC,eAAe,CAACT,QAAQ,CAAC,EAAG;EAE5E,MAAMU,OAAO,GAAG,CAAAJ,SAAS,aAATA,SAAS,wBAAAT,aAAA,GAATS,SAAS,CAAGR,EAAE,CAAC,cAAAD,aAAA,uBAAfA,aAAA,CAAiBc,MAAM,IAAG,CAAC;EAC3C,MAAMC,WAAW,GAAG,CAAAL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGT,EAAE,CAAC,KAAI,CAAC;EACxC,MAAMe,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,WAAW,GAAGR,aAAa;EAE7D,IAAIM,OAAO,IAAIG,YAAY,EAAE;IACzB,OAAO;MAAEG,IAAI,EAAEV,SAAS,CAACR,EAAE;IAAE,CAAC;EAClC;EAEA,OAAO,MAAMG,QAAQ,CAACE,WAAW,CAACL,EAAE,CAAC,CAAC,CAACmB,MAAM,CAAC,CAAC;AACnD,CAAC;AAED,MAAMR,eAAe,GAAIS,GAAG,IAAKA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}