import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import PostClassRequest from '../dtos/requests/class/PostClassRequest.js'
import PutClassRequest from '../dtos/requests/class/PutClassRequest.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as ClassController from '../controllers/ClassController.js'
import uploadGoogleImageMiddleware from '../middlewares/imageGoogleUpload.js'

const router = express.Router()

router.get('/v1/user/class',
    // requireRoles([]),
    asyncHandler(ClassController.getPublicClass)
)

router.get('/v1/user/class/overview',
    requireRoles(Roles.JustStudent),
    as<PERSON><PERSON>and<PERSON>(ClassController.getOverviewClass)
)

router.get('/v1/user/class/code/:classCode',
    requireRoles(Roles.JustStudent),
    async<PERSON>and<PERSON>(ClassController.findClassByCode)
)
router.get('/v1/admin/class/search',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.findClasses)
)
router.get('/v1/admin/class',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.getAllClass)
)

router.get('/v1/user/class/:classCode/lesson/learning-item',
    requireRoles(Roles.JustStudent),
    asyncHandler(ClassController.getDetailLessonLearningItemByClassId)
)

router.get('/v1/admin/class/:id/lessons',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.getFullLessonByClassID)
)

router.get('/v1/user/class/:classCode/learning',
    requireRoles(Roles.JustStudent),
    asyncHandler(ClassController.getFullLessonLearningItemByClassCode)
)

router.get('/v1/user/class/joined',
    requireRoles(Roles.JustStudent),
    asyncHandler(ClassController.getClassByUser)
)

router.get('/v1/admin/class/joined/:userId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.getClassByUserId)
)

router.put('/v1/admin/user/:studentId/class/:classId/accept',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.acceptStudentJoinClass)
)

router.post('/v1/admin/user/:studentId/class/:classId/add',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.addStudentToClass)
)

router.get('/v1/user/class/:classCode',
    requireRoles(Roles.JustStudent),
    asyncHandler(ClassController.getDetailClassByUser)
)

router.get('/v1/admin/class/:id',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.getDetailClassByAdmin)
)

router.post('/v1/user/class/:classCode/join',
    requireRoles(Roles.JustStudent),
    asyncHandler(ClassController.joinClass)
)

router.post('/v1/admin/class',
    validate(PostClassRequest),
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.postClass)
)

router.put('/v1/admin/class/:id',
    validate(PutClassRequest),
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.putClass)
)

router.put('/v1/admin/class/:id/images',
    requireRoles(Roles.AllExceptMarketingStudent),
    uploadGoogleImageMiddleware.fields([
        { name: 'images', maxCount: 5 }
    ]),
    asyncHandler(ClassController.putSlideImagesForClass)
)

router.delete('/v1/admin/class/:id',
    requireRoles(Roles.JustClassManagement),
    asyncHandler(ClassController.deleteClass)
)

router.delete('/v1/admin/user/:studentId/class/:classId/kick',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(ClassController.kickStudentFromClass)
)

export default router