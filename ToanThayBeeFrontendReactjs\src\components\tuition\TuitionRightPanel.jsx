import React from 'react';
import { AlertCircle, X } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { createTuitionPayment, createBatchTuitionPayments } from "src/features/tuition/tuitionSlice";

const TuitionRightPanel = ({
  rightPanelType,
  closeRightPanel,
  batchMonth,
  setBatchMonth,
  batchAmount,
  setBatchAmount,
  batchDueDate,
  setBatchDueDate,
  batchClass,
  setBatchClass,
  batchNote,
  setBatchNote,
  formErrors,
  setFormErrors,
  isSubmitting,
  setIsSubmitting,
  handleBatchTuitionSubmit
}) => {
  const dispatch = useDispatch();

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-white shadow-lg z-50 overflow-y-auto">
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-lg font-semibold">
          {rightPanelType === "add" && "Thêm thanh toán học phí"}
          {rightPanelType === "batchByMonth" && "Tạo học phí hàng loạt"}
          {rightPanelType === "batchByClass" && "Tạo học phí theo lớp"}
        </h2>
        <button
          onClick={closeRightPanel}
          className="text-gray-500 hover:text-gray-700"
        >
          <X size={20} />
        </button>
      </div>

      <div className="p-4">
        {rightPanelType === "add" && (
          <div>
            <p className="mb-4">Thêm khoản thanh toán học phí mới cho học sinh.</p>
            {/* Form content for adding new payment */}
            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Học sinh</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option value="">Chọn học sinh</option>
                  <option value="1">Nguyễn Văn A</option>
                  <option value="2">Trần Thị B</option>
                  <option value="3">Lê Văn C</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Lớp học</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option value="">Chọn lớp học</option>
                  <option value="1">Lớp 10A1</option>
                  <option value="2">Lớp 11A2</option>
                  <option value="3">Lớp 12A3</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tháng</label>
                <input
                  type="month"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền cần đóng</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Nhập số tiền"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền đã đóng</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Nhập số tiền đã đóng"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Hạn thanh toán</label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  rows="3"
                  placeholder="Nhập ghi chú (nếu có)"
                ></textarea>
              </div>
              <button
                type="button"
                className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600"
              >
                Lưu thanh toán
              </button>
            </form>
          </div>
        )}

        {rightPanelType === "batchByMonth" && (
          <div>
            <p className="mb-4">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>
            {/* Form content for batch tuition by month */}
            <form className="space-y-4" onSubmit={(e) => handleBatchTuitionSubmit(e)}>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tháng <span className="text-red-500">*</span></label>
                <input
                  type="month"
                  className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                  value={batchMonth}
                  onChange={(e) => setBatchMonth(e.target.value)}
                  required
                />
                {formErrors.batchMonth && (
                  <p className="mt-1 text-sm text-red-500 flex items-center">
                    <AlertCircle size={14} className="mr-1" /> {formErrors.batchMonth}
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền cần đóng <span className="text-red-500">*</span></label>
                <input
                  type="number"
                  className={`w-full px-3 py-2 border ${formErrors.batchAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                  placeholder="Nhập số tiền"
                  value={batchAmount}
                  onChange={(e) => setBatchAmount(e.target.value)}
                  required
                  min="0"
                />
                {formErrors.batchAmount && (
                  <p className="mt-1 text-sm text-red-500 flex items-center">
                    <AlertCircle size={14} className="mr-1" /> {formErrors.batchAmount}
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Lớp <span className="text-red-500">*</span></label>
                <select
                  className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                  value={batchClass}
                  onChange={(e) => setBatchClass(e.target.value)}
                  required
                >
                  <option value="">Chọn lớp</option>
                  <option value="10">Lớp 10</option>
                  <option value="11">Lớp 11</option>
                  <option value="12">Lớp 12</option>
                </select>
                {formErrors.batchClass && (
                  <p className="mt-1 text-sm text-red-500 flex items-center">
                    <AlertCircle size={14} className="mr-1" /> {formErrors.batchClass}
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Hạn thanh toán <span className="text-red-500">*</span></label>
                <input
                  type="date"
                  className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                  value={batchDueDate}
                  onChange={(e) => setBatchDueDate(e.target.value)}
                  required
                />
                {formErrors.batchDueDate && (
                  <p className="mt-1 text-sm text-red-500 flex items-center">
                    <AlertCircle size={14} className="mr-1" /> {formErrors.batchDueDate}
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  rows="3"
                  placeholder="Nhập ghi chú (nếu có)"
                  value={batchNote}
                  onChange={(e) => setBatchNote(e.target.value)}
                ></textarea>
              </div>
              <button
                type="submit"
                className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Đang xử lý..." : "Tạo học phí hàng loạt"}
              </button>
            </form>
          </div>
        )}

        {rightPanelType === "batchByClass" && (
          <div>
            <p className="mb-4">Tạo học phí cho tất cả học sinh trong một lớp học.</p>
            {/* Form content for batch tuition by class */}
            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Lớp học</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option value="">Chọn lớp học</option>
                  <option value="1">Lớp 10A1</option>
                  <option value="2">Lớp 11A2</option>
                  <option value="3">Lớp 12A3</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tháng</label>
                <input
                  type="month"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Nhập số tiền"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Hạn thanh toán</label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  rows="3"
                  placeholder="Nhập ghi chú (nếu có)"
                ></textarea>
              </div>
              <button
                type="button"
                className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600"
              >
                Tạo học phí theo lớp
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default TuitionRightPanel;
