import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
    Users,
    FileText,
    CreditCard,
    Newspaper,
    Globe,
    School,
    ClipboardList,
    FileSearch,
    DollarSign,
    PlusCircle,
    Settings
} from "lucide-react";
import UserType from "../../constants/UserType";
import {
    fetchDashboardStats,
    fetchRecentActivities,
    fetchSystemStatus
} from "../../features/dashboard/dashboardSlice";
import AdminLayout from "../../components/layout/AdminLayout";

// Dashboard Stats Card Component
const StatsCard = ({ title, value, icon: Icon, color, trend, onClick, loading = false }) => {
    return (
        <div
            className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 ${onClick ? 'cursor-pointer hover:scale-105' : ''}`}
            onClick={onClick}
        >
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    {loading ? (
                        <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mt-2"></div>
                    ) : (
                        <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
                    )}
                    {trend && (
                        <p className={`text-sm mt-1 ${trend.positive ? 'text-green-600' : 'text-red-600'}`}>
                            {trend.positive ? '↗' : '↘'} {trend.value}
                        </p>
                    )}
                </div>
                <div className={`p-3 rounded-full ${color}`}>
                    <Icon className="w-6 h-6 text-white" />
                </div>
            </div>
        </div>
    );
};

// Quick Action Card Component
const QuickActionCard = ({ title, description, icon: Icon, color, onClick, disabled = false }) => {
    return (
        <div
            className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 transition-all duration-200 ${disabled
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-md cursor-pointer hover:scale-105'
                }`}
            onClick={disabled ? undefined : onClick}
        >
            <div className="flex items-start space-x-4">
                <div className={`p-3 rounded-lg ${color}`}>
                    <Icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
                    <p className="text-sm text-gray-600 mt-1">{description}</p>
                </div>
            </div>
        </div>
    );
};

export default function AdminDashboard() {
    const { user } = useSelector((state) => state.auth);
    const { stats, loading, recentActivities, systemStatus } = useSelector((state) => state.dashboard || {
        stats: {
            students: { count: 0, trend: null },
            classes: { count: 0, trend: null },
            exams: { count: 0, trend: null },
            questions: { count: 0, trend: null },
            articles: { count: 0, trend: null },
            tuitionRevenue: { count: 0, trend: null }
        },
        loading: { stats: false },
        recentActivities: [],
        systemStatus: { server: 'unknown', database: 'unknown', aiService: 'unknown' }
    });

    const navigate = useNavigate();
    const dispatch = useDispatch();

    const userType = user?.userType;

    // Permission-based access control
    const hasAccess = (requiredRoles) => {
        if (!userType) return false;
        return requiredRoles.includes(userType);
    };

    // Load dashboard data
    useEffect(() => {
        const loadDashboardData = async () => {
            try {
                // Load dashboard stats with fallback to mock data
                try {
                    await dispatch(fetchDashboardStats()).unwrap();
                } catch (error) {
                    console.warn('Failed to load real stats, using mock data:', error);
                    // Mock data fallback
                    dispatch({
                        type: 'dashboard/updateStats',
                        payload: {
                            students: { count: 1247, trend: { positive: true, value: '+12%' } },
                            classes: { count: 45, trend: { positive: true, value: '+3%' } },
                            exams: { count: 89, trend: { positive: false, value: '-2%' } },
                            questions: { count: 2156, trend: { positive: true, value: '+8%' } },
                            articles: { count: 34, trend: { positive: true, value: '+5%' } },
                            tuitionRevenue: { count: '2.4M', trend: { positive: true, value: '+15%' } }
                        }
                    });
                }

                // Load other data
                dispatch(fetchRecentActivities());
                dispatch(fetchSystemStatus());
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        };

        loadDashboardData();
    }, [dispatch]);

    // Define stats cards based on user permissions
    const getStatsCards = () => {
        const cards = [];

        if (hasAccess([UserType.ADMIN, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT])) {
            cards.push({
                title: "Tổng học sinh",
                value: stats.students.count.toLocaleString(),
                icon: Users,
                color: "bg-blue-500",
                trend: stats.students.trend,
                onClick: () => navigate('/admin/student-management')
            });
        }

        if (hasAccess([UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT])) {
            cards.push({
                title: "Lớp học",
                value: stats.classes.count,
                icon: School,
                color: "bg-green-500",
                trend: stats.classes.trend,
                onClick: () => navigate('/admin/class-management')
            });
        }

        if (hasAccess([UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT])) {
            cards.push({
                title: "Đề thi",
                value: stats.exams.count,
                icon: FileText,
                color: "bg-purple-500",
                trend: stats.exams.trend,
                onClick: () => navigate('/admin/exam-management')
            });
        }

        if (hasAccess([UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT])) {
            cards.push({
                title: "Câu hỏi",
                value: stats.questions.count.toLocaleString(),
                icon: FileSearch,
                color: "bg-orange-500",
                trend: stats.questions.trend,
                onClick: () => navigate('/admin/question-management')
            });
        }

        if (hasAccess([UserType.ADMIN, UserType.HUMANRESOURCEMANAGEMENT])) {
            cards.push({
                title: "Doanh thu học phí",
                value: stats.tuitionRevenue.count,
                icon: DollarSign,
                color: "bg-emerald-500",
                trend: stats.tuitionRevenue.trend,
                onClick: () => navigate('/admin/tuition-payment')
            });
        }

        if (hasAccess([UserType.ADMIN])) {
            cards.push({
                title: "Bài viết",
                value: stats.articles.count,
                icon: Newspaper,
                color: "bg-indigo-500",
                trend: stats.articles.trend,
                onClick: () => navigate('/admin/article-management')
            });
        }

        return cards;
    };

    // Define quick actions based on user permissions
    const getQuickActions = () => {
        const actions = [];

        if (hasAccess([UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT])) {
            actions.push({
                title: "Tạo đề thi mới",
                description: "Tạo đề thi từ AI hoặc thủ công",
                icon: PlusCircle,
                color: "bg-blue-500",
                onClick: () => navigate('/admin/exam-management')
            });
        }

        if (hasAccess([UserType.ADMIN, UserType.CLASSMANAGEMENT])) {
            actions.push({
                title: "Quản lý lớp học",
                description: "Xem và quản lý tất cả lớp học",
                icon: School,
                color: "bg-green-500",
                onClick: () => navigate('/admin/class-management')
            });
        }

        if (hasAccess([UserType.ADMIN, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT])) {
            actions.push({
                title: "Điểm danh học sinh",
                description: "Theo dõi và quản lý điểm danh",
                icon: ClipboardList,
                color: "bg-purple-500",
                onClick: () => navigate('/admin/class-management') // Replace with actual user selection
            });
        }

        if (hasAccess([UserType.ADMIN, UserType.HUMANRESOURCEMANAGEMENT])) {
            actions.push({
                title: "Quản lý học phí",
                description: "Theo dõi thanh toán và doanh thu",
                icon: CreditCard,
                color: "bg-emerald-500",
                onClick: () => navigate('/admin/tuition-payment')
            });
        }

        if (hasAccess([UserType.ADMIN])) {
            actions.push({
                title: "Viết bài mới",
                description: "Tạo bài viết cho trang chủ",
                icon: Newspaper,
                color: "bg-indigo-500",
                onClick: () => navigate('/admin/article-post')
            });
        }

        if (hasAccess([UserType.ADMIN, UserType.MARKETING])) {
            actions.push({
                title: "Quản lý trang chủ",
                description: "Cập nhật nội dung trang chủ",
                icon: Globe,
                color: "bg-orange-500",
                onClick: () => navigate('/admin/homepage-management')
            });
        }

        return actions;
    };

    const statsCards = getStatsCards();
    const quickActions = getQuickActions();

    return (
        <AdminLayout
            title="Dashboard Admin"
            subtitle={`Chào mừng trở lại, ${user?.lastName + " " + user?.firstName || user?.username}`}
        >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

                {/* Stats Cards */}
                {statsCards.length > 0 && (
                    <div className="mb-8">
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">Thống kê tổng quan (Dữ liệu fake đừng tin để làm sau)</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            {statsCards.map((card, index) => (
                                <StatsCard
                                    key={index}
                                    title={card.title}
                                    value={card.value}
                                    icon={card.icon}
                                    color={card.color}
                                    trend={card.trend}
                                    onClick={card.onClick}
                                    loading={loading?.stats || false}
                                />
                            ))}
                        </div>
                    </div>
                )}

                {/* Quick Actions */}
                {quickActions.length > 0 && (
                    <div className="mb-8">
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">Thao tác nhanh</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {quickActions.map((action, index) => (
                                <QuickActionCard
                                    key={index}
                                    title={action.title}
                                    description={action.description}
                                    icon={action.icon}
                                    color={action.color}
                                    onClick={action.onClick}
                                />
                            ))}
                        </div>
                    </div>
                )}

                {/* Recent Activity or Additional Widgets */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Recent Activity */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Hoạt động gần đây</h3>
                        <div className="space-y-4">
                            <div className="flex items-center space-x-3">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <p className="text-sm text-gray-600">Đề thi "Toán 12 - Chương 1" được tạo</p>
                                <span className="text-xs text-gray-400">2 giờ trước</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <p className="text-sm text-gray-600">15 học sinh mới đăng ký</p>
                                <span className="text-xs text-gray-400">4 giờ trước</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <p className="text-sm text-gray-600">Lớp "Toán 12A1" hoàn thành điểm danh</p>
                                <span className="text-xs text-gray-400">6 giờ trước</span>
                            </div>
                        </div>
                    </div>

                    {/* System Status */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Trạng thái hệ thống</h3>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Server Status</span>
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Online
                                </span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Database</span>
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Connected
                                </span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">AI Service</span>
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Maintenance
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* No Access Message */}
                {statsCards.length === 0 && quickActions.length === 0 && (
                    <div className="text-center py-12">
                        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <Settings className="w-12 h-12 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Không có quyền truy cập</h3>
                        <p className="text-gray-600">
                            Bạn không có quyền truy cập vào các chức năng quản lý.
                            Vui lòng liên hệ quản trị viên để được cấp quyền.
                        </p>
                    </div>
                )}
            </div>
        </AdminLayout>
    );
}
