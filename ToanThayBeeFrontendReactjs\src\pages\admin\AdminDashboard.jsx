import React from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";

// <PERSON><PERSON><PERSON> mục quản lý
const allNavItems = [
    { label: "<PERSON>uản lý Đề thi", path: "/exam-management", key: "exam" },
    { label: "Chỉnh sửa Câu hỏi", path: "/question-editor", key: "question" },
    { label: "<PERSON><PERSON> sách lớp học", path: "/classes", key: "class" },
    { label: "Điểm danh học sinh", path: "/attendance", key: "attendance" },
    { label: "Quản lý học phí", path: "/tuition", key: "tuition" },
    { label: "Quản lý trang chủ", path: "/homepage", key: "homepage" },
];

// Quyền truy cập theo userType
const navAccessByUserType = {
    HS1: [],
    GV: ["exam", "question", "class", "attendance", "homepage"],
    AD: ["exam", "question", "class", "attendance", "tuition", "homepage"],
    AS: ["class", "attendance"],
    QL: ["class", "attendance", "tuition"],
    QLLH: ["exam", "question", "class", "attendance"],
    MKT: ["homepage"],
};

export default function AdminDashboard() {
    const { user } = useSelector((state) => state.auth);
    const userType = user?.userType;

    const allowedKeys = navAccessByUserType[userType] || [];
    const allowedItems = allNavItems.filter(item => allowedKeys.includes(item.key));

    return (
        <div className="max-w-5xl mx-auto px-4 py-10">
            <h1 className="text-3xl font-bold mb-8 text-center">Trang Quản Trị</h1>

            {allowedItems.length === 0 ? (
                <p className="text-center text-gray-500">Bạn không có quyền truy cập chức năng quản lý nào.</p>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                    {allowedItems.map((item, idx) => (
                        <Link
                            key={idx}
                            to={item.path}
                            className="block p-6 border rounded-xl shadow hover:shadow-md hover:bg-gray-50 transition duration-200"
                        >
                            <h3 className="text-lg font-semibold text-gray-800">{item.label}</h3>
                        </Link>
                    ))}
                </div>
            )}
        </div>
    );
}
