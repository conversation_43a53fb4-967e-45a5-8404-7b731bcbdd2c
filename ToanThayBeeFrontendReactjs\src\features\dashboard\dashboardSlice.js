import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as dashboardApi from "../../services/dashboardApi";
import { apiHandler } from "../../utils/apiHandler";

// Async thunks
export const fetchDashboardStats = createAsyncThunk(
    "dashboard/fetchDashboardStats",
    async (_, { dispatch }) => {
        return await apiHandler(
            dispatch,
            dashboardApi.getDashboardStatsAPI,
            null,
            null,
            false,
            false,
            false,
            false
        );
    }
);

export const fetchStudentStats = createAsyncThunk(
    "dashboard/fetchStudentStats",
    async (params, { dispatch }) => {
        return await apiHandler(
            dispatch,
            dashboardApi.getStudentStatsAPI,
            params,
            null,
            false,
            false,
            false,
            false
        );
    }
);

export const fetchClassStats = createAsyncThunk(
    "dashboard/fetchClassStats",
    async (params, { dispatch }) => {
        return await apiHandler(
            dispatch,
            dashboardApi.getClassStatsAPI,
            params,
            null,
            false,
            false,
            false,
            false
        );
    }
);

export const fetchExamStats = createAsyncThunk(
    "dashboard/fetchExamStats",
    async (params, { dispatch }) => {
        return await apiHandler(
            dispatch,
            dashboardApi.getExamStatsAPI,
            params,
            null,
            false,
            false,
            false,
            false
        );
    }
);

export const fetchQuestionStats = createAsyncThunk(
    "dashboard/fetchQuestionStats",
    async (params, { dispatch }) => {
        return await apiHandler(
            dispatch,
            dashboardApi.getQuestionStatsAPI,
            params,
            null,
            false,
            false,
            false,
            false
        );
    }
);

export const fetchRecentActivities = createAsyncThunk(
    "dashboard/fetchRecentActivities",
    async (params, { dispatch }) => {
        return await apiHandler(
            dispatch,
            dashboardApi.getRecentActivitiesAPI,
            params,
            null,
            false,
            false,
            false,
            false
        );
    }
);

export const fetchSystemStatus = createAsyncThunk(
    "dashboard/fetchSystemStatus",
    async (_, { dispatch }) => {
        return await apiHandler(
            dispatch,
            dashboardApi.getSystemStatusAPI,
            null,
            null,
            false,
            false,
            false,
            false
        );
    }
);

// Initial state
const initialState = {
    // Statistics
    stats: {
        students: { count: 0, trend: null },
        classes: { count: 0, trend: null },
        exams: { count: 0, trend: null },
        questions: { count: 0, trend: null },
        articles: { count: 0, trend: null },
        tuitionRevenue: { count: 0, trend: null }
    },
    
    // Recent activities
    recentActivities: [],
    
    // System status
    systemStatus: {
        server: 'unknown',
        database: 'unknown',
        aiService: 'unknown'
    },
    
    // Loading states
    loading: {
        stats: false,
        activities: false,
        systemStatus: false
    },
    
    // Error states
    error: {
        stats: null,
        activities: null,
        systemStatus: null
    }
};

// Dashboard slice
const dashboardSlice = createSlice({
    name: "dashboard",
    initialState,
    reducers: {
        clearErrors: (state) => {
            state.error = {
                stats: null,
                activities: null,
                systemStatus: null
            };
        },
        updateStats: (state, action) => {
            state.stats = { ...state.stats, ...action.payload };
        }
    },
    extraReducers: (builder) => {
        builder
            // Dashboard stats
            .addCase(fetchDashboardStats.pending, (state) => {
                state.loading.stats = true;
                state.error.stats = null;
            })
            .addCase(fetchDashboardStats.fulfilled, (state, action) => {
                state.loading.stats = false;
                if (action.payload) {
                    state.stats = { ...state.stats, ...action.payload };
                }
            })
            .addCase(fetchDashboardStats.rejected, (state, action) => {
                state.loading.stats = false;
                state.error.stats = action.error.message;
            })
            
            // Recent activities
            .addCase(fetchRecentActivities.pending, (state) => {
                state.loading.activities = true;
                state.error.activities = null;
            })
            .addCase(fetchRecentActivities.fulfilled, (state, action) => {
                state.loading.activities = false;
                if (action.payload) {
                    state.recentActivities = action.payload;
                }
            })
            .addCase(fetchRecentActivities.rejected, (state, action) => {
                state.loading.activities = false;
                state.error.activities = action.error.message;
            })
            
            // System status
            .addCase(fetchSystemStatus.pending, (state) => {
                state.loading.systemStatus = true;
                state.error.systemStatus = null;
            })
            .addCase(fetchSystemStatus.fulfilled, (state, action) => {
                state.loading.systemStatus = false;
                if (action.payload) {
                    state.systemStatus = action.payload;
                }
            })
            .addCase(fetchSystemStatus.rejected, (state, action) => {
                state.loading.systemStatus = false;
                state.error.systemStatus = action.error.message;
            });
    }
});

// Export actions
export const { clearErrors, updateStats } = dashboardSlice.actions;

// Export reducer
export default dashboardSlice.reducer;
