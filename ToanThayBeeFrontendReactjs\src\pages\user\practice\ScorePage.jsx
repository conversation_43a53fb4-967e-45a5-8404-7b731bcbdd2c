import UserLayout from "../../../layouts/UserLayout";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { fetchQuestionAndAnswersByAttempt } from "../../../features/answer/answerSlice";
import { reExamination } from "../../../features/exam/examSlice";
import LatexRenderer from "../../../components/latex/RenderLatex";
import { useMemo } from "react";
import LoadingSpinner from "../../../components/loading/LoadingSpinner";
import PdfViewer from "../../../components/ViewPdf";
import ReportButton from "../../../components/button/ReportButton";
import { setErrorMessage } from "src/features/state/stateApiSlice";
import Breadcrumb from "../../../components/breadcrumb/Breadcrumb";
import ModernScoreSummaryTable from "../../../components/bar/ModernScoreSummaryTable";
import ModernAnswerSummaryChart from "../../../components/bar/ModernAnswerSummaryChart";
import {
    CheckCircle,
    XCircle,
    AlertCircle,
    RefreshCw,
    ChevronDown,
    ChevronUp,
    FileText,
    Award,
    BookOpen,
    Clock,
    BarChart2,
    TrendingUp
} from "lucide-react";
import MarkdownPreviewWithMath from "src/components/latex/MarkDownPreview";



const calculateStatsFromAnswers = (answers, questions) => {
    const stats = {
        TN: { correct: 0, incorrect: 0, unanswered: 0, score: 0 },
        DS: { correct: 0, incorrect: 0, unanswered: 0, score: 0 },
        TLN: { correct: 0, incorrect: 0, unanswered: 0, score: 0 },
    };

    answers.forEach(answer => {
        const { typeOfQuestion, result } = answer;
        if (!stats[typeOfQuestion]) return;

        if (typeOfQuestion === "DS") {
            try {
                const parsed = JSON.parse(answer.answerContent);
                let count = 0;
                parsed.forEach(item => {
                    if (item.answer === questions.find(q => q.id === answer.questionId).statements.find(s => s.id === item.statementId).isCorrect) count += 1;
                })
                if (count === 1) stats[typeOfQuestion].score += 0.1;
                else if (count === 2) stats[typeOfQuestion].score += 0.25;
                else if (count === 3) stats[typeOfQuestion].score += 0.5;
                else if (count === 4) stats[typeOfQuestion].score += 1;
            } catch (err) {
                // Handle error silently
            }
        }

        if (result === true) {
            stats[typeOfQuestion].correct += 1;
            if (typeOfQuestion === "TN") stats[typeOfQuestion].score += 0.25;
            else if (typeOfQuestion === "TLN") stats[typeOfQuestion].score += 0.5;
        } else if (result === false) {
            stats[typeOfQuestion].incorrect += 1;
        } else {
            stats[typeOfQuestion].unanswered += 1;
        }
    });

    return stats;
};

const SolutionContent = ({ question }) => {
    return (
        <div className="p-4 bg-sky-50 border border-sky-100 rounded-md">
            <h4 className="font-semibold text-sky-800 mb-2">Lời giải:</h4>

            {question.solution ? (
                <>
                    <div className="text-gray-800 ">
                        <MarkdownPreviewWithMath content={question.solution} />
                    </div>
                    {question.solutionImageUrl && (
                        <div className="flex justify-center">
                            <img
                                src={question.solutionImageUrl}
                                alt="Lời giải"
                                className="max-h-80 object-contain rounded-md border border-gray-200"
                            />
                        </div>
                    )}
                    {question.solutionUrl && (
                        <a
                            href={question.solutionUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sky-600 hover:text-sky-800 underline flex items-center gap-1"
                        >
                            <FileText size={16} />
                            Xem lời giải chi tiết
                        </a>
                    )}
                </>
            ) : (
                <p className="text-gray-600 italic">Không có lời giải cho câu hỏi này.</p>
            )}
        </div>
    );
};

const ScorePage = () => {
    const { attemptId } = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { exam } = useSelector((state) => state.exams);
    const { questions } = useSelector((state) => state.questions);
    const { answers, score } = useSelector((state) => state.answers);
    const { loading } = useSelector((state) => state.states);
    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];
    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];
    const [questionTN, setQuestionTN] = useState([]);
    const [questionDS, setQuestionDS] = useState([]);
    const [questionTLN, setQuestionTLN] = useState([]);
    const [answersDS, setAnswersDS] = useState({});
    const [answersTNTLN, setAnswersTNTLN] = useState({});
    const correctCount = answers.filter(a => a.result === true).length;
    const incorrectCount = answers.filter(a => a.result === false).length;
    const unansweredCount = answers.filter(a => a.result === null || a.answerContent === null).length;
    const [isPart1, setIsPart1] = useState(true);
    const [isPart2, setIsPart2] = useState(false);
    const [isPart3, setIsPart3] = useState(false);
    const stats = useMemo(() => calculateStatsFromAnswers(answers, questions), [answers, questions]);

    const [shownSolutions, setShownSolutions] = useState({});
    const [isReexamining, setIsReexamining] = useState(false);

    // Hàm xử lý chấm lại bài
    const handleReExamination = () => {
        setIsReexamining(true);
        dispatch(reExamination(attemptId))
            .unwrap()
            .then(() => {
                dispatch(fetchQuestionAndAnswersByAttempt({ attemptId }));
            })
            .finally(() => { setIsReexamining(false) });
    };

    useEffect(() => {
        if (questions) {
            setQuestionTN(questions.filter((question) => question.typeOfQuestion === "TN"));
            setQuestionDS(questions.filter((question) => question.typeOfQuestion === "DS"));
            setQuestionTLN(questions.filter((question) => question.typeOfQuestion === "TLN"));
        }
    }, [questions]);

    useEffect(() => {
        const dsAnswers = {};
        const dsAnswersTNTLN = {};
        answers.forEach((answer) => {
            if (answer.typeOfQuestion === "DS") {
                try {
                    if (!answer.answerContent) return;
                    const parsed = JSON.parse(answer.answerContent); // là mảng

                    parsed.forEach(item => {
                        dsAnswers[item.statementId] = item.answer; // ví dụ: { 225: false, 226: false, ... }
                    });
                } catch (err) {
                    console.error("Lỗi parse answerContent:", err);
                }
            } else {
                dsAnswersTNTLN[answer.questionId] = {
                    result: answer.result,
                    answer: answer.answerContent
                };
            }
        });
        setAnswersDS(dsAnswers);
        setAnswersTNTLN(dsAnswersTNTLN);
    }, [answers]);

    useEffect(() => {
        dispatch(fetchQuestionAndAnswersByAttempt({ attemptId }));
    }, [dispatch, attemptId]);

    useEffect(() => {
        if (exam && !exam.seeCorrectAnswer) {
            dispatch(setErrorMessage("Không thể xem kết quả bài thi này!"));
            navigate(`/practice/exam/${exam.id}`);
        }
    }, [exam, dispatch, navigate]);

    const toggleSolution = (questionId) => {
        setShownSolutions(prev => ({
            ...prev,
            [questionId]: !prev[questionId]
        }));
    };

    // Render status icon based on result
    const renderStatusIcon = (result) => {
        if (result === true) {
            return <CheckCircle size={20} className="text-green-500" />;
        } else if (result === false) {
            return <XCircle size={20} className="text-red-500" />;
        } else {
            return <AlertCircle size={20} className="text-amber-500" />;
        }
    };

    // Get score color based on value
    const getScoreColor = (scoreValue) => {
        if (scoreValue >= 8) return "text-green-600";
        if (scoreValue >= 6.5) return "text-blue-600";
        if (scoreValue >= 5) return "text-amber-600";
        return "text-red-600";
    };

    if (loading) {
        return (
            <UserLayout>
                <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 px-4 py-6">
                    <div className="w-full max-w-7xl mx-auto flex items-center justify-center">
                        <div className=" p-8 flex flex-col items-center">
                            <LoadingSpinner
                                type="dots"
                                color="border-sky-600"
                                size="5rem"
                                showText={true}
                                text="Đang tải kết quả bài thi..."
                            />
                        </div>
                    </div>
                </div>
            </UserLayout>
        );
    }

    return (
        <UserLayout>
            <div className="flex flex-col items-center bg-gray-50 px-4 py-6">
                <div className="w-full max-w-7xl mx-auto overflow-y-auto hide-scrollbar">
                    {/* Breadcrumb */}
                    <Breadcrumb
                        items={[
                            { label: "Trang chủ", path: "/", icon: "home" },
                            { label: "Luyện đề", path: "/practice", icon: "practice" },
                            { label: exam?.name, path: `/practice/exam/${exam?.id}`, icon: "exam" },
                            { label: "Lịch sử làm bài", path: `/practice/exam/${exam?.id}/history`, icon: "history" },
                            { label: "Kết quả", path: "", icon: "score", active: true }
                        ]}
                    />

                    {/* Main Content */}
                    <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 transition-all duration-300 mb-8">
                        {/* Header with exam name and score */}
                        <div className="bg-gradient-to-r from-sky-50 to-white p-6 border-b border-gray-100">
                            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                                <div>
                                    <h1 className="text-xl md:text-2xl font-bold text-sky-800 mb-2">
                                        {exam?.name}
                                    </h1>
                                    <div className="flex items-center gap-2 text-gray-600">
                                        <Clock size={16} />
                                        <span className="text-sm">Thời gian làm bài: {exam?.testDuration} phút</span>
                                    </div>
                                </div>

                                {score !== undefined && (
                                    <div className="flex flex-col items-end">
                                        <div className="text-sm text-gray-600 mb-1">Điểm của bạn</div>
                                        <div className={`text-3xl font-bold ${getScoreColor(score)}`}>
                                            {score}/10
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Score summary section */}
                        <div className="p-6 border-b border-gray-100">
                            <div className="flex flex-col gap-4">
                                <div className="flex items-center justify-between">
                                    <h2 className="text-lg md:text-xl font-semibold text-gray-800 flex items-center gap-2">
                                        <Award size={20} className="text-sky-600" />
                                        Thống kê kết quả
                                    </h2>

                                    <button
                                        onClick={handleReExamination}
                                        disabled={isReexamining}
                                        className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white font-medium transition-all ${isReexamining
                                            ? 'bg-gray-400 cursor-not-allowed'
                                            : 'bg-sky-600 hover:bg-sky-700 shadow-md hover:shadow-lg'
                                            }`}
                                    >
                                        {isReexamining ? (
                                            <>
                                                <RefreshCw size={16} className="animate-spin" />
                                                <span>Đang chấm lại...</span>
                                            </>
                                        ) : (
                                            <>
                                                <RefreshCw size={16} />
                                                <span>Chấm lại bài</span>
                                            </>
                                        )}
                                    </button>
                                </div>

                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4">
                                    <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-4 hover:shadow-md transition-shadow">
                                        <ModernScoreSummaryTable stats={stats} />
                                    </div>
                                    <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-4 hover:shadow-md transition-shadow">
                                        <ModernAnswerSummaryChart
                                            correct={correctCount}
                                            incorrect={incorrectCount}
                                            unanswered={unansweredCount}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* PDF Solution section */}
                        {exam?.solutionPdfUrl && (
                            <div className="p-6 border-b border-gray-100">
                                <h2 className="text-lg md:text-xl font-semibold text-gray-800 flex items-center gap-2 mb-4">
                                    <FileText size={20} className="text-sky-600" />
                                    Lời giải chi tiết
                                </h2>
                                <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                                    <PdfViewer url={exam?.solutionPdfUrl} />
                                </div>
                            </div>
                        )}

                        {/* Question details section */}
                        <div className="p-6">
                            <h2 className="text-lg md:text-xl font-semibold text-gray-800 flex items-center gap-2 mb-6">
                                <BookOpen size={20} className="text-sky-600" />
                                Chi tiết bài làm
                            </h2>

                            {/* Tab navigation */}
                            <div className="flex flex-wrap gap-2 mb-6">
                                <button
                                    onClick={() => {
                                        setIsPart1(true);
                                        setIsPart2(false);
                                        setIsPart3(false);
                                    }}
                                    className={`px-4 py-2 rounded-full font-medium text-sm transition-all ${isPart1
                                        ? 'bg-sky-600 text-white shadow-md'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    Phần I - Trắc nghiệm
                                </button>
                                <button
                                    onClick={() => {
                                        setIsPart1(false);
                                        setIsPart2(true);
                                        setIsPart3(false);
                                    }}
                                    className={`px-4 py-2 rounded-full font-medium text-sm transition-all ${isPart2
                                        ? 'bg-sky-600 text-white shadow-md'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    Phần II - Đúng sai
                                </button>
                                <button
                                    onClick={() => {
                                        setIsPart1(false);
                                        setIsPart2(false);
                                        setIsPart3(true);
                                    }}
                                    className={`px-4 py-2 rounded-full font-medium text-sm transition-all ${isPart3
                                        ? 'bg-sky-600 text-white shadow-md'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    Phần III - Trả lời ngắn
                                </button>
                            </div>

                            {/* Question content based on selected tab */}
                            <div className="mt-4">
                                {isPart1 && (
                                    <div className="space-y-6">
                                        {questionTN.map((question, idx) => {
                                            const userAnswerId = answersTNTLN[question.id]?.answer;
                                            const isCorrect = answersTNTLN[question.id]?.result;

                                            return (
                                                <div
                                                    key={question.id + "TN"}
                                                    className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                                                >
                                                    {/* Question header */}
                                                    <div className={`p-4 flex items-center justify-between border-b ${isCorrect === true
                                                        ? 'bg-green-50 border-green-100'
                                                        : isCorrect === false
                                                            ? 'bg-red-50 border-red-100'
                                                            : 'bg-amber-50 border-amber-100'
                                                        }`}>
                                                        <div className="flex items-center gap-2">
                                                            <div className={`p-1.5 rounded-full ${isCorrect === true
                                                                ? 'bg-green-100'
                                                                : isCorrect === false
                                                                    ? 'bg-red-100'
                                                                    : 'bg-amber-100'
                                                                }`}>
                                                                {renderStatusIcon(isCorrect)}
                                                            </div>
                                                            <h3 className="font-semibold text-gray-800">
                                                                Câu {idx + 1}
                                                            </h3>
                                                        </div>
                                                        <ReportButton questionId={question.id} />
                                                    </div>

                                                    {/* Question content */}
                                                    <div className="p-4">
                                                        <div className="mb-4">
                                                            <LatexRenderer text={question.content} />

                                                            {question.imageUrl && (
                                                                <div className="mt-3 flex justify-center">
                                                                    <img
                                                                        src={question.imageUrl}
                                                                        alt="Hình minh họa"
                                                                        className="max-h-64 object-contain rounded-md border border-gray-200"
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>

                                                        {/* Answer options */}
                                                        <div className="space-y-2 mt-4">
                                                            {question.statements.map((statement, index) => {
                                                                const isUserSelected = statement.id == userAnswerId;
                                                                const isStatementCorrect = statement.isCorrect === true;

                                                                return (
                                                                    <div
                                                                        key={statement.id}
                                                                        className={`p-3 rounded-md flex items-start gap-3 ${isStatementCorrect
                                                                            ? 'bg-green-50 border border-green-200'
                                                                            : isUserSelected
                                                                                ? 'bg-red-50 border border-red-200'
                                                                                : 'bg-gray-50 border border-gray-200'
                                                                            }`}
                                                                    >
                                                                        <div className="flex-shrink-0 mt-0.5">
                                                                            <span className="font-semibold text-gray-700">
                                                                                {prefixStatementTN[index]}
                                                                            </span>
                                                                        </div>
                                                                        <div className="flex-1">
                                                                            <LatexRenderer text={statement.content} />
                                                                        </div>
                                                                        <div className="flex-shrink-0">
                                                                            {isStatementCorrect && (
                                                                                <CheckCircle size={18} className="text-green-500" />
                                                                            )}
                                                                            {!isStatementCorrect && isUserSelected && (
                                                                                <XCircle size={18} className="text-red-500" />
                                                                            )}
                                                                        </div>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    </div>

                                                    {/* Solution toggle button */}
                                                    <div className="px-4 pb-4 flex justify-start">
                                                        <button
                                                            onClick={() => toggleSolution(question.id)}
                                                            className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
                                                        >
                                                            {shownSolutions[question.id] ? (
                                                                <>
                                                                    <ChevronUp size={16} />
                                                                    <span>Ẩn lời giải</span>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <ChevronDown size={16} />
                                                                    <span>Hiển thị lời giải</span>
                                                                </>
                                                            )}
                                                        </button>
                                                    </div>

                                                    {/* Solution content */}
                                                    {shownSolutions[question.id] && (
                                                        <div className="px-4 pb-4">
                                                            <SolutionContent question={question} />
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                )}

                                {/* Phần II - Đúng sai */}
                                {isPart2 && (
                                    <div className="space-y-6">
                                        {questionDS.map((question, idx) => {
                                            // Kiểm tra xem có câu trả lời nào cho câu hỏi này không
                                            const answer = answers.find(a => a.questionId === question.id);
                                            const isCorrect = answer?.result;

                                            return (
                                                <div
                                                    key={question.id + "DS"}
                                                    className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                                                >
                                                    {/* Question header */}
                                                    <div className={`p-4 flex items-center justify-between border-b ${isCorrect === true
                                                        ? 'bg-green-50 border-green-100'
                                                        : isCorrect === false
                                                            ? 'bg-red-50 border-red-100'
                                                            : 'bg-amber-50 border-amber-100'
                                                        }`}>
                                                        <div className="flex items-center gap-2">
                                                            <div className={`p-1.5 rounded-full ${isCorrect === true
                                                                ? 'bg-green-100'
                                                                : isCorrect === false
                                                                    ? 'bg-red-100'
                                                                    : 'bg-amber-100'
                                                                }`}>
                                                                {renderStatusIcon(isCorrect)}
                                                            </div>
                                                            <h3 className="font-semibold text-gray-800">
                                                                Câu {idx + 1}
                                                            </h3>
                                                        </div>
                                                        <ReportButton questionId={question.id} />
                                                    </div>

                                                    {/* Question content */}
                                                    <div className="p-4">
                                                        <div className="mb-4">
                                                            <LatexRenderer text={question.content} />

                                                            {question.imageUrl && (
                                                                <div className="mt-3 flex justify-center">
                                                                    <img
                                                                        src={question.imageUrl}
                                                                        alt="Hình minh họa"
                                                                        className="max-h-64 object-contain rounded-md border border-gray-200"
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>

                                                        {/* Statements table - Desktop view */}
                                                        <div className="hidden md:block overflow-x-auto rounded-lg border border-gray-200 mt-4">
                                                            <table className="min-w-full divide-y divide-gray-200">
                                                                <thead className="bg-gray-50">
                                                                    <tr>
                                                                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[60%]">
                                                                            Mệnh đề
                                                                        </th>
                                                                        <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                            Trả lời của bạn
                                                                        </th>
                                                                        <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                            Đáp án đúng
                                                                        </th>
                                                                        <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                            Kết quả
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody className="bg-white divide-y divide-gray-200">
                                                                    {question.statements.map((statement, index) => {
                                                                        const userAnswer = answersDS[statement.id];
                                                                        const isStatementCorrect = userAnswer === statement.isCorrect;

                                                                        return (
                                                                            <tr key={statement.id} className={isStatementCorrect ? 'bg-green-50' : 'bg-red-50'}>
                                                                                <td className="px-4 py-3 text-sm text-gray-900">
                                                                                    <div className="flex items-start gap-2">
                                                                                        <span className="font-medium text-gray-700 mt-0.5">
                                                                                            {prefixStatementDS[index]}
                                                                                        </span>
                                                                                        <div>
                                                                                            <LatexRenderer text={statement.content} />

                                                                                            {statement.imageUrl && (
                                                                                                <div className="mt-2">
                                                                                                    <img
                                                                                                        src={statement.imageUrl}
                                                                                                        alt="Hình minh họa mệnh đề"
                                                                                                        className="max-h-32 object-contain rounded-md border border-gray-200"
                                                                                                    />
                                                                                                </div>
                                                                                            )}
                                                                                        </div>
                                                                                    </div>
                                                                                </td>
                                                                                <td className="px-4 py-3 text-sm text-center">
                                                                                    {userAnswer === true ? (
                                                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                                            Đúng
                                                                                        </span>
                                                                                    ) : userAnswer === false ? (
                                                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                                                            Sai
                                                                                        </span>
                                                                                    ) : (
                                                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                                                                            Không trả lời
                                                                                        </span>
                                                                                    )}
                                                                                </td>
                                                                                <td className="px-4 py-3 text-sm text-center">
                                                                                    {statement.isCorrect ? (
                                                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                                            Đúng
                                                                                        </span>
                                                                                    ) : (
                                                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                                                            Sai
                                                                                        </span>
                                                                                    )}
                                                                                </td>
                                                                                <td className="px-4 py-3 text-sm text-center">
                                                                                    {isStatementCorrect ? (
                                                                                        <CheckCircle size={18} className="text-green-500 mx-auto" />
                                                                                    ) : (
                                                                                        <XCircle size={18} className="text-red-500 mx-auto" />
                                                                                    )}
                                                                                </td>
                                                                            </tr>
                                                                        );
                                                                    })}
                                                                </tbody>
                                                            </table>
                                                        </div>

                                                        {/* Statements cards - Mobile view */}
                                                        <div className="md:hidden space-y-4 mt-4">
                                                            {question.statements.map((statement, index) => {
                                                                const userAnswer = answersDS[statement.id];
                                                                const isStatementCorrect = userAnswer === statement.isCorrect;

                                                                return (
                                                                    <div
                                                                        key={statement.id}
                                                                        className={`rounded-lg border p-3 ${isStatementCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                                                                            }`}
                                                                    >
                                                                        {/* Statement content */}
                                                                        <div className="flex items-start gap-2 mb-3 pb-3 border-b border-gray-200">
                                                                            <span className="font-medium text-gray-700 mt-0.5">
                                                                                {prefixStatementDS[index]}
                                                                            </span>
                                                                            <div className="flex-1">
                                                                                <LatexRenderer text={statement.content} />

                                                                                {statement.imageUrl && (
                                                                                    <div className="mt-2">
                                                                                        <img
                                                                                            src={statement.imageUrl}
                                                                                            alt="Hình minh họa mệnh đề"
                                                                                            className="max-h-32 object-contain rounded-md border border-gray-200"
                                                                                        />
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        </div>

                                                                        {/* Answer details */}
                                                                        <div className="grid grid-cols-3 gap-2 text-xs">
                                                                            <div>
                                                                                <p className="text-gray-500 mb-1 font-medium">Trả lời của bạn:</p>
                                                                                <div className="flex justify-center">
                                                                                    {userAnswer === true ? (
                                                                                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                                            Đúng
                                                                                        </span>
                                                                                    ) : userAnswer === false ? (
                                                                                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                                                            Sai
                                                                                        </span>
                                                                                    ) : (
                                                                                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                                                                            Không trả lời
                                                                                        </span>
                                                                                    )}
                                                                                </div>
                                                                            </div>

                                                                            <div>
                                                                                <p className="text-gray-500 mb-1 font-medium">Đáp án đúng:</p>
                                                                                <div className="flex justify-center">
                                                                                    {statement.isCorrect ? (
                                                                                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                                            Đúng
                                                                                        </span>
                                                                                    ) : (
                                                                                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                                                            Sai
                                                                                        </span>
                                                                                    )}
                                                                                </div>
                                                                            </div>

                                                                            <div>
                                                                                <p className="text-gray-500 mb-1 font-medium">Kết quả:</p>
                                                                                <div className="flex justify-center">
                                                                                    {isStatementCorrect ? (
                                                                                        <CheckCircle size={18} className="text-green-500" />
                                                                                    ) : (
                                                                                        <XCircle size={18} className="text-red-500" />
                                                                                    )}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    </div>

                                                    {/* Solution toggle button */}
                                                    <div className="px-4 pb-4 flex justify-start">
                                                        <button
                                                            onClick={() => toggleSolution(question.id)}
                                                            className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
                                                        >
                                                            {shownSolutions[question.id] ? (
                                                                <>
                                                                    <ChevronUp size={16} />
                                                                    <span>Ẩn lời giải</span>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <ChevronDown size={16} />
                                                                    <span>Hiển thị lời giải</span>
                                                                </>
                                                            )}
                                                        </button>
                                                    </div>

                                                    {/* Solution content */}
                                                    {shownSolutions[question.id] && (
                                                        <div className="px-4 pb-4">
                                                            <SolutionContent question={question} />

                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                )}

                                {/* Phần III - Trả lời ngắn */}
                                {isPart3 && (
                                    <div className="space-y-6">
                                        {questionTLN.map((question, idx) => {
                                            const userAnswer = answersTNTLN[question.id]?.answer || "";
                                            const isCorrect = answersTNTLN[question.id]?.result;
                                            const correctAnswer = question.correctAnswer || "";

                                            return (
                                                <div
                                                    key={question.id + "TLN"}
                                                    className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                                                >
                                                    {/* Question header */}
                                                    <div className={`p-4 flex items-center justify-between border-b ${isCorrect === true
                                                        ? 'bg-green-50 border-green-100'
                                                        : isCorrect === false
                                                            ? 'bg-red-50 border-red-100'
                                                            : 'bg-amber-50 border-amber-100'
                                                        }`}>
                                                        <div className="flex items-center gap-2">
                                                            <div className={`p-1.5 rounded-full ${isCorrect === true
                                                                ? 'bg-green-100'
                                                                : isCorrect === false
                                                                    ? 'bg-red-100'
                                                                    : 'bg-amber-100'
                                                                }`}>
                                                                {renderStatusIcon(isCorrect)}
                                                            </div>
                                                            <h3 className="font-semibold text-gray-800">
                                                                Câu {idx + 1}
                                                            </h3>
                                                        </div>
                                                        <ReportButton questionId={question.id} />
                                                    </div>

                                                    {/* Question content */}
                                                    <div className="p-4">
                                                        <div className="mb-4">
                                                            <LatexRenderer text={question.content} />

                                                            {question.imageUrl && (
                                                                <div className="mt-3 flex justify-center">
                                                                    <img
                                                                        src={question.imageUrl}
                                                                        alt="Hình minh họa"
                                                                        className="max-h-64 object-contain rounded-md border border-gray-200"
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>

                                                        {/* User answer */}
                                                        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-md">
                                                            <h4 className="font-medium text-gray-700 mb-2">Câu trả lời của bạn:</h4>
                                                            <div className="text-gray-800 bg-white p-3 rounded border border-gray-200 min-h-[40px]">
                                                                {userAnswer ? (
                                                                    <LatexRenderer text={userAnswer} />
                                                                ) : (
                                                                    <span className="text-gray-400 italic">Không có câu trả lời</span>
                                                                )}
                                                            </div>
                                                        </div>

                                                        {/* Correct answer */}
                                                        {correctAnswer && (
                                                            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                                                                <h4 className="font-medium text-green-700 mb-2">Đáp án đúng:</h4>
                                                                <div className="text-green-800 bg-white p-3 rounded border border-green-200">
                                                                    <LatexRenderer text={correctAnswer} />
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>

                                                    {/* Solution toggle button */}
                                                    <div className="px-4 pb-4 flex justify-start">
                                                        <button
                                                            onClick={() => toggleSolution(question.id)}
                                                            className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
                                                        >
                                                            {shownSolutions[question.id] ? (
                                                                <>
                                                                    <ChevronUp size={16} />
                                                                    <span>Ẩn lời giải</span>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <ChevronDown size={16} />
                                                                    <span>Hiển thị lời giải</span>
                                                                </>
                                                            )}
                                                        </button>
                                                    </div>

                                                    {/* Solution content */}
                                                    {shownSolutions[question.id] && (
                                                        <div className="px-4 pb-4">
                                                            <SolutionContent question={question} />

                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </UserLayout>
    );
};

export default ScorePage;