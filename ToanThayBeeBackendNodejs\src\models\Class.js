'use strict'
import { Model } from 'sequelize'

export default (sequelize, DataTypes) => {
  class Class extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      Class.hasMany(models.StudentClassStatus, {
        foreignKey: 'classId',
        as: 'classStatuses',
      })
      Class.belongsTo(models.Slide, {
        foreignKey: 'slideId',
        as: 'slide'
      });
      Class.hasMany(models.Lesson, {
        foreignKey: 'classId',
        as: 'lessons',
      })

    }
  }
  Class.init({
    name: DataTypes.STRING,
    grade: DataTypes.STRING(2),
    description: DataTypes.TEXT,
    academicYear: DataTypes.STRING,
    status: DataTypes.STRING,
    slideId: DataTypes.INTEGER,
    dayOfWeek1: DataTypes.STRING,
    dayOfWeek2: DataTypes.STRING,
    startTime1: DataTypes.TIME,
    endTime1: DataTypes.TIME,
    startTime2: DataTypes.TIME,
    endTime2: DataTypes.TIME,
    public: DataTypes.BOOLEAN,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
    class_code: DataTypes.STRING(20)
  }, {
    sequelize,
    modelName: 'Class',
    tableName: 'class',
    indexes: [
      {
        name: 'idx_class_code',
        fields: ['class_code'],
        unique: true
      },
      {
        name: 'idx_class_grade',
        fields: ['grade']
      },
      {
        name: 'idx_class_academicYear',
        fields: ['academicYear']
      },
      {
        name: 'idx_class_status',
        fields: ['status']
      },
      {
        name: 'idx_class_grade_academicYear',
        fields: ['grade', 'academicYear']
      }
    ]
  })
  return Class
}