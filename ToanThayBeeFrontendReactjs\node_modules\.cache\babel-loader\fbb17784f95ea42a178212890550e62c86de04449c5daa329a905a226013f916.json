{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\AiExamDetailAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, use } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport { fetchQuestionsByExamId, setSelectedQuestion, reorderQuestions } from \"src/features/examAI/examAISlice\";\nimport Header from \"src/components/PageAIexam/Header\";\nimport LeftContent from \"src/components/PageAIexam/LeftContent\";\nimport RightContent from \"src/components/PageAIexam/RightContent\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\nimport { fetchImages } from \"src/features/image/imageSlice\";\nimport { DndContext, closestCenter, PointerSensor, KeyboardSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { sortableKeyboardCoordinates } from '@dnd-kit/sortable';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditExamAIPage = () => {\n  _s();\n  const {\n    examId\n  } = useParams();\n  const {\n    exam,\n    questions,\n    questionsEdited,\n    isAddImage\n  } = useSelector(state => state.examAI);\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n  const dispatch = useDispatch();\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const handleDragEnd = event => {\n    var _active$data$current;\n    const {\n      active,\n      over\n    } = event;\n    if (!over) return;\n\n    // Xử lý drag-and-drop cho questions (khi isAddImage = false)\n    if (!isAddImage && active.id !== over.id) {\n      const oldIndex = questionsEdited.findIndex(q => q.id === active.id);\n      const newIndex = questionsEdited.findIndex(q => q.id === over.id);\n      if (oldIndex !== -1 && newIndex !== -1) {\n        dispatch(reorderQuestions({\n          oldIndex,\n          newIndex\n        }));\n      }\n    }\n\n    // Xử lý drag-and-drop cho images (khi isAddImage = true)\n    if (isAddImage && ((_active$data$current = active.data.current) === null || _active$data$current === void 0 ? void 0 : _active$data$current.type) === 'image') {\n      const imageUrl = active.data.current.imageUrl;\n      const dropZoneId = over.id;\n\n      // Tìm question tương ứng với drop zone\n      if (dropZoneId.startsWith('question-content-')) {\n        const questionId = dropZoneId.replace('question-content-', '');\n        const questionIndex = questionsEdited.findIndex(q => q.id === questionId);\n        if (questionIndex !== -1) {\n          const updatedQuestion = {\n            ...questionsEdited[questionIndex],\n            imageUrl\n          };\n          dispatch(setSelectedQuestion(updatedQuestion));\n        }\n      } else if (dropZoneId.startsWith('question-solution-')) {\n        const questionId = dropZoneId.replace('question-solution-', '');\n        const questionIndex = questionsEdited.findIndex(q => q.id === questionId);\n        if (questionIndex !== -1) {\n          const updatedQuestion = {\n            ...questionsEdited[questionIndex],\n            solutionImageUrl: imageUrl\n          };\n          dispatch(setSelectedQuestion(updatedQuestion));\n        }\n      } else if (dropZoneId.startsWith('statement-')) {\n        const parts = dropZoneId.split('-');\n        const questionId = parts[1];\n        const statementIndex = parseInt(parts[2]);\n        const questionIndex = questionsEdited.findIndex(q => q.id === questionId);\n        if (questionIndex !== -1) {\n          const updatedQuestion = {\n            ...questionsEdited[questionIndex]\n          };\n          if (updatedQuestion.statement1s && updatedQuestion.statement1s[statementIndex]) {\n            updatedQuestion.statement1s[statementIndex].imageUrl = imageUrl;\n            dispatch(setSelectedQuestion(updatedQuestion));\n          }\n        }\n      }\n    }\n  };\n  useEffect(() => {\n    var _questions$;\n    if (!examId && questions.length > 0) return;\n    setSelectedQuestion(questions[0]);\n    setEditedText(((_questions$ = questions[0]) === null || _questions$ === void 0 ? void 0 : _questions$.content) || \"\");\n  }, [questions, examId]);\n  useEffect(() => {\n    if (examId) {\n      dispatch(fetchImages(\"ImageFormN8N\"));\n      dispatch(fetchQuestionsByExamId(examId));\n      dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\n    }\n  }, [examId, dispatch]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" bg-gray-50 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \" bg-gray-50 flex flex-col \".concat(closeSidebar ? \"ml-[104px]\" : \"ml-64\"),\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        title: exam === null || exam === void 0 ? void 0 : exam.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-[calc(100vh_-_64px)] bg-gray-50 flex mt-16 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(LeftContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(RightContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 9\n  }, this);\n};\n_s(EditExamAIPage, \"NRqLFZR6Atf+q8P68WdcOYEWxXY=\", false, function () {\n  return [useParams, useSelector, useSelector, useDispatch, useSensors, useSensor, useSensor];\n});\n_c = EditExamAIPage;\nexport default EditExamAIPage;\nvar _c;\n$RefreshReg$(_c, \"EditExamAIPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "use", "useSelector", "useDispatch", "useParams", "fetchQuestionsByExamId", "setSelectedQuestion", "reorderQuestions", "Header", "LeftContent", "RightContent", "fetchCodesByType", "AdminSidebar", "fetchImages", "DndContext", "closestCenter", "PointerSensor", "KeyboardSensor", "useSensor", "useSensors", "sortableKeyboardCoordinates", "jsxDEV", "_jsxDEV", "EditExamAIPage", "_s", "examId", "exam", "questions", "questionsEdited", "isAddImage", "state", "examAI", "closeSidebar", "sidebar", "dispatch", "sensors", "activationConstraint", "distance", "coordinateGetter", "handleDragEnd", "event", "_active$data$current", "active", "over", "id", "oldIndex", "findIndex", "q", "newIndex", "data", "current", "type", "imageUrl", "dropZoneId", "startsWith", "questionId", "replace", "questionIndex", "updatedQuestion", "solutionImageUrl", "parts", "split", "statementIndex", "parseInt", "statement1s", "_questions$", "length", "setEditedText", "content", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "concat", "title", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/AiExamDetailAdmin.jsx"], "sourcesContent": ["import React, { useState, useEffect, use } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { fetchQuestionsByExamId, setSelectedQuestion, reorderQuestions } from \"src/features/examAI/examAISlice\";\r\nimport Header from \"src/components/PageAIexam/Header\";\r\nimport LeftContent from \"src/components/PageAIexam/LeftContent\";\r\nimport RightContent from \"src/components/PageAIexam/RightContent\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\r\nimport { fetchImages } from \"src/features/image/imageSlice\";\r\nimport { DndContext, closestCenter, PointerSensor, KeyboardSensor, useSensor, useSensors } from '@dnd-kit/core';\r\nimport { sortableKeyboardCoordinates } from '@dnd-kit/sortable';\r\n\r\nconst EditExamAIPage = () => {\r\n    const { examId } = useParams();\r\n    const { exam, questions, questionsEdited, isAddImage } = useSelector((state) => state.examAI);\r\n    const { closeSidebar } = useSelector((state) => state.sidebar);\r\n    const dispatch = useDispatch();\r\n\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor, {\r\n            activationConstraint: {\r\n                distance: 8,\r\n            },\r\n        }),\r\n        useSensor(KeyboardSensor, {\r\n            coordinateGetter: sortableKeyboardCoordinates,\r\n        })\r\n    );\r\n\r\n    const handleDragEnd = (event) => {\r\n        const { active, over } = event;\r\n\r\n        if (!over) return;\r\n\r\n        // Xử lý drag-and-drop cho questions (khi isAddImage = false)\r\n        if (!isAddImage && active.id !== over.id) {\r\n            const oldIndex = questionsEdited.findIndex(q => q.id === active.id);\r\n            const newIndex = questionsEdited.findIndex(q => q.id === over.id);\r\n\r\n            if (oldIndex !== -1 && newIndex !== -1) {\r\n                dispatch(reorderQuestions({\r\n                    oldIndex,\r\n                    newIndex\r\n                }));\r\n            }\r\n        }\r\n\r\n        // Xử lý drag-and-drop cho images (khi isAddImage = true)\r\n        if (isAddImage && active.data.current?.type === 'image') {\r\n            const imageUrl = active.data.current.imageUrl;\r\n            const dropZoneId = over.id;\r\n\r\n            // Tìm question tương ứng với drop zone\r\n            if (dropZoneId.startsWith('question-content-')) {\r\n                const questionId = dropZoneId.replace('question-content-', '');\r\n                const questionIndex = questionsEdited.findIndex(q => q.id === questionId);\r\n                if (questionIndex !== -1) {\r\n                    const updatedQuestion = { ...questionsEdited[questionIndex], imageUrl };\r\n                    dispatch(setSelectedQuestion(updatedQuestion));\r\n                }\r\n            } else if (dropZoneId.startsWith('question-solution-')) {\r\n                const questionId = dropZoneId.replace('question-solution-', '');\r\n                const questionIndex = questionsEdited.findIndex(q => q.id === questionId);\r\n                if (questionIndex !== -1) {\r\n                    const updatedQuestion = { ...questionsEdited[questionIndex], solutionImageUrl: imageUrl };\r\n                    dispatch(setSelectedQuestion(updatedQuestion));\r\n                }\r\n            } else if (dropZoneId.startsWith('statement-')) {\r\n                const parts = dropZoneId.split('-');\r\n                const questionId = parts[1];\r\n                const statementIndex = parseInt(parts[2]);\r\n                const questionIndex = questionsEdited.findIndex(q => q.id === questionId);\r\n\r\n                if (questionIndex !== -1) {\r\n                    const updatedQuestion = { ...questionsEdited[questionIndex] };\r\n                    if (updatedQuestion.statement1s && updatedQuestion.statement1s[statementIndex]) {\r\n                        updatedQuestion.statement1s[statementIndex].imageUrl = imageUrl;\r\n                        dispatch(setSelectedQuestion(updatedQuestion));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (!examId && questions.length > 0) return\r\n        setSelectedQuestion(questions[0]);\r\n        setEditedText(questions[0]?.content || \"\");\r\n    }, [questions, examId]);\r\n\r\n    useEffect(() => {\r\n        if (examId) {\r\n            dispatch(fetchImages(\"ImageFormN8N\"));\r\n            dispatch(fetchQuestionsByExamId(examId))\r\n            dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\r\n        }\r\n    }, [examId, dispatch]);\r\n\r\n    return (\r\n        <div className=\" bg-gray-50 flex flex-col\">\r\n            <AdminSidebar />\r\n\r\n            <div className={` bg-gray-50 flex flex-col ${closeSidebar ? \"ml-[104px]\" : \"ml-64\"}`}>\r\n                <Header title={exam?.name} />\r\n                <div className=\"h-[calc(100vh_-_64px)] bg-gray-50 flex mt-16 overflow-hidden\">\r\n                    {/* LEFT: Danh sách câu hỏi */}\r\n                    <LeftContent />\r\n\r\n                    {/* RIGHT: Form chỉnh sửa */}\r\n                    <RightContent />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditExamAIPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,OAAO;AACvD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,sBAAsB,EAAEC,mBAAmB,EAAEC,gBAAgB,QAAQ,iCAAiC;AAC/G,OAAOC,MAAM,MAAM,kCAAkC;AACrD,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,cAAc,EAAEC,SAAS,EAAEC,UAAU,QAAQ,eAAe;AAC/G,SAASC,2BAA2B,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAO,CAAC,GAAGrB,SAAS,CAAC,CAAC;EAC9B,MAAM;IAAEsB,IAAI;IAAEC,SAAS;IAAEC,eAAe;IAAEC;EAAW,CAAC,GAAG3B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAC7F,MAAM;IAAEC;EAAa,CAAC,GAAG9B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACG,OAAO,CAAC;EAC9D,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAE9B,MAAMgC,OAAO,GAAGhB,UAAU,CACtBD,SAAS,CAACF,aAAa,EAAE;IACrBoB,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACFnB,SAAS,CAACD,cAAc,EAAE;IACtBqB,gBAAgB,EAAElB;EACtB,CAAC,CACL,CAAC;EAED,MAAMmB,aAAa,GAAIC,KAAK,IAAK;IAAA,IAAAC,oBAAA;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGH,KAAK;IAE9B,IAAI,CAACG,IAAI,EAAE;;IAEX;IACA,IAAI,CAACd,UAAU,IAAIa,MAAM,CAACE,EAAE,KAAKD,IAAI,CAACC,EAAE,EAAE;MACtC,MAAMC,QAAQ,GAAGjB,eAAe,CAACkB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACnE,MAAMI,QAAQ,GAAGpB,eAAe,CAACkB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;MAEjE,IAAIC,QAAQ,KAAK,CAAC,CAAC,IAAIG,QAAQ,KAAK,CAAC,CAAC,EAAE;QACpCd,QAAQ,CAAC3B,gBAAgB,CAAC;UACtBsC,QAAQ;UACRG;QACJ,CAAC,CAAC,CAAC;MACP;IACJ;;IAEA;IACA,IAAInB,UAAU,IAAI,EAAAY,oBAAA,GAAAC,MAAM,CAACO,IAAI,CAACC,OAAO,cAAAT,oBAAA,uBAAnBA,oBAAA,CAAqBU,IAAI,MAAK,OAAO,EAAE;MACrD,MAAMC,QAAQ,GAAGV,MAAM,CAACO,IAAI,CAACC,OAAO,CAACE,QAAQ;MAC7C,MAAMC,UAAU,GAAGV,IAAI,CAACC,EAAE;;MAE1B;MACA,IAAIS,UAAU,CAACC,UAAU,CAAC,mBAAmB,CAAC,EAAE;QAC5C,MAAMC,UAAU,GAAGF,UAAU,CAACG,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;QAC9D,MAAMC,aAAa,GAAG7B,eAAe,CAACkB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKW,UAAU,CAAC;QACzE,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;UACtB,MAAMC,eAAe,GAAG;YAAE,GAAG9B,eAAe,CAAC6B,aAAa,CAAC;YAAEL;UAAS,CAAC;UACvElB,QAAQ,CAAC5B,mBAAmB,CAACoD,eAAe,CAAC,CAAC;QAClD;MACJ,CAAC,MAAM,IAAIL,UAAU,CAACC,UAAU,CAAC,oBAAoB,CAAC,EAAE;QACpD,MAAMC,UAAU,GAAGF,UAAU,CAACG,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;QAC/D,MAAMC,aAAa,GAAG7B,eAAe,CAACkB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKW,UAAU,CAAC;QACzE,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;UACtB,MAAMC,eAAe,GAAG;YAAE,GAAG9B,eAAe,CAAC6B,aAAa,CAAC;YAAEE,gBAAgB,EAAEP;UAAS,CAAC;UACzFlB,QAAQ,CAAC5B,mBAAmB,CAACoD,eAAe,CAAC,CAAC;QAClD;MACJ,CAAC,MAAM,IAAIL,UAAU,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;QAC5C,MAAMM,KAAK,GAAGP,UAAU,CAACQ,KAAK,CAAC,GAAG,CAAC;QACnC,MAAMN,UAAU,GAAGK,KAAK,CAAC,CAAC,CAAC;QAC3B,MAAME,cAAc,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;QACzC,MAAMH,aAAa,GAAG7B,eAAe,CAACkB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKW,UAAU,CAAC;QAEzE,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;UACtB,MAAMC,eAAe,GAAG;YAAE,GAAG9B,eAAe,CAAC6B,aAAa;UAAE,CAAC;UAC7D,IAAIC,eAAe,CAACM,WAAW,IAAIN,eAAe,CAACM,WAAW,CAACF,cAAc,CAAC,EAAE;YAC5EJ,eAAe,CAACM,WAAW,CAACF,cAAc,CAAC,CAACV,QAAQ,GAAGA,QAAQ;YAC/DlB,QAAQ,CAAC5B,mBAAmB,CAACoD,eAAe,CAAC,CAAC;UAClD;QACJ;MACJ;IACJ;EACJ,CAAC;EAED1D,SAAS,CAAC,MAAM;IAAA,IAAAiE,WAAA;IACZ,IAAI,CAACxC,MAAM,IAAIE,SAAS,CAACuC,MAAM,GAAG,CAAC,EAAE;IACrC5D,mBAAmB,CAACqB,SAAS,CAAC,CAAC,CAAC,CAAC;IACjCwC,aAAa,CAAC,EAAAF,WAAA,GAAAtC,SAAS,CAAC,CAAC,CAAC,cAAAsC,WAAA,uBAAZA,WAAA,CAAcG,OAAO,KAAI,EAAE,CAAC;EAC9C,CAAC,EAAE,CAACzC,SAAS,EAAEF,MAAM,CAAC,CAAC;EAEvBzB,SAAS,CAAC,MAAM;IACZ,IAAIyB,MAAM,EAAE;MACRS,QAAQ,CAACrB,WAAW,CAAC,cAAc,CAAC,CAAC;MACrCqB,QAAQ,CAAC7B,sBAAsB,CAACoB,MAAM,CAAC,CAAC;MACxCS,QAAQ,CAACvB,gBAAgB,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;IACvF;EACJ,CAAC,EAAE,CAACc,MAAM,EAAES,QAAQ,CAAC,CAAC;EAEtB,oBACIZ,OAAA;IAAK+C,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACtChD,OAAA,CAACV,YAAY;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhBpD,OAAA;MAAK+C,SAAS,+BAAAM,MAAA,CAA+B3C,YAAY,GAAG,YAAY,GAAG,OAAO,CAAG;MAAAsC,QAAA,gBACjFhD,OAAA,CAACd,MAAM;QAACoE,KAAK,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD;MAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7BpD,OAAA;QAAK+C,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAEzEhD,OAAA,CAACb,WAAW;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGfpD,OAAA,CAACZ,YAAY;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAClD,EAAA,CAtGID,cAAc;EAAA,QACGnB,SAAS,EAC6BF,WAAW,EAC3CA,WAAW,EACnBC,WAAW,EAEZgB,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAA4D,EAAA,GAZXvD,cAAc;AAwGpB,eAAeA,cAAc;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}