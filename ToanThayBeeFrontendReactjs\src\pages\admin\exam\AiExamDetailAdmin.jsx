import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchQuestionsByExamId, setModalOpen, setConfirm, commitExam } from "src/features/examAI/examAISlice";
import Header from "src/components/PageAIexam/Header";
import LeftContent from "src/components/PageAIexam/LeftContent";
import RightContent from "src/components/PageAIexam/RightContent";
import { fetchCodesByType } from "src/features/code/codeSlice";
import AdminSidebar from "src/components/sidebar/AdminSidebar";
import { fetchImages } from "src/features/image/imageSlice";
import AddImagesModal from "src/components/modal/AddImagesModal";
import ConfirmModal from "src/components/modal/ConfirmModal";

const EditExamAIPage = () => {
    const { examId } = useParams();
    const { exam, modalOpen, isChange, confirm, loadingCommit, showAddImagesModal } = useSelector((state) => state.examAI);
    const { closeSidebar } = useSelector((state) => state.sidebar);
    const dispatch = useDispatch();

    useEffect(() => {
        if (examId) {
            dispatch(fetchImages("ImageFormN8N"));
            dispatch(fetchQuestionsByExamId(examId))
            dispatch(fetchCodesByType(["chapter", "difficulty", "grade", "exam type", "year"]));
        }
    }, [examId, dispatch]);

    useEffect(() => {
        if (confirm) {
            dispatch(commitExam(exam.id));
            setConfirm(false);
        }
    }, [confirm]);

    return (
        <div className=" bg-gray-50 flex flex-col">
            <AdminSidebar />
            <div className={` bg-gray-50 flex flex-col ${closeSidebar ? "ml-[104px]" : "ml-64"}`}>
                <Header title={exam?.name} />
                <div className="h-[calc(100vh_-_64px)] bg-gray-50 flex mt-16 overflow-hidden">
                    {/* LEFT: Danh sách câu hỏi */}
                    <LeftContent />

                    {/* RIGHT: Form chỉnh sửa */}
                    <RightContent />
                </div>
            </div>
            {showAddImagesModal && <AddImagesModal showAddImagesModal={showAddImagesModal} folder="ImageFormN8N" />}
            <ConfirmModal
                isOpen={modalOpen}
                onClose={() => dispatch(setModalOpen(false))}
                onConfirm={() => dispatch(setConfirm(true))}
                title="Xác nhận cập nhật"
                message={`${isChange ? "Bạn có sự thay đổi, s" : "S"}au khi cập nhật đề thi thật bản nháp này sẽ mất bạn chắc chắn muốn cập nhật không?`}
                loading={loadingCommit}
            />
        </div>
    );
};

export default EditExamAIPage;
