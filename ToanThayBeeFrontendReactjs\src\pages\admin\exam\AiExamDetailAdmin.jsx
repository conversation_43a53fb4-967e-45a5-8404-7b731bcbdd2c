import React, { useState, useEffect, use } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchQuestionsByExamId } from "src/features/examAI/examAISlice";
import Header from "src/components/PageAIexam/Header";
import LeftContent from "src/components/PageAIexam/LeftContent";
import RightContent from "src/components/PageAIexam/RightContent";
import { fetchCodesByType } from "src/features/code/codeSlice";

const EditExamAIPage = () => {
    const { examId } = useParams();
    const [selectedQuestion, setSelectedQuestion] = useState(null);
    const [editedText, setEditedText] = useState("");
    const { exam, questions } = useSelector((state) => state.examAI);

    const dispatch = useDispatch();

    useEffect(() => {
        if (!examId && questions.length > 0) return
        setSelectedQuestion(questions[0]);
        setEditedText(questions[0]?.content || "");
    }, [questions, examId]);

    useEffect(() => {
        if (examId) {
            dispatch(fetchQuestionsByExamId(examId))
            dispatch(fetchCodesByType(["chapter", "difficulty", "grade"]));
        }
    }, [examId, dispatch]);

    return (
        <div className="w-full bg-gray-50 flex flex-col">
            <Header title={exam?.name} />
            <div className="w-full h-full bg-gray-50 flex mt-16">
                {/* LEFT: Danh sách câu hỏi */}
                <LeftContent />

                {/* RIGHT: Form chỉnh sửa */}
                <RightContent />
            </div>
        </div>
    );
};

export default EditExamAIPage;
