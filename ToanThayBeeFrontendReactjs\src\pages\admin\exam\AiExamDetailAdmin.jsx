import React, { useState, useEffect, use } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchQuestionsByExamId, setSelectedQuestion, reorderQuestions } from "src/features/examAI/examAISlice";
import Header from "src/components/PageAIexam/Header";
import LeftContent from "src/components/PageAIexam/LeftContent";
import RightContent from "src/components/PageAIexam/RightContent";
import { fetchCodesByType } from "src/features/code/codeSlice";
import AdminSidebar from "src/components/sidebar/AdminSidebar";
import { fetchImages } from "src/features/image/imageSlice";
import { DndContext, closestCenter, PointerSensor, KeyboardSensor, useSensor, useSensors } from '@dnd-kit/core';
import { sortableKeyboardCoordinates } from '@dnd-kit/sortable';

const EditExamAIPage = () => {
    const { examId } = useParams();
    const { exam, questionsEdited, isAddImage } = useSelector((state) => state.examAI);
    const { closeSidebar } = useSelector((state) => state.sidebar);
    const dispatch = useDispatch();

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event) => {
        const { active, over } = event;

        if (!over) return;

        // Xử lý drag-and-drop cho questions (khi isAddImage = false)
        if (!isAddImage && active.id !== over.id) {
            const oldIndex = questionsEdited.findIndex(q => q.id === active.id);
            const newIndex = questionsEdited.findIndex(q => q.id === over.id);

            if (oldIndex !== -1 && newIndex !== -1) {
                dispatch(reorderQuestions({
                    oldIndex,
                    newIndex
                }));
            }
        }

        // Xử lý drag-and-drop cho images (khi isAddImage = true)
        if (isAddImage && active.data.current?.type === 'image') {
            const imageUrl = active.data.current.imageUrl;
            const dropZoneId = over.id;

            // Tìm question tương ứng với drop zone
            if (dropZoneId.startsWith('question-content-')) {
                const questionId = dropZoneId.replace('question-content-', '');
                const questionIndex = questionsEdited.findIndex(q => q.id === questionId);
                if (questionIndex !== -1) {
                    const updatedQuestion = { ...questionsEdited[questionIndex], imageUrl };
                    dispatch(setSelectedQuestion(updatedQuestion));
                }
            } else if (dropZoneId.startsWith('question-solution-')) {
                const questionId = dropZoneId.replace('question-solution-', '');
                const questionIndex = questionsEdited.findIndex(q => q.id === questionId);
                if (questionIndex !== -1) {
                    const updatedQuestion = { ...questionsEdited[questionIndex], solutionImageUrl: imageUrl };
                    dispatch(setSelectedQuestion(updatedQuestion));
                }
            } else if (dropZoneId.startsWith('statement-')) {
                const parts = dropZoneId.split('-');
                const questionId = parts[1];
                const statementIndex = parseInt(parts[2]);
                const questionIndex = questionsEdited.findIndex(q => q.id === questionId);

                if (questionIndex !== -1) {
                    const updatedQuestion = { ...questionsEdited[questionIndex] };
                    if (updatedQuestion.statement1s && updatedQuestion.statement1s[statementIndex]) {
                        updatedQuestion.statement1s[statementIndex].imageUrl = imageUrl;
                        dispatch(setSelectedQuestion(updatedQuestion));
                    }
                }
            }
        }
    };

    // Removed unused useEffect

    useEffect(() => {
        if (examId) {
            dispatch(fetchImages("ImageFormN8N"));
            dispatch(fetchQuestionsByExamId(examId))
            dispatch(fetchCodesByType(["chapter", "difficulty", "grade", "exam type", "year"]));
        }
    }, [examId, dispatch]);

    return (
        <div className=" bg-gray-50 flex flex-col">
            <AdminSidebar />

            <div className={` bg-gray-50 flex flex-col ${closeSidebar ? "ml-[104px]" : "ml-64"}`}>
                <Header title={exam?.name} />
                <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                >
                    <div className="h-[calc(100vh_-_64px)] bg-gray-50 flex mt-16 overflow-hidden">
                        {/* LEFT: Danh sách câu hỏi */}
                        <LeftContent />

                        {/* RIGHT: Form chỉnh sửa */}
                        <RightContent />
                    </div>
                </DndContext>
            </div>
        </div>
    );
};

export default EditExamAIPage;
