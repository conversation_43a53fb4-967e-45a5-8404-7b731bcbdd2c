import React, { useState, useEffect, use } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchQuestionsByExamId } from "src/features/examAI/examAISlice";
import Header from "src/components/PageAIexam/Header";
import LeftContent from "src/components/PageAIexam/LeftContent";
import RightContent from "src/components/PageAIexam/RightContent";
import { fetchCodesByType } from "src/features/code/codeSlice";
import AdminSidebar from "src/components/sidebar/AdminSidebar";
import { fetchImages } from "src/features/image/imageSlice";

const EditExamAIPage = () => {
    const { examId } = useParams();
    const [selectedQuestion, setSelectedQuestion] = useState(null);
    const [editedText, setEditedText] = useState("");
    const { exam, questions } = useSelector((state) => state.examAI);
    const { closeSidebar } = useSelector((state) => state.sidebar);
    const dispatch = useDispatch();

    useEffect(() => {
        if (!examId && questions.length > 0) return
        setSelectedQuestion(questions[0]);
        setEditedText(questions[0]?.content || "");
    }, [questions, examId]);

    useEffect(() => {
        if (examId) {
            dispatch(fetchImages("ImageFormN8N"));
            dispatch(fetchQuestionsByExamId(examId))
            dispatch(fetchCodesByType(["chapter", "difficulty", "grade", "exam type", "year"]));
        }
    }, [examId, dispatch]);

    return (
        <div className=" bg-gray-50 flex flex-col">
            <AdminSidebar />
            <div className={` bg-gray-50 flex flex-col ${closeSidebar ? "ml-[104px]" : "ml-64"}`}>
                <Header title={exam?.name} />
                <div className="h-[calc(100vh_-_64px)] bg-gray-50 flex mt-16 overflow-hidden">
                    {/* LEFT: Danh sách câu hỏi */}
                    <LeftContent />

                    {/* RIGHT: Form chỉnh sửa */}
                    <RightContent />
                </div>
            </div>
        </div>
    );
};

export default EditExamAIPage;
