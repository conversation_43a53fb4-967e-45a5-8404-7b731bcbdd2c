import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import Roles from '../constants/Roles.js'
import * as DoExamController from '../controllers/DoExamController.js'

const router = express.Router()

router.get('/v1/user/join-exam/:examId',
    requireRoles(Roles.JustStudent),
    async<PERSON>andler(DoExamController.joinExam)
)

router.post('/v1/user/submit-answer',
    requireRoles(Roles.JustStudent),
    asyncHandler(DoExamController.submitAnswerHandler)
)

router.post('/v1/user/calculate-score/:attemptId',
    requireRoles(Roles.JustStudent),
    asyncHandler(DoExamController.calculateScoreHandler)
)

// New API routes to replace socket functionality
router.get('/v1/user/exam-time/:examId/:attemptId',
    requireRoles(Roles.JustStudent),
    asyncHandler(DoExamController.getRemainingTimeHandler)
)

router.post('/v1/user/log-activity',
    requireRoles(Roles.JustStudent),
    asyncHandler(DoExamController.logUserActivityHandler)
)

router.post('/v1/user/submit-answer-attempt',
    requireRoles(Roles.JustStudent),
    asyncHandler(DoExamController.submitAnswerWithAttemptHandler)
)

router.post('/v1/user/leave-exam',
    requireRoles(Roles.JustStudent),
    asyncHandler(DoExamController.leaveExamHandler)
)


export default router