{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\AddImagesModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { X, Upload, Image as ImageIcon, Trash2 } from \"lucide-react\";\nimport { setShowAddImagesModal } from \"../../features/examAI/examAISlice\";\nimport { uploadMultipleImages } from \"../../features/image/imageSlice\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddImagesModal = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    showAddImagesModal\n  } = useSelector(state => state.examAI);\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [previewUrls, setPreviewUrls] = useState([]);\n  const fileInputRef = useRef(null);\n  const {\n    images,\n    loadingUploadImages\n  } = useSelector(state => state.images);\n  const handleClose = () => {\n    dispatch(setShowAddImagesModal(false));\n    setSelectedFiles([]);\n    setPreviewUrls([]);\n  };\n  const handleFileSelect = e => {\n    const files = Array.from(e.target.files);\n\n    // Limit to 10 files\n    const limitedFiles = files.slice(0, 10);\n    setSelectedFiles(limitedFiles);\n\n    // Create preview URLs\n    const urls = limitedFiles.map(file => URL.createObjectURL(file));\n    setPreviewUrls(urls);\n  };\n  const handleRemoveFile = index => {\n    const newFiles = selectedFiles.filter((_, i) => i !== index);\n    const newUrls = previewUrls.filter((_, i) => i !== index);\n\n    // Revoke the removed URL to prevent memory leaks\n    URL.revokeObjectURL(previewUrls[index]);\n    setSelectedFiles(newFiles);\n    setPreviewUrls(newUrls);\n  };\n  const handleUpload = async () => {\n    if (selectedFiles.length === 0) return;\n    try {\n      await dispatch(uploadMultipleImages({\n        files: selectedFiles,\n        folder: 'ImageFormN8N'\n      })).unwrap();\n      // Clear selected files after successful upload\n      setSelectedFiles([]);\n      setPreviewUrls([]);\n      // Reset file input\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    } catch (error) {\n      console.error('Upload failed:', error);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    const files = Array.from(e.dataTransfer.files);\n    const imageFiles = files.filter(file => file.type.startsWith('image/'));\n    const limitedFiles = imageFiles.slice(0, 10);\n    setSelectedFiles(limitedFiles);\n    const urls = limitedFiles.map(file => URL.createObjectURL(file));\n    setPreviewUrls(urls);\n  };\n  if (!showAddImagesModal) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800\",\n          children: \"Th\\xEAm \\u1EA3nh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClose,\n          className: \"p-2 hover:bg-gray-100 rounded-full transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 overflow-y-auto max-h-[calc(90vh-140px)]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors\",\n            onDragOver: handleDragOver,\n            onDragEnter: handleDragEnter,\n            onDragLeave: handleDragLeave,\n            onDrop: handleDrop,\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-medium text-gray-700 mb-2\",\n              children: \"K\\xE9o th\\u1EA3 \\u1EA3nh v\\xE0o \\u0111\\xE2y ho\\u1EB7c click \\u0111\\u1EC3 ch\\u1ECDn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 mb-4\",\n              children: \"T\\u1ED1i \\u0111a 10 \\u1EA3nh, \\u0111\\u1ECBnh d\\u1EA1ng JPG, PNG, GIF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ref: fileInputRef,\n              type: \"file\",\n              multiple: true,\n              accept: \"image/*\",\n              onChange: handleFileSelect,\n              className: \"hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _fileInputRef$current;\n                return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n              },\n              className: \"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors\",\n              children: \"Ch\\u1ECDn \\u1EA3nh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this), selectedFiles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-800 mb-3\",\n            children: [\"\\u1EA2nh \\u0111\\xE3 ch\\u1ECDn (\", selectedFiles.length, \"/10)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n            children: previewUrls.map((url, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: url,\n                alt: \"Preview \".concat(index + 1),\n                className: \"w-full h-32 object-cover rounded-lg border\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleRemoveFile(index),\n                className: \"absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\",\n                children: selectedFiles[index].name.length > 15 ? selectedFiles[index].name.substring(0, 15) + '...' : selectedFiles[index].name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 41\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-800 mb-3\",\n            children: [\"\\u1EA2nh \\u0111\\xE3 t\\u1EA3i l\\xEAn (\", images.ImageFormN8N.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 25\n          }, this), images.ImageFormN8N.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n            children: images.ImageFormN8N.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: image.ImageFormN8N || image.url || image,\n                alt: \"Uploaded \".concat(index + 1),\n                className: \"w-full h-32 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 41\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(ImageIcon, {\n              className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Ch\\u01B0a c\\xF3 \\u1EA3nh n\\xE0o \\u0111\\u01B0\\u1EE3c t\\u1EA3i l\\xEAn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-t bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: selectedFiles.length > 0 && \"\".concat(selectedFiles.length, \" \\u1EA3nh \\u0111\\xE3 ch\\u1ECDn\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n            children: \"\\u0110\\xF3ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 25\n          }, this), selectedFiles.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleUpload,\n            disabled: loadingUploadImages,\n            className: \"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-lg transition-colors flex items-center gap-2\",\n            children: loadingUploadImages ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 41\n              }, this), \"\\u0110ang t\\u1EA3i l\\xEAn...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Upload, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 41\n              }, this), \"T\\u1EA3i l\\xEAn\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 9\n  }, this);\n};\n_s(AddImagesModal, \"M5ris93dNtTSfDC2tkGRFtJd8wg=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = AddImagesModal;\nexport default AddImagesModal;\nvar _c;\n$RefreshReg$(_c, \"AddImagesModal\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useDispatch", "useSelector", "X", "Upload", "Image", "ImageIcon", "Trash2", "setShowAddImagesModal", "uploadMultipleImages", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddImagesModal", "_s", "dispatch", "showAddImagesModal", "state", "examAI", "selectedFiles", "setSelectedFiles", "previewUrls", "setPreviewUrls", "fileInputRef", "images", "loadingUploadImages", "handleClose", "handleFileSelect", "e", "files", "Array", "from", "target", "limitedFiles", "slice", "urls", "map", "file", "URL", "createObjectURL", "handleRemoveFile", "index", "newFiles", "filter", "_", "i", "newUrls", "revokeObjectURL", "handleUpload", "length", "folder", "unwrap", "current", "value", "error", "console", "handleDragOver", "preventDefault", "stopPropagation", "handleDragEnter", "handleDragLeave", "handleDrop", "dataTransfer", "imageFiles", "type", "startsWith", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "ref", "multiple", "accept", "onChange", "_fileInputRef$current", "click", "url", "src", "alt", "concat", "name", "substring", "ImageFormN8N", "image", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/AddImagesModal.jsx"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { X, Upload, Image as ImageIcon, Trash2 } from \"lucide-react\";\nimport { setShowAddImagesModal } from \"../../features/examAI/examAISlice\";\nimport { uploadMultipleImages } from \"../../features/image/imageSlice\";\n\nconst AddImagesModal = () => {\n    const dispatch = useDispatch();\n    const { showAddImagesModal } = useSelector(state => state.examAI);\n    const [selectedFiles, setSelectedFiles] = useState([]);\n    const [previewUrls, setPreviewUrls] = useState([]);\n    const fileInputRef = useRef(null);\n    const { images, loadingUploadImages } = useSelector(state => state.images);\n\n    const handleClose = () => {\n        dispatch(setShowAddImagesModal(false));\n        setSelectedFiles([]);\n        setPreviewUrls([]);\n    };\n\n    const handleFileSelect = (e) => {\n        const files = Array.from(e.target.files);\n\n        // Limit to 10 files\n        const limitedFiles = files.slice(0, 10);\n\n        setSelectedFiles(limitedFiles);\n\n        // Create preview URLs\n        const urls = limitedFiles.map(file => URL.createObjectURL(file));\n        setPreviewUrls(urls);\n    };\n\n    const handleRemoveFile = (index) => {\n        const newFiles = selectedFiles.filter((_, i) => i !== index);\n        const newUrls = previewUrls.filter((_, i) => i !== index);\n\n        // Revoke the removed URL to prevent memory leaks\n        URL.revokeObjectURL(previewUrls[index]);\n\n        setSelectedFiles(newFiles);\n        setPreviewUrls(newUrls);\n    };\n\n    const handleUpload = async () => {\n        if (selectedFiles.length === 0) return;\n\n        try {\n            await dispatch(uploadMultipleImages({ files: selectedFiles, folder: 'ImageFormN8N' })).unwrap();\n            // Clear selected files after successful upload\n            setSelectedFiles([]);\n            setPreviewUrls([]);\n            // Reset file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n        } catch (error) {\n            console.error('Upload failed:', error);\n        }\n    };\n\n    const handleDragOver = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n    };\n\n    const handleDragEnter = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n    };\n\n    const handleDragLeave = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n    };\n\n    const handleDrop = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n\n        const files = Array.from(e.dataTransfer.files);\n        const imageFiles = files.filter(file => file.type.startsWith('image/'));\n        const limitedFiles = imageFiles.slice(0, 10);\n\n        setSelectedFiles(limitedFiles);\n\n        const urls = limitedFiles.map(file => URL.createObjectURL(file));\n        setPreviewUrls(urls);\n    };\n\n    if (!showAddImagesModal) return null;\n\n    return (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\">\n                {/* Header */}\n                <div className=\"flex items-center justify-between p-6 border-b\">\n                    <h2 className=\"text-xl font-semibold text-gray-800\">Thêm ảnh</h2>\n                    <button\n                        onClick={handleClose}\n                        className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n                    >\n                        <X className=\"w-5 h-5\" />\n                    </button>\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-140px)]\">\n                    {/* Upload Area */}\n                    <div className=\"mb-6\">\n                        <div\n                            className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors\"\n                            onDragOver={handleDragOver}\n                            onDragEnter={handleDragEnter}\n                            onDragLeave={handleDragLeave}\n                            onDrop={handleDrop}\n                        >\n                            <Upload className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                            <p className=\"text-lg font-medium text-gray-700 mb-2\">\n                                Kéo thả ảnh vào đây hoặc click để chọn\n                            </p>\n                            <p className=\"text-sm text-gray-500 mb-4\">\n                                Tối đa 10 ảnh, định dạng JPG, PNG, GIF\n                            </p>\n                            <input\n                                ref={fileInputRef}\n                                type=\"file\"\n                                multiple\n                                accept=\"image/*\"\n                                onChange={handleFileSelect}\n                                className=\"hidden\"\n                            />\n                            <button\n                                onClick={() => fileInputRef.current?.click()}\n                                className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors\"\n                            >\n                                Chọn ảnh\n                            </button>\n                        </div>\n                    </div>\n\n                    {/* Selected Files Preview */}\n                    {selectedFiles.length > 0 && (\n                        <div className=\"mb-6\">\n                            <h3 className=\"text-lg font-medium text-gray-800 mb-3\">\n                                Ảnh đã chọn ({selectedFiles.length}/10)\n                            </h3>\n                            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n                                {previewUrls.map((url, index) => (\n                                    <div key={index} className=\"relative group\">\n                                        <img\n                                            src={url}\n                                            alt={`Preview ${index + 1}`}\n                                            className=\"w-full h-32 object-cover rounded-lg border\"\n                                        />\n                                        <button\n                                            onClick={() => handleRemoveFile(index)}\n                                            className=\"absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\"\n                                        >\n                                            <Trash2 className=\"w-4 h-4\" />\n                                        </button>\n                                        <div className=\"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\">\n                                            {selectedFiles[index].name.length > 15\n                                                ? selectedFiles[index].name.substring(0, 15) + '...'\n                                                : selectedFiles[index].name\n                                            }\n                                        </div>\n                                    </div>\n                                ))}\n                            </div>\n                        </div>\n                    )}\n\n                    {/* Existing Images */}\n                    <div>\n                        <h3 className=\"text-lg font-medium text-gray-800 mb-3\">\n                            Ảnh đã tải lên ({images.ImageFormN8N.length})\n                        </h3>\n                        {images.ImageFormN8N.length > 0 ? (\n                            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n                                {images.ImageFormN8N.map((image, index) => (\n                                    <div key={index} className=\"relative group\">\n                                        <img\n                                            src={image.ImageFormN8N || image.url || image}\n                                            alt={`Uploaded ${index + 1}`}\n                                            className=\"w-full h-32 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity\"\n                                        />\n                                    </div>\n                                ))}\n                            </div>\n                        ) : (\n                            <div className=\"text-center py-8 text-gray-500\">\n                                <ImageIcon className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                                <p>Chưa có ảnh nào được tải lên</p>\n                            </div>\n                        )}\n                    </div>\n                </div>\n\n                {/* Footer */}\n                <div className=\"flex items-center justify-between p-6 border-t bg-gray-50\">\n                    <div className=\"text-sm text-gray-600\">\n                        {selectedFiles.length > 0 && `${selectedFiles.length} ảnh đã chọn`}\n                    </div>\n                    <div className=\"flex gap-3\">\n                        <button\n                            onClick={handleClose}\n                            className=\"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n                        >\n                            Đóng\n                        </button>\n                        {selectedFiles.length > 0 && (\n                            <button\n                                onClick={handleUpload}\n                                disabled={loadingUploadImages}\n                                className=\"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-lg transition-colors flex items-center gap-2\"\n                            >\n                                {loadingUploadImages ? (\n                                    <>\n                                        <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                                        Đang tải lên...\n                                    </>\n                                ) : (\n                                    <>\n                                        <Upload className=\"w-4 h-4\" />\n                                        Tải lên\n                                    </>\n                                )}\n                            </button>\n                        )}\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default AddImagesModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,CAAC,EAAEC,MAAM,EAAEC,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,cAAc;AACpE,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,oBAAoB,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAmB,CAAC,GAAGf,WAAW,CAACgB,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EACjE,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMyB,YAAY,GAAGxB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAEyB,MAAM;IAAEC;EAAoB,CAAC,GAAGxB,WAAW,CAACgB,KAAK,IAAIA,KAAK,CAACO,MAAM,CAAC;EAE1E,MAAME,WAAW,GAAGA,CAAA,KAAM;IACtBX,QAAQ,CAACR,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtCa,gBAAgB,CAAC,EAAE,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAMK,gBAAgB,GAAIC,CAAC,IAAK;IAC5B,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;;IAExC;IACA,MAAMI,YAAY,GAAGJ,KAAK,CAACK,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAEvCd,gBAAgB,CAACa,YAAY,CAAC;;IAE9B;IACA,MAAME,IAAI,GAAGF,YAAY,CAACG,GAAG,CAACC,IAAI,IAAIC,GAAG,CAACC,eAAe,CAACF,IAAI,CAAC,CAAC;IAChEf,cAAc,CAACa,IAAI,CAAC;EACxB,CAAC;EAED,MAAMK,gBAAgB,GAAIC,KAAK,IAAK;IAChC,MAAMC,QAAQ,GAAGvB,aAAa,CAACwB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAC5D,MAAMK,OAAO,GAAGzB,WAAW,CAACsB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;;IAEzD;IACAH,GAAG,CAACS,eAAe,CAAC1B,WAAW,CAACoB,KAAK,CAAC,CAAC;IAEvCrB,gBAAgB,CAACsB,QAAQ,CAAC;IAC1BpB,cAAc,CAACwB,OAAO,CAAC;EAC3B,CAAC;EAED,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI7B,aAAa,CAAC8B,MAAM,KAAK,CAAC,EAAE;IAEhC,IAAI;MACA,MAAMlC,QAAQ,CAACP,oBAAoB,CAAC;QAAEqB,KAAK,EAAEV,aAAa;QAAE+B,MAAM,EAAE;MAAe,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MAC/F;MACA/B,gBAAgB,CAAC,EAAE,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClB;MACA,IAAIC,YAAY,CAAC6B,OAAO,EAAE;QACtB7B,YAAY,CAAC6B,OAAO,CAACC,KAAK,GAAG,EAAE;MACnC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IAC1C;EACJ,CAAC;EAED,MAAME,cAAc,GAAI5B,CAAC,IAAK;IAC1BA,CAAC,CAAC6B,cAAc,CAAC,CAAC;IAClB7B,CAAC,CAAC8B,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAMC,eAAe,GAAI/B,CAAC,IAAK;IAC3BA,CAAC,CAAC6B,cAAc,CAAC,CAAC;IAClB7B,CAAC,CAAC8B,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAME,eAAe,GAAIhC,CAAC,IAAK;IAC3BA,CAAC,CAAC6B,cAAc,CAAC,CAAC;IAClB7B,CAAC,CAAC8B,eAAe,CAAC,CAAC;EACvB,CAAC;EAED,MAAMG,UAAU,GAAIjC,CAAC,IAAK;IACtBA,CAAC,CAAC6B,cAAc,CAAC,CAAC;IAClB7B,CAAC,CAAC8B,eAAe,CAAC,CAAC;IAEnB,MAAM7B,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACkC,YAAY,CAACjC,KAAK,CAAC;IAC9C,MAAMkC,UAAU,GAAGlC,KAAK,CAACc,MAAM,CAACN,IAAI,IAAIA,IAAI,CAAC2B,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACvE,MAAMhC,YAAY,GAAG8B,UAAU,CAAC7B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAE5Cd,gBAAgB,CAACa,YAAY,CAAC;IAE9B,MAAME,IAAI,GAAGF,YAAY,CAACG,GAAG,CAACC,IAAI,IAAIC,GAAG,CAACC,eAAe,CAACF,IAAI,CAAC,CAAC;IAChEf,cAAc,CAACa,IAAI,CAAC;EACxB,CAAC;EAED,IAAI,CAACnB,kBAAkB,EAAE,OAAO,IAAI;EAEpC,oBACIN,OAAA;IAAKwD,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACvFzD,OAAA;MAAKwD,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBAE7FzD,OAAA;QAAKwD,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC3DzD,OAAA;UAAIwD,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjE7D,OAAA;UACI8D,OAAO,EAAE9C,WAAY;UACrBwC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eAEhEzD,OAAA,CAACR,CAAC;YAACgE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGN7D,OAAA;QAAKwD,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAEzDzD,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACjBzD,OAAA;YACIwD,SAAS,EAAC,2GAA2G;YACrHO,UAAU,EAAEjB,cAAe;YAC3BkB,WAAW,EAAEf,eAAgB;YAC7BgB,WAAW,EAAEf,eAAgB;YAC7BgB,MAAM,EAAEf,UAAW;YAAAM,QAAA,gBAEnBzD,OAAA,CAACP,MAAM;cAAC+D,SAAS,EAAC;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3D7D,OAAA;cAAGwD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ7D,OAAA;cAAGwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ7D,OAAA;cACImE,GAAG,EAAEtD,YAAa;cAClByC,IAAI,EAAC,MAAM;cACXc,QAAQ;cACRC,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAErD,gBAAiB;cAC3BuC,SAAS,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACF7D,OAAA;cACI8D,OAAO,EAAEA,CAAA;gBAAA,IAAAS,qBAAA;gBAAA,QAAAA,qBAAA,GAAM1D,YAAY,CAAC6B,OAAO,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;cAAA,CAAC;cAC7ChB,SAAS,EAAC,iFAAiF;cAAAC,QAAA,EAC9F;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLpD,aAAa,CAAC8B,MAAM,GAAG,CAAC,iBACrBvC,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACjBzD,OAAA;YAAIwD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,iCACtC,EAAChD,aAAa,CAAC8B,MAAM,EAAC,MACvC;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7D,OAAA;YAAKwD,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAChE9C,WAAW,CAACe,GAAG,CAAC,CAAC+C,GAAG,EAAE1C,KAAK,kBACxB/B,OAAA;cAAiBwD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBACvCzD,OAAA;gBACI0E,GAAG,EAAED,GAAI;gBACTE,GAAG,aAAAC,MAAA,CAAa7C,KAAK,GAAG,CAAC,CAAG;gBAC5ByB,SAAS,EAAC;cAA4C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACF7D,OAAA;gBACI8D,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAACC,KAAK,CAAE;gBACvCyB,SAAS,EAAC,uIAAuI;gBAAAC,QAAA,eAEjJzD,OAAA,CAACJ,MAAM;kBAAC4D,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACT7D,OAAA;gBAAKwD,SAAS,EAAC,sFAAsF;gBAAAC,QAAA,EAChGhD,aAAa,CAACsB,KAAK,CAAC,CAAC8C,IAAI,CAACtC,MAAM,GAAG,EAAE,GAChC9B,aAAa,CAACsB,KAAK,CAAC,CAAC8C,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAClDrE,aAAa,CAACsB,KAAK,CAAC,CAAC8C;cAAI;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE9B,CAAC;YAAA,GAjBA9B,KAAK;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,eAGD7D,OAAA;UAAAyD,QAAA,gBACIzD,OAAA;YAAIwD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,uCACnC,EAAC3C,MAAM,CAACiE,YAAY,CAACxC,MAAM,EAAC,GAChD;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACJ/C,MAAM,CAACiE,YAAY,CAACxC,MAAM,GAAG,CAAC,gBAC3BvC,OAAA;YAAKwD,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAChE3C,MAAM,CAACiE,YAAY,CAACrD,GAAG,CAAC,CAACsD,KAAK,EAAEjD,KAAK,kBAClC/B,OAAA;cAAiBwD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eACvCzD,OAAA;gBACI0E,GAAG,EAAEM,KAAK,CAACD,YAAY,IAAIC,KAAK,CAACP,GAAG,IAAIO,KAAM;gBAC9CL,GAAG,cAAAC,MAAA,CAAc7C,KAAK,GAAG,CAAC,CAAG;gBAC7ByB,SAAS,EAAC;cAA+F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5G;YAAC,GALI9B,KAAK;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,gBAEN7D,OAAA;YAAKwD,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC3CzD,OAAA,CAACL,SAAS;cAAC6D,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3D7D,OAAA;cAAAyD,QAAA,EAAG;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN7D,OAAA;QAAKwD,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACtEzD,OAAA;UAAKwD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACjChD,aAAa,CAAC8B,MAAM,GAAG,CAAC,OAAAqC,MAAA,CAAOnE,aAAa,CAAC8B,MAAM;QAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACN7D,OAAA;UAAKwD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBzD,OAAA;YACI8D,OAAO,EAAE9C,WAAY;YACrBwC,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAC5E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRpD,aAAa,CAAC8B,MAAM,GAAG,CAAC,iBACrBvC,OAAA;YACI8D,OAAO,EAAExB,YAAa;YACtB2C,QAAQ,EAAElE,mBAAoB;YAC9ByC,SAAS,EAAC,8HAA8H;YAAAC,QAAA,EAEvI1C,mBAAmB,gBAChBf,OAAA,CAAAE,SAAA;cAAAuD,QAAA,gBACIzD,OAAA;gBAAKwD,SAAS,EAAC;cAA8E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gCAExG;YAAA,eAAE,CAAC,gBAEH7D,OAAA,CAAAE,SAAA;cAAAuD,QAAA,gBACIzD,OAAA,CAACP,MAAM;gBAAC+D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAElC;YAAA,eAAE;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzD,EAAA,CArOID,cAAc;EAAA,QACCb,WAAW,EACGC,WAAW,EAIFA,WAAW;AAAA;AAAA2F,EAAA,GANjD/E,cAAc;AAuOpB,eAAeA,cAAc;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}