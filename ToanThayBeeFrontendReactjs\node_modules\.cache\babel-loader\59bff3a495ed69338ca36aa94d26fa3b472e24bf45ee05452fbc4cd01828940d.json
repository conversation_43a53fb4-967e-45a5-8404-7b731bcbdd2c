{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as sheetApi from \"../../services/sheetApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nexport const readSheetAndUpdateTuitionSheet = createAsyncThunk(\"sheet/readSheetAndUpdateTuitionSheet\", async (_ref, _ref2) => {\n  let {\n    sheetUrl,\n    month,\n    colNames\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, sheetApi.readSheetAndUpdateTuitionSheet, {\n    sheetUrl,\n    month\n  }, null, true, false, false, false);\n});\nconst initialState = {\n  loadingUpdate: false,\n  error: null,\n  success: false,\n  data: null,\n  sheetUrl: null,\n  month: null,\n  colNames: {\n    studentPhone: \"SĐT HS\",\n    parentPhone: \"SĐT PH\",\n    fullName: \"HỌ VÀ TÊN\",\n    targetMonthCol: \"ĐÃ ĐÓNG T ...\"\n  }\n};\nconst sheetSlice = createSlice({\n  name: \"sheet\",\n  initialState,\n  reducers: {\n    setSheetUrl: (state, action) => {\n      state.sheetUrl = action.payload;\n    },\n    setMonth: (state, action) => {\n      state.month = action.payload;\n    },\n    setColNames: (state, action) => {\n      state.colNames = action.payload;\n    },\n    resetSheetState: state => {\n      state.loadingUpdate = false;\n      state.error = null;\n      state.success = false;\n      state.data = null;\n      state.sheetUrl = null;\n      state.month = null;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(readSheetAndUpdateTuitionSheet.pending, state => {\n      state.loadingUpdate = true;\n      state.success = false;\n    }).addCase(readSheetAndUpdateTuitionSheet.fulfilled, (state, action) => {\n      state.loadingUpdate = false;\n      state.success = true;\n      if (action.payload) {\n        state.data = action.payload.data;\n      }\n    }).addCase(readSheetAndUpdateTuitionSheet.rejected, (state, action) => {\n      state.loadingUpdate = false;\n    });\n  }\n});\nexport const {\n  setSheetUrl,\n  setMonth,\n  setColNames,\n  resetSheetState\n} = sheetSlice.actions;\nexport default sheetSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "sheetApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "readSheetAndUpdateTuitionSheet", "_ref", "_ref2", "sheetUrl", "month", "colNames", "dispatch", "initialState", "loadingUpdate", "error", "success", "data", "studentPhone", "parentPhone", "fullName", "targetMonthCol", "sheetSlice", "name", "reducers", "setSheetUrl", "state", "action", "payload", "setMonth", "setColNames", "resetSheetState", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/sheet/sheetSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as sheetApi from \"../../services/sheetApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\n\r\nexport const readSheetAndUpdateTuitionSheet = createAsyncThunk(\r\n    \"sheet/readSheetAndUpdateTuitionSheet\",\r\n    async ({ sheetUrl, month, colNames }, { dispatch }) => {\r\n        return await apiHandler(dispatch, sheetApi.readSheetAndUpdateTuitionSheet, { sheetUrl, month }, null, true, false, false, false);\r\n\r\n    }\r\n);\r\n\r\nconst initialState = {\r\n    loadingUpdate: false,\r\n    error: null,\r\n    success: false,\r\n    data: null,\r\n    sheetUrl: null,\r\n    month: null,\r\n    colNames: {\r\n        studentPhone: \"SĐT HS\",\r\n        parentPhone: \"SĐT PH\",\r\n        fullName: \"HỌ VÀ TÊN\",\r\n        targetMonthCol: \"ĐÃ ĐÓNG T ...\"\r\n    }\r\n};\r\n\r\nconst sheetSlice = createSlice({\r\n    name: \"sheet\",\r\n    initialState,\r\n    reducers: {\r\n        setSheetUrl: (state, action) => {\r\n            state.sheetUrl = action.payload;\r\n        },\r\n        setMonth: (state, action) => {\r\n            state.month = action.payload;\r\n        },\r\n        setColNames: (state, action) => {\r\n            state.colNames = action.payload;\r\n        },\r\n        resetSheetState: (state) => {\r\n            state.loadingUpdate = false;\r\n            state.error = null;\r\n            state.success = false;\r\n            state.data = null;\r\n            state.sheetUrl = null;\r\n            state.month = null;\r\n        },\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(readSheetAndUpdateTuitionSheet.pending, (state) => {\r\n                state.loadingUpdate = true;\r\n                state.success = false;\r\n            })\r\n            .addCase(readSheetAndUpdateTuitionSheet.fulfilled, (state, action) => {\r\n                state.loadingUpdate = false;\r\n                state.success = true;\r\n                if (action.payload) {\r\n                    state.data = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(readSheetAndUpdateTuitionSheet.rejected, (state, action) => {\r\n                state.loadingUpdate = false;\r\n            });\r\n    },\r\n});\r\n\r\nexport const {\r\n    setSheetUrl,\r\n    setMonth,\r\n    setColNames,\r\n    resetSheetState\r\n} = sheetSlice.actions;\r\nexport default sheetSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,QAAQ,MAAM,yBAAyB;AACnD,SAASC,UAAU,QAAQ,wBAAwB;AAEnD,OAAO,MAAMC,8BAA8B,GAAGH,gBAAgB,CAC1D,sCAAsC,EACtC,OAAAI,IAAA,EAAAC,KAAA,KAAuD;EAAA,IAAhD;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAAJ,IAAA;EAAA,IAAE;IAAEK;EAAS,CAAC,GAAAJ,KAAA;EAC9C,OAAO,MAAMH,UAAU,CAACO,QAAQ,EAAER,QAAQ,CAACE,8BAA8B,EAAE;IAAEG,QAAQ;IAAEC;EAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAEpI,CACJ,CAAC;AAED,MAAMG,YAAY,GAAG;EACjBC,aAAa,EAAE,KAAK;EACpBC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,KAAK;EACdC,IAAI,EAAE,IAAI;EACVR,QAAQ,EAAE,IAAI;EACdC,KAAK,EAAE,IAAI;EACXC,QAAQ,EAAE;IACNO,YAAY,EAAE,QAAQ;IACtBC,WAAW,EAAE,QAAQ;IACrBC,QAAQ,EAAE,WAAW;IACrBC,cAAc,EAAE;EACpB;AACJ,CAAC;AAED,MAAMC,UAAU,GAAGpB,WAAW,CAAC;EAC3BqB,IAAI,EAAE,OAAO;EACbV,YAAY;EACZW,QAAQ,EAAE;IACNC,WAAW,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC5BD,KAAK,CAACjB,QAAQ,GAAGkB,MAAM,CAACC,OAAO;IACnC,CAAC;IACDC,QAAQ,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MACzBD,KAAK,CAAChB,KAAK,GAAGiB,MAAM,CAACC,OAAO;IAChC,CAAC;IACDE,WAAW,EAAEA,CAACJ,KAAK,EAAEC,MAAM,KAAK;MAC5BD,KAAK,CAACf,QAAQ,GAAGgB,MAAM,CAACC,OAAO;IACnC,CAAC;IACDG,eAAe,EAAGL,KAAK,IAAK;MACxBA,KAAK,CAACZ,aAAa,GAAG,KAAK;MAC3BY,KAAK,CAACX,KAAK,GAAG,IAAI;MAClBW,KAAK,CAACV,OAAO,GAAG,KAAK;MACrBU,KAAK,CAACT,IAAI,GAAG,IAAI;MACjBS,KAAK,CAACjB,QAAQ,GAAG,IAAI;MACrBiB,KAAK,CAAChB,KAAK,GAAG,IAAI;IACtB;EACJ,CAAC;EACDsB,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC5B,8BAA8B,CAAC6B,OAAO,EAAGT,KAAK,IAAK;MACxDA,KAAK,CAACZ,aAAa,GAAG,IAAI;MAC1BY,KAAK,CAACV,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDkB,OAAO,CAAC5B,8BAA8B,CAAC8B,SAAS,EAAE,CAACV,KAAK,EAAEC,MAAM,KAAK;MAClED,KAAK,CAACZ,aAAa,GAAG,KAAK;MAC3BY,KAAK,CAACV,OAAO,GAAG,IAAI;MACpB,IAAIW,MAAM,CAACC,OAAO,EAAE;QAChBF,KAAK,CAACT,IAAI,GAAGU,MAAM,CAACC,OAAO,CAACX,IAAI;MACpC;IACJ,CAAC,CAAC,CACDiB,OAAO,CAAC5B,8BAA8B,CAAC+B,QAAQ,EAAE,CAACX,KAAK,EAAEC,MAAM,KAAK;MACjED,KAAK,CAACZ,aAAa,GAAG,KAAK;IAC/B,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACTW,WAAW;EACXI,QAAQ;EACRC,WAAW;EACXC;AACJ,CAAC,GAAGT,UAAU,CAACgB,OAAO;AACtB,eAAehB,UAAU,CAACiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}