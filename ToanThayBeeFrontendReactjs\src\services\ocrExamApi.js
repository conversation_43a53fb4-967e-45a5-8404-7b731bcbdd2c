import apin8n from "./apin8n";
import api from "./api";

export const uploadFile = async (Invoice) => {
    const formData = new FormData();
    formData.append('Invoice', Invoice);

    const response = await apin8n.post(
        "/webhook/ocr-exam",
        formData,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }
    );

    return response;
}

export const getAllExamAPI = async ({ search, page, pageSize, sortOrder }) => {
    const data = {
        search,
        page,
        pageSize,
        sortOrder
    };

    return api.get("/v1/admin/exam1", {
        params: data
    });
}

export const getAllQuestionsByExamIdAPI = async (examId) => {
    return api.get(`/v1/admin/exam1/${examId}`);
}

export const saveExam1API = async ({ examData, questions, id }) => {
    const response = await api.put(`/v1/admin/exam1/${id}`, {
        exam: examData,
        questions,
    });

    return response;
}

export const commitExam1API = async (id) => {
    return api.post(`/v1/admin/exam1/${id}/commit`);
}
