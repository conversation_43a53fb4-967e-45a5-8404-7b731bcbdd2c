import { useSelector, useDispatch } from "react-redux";
import { setSelectedIndex } from "src/features/questionsExam/questionsExamSlice";
import LatexRenderer from "../latex/RenderLatex";
import MarkdownPreviewWithMath from "../latex/MarkDownPreview";

const QuestionContent = ({ question, index }) => {
    const dispatch = useDispatch();
    const { selectedIndex } = useSelector((state) => state.questionsExam);
    const { codes } = useSelector((state) => state.codes);

    return (
        <div
            key={question.id}
            className={`
            flex flex-col gap-3 p-3 rounded-lg border transition cursor-pointer
            ${selectedIndex === question.ExamQuestions?.order ? "bg-blue-50 border-blue-500" : "bg-white hover:bg-gray-50 border-gray-300"}`}
            onClick={() => dispatch(setSelectedIndex(question.ExamQuestions?.order))}
        >
            {/* Thông tin câu hỏi */}
            <div div className="flex flex-wrap items-center text-xs text-gray-500 gap-x-4 gap-y-1" >
                <span className="font-medium text-gray-700">Câu {index + 1}</span>
                <span>Độ khó: <span className="text-gray-700">{question.difficulty}</span></span>
                <span>
                    Chương:{" "}
                    <span className="text-gray-700">
                        {codes["chapter"]?.find(c => c.code === question.chapter)?.description || "Chưa xác định"}
                    </span>
                </span>
            </div>

            <div className="flex flex-col gap-2">
                <div className="text-base text-gray-800 leading-relaxed">
                    <LatexRenderer className="text-gray-800 text-xs" text={question.content} />
                </div>
                {question.imageUrl && (
                    <div className="flex flex-col items-center justify-center w-full h-[10rem] mt-1">
                        <img
                            src={question.imageUrl}
                            alt="question"
                            className="object-contain w-full h-full"
                        />
                    </div>
                )}
            </div>

            {question.solution && (
                <div className="mt-2">
                    <h6 className="text-xs font-bold">Lời giải</h6>
                    <MarkdownPreviewWithMath content={question.solution} className=" w-full" style={{ fontSize: '12px' }} />
                </div>
            )}


        </div>
    )
}

export default QuestionContent;