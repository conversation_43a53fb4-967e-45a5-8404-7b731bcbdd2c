{"ast": null, "code": "/**\n * Global filter for KaTeX warnings\n * This should be imported and called once in the main App.js or index.js\n */\n\nexport const setupKatexWarningFilter = () => {\n  // Store the original console.warn\n  const originalWarn = console.warn;\n\n  // Override console.warn with a filtered version\n  console.warn = function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    const message = args.join(' ');\n\n    // Filter out KaTeX-related warnings that we don't want to see\n    const katexWarningPatterns = ['No character metrics for', 'Unrecognized Unicode character', 'LaTeX-incompatible input and strict mode is set to', 'unknownSymbol'];\n\n    // Check if this warning matches any KaTeX pattern\n    const isKatexWarning = katexWarningPatterns.some(pattern => message.includes(pattern));\n\n    // Only show the warning if it's not a KaTeX warning we want to suppress\n    if (!isKatexWarning) {\n      originalWarn.apply(console, args);\n    }\n  };\n\n  // Return a function to restore original console.warn if needed\n  return () => {\n    console.warn = originalWarn;\n  };\n};", "map": {"version": 3, "names": ["setupKatexWarningFilter", "originalWarn", "console", "warn", "_len", "arguments", "length", "args", "Array", "_key", "message", "join", "katexWarningPatterns", "isKatexWarning", "some", "pattern", "includes", "apply"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/utils/setupKatexWarningFilter.js"], "sourcesContent": ["/**\n * Global filter for KaTeX warnings\n * This should be imported and called once in the main App.js or index.js\n */\n\nexport const setupKatexWarningFilter = () => {\n    // Store the original console.warn\n    const originalWarn = console.warn;\n    \n    // Override console.warn with a filtered version\n    console.warn = (...args) => {\n        const message = args.join(' ');\n        \n        // Filter out KaTeX-related warnings that we don't want to see\n        const katexWarningPatterns = [\n            'No character metrics for',\n            'Unrecognized Unicode character',\n            'LaTeX-incompatible input and strict mode is set to',\n            'unknownSymbol'\n        ];\n        \n        // Check if this warning matches any KaTeX pattern\n        const isKatexWarning = katexWarningPatterns.some(pattern => \n            message.includes(pattern)\n        );\n        \n        // Only show the warning if it's not a KaTeX warning we want to suppress\n        if (!isKatexWarning) {\n            originalWarn.apply(console, args);\n        }\n    };\n    \n    // Return a function to restore original console.warn if needed\n    return () => {\n        console.warn = originalWarn;\n    };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,uBAAuB,GAAGA,CAAA,KAAM;EACzC;EACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,IAAI;;EAEjC;EACAD,OAAO,CAACC,IAAI,GAAG,YAAa;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IACnB,MAAMC,OAAO,GAAGH,IAAI,CAACI,IAAI,CAAC,GAAG,CAAC;;IAE9B;IACA,MAAMC,oBAAoB,GAAG,CACzB,0BAA0B,EAC1B,gCAAgC,EAChC,oDAAoD,EACpD,eAAe,CAClB;;IAED;IACA,MAAMC,cAAc,GAAGD,oBAAoB,CAACE,IAAI,CAACC,OAAO,IACpDL,OAAO,CAACM,QAAQ,CAACD,OAAO,CAC5B,CAAC;;IAED;IACA,IAAI,CAACF,cAAc,EAAE;MACjBZ,YAAY,CAACgB,KAAK,CAACf,OAAO,EAAEK,IAAI,CAAC;IACrC;EACJ,CAAC;;EAED;EACA,OAAO,MAAM;IACTL,OAAO,CAACC,IAAI,GAAGF,YAAY;EAC/B,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}