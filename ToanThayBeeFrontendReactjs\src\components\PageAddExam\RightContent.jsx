// Optimized Preview Panel Component
import { useSelector, useDispatch } from "react-redux";
import { Eye, FileText, Image, File, ImagePlus } from "lucide-react";
import { useEffect, useState } from "react";
import PdfViewer from "../ViewPdf";
import NavigateBar from "./NavigateBar";
import * as questionUntil from "src/utils/question/questionUtils";
import useDebouncedEffect from "src/hooks/useDebouncedEffect";
import { setQuestions, setSelectedIndex, setShowAddImagesModal, setViewRightContent } from "src/features/addExam/addExamSlice";
import LatexRenderer from "../latex/RenderLatex";
import MarkdownPreviewWithMath from "../latex/MarkDownPreview";

const ImageExamView = () => {
    const { examImage } = useSelector((state) => state.addExam);

    return (
        <>
            {examImage ? (
                <div className="p-3">
                    <img src={URL.createObjectURL(examImage)} alt="exam" className="w-full object-contain" />
                </div>
            ) : (
                <div className="p-3 flex flex-col items-center">
                    <Image className="w-6 h-6 text-gray-400" />
                    <p className="text-sm text-gray-500">Chưa có ảnh</p>
                </div>
            )}
        </>
    )
}

const PdfView = () => {
    const { examFile } = useSelector((state) => state.addExam);

    return (
        <>
            {examFile ? (
                <div className="p-3">
                    <PdfViewer url={URL.createObjectURL(examFile)} />
                </div>
            ) : (
                <div className="p-3 flex flex-col items-center">
                    <File className="w-6 h-6 text-gray-400" />
                    <p className="text-sm text-gray-500">Chưa có file PDF</p>
                </div>
            )}
        </>
    )
}

const ImageView = () => {
    const { images } = useSelector((state) => state.images);
    const { folder } = useSelector((state) => state.addExam);
    const dispatch = useDispatch();

    return (
        <div className="text-xs grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-3">
            <button
                onClick={() => dispatch(setShowAddImagesModal(true))}
                className="border-dashed border-2 hover:bg-gray-100 border-gray-300 hover:shadow-lg rounded-lg p-8 flex flex-col items-center justify-center gap-2"
            >
                Thêm ảnh
            </button>
            {images?.[folder]?.map((image, index) => (
                <div key={index} className="mb-6 group relative w-full">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hình ảnh {index + 1}:
                    </label>

                    <div
                        className="relative w-full h-48 rounded-lg overflow-hidden border transition-all duration-300 
                   hover:border-sky-400 hover:shadow-lg group cursor-move"
                        draggable
                        onDragStart={(e) => {
                            e.dataTransfer.setData("text/plain", image);
                        }}
                    >
                        <img
                            src={image}
                            alt={`image-${index}`}
                            className="w-full h-full object-contain transition-all duration-300 group-hover:brightness-75"
                        />

                        {/* Icon hiện khi hover */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                            <ImagePlus className="w-8 h-8 text-white bg-sky-500 p-1 rounded-full shadow-lg" />
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
}


const ExamView = () => {
    const { examData, examImage, examFile } = useSelector((state) => state.addExam);
    const [view, setView] = useState('image');

    return (
        <>
            {/* Compact Exam Header */}
            <div div className="mb-3 p-3" >
                <h3 className="text-sm font-bold text-gray-900 mb-2">
                    {examData.name || "Tên đề thi"}
                </h3>
                <div className="grid grid-cols-2 gap-1 text-xs text-gray-600">
                    <div><span className="font-medium">Kiểu:</span> {examData.typeOfExam || "Chưa chọn"}</div>
                    <div><span className="font-medium">Lớp:</span> {examData.class || "Chưa chọn"}</div>
                    <div><span className="font-medium">Năm:</span> {examData.year || "Chưa chọn"}</div>
                    {examData.chapter && <div><span className="font-medium">Chương:</span> {examData.chapter}</div>}
                    {examData.testDuration && <div><span className="font-medium">Thời gian:</span> {examData.testDuration}p</div>}
                    {examData.passRate && <div><span className="font-medium">Điểm đạt:</span> {examData.passRate}%</div>}
                </div>
                {
                    examData.solutionUrl && (
                        <div className="mt-2">
                            <p className="text-xs text-gray-600">Link lời giải: {examData.solutionUrl}</p>
                        </div>
                    )
                }
                {
                    examData.description && (
                        <div className="mt-2">
                            <p className="text-xs text-gray-600">{examData.description}</p>
                        </div>
                    )
                }
            </div>

            {/* Compact Status Badges */}
            <div className="p-3" >
                <div className="flex flex-wrap gap-1">
                    {examData.public && (
                        <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                            Công khai
                        </span>
                    )}
                    {examData.isClassroomExam && (
                        <span className="px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                            Đề thi lớp
                        </span>
                    )}
                    {!examData.public && !examData.isClassroomExam && (
                        <span className="px-2 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                            Riêng tư
                        </span>
                    )}
                </div>
            </div>
            <NavigateBar
                list={[{
                    id: 1,
                    name: 'Ảnh đề thi',
                    value: 'image'
                },
                {
                    id: 2,
                    name: 'File đề thi',
                    value: 'pdf'
                }
                ]}
                active={view}
                setActive={setView}
            />
            {view === 'image' && <ImageExamView />}
            {view === 'pdf' && <PdfView />}

        </>
    )
}
const QuestionViewHeader = ({ title, count, noQuestionText }) => {
    return (
        <div className=" p-3 ">
            <div className="flex items-center gap-1 mb-2">
                <FileText className="w-3 h-3 text-gray-600" />
                <h4 className="text-xs font-semibold text-gray-900">{title} ({count})</h4>
            </div>
            {count === 0 && (
                <div className="text-center py-4 text-gray-500">
                    <FileText className="w-6 h-6 mx-auto mb-1 opacity-50" />
                    <p className="text-xs">{noQuestionText}</p>
                </div>
            )}
        </div>
    )
}

const QuestionTNView = () => {
    const { questions, step, selectedIndex } = useSelector((state) => state.addExam);
    const [questionsTN, setQuestionsTN] = useState([]);
    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];
    const dispatch = useDispatch();

    useEffect(() => {
        setQuestionsTN(questions.filter(q => q.questionData.typeOfQuestion === 'TN'));
    }, [questions]);

    return (
        <>
            <QuestionViewHeader title="Câu hỏi trắc nghiệm" count={questionsTN.length} noQuestionText="Chưa có câu hỏi trắc nghiệm" />
            <div className="p-3">
                {questionsTN.map((question, index) => (
                    <div
                        key={index} className={`mb-4 p-1 ${step === 3 && selectedIndex === index ? 'border border-blue-500 rounded-lg bg-blue-50' : ''} ${step === 3 ? 'cursor-pointer' : ''}`}
                        onClick={() => step === 3 && dispatch(setSelectedIndex(index))}
                    >
                        <h5 className="text-xs font-bold">Câu hỏi {index + 1}
                            <span className="text-gray-500 font-normal"> ({question.questionData.class || "Chưa chọn"} - {question.questionData.chapter || "Chưa chọn"} - {question.questionData.difficulty || "Chưa chọn"})</span>
                        </h5>
                        <LatexRenderer text={question.questionData.content} className="text-xs break-words w-full" />
                        <div className="flex flex-col gap-2 mt-2">
                            {question.statements.map((statement, index) => (
                                <div key={index} className="flex items-center gap-1">
                                    <p className="text-xs font-bold whitespace-nowrap">
                                        {prefixTN[index]}
                                    </p>
                                    <LatexRenderer text={statement.content} className={`text-xs break-words w-full ${statement.isCorrect ? 'text-green-500' : ''}`} />
                                </div>
                            ))}
                        </div>
                        {question.questionData.solution && (
                            <div className="mt-2">
                                <h6 className="text-xs font-bold">Lời giải</h6>
                                <MarkdownPreviewWithMath content={question.questionData.solution} className=" w-full" style={{ fontSize: '12px' }} />
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </>
    )
}

const QuestionDSView = () => {
    const { questions, step, selectedIndex } = useSelector((state) => state.addExam);
    const [questionsDS, setQuestionsDS] = useState([]);
    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];
    const dispatch = useDispatch();

    useEffect(() => {
        setQuestionsDS(questions.filter(q => q.questionData.typeOfQuestion === 'DS'));
    }, [questions]);

    return (
        <>
            <QuestionViewHeader title="Câu hỏi đúng sai" count={questionsDS.length} noQuestionText="Chưa có câu hỏi đúng sai" />
            <div className="p-3">
                {questionsDS.map((question, index) => (
                    <div
                        key={index} className={`mb-4 p-1 ${step === 3 && selectedIndex === index ? 'border border-blue-500 rounded-lg bg-blue-50' : ''} ${step === 3 ? 'cursor-pointer' : ''}`}
                        onClick={() => step === 3 && dispatch(setSelectedIndex(index))}
                    >
                        <h5 className="text-xs font-bold">Câu hỏi {index + 1}
                            <span className="text-gray-500 font-normal"> ({question.questionData.class || "Chưa chọn"} - {question.questionData.chapter || "Chưa chọn"} - {question.questionData.difficulty || "Chưa chọn"})</span>
                        </h5>
                        <LatexRenderer text={question.questionData.content} className="text-xs break-words w-full" />
                        <div className="flex flex-col gap-2 mt-2">
                            {question.statements.map((statement, index) => (
                                <div key={index} className="flex items-center gap-1">
                                    <p className="text-xs font-bold whitespace-nowrap">
                                        {prefixDS[index]}
                                    </p>
                                    <LatexRenderer text={statement.content} className={`text-xs break-words w-full ${statement.isCorrect ? 'text-green-500' : 'text-red-500'}`} />
                                </div>
                            ))}
                        </div>
                        {question.questionData.solution && (
                            <div className="mt-2">
                                <h6 className="text-xs font-bold">Lời giải</h6>
                                <MarkdownPreviewWithMath content={question.questionData.solution} className=" w-full" style={{ fontSize: '0.75rem' }} />
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </>
    )
}

const QuestionTLNView = () => {
    const { questions, step, selectedIndex } = useSelector((state) => state.addExam);
    const dispatch = useDispatch();
    const [questionsTLN, setQuestionsTLN] = useState([]);

    useEffect(() => {
        setQuestionsTLN(questions.filter(q => q.questionData.typeOfQuestion === 'TLN'));
    }, [questions]);

    return (
        <>
            <QuestionViewHeader title="Câu hỏi trả lời ngắn" count={questionsTLN.length} noQuestionText="Chưa có câu hỏi trả lời ngắn" />
            <div className="p-3">
                {questionsTLN.map((question, index) => (
                    <div
                        key={index} className={`mb-4 p-1 ${step === 3 && selectedIndex === index ? 'border border-blue-500 rounded-lg bg-blue-50' : ''} ${step === 3 ? 'cursor-pointer' : ''}`}
                        onClick={() => step === 3 && dispatch(setSelectedIndex(index))}
                    >
                        <h5 className="text-xs font-bold">Câu hỏi {index + 1}
                            <span className="text-gray-500 font-normal"> ({question.questionData.class || "Chưa chọn"} - {question.questionData.chapter || "Chưa chọn"} - {question.questionData.difficulty || "Chưa chọn"})</span>
                        </h5>
                        <LatexRenderer text={question.questionData.content} className="text-xs break-words w-full" />
                        <p className="text-xs font-bold mt-2">Đáp án:</p>
                        <LatexRenderer text={question.questionData.correctAnswer} className="text-xs break-words w-full" />
                        {question.questionData.solution && (
                            <div className="mt-2">
                                <h6 className="text-xs font-bold">Lời giải</h6>
                                <MarkdownPreviewWithMath content={question.questionData.solution} className=" w-full" style={{ fontSize: '0.75rem' }} />
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </>
    )
}

const QuestionView = () => {

    return (
        <>
            <QuestionTNView />
            <hr className="border-gray-200" />
            <QuestionDSView />
            <hr className="border-gray-200" />
            <QuestionTLNView />
        </>
    )
}

const RightContent = () => {
    const { examData, view } = useSelector((state) => state.addExam);
    const dispatch = useDispatch();

    return (
        <div className="flex flex-col h-[calc(100vh_-_42px)] bg-gray-50">
            {/* Compact Preview Header */}
            <div className="flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10">
                <div className="flex items-center gap-2">
                    <Eye className="w-3 h-3 text-gray-600" />
                    <h2 className="text-xs font-semibold text-gray-900">Xem trước</h2>
                </div>
            </div>

            {/* Scrollable Preview Content */}
            <div className="flex-1 overflow-y-auto p-3">
                <div className="bg-white rounded border border-gray-200 ">
                    <NavigateBar
                        list={[{
                            id: 1,
                            name: 'Đề thi',
                            value: 'exam'
                        },
                        {
                            id: 2,
                            name: 'Câu hỏi',
                            value: 'question'
                        },
                        {
                            id: 3,
                            name: 'Ảnh',
                            value: 'image'
                        }
                        ]}
                        active={view}
                        setActive={(value) => dispatch(setViewRightContent(value))}
                    />
                    {view === 'exam' && <ExamView />}
                    {view === 'question' && <QuestionView />}
                    {view === 'image' && <ImageView />}
                </div>
            </div>
        </div>
    );
};


export default RightContent;