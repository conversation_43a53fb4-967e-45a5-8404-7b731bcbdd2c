// Optimized Preview Panel Component
import { useSelector } from "react-redux";
import { Eye, FileText } from "lucide-react";


const RightContent = () => {
    const { examData } = useSelector((state) => state.addExam);

    return (
        <div className="flex flex-col h-full bg-gray-50">
            {/* Compact Preview Header */}
            <div className="bg-white border-b border-gray-200 px-3 py-2">
                <div className="flex items-center gap-2">
                    <Eye className="w-3 h-3 text-gray-600" />
                    <h2 className="text-xs font-semibold text-gray-900">Xem trước</h2>
                </div>
            </div>

            {/* Scrollable Preview Content */}
            <div className="flex-1 overflow-y-auto p-3">
                <div className="bg-white rounded border border-gray-200 p-3">
                    {/* Compact Exam Header */}
                    <div className="mb-3">
                        <h3 className="text-sm font-bold text-gray-900 mb-2">
                            {examData.name || "Tên đề thi"}
                        </h3>
                        <div className="grid grid-cols-2 gap-1 text-xs text-gray-600">
                            <div><span className="font-medium">Kiểu:</span> {examData.typeOfExam || "Chưa chọn"}</div>
                            <div><span className="font-medium">Lớp:</span> {examData.class || "Chưa chọn"}</div>
                            <div><span className="font-medium">Năm:</span> {examData.year || "Chưa chọn"}</div>
                            {examData.chapter && <div><span className="font-medium">Chương:</span> {examData.chapter}</div>}
                            {examData.testDuration && <div><span className="font-medium">Thời gian:</span> {examData.testDuration}p</div>}
                            {examData.passRate && <div><span className="font-medium">Điểm đạt:</span> {examData.passRate}%</div>}
                        </div>
                        {examData.description && (
                            <div className="mt-2">
                                <p className="text-xs text-gray-600">{examData.description}</p>
                            </div>
                        )}
                    </div>

                    {/* Compact Status Badges */}
                    <div className="mb-3">
                        <div className="flex flex-wrap gap-1">
                            {examData.public && (
                                <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                    Công khai
                                </span>
                            )}
                            {examData.isClassroomExam && (
                                <span className="px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                    Đề thi lớp
                                </span>
                            )}
                            {!examData.public && !examData.isClassroomExam && (
                                <span className="px-2 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                                    Riêng tư
                                </span>
                            )}
                        </div>
                    </div>

                    {/* Questions Preview Placeholder */}
                    <div className="border-t border-gray-200 pt-3">
                        <div className="flex items-center gap-1 mb-2">
                            <FileText className="w-3 h-3 text-gray-600" />
                            <h4 className="text-xs font-semibold text-gray-900">Câu hỏi (0)</h4>
                        </div>
                        <div className="text-center py-4 text-gray-500">
                            <FileText className="w-6 h-6 mx-auto mb-1 opacity-50" />
                            <p className="text-xs">Chưa có câu hỏi</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};


export default RightContent;