// Optimized Preview Panel Component
import { useSelector, useDispatch } from "react-redux";
import { Eye, FileText, Image, File } from "lucide-react";
import { useEffect, useState } from "react";
import PdfViewer from "../ViewPdf";
import NavigateBar from "./NavigateBar";
import * as questionUntil from "src/utils/question/questionUtils";
import useDebouncedEffect from "src/hooks/useDebouncedEffect";

const ImageView = () => {
    const { examImage } = useSelector((state) => state.addExam);

    return (
        <>
            {examImage ? (
                <div className="p-3">
                    <img src={URL.createObjectURL(examImage)} alt="exam" className="w-full object-contain" />
                </div>
            ) : (
                <div className="p-3 flex flex-col items-center">
                    <Image className="w-6 h-6 text-gray-400" />
                    <p className="text-sm text-gray-500"><PERSON><PERSON><PERSON> c<PERSON>nh</p>
                </div>
            )}
        </>
    )
}

const PdfView = () => {
    const { examFile } = useSelector((state) => state.addExam);

    return (
        <>
            {examFile ? (
                <div className="p-3">
                    <PdfViewer url={URL.createObjectURL(examFile)} />
                </div>
            ) : (
                <div className="p-3 flex flex-col items-center">
                    <File className="w-6 h-6 text-gray-400" />
                    <p className="text-sm text-gray-500">Chưa có file PDF</p>
                </div>
            )}
        </>
    )
}

const ExamView = () => {
    const { examData, examImage, examFile } = useSelector((state) => state.addExam);
    const [view, setView] = useState('image');

    return (
        <>
            {/* Compact Exam Header */}
            <div div className="mb-3 p-3" >
                <h3 className="text-sm font-bold text-gray-900 mb-2">
                    {examData.name || "Tên đề thi"}
                </h3>
                <div className="grid grid-cols-2 gap-1 text-xs text-gray-600">
                    <div><span className="font-medium">Kiểu:</span> {examData.typeOfExam || "Chưa chọn"}</div>
                    <div><span className="font-medium">Lớp:</span> {examData.class || "Chưa chọn"}</div>
                    <div><span className="font-medium">Năm:</span> {examData.year || "Chưa chọn"}</div>
                    {examData.chapter && <div><span className="font-medium">Chương:</span> {examData.chapter}</div>}
                    {examData.testDuration && <div><span className="font-medium">Thời gian:</span> {examData.testDuration}p</div>}
                    {examData.passRate && <div><span className="font-medium">Điểm đạt:</span> {examData.passRate}%</div>}
                </div>
                {
                    examData.solutionUrl && (
                        <div className="mt-2">
                            <p className="text-xs text-gray-600">Link lời giải: {examData.solutionUrl}</p>
                        </div>
                    )
                }
                {
                    examData.description && (
                        <div className="mt-2">
                            <p className="text-xs text-gray-600">{examData.description}</p>
                        </div>
                    )
                }
            </div>

            {/* Compact Status Badges */}
            <div className="p-3" >
                <div className="flex flex-wrap gap-1">
                    {examData.public && (
                        <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                            Công khai
                        </span>
                    )}
                    {examData.isClassroomExam && (
                        <span className="px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                            Đề thi lớp
                        </span>
                    )}
                    {!examData.public && !examData.isClassroomExam && (
                        <span className="px-2 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                            Riêng tư
                        </span>
                    )}
                </div>
            </div>
            <NavigateBar
                list={[{
                    id: 1,
                    name: 'Ảnh đề thi',
                    value: 'image'
                },
                {
                    id: 2,
                    name: 'File đề thi',
                    value: 'pdf'
                }
                ]}
                active={view}
                setActive={setView}
            />
            {view === 'image' && <ImageView />}
            {view === 'pdf' && <PdfView />}

        </>
    )
}

const QuestionView = () => {
    const dispatch = useDispatch();
    const { questionTNContent, questionDSContent, questionTLNContent, correctAnswerTN, correctAnswerDS, correctAnswerTLN, questions } = useSelector((state) => state.addExam);

    useDebouncedEffect(() => {
        const questionTN = questionUntil.splitContentTN(questionTNContent, correctAnswerTN, dispatch);
        console.log(questionTN)
    }, [questionTNContent, correctAnswerTN], 500)

    useDebouncedEffect(() => {
        const questionDS = questionUntil.splitContentDS(questionDSContent, correctAnswerDS, dispatch);
    }, [questionDSContent, correctAnswerDS], 500)

    useDebouncedEffect(() => {
        const questionTLN = questionUntil.splitContentTLN(questionTLNContent, correctAnswerTLN, dispatch);
    }, [questionTLNContent, correctAnswerTLN], 500)

    return (
        <>
            {/* Questions Preview Placeholder */}
            <div className=" p-3 ">
                <div className="flex items-center gap-1 mb-2">
                    <FileText className="w-3 h-3 text-gray-600" />
                    <h4 className="text-xs font-semibold text-gray-900">Câu hỏi (0)</h4>
                </div>
                <div className="text-center py-4 text-gray-500">
                    <FileText className="w-6 h-6 mx-auto mb-1 opacity-50" />
                    <p className="text-xs">Chưa có câu hỏi</p>
                </div>
            </div>
        </>
    )
}

const RightContent = () => {
    const { examData } = useSelector((state) => state.addExam);
    const [view, setView] = useState('exam');

    return (
        <div className="flex flex-col h-[calc(100vh_-_42px)] bg-gray-50">
            {/* Compact Preview Header */}
            <div className="flex items-center justify-start bg-white border-b border-gray-200 px-3 py-2 h-10">
                <div className="flex items-center gap-2">
                    <Eye className="w-3 h-3 text-gray-600" />
                    <h2 className="text-xs font-semibold text-gray-900">Xem trước</h2>
                </div>
            </div>

            {/* Scrollable Preview Content */}
            <div className="flex-1 overflow-y-auto p-3">
                <div className="bg-white rounded border border-gray-200 ">
                    <NavigateBar
                        list={[{
                            id: 1,
                            name: 'Đề thi',
                            value: 'exam'
                        },
                        {
                            id: 2,
                            name: 'Câu hỏi',
                            value: 'question'
                        }
                        ]}
                        active={view}
                        setActive={setView}
                    />
                    {view === 'exam' && <ExamView />}
                    {view === 'question' && <QuestionView />}
                </div>
            </div>
        </div>
    );
};


export default RightContent;