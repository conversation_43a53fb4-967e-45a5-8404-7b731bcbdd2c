{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\AiExamDetailAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, use } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport { fetchQuestionsByExamId } from \"src/features/examAI/examAISlice\";\nimport Header from \"src/components/PageAIexam/Header\";\nimport LeftContent from \"src/components/PageAIexam/LeftContent\";\nimport RightContent from \"src/components/PageAIexam/RightContent\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditExamAIPage = () => {\n  _s();\n  const {\n    examId\n  } = useParams();\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [editedText, setEditedText] = useState(\"\");\n  const {\n    exam,\n    questions\n  } = useSelector(state => state.examAI);\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    var _questions$;\n    if (!examId && questions.length > 0) return;\n    setSelectedQuestion(questions[0]);\n    setEditedText(((_questions$ = questions[0]) === null || _questions$ === void 0 ? void 0 : _questions$.content) || \"\");\n  }, [questions, examId]);\n  useEffect(() => {\n    if (examId) {\n      dispatch(fetchQuestionsByExamId(examId));\n      dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\n    }\n  }, [examId, dispatch]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" bg-gray-50 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \" bg-gray-50 flex flex-col \".concat(closeSidebar ? \"ml-[104px]\" : \"ml-64\"),\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        title: exam === null || exam === void 0 ? void 0 : exam.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \" h-screen bg-gray-50 flex mt-16 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(LeftContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(RightContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 9\n  }, this);\n};\n_s(EditExamAIPage, \"KM9EAqABqMnIg7uCzC20CVGL6uA=\", false, function () {\n  return [useParams, useSelector, useSelector, useDispatch];\n});\n_c = EditExamAIPage;\nexport default EditExamAIPage;\nvar _c;\n$RefreshReg$(_c, \"EditExamAIPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "use", "useSelector", "useDispatch", "useParams", "fetchQuestionsByExamId", "Header", "LeftContent", "RightContent", "fetchCodesByType", "AdminSidebar", "jsxDEV", "_jsxDEV", "EditExamAIPage", "_s", "examId", "selectedQuestion", "setSelectedQuestion", "editedText", "setEditedText", "exam", "questions", "state", "examAI", "closeSidebar", "sidebar", "dispatch", "_questions$", "length", "content", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "concat", "title", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/AiExamDetailAdmin.jsx"], "sourcesContent": ["import React, { useState, useEffect, use } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { fetchQuestionsByExamId } from \"src/features/examAI/examAISlice\";\r\nimport Header from \"src/components/PageAIexam/Header\";\r\nimport LeftContent from \"src/components/PageAIexam/LeftContent\";\r\nimport RightContent from \"src/components/PageAIexam/RightContent\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\r\n\r\nconst EditExamAIPage = () => {\r\n    const { examId } = useParams();\r\n    const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n    const [editedText, setEditedText] = useState(\"\");\r\n    const { exam, questions } = useSelector((state) => state.examAI);\r\n    const { closeSidebar } = useSelector((state) => state.sidebar);\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        if (!examId && questions.length > 0) return\r\n        setSelectedQuestion(questions[0]);\r\n        setEditedText(questions[0]?.content || \"\");\r\n    }, [questions, examId]);\r\n\r\n    useEffect(() => {\r\n        if (examId) {\r\n            dispatch(fetchQuestionsByExamId(examId))\r\n            dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\r\n        }\r\n    }, [examId, dispatch]);\r\n\r\n    return (\r\n        <div className=\" bg-gray-50 flex flex-col\">\r\n            <AdminSidebar />\r\n\r\n            <div className={` bg-gray-50 flex flex-col ${closeSidebar ? \"ml-[104px]\" : \"ml-64\"}`}>\r\n                <Header title={exam?.name} />\r\n                <div className=\" h-screen bg-gray-50 flex mt-16 overflow-hidden\">\r\n                    {/* LEFT: Danh sách câu hỏi */}\r\n                    <LeftContent />\r\n\r\n                    {/* RIGHT: Form chỉnh sửa */}\r\n                    <RightContent />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditExamAIPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,OAAO;AACvD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,OAAOC,MAAM,MAAM,kCAAkC;AACrD,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,YAAY,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAO,CAAC,GAAGX,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM;IAAEqB,IAAI;IAAEC;EAAU,CAAC,GAAGnB,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAChE,MAAM;IAAEC;EAAa,CAAC,GAAGtB,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACG,OAAO,CAAC;EAC9D,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9BH,SAAS,CAAC,MAAM;IAAA,IAAA2B,WAAA;IACZ,IAAI,CAACZ,MAAM,IAAIM,SAAS,CAACO,MAAM,GAAG,CAAC,EAAE;IACrCX,mBAAmB,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC;IACjCF,aAAa,CAAC,EAAAQ,WAAA,GAAAN,SAAS,CAAC,CAAC,CAAC,cAAAM,WAAA,uBAAZA,WAAA,CAAcE,OAAO,KAAI,EAAE,CAAC;EAC9C,CAAC,EAAE,CAACR,SAAS,EAAEN,MAAM,CAAC,CAAC;EAEvBf,SAAS,CAAC,MAAM;IACZ,IAAIe,MAAM,EAAE;MACRW,QAAQ,CAACrB,sBAAsB,CAACU,MAAM,CAAC,CAAC;MACxCW,QAAQ,CAACjB,gBAAgB,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;IACvF;EACJ,CAAC,EAAE,CAACM,MAAM,EAAEW,QAAQ,CAAC,CAAC;EAEtB,oBACId,OAAA;IAAKkB,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACtCnB,OAAA,CAACF,YAAY;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhBvB,OAAA;MAAKkB,SAAS,+BAAAM,MAAA,CAA+BZ,YAAY,GAAG,YAAY,GAAG,OAAO,CAAG;MAAAO,QAAA,gBACjFnB,OAAA,CAACN,MAAM;QAAC+B,KAAK,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB;MAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7BvB,OAAA;QAAKkB,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE5DnB,OAAA,CAACL,WAAW;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGfvB,OAAA,CAACJ,YAAY;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACrB,EAAA,CArCID,cAAc;EAAA,QACGT,SAAS,EAGAF,WAAW,EACdA,WAAW,EACnBC,WAAW;AAAA;AAAAoC,EAAA,GAN1B1B,cAAc;AAuCpB,eAAeA,cAAc;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}