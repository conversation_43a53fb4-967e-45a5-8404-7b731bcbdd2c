'use strict'
/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('studentExamAttempt', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      studentId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'user',
          key: 'id'
        },
        onDelete: "CASCADE"
      },
      examId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'exam',
          key: 'id'
        },
        onDelete: "CASCADE"
      },
      startTime: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      endTime: {
        type: Sequelize.DATE
      },
      score: {
        type: Sequelize.FLOAT
      },
    })
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('studentExamAttempt')
  }
}