import UserLayout from "../../../layouts/UserLayout"
import { useEffect, useState, useRef } from "react"
import { useDispatch, useSelector } from "react-redux"
// import { MoveRight, MoveLeft, Calendar, BookOpen, History, FileText, GraduationCap, ChevronLeft } from "lucide-react"
import { motion } from "framer-motion"
import ClassImage from "../../../components/image/ClassImage"
import { fetchClassesOverview } from "../../../features/class/classSlice"
import { getUncompletedLearningItem, getLearningItemWeekend } from "../../../features/learningItem/learningItemSlice"
import { fetchAttemptsByUser } from "../../../features/attempt/attemptSlice"
import { fetchSavedExam } from "../../../features/exam/examSlice"
import { fetchCodesByType } from "../../../features/code/codeSlice"
import { useNavigate } from "react-router-dom"
import Schedule from "../../../components/Schedule"
import { formatDate } from "../../../utils/formatters"
import LoadingSpinner from "../../../components/loading/LoadingSpinner"
import { setCurrentPage } from "../../../features/filter/filterSlice"
import Pagination from "../../../components/Pagination"
import NoDataFound from "../../../assets/images/error-file.png"
import ExamCard from "../../../components/card/ExamCard"
import { resetFilters } from "../../../features/filter/filterSlice";
import { Calendar, BookOpen, FileText, History, Home, ExternalLink, Loader, GraduationCap, Bell, X, CreditCard } from "lucide-react"
import {
    fetchNotifications,
    markAsRead as markNotificationAsRead,
    resetNotifications,
    resetDidInit as resetNotificationDidInit,
    fetchUnreadCount
} from "../../../features/notification/notificationSlice"

import { setCurrentPageLT } from "../../../features/learningItem/learningItemSlice";

const ButtonSidebar = ({ choice, onClick, value, text, icon, isOpen, count = null, scrollToRef }) => {
    const isActive = choice === value;
    const Icon = icon; // Lưu trữ component icon

    const handleClick = () => {
        onClick?.();
        if (scrollToRef?.current) {
            const offset = 200;
            const elementTop = scrollToRef.current.getBoundingClientRect().top + window.pageYOffset;

            window.scrollTo({
                top: elementTop - offset,
                behavior: 'smooth'
            });
        }
    };

    return (
        <button
            onClick={handleClick}
            className={`cursor-pointer self-stretch p-2 ${isActive
                ? 'bg-sky-100 text-sky-700 font-medium'
                : 'hover:bg-gray-100 text-gray-700'
                } rounded-lg inline-flex w-full justify-start items-center gap-3 transition-colors`}
        >
            <div className={`flex justify-center items-center p-2 ${isActive ? 'bg-sky-200 text-sky-700' : 'bg-gray-100 text-gray-600'} rounded-full transition-colors`}>
                <Icon size={16} />
            </div>
            <motion.div
                initial={false}
                animate={{
                    opacity: isOpen ? 1 : 0,
                    width: isOpen ? '100%' : 0,
                }}
                transition={{
                    duration: 0.2,
                    ease: [0.25, 0.1, 0.25, 1.0],
                }}
                className="flex flex-row w-full items-center justify-between gap-2"
            >
                <p className="text-sm font-medium text-start truncate w-full">{text}</p>
                {count !== null && (
                    <div className={`px-2 py-1 text-xs rounded-full ${isActive ? 'bg-sky-200 text-sky-700' : 'bg-gray-200 text-gray-700'} font-medium min-w-[1.5rem] text-center`}>
                        {count}
                    </div>
                )}
            </motion.div>
        </button>
    );
};


const ClassCard = ({ name, classData, codes, onClick }) => {
    // Helper function to format time
    const formatTime = (time) => {
        if (!time) return '';
        return time.substring(0, 5); // HH:MM format
    };

    // Helper function to get day of week description
    const getDayOfWeekDescription = (dayCode) => {
        if (!dayCode) return '';
        return codes['dow']?.find((code) => code.code === dayCode)?.description || dayCode;
    };

    // Build schedule display
    const buildScheduleDisplay = () => {
        const schedules = [];

        // First schedule
        if (classData?.dayOfWeek1 && classData?.startTime1) {
            const day1 = getDayOfWeekDescription(classData.dayOfWeek1);
            const time1 = `${formatTime(classData.startTime1)}${classData.endTime1 ? ` - ${formatTime(classData.endTime1)}` : ''}`;
            schedules.push(`${day1} ${time1}`);
        }

        // Second schedule
        if (classData?.dayOfWeek2 && classData?.startTime2) {
            const day2 = getDayOfWeekDescription(classData.dayOfWeek2);
            const time2 = `${formatTime(classData.startTime2)}${classData.endTime2 ? ` - ${formatTime(classData.endTime2)}` : ''}`;
            schedules.push(`${day2} ${time2}`);
        }

        // Fallback to old format if new format not available
        if (schedules.length === 0 && classData?.dayOfWeek && classData?.studyTime) {
            const oldDay = getDayOfWeekDescription(classData.dayOfWeek);
            schedules.push(`${oldDay} ${classData.studyTime}`);
        }

        return schedules;
    };

    const schedules = buildScheduleDisplay();

    return (
        <div
            onClick={onClick}
            className="flex flex-col p-3 lg:p-4 gap-3 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-cyan-300 cursor-pointer transition-all duration-200 group min-w-[240px] sm:min-w-[280px] flex-1 max-w-[320px]">

            {/* Class Image */}
            <div className="flex justify-center">
                <ClassImage name={name} className="h-20 sm:h-24 lg:h-28 w-28 sm:w-32 lg:w-36 group-hover:scale-105 transition-transform duration-200" />
            </div>

            {/* Class Name */}
            <div className="text-center">
                <h3 className="text-zinc-900 font-semibold font-bevietnam text-sm lg:text-base truncate group-hover:text-cyan-700 transition-colors">
                    {name}
                </h3>
                <p className="text-xs text-gray-500 mt-1">Lớp học</p>
            </div>

            {/* Schedule Information */}
            <div className="space-y-2">
                {schedules.length > 0 ? (
                    schedules.map((schedule, index) => (
                        <div key={index} className="flex items-center justify-center gap-2 text-slate-700">
                            <Calendar className="w-3 h-3 lg:w-4 lg:h-4 text-cyan-600 flex-shrink-0" />
                            <p className="text-xs lg:text-sm font-medium text-center">{schedule}</p>
                        </div>
                    ))
                ) : (
                    <div className="flex items-center justify-center gap-2 text-slate-500">
                        <Calendar className="w-3 h-3 lg:w-4 lg:h-4 flex-shrink-0" />
                        <p className="text-xs lg:text-sm">Chưa có lịch học</p>
                    </div>
                )}
            </div>

            {/* Additional Info */}
            <div className="pt-2 border-t border-gray-100">
                <div className="flex items-center justify-center gap-2 text-gray-500">
                    <GraduationCap className="w-3 h-3" />
                    <span className="text-xs">Năm học: {classData?.academicYear || 'N/A'}</span>
                </div>
            </div>
        </div>
    )
}


const OverViewPage = () => {
    const navigate = useNavigate()
    const dispatch = useDispatch()

    const [choice, setChoice] = useState(0)
    const { currentPage, totalItems } = useSelector((state) => state.filter);
    const { classesOverview: classes, loadingClass } = useSelector((state) => state.classes);
    const { codes } = useSelector((state) => state.codes);
    const { attempts, loadingAttempt } = useSelector((state) => state.attempts);
    const { exams, loadingExam } = useSelector((state) => state.exams);
    const { notifications, unreadCount, loading: loadingNotifications } = useSelector((state) => state.notifications);

    const { loading: loadingLearningItem, learningItems, pagination, learningItemsWeekend } = useSelector((state) => state.learningItems);

    const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên

    useEffect(() => {
        if (!didInit) {
            dispatch(resetFilters());
            setDidInit(true);
        }
    }, [dispatch, didInit]);

    const [savedExamPage, setSavedExamPage] = useState(1);
    const examsPerPage = 6;

    // Filter states
    const [filters, setFilters] = useState({
        chapter: '',
        typeOfExam: '',
        class: '',
        name: ''
    });

    // Apply filters to exams
    let filteredExams = []
    if (exams?.length > 0 && exams[0]?.exam) {
        filteredExams = exams?.filter(exam => {
            // console.log(exam.exam, filters.name)

            return (
                // Filter by chapter
                (filters.chapter === '' ||
                    exam.exam?.chapter === filters.chapter) &&
                // Filter by exam type
                (filters.typeOfExam === '' ||
                    exam.exam?.typeOfExam === filters.typeOfExam) &&
                // Filter by class
                (filters.class === '' ||
                    exam.exam?.class === filters.class) &&
                // Filter by name
                (filters.name === '' ||
                    (exam.exam?.name && exam.exam?.name?.toLowerCase().includes(filters.name.toLowerCase())))
            );
        });
    }


    const paginatedExams = filteredExams?.slice(
        (savedExamPage - 1) * examsPerPage,
        savedExamPage * examsPerPage
    );

    // Get unique values for dropdowns
    const getUniqueValues = (field, codeField) => {
        if (!exams || exams.length === 0 || !exams[0]?.exam) return [];
        const values = exams
            .map(exam => exam.exam?.[field])
            .filter(value => value); // Remove null/undefined

        const valuesDescription = values.map(value => {
            const code = codes[codeField]?.find(c => c.code === value);
            return code ? code.description : value;
        });

        return [...new Set(valuesDescription)].sort();
    };

    const uniqueChapters = getUniqueValues('chapter', 'chapter');
    const uniqueExamTypes = getUniqueValues('typeOfExam', 'exam type');
    const uniqueClasses = getUniqueValues('class', 'grade');

    // Handle filter changes
    const handleFilterChange = (e) => {
        const { name, value } = e.target;

        const codeField = name === 'chapter' ? 'chapter' : name === 'typeOfExam' ? 'exam type' : 'grade';
        setSavedExamPage(1);

        if (name === 'name' || value == '') {
            setFilters(prev => ({
                ...prev,
                [name]: value
            }));
            return;
        }

        setFilters(prev => ({
            ...prev,
            [name]: codes[codeField]?.find(c => c.description === value)?.code
        }));

        // setFilters(prev => ({
        //     ...prev,
        //     [name]: value
        // }));
    };

    const sectionRefs = {
        class: useRef(null),
        schedule: useRef(null),
        exercise: useRef(null),
        history: useRef(null),
        exams: useRef(null),
    };

    const handlePageChange = (page) => {
        dispatch(setCurrentPage(page));
    };

    const handlePageChangeLT = (page) => {
        // console.log('Changing page to:', page);
        dispatch(setCurrentPageLT(page));
    };

    useEffect(() => {
        dispatch(fetchClassesOverview())
        dispatch(fetchSavedExam())
        dispatch(fetchCodesByType(['duration', 'dow', 'chapter', 'exam type', 'grade']))
        dispatch(getLearningItemWeekend({ startOfWeek: null, endOfWeek: null }))
        // Fetch notifications for right panel (limit to 5 recent notifications)
        dispatch(resetNotificationDidInit());
        dispatch(resetNotifications());
        dispatch(fetchNotifications({ limit: 5, offset: 0 }));
        dispatch(fetchUnreadCount());
    }, [dispatch])

    useEffect(() => {
        // console.log('Fetching uncompleted learning items for page:', pagination.page, 'with limit:', pagination.limit);
        dispatch(getUncompletedLearningItem({ page: pagination.page, limit: pagination.limit }))
    }, [dispatch, pagination.page, pagination.limit])

    useEffect(() => {
        if (didInit) dispatch(fetchAttemptsByUser({ currentPage }))
    }, [dispatch, currentPage, didInit])

    // Get icon based on notification type (same as NotificationsPage)
    const getNotificationIcon = (type) => {
        switch (type) {
            case 'exam':
                return (
                    <div className="p-2 bg-blue-100 rounded-full">
                        <svg className="w-4 h-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                            <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                            <path d="M9 14l2 2 4-4"></path>
                        </svg>
                    </div>
                );
            case 'reminder':
                return (
                    <div className="p-2 bg-yellow-100 rounded-full">
                        <svg className="w-4 h-4 text-yellow-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                            <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                        </svg>
                    </div>
                );
            case 'result':
                return (
                    <div className="p-2 bg-green-100 rounded-full">
                        <svg className="w-4 h-4 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                    </div>
                );
            case 'tuition':
                return (
                    <div className="p-2 bg-yellow-100 rounded-full">
                        <CreditCard className="w-4 h-4 text-yellow-600" />
                    </div>
                );
            default:
                return (
                    <div className="p-2 bg-gray-100 rounded-full">
                        <svg className="w-4 h-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                    </div>
                );
        }
    };

    // Handle notification click
    const handleNotificationClick = (notification) => {
        dispatch(markNotificationAsRead([notification.id]));
        if (notification.link) {
            navigate(notification.link);
        }
    };


    // Sử dụng các icon Lucide trực tiếp trong các component

    return (
        <UserLayout>
            <div className="flex flex-col lg:flex-row w-full bg-gray-50 min-h-screen">
                {/* Mobile Navigation */}
                {/* Left Sidebar - Desktop */}
                <div className="sticky top-20 py-4 px-2 w-[280px] xl:w-[300px] h-[90vh] overflow-y-auto hide-scrollbar hidden lg:block">
                    <div className="inline-flex w-full flex-row justify-center items-center">
                        <div className="text-center truncate text-zinc-900 text-xl font-semibold font-bevietnam">
                            Quản lý học tập
                        </div>
                    </div>
                    <hr className="w-full h-[1px] bg-neutral-200 my-4" />
                    <div className="self-stretch text-sm w-full rounded-md flex flex-col justify-start items-start gap-1">
                        <ButtonSidebar choice={choice} onClick={() => setChoice(0)} value={0} text="Lớp học hôm nay" icon={GraduationCap} isOpen={true} count={classes?.length} scrollToRef={sectionRefs.class} />
                        <ButtonSidebar choice={choice} onClick={() => setChoice(1)} value={1} text="Lịch học của bạn" icon={Calendar} isOpen={true} scrollToRef={sectionRefs.schedule} />
                        <ButtonSidebar choice={choice} onClick={() => setChoice(2)} value={2} text="Bài tập" icon={BookOpen} isOpen={true} count={pagination.total} scrollToRef={sectionRefs.exercise} />
                        <ButtonSidebar choice={choice} onClick={() => setChoice(3)} value={3} text="Lịch sử làm bài" icon={History} isOpen={true} count={totalItems} scrollToRef={sectionRefs.history} />
                        <ButtonSidebar choice={choice} onClick={() => setChoice(4)} value={4} text="Đề thi của bạn" icon={FileText} isOpen={true} count={exams.length} scrollToRef={sectionRefs.exams} />
                    </div>
                </div>

                {/* Main Content */}
                <div className="flex-1 transition-all duration-300 lg:max-w-none">
                    <div className="container px-4 py-4 lg:py-8 max-w-none lg:max-w-4xl mx-auto">
                        {/* Page Header */}
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 lg:mb-8 gap-4">
                            <div className="flex-1">
                                <h1 className="text-2xl lg:text-3xl font-bold text-gray-800 flex items-center gap-2 lg:gap-3">
                                    <div className="w-8 h-8 lg:w-10 lg:h-10 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl flex items-center justify-center">
                                        <Home className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
                                    </div>
                                    Trang chủ
                                </h1>
                                <p className="text-gray-600 mt-1 lg:mt-2 text-sm lg:text-base">Tổng quan về hoạt động học tập của bạn</p>
                            </div>
                            <div className="flex items-center gap-2 px-3 lg:px-4 py-2 bg-sky-50 border border-sky-200 rounded-lg self-start sm:self-auto">
                                <div className="w-2 h-2 bg-sky-500 rounded-full animate-pulse"></div>
                                <span className="text-sky-700 text-sm font-medium">Đang hoạt động</span>
                            </div>
                        </div>

                        {/* Classes Section */}
                        <div ref={sectionRefs.class} className="mb-6 lg:mb-8">
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 lg:mb-6 gap-3">
                                <h2 className="text-xl lg:text-2xl font-bold text-gray-800 flex items-center gap-2">
                                    <GraduationCap className="text-sky-600 w-5 h-5 lg:w-6 lg:h-6" />
                                    Lớp học hôm nay
                                    {classes?.length > 0 && (
                                        <span className="ml-2 bg-sky-500 text-white text-xs lg:text-sm font-bold px-2 py-1 rounded-full">
                                            {classes.length}
                                        </span>
                                    )}
                                </h2>
                                {classes?.length > 0 && (
                                    <button
                                        onClick={() => navigate('/class')}
                                        className="flex items-center gap-1 text-sm text-sky-600 hover:text-sky-700 px-3 py-1.5 bg-sky-50 hover:bg-sky-100 rounded-md transition-colors self-start sm:self-auto"
                                    >
                                        <ExternalLink size={14} />
                                        <span className="hidden sm:inline">Xem tất cả</span>
                                        <span className="sm:hidden">Tất cả</span>
                                    </button>
                                )}
                            </div>

                            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                                {loadingClass ? (
                                    <div className="p-6 lg:p-8 text-center text-gray-500">
                                        <Loader size={32} className="mx-auto mb-4 text-gray-300 animate-spin lg:w-10 lg:h-10" />
                                        <p className="text-sm lg:text-base">Đang tải danh sách lớp học...</p>
                                    </div>
                                ) : classes && classes.length > 0 ? (
                                    <div className="p-4 lg:p-6">
                                        <div className="flex flex-col sm:flex-row gap-4 lg:gap-6 overflow-x-auto">
                                            {classes.map((cls) => (
                                                <ClassCard
                                                    key={cls.class?.id}
                                                    name={cls.class?.name}
                                                    classData={cls.class}
                                                    codes={codes}
                                                    onClick={() => navigate(`/class/${cls.class?.class_code}`)}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="p-6 lg:p-8 text-center text-gray-500">
                                        <GraduationCap size={32} className="mx-auto mb-4 text-gray-300 lg:w-10 lg:h-10" />
                                        <p className="text-sm lg:text-base">Chưa có lớp học nào.</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Schedule Section */}
                        <div ref={sectionRefs.schedule} className="mb-8">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                                    <Calendar className="text-sky-600" />
                                    Lịch học
                                </h2>
                                <button
                                    onClick={() => navigate('/overview/schedule')}
                                    className="flex items-center gap-1 text-sm text-sky-600 hover:text-sky-700 px-3 py-1.5 bg-sky-50 hover:bg-sky-100 rounded-md transition-colors self-start sm:self-auto"
                                >
                                    <ExternalLink size={14} />
                                    <span className="hidden sm:inline">Xem tất cả</span>
                                    <span className="sm:hidden">Tất cả</span>
                                </button>
                            </div>

                            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                                {loadingClass ? (
                                    <div className="p-8 text-center text-gray-500">
                                        <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
                                        <p>Đang tải lịch học...</p>
                                    </div>
                                ) : classes && classes.length > 0 && classes[0].class ? (
                                    <div className="p-6">
                                        <Schedule classes={classes?.map((cls) => cls.class)} learningItems={learningItemsWeekend} />
                                    </div>
                                ) : (
                                    <div className="p-8 text-center text-gray-500">
                                        <Calendar size={40} className="mx-auto mb-4 text-gray-300" />
                                        <p>Chưa có lịch học nào.</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Learning Items Section */}
                        <div ref={sectionRefs.exercise} className="mb-8">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                                    <BookOpen className="text-sky-600" />
                                    Bài tập chưa hoàn thành
                                    {pagination.total > 0 && (
                                        <span className="ml-2 bg-orange-500 text-white text-sm font-bold px-2 py-1 rounded-full">
                                            {pagination.total}
                                        </span>
                                    )}
                                </h2>
                            </div>

                            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                                {loadingLearningItem ? (
                                    <div className="p-8 text-center text-gray-500">
                                        <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
                                        <p>Đang tải bài tập...</p>
                                    </div>
                                ) : learningItems && learningItems.length > 0 ? (
                                    <>
                                        <div className="p-4 text-center text-sm text-gray-500 border-b border-gray-100">
                                            Hiển thị {learningItems.length} trong tổng số {pagination?.total || 0} bài tập chưa hoàn thành
                                        </div>
                                        <div className="divide-y divide-gray-100">
                                            {learningItems.map((learningItem, index) => (
                                                <div
                                                    key={learningItem.learningItem?.id}
                                                    onClick={() => navigate(`/class/${learningItem.learningItem?.lesson?.class?.class_code}/learning`)}
                                                    className="p-4 hover:bg-gray-50 transition-colors cursor-pointer">
                                                    <div className="flex gap-4">
                                                        <div className="p-3 bg-orange-100 rounded-full">
                                                            <BookOpen className="w-6 h-6 text-orange-600" />
                                                        </div>
                                                        <div className="flex-1">
                                                            <div className="flex justify-between items-start">
                                                                <h4 className="text-base font-semibold text-gray-800">
                                                                    {learningItem.learningItem?.name} - {learningItem.learningItem?.lesson?.name}
                                                                </h4>
                                                                <span className="text-sm text-gray-500">{formatDate(learningItem.learningItem?.createdAt)}</span>
                                                            </div>
                                                            <p className="text-gray-600 mt-1">Lớp: {learningItem.learningItem?.lesson?.class?.name}</p>
                                                            <div className="flex justify-between items-center mt-3">
                                                                <button
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        navigate(`/class/${learningItem.learningItem?.lesson?.class?.class_code}/learning`);
                                                                    }}
                                                                    className="text-sm text-sky-600 hover:text-sky-700 flex items-center"
                                                                >
                                                                    <span>Xem chi tiết</span>
                                                                    <ExternalLink size={14} className="ml-1" />
                                                                </button>
                                                                <div className="flex items-center gap-2 px-3 py-1 bg-orange-50 border border-orange-200 rounded-full">
                                                                    <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                                                                    <span className="text-orange-700 text-sm font-medium">Chưa hoàn thành</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                        <div className="p-4 text-center">
                                            <Pagination
                                                currentPage={pagination?.page || 1}
                                                totalItems={pagination?.total || 0}
                                                limit={10}
                                                onPageChange={handlePageChangeLT}
                                            />
                                        </div>
                                    </>
                                ) : (
                                    <div className="p-8 text-center text-gray-500">
                                        <BookOpen size={40} className="mx-auto mb-4 text-gray-300" />
                                        <p>Không có bài tập chưa hoàn thành.</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* History Section */}
                        <div ref={sectionRefs.history} className="mb-8">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                                    <History className="text-sky-600" />
                                    Lịch sử làm bài
                                    {attempts?.length > 0 && (
                                        <span className="ml-2 bg-sky-500 text-white text-sm font-bold px-2 py-1 rounded-full">
                                            {totalItems}
                                        </span>
                                    )}
                                </h2>
                            </div>

                            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                                {loadingAttempt ? (
                                    <div className="p-8 text-center text-gray-500">
                                        <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
                                        <p>Đang tải lịch sử làm bài...</p>
                                    </div>
                                ) : attempts && attempts.length > 0 ? (
                                    <>
                                        <div className="p-4 text-center text-sm text-gray-500 border-b border-gray-100">
                                            Hiển thị {attempts.length} trong tổng số {totalItems} lần làm bài
                                        </div>
                                        <div className="divide-y divide-gray-100">
                                            {attempts.map((attempt, index) => (
                                                <div
                                                    key={attempt?.id}
                                                    onClick={() => navigate(`/practice/exam/attempt/${attempt.id}/score`)}
                                                    className="p-4 hover:bg-gray-50 transition-colors cursor-pointer">
                                                    <div className="flex gap-4">
                                                        <div className="p-3 bg-sky-100 rounded-full">
                                                            <FileText className="w-6 h-6 text-sky-600" />
                                                        </div>
                                                        <div className="flex-1">
                                                            <div className="flex justify-between items-start">
                                                                <h4 className="text-base font-semibold text-gray-800">
                                                                    {attempt?.exam?.name}
                                                                </h4>
                                                                <span className="text-sm text-gray-500">{formatDate(attempt?.endTime)}</span>
                                                            </div>
                                                            <p className="text-gray-600 mt-1">Thời gian: {attempt?.duration}</p>
                                                            <div className="flex justify-between items-center mt-3">
                                                                <button
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        navigate(`/practice/exam/attempt/${attempt.id}/score`);
                                                                    }}
                                                                    className="text-sm text-sky-600 hover:text-sky-700 flex items-center"
                                                                >
                                                                    <span>Xem chi tiết</span>
                                                                    <ExternalLink size={14} className="ml-1" />
                                                                </button>
                                                                <div className="flex items-center gap-2 px-3 py-1 bg-sky-50 border border-sky-200 rounded-full">
                                                                    <div className="w-2 h-2 bg-sky-500 rounded-full"></div>
                                                                    <span className="text-sky-700 text-sm font-medium">
                                                                        {(attempt?.score || attempt?.score === 0) ? attempt?.score : "N/A"} điểm
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                        <div className="p-4 text-center">
                                            <Pagination
                                                currentPage={currentPage}
                                                totalItems={totalItems}
                                                limit={10}
                                                onPageChange={handlePageChange}
                                            />
                                        </div>
                                    </>
                                ) : (
                                    <div className="p-8 text-center text-gray-500">
                                        <History size={40} className="mx-auto mb-4 text-gray-300" />
                                        <p>Chưa có lịch sử làm bài nào.</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Saved Exams Section */}
                        <div ref={sectionRefs.exams} className="mb-8">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                                    <FileText className="text-sky-600" />
                                    Đề thi đã lưu
                                    {exams?.length > 0 && (
                                        <span className="ml-2 bg-green-500 text-white text-sm font-bold px-2 py-1 rounded-full">
                                            {exams.length}
                                        </span>
                                    )}
                                </h2>
                            </div>

                            {/* Filter section */}
                            <div className="bg-white p-3 lg:p-4 rounded-lg shadow-sm border border-gray-200 mb-4">
                                <h3 className="text-base lg:text-lg font-medium mb-3">Bộ lọc</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-3 lg:gap-4">
                                    <div className="flex flex-col">
                                        <label htmlFor="name" className="text-sm font-medium text-gray-700 mb-1">Tên đề thi</label>
                                        <input
                                            type="text"
                                            id="name"
                                            name="name"
                                            value={filters.name}
                                            onChange={handleFilterChange}
                                            placeholder="Tìm theo tên..."
                                            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                                        />
                                    </div>
                                    <div className="flex flex-col">
                                        <label htmlFor="chapter" className="text-sm font-medium text-gray-700 mb-1">Chương</label>
                                        <select
                                            id="chapter"
                                            name="chapter"
                                            value={codes['chapter']?.find(c => c.code === filters.chapter)?.description || ''}
                                            onChange={handleFilterChange}
                                            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                                        >
                                            <option value="">Tất cả chương</option>
                                            {uniqueChapters.map(chapter => (
                                                <option key={chapter} value={chapter}>{chapter}</option>
                                            ))}
                                        </select>
                                    </div>
                                    <div className="flex flex-col">
                                        <label htmlFor="typeOfExam" className="text-sm font-medium text-gray-700 mb-1">Loại đề</label>
                                        <select
                                            id="typeOfExam"
                                            name="typeOfExam"
                                            value={codes['exam type']?.find(c => c.code === filters.typeOfExam)?.description || ''}
                                            onChange={handleFilterChange}
                                            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                                        >
                                            <option value="">Tất cả loại đề</option>
                                            {uniqueExamTypes.map(type => (
                                                <option key={type} value={type}>{type}</option>
                                            ))}
                                        </select>
                                    </div>
                                    <div className="flex flex-col">
                                        <label htmlFor="class" className="text-sm font-medium text-gray-700 mb-1">Lớp</label>
                                        <select
                                            id="class"
                                            name="class"
                                            value={codes['grade']?.find(c => c.code === filters.class)?.description || ''}
                                            onChange={handleFilterChange}
                                            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                                        >
                                            <option value="">Tất cả lớp</option>
                                            {uniqueClasses.map(cls => (
                                                <option key={cls} value={cls}>{cls}</option>
                                            ))}
                                        </select>
                                    </div>
                                </div>
                                <div className="flex justify-end mt-3 lg:mt-4">
                                    <button
                                        onClick={() => {
                                            setFilters({
                                                chapter: '',
                                                typeOfExam: '',
                                                class: '',
                                                name: ''
                                            });
                                            setSavedExamPage(1);
                                        }}
                                        className="px-3 lg:px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md transition-colors duration-200 text-sm"
                                    >
                                        Xóa bộ lọc
                                    </button>
                                </div>
                            </div>

                            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                                {loadingExam ? (
                                    <div className="p-8 text-center text-gray-500">
                                        <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
                                        <p>Đang tải đề thi đã lưu...</p>
                                    </div>
                                ) : !exams || exams.length === 0 ? (
                                    <div className="p-8 text-center text-gray-500">
                                        <FileText size={40} className="mx-auto mb-4 text-gray-300" />
                                        <p>Không có đề thi đã lưu nào.</p>
                                    </div>
                                ) : paginatedExams.length === 0 ? (
                                    <div className="p-8 text-center text-gray-500">
                                        <FileText size={40} className="mx-auto mb-4 text-gray-300" />
                                        <p>Không có đề thi nào phù hợp với bộ lọc.</p>
                                    </div>
                                ) : (
                                    <>
                                        <div className="p-4 text-center text-sm text-gray-500 border-b border-gray-100">
                                            Hiển thị {paginatedExams.length} trong tổng số {filteredExams?.length || 0} đề thi
                                        </div>
                                        <div className="p-6 space-y-4">
                                            {paginatedExams.map((examData) => (
                                                <ExamCard
                                                    key={examData.exam?.id}
                                                    exam={{
                                                        ...examData.exam,
                                                        id: examData.exam?.id,
                                                        isDone: examData.isDone,
                                                        isSave: true
                                                    }}
                                                    codes={codes}
                                                    horizontal={true}
                                                />
                                            ))}
                                        </div>
                                        <div className="p-4 text-center border-t border-gray-100">
                                            <Pagination
                                                currentPage={savedExamPage}
                                                totalItems={filteredExams?.length || 0}
                                                limit={examsPerPage}
                                                onPageChange={setSavedExamPage}
                                            />
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Notifications Panel - Desktop */}
                <div className="sticky top-20 py-4 px-2 w-[280px] xl:w-[300px] h-[90vh] overflow-y-auto hide-scrollbar hidden xl:block">
                    {/* Header */}
                    <div className="inline-flex w-full flex-row justify-center items-center mb-4">
                        <div className="text-center truncate text-zinc-900 text-lg xl:text-xl font-semibold font-bevietnam flex items-center gap-2">
                            Thông báo
                            {unreadCount > 0 && (
                                <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                    {unreadCount}
                                </span>
                            )}
                        </div>
                    </div>
                    <hr className="w-full h-[1px] bg-neutral-200 mb-4" />

                    {/* Notifications Content */}
                    <div className="space-y-2 xl:space-y-3">
                        {loadingNotifications ? (
                            <div className="p-3 xl:p-4 text-center text-gray-500">
                                <Loader size={20} className="mx-auto mb-2 text-gray-300 animate-spin xl:w-6 xl:h-6" />
                                <p className="text-xs">Đang tải thông báo...</p>
                            </div>
                        ) : notifications && notifications.length > 0 ? (
                            notifications.map((notification) => (
                                <div
                                    key={notification.id}
                                    onClick={() => handleNotificationClick(notification)}
                                    className={`p-2 xl:p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer ${!notification.isRead ? 'bg-sky-50 border border-sky-200' : 'bg-white border border-gray-200'
                                        }`}
                                >
                                    <div className="flex items-start gap-2 xl:gap-3">
                                        <div className="flex-shrink-0">
                                            {getNotificationIcon(notification.type)}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h4 className={`text-xs xl:text-sm font-semibold truncate ${!notification.isRead ? 'text-sky-700' : 'text-gray-800'
                                                }`}>
                                                {notification.title}
                                            </h4>
                                            <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                                {notification.message}
                                            </p>
                                            <span className="text-xs text-gray-500">{notification.time}</span>
                                        </div>
                                        {!notification.isRead && (
                                            <div className="w-2 h-2 bg-sky-500 rounded-full mt-1 flex-shrink-0"></div>
                                        )}
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="p-3 xl:p-4 text-center text-gray-500">
                                <Bell size={20} className="mx-auto mb-2 text-gray-300 xl:w-6 xl:h-6" />
                                <p className="text-xs">Không có thông báo nào.</p>
                            </div>
                        )}
                    </div>

                    {/* View All Button */}
                    <div className="mt-4 xl:mt-6 pt-3 xl:pt-4 border-t border-gray-200">
                        <button
                            onClick={() => navigate('/notifications')}
                            className="w-full px-3 xl:px-4 py-2 text-sm text-sky-600 hover:text-sky-700 hover:bg-sky-50 rounded-md transition-colors flex items-center justify-center gap-2"
                        >
                            <span className="text-xs xl:text-sm">Xem tất cả thông báo</span>
                            <ExternalLink size={12} className="xl:w-3.5 xl:h-3.5" />
                        </button>
                    </div>
                </div>

                {/* Mobile Bottom Navigation */}
                <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 flex justify-between items-center p-1 md:hidden z-40 shadow-md">
                    <button
                        onClick={() => {
                            setChoice(0);
                            if (sectionRefs.class?.current) {
                                const offset = 100;
                                const elementTop = sectionRefs.class.current.getBoundingClientRect().top + window.pageYOffset;
                                window.scrollTo({
                                    top: elementTop - offset,
                                    behavior: 'smooth'
                                });
                            }
                        }}
                        className={`flex flex-col items-center justify-center p-2 flex-1 ${choice === 0 ? 'bg-sky-100 text-sky-700' : 'text-gray-600 hover:bg-gray-100'} rounded-md transition-colors`}
                    >
                        <GraduationCap size={20} className={choice === 0 ? 'text-sky-700' : 'text-gray-600'} />
                        <span className="text-xs mt-1">Lớp học</span>
                    </button>

                    <button
                        onClick={() => {
                            setChoice(1);
                            if (sectionRefs.schedule?.current) {
                                const offset = 100;
                                const elementTop = sectionRefs.schedule.current.getBoundingClientRect().top + window.pageYOffset;
                                window.scrollTo({
                                    top: elementTop - offset,
                                    behavior: 'smooth'
                                });
                            }
                        }}
                        className={`flex flex-col items-center justify-center p-2 flex-1 ${choice === 1 ? 'bg-sky-100 text-sky-700' : 'text-gray-600 hover:bg-gray-100'} rounded-md transition-colors`}
                    >
                        <Calendar size={20} className={choice === 1 ? 'text-sky-700' : 'text-gray-600'} />
                        <span className="text-xs mt-1">Lịch học</span>
                    </button>

                    <button
                        onClick={() => {
                            setChoice(2);
                            if (sectionRefs.exercise?.current) {
                                const offset = 100;
                                const elementTop = sectionRefs.exercise.current.getBoundingClientRect().top + window.pageYOffset;
                                window.scrollTo({
                                    top: elementTop - offset,
                                    behavior: 'smooth'
                                });
                            }
                        }}
                        className={`flex flex-col items-center justify-center p-2 flex-1 ${choice === 2 ? 'bg-sky-100 text-sky-700' : 'text-gray-600 hover:bg-gray-100'} rounded-md transition-colors`}
                    >
                        <BookOpen size={20} className={choice === 2 ? 'text-sky-700' : 'text-gray-600'} />
                        <span className="text-xs mt-1">Bài tập</span>
                    </button>

                    <button
                        onClick={() => {
                            setChoice(3);
                            if (sectionRefs.history?.current) {
                                const offset = 100;
                                const elementTop = sectionRefs.history.current.getBoundingClientRect().top + window.pageYOffset;
                                window.scrollTo({
                                    top: elementTop - offset,
                                    behavior: 'smooth'
                                });
                            }
                        }}
                        className={`flex flex-col items-center justify-center p-2 flex-1 ${choice === 3 ? 'bg-sky-100 text-sky-700' : 'text-gray-600 hover:bg-gray-100'} rounded-md transition-colors`}
                    >
                        <History size={20} className={choice === 3 ? 'text-sky-700' : 'text-gray-600'} />
                        <span className="text-xs mt-1">Lịch sử</span>
                    </button>

                    <button
                        onClick={() => {
                            setChoice(4);
                            if (sectionRefs.exams?.current) {
                                const offset = 100;
                                const elementTop = sectionRefs.exams.current.getBoundingClientRect().top + window.pageYOffset;
                                window.scrollTo({
                                    top: elementTop - offset,
                                    behavior: 'smooth'
                                });
                            }
                        }}
                        className={`flex flex-col items-center justify-center p-2 flex-1 ${choice === 4 ? 'bg-sky-100 text-sky-700' : 'text-gray-600 hover:bg-gray-100'} rounded-md transition-colors`}
                    >
                        <FileText size={20} className={choice === 4 ? 'text-sky-700' : 'text-gray-600'} />
                        <span className="text-xs mt-1">Đề thi</span>
                    </button>
                </div>

                {/* Add padding to bottom of content on mobile to account for bottom nav */}
                <div className="h-16 sm:hidden"></div>
            </div>
        </UserLayout>
    )
}

export default OverViewPage