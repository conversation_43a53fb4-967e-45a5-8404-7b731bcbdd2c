import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import UserLayout from "../../../layouts/UserLayout";
import { fetchArticles } from "../../../features/article/articleSlice";
import { fetchCodesByType } from "../../../features/code/codeSlice";
import LoadingSpinner from "../../../components/loading/LoadingSpinner";
import {
    Filter,
    Calendar,
    User,
    ChevronRight,
    Home,
    FileText,
    Loader,
    Tag,
    BookOpen,
    GraduationCap,
    BookText
} from "lucide-react";
// Import components
import Pagination from "../../../components/Pagination";
import ModernArticleSidebar from "../../../components/article/ModernArticleSidebar";
import SearchBar from "../../../components/article/SearchBar";

const ArticleCard = ({ article, onClick, formatDate, getTypeDescription, getClassDescription }) => {
    return (
        <div
            onClick={onClick}
            className="flex flex-col sm:flex-row p-3 sm:p-4 gap-3 sm:gap-4 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-cyan-300 cursor-pointer transition-all duration-200 group"
        >
            {/* Article Icon */}
            <div className="flex-shrink-0 flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-sky-100 to-blue-100 rounded-lg group-hover:scale-105 transition-transform duration-200 mx-auto sm:mx-0">
                <FileText className="w-6 h-6 sm:w-8 sm:h-8 text-sky-600" />
            </div>

            {/* Article Content */}
            <div className="flex-1 flex flex-col justify-between">
                {/* Article Title */}
                <div>
                    <h3 className="text-zinc-900 font-semibold text-sm sm:text-base line-clamp-2 group-hover:text-cyan-700 transition-colors text-center sm:text-left">
                        {article.title}
                    </h3>
                    <p className="text-xs sm:text-sm text-gray-600 mt-1 sm:mt-2 line-clamp-2 text-center sm:text-left">
                        {article.content.replace(/[#*`]/g, '').slice(0, 100)}...
                    </p>
                </div>

                {/* Article Meta */}
                <div className="mt-2 sm:mt-3 flex flex-wrap items-center justify-center sm:justify-start gap-2 sm:gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span className="hidden sm:inline">{formatDate(article.createdAt)}</span>
                        <span className="sm:hidden">{new Date(article.createdAt).toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' })}</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        <span className="truncate max-w-20 sm:max-w-none">{article.author}</span>
                    </div>
                    {article.type && (
                        <div className="flex items-center gap-1">
                            <Tag className="w-3 h-3" />
                            <span className="truncate max-w-16 sm:max-w-none">{getTypeDescription(article.type)}</span>
                        </div>
                    )}
                    {article.class && (
                        <div className="flex items-center gap-1">
                            <GraduationCap className="w-3 h-3" />
                            <span>{getClassDescription(article.class)}</span>
                        </div>
                    )}
                </div>
            </div>

            {/* Read More Button */}
            <div className="flex-shrink-0 flex items-center justify-center sm:justify-start">
                <div className="flex items-center gap-2 px-3 py-1 bg-sky-50 text-sky-700 rounded-full text-xs font-medium hover:bg-sky-100 transition-colors">
                    <span>Đọc tiếp</span>
                    <ChevronRight className="w-3 h-3" />
                </div>
            </div>
        </div>
    );
};

const ArticleListPage = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const location = useLocation();
    const { articles, pagination, loading: articlesLoading } = useSelector(state => state.articles);
    const { codes } = useSelector(state => state.codes);
    const { loading: stateLoading } = useSelector(state => state.states);
    const loading = articlesLoading || stateLoading;
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedType, setSelectedType] = useState("");
    const [selectedClass, setSelectedClass] = useState("");
    const [showMobileSidebar, setShowMobileSidebar] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);

    // Parse query parameters
    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const typeParam = params.get("type");
        const classParam = params.get("class");
        const chapterParam = params.get("chapter");
        const searchParam = params.get("search");
        const pageParam = params.get("page");

        if (typeParam) setSelectedType(typeParam);
        if (classParam) setSelectedClass(classParam);
        if (chapterParam) setSelectedChapter(chapterParam);
        if (searchParam) setSearchTerm(searchParam);
        if (pageParam && !isNaN(parseInt(pageParam))) {
            setCurrentPage(parseInt(pageParam));
        }
    }, [location.search]);

    // Fetch code data
    useEffect(() => {
        dispatch(fetchCodesByType(["article type", "grade", "chapter"]));
    }, [dispatch]);

    // Initial fetch of articles
    useEffect(() => {
        // Only fetch articles on initial load
        if (location.search === "") {
            dispatch(fetchArticles({ page: 1, limit: 10 }));
        }
    }, [dispatch]);

    // Format date for display
    const formatDate = (dateString) => {
        if (!dateString) return "N/A";
        const date = new Date(dateString);
        return date.toLocaleDateString("vi-VN", {
            year: "numeric",
            month: "long",
            day: "numeric"
        });
    };

    // Get type, class, and chapter descriptions
    const getTypeDescription = (typeCode) => {
        if (!typeCode || !codes || !codes["article type"]) return typeCode;
        const type = codes["article type"].find(t => t.code === typeCode);
        return type ? type.description : typeCode;
    };

    const getClassDescription = (classCode) => {
        if (!classCode || !codes || !codes["grade"]) return classCode;
        const grade = codes["grade"].find(g => g.code === classCode);
        return grade ? grade.description : classCode;
    };

    const getChapterDescription = (chapterCode) => {
        if (!chapterCode || !codes || !codes["chapter"]) return chapterCode;
        const chapter = codes["chapter"].find(c => c.code === chapterCode);
        return chapter ? chapter.description : chapterCode;
    };

    // Check if the selected class is a chapter code class (10C1, 11C1, 12C1)
    const isChapterCodeClass = selectedClass === "10C1" || selectedClass === "11C1" || selectedClass === "12C1";

    // State for chapter filter
    const [selectedChapter, setSelectedChapter] = useState("");

    // Reset chapter filter when class changes
    useEffect(() => {
        if (!isChapterCodeClass) {
            setSelectedChapter("");
        }
    }, [selectedClass, isChapterCodeClass]);



    // Update URL with current filters and page
    const updateURL = (page = currentPage) => {
        const params = new URLSearchParams();
        if (searchTerm) params.append("search", searchTerm);
        if (selectedType) params.append("type", selectedType);
        if (selectedClass) params.append("class", selectedClass);
        if (isChapterCodeClass && selectedChapter) params.append("chapter", selectedChapter);
        if (page > 1) params.append("page", page.toString());

        navigate(`/articles?${params.toString()}`);
    };

    // Handle search form submission
    const handleSearch = (e) => {
        if (e) e.preventDefault();

        // Reset to first page when searching
        setCurrentPage(1);
        updateURL(1);

        // Close mobile sidebar on mobile
        if (window.innerWidth < 768) {
            setShowMobileSidebar(false);
        }
    };

    // Handle page change
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);

        // Create API params for fetching the new page
        const params = new URLSearchParams(location.search);
        const apiParams = {
            page: pageNumber,
            limit: 10
        };

        if (params.get("search")) apiParams.search = params.get("search");
        if (params.get("type")) apiParams.type = params.get("type");
        if (params.get("class")) apiParams.class = params.get("class");
        if (params.get("chapter")) apiParams.chapter = params.get("chapter");

        // Dispatch action to fetch articles for the new page
        dispatch(fetchArticles(apiParams));

        // Update URL
        updateURL(pageNumber);

        // Scroll to top of the article list
        window.scrollTo({
            top: document.querySelector('.article-list-container')?.offsetTop - 100 || 0,
            behavior: 'smooth'
        });
    };

    // Reset all filters
    const handleResetFilters = () => {
        setSearchTerm("");
        setSelectedType("");
        setSelectedClass("");
        setSelectedChapter("");
        setCurrentPage(1);

        // Fetch all articles without filters
        dispatch(fetchArticles({ page: 1, limit: 10 }));

        // Navigate to articles page without params
        navigate("/articles");
    };



    return (
        <UserLayout>

            <div className="flex flex-col lg:flex-row w-full bg-gray-50 min-h-screen">
                {/* Left Sidebar - Filters - Hidden on mobile, shown on desktop */}
                <div className="hidden lg:block">
                    <ModernArticleSidebar />
                </div>

                {/* Main Content */}
                <div className="flex-1 transition-all duration-300 w-full lg:w-auto">
                    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 max-w-full lg:max-w-6xl">
                        {/* Page Header */}
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 gap-4">
                            <div className="text-center sm:text-left">
                                <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 flex items-center justify-start gap-2 sm:gap-3">
                                    <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl flex items-center justify-center">
                                        <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                                    </div>
                                    Bài viết Wiki
                                </h1>
                                <p className="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">Khám phá kiến thức toán học qua các bài viết chất lượng</p>
                            </div>
                            <div className="flex items-center justify-center sm:justify-start gap-2 px-3 sm:px-4 py-2 bg-sky-50 border border-sky-200 rounded-lg">
                                <div className="w-2 h-2 bg-sky-500 rounded-full animate-pulse"></div>
                                <span className="text-sky-700 text-sm font-medium">
                                    {articles.length} bài viết
                                </span>
                            </div>
                        </div>

                        {/* Mobile Filters */}
                        <div className="lg:hidden mb-6">
                            <button
                                className="w-full bg-sky-600 text-white p-3 rounded-lg flex items-center justify-center gap-2"
                                onClick={() => setShowMobileSidebar(!showMobileSidebar)}
                            >
                                <Filter className="w-4 h-4" />
                                Bộ lọc
                            </button>

                            {showMobileSidebar && (
                                <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
                                    <SearchBar
                                        searchTerm={searchTerm}
                                        setSearchTerm={setSearchTerm}
                                        handleSearch={handleSearch}
                                        isMobile={true}
                                    />
                                    {/* Add mobile filter options here */}
                                </div>
                            )}
                        </div>

                        {/* Articles Section */}
                        <div className="mb-6 sm:mb-8">
                            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                                {loading ? (
                                    <div className="p-6 sm:p-8 text-center text-gray-500">
                                        <Loader size={32} className="sm:w-10 sm:h-10 mx-auto mb-4 text-gray-300 animate-spin" />
                                        <p className="text-sm sm:text-base">Đang tải bài viết...</p>
                                    </div>
                                ) : articles && articles.length > 0 ? (
                                    <div className="p-3 sm:p-6">
                                        <div className="space-y-3 sm:space-y-4">
                                            {articles.map((article) => (
                                                <ArticleCard
                                                    key={article.id}
                                                    article={article}
                                                    onClick={() => navigate(`/articles/${article.id}`)}
                                                    formatDate={formatDate}
                                                    getTypeDescription={getTypeDescription}
                                                    getClassDescription={getClassDescription}
                                                />
                                            ))}
                                        </div>

                                        {/* Pagination */}
                                        <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-100">
                                            <Pagination
                                                currentPage={currentPage}
                                                totalItems={pagination?.total || articles.length}
                                                limit={10}
                                                onPageChange={handlePageChange}
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <div className="p-6 sm:p-8 text-center text-gray-500">
                                        <FileText size={32} className="sm:w-10 sm:h-10 mx-auto mb-4 text-gray-300" />
                                        <p className="text-sm sm:text-base">Không có bài viết nào.</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Panel - Quick Actions - Hidden on mobile and tablet */}
                <div className="sticky top-20 py-4 w-[280px] xl:w-[300px] h-[90vh] overflow-y-auto hide-scrollbar hidden xl:block">
                    {/* You can add quick actions or additional content here */}
                </div>
            </div>
        </UserLayout>
    );
};

export default ArticleListPage;
