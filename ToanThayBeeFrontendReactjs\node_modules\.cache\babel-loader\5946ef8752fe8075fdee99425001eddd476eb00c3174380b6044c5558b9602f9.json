{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport * as questionApi from \"../../services/questionApi\";\nexport const fetchExamQuestionsWithoutPagination = createAsyncThunk(\"questions/fetchExamQuestionsWithExam\", async (_ref, _ref2) => {\n  let {\n    id\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, {\n    id,\n    pageSize: 1000,\n    sortOrder: 'DESC'\n  }, data => {}, false, false);\n});\nconst questionsExamSlice = createSlice({\n  name: \"questionsExam\",\n  initialState: {\n    questionsExam: [],\n    loading: false,\n    view: 'question',\n    showAddImagesModal: false,\n    folder: \"questionImage\",\n    selectedIndex: 0\n  },\n  reducers: {\n    setQuestionsExam: (state, action) => {\n      state.questionsExam = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setViewRightContent: (state, action) => {\n      state.view = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchExamQuestionsWithoutPagination.pending, state => {\n      state.loading = true;\n    }).addCase(fetchExamQuestionsWithoutPagination.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.questionsExam = action.payload.data;\n      }\n      state.loading = false;\n    }).addCase(fetchExamQuestionsWithoutPagination.rejected, state => {\n      state.questionsExam = [];\n      state.loading = false;\n    });\n  }\n});\nexport const {\n  setQuestionsExam,\n  setLoading,\n  setViewRightContent\n} = questionsExamSlice.actions;\nexport default questionsExamSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questionA<PERSON>", "fetchExamQuestionsWithoutPagination", "_ref", "_ref2", "id", "dispatch", "getExamQuestionsAPI", "pageSize", "sortOrder", "data", "questionsExamSlice", "name", "initialState", "questionsExam", "loading", "view", "showAddImagesModal", "folder", "selectedIndex", "reducers", "setQuestionsExam", "state", "action", "payload", "setLoading", "setViewRightContent", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/questionsExam/questionsExamSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport * as questionApi from \"../../services/questionApi\";\r\n\r\nexport const fetchExamQuestionsWithoutPagination = createAsyncThunk(\r\n    \"questions/fetchExamQuestionsWithExam\",\r\n    async ({ id }, { dispatch }) => {\r\n        return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, { id, pageSize: 1000, sortOrder: 'DESC' }, (data) => {\r\n        }, false, false);\r\n    }\r\n);\r\n\r\n\r\nconst questionsExamSlice = createSlice({\r\n    name: \"questionsExam\",\r\n    initialState: {\r\n        questionsExam: [],\r\n        loading: false,\r\n        view: 'question',\r\n        showAddImagesModal: false,\r\n        folder: \"questionImage\",\r\n        selectedIndex: 0,\r\n    },\r\n    reducers: {\r\n        setQuestionsExam: (state, action) => {\r\n            state.questionsExam = action.payload;\r\n        },\r\n        setLoading: (state, action) => {\r\n            state.loading = action.payload;\r\n        },\r\n        setViewRightContent: (state, action) => {\r\n            state.view = action.payload;\r\n        },\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchExamQuestionsWithoutPagination.pending, (state) => {\r\n                state.loading = true;\r\n            })\r\n            .addCase(fetchExamQuestionsWithoutPagination.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.questionsExam = action.payload.data;\r\n                }\r\n                state.loading = false;\r\n            })\r\n            .addCase(fetchExamQuestionsWithoutPagination.rejected, (state) => {\r\n                state.questionsExam = [];\r\n                state.loading = false;\r\n            })\r\n    },\r\n});\r\n\r\nexport const {\r\n    setQuestionsExam,\r\n    setLoading,\r\n    setViewRightContent,\r\n} = questionsExamSlice.actions;\r\nexport default questionsExamSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAO,KAAKC,WAAW,MAAM,4BAA4B;AAEzD,OAAO,MAAMC,mCAAmC,GAAGJ,gBAAgB,CAC/D,sCAAsC,EACtC,OAAAK,IAAA,EAAAC,KAAA,KAAgC;EAAA,IAAzB;IAAEC;EAAG,CAAC,GAAAF,IAAA;EAAA,IAAE;IAAEG;EAAS,CAAC,GAAAF,KAAA;EACvB,OAAO,MAAMJ,UAAU,CAACM,QAAQ,EAAEL,WAAW,CAACM,mBAAmB,EAAE;IAAEF,EAAE;IAAEG,QAAQ,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAO,CAAC,EAAGC,IAAI,IAAK,CACxH,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACpB,CACJ,CAAC;AAGD,MAAMC,kBAAkB,GAAGd,WAAW,CAAC;EACnCe,IAAI,EAAE,eAAe;EACrBC,YAAY,EAAE;IACVC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,UAAU;IAChBC,kBAAkB,EAAE,KAAK;IACzBC,MAAM,EAAE,eAAe;IACvBC,aAAa,EAAE;EACnB,CAAC;EACDC,QAAQ,EAAE;IACNC,gBAAgB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MACjCD,KAAK,CAACR,aAAa,GAAGS,MAAM,CAACC,OAAO;IACxC,CAAC;IACDC,UAAU,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MAC3BD,KAAK,CAACP,OAAO,GAAGQ,MAAM,CAACC,OAAO;IAClC,CAAC;IACDE,mBAAmB,EAAEA,CAACJ,KAAK,EAAEC,MAAM,KAAK;MACpCD,KAAK,CAACN,IAAI,GAAGO,MAAM,CAACC,OAAO;IAC/B;EACJ,CAAC;EACDG,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC3B,mCAAmC,CAAC4B,OAAO,EAAGR,KAAK,IAAK;MAC7DA,KAAK,CAACP,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDc,OAAO,CAAC3B,mCAAmC,CAAC6B,SAAS,EAAE,CAACT,KAAK,EAAEC,MAAM,KAAK;MACvE,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBF,KAAK,CAACR,aAAa,GAAGS,MAAM,CAACC,OAAO,CAACd,IAAI;MAC7C;MACAY,KAAK,CAACP,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDc,OAAO,CAAC3B,mCAAmC,CAAC8B,QAAQ,EAAGV,KAAK,IAAK;MAC9DA,KAAK,CAACR,aAAa,GAAG,EAAE;MACxBQ,KAAK,CAACP,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACTM,gBAAgB;EAChBI,UAAU;EACVC;AACJ,CAAC,GAAGf,kBAAkB,CAACsB,OAAO;AAC9B,eAAetB,kBAAkB,CAACuB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}