{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\article\\\\ArticleContent.jsx\",\n  _s = $RefreshSig$();\nimport React, { useMemo } from 'react';\nimport MarkdownPreview from \"@uiw/react-markdown-preview\";\nimport remarkMath from \"remark-math\";\nimport rehypeKatex from \"rehype-katex\";\nimport \"@uiw/react-markdown-preview/markdown.css\";\nimport \"katex/dist/katex.min.css\";\nimport \"../../styles/markdown-preview.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ArticleContent = _ref => {\n  _s();\n  let {\n    content\n  } = _ref;\n  // Process content to replace LaTeX delimiters\n  const processedContent = useMemo(() => {\n    if (!content) return \"\";\n\n    // Replace \\( \\) with $ $ for inline math\n    // Replace \\[ \\] with $$ $$ for block math\n    let processed = content.replace(/\\\\\\(/g, \"$\").replace(/\\\\\\)/g, \"$\").replace(/\\\\\\[/g, \"$$\").replace(/\\\\\\]/g, \"$$\");\n    return processed;\n  }, [content]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white border rounded-md overflow-hidden mb-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"custom-markdown-preview\",\n        children: /*#__PURE__*/_jsxDEV(MarkdownPreview, {\n          source: processedContent,\n          remarkPlugins: [remarkMath],\n          rehypePlugins: [[rehypeKatex, {\n            strict: false,\n            throwOnError: false,\n            errorColor: '#cc0000'\n          }]],\n          className: \"markdown-preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 9\n  }, this);\n};\n_s(ArticleContent, \"pj7ZUFO/TIPT+0Iv71RTcfjVF4c=\");\n_c = ArticleContent;\nexport default ArticleContent;\nvar _c;\n$RefreshReg$(_c, \"ArticleContent\");", "map": {"version": 3, "names": ["React", "useMemo", "MarkdownPreview", "remarkMath", "rehypeKatex", "jsxDEV", "_jsxDEV", "ArticleContent", "_ref", "_s", "content", "processedContent", "processed", "replace", "className", "children", "source", "remarkPlugins", "rehypePlugins", "strict", "throwOnError", "errorColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/article/ArticleContent.jsx"], "sourcesContent": ["import React, { useMemo } from 'react';\r\nimport MarkdownPreview from \"@uiw/react-markdown-preview\";\r\nimport remarkMath from \"remark-math\";\r\nimport rehypeKatex from \"rehype-katex\";\r\nimport \"@uiw/react-markdown-preview/markdown.css\";\r\nimport \"katex/dist/katex.min.css\";\r\nimport \"../../styles/markdown-preview.css\";\r\n\r\nconst ArticleContent = ({ content }) => {\r\n    // Process content to replace LaTeX delimiters\r\n    const processedContent = useMemo(() => {\r\n        if (!content) return \"\";\r\n\r\n        // Replace \\( \\) with $ $ for inline math\r\n        // Replace \\[ \\] with $$ $$ for block math\r\n        let processed = content\r\n            .replace(/\\\\\\(/g, \"$\")\r\n            .replace(/\\\\\\)/g, \"$\")\r\n            .replace(/\\\\\\[/g, \"$$\")\r\n            .replace(/\\\\\\]/g, \"$$\");\r\n\r\n        return processed;\r\n    }, [content]);\r\n\r\n    return (\r\n        <div className=\"bg-white border rounded-md overflow-hidden mb-6\">\r\n            <div className=\"p-6\">\r\n                <div className=\"custom-markdown-preview\">\r\n                    <MarkdownPreview\r\n                        source={processedContent}\r\n                        remarkPlugins={[remarkMath]}\r\n                        rehypePlugins={[\r\n                            [rehypeKatex, {\r\n                                strict: false,\r\n                                throwOnError: false,\r\n                                errorColor: '#cc0000'\r\n                            }]\r\n                        ]}\r\n                        className=\"markdown-preview\"\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ArticleContent;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,0CAA0C;AACjD,OAAO,0BAA0B;AACjC,OAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,cAAc,GAAGC,IAAA,IAAiB;EAAAC,EAAA;EAAA,IAAhB;IAAEC;EAAQ,CAAC,GAAAF,IAAA;EAC/B;EACA,MAAMG,gBAAgB,GAAGV,OAAO,CAAC,MAAM;IACnC,IAAI,CAACS,OAAO,EAAE,OAAO,EAAE;;IAEvB;IACA;IACA,IAAIE,SAAS,GAAGF,OAAO,CAClBG,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;IAE3B,OAAOD,SAAS;EACpB,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC;EAEb,oBACIJ,OAAA;IAAKQ,SAAS,EAAC,iDAAiD;IAAAC,QAAA,eAC5DT,OAAA;MAAKQ,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChBT,OAAA;QAAKQ,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCT,OAAA,CAACJ,eAAe;UACZc,MAAM,EAAEL,gBAAiB;UACzBM,aAAa,EAAE,CAACd,UAAU,CAAE;UAC5Be,aAAa,EAAE,CACX,CAACd,WAAW,EAAE;YACVe,MAAM,EAAE,KAAK;YACbC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE;UAChB,CAAC,CAAC,CACJ;UACFP,SAAS,EAAC;QAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAChB,EAAA,CApCIF,cAAc;AAAAmB,EAAA,GAAdnB,cAAc;AAsCpB,eAAeA,cAAc;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}