{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\Header.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { ChevronLeft } from 'lucide-react';\nimport Button from \"src/components/PageAIexam/Button\";\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useState, useEffect } from 'react';\nimport { setViewEdit } from 'src/features/examAI/examAISlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst arrayOfObjectEqualUnordered = (arr1, arr2) => {\n  if (arr1.length !== arr2.length) return false;\n  const sortedArr1 = arr1.map(obj => JSON.stringify(obj)).sort();\n  const sortedArr2 = arr2.map(obj => JSON.stringify(obj)).sort();\n  return sortedArr1.every((value, index) => value === sortedArr2[index]);\n};\nconst Header = _ref => {\n  _s();\n  let {\n    title = \"Đề thi AI\"\n  } = _ref;\n  const navigate = useNavigate();\n  const [isChanged, setIsChanged] = useState(false);\n  const {\n    questions,\n    questionsEdited,\n    viewEdit,\n    exam,\n    editedExam\n  } = useSelector(state => state.examAI);\n  const dispatch = useDispatch();\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n  useEffect(() => {\n    if (isChanged === true) return;\n    setIsChanged(!arrayOfObjectEqualUnordered(questions, questionsEdited));\n  }, [questions, questionsEdited]);\n  useEffect(() => {\n    if (isChanged === true) return;\n    setIsChanged(!arrayOfObjectEqualUnordered(questions, questionsEdited));\n  }, [exam, editedExam]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed z-50 top-0  bg-white h-16 px-4 flex items-center justify-between border-b border-gray-300 \".concat(closeSidebar ? \"w-[calc(100vw_-_104px)]\" : \"w-[calc(100vw_-16rem)]\"),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => navigate(-1),\n        type: \"none\",\n        children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n          className: \"w-5 h-5 text-black\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"font-bold\",\n        children: \"Qu\\u1EA3n l\\xFD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-4\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        // onClick={}\n        className: \"px-2 py-1 text-sm rounded-md transition-colors duration-300\\n                \".concat(isChanged ? 'bg-emerald-500 hover:bg-emerald-700' : 'bg-gray-300 cursor-not-allowed'),\n        disabled: !isChanged,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white\",\n          children: \"L\\u01B0u thay \\u0111\\u1ED5i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute left-1/2 -translate-x-1/2 bottom-[-1.25rem] z-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => dispatch(setViewEdit('exam')),\n        className: \"px-4 py-1 \".concat(viewEdit === 'exam' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200', \" text-sm border border-gray-300\"),\n        children: \"\\u0110\\u1EC1 thi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => dispatch(setViewEdit('question')),\n        className: \"px-4 py-1 \".concat(viewEdit === 'question' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200', \" text-sm  border border-gray-300\"),\n        children: \"C\\xE2u h\\u1ECFi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 9\n  }, this);\n};\n_s(Header, \"ULx61I+XmI8P0hMGZAFfR02dTTY=\", false, function () {\n  return [useNavigate, useSelector, useDispatch, useSelector];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useNavigate", "ChevronLeft", "<PERSON><PERSON>", "useSelector", "useDispatch", "useState", "useEffect", "setViewEdit", "jsxDEV", "_jsxDEV", "arrayOfObjectEqualUnordered", "arr1", "arr2", "length", "sortedArr1", "map", "obj", "JSON", "stringify", "sort", "sortedArr2", "every", "value", "index", "Header", "_ref", "_s", "title", "navigate", "isChanged", "setIsChanged", "questions", "questionsEdited", "viewEdit", "exam", "editedExam", "state", "examAI", "dispatch", "closeSidebar", "sidebar", "className", "concat", "children", "onClick", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/Header.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { ChevronLeft } from 'lucide-react';\r\nimport Button from \"src/components/PageAIexam/Button\";\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { useState, useEffect } from 'react';\r\nimport { setViewEdit } from 'src/features/examAI/examAISlice';\r\n\r\nconst arrayOfObjectEqualUnordered = (arr1, arr2) => {\r\n    if (arr1.length !== arr2.length) return false;\r\n    const sortedArr1 = arr1.map(obj => JSON.stringify(obj)).sort();\r\n    const sortedArr2 = arr2.map(obj => JSON.stringify(obj)).sort();\r\n    return sortedArr1.every((value, index) => value === sortedArr2[index]);\r\n};\r\n\r\nconst Header = ({ title = \"Đề thi AI\" }) => {\r\n    const navigate = useNavigate();\r\n    const [isChanged, setIsChanged] = useState(false);\r\n    const { questions, questionsEdited, viewEdit, exam, editedExam } = useSelector((state) => state.examAI);\r\n    const dispatch = useDispatch();\r\n    const { closeSidebar } = useSelector((state) => state.sidebar);\r\n\r\n    useEffect(() => {\r\n        if (isChanged === true) return\r\n        setIsChanged(!arrayOfObjectEqualUnordered(questions, questionsEdited));\r\n    }, [questions, questionsEdited]);\r\n\r\n    useEffect(() => {\r\n        if (isChanged === true) return\r\n        setIsChanged(!arrayOfObjectEqualUnordered(questions, questionsEdited));\r\n    }, [exam, editedExam]);\r\n\r\n    return (\r\n        <div className={`fixed z-50 top-0  bg-white h-16 px-4 flex items-center justify-between border-b border-gray-300 ${closeSidebar ? \"w-[calc(100vw_-_104px)]\" : \"w-[calc(100vw_-16rem)]\"}`}>\r\n            <div className=\"flex items-center gap-4\">\r\n                <Button onClick={() => navigate(-1)} type=\"none\">\r\n                    <ChevronLeft className=\"w-5 h-5 text-black\" />\r\n                </Button>\r\n                <p className=\"font-bold\">Quản lý</p>\r\n                <p className=\"text-gray-500\">{title}</p>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-4\">\r\n                <button\r\n                    // onClick={}\r\n                    className={`px-2 py-1 text-sm rounded-md transition-colors duration-300\r\n                ${isChanged ? 'bg-emerald-500 hover:bg-emerald-700' : 'bg-gray-300 cursor-not-allowed'}`}\r\n                    disabled={!isChanged}\r\n                >\r\n                    <span className=\"text-white\">Lưu thay đổi</span>\r\n                </button>\r\n            </div>\r\n\r\n            {/* 🟦 Nút giữa cạnh dưới header */}\r\n            <div className=\"absolute left-1/2 -translate-x-1/2 bottom-[-1.25rem] z-50\">\r\n                <button\r\n                    onClick={() => dispatch(setViewEdit('exam'))}\r\n                    className={`px-4 py-1 ${viewEdit === 'exam' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200'} text-sm border border-gray-300`}\r\n                >\r\n                    Đề thi\r\n                </button>\r\n                <button\r\n                    onClick={() => dispatch(setViewEdit('question'))}\r\n                    className={`px-4 py-1 ${viewEdit === 'question' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200'} text-sm  border border-gray-300`}\r\n                >\r\n                    Câu hỏi\r\n                </button>\r\n            </div>\r\n        </div>\r\n\r\n    );\r\n};\r\nexport default Header;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,MAAM,MAAM,kCAAkC;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,2BAA2B,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAChD,IAAID,IAAI,CAACE,MAAM,KAAKD,IAAI,CAACC,MAAM,EAAE,OAAO,KAAK;EAC7C,MAAMC,UAAU,GAAGH,IAAI,CAACI,GAAG,CAACC,GAAG,IAAIC,IAAI,CAACC,SAAS,CAACF,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;EAC9D,MAAMC,UAAU,GAAGR,IAAI,CAACG,GAAG,CAACC,GAAG,IAAIC,IAAI,CAACC,SAAS,CAACF,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;EAC9D,OAAOL,UAAU,CAACO,KAAK,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKD,KAAK,KAAKF,UAAU,CAACG,KAAK,CAAC,CAAC;AAC1E,CAAC;AAED,MAAMC,MAAM,GAAGC,IAAA,IAA6B;EAAAC,EAAA;EAAA,IAA5B;IAAEC,KAAK,GAAG;EAAY,CAAC,GAAAF,IAAA;EACnC,MAAMG,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAE0B,SAAS;IAAEC,eAAe;IAAEC,QAAQ;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGhC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACvG,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmC;EAAa,CAAC,GAAGpC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACI,OAAO,CAAC;EAE9DlC,SAAS,CAAC,MAAM;IACZ,IAAIuB,SAAS,KAAK,IAAI,EAAE;IACxBC,YAAY,CAAC,CAACpB,2BAA2B,CAACqB,SAAS,EAAEC,eAAe,CAAC,CAAC;EAC1E,CAAC,EAAE,CAACD,SAAS,EAAEC,eAAe,CAAC,CAAC;EAEhC1B,SAAS,CAAC,MAAM;IACZ,IAAIuB,SAAS,KAAK,IAAI,EAAE;IACxBC,YAAY,CAAC,CAACpB,2BAA2B,CAACqB,SAAS,EAAEC,eAAe,CAAC,CAAC;EAC1E,CAAC,EAAE,CAACE,IAAI,EAAEC,UAAU,CAAC,CAAC;EAEtB,oBACI1B,OAAA;IAAKgC,SAAS,qGAAAC,MAAA,CAAqGH,YAAY,GAAG,yBAAyB,GAAG,wBAAwB,CAAG;IAAAI,QAAA,gBACrLlC,OAAA;MAAKgC,SAAS,EAAC,yBAAyB;MAAAE,QAAA,gBACpClC,OAAA,CAACP,MAAM;QAAC0C,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,CAAC,CAAC,CAAE;QAACiB,IAAI,EAAC,MAAM;QAAAF,QAAA,eAC5ClC,OAAA,CAACR,WAAW;UAACwC,SAAS,EAAC;QAAoB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACTxC,OAAA;QAAGgC,SAAS,EAAC,WAAW;QAAAE,QAAA,EAAC;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpCxC,OAAA;QAAGgC,SAAS,EAAC,eAAe;QAAAE,QAAA,EAAEhB;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAENxC,OAAA;MAAKgC,SAAS,EAAC,yBAAyB;MAAAE,QAAA,eACpClC,OAAA;QACI;QACAgC,SAAS,kFAAAC,MAAA,CACXb,SAAS,GAAG,qCAAqC,GAAG,gCAAgC,CAAG;QACrFqB,QAAQ,EAAE,CAACrB,SAAU;QAAAc,QAAA,eAErBlC,OAAA;UAAMgC,SAAS,EAAC,YAAY;UAAAE,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNxC,OAAA;MAAKgC,SAAS,EAAC,2DAA2D;MAAAE,QAAA,gBACtElC,OAAA;QACImC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC/B,WAAW,CAAC,MAAM,CAAC,CAAE;QAC7CkC,SAAS,eAAAC,MAAA,CAAeT,QAAQ,KAAK,MAAM,GAAG,0CAA0C,GAAG,uCAAuC,oCAAkC;QAAAU,QAAA,EACvK;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxC,OAAA;QACImC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC/B,WAAW,CAAC,UAAU,CAAC,CAAE;QACjDkC,SAAS,eAAAC,MAAA,CAAeT,QAAQ,KAAK,UAAU,GAAG,0CAA0C,GAAG,uCAAuC,qCAAmC;QAAAU,QAAA,EAC5K;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAGd,CAAC;AAACvB,EAAA,CAxDIF,MAAM;EAAA,QACSxB,WAAW,EAEuCG,WAAW,EAC7DC,WAAW,EACHD,WAAW;AAAA;AAAAgD,EAAA,GALlC3B,MAAM;AAyDZ,eAAeA,MAAM;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}