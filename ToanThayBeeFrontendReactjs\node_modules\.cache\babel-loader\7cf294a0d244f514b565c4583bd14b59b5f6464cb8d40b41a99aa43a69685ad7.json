{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\comment\\\\CommentItem.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { Send } from \"lucide-react\";\nimport { formatDistanceToNow } from \"date-fns\";\nimport { vi } from \"date-fns/locale\";\nimport { useSelector } from \"react-redux\";\nimport EmojiPicker from \"./EmojiPicker\";\nimport CommentInput from \"./CommentInput\";\nimport { fetchRepliesByCommentId } from \"src/features/comments/ExamCommentsSlice\";\nimport { useDispatch } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CommentItem = _ref => {\n  _s();\n  var _userInfo$firstName$, _userInfo$firstName, _userInfo$lastName$, _userInfo$lastName, _codes$userType, _codes$userType$find;\n  let {\n    comment,\n    user,\n    codes,\n    onUpdate,\n    onDelete,\n    onReply,\n    isChild\n  } = _ref;\n  const [editingId, setEditingId] = useState(null);\n  const [editContent, setEditContent] = useState(\"\");\n  const [showReply, setShowReply] = useState(false);\n  const [replyContent, setReplyContent] = useState(\"\");\n  const [showReplies, setShowReplies] = useState(false);\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const {\n    replies: repliesState\n  } = useSelector(state => state.comments);\n  const [replies, setReplies] = useState([]);\n  const handleShowReplies = async () => {\n    setShowReplies(true);\n    setLoading(true);\n    await dispatch(fetchRepliesByCommentId({\n      commentId: comment.id\n    })).unwrap().then(data => {\n      setLoading(false);\n    }).catch(error => {\n      setLoading(false);\n    });\n  };\n  useEffect(() => {\n    if (Array.isArray(repliesState[comment.id])) {\n      setReplies(repliesState[comment.id]);\n    }\n  }, [repliesState[comment.id]]);\n  const startEditing = () => {\n    setEditingId(comment.id);\n    setEditContent(comment.content);\n  };\n  const startReplying = () => {\n    setShowReply(true);\n    setReplyContent(\"\");\n  };\n  const handleUpdate = content => {\n    if (content.trim() === \"\") return;\n    onUpdate === null || onUpdate === void 0 ? void 0 : onUpdate(comment.id, content);\n    setEditingId(null);\n    setEditContent(\"\");\n  };\n  const handleCancel = () => {\n    setEditingId(null);\n    setEditContent(\"\");\n  };\n  const handleOnReply = (content, parentCommentId) => {\n    if (content.trim() === \"\") return;\n    onReply === null || onReply === void 0 ? void 0 : onReply(content, parentCommentId);\n    setShowReply(false);\n    setReplyContent(\"\");\n  };\n  const isOwner = (user === null || user === void 0 ? void 0 : user.id) === comment.userId;\n  const userInfo = comment.user || {};\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start gap-3 w-full \",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative \".concat(isChild ? \"w-7\" : \"w-8\", \" flex flex-col items-center h-full\"),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\".concat(isChild ? \"w-7 h-7\" : \"w-8 h-8\", \" rounded-full bg-gray-300 flex items-center justify-center text-white font-bold text-sm z-10\"),\n          children: userInfo !== null && userInfo !== void 0 && userInfo.avatarUrl ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: userInfo === null || userInfo === void 0 ? void 0 : userInfo.avatarUrl,\n            alt: \"avatar\",\n            className: \"w-full h-full object-cover rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 29\n          }, this) : ((_userInfo$firstName$ = userInfo === null || userInfo === void 0 ? void 0 : (_userInfo$firstName = userInfo.firstName) === null || _userInfo$firstName === void 0 ? void 0 : _userInfo$firstName[0]) !== null && _userInfo$firstName$ !== void 0 ? _userInfo$firstName$ : \"\") + ((_userInfo$lastName$ = userInfo === null || userInfo === void 0 ? void 0 : (_userInfo$lastName = userInfo.lastName) === null || _userInfo$lastName === void 0 ? void 0 : _userInfo$lastName[0]) !== null && _userInfo$lastName$ !== void 0 ? _userInfo$lastName$ : \"\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 h-full bg-gray-300 w-[2px] \".concat(comment.replyCount > 0 || showReply ? \"block\" : \"hidden\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2 group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 px-3 py-2 rounded-2xl relative \".concat(editingId === comment.id ? \"w-full\" : \"w-fit\"),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs font-semibold text-gray-800 \".concat(editingId === comment.id ? \"hidden\" : \"\"),\n              children: [userInfo === null || userInfo === void 0 ? void 0 : userInfo.firstName, \" \", (userInfo === null || userInfo === void 0 ? void 0 : userInfo.lastName) + \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: (userInfo === null || userInfo === void 0 ? void 0 : userInfo.userType) !== \"HS1\" && (codes === null || codes === void 0 ? void 0 : (_codes$userType = codes[\"user type\"]) === null || _codes$userType === void 0 ? void 0 : (_codes$userType$find = _codes$userType.find(c => c.code === (userInfo === null || userInfo === void 0 ? void 0 : userInfo.userType))) === null || _codes$userType$find === void 0 ? void 0 : _codes$userType$find.description)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 29\n            }, this), editingId === comment.id ? /*#__PURE__*/_jsxDEV(CommentInput, {\n              onSend: handleUpdate,\n              value: editContent\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-800 whitespace-pre-wrap\",\n              children: comment.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 25\n          }, this), isOwner && editingId !== comment.id && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden group-hover:flex gap-2 text-xs text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: startEditing,\n              className: \"hover:underline\",\n              children: \"S\\u1EEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onDelete === null || onDelete === void 0 ? void 0 : onDelete(comment.id),\n              className: \"hover:underline\",\n              children: \"Xo\\xE1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 29\n          }, this), isOwner && editingId === comment.id && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2 text-xs text-gray-500\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCancel,\n              className: \"hover:underline\",\n              children: \"H\\u1EE7y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 text-xs text-gray-500 mt-0.5 pl-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatDistanceToNow(new Date(comment.createdAt), {\n              addSuffix: true,\n              locale: vi\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startReplying(),\n            className: \"hover:underline \".concat(isChild ? \"hidden\" : \"block\"),\n            children: \"Tr\\u1EA3 l\\u1EDDi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this), !showReplies && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative flex-row py-2 items-center \".concat(comment.replyCount > 0 ? \"flex\" : \"hidden\"),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \" h-full w-[29px] pb-[8px]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full ml-[15px] w-full border-l-2 border-b-2 border-gray-300 rounded-bl-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-[15px] top-0 bottom-0 bg-gray-300 w-[2px] \".concat(showReply ? \"h-full\" : \"h-1/4\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: handleShowReplies,\n        className: \"text-sm text-gray-500 pl-6 cursor-pointer\",\n        children: [comment.replyCount, \" ph\\u1EA3n h\\u1ED3i\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 17\n    }, this), replies.length > 0 && showReplies && replies.map((reply, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative flex-row py-2 items-center \".concat(comment.replyCount > 0 ? \"flex\" : \"hidden\"),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \" h-full w-[29px] pb-[50px]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full ml-[15px] w-full border-l-2 border-b-2 border-gray-300 rounded-bl-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"\".concat(replies.length === index + 1 && !showReply ? \"h-1/5\" : \"\", \" absolute left-[15px] top-0 bottom-0 bg-gray-300 w-[2px]\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pl-6 w-full\",\n        children: /*#__PURE__*/_jsxDEV(CommentItem, {\n          comment: reply,\n          user: user,\n          codes: codes,\n          onUpdate: onUpdate,\n          onDelete: onDelete,\n          onReply: onReply,\n          isChild: true\n        }, reply.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 21\n    }, this)), showReply && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative flex-row py-2 items-center flex\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative h-full w-[29px] pb-[15px]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full ml-[15px] w-full border-l-2 border-b-2 border-gray-300 rounded-bl-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-[15px] top-0 h-1/3 bg-gray-300 w-[2px]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pl-6 w-full\",\n        children: /*#__PURE__*/_jsxDEV(CommentInput, {\n          onReply: handleOnReply,\n          parentCommentId: comment.id,\n          value: replyContent\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 9\n  }, this);\n};\n_s(CommentItem, \"3kRC/QmUEiO/ORvXe3EiNmABDos=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = CommentItem;\nexport default CommentItem;\nvar _c;\n$RefreshReg$(_c, \"CommentItem\");", "map": {"version": 3, "names": ["useEffect", "useState", "Send", "formatDistanceToNow", "vi", "useSelector", "EmojiPicker", "CommentInput", "fetchRepliesByCommentId", "useDispatch", "jsxDEV", "_jsxDEV", "CommentItem", "_ref", "_s", "_userInfo$firstName$", "_userInfo$firstName", "_userInfo$lastName$", "_userInfo$lastName", "_codes$userType", "_codes$userType$find", "comment", "user", "codes", "onUpdate", "onDelete", "onReply", "<PERSON><PERSON><PERSON><PERSON>", "editingId", "setEditingId", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showReply", "setShowReply", "replyContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showReplies", "setShowReplies", "dispatch", "loading", "setLoading", "replies", "repliesState", "state", "comments", "setReplies", "handleShowReplies", "commentId", "id", "unwrap", "then", "data", "catch", "error", "Array", "isArray", "startEditing", "content", "startReplying", "handleUpdate", "trim", "handleCancel", "handleOnReply", "parentCommentId", "isOwner", "userId", "userInfo", "className", "children", "concat", "avatarUrl", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "lastName", "replyCount", "userType", "find", "c", "code", "description", "onSend", "value", "onClick", "Date", "createdAt", "addSuffix", "locale", "length", "map", "reply", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/comment/CommentItem.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { Send } from \"lucide-react\";\r\nimport { formatDistanceToNow } from \"date-fns\";\r\nimport { vi } from \"date-fns/locale\";\r\nimport { useSelector } from \"react-redux\";\r\nimport EmojiPicker from \"./EmojiPicker\";\r\nimport CommentInput from \"./CommentInput\";\r\nimport { fetchRepliesByCommentId } from \"src/features/comments/ExamCommentsSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\n\r\nconst CommentItem = ({ comment, user, codes, onUpdate, onDelete, onReply, isChild }) => {\r\n    const [editingId, setEditingId] = useState(null);\r\n    const [editContent, setEditContent] = useState(\"\");\r\n    const [showReply, setShowReply] = useState(false);\r\n    const [replyContent, setReplyContent] = useState(\"\");\r\n\r\n    const [showReplies, setShowReplies] = useState(false);\r\n    const dispatch = useDispatch();\r\n    const [loading, setLoading] = useState(false);\r\n    const { replies: repliesState } = useSelector((state) => state.comments);\r\n    const [replies, setReplies] = useState([]);\r\n\r\n    const handleShowReplies = async () => {\r\n        setShowReplies(true);\r\n        setLoading(true);\r\n        await dispatch(fetchRepliesByCommentId({ commentId: comment.id }))\r\n            .unwrap()\r\n            .then((data) => {\r\n                setLoading(false);\r\n            })\r\n            .catch((error) => {\r\n                setLoading(false);\r\n            });\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(repliesState[comment.id])) {\r\n            setReplies(repliesState[comment.id]);\r\n        }\r\n    }, [repliesState[comment.id]]);\r\n\r\n    const startEditing = () => {\r\n        setEditingId(comment.id);\r\n        setEditContent(comment.content);\r\n    };\r\n\r\n    const startReplying = () => {\r\n        setShowReply(true);\r\n        setReplyContent(\"\");\r\n    };\r\n\r\n    const handleUpdate = (content) => {\r\n        if (content.trim() === \"\") return;\r\n        onUpdate?.(comment.id, content);\r\n        setEditingId(null);\r\n        setEditContent(\"\");\r\n    };\r\n\r\n    const handleCancel = () => {\r\n        setEditingId(null);\r\n        setEditContent(\"\");\r\n    };\r\n\r\n    const handleOnReply = (content, parentCommentId) => {\r\n        if (content.trim() === \"\") return;\r\n        onReply?.(content, parentCommentId);\r\n        setShowReply(false);\r\n        setReplyContent(\"\");\r\n    };\r\n\r\n    const isOwner = user?.id === comment.userId;\r\n    const userInfo = comment.user || {};\r\n\r\n    return (\r\n        <div className=\"flex flex-col w-full\">\r\n            <div className=\"flex items-start gap-3 w-full \">\r\n                <div className={`relative ${isChild ? \"w-7\" : \"w-8\"} flex flex-col items-center h-full`}>\r\n                    {/* Avatar */}\r\n                    <div className={`${isChild ? \"w-7 h-7\" : \"w-8 h-8\"} rounded-full bg-gray-300 flex items-center justify-center text-white font-bold text-sm z-10`}>\r\n                        {userInfo?.avatarUrl ? (\r\n                            <img src={userInfo?.avatarUrl} alt=\"avatar\" className=\"w-full h-full object-cover rounded-full\" />\r\n                        ) : (userInfo?.firstName?.[0] ?? \"\") + (userInfo?.lastName?.[0] ?? \"\")}\r\n                    </div>\r\n\r\n                    {/* Đường kẻ kéo xuống */}\r\n                    <div className={`absolute top-0 h-full bg-gray-300 w-[2px] ${comment.replyCount > 0 || showReply ? \"block\" : \"hidden\"}`} />\r\n\r\n                </div>\r\n\r\n\r\n                <div className=\"flex-1\">\r\n                    <div className=\"flex flex-row gap-2 group\">\r\n                        <div className={`bg-gray-100 px-3 py-2 rounded-2xl relative ${editingId === comment.id ? \"w-full\" : \"w-fit\"}`}>\r\n                            <p className={`text-xs font-semibold text-gray-800 ${editingId === comment.id ? \"hidden\" : \"\"}`}>\r\n                                {userInfo?.firstName} {userInfo?.lastName + \" \"}\r\n                                <span className=\"text-xs text-gray-500\">\r\n                                    {userInfo?.userType !== \"HS1\" && codes?.[\"user type\"]?.find(c => c.code === userInfo?.userType)?.description}\r\n                                </span>\r\n                            </p>\r\n\r\n                            {editingId === comment.id ? (\r\n                                <CommentInput onSend={handleUpdate} value={editContent} />\r\n                            ) : (\r\n                                <p className=\"text-sm text-gray-800 whitespace-pre-wrap\">{comment.content}</p>\r\n                            )}\r\n                        </div>\r\n\r\n                        {isOwner && editingId !== comment.id && (\r\n                            <div className=\"hidden group-hover:flex gap-2 text-xs text-gray-500\">\r\n                                <button onClick={startEditing} className=\"hover:underline\">Sửa</button>\r\n                                <button onClick={() => onDelete?.(comment.id)} className=\"hover:underline\">Xoá</button>\r\n                            </div>\r\n                        )}\r\n\r\n                        {isOwner && editingId === comment.id && (\r\n                            <div className=\"flex gap-2 text-xs text-gray-500\">\r\n                                <button onClick={handleCancel} className=\"hover:underline\">Hủy</button>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center gap-3 text-xs text-gray-500 mt-0.5 pl-2\">\r\n                        <span>{formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true, locale: vi })}</span>\r\n                        <button onClick={() => startReplying()} className={`hover:underline ${isChild ? \"hidden\" : \"block\"}`}>Trả lời</button>\r\n                    </div>\r\n\r\n                </div>\r\n            </div>\r\n            {!showReplies && (\r\n                <div className={`relative flex-row py-2 items-center ${comment.replyCount > 0 ? \"flex\" : \"hidden\"}`}>\r\n                    <div className=\" h-full w-[29px] pb-[8px]\">\r\n                        <div className=\"h-full ml-[15px] w-full border-l-2 border-b-2 border-gray-300 rounded-bl-xl\" >\r\n                        </div>\r\n                    </div>\r\n                    <div className={`absolute left-[15px] top-0 bottom-0 bg-gray-300 w-[2px] ${showReply ? \"h-full\" : \"h-1/4\"}`} />\r\n                    <div\r\n                        onClick={handleShowReplies}\r\n                        className=\"text-sm text-gray-500 pl-6 cursor-pointer\">{comment.replyCount} phản hồi</div>\r\n                </div>\r\n            )}\r\n            {replies.length > 0 && showReplies && (\r\n                replies.map((reply, index) => (\r\n                    <div className={`relative flex-row py-2 items-center ${comment.replyCount > 0 ? \"flex\" : \"hidden\"}`}>\r\n                        <div className=\" h-full w-[29px] pb-[50px]\">\r\n                            <div className=\"h-full ml-[15px] w-full border-l-2 border-b-2 border-gray-300 rounded-bl-xl\" >\r\n                            </div>\r\n\r\n                        </div>\r\n                        <div className={`${replies.length === index + 1 && !showReply ? \"h-1/5\" : \"\"} absolute left-[15px] top-0 bottom-0 bg-gray-300 w-[2px]`} />\r\n                        <div className=\"pl-6 w-full\">\r\n                            <CommentItem\r\n                                key={reply.id}\r\n                                comment={reply}\r\n                                user={user}\r\n                                codes={codes}\r\n                                onUpdate={onUpdate}\r\n                                onDelete={onDelete}\r\n                                onReply={onReply}\r\n                                isChild={true}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                ))\r\n            )}\r\n            {showReply && (\r\n                <div className=\"relative flex-row py-2 items-center flex\">\r\n                    <div className=\"relative h-full w-[29px] pb-[15px]\">\r\n                        <div className=\"h-full ml-[15px] w-full border-l-2 border-b-2 border-gray-300 rounded-bl-xl\" >\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"absolute left-[15px] top-0 h-1/3 bg-gray-300 w-[2px]\" />\r\n\r\n                    <div className=\"pl-6 w-full\">\r\n                        <CommentInput onReply={handleOnReply} parentCommentId={comment.id} value={replyContent} />\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CommentItem;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,mBAAmB,QAAQ,UAAU;AAC9C,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,GAAGC,IAAA,IAAoE;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,eAAA,EAAAC,oBAAA;EAAA,IAAnE;IAAEC,OAAO;IAAEC,IAAI;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAAd,IAAA;EAC/E,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMqC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEwC,OAAO,EAAEC;EAAa,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACC,QAAQ,CAAC;EACxE,MAAM,CAACH,OAAO,EAAEI,UAAU,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM6C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClCT,cAAc,CAAC,IAAI,CAAC;IACpBG,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMF,QAAQ,CAAC9B,uBAAuB,CAAC;MAAEuC,SAAS,EAAE1B,OAAO,CAAC2B;IAAG,CAAC,CAAC,CAAC,CAC7DC,MAAM,CAAC,CAAC,CACRC,IAAI,CAAEC,IAAI,IAAK;MACZX,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,CACDY,KAAK,CAAEC,KAAK,IAAK;MACdb,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACV,CAAC;EAEDxC,SAAS,CAAC,MAAM;IACZ,IAAIsD,KAAK,CAACC,OAAO,CAACb,YAAY,CAACrB,OAAO,CAAC2B,EAAE,CAAC,CAAC,EAAE;MACzCH,UAAU,CAACH,YAAY,CAACrB,OAAO,CAAC2B,EAAE,CAAC,CAAC;IACxC;EACJ,CAAC,EAAE,CAACN,YAAY,CAACrB,OAAO,CAAC2B,EAAE,CAAC,CAAC,CAAC;EAE9B,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACvB3B,YAAY,CAACR,OAAO,CAAC2B,EAAE,CAAC;IACxBjB,cAAc,CAACV,OAAO,CAACoC,OAAO,CAAC;EACnC,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxBzB,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC,EAAE,CAAC;EACvB,CAAC;EAED,MAAMwB,YAAY,GAAIF,OAAO,IAAK;IAC9B,IAAIA,OAAO,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAC3BpC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGH,OAAO,CAAC2B,EAAE,EAAES,OAAO,CAAC;IAC/B5B,YAAY,CAAC,IAAI,CAAC;IAClBE,cAAc,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAM8B,YAAY,GAAGA,CAAA,KAAM;IACvBhC,YAAY,CAAC,IAAI,CAAC;IAClBE,cAAc,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAM+B,aAAa,GAAGA,CAACL,OAAO,EAAEM,eAAe,KAAK;IAChD,IAAIN,OAAO,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAC3BlC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG+B,OAAO,EAAEM,eAAe,CAAC;IACnC9B,YAAY,CAAC,KAAK,CAAC;IACnBE,eAAe,CAAC,EAAE,CAAC;EACvB,CAAC;EAED,MAAM6B,OAAO,GAAG,CAAA1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,EAAE,MAAK3B,OAAO,CAAC4C,MAAM;EAC3C,MAAMC,QAAQ,GAAG7C,OAAO,CAACC,IAAI,IAAI,CAAC,CAAC;EAEnC,oBACIX,OAAA;IAAKwD,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACjCzD,OAAA;MAAKwD,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC3CzD,OAAA;QAAKwD,SAAS,cAAAE,MAAA,CAAc1C,OAAO,GAAG,KAAK,GAAG,KAAK,uCAAqC;QAAAyC,QAAA,gBAEpFzD,OAAA;UAAKwD,SAAS,KAAAE,MAAA,CAAK1C,OAAO,GAAG,SAAS,GAAG,SAAS,iGAA+F;UAAAyC,QAAA,EAC5IF,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEI,SAAS,gBAChB3D,OAAA;YAAK4D,GAAG,EAAEL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,SAAU;YAACE,GAAG,EAAC,QAAQ;YAACL,SAAS,EAAC;UAAyC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClG,EAAA7D,oBAAA,GAACmD,QAAQ,aAARA,QAAQ,wBAAAlD,mBAAA,GAARkD,QAAQ,CAAEW,SAAS,cAAA7D,mBAAA,uBAAnBA,mBAAA,CAAsB,CAAC,CAAC,cAAAD,oBAAA,cAAAA,oBAAA,GAAI,EAAE,MAAAE,mBAAA,GAAKiD,QAAQ,aAARA,QAAQ,wBAAAhD,kBAAA,GAARgD,QAAQ,CAAEY,QAAQ,cAAA5D,kBAAA,uBAAlBA,kBAAA,CAAqB,CAAC,CAAC,cAAAD,mBAAA,cAAAA,mBAAA,GAAI,EAAE;QAAC;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eAGNjE,OAAA;UAAKwD,SAAS,+CAAAE,MAAA,CAA+ChD,OAAO,CAAC0D,UAAU,GAAG,CAAC,IAAI/C,SAAS,GAAG,OAAO,GAAG,QAAQ;QAAG;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE1H,CAAC,eAGNjE,OAAA;QAAKwD,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACnBzD,OAAA;UAAKwD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACtCzD,OAAA;YAAKwD,SAAS,gDAAAE,MAAA,CAAgDzC,SAAS,KAAKP,OAAO,CAAC2B,EAAE,GAAG,QAAQ,GAAG,OAAO,CAAG;YAAAoB,QAAA,gBAC1GzD,OAAA;cAAGwD,SAAS,yCAAAE,MAAA,CAAyCzC,SAAS,KAAKP,OAAO,CAAC2B,EAAE,GAAG,QAAQ,GAAG,EAAE,CAAG;cAAAoB,QAAA,GAC3FF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEW,SAAS,EAAC,GAAC,EAAC,CAAAX,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEY,QAAQ,IAAG,GAAG,eAC/CnE,OAAA;gBAAMwD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAClC,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,QAAQ,MAAK,KAAK,KAAIzD,KAAK,aAALA,KAAK,wBAAAJ,eAAA,GAALI,KAAK,CAAG,WAAW,CAAC,cAAAJ,eAAA,wBAAAC,oBAAA,GAApBD,eAAA,CAAsB8D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,MAAKjB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,QAAQ,EAAC,cAAA5D,oBAAA,uBAA9DA,oBAAA,CAAgEgE,WAAW;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEHhD,SAAS,KAAKP,OAAO,CAAC2B,EAAE,gBACrBrC,OAAA,CAACJ,YAAY;cAAC8E,MAAM,EAAE1B,YAAa;cAAC2B,KAAK,EAAExD;YAAY;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE1DjE,OAAA;cAAGwD,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAE/C,OAAO,CAACoC;YAAO;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAChF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAELZ,OAAO,IAAIpC,SAAS,KAAKP,OAAO,CAAC2B,EAAE,iBAChCrC,OAAA;YAAKwD,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAChEzD,OAAA;cAAQ4E,OAAO,EAAE/B,YAAa;cAACW,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvEjE,OAAA;cAAQ4E,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGJ,OAAO,CAAC2B,EAAE,CAAE;cAACmB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CACR,EAEAZ,OAAO,IAAIpC,SAAS,KAAKP,OAAO,CAAC2B,EAAE,iBAChCrC,OAAA;YAAKwD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,eAC7CzD,OAAA;cAAQ4E,OAAO,EAAE1B,YAAa;cAACM,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAENjE,OAAA;UAAKwD,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACtEzD,OAAA;YAAAyD,QAAA,EAAOjE,mBAAmB,CAAC,IAAIqF,IAAI,CAACnE,OAAO,CAACoE,SAAS,CAAC,EAAE;cAAEC,SAAS,EAAE,IAAI;cAAEC,MAAM,EAAEvF;YAAG,CAAC;UAAC;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChGjE,OAAA;YAAQ4E,OAAO,EAAEA,CAAA,KAAM7B,aAAa,CAAC,CAAE;YAACS,SAAS,qBAAAE,MAAA,CAAqB1C,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAG;YAAAyC,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACL,CAACxC,WAAW,iBACTzB,OAAA;MAAKwD,SAAS,yCAAAE,MAAA,CAAyChD,OAAO,CAAC0D,UAAU,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ,CAAG;MAAAX,QAAA,gBAChGzD,OAAA;QAAKwD,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACtCzD,OAAA;UAAKwD,SAAS,EAAC;QAA6E;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNjE,OAAA;QAAKwD,SAAS,6DAAAE,MAAA,CAA6DrC,SAAS,GAAG,QAAQ,GAAG,OAAO;MAAG;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/GjE,OAAA;QACI4E,OAAO,EAAEzC,iBAAkB;QAC3BqB,SAAS,EAAC,2CAA2C;QAAAC,QAAA,GAAE/C,OAAO,CAAC0D,UAAU,EAAC,qBAAS;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CACR,EACAnC,OAAO,CAACmD,MAAM,GAAG,CAAC,IAAIxD,WAAW,IAC9BK,OAAO,CAACoD,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACrBpF,OAAA;MAAKwD,SAAS,yCAAAE,MAAA,CAAyChD,OAAO,CAAC0D,UAAU,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ,CAAG;MAAAX,QAAA,gBAChGzD,OAAA;QAAKwD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACvCzD,OAAA;UAAKwD,SAAS,EAAC;QAA6E;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEL,CAAC,eACNjE,OAAA;QAAKwD,SAAS,KAAAE,MAAA,CAAK5B,OAAO,CAACmD,MAAM,KAAKG,KAAK,GAAG,CAAC,IAAI,CAAC/D,SAAS,GAAG,OAAO,GAAG,EAAE;MAA2D;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1IjE,OAAA;QAAKwD,SAAS,EAAC,aAAa;QAAAC,QAAA,eACxBzD,OAAA,CAACC,WAAW;UAERS,OAAO,EAAEyE,KAAM;UACfxE,IAAI,EAAEA,IAAK;UACXC,KAAK,EAAEA,KAAM;UACbC,QAAQ,EAAEA,QAAS;UACnBC,QAAQ,EAAEA,QAAS;UACnBC,OAAO,EAAEA,OAAQ;UACjBC,OAAO,EAAE;QAAK,GAPTmE,KAAK,CAAC9C,EAAE;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQhB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,CACJ,EACA5C,SAAS,iBACNrB,OAAA;MAAKwD,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACrDzD,OAAA;QAAKwD,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eAC/CzD,OAAA;UAAKwD,SAAS,EAAC;QAA6E;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNjE,OAAA;QAAKwD,SAAS,EAAC;MAAsD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAExEjE,OAAA;QAAKwD,SAAS,EAAC,aAAa;QAAAC,QAAA,eACxBzD,OAAA,CAACJ,YAAY;UAACmB,OAAO,EAAEoC,aAAc;UAACC,eAAe,EAAE1C,OAAO,CAAC2B,EAAG;UAACsC,KAAK,EAAEpD;QAAa;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC9D,EAAA,CAzKIF,WAAW;EAAA,QAOIH,WAAW,EAEMJ,WAAW;AAAA;AAAA2F,EAAA,GAT3CpF,WAAW;AA2KjB,eAAeA,WAAW;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}