{"ast": null, "code": "import api from \"./api\";\nexport const getAllQuestionAPI = _ref => {\n  let {\n    search = \"\",\n    page = 1,\n    pageSize = 10,\n    sortOrder = 'asc'\n  } = _ref;\n  return api.get(\"/v1/admin/question\", {\n    params: {\n      search,\n      page,\n      limit: pageSize,\n      sortOrder\n    }\n  });\n};\nexport const deleteQuestionAPI = questionId => {\n  return api.delete(\"/v1/admin/question/\".concat(questionId));\n};\nexport const getExamQuestionsAPI = _ref2 => {\n  let {\n    id,\n    search = \"\",\n    page = 1,\n    pageSize = 10,\n    sortOrder = 'asc'\n  } = _ref2;\n  return api.get(\"/v1/admin/exam/\".concat(id, \"/questions\"), {\n    params: {\n      search,\n      page,\n      limit: pageSize,\n      sortOrder\n    }\n  });\n};\nexport const getPublicExamQuestionsAPI = _ref3 => {\n  let {\n    id\n  } = _ref3;\n  return api.get(\"/v1/user/exam/\".concat(id, \"/questions\"));\n};\nexport const getQuestionByIdAPI = id => {\n  return api.get(\"/v1/admin/question/\".concat(id));\n};\nexport const findQuestionsAPI = search => {\n  return api.get(\"/v1/user/question/search\", {\n    params: {\n      search\n    }\n  });\n};\nexport const putQuestionsExamAPI = async _ref4 => {\n  let {\n    examId,\n    questions\n  } = _ref4;\n  const response = await api.put(\"/v1/admin/exam/\".concat(examId, \"/questions\"), {\n    questions\n  });\n  return response;\n};\nexport const postQuestionAPI = async _ref5 => {\n  let {\n    questionData,\n    statementOptions,\n    questionImage,\n    solutionImage,\n    statementImages,\n    examId = null\n  } = _ref5;\n  // Tạo đối tượng FormData để gửi dữ liệu dạng multipart/form-data\n  const formData = new FormData();\n  if (statementOptions.length === 0) {\n    formData.append(\"data\", JSON.stringify({\n      questionData,\n      examId\n    }));\n  } else {\n    formData.append(\"data\", JSON.stringify({\n      questionData,\n      statementOptions,\n      examId\n    }));\n  }\n\n  // Nếu có file ảnh cho câu hỏi, thêm vào formData\n  if (questionImage) {\n    formData.append(\"questionImage\", questionImage);\n  }\n  if (solutionImage) {\n    formData.append(\"solutionImage\", solutionImage);\n  }\n\n  // Nếu có các file ảnh cho mệnh đề, thêm từng file vào formData\n  if (statementImages && statementImages.length > 0) {\n    statementImages.forEach(file => {\n      if (file !== null) formData.append(\"statementImages\", file);\n    });\n  }\n\n  // Gọi API POST với formData, thiết lập header \"Content-Type\" là multipart/form-data\n  const response = await api.post(\"/v1/admin/question\", formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n  return response.data;\n};\nexport const putImageQuestionAPI = async _ref6 => {\n  let {\n    questionId,\n    questionImage\n  } = _ref6;\n  const formData = new FormData();\n  formData.append(\"questionImage\", questionImage);\n  const response = await api.put(\"/v1/admin/question/\".concat(questionId, \"/image\"), formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n  return response.data;\n};\nexport const putImageSolutionAPI = async _ref7 => {\n  let {\n    questionId,\n    solutionImage\n  } = _ref7;\n  const formData = new FormData();\n  formData.append(\"solutionImage\", solutionImage);\n  const response = await api.put(\"/v1/admin/question/\".concat(questionId, \"/solutionImage\"), formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n  return response.data;\n};\nexport const putStatementImageAPI = async _ref8 => {\n  let {\n    statementId,\n    statementImage\n  } = _ref8;\n  const formData = new FormData();\n  formData.append(\"statementImage\", statementImage);\n  const response = await api.put(\"/v1/admin/statement/\".concat(statementId, \"/image\"), formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    }\n  });\n  return response.data;\n};\nexport const putQuestionAPI = async _ref9 => {\n  let {\n    questionId,\n    questionData,\n    statements\n  } = _ref9;\n  const response = await api.put(\"/v1/admin/question/\".concat(questionId), {\n    questionData,\n    statements\n  });\n  return response.data;\n};", "map": {"version": 3, "names": ["api", "getAllQuestionAPI", "_ref", "search", "page", "pageSize", "sortOrder", "get", "params", "limit", "deleteQuestionAPI", "questionId", "delete", "concat", "getExamQuestionsAPI", "_ref2", "id", "getPublicExamQuestionsAPI", "_ref3", "getQuestionByIdAPI", "findQuestionsAPI", "putQuestionsExamAPI", "_ref4", "examId", "questions", "response", "put", "postQuestionAPI", "_ref5", "questionData", "statementOptions", "questionImage", "solutionImage", "statementImages", "formData", "FormData", "length", "append", "JSON", "stringify", "for<PERSON>ach", "file", "post", "headers", "data", "putImageQuestionAPI", "_ref6", "putImageSolutionAPI", "_ref7", "putStatementImageAPI", "_ref8", "statementId", "statementImage", "putQuestionAPI", "_ref9", "statements"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/services/questionApi.js"], "sourcesContent": ["import api from \"./api\";\r\n\r\nexport const getAllQuestionAPI = ({ search = \"\", page = 1, pageSize = 10, sortOrder = 'asc' }) => {\r\n    return api.get(\"/v1/admin/question\", {\r\n        params: {\r\n            search,\r\n            page,\r\n            limit: pageSize,\r\n            sortOrder,\r\n        }\r\n    });\r\n};\r\n\r\nexport const deleteQuestionAPI = (questionId) => {\r\n    return api.delete(`/v1/admin/question/${questionId}`);\r\n}\r\n\r\nexport const getExamQuestionsAPI = ({ id, search = \"\", page = 1, pageSize = 10, sortOrder = 'asc' }) => {\r\n    return api.get(`/v1/admin/exam/${id}/questions`, {\r\n        params: {\r\n            search,\r\n            page,\r\n            limit: pageSize,\r\n            sortOrder,\r\n        }\r\n    });\r\n}\r\n\r\nexport const getPublicExamQuestionsAPI = ({ id }) => {\r\n    return api.get(`/v1/user/exam/${id}/questions`);\r\n}\r\n\r\nexport const getQuestionByIdAPI = (id) => {\r\n    return api.get(`/v1/admin/question/${id}`);\r\n};\r\n\r\nexport const findQuestionsAPI = (search) => {\r\n    return api.get(\"/v1/user/question/search\", {\r\n        params: {\r\n            search,\r\n        }\r\n    });\r\n}\r\n\r\nexport const putQuestionsExamAPI = async ({ examId, questions }) => {\r\n    const response = await api.put(`/v1/admin/exam/${examId}/questions`, { questions });\r\n    return response;\r\n}\r\n\r\n\r\nexport const postQuestionAPI = async ({ questionData, statementOptions, questionImage, solutionImage, statementImages, examId = null }) => {\r\n    // Tạo đối tượng FormData để gửi dữ liệu dạng multipart/form-data\r\n    const formData = new FormData();\r\n\r\n    if (statementOptions.length === 0) {\r\n        formData.append(\"data\", JSON.stringify({ questionData, examId }));\r\n    } else {\r\n        formData.append(\"data\", JSON.stringify({ questionData, statementOptions, examId }));\r\n    }\r\n\r\n    // Nếu có file ảnh cho câu hỏi, thêm vào formData\r\n    if (questionImage) {\r\n        formData.append(\"questionImage\", questionImage);\r\n    }\r\n\r\n    if (solutionImage) {\r\n        formData.append(\"solutionImage\", solutionImage);\r\n    }\r\n\r\n    // Nếu có các file ảnh cho mệnh đề, thêm từng file vào formData\r\n    if (statementImages && statementImages.length > 0) {\r\n        statementImages.forEach((file) => {\r\n            if (file !== null) formData.append(\"statementImages\", file);\r\n        });\r\n    }\r\n\r\n    // Gọi API POST với formData, thiết lập header \"Content-Type\" là multipart/form-data\r\n    const response = await api.post(\"/v1/admin/question\", formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n    return response.data;\r\n\r\n};\r\n\r\nexport const putImageQuestionAPI = async ({ questionId, questionImage }) => {\r\n    const formData = new FormData();\r\n    formData.append(\"questionImage\", questionImage);\r\n    const response = await api.put(`/v1/admin/question/${questionId}/image`, formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n    return response.data;\r\n}\r\n\r\nexport const putImageSolutionAPI = async ({ questionId, solutionImage }) => {\r\n    const formData = new FormData();\r\n    formData.append(\"solutionImage\", solutionImage);\r\n    const response = await api.put(`/v1/admin/question/${questionId}/solutionImage`, formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n    return response.data;\r\n}\r\n\r\nexport const putStatementImageAPI = async ({ statementId, statementImage }) => {\r\n    const formData = new FormData();\r\n    formData.append(\"statementImage\", statementImage);\r\n    const response = await api.put(`/v1/admin/statement/${statementId}/image`, formData, {\r\n        headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n    });\r\n    return response.data;\r\n}\r\n\r\nexport const putQuestionAPI = async ({ questionId, questionData, statements }) => {\r\n    const response = await api.put(`/v1/admin/question/${questionId}`, {\r\n        questionData,\r\n        statements,\r\n    });\r\n\r\n    return response.data;\r\n};\r\n\r\n\r\n\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,OAAO,MAAMC,iBAAiB,GAAGC,IAAA,IAAiE;EAAA,IAAhE;IAAEC,MAAM,GAAG,EAAE;IAAEC,IAAI,GAAG,CAAC;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG;EAAM,CAAC,GAAAJ,IAAA;EACzF,OAAOF,GAAG,CAACO,GAAG,CAAC,oBAAoB,EAAE;IACjCC,MAAM,EAAE;MACJL,MAAM;MACNC,IAAI;MACJK,KAAK,EAAEJ,QAAQ;MACfC;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMI,iBAAiB,GAAIC,UAAU,IAAK;EAC7C,OAAOX,GAAG,CAACY,MAAM,uBAAAC,MAAA,CAAuBF,UAAU,CAAE,CAAC;AACzD,CAAC;AAED,OAAO,MAAMG,mBAAmB,GAAGC,KAAA,IAAqE;EAAA,IAApE;IAAEC,EAAE;IAAEb,MAAM,GAAG,EAAE;IAAEC,IAAI,GAAG,CAAC;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG;EAAM,CAAC,GAAAS,KAAA;EAC/F,OAAOf,GAAG,CAACO,GAAG,mBAAAM,MAAA,CAAmBG,EAAE,iBAAc;IAC7CR,MAAM,EAAE;MACJL,MAAM;MACNC,IAAI;MACJK,KAAK,EAAEJ,QAAQ;MACfC;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMW,yBAAyB,GAAGC,KAAA,IAAY;EAAA,IAAX;IAAEF;EAAG,CAAC,GAAAE,KAAA;EAC5C,OAAOlB,GAAG,CAACO,GAAG,kBAAAM,MAAA,CAAkBG,EAAE,eAAY,CAAC;AACnD,CAAC;AAED,OAAO,MAAMG,kBAAkB,GAAIH,EAAE,IAAK;EACtC,OAAOhB,GAAG,CAACO,GAAG,uBAAAM,MAAA,CAAuBG,EAAE,CAAE,CAAC;AAC9C,CAAC;AAED,OAAO,MAAMI,gBAAgB,GAAIjB,MAAM,IAAK;EACxC,OAAOH,GAAG,CAACO,GAAG,CAAC,0BAA0B,EAAE;IACvCC,MAAM,EAAE;MACJL;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMkB,mBAAmB,GAAG,MAAAC,KAAA,IAAiC;EAAA,IAA1B;IAAEC,MAAM;IAAEC;EAAU,CAAC,GAAAF,KAAA;EAC3D,MAAMG,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,mBAAAb,MAAA,CAAmBU,MAAM,iBAAc;IAAEC;EAAU,CAAC,CAAC;EACnF,OAAOC,QAAQ;AACnB,CAAC;AAGD,OAAO,MAAME,eAAe,GAAG,MAAAC,KAAA,IAA4G;EAAA,IAArG;IAAEC,YAAY;IAAEC,gBAAgB;IAAEC,aAAa;IAAEC,aAAa;IAAEC,eAAe;IAAEV,MAAM,GAAG;EAAK,CAAC,GAAAK,KAAA;EAClI;EACA,MAAMM,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAE/B,IAAIL,gBAAgB,CAACM,MAAM,KAAK,CAAC,EAAE;IAC/BF,QAAQ,CAACG,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;MAAEV,YAAY;MAAEN;IAAO,CAAC,CAAC,CAAC;EACrE,CAAC,MAAM;IACHW,QAAQ,CAACG,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;MAAEV,YAAY;MAAEC,gBAAgB;MAAEP;IAAO,CAAC,CAAC,CAAC;EACvF;;EAEA;EACA,IAAIQ,aAAa,EAAE;IACfG,QAAQ,CAACG,MAAM,CAAC,eAAe,EAAEN,aAAa,CAAC;EACnD;EAEA,IAAIC,aAAa,EAAE;IACfE,QAAQ,CAACG,MAAM,CAAC,eAAe,EAAEL,aAAa,CAAC;EACnD;;EAEA;EACA,IAAIC,eAAe,IAAIA,eAAe,CAACG,MAAM,GAAG,CAAC,EAAE;IAC/CH,eAAe,CAACO,OAAO,CAAEC,IAAI,IAAK;MAC9B,IAAIA,IAAI,KAAK,IAAI,EAAEP,QAAQ,CAACG,MAAM,CAAC,iBAAiB,EAAEI,IAAI,CAAC;IAC/D,CAAC,CAAC;EACN;;EAEA;EACA,MAAMhB,QAAQ,GAAG,MAAMzB,GAAG,CAAC0C,IAAI,CAAC,oBAAoB,EAAER,QAAQ,EAAE;IAC5DS,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOlB,QAAQ,CAACmB,IAAI;AAExB,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG,MAAAC,KAAA,IAAyC;EAAA,IAAlC;IAAEnC,UAAU;IAAEoB;EAAc,CAAC,GAAAe,KAAA;EACnE,MAAMZ,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACG,MAAM,CAAC,eAAe,EAAEN,aAAa,CAAC;EAC/C,MAAMN,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,uBAAAb,MAAA,CAAuBF,UAAU,aAAUuB,QAAQ,EAAE;IAC/ES,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOlB,QAAQ,CAACmB,IAAI;AACxB,CAAC;AAED,OAAO,MAAMG,mBAAmB,GAAG,MAAAC,KAAA,IAAyC;EAAA,IAAlC;IAAErC,UAAU;IAAEqB;EAAc,CAAC,GAAAgB,KAAA;EACnE,MAAMd,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACG,MAAM,CAAC,eAAe,EAAEL,aAAa,CAAC;EAC/C,MAAMP,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,uBAAAb,MAAA,CAAuBF,UAAU,qBAAkBuB,QAAQ,EAAE;IACvFS,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOlB,QAAQ,CAACmB,IAAI;AACxB,CAAC;AAED,OAAO,MAAMK,oBAAoB,GAAG,MAAAC,KAAA,IAA2C;EAAA,IAApC;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAAF,KAAA;EACtE,MAAMhB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACG,MAAM,CAAC,gBAAgB,EAAEe,cAAc,CAAC;EACjD,MAAM3B,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,wBAAAb,MAAA,CAAwBsC,WAAW,aAAUjB,QAAQ,EAAE;IACjFS,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOlB,QAAQ,CAACmB,IAAI;AACxB,CAAC;AAED,OAAO,MAAMS,cAAc,GAAG,MAAAC,KAAA,IAAoD;EAAA,IAA7C;IAAE3C,UAAU;IAAEkB,YAAY;IAAE0B;EAAW,CAAC,GAAAD,KAAA;EACzE,MAAM7B,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,uBAAAb,MAAA,CAAuBF,UAAU,GAAI;IAC/DkB,YAAY;IACZ0B;EACJ,CAAC,CAAC;EAEF,OAAO9B,QAAQ,CAACmB,IAAI;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}