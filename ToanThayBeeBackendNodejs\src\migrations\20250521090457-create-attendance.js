'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('attendance', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'user', // Tên bảng user
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      lessonId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'lesson', // Tên bảng lesson
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      status: {
        type: Sequelize.ENUM('present', 'absent', 'late'),
        allowNull: false,
        defaultValue: 'present',
      },
      attendanceTime: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      note: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Tạo UNIQUE constraint để đảm bảo mỗi học sinh chỉ điểm danh 1 lần mỗi buổi
    await queryInterface.addConstraint('attendance', {
      fields: ['userId', 'lessonId'],
      type: 'unique',
      name: 'unique_attendance_per_user_per_lesson',
    });
  },

  async down(queryInterface, Sequelize) {
    // Xóa bảng và ENUM khi rollback
    await queryInterface.dropTable('attendance');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_attendance_status";'); // Chỉ PostgreSQL
  },
};
