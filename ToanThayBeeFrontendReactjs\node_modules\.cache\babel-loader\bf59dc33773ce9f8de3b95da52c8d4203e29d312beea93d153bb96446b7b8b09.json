{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\SortableStatementsContainer.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/questionsExam/questionsExamSlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableStatementsContainer = _ref => {\n  _s();\n  let {\n    question\n  } = _ref;\n  const dispatch = useDispatch();\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n  const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    console.log('Statement drag end:', {\n      activeId: active.id,\n      overId: over === null || over === void 0 ? void 0 : over.id\n    });\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      // Sử dụng index trực tiếp từ id (vì id có format \"statementId-index\" hoặc chỉ là index)\n      const oldIndex = parseInt(active.id.toString().split('-').pop()) || question.statements.findIndex((item, idx) => \"\".concat(item.id || idx) === active.id);\n      const newIndex = parseInt(over.id.toString().split('-').pop()) || question.statements.findIndex((item, idx) => \"\".concat(item.id || idx) === over.id);\n      if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {\n        dispatch(reorderStatements({\n          questionId: question.id,\n          oldIndex,\n          newIndex\n        }));\n      }\n    }\n  };\n  const getPrefix = index => {\n    if (question.typeOfQuestion === \"TN\") {\n      return prefixTN[index] || \"\".concat(String.fromCharCode(65 + index), \".\");\n    } else if (question.typeOfQuestion === \"DS\") {\n      return prefixDS[index] || \"\".concat(String.fromCharCode(97 + index), \")\");\n    }\n    return \"\".concat(index + 1, \".\");\n  };\n  if (question.typeOfQuestion === \"TLN\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-800 mt-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"\\u0110\\xE1p \\xE1n: \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: question.correctAnswer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this);\n  }\n  if (!question.statements || question.statements.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2\",\n    children: /*#__PURE__*/_jsxDEV(DndContext, {\n      sensors: sensors,\n      collisionDetection: closestCenter,\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(SortableContext, {\n        items: question.statements.map((item, idx) => \"\".concat(item.id || idx)),\n        strategy: verticalListSortingStrategy,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1\",\n          children: question.statements.map((item, idx) => /*#__PURE__*/_jsxDEV(SortableStatementItem, {\n            statement: item,\n            index: idx,\n            prefix: getPrefix(idx),\n            isCorrect: item.isCorrect,\n            questionType: question.typeOfQuestion\n          }, \"\".concat(item.id || idx), false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableStatementsContainer, \"fKWeCsq/OsPww/eQUkThDyU1Mpg=\", false, function () {\n  return [useDispatch, useSensors, useSensor, useSensor];\n});\n_c = SortableStatementsContainer;\nexport default SortableStatementsContainer;\nvar _c;\n$RefreshReg$(_c, \"SortableStatementsContainer\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "reorderStatements", "SortableStatementItem", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SortableStatementsContainer", "_ref", "_s", "question", "dispatch", "sensors", "activationConstraint", "distance", "coordinateGetter", "prefixTN", "prefixDS", "handleDragEnd", "event", "active", "over", "console", "log", "activeId", "id", "overId", "oldIndex", "parseInt", "toString", "split", "pop", "statements", "findIndex", "item", "idx", "concat", "newIndex", "questionId", "getPrefix", "index", "typeOfQuestion", "String", "fromCharCode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "length", "collisionDetection", "onDragEnd", "items", "map", "strategy", "statement", "prefix", "isCorrect", "questionType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/SortableStatementsContainer.jsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/questionsExam/questionsExamSlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport {\n    DndContext,\n    closestCenter,\n    KeyboardSensor,\n    PointerSensor,\n    useSensor,\n    useSensors,\n} from '@dnd-kit/core';\nimport {\n    SortableContext,\n    sortableKeyboardCoordinates,\n    verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport LatexRenderer from \"../latex/RenderLatex\";\n\nconst SortableStatementsContainer = ({ question }) => {\n    const dispatch = useDispatch();\n\n    const sensors = useSensors(\n        useSensor(PointerSensor, {\n            activationConstraint: {\n                distance: 8,\n            },\n        }),\n        useSensor(KeyboardSensor, {\n            coordinateGetter: sortableKeyboardCoordinates,\n        })\n    );\n\n    const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n    const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n\n    const handleDragEnd = (event) => {\n        const { active, over } = event;\n        console.log('Statement drag end:', { activeId: active.id, overId: over?.id });\n\n        if (active.id !== over?.id) {\n            // Sử dụng index trực tiếp từ id (vì id có format \"statementId-index\" hoặc chỉ là index)\n            const oldIndex = parseInt(active.id.toString().split('-').pop()) ||\n                            question.statements.findIndex((item, idx) => `${item.id || idx}` === active.id);\n            const newIndex = parseInt(over.id.toString().split('-').pop()) ||\n                           question.statements.findIndex((item, idx) => `${item.id || idx}` === over.id);\n\n            if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {\n                dispatch(reorderStatements({\n                    questionId: question.id,\n                    oldIndex,\n                    newIndex\n                }));\n            }\n        }\n    };\n\n    const getPrefix = (index) => {\n        if (question.typeOfQuestion === \"TN\") {\n            return prefixTN[index] || `${String.fromCharCode(65 + index)}.`;\n        } else if (question.typeOfQuestion === \"DS\") {\n            return prefixDS[index] || `${String.fromCharCode(97 + index)})`;\n        }\n        return `${index + 1}.`;\n    };\n\n    if (question.typeOfQuestion === \"TLN\") {\n        return (\n            <div className=\"text-gray-800 mt-2\">\n                <span className=\"font-semibold\">Đáp án: </span>\n                <span>{question.correctAnswer}</span>\n            </div>\n        );\n    }\n\n    if (!question.statements || question.statements.length === 0) {\n        return null;\n    }\n\n    return (\n        <div className=\"mt-2\">\n            <DndContext\n                sensors={sensors}\n                collisionDetection={closestCenter}\n                onDragEnd={handleDragEnd}\n            >\n                <SortableContext\n                    items={question.statements.map((item, idx) => `${item.id || idx}`)}\n                    strategy={verticalListSortingStrategy}\n                >\n                    <div className=\"space-y-1\">\n                        {question.statements.map((item, idx) => (\n                            <SortableStatementItem\n                                key={`${item.id || idx}`}\n                                statement={item}\n                                index={idx}\n                                prefix={getPrefix(idx)}\n                                isCorrect={item.isCorrect}\n                                questionType={question.typeOfQuestion}\n                            />\n                        ))}\n                    </div>\n                </SortableContext>\n            </DndContext>\n        </div>\n    );\n};\n\nexport default SortableStatementsContainer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAC1B,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,2BAA2B,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC7C,MAAMG,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9B,MAAMqB,OAAO,GAAGZ,UAAU,CACtBD,SAAS,CAACD,aAAa,EAAE;IACrBe,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACFf,SAAS,CAACF,cAAc,EAAE;IACtBkB,gBAAgB,EAAEb;EACtB,CAAC,CACL,CAAC;EAED,MAAMc,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAC9BG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MAAEC,QAAQ,EAAEJ,MAAM,CAACK,EAAE;MAAEC,MAAM,EAAEL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI;IAAG,CAAC,CAAC;IAE7E,IAAIL,MAAM,CAACK,EAAE,MAAKJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,EAAE,GAAE;MACxB;MACA,MAAME,QAAQ,GAAGC,QAAQ,CAACR,MAAM,CAACK,EAAE,CAACI,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,IAChDrB,QAAQ,CAACsB,UAAU,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK,GAAAC,MAAA,CAAGF,IAAI,CAACT,EAAE,IAAIU,GAAG,MAAOf,MAAM,CAACK,EAAE,CAAC;MAC/F,MAAMY,QAAQ,GAAGT,QAAQ,CAACP,IAAI,CAACI,EAAE,CAACI,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,IAC/CrB,QAAQ,CAACsB,UAAU,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK,GAAAC,MAAA,CAAGF,IAAI,CAACT,EAAE,IAAIU,GAAG,MAAOd,IAAI,CAACI,EAAE,CAAC;MAE5F,IAAIE,QAAQ,KAAK,CAAC,CAAC,IAAIU,QAAQ,KAAK,CAAC,CAAC,IAAIV,QAAQ,KAAKU,QAAQ,EAAE;QAC7D1B,QAAQ,CAAClB,iBAAiB,CAAC;UACvB6C,UAAU,EAAE5B,QAAQ,CAACe,EAAE;UACvBE,QAAQ;UACRU;QACJ,CAAC,CAAC,CAAC;MACP;IACJ;EACJ,CAAC;EAED,MAAME,SAAS,GAAIC,KAAK,IAAK;IACzB,IAAI9B,QAAQ,CAAC+B,cAAc,KAAK,IAAI,EAAE;MAClC,OAAOzB,QAAQ,CAACwB,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE,CAAC,MAAM,IAAI9B,QAAQ,CAAC+B,cAAc,KAAK,IAAI,EAAE;MACzC,OAAOxB,QAAQ,CAACuB,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE;IACA,UAAAJ,MAAA,CAAUI,KAAK,GAAG,CAAC;EACvB,CAAC;EAED,IAAI9B,QAAQ,CAAC+B,cAAc,KAAK,KAAK,EAAE;IACnC,oBACInC,OAAA;MAAKsC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBAC/BvC,OAAA;QAAMsC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/C3C,OAAA;QAAAuC,QAAA,EAAOnC,QAAQ,CAACwC;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEd;EAEA,IAAI,CAACvC,QAAQ,CAACsB,UAAU,IAAItB,QAAQ,CAACsB,UAAU,CAACmB,MAAM,KAAK,CAAC,EAAE;IAC1D,OAAO,IAAI;EACf;EAEA,oBACI7C,OAAA;IAAKsC,SAAS,EAAC,MAAM;IAAAC,QAAA,eACjBvC,OAAA,CAACX,UAAU;MACPiB,OAAO,EAAEA,OAAQ;MACjBwC,kBAAkB,EAAExD,aAAc;MAClCyD,SAAS,EAAEnC,aAAc;MAAA2B,QAAA,eAEzBvC,OAAA,CAACL,eAAe;QACZqD,KAAK,EAAE5C,QAAQ,CAACsB,UAAU,CAACuB,GAAG,CAAC,CAACrB,IAAI,EAAEC,GAAG,QAAAC,MAAA,CAAQF,IAAI,CAACT,EAAE,IAAIU,GAAG,CAAE,CAAE;QACnEqB,QAAQ,EAAErD,2BAA4B;QAAA0C,QAAA,eAEtCvC,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACrBnC,QAAQ,CAACsB,UAAU,CAACuB,GAAG,CAAC,CAACrB,IAAI,EAAEC,GAAG,kBAC/B7B,OAAA,CAACZ,qBAAqB;YAElB+D,SAAS,EAAEvB,IAAK;YAChBM,KAAK,EAAEL,GAAI;YACXuB,MAAM,EAAEnB,SAAS,CAACJ,GAAG,CAAE;YACvBwB,SAAS,EAAEzB,IAAI,CAACyB,SAAU;YAC1BC,YAAY,EAAElD,QAAQ,CAAC+B;UAAe,MAAAL,MAAA,CAL9BF,IAAI,CAACT,EAAE,IAAIU,GAAG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMzB,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEd,CAAC;AAACxC,EAAA,CAvFIF,2BAA2B;EAAA,QACZhB,WAAW,EAEZS,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAA8D,EAAA,GATXtD,2BAA2B;AAyFjC,eAAeA,2BAA2B;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}