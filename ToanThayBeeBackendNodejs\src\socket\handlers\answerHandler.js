import db from '../../models/index.js';
import { EVENTS, ANSWER_TYPES, PREFIX_TN, ROOMS } from '../constants.js';

/**
 * Handle select_answer event
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleSelectAnswer = async (socket, io, { attemptId, questionId, answerContent, studentId, type, examId, name }) => {
  try {
    const existing = await db.Answer.findOne({ where: { attemptId, questionId } });
    let answer = '';
    let isCorrect = false;

    if (type === ANSWER_TYPES.MULTIPLE_CHOICE) {
      const statement = await db.Statement.findByPk(answerContent);
      answer = PREFIX_TN[statement.order - 1];
      isCorrect = statement?.isCorrect || false;
    } else if (type === ANSWER_TYPES.TRUE_FALSE) {
      if (!Array.isArray(answerContent)) {
        throw new Error("Answer content phải là mảng các statement");
      }

      const statementIds = answerContent.map(item => item.statementId);
      const statements = await db.Statement.findAll({
        where: { id: statementIds },
        attributes: ['id', 'order', 'isCorrect']
      });

      const enrichedAnswerContent = answerContent.map(item => {
        const stmt = statements.find(s => s.id === item.statementId);
        return {
          ...item,
          order: stmt?.order || 0,
          isCorrect: stmt?.isCorrect
        };
      }).sort((a, b) => a.order - b.order);

      answer = '';
      let allCorrect = true;

      for (const item of enrichedAnswerContent) {
        answer += item.answer ? 'Đ ' : 'S ';
        if (item.isCorrect !== item.answer) {
          allCorrect = false;
        }
      }

      isCorrect = allCorrect;
    } else if (type === ANSWER_TYPES.SHORT_ANSWER) {
      const question = await db.Question.findByPk(questionId);
      const formattedAnswer = answerContent.trim().replace(',', '.');
      isCorrect = question?.correctAnswer.trim().replace(',', '.') === formattedAnswer;
      answer = formattedAnswer;
      // console.log("Short answer content:", answerContent, "Formatted:", formattedAnswer);
    }

    const formattedAnswerContent = type === ANSWER_TYPES.TRUE_FALSE ? JSON.stringify(answerContent) : type === ANSWER_TYPES.SHORT_ANSWER ? answerContent.trim().replace(',', '.') : answerContent;

    if (existing) {
      await db.Answer.update(
        {
          answerContent: formattedAnswerContent,
          result: isCorrect
        },
        { where: { id: existing.id } }
      );
    } else {
      await db.Answer.create({
        attemptId,
        questionId,
        answerContent: formattedAnswerContent,
        studentId,
        result: isCorrect
      });
    }

    const isDSFullyAnswered = type === ANSWER_TYPES.TRUE_FALSE && Array.isArray(answerContent) && answerContent.length >= 4;

    if (type !== ANSWER_TYPES.TRUE_FALSE || isDSFullyAnswered) {
      io.to(`${ROOMS.ADMIN_EXAM}${examId}`).emit(EVENTS.ADMIN_STUDENT_ANSWER, {
        studentId,
        attemptId,
        questionId,
        answerContent: answer,
        isCorrect,
        type,
        name,
        timestamp: new Date().toISOString(),
      });
    }

    if (type === ANSWER_TYPES.TRUE_FALSE) {
      if (!isDSFullyAnswered) return;
    }

    socket.emit(EVENTS.ANSWER_SAVED, {
      questionId,
      answerContent,
      attemptId,
    });

    // console.log("Đã gửi đáp án cho admin:", studentId, questionId, answerContent, isCorrect);

  } catch (err) {
    console.error("Lỗi khi ghi đáp án:", err);
    socket.emit(EVENTS.ANSWER_ERROR, { message: "Không thể lưu đáp án", questionId });
  }
};

/**
 * Handle calculate_score event
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleCalculateScore = async (socket, io, { attemptId, answers, examId, student }) => {
  try {
    const t = await db.sequelize.transaction();

    // Lấy attempt hiện tại
    const attempt = await db.StudentExamAttempt.findByPk(attemptId, { transaction: t });
    if (!attempt) {
      await t.rollback();
      return socket.emit(EVENTS.SCORE_CALCULATION_ERROR, { message: "Không tìm thấy lượt làm bài." });
    }

    if (attempt.endTime) {
      await t.rollback();
      return socket.emit(EVENTS.SCORE_CALCULATION_ERROR, { message: "Bài thi đã kết thúc." });
    }

    // Lấy tất cả questionIds và statementIds cần thiết
    const questionIds = [];
    const tnStatementIds = [];
    const dsStatementIdsMap = {};

    for (const answer of answers) {
      const { questionId, typeOfQuestion, answerContent } = answer;
      questionIds.push(questionId);

      if (typeOfQuestion === ANSWER_TYPES.MULTIPLE_CHOICE && answerContent) {
        tnStatementIds.push(answerContent);
      } else if (typeOfQuestion === ANSWER_TYPES.TRUE_FALSE && answerContent) {
        const answersDS = typeof answerContent === 'string' ?
          JSON.parse(answerContent) : answerContent;

        if (Array.isArray(answersDS)) {
          dsStatementIdsMap[questionId] = answersDS.map(a => a.statementId);
        }
      }
    }

    // Lấy tất cả câu hỏi trong một lần truy vấn
    const questions = await db.Question.findAll({
      where: { id: questionIds },
      transaction: t
    });

    // Tạo map câu hỏi để truy cập nhanh
    const questionMap = {};
    questions.forEach(q => {
      questionMap[q.id] = q;
    });

    // Lấy tất cả statements cho câu hỏi trắc nghiệm
    const allStatementIds = [...tnStatementIds, ...Object.values(dsStatementIdsMap).flat()];
    const statements = await db.Statement.findAll({
      where: { id: allStatementIds },
      transaction: t
    });

    // Tạo map statements để truy cập nhanh
    const statementMap = {};
    statements.forEach(s => {
      statementMap[s.id] = s;
    });

    // Tính điểm
    let totalScore = 0;

    for (const answer of answers) {
      const { questionId, typeOfQuestion, answerContent } = answer;
      const question = questionMap[questionId];
      if (!question) continue;

      if (typeOfQuestion === ANSWER_TYPES.MULTIPLE_CHOICE) {
        const statement = statementMap[answerContent];
        if (statement && statement.isCorrect) {
          totalScore += 0.25;
        }
      } else if (typeOfQuestion === ANSWER_TYPES.SHORT_ANSWER) {
        const formattedAnswer = answerContent.trim().replace(',', '.');
        if (question.correctAnswer === formattedAnswer) {
          totalScore += 0.5;
        }
      } else if (typeOfQuestion === ANSWER_TYPES.TRUE_FALSE) {
        let count = 0;
        if (!answerContent || (Array.isArray(answerContent) && answerContent.length === 0)) continue;

        const answersDS = typeof answerContent === 'string' ?
          JSON.parse(answerContent) : answerContent;

        for (const answerDS of answersDS || []) {
          const statement = statementMap[answerDS.statementId];
          if (statement && statement.isCorrect === answerDS.answer) {
            count++;
          }
        }

        // Tính điểm dựa vào số lượng đúng
        if (count === 1) totalScore += 0.1;
        else if (count === 2) totalScore += 0.25;
        else if (count === 3) totalScore += 0.5;
        else if (count >= 4) totalScore += 1.0;
      }
    }

    // Cập nhật điểm vào attempt (không cập nhật endTime)
    attempt.score = parseFloat(totalScore.toFixed(2));
    await attempt.save({ transaction: t });

    io.to(`${ROOMS.ADMIN_EXAM}${examId}`).emit(EVENTS.ADMIN_SCORE_CALCULATED, {
      attempt,
      student
    });

    // console.log("Đã tính điểm:", attemptId, attempt.score);

    await t.commit();

  } catch (err) {
    console.error("Lỗi khi tính điểm:", err);
    socket.emit(EVENTS.SCORE_CALCULATION_ERROR, { message: "Không thể tính điểm", error: err.message });
  }
};
