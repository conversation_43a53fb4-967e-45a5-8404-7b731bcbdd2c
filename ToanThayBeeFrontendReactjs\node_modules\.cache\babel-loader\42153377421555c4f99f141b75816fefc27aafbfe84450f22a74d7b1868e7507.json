{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nexport const postExam = createAsyncThunk(\"addExam/postExam\", async (_ref, _ref2) => {\n  let {\n    examData,\n    examImage,\n    questions,\n    questionImages,\n    statementImages,\n    solutionImages,\n    examFile\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, examApi.postExamAPI, {\n    examData,\n    examImage,\n    questions,\n    questionImages,\n    statementImages,\n    solutionImages,\n    examFile\n  }, () => {}, true, false);\n});\nconst addExamSlice = createSlice({\n  name: \"addExam\",\n  initialState: {\n    loading: false,\n    step: 1,\n    examData: {\n      name: \"\",\n      typeOfExam: null,\n      class: null,\n      chapter: null,\n      year: null,\n      description: \"\",\n      testDuration: null,\n      passRate: null,\n      solutionUrl: \"\",\n      imageUrl: \"\",\n      public: false,\n      isClassroomExam: false\n    },\n    examImage: null,\n    questions: [],\n    questionImages: [],\n    statementImages: [],\n    solutionImages: [],\n    examFile: null\n  },\n  reducers: {\n    nextStep: state => {\n      state.step++;\n    },\n    prevStep: state => {\n      state.step--;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setExamData: (state, action) => {\n      state.examData = action.payload;\n    },\n    resetData: state => {\n      state.examData = {\n        name: \"\",\n        typeOfExam: null,\n        class: null,\n        chapter: null,\n        year: null,\n        description: \"\",\n        testDuration: null,\n        passRate: null,\n        solutionUrl: \"\",\n        imageUrl: \"\",\n        public: false,\n        isClassroomExam: false\n      };\n      state.examImage = null;\n      state.questions = [];\n      state.questionImages = [];\n      state.statementImages = [];\n      state.solutionImages = [];\n      state.examFile = null;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(postExam.pending, state => {\n      state.loading = true;\n    }).addCase(postExam.fulfilled, state => {\n      state.loading = false;\n    }).addCase(postExam.rejected, state => {\n      state.loading = false;\n    });\n  }\n});\nexport const {\n  setLoading,\n  resetData,\n  nextStep,\n  prevStep\n} = addExamSlice.actions;\nexport default addExamSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postExam", "_ref", "_ref2", "examData", "examImage", "questions", "questionImages", "statementImages", "solutionImages", "examFile", "dispatch", "postExamAPI", "addExamSlice", "name", "initialState", "loading", "step", "typeOfExam", "class", "chapter", "year", "description", "testDuration", "passRate", "solutionUrl", "imageUrl", "public", "isClassroomExam", "reducers", "nextStep", "state", "prevStep", "setLoading", "action", "payload", "setExamData", "resetData", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/addExam/addExamSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\n\r\nexport const postExam = createAsyncThunk(\r\n    \"addExam/postExam\",\r\n    async ({ examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.postExamAPI, { examData, examImage, questions, questionImages, statementImages, solutionImages, examFile }, () => { }, true, false);\r\n    }\r\n);\r\n\r\nconst addExamSlice = createSlice({\r\n    name: \"addExam\",\r\n    initialState: {\r\n        loading: false,\r\n        step: 1,\r\n        examData:\r\n        {\r\n            name: \"\",\r\n            typeOfExam: null,\r\n            class: null,\r\n            chapter: null,\r\n            year: null,\r\n            description: \"\",\r\n            testDuration: null,\r\n            passRate: null,\r\n            solutionUrl: \"\",\r\n            imageUrl: \"\",\r\n            public: false,\r\n            isClassroomExam: false,\r\n        },\r\n        examImage: null,\r\n        questions: [],\r\n        questionImages: [],\r\n        statementImages: [],\r\n        solutionImages: [],\r\n        examFile: null,\r\n    },\r\n    reducers: {\r\n        nextStep: (state) => {\r\n            state.step++;\r\n        },\r\n        prevStep: (state) => {\r\n            state.step--;\r\n        },\r\n        setLoading: (state, action) => {\r\n            state.loading = action.payload;\r\n        },\r\n        setExamData: (state, action) => {\r\n            state.examData = action.payload;\r\n        },\r\n        resetData: (state) => {\r\n            state.examData = {\r\n                name: \"\",\r\n                typeOfExam: null,\r\n                class: null,\r\n                chapter: null,\r\n                year: null,\r\n                description: \"\",\r\n                testDuration: null,\r\n                passRate: null,\r\n                solutionUrl: \"\",\r\n                imageUrl: \"\",\r\n                public: false,\r\n                isClassroomExam: false,\r\n            };\r\n            state.examImage = null;\r\n            state.questions = [];\r\n            state.questionImages = [];\r\n            state.statementImages = [];\r\n            state.solutionImages = [];\r\n            state.examFile = null;\r\n        }\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(postExam.pending, (state) => {\r\n                state.loading = true;\r\n            })\r\n            .addCase(postExam.fulfilled, (state) => {\r\n                state.loading = false;\r\n            })\r\n            .addCase(postExam.rejected, (state) => {\r\n                state.loading = false;\r\n            })\r\n    }\r\n});\r\n\r\nexport const { setLoading, resetData, nextStep, prevStep } = addExamSlice.actions;\r\nexport default addExamSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD;AACA,SAASC,UAAU,QAAQ,wBAAwB;AAEnD,OAAO,MAAMC,QAAQ,GAAGH,gBAAgB,CACpC,kBAAkB,EAClB,OAAAI,IAAA,EAAAC,KAAA,KAAuH;EAAA,IAAhH;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,SAAS;IAAEC,cAAc;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAS,CAAC,GAAAR,IAAA;EAAA,IAAE;IAAES;EAAS,CAAC,GAAAR,KAAA;EAC9G,OAAO,MAAMH,UAAU,CAACW,QAAQ,EAAEZ,OAAO,CAACa,WAAW,EAAE;IAAER,QAAQ;IAAEC,SAAS;IAAEC,SAAS;IAAEC,cAAc;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAS,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACjL,CACJ,CAAC;AAED,MAAMG,YAAY,GAAGhB,WAAW,CAAC;EAC7BiB,IAAI,EAAE,SAAS;EACfC,YAAY,EAAE;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,CAAC;IACPb,QAAQ,EACR;MACIU,IAAI,EAAE,EAAE;MACRI,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,KAAK;MACbC,eAAe,EAAE;IACrB,CAAC;IACDvB,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE;EACd,CAAC;EACDmB,QAAQ,EAAE;IACNC,QAAQ,EAAGC,KAAK,IAAK;MACjBA,KAAK,CAACd,IAAI,EAAE;IAChB,CAAC;IACDe,QAAQ,EAAGD,KAAK,IAAK;MACjBA,KAAK,CAACd,IAAI,EAAE;IAChB,CAAC;IACDgB,UAAU,EAAEA,CAACF,KAAK,EAAEG,MAAM,KAAK;MAC3BH,KAAK,CAACf,OAAO,GAAGkB,MAAM,CAACC,OAAO;IAClC,CAAC;IACDC,WAAW,EAAEA,CAACL,KAAK,EAAEG,MAAM,KAAK;MAC5BH,KAAK,CAAC3B,QAAQ,GAAG8B,MAAM,CAACC,OAAO;IACnC,CAAC;IACDE,SAAS,EAAGN,KAAK,IAAK;MAClBA,KAAK,CAAC3B,QAAQ,GAAG;QACbU,IAAI,EAAE,EAAE;QACRI,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,IAAI;QACVC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,IAAI;QAClBC,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,KAAK;QACbC,eAAe,EAAE;MACrB,CAAC;MACDG,KAAK,CAAC1B,SAAS,GAAG,IAAI;MACtB0B,KAAK,CAACzB,SAAS,GAAG,EAAE;MACpByB,KAAK,CAACxB,cAAc,GAAG,EAAE;MACzBwB,KAAK,CAACvB,eAAe,GAAG,EAAE;MAC1BuB,KAAK,CAACtB,cAAc,GAAG,EAAE;MACzBsB,KAAK,CAACrB,QAAQ,GAAG,IAAI;IACzB;EACJ,CAAC;EACD4B,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAACvC,QAAQ,CAACwC,OAAO,EAAGV,KAAK,IAAK;MAClCA,KAAK,CAACf,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDwB,OAAO,CAACvC,QAAQ,CAACyC,SAAS,EAAGX,KAAK,IAAK;MACpCA,KAAK,CAACf,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDwB,OAAO,CAACvC,QAAQ,CAAC0C,QAAQ,EAAGZ,KAAK,IAAK;MACnCA,KAAK,CAACf,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEiB,UAAU;EAAEI,SAAS;EAAEP,QAAQ;EAAEE;AAAS,CAAC,GAAGnB,YAAY,CAAC+B,OAAO;AACjF,eAAe/B,YAAY,CAACgC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}