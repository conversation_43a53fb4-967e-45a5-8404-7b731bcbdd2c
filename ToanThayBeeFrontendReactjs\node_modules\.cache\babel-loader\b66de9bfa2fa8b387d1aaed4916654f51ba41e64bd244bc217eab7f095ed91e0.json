{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\loading\\\\LoadingText.jsx\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LoadingText = _ref => {\n  let {\n    children,\n    loading,\n    color = 'bg-gray-300',\n    w = 'w-48',\n    h = 'h-4',\n    rounded = 'rounded-full'\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\".concat(rounded, \" animate-pulse \").concat(color, \" \").concat(w, \" \").concat(h)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 17\n    }, this) : children\n  }, void 0, false);\n};\n_c = LoadingText;\nexport default LoadingText;\nvar _c;\n$RefreshReg$(_c, \"LoadingText\");", "map": {"version": 3, "names": ["LoadingText", "_ref", "children", "loading", "color", "w", "h", "rounded", "_jsxDEV", "_Fragment", "className", "concat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/loading/LoadingText.jsx"], "sourcesContent": ["const LoadingText = ({ children, loading, color = 'bg-gray-300', w = 'w-48', h = 'h-4', rounded = 'rounded-full' }) => {\r\n    return (\r\n        <>\r\n            {loading ? (\r\n                <div className={`${rounded} animate-pulse ${color} ${w} ${h}`}></div>\r\n            ) : (\r\n                children\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\nexport default LoadingText;"], "mappings": ";;AAAA,MAAMA,WAAW,GAAGC,IAAA,IAAmG;EAAA,IAAlG;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,KAAK,GAAG,aAAa;IAAEC,CAAC,GAAG,MAAM;IAAEC,CAAC,GAAG,KAAK;IAAEC,OAAO,GAAG;EAAe,CAAC,GAAAN,IAAA;EAC9G,oBACIO,OAAA,CAAAC,SAAA;IAAAP,QAAA,EACKC,OAAO,gBACJK,OAAA;MAAKE,SAAS,KAAAC,MAAA,CAAKJ,OAAO,qBAAAI,MAAA,CAAkBP,KAAK,OAAAO,MAAA,CAAIN,CAAC,OAAAM,MAAA,CAAIL,CAAC;IAAG;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,GAErEb;EACH,gBACH,CAAC;AAEX,CAAC;AAACc,EAAA,GAVIhB,WAAW;AAYjB,eAAeA,WAAW;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}