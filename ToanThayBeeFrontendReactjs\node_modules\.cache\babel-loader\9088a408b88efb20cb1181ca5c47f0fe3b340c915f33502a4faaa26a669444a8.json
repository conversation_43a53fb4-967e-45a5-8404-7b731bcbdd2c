{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\SortableQuestionItem.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { GripVertical } from 'lucide-react';\nimport QuestionContent from './QuestionContent';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableQuestionItem = _ref => {\n  _s();\n  let {\n    question,\n    index\n  } = _ref;\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging\n  } = useSortable({\n    id: question.id\n  });\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n    opacity: isDragging ? 0.5 : 1\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setNodeRef,\n    style: style,\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ...attributes,\n      ...listeners,\n      className: \"absolute left-2 top-4 z-10 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-200 transition-colors\",\n      title: \"K\\xE9o \\u0111\\u1EC3 s\\u1EAFp x\\u1EBFp l\\u1EA1i th\\u1EE9 t\\u1EF1\",\n      children: /*#__PURE__*/_jsxDEV(GripVertical, {\n        size: 16,\n        className: \"text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pl-8\",\n      children: /*#__PURE__*/_jsxDEV(QuestionContent, {\n        question: question,\n        index: index\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableQuestionItem, \"bHcBvfvWWXIh6rUHlnQDLt/lZWI=\", false, function () {\n  return [useSortable];\n});\n_c = SortableQuestionItem;\nexport default SortableQuestionItem;\nvar _c;\n$RefreshReg$(_c, \"SortableQuestionItem\");", "map": {"version": 3, "names": ["React", "useSortable", "CSS", "GripVertical", "QuestionContent", "jsxDEV", "_jsxDEV", "SortableQuestionItem", "_ref", "_s", "question", "index", "attributes", "listeners", "setNodeRef", "transform", "transition", "isDragging", "id", "style", "Transform", "toString", "opacity", "ref", "className", "children", "title", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/SortableQuestionItem.jsx"], "sourcesContent": ["import React from 'react';\nimport { useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { GripVertical } from 'lucide-react';\nimport QuestionContent from './QuestionContent';\n\nconst SortableQuestionItem = ({ question, index }) => {\n    const {\n        attributes,\n        listeners,\n        setNodeRef,\n        transform,\n        transition,\n        isDragging,\n    } = useSortable({ id: question.id });\n\n    const style = {\n        transform: CSS.Transform.toString(transform),\n        transition,\n        opacity: isDragging ? 0.5 : 1,\n    };\n\n    return (\n        <div ref={setNodeRef} style={style} className=\"relative\">\n            {/* Drag Handle */}\n            <div\n                {...attributes}\n                {...listeners}\n                className=\"absolute left-2 top-4 z-10 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-200 transition-colors\"\n                title=\"Kéo để sắp xếp lại thứ tự\"\n            >\n                <GripVertical size={16} className=\"text-gray-400\" />\n            </div>\n\n            {/* Question Content with left padding for drag handle */}\n            <div className=\"pl-8\">\n                <QuestionContent question={question} index={index} />\n            </div>\n        </div>\n    );\n};\n\nexport default SortableQuestionItem;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,GAAG,QAAQ,oBAAoB;AACxC,SAASC,YAAY,QAAQ,cAAc;AAC3C,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,oBAAoB,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAH,IAAA;EAC7C,MAAM;IACFI,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC;EACJ,CAAC,GAAGhB,WAAW,CAAC;IAAEiB,EAAE,EAAER,QAAQ,CAACQ;EAAG,CAAC,CAAC;EAEpC,MAAMC,KAAK,GAAG;IACVJ,SAAS,EAAEb,GAAG,CAACkB,SAAS,CAACC,QAAQ,CAACN,SAAS,CAAC;IAC5CC,UAAU;IACVM,OAAO,EAAEL,UAAU,GAAG,GAAG,GAAG;EAChC,CAAC;EAED,oBACIX,OAAA;IAAKiB,GAAG,EAAET,UAAW;IAACK,KAAK,EAAEA,KAAM;IAACK,SAAS,EAAC,UAAU;IAAAC,QAAA,gBAEpDnB,OAAA;MAAA,GACQM,UAAU;MAAA,GACVC,SAAS;MACbW,SAAS,EAAC,+GAA+G;MACzHE,KAAK,EAAC,iEAA2B;MAAAD,QAAA,eAEjCnB,OAAA,CAACH,YAAY;QAACwB,IAAI,EAAE,EAAG;QAACH,SAAS,EAAC;MAAe;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAGNzB,OAAA;MAAKkB,SAAS,EAAC,MAAM;MAAAC,QAAA,eACjBnB,OAAA,CAACF,eAAe;QAACM,QAAQ,EAAEA,QAAS;QAACC,KAAK,EAAEA;MAAM;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACtB,EAAA,CAlCIF,oBAAoB;EAAA,QAQlBN,WAAW;AAAA;AAAA+B,EAAA,GARbzB,oBAAoB;AAoC1B,eAAeA,oBAAoB;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}