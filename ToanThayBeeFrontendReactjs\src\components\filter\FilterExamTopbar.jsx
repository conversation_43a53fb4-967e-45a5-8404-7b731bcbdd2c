import { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchCodesByType } from "../../features/code/codeSlice";
import { fetchPublicExams } from "../../features/exam/examSlice";
import { setSelectedGrade, setSelectedChapters, setSelectedExamTypes, setIsSearch } from "../../features/filter/filterSlice";
import { setCurrentPage } from "../../features/exam/examSlice";
import LoadingSpinner from "../loading/LoadingSpinner";
import { Search, X, Filter } from "lucide-react";

const FilterExamTopbar = () => {
    const { codes } = useSelector((state) => state.codes);
    const { isSearch, selectedGrade, selectedChapters, selectedExamTypes } = useSelector((state) => state.filter);
    const { pagination } = useSelector((state) => state.exams);
    const { page: currentPage, sortOrder } = pagination;

    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const [isClassroomExam, setIsClassroomExam] = useState(null);
    const [showMobileFilter, setShowMobileFilter] = useState(false);
    const [activeTab, setActiveTab] = useState('all'); // 'all', 'classroom', 'self'

    const mobileFilterRef = useRef(null);

    useEffect(() => {
        dispatch(fetchCodesByType(['chapter', 'grade', 'exam type']));
    }, [dispatch]);

    const fetchExams = (override = {}) => {
        // Only apply filters if isSearch is true or if explicitly overridden
        const shouldApplyFilters = isSearch || override.applyFilters;

        dispatch(fetchPublicExams({
            page: override.page ?? currentPage,
            limit: 10,
            sortOrder,
            typeOfExam: shouldApplyFilters ? (override.typeOfExam ?? selectedExamTypes) : [],
            class: shouldApplyFilters ? (override.class === null ? override.class : selectedGrade) : null,
            chapter: shouldApplyFilters ? (override.chapter ?? selectedChapters) : [],
            search: shouldApplyFilters ? search : "",
            isClassroomExam: override.isClassroomExam
        }));
    }

    // Only fetch exams when page changes, not when filters change
    useEffect(() => {
        if (isSearch) {
            fetchExams({ isClassroomExam });
        }
    }, [dispatch, isSearch]);

    useEffect(() => {
        fetchExams({ isClassroomExam });
    }, [currentPage]);

    useEffect(() => {
        if (selectedChapters.length === 0 && selectedGrade === null && selectedExamTypes.length === 0 && search === "") {
            dispatch(setIsSearch(false));
        }
    }, [dispatch, selectedChapters, selectedGrade, selectedExamTypes, search]);

    // Handle click outside to close mobile filter
    useEffect(() => {
        const handleClickOutside = (e) => {
            if (mobileFilterRef.current && !mobileFilterRef.current.contains(e.target)) {
                setShowMobileFilter(false);
            }
        };

        if (showMobileFilter) {
            document.addEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [showMobileFilter]);

    const handleSearch = () => {
        setLoading(true);
        // Set isSearch to true first so filters will be applied
        dispatch(setIsSearch(true));
        setShowMobileFilter(false); // Close mobile filter after search

        dispatch(fetchPublicExams({
            page: currentPage,
            limit: 10,
            sortOrder,
            typeOfExam: selectedExamTypes,
            class: selectedGrade,
            chapter: selectedChapters,
            search,
            isClassroomExam
        }))
            .then(() => {
                setLoading(false);
            });
    }

    const resetAllFilters = () => {
        setSearch("");
        dispatch(setSelectedGrade(null));
        dispatch(setSelectedChapters([]));
        dispatch(setSelectedExamTypes([]));

        // Set isSearch to true to ensure filters are applied (in this case, empty filters)
        dispatch(setIsSearch(true));

        // Apply the reset filters immediately
        setLoading(true);
        dispatch(fetchPublicExams({
            page: currentPage,
            limit: 10,
            sortOrder,
            typeOfExam: [],
            class: null,
            chapter: [],
            search: "",
            isClassroomExam
        }))
            .then(() => {
                setLoading(false);
            });
    }

    // Get count of active filters
    const getActiveFilterCount = () => {
        let count = 0;
        if (selectedGrade) count++;
        if (selectedChapters.length > 0) count += selectedChapters.length;
        if (selectedExamTypes.length > 0) count += selectedExamTypes.length;
        if (search) count++;
        return count;
    }

    return (
        <div className="w-full bg-white shadow-sm px-4 py-3 sticky top-0 z-20 ">
            <div className="max-w-[70rem] mx-auto">
                {/* Title and search bar */}
                <div className="flex flex-col md:flex-row md:items-center gap-3 mb-3">
                    <h1 className="text-2xl font-bold text-zinc-800">Thư viện đề thi</h1>

                    <div className="flex-1 flex items-center gap-2">
                        <div className="relative flex-1">
                            <input
                                type="text"
                                value={search}
                                onChange={(e) => setSearch(e.target.value)}
                                placeholder="Tìm kiếm đề thi..."
                                className="w-full h-10 pl-10 pr-10 text-sm text-gray-700 placeholder-gray-400 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-400 focus:border-sky-400 transition-all duration-150"
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        handleSearch();
                                    }
                                }}
                            />
                            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                                <Search size={18} className="text-gray-400" />
                            </div>
                            {loading && (
                                <div className="absolute inset-y-0 right-3 flex items-center">
                                    <LoadingSpinner color="border-black" size="1.25rem" />
                                </div>
                            )}
                        </div>

                        {/* Filter button for mobile */}
                        <button
                            onClick={() => setShowMobileFilter(!showMobileFilter)}
                            className="md:hidden flex items-center gap-1 bg-gray-100 border border-gray-300 text-gray-700 rounded-lg px-3 py-2 text-sm font-medium hover:bg-gray-200 transition-colors"
                        >
                            <Filter size={16} />
                            <span>Lọc</span>
                            {getActiveFilterCount() > 0 && (
                                <span className="bg-sky-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                    {getActiveFilterCount()}
                                </span>
                            )}
                        </button>

                        {/* Search button */}
                        <button
                            onClick={handleSearch}
                            className="bg-slate-800 hover:bg-slate-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-all"
                        >
                            Tìm kiếm
                        </button>
                    </div>
                </div>

                {/* Tab navigation */}
                <div className="flex gap-6 border-b border-gray-200">
                    <div
                        className={`p-2 cursor-pointer ${activeTab === 'all' ? 'font-medium text-sky-700 border-b-2 border-sky-700 bg-gray-50' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'}`}
                        onClick={() => {
                            setIsClassroomExam(null);
                            setActiveTab('all');
                            dispatch(setCurrentPage(1));
                            fetchExams({
                                page: 1,
                                isClassroomExam: null,
                                applyFilters: isSearch
                            });
                        }}
                    >
                        Tất cả
                    </div>
                    <div
                        className={`p-2 cursor-pointer ${activeTab === 'classroom' ? 'font-medium text-sky-700 border-b-2 border-sky-700 bg-gray-50' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'}`}
                        onClick={() => {
                            setIsClassroomExam(true);
                            setActiveTab('classroom');
                            dispatch(setCurrentPage(1));
                            fetchExams({
                                page: 1,
                                isClassroomExam: true,
                                applyFilters: isSearch
                            });
                        }}
                    >
                        Đề trên lớp
                    </div>
                    <div
                        className={`p-2 cursor-pointer ${activeTab === 'self' ? 'font-medium text-sky-700 border-b-2 border-sky-700 bg-gray-50' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'}`}
                        onClick={() => {
                            setIsClassroomExam(false);
                            setActiveTab('self');
                            dispatch(setCurrentPage(1));
                            fetchExams({
                                page: 1,
                                isClassroomExam: false,
                                applyFilters: isSearch
                            });
                        }}
                    >
                        Đề tự luyện
                    </div>
                </div>

                {/* Mobile filter panel */}
                {showMobileFilter && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-start pt-[8rem] md:hidden">
                        <div
                            ref={mobileFilterRef}
                            className="bg-gray-50 rounded-lg shadow-lg w-[90%] max-w-md max-h-[80vh] overflow-y-auto"
                        >
                            <div className="p-4 border-b border-gray-300 flex justify-between items-center bg-gray-100">
                                <p className="text-lg font-bold text-gray-800">Bộ lọc</p>
                                <button
                                    onClick={() => setShowMobileFilter(false)}
                                    className="text-gray-500 hover:text-gray-700"
                                >
                                    <X size={20} />
                                </button>
                            </div>

                            <div className="p-4 space-y-4">
                                {/* Grade filter */}
                                <div>
                                    <h3 className="text-base font-medium text-gray-700 mb-2">Lớp</h3>
                                    <div className="flex flex-wrap gap-2">
                                        {codes?.['grade']?.map((code) => (
                                            <div
                                                key={code.code}
                                                onClick={() => dispatch(setSelectedGrade(selectedGrade === code.code ? null : code.code))}
                                                className={`px-3 py-1.5 rounded-lg text-sm cursor-pointer ${selectedGrade === code.code
                                                    ? 'bg-gray-200 text-sky-700 border border-gray-300 font-medium'
                                                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                                                    }`}
                                            >
                                                {code.description}
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Chapter filter */}
                                <div>
                                    <h3 className="text-base font-medium text-gray-700 mb-2">Chương</h3>
                                    {!selectedGrade ? (
                                        <div className="text-sm text-gray-500 italic">
                                            Chọn lớp để hiển thị chương
                                        </div>
                                    ) : (
                                        <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
                                            {codes?.['chapter']
                                                ?.filter((code) => code.code.startsWith(selectedGrade) && code.code.length === 4)
                                                ?.map((code) => (
                                                    <div
                                                        key={code.code}
                                                        onClick={() => {
                                                            const newChapters = selectedChapters.includes(code.code)
                                                                ? selectedChapters.filter(c => c !== code.code)
                                                                : [...selectedChapters, code.code];
                                                            dispatch(setSelectedChapters(newChapters));
                                                        }}
                                                        className={`px-3 py-1.5 rounded-lg text-sm cursor-pointer ${selectedChapters.includes(code.code)
                                                            ? 'bg-gray-200 text-sky-700 border border-gray-300 font-medium'
                                                            : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                                                            }`}
                                                    >
                                                        {code.description}
                                                    </div>
                                                ))
                                            }
                                        </div>
                                    )}
                                </div>

                                {/* Exam type filter */}
                                <div>
                                    <h3 className="text-base font-medium text-gray-700 mb-2">Loại đề</h3>
                                    <div className="flex flex-wrap gap-2">
                                        {codes?.['exam type']?.map((code) => (
                                            <div
                                                key={code.code}
                                                onClick={() => {
                                                    const newTypes = selectedExamTypes.includes(code.code)
                                                        ? selectedExamTypes.filter(t => t !== code.code)
                                                        : [...selectedExamTypes, code.code];
                                                    dispatch(setSelectedExamTypes(newTypes));
                                                }}
                                                className={`px-3 py-1.5 rounded-lg text-sm cursor-pointer ${selectedExamTypes.includes(code.code)
                                                    ? 'bg-gray-200 text-sky-700 border border-gray-300 font-medium'
                                                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                                                    }`}
                                            >
                                                {code.description}
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Action buttons */}
                                <div className="flex gap-2 pt-4">
                                    <button
                                        onClick={resetAllFilters}
                                        className="flex-1 border border-gray-300 text-gray-700 hover:bg-gray-200 bg-gray-100 text-sm font-medium py-2 px-4 rounded-lg transition-all"
                                    >
                                        Xóa bộ lọc
                                    </button>
                                    <button
                                        onClick={handleSearch}
                                        className="flex-1 bg-slate-800 hover:bg-slate-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-all"
                                    >
                                        Áp dụng
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default FilterExamTopbar;
