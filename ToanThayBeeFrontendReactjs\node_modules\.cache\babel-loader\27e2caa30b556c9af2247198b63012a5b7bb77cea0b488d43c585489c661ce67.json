{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\ImageDropZone.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\nimport classNames from \"classnames\"; // optional: tiện xử lý class\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDropZone = _ref => {\n  _s();\n  let {\n    imageUrl,\n    onImageDrop,\n    onImageRemove\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\n    if (draggedImage && onImageDrop) {\n      onImageDrop(draggedImage);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: classNames(\"relative flex items-center justify-center border-2 border-dashed rounded-md p-4 transition-all duration-300 cursor-pointer\", isDraggingOver ? \"border-sky-500 bg-sky-50\" : \"border-gray-300 hover:border-sky-400 hover:bg-sky-50\"),\n    onDragOver: e => e.preventDefault(),\n    onDragEnter: () => setIsDraggingOver(true),\n    onDragLeave: () => setIsDraggingOver(false),\n    onDrop: handleDrop,\n    children: !imageUrl ? /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-500 text-sm\",\n      children: \"K\\xE9o \\u1EA3nh v\\xE0o \\u0111\\xE2y \\u0111\\u1EC3 th\\xEAm\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative group w-full max-w-xs\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: \"dropped\",\n        className: \"rounded-md max-h-40 object-contain w-full border transition-all group-hover:brightness-75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition\",\n        onClick: e => {\n          e.stopPropagation();\n          onImageRemove === null || onImageRemove === void 0 ? void 0 : onImageRemove();\n        },\n        title: \"X\\xF3a \\u1EA3nh\",\n        children: /*#__PURE__*/_jsxDEV(Trash2, {\n          className: \"w-6 h-6 text-white bg-red-500 p-1 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 9\n  }, this);\n};\n_s(ImageDropZone, \"VKW1ZLDpPe+zo2/qAoOWVX5YkVs=\");\n_c = ImageDropZone;\nexport default ImageDropZone;\nvar _c;\n$RefreshReg$(_c, \"ImageDropZone\");", "map": {"version": 3, "names": ["React", "useState", "Trash2", "classNames", "jsxDEV", "_jsxDEV", "ImageDropZone", "_ref", "_s", "imageUrl", "onImageDrop", "onImageRemove", "isDraggingOver", "setIsDraggingOver", "handleDrop", "e", "preventDefault", "draggedImage", "dataTransfer", "getData", "className", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "onClick", "stopPropagation", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/ImageDropZone.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\r\nimport classNames from \"classnames\"; // optional: tiện xử lý class\r\n\r\nconst ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\r\n        if (draggedImage && onImageDrop) {\r\n            onImageDrop(draggedImage);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div\r\n            className={classNames(\r\n                \"relative flex items-center justify-center border-2 border-dashed rounded-md p-4 transition-all duration-300 cursor-pointer\",\r\n                isDraggingOver ? \"border-sky-500 bg-sky-50\" : \"border-gray-300 hover:border-sky-400 hover:bg-sky-50\"\r\n            )}\r\n            onDragOver={(e) => e.preventDefault()}\r\n            onDragEnter={() => setIsDraggingOver(true)}\r\n            onDragLeave={() => setIsDraggingOver(false)}\r\n            onDrop={handleDrop}\r\n        >\r\n            {!imageUrl ? (\r\n                <p className=\"text-gray-500 text-sm\">Kéo ảnh vào đây để thêm</p>\r\n            ) : (\r\n                <div className=\"relative group w-full max-w-xs\">\r\n                    <img\r\n                        src={imageUrl}\r\n                        alt=\"dropped\"\r\n                        className=\"rounded-md max-h-40 object-contain w-full border transition-all group-hover:brightness-75\"\r\n                    />\r\n                    <button\r\n                        className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition\"\r\n                        onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            onImageRemove?.();\r\n                        }}\r\n                        title=\"Xóa ảnh\"\r\n                    >\r\n                        <Trash2 className=\"w-6 h-6 text-white bg-red-500 p-1 rounded-full\" />\r\n                    </button>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ImageDropZone;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,cAAc,CAAC,CAAC;AACvC,OAAOC,UAAU,MAAM,YAAY,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,aAAa,GAAGC,IAAA,IAA8C;EAAAC,EAAA;EAAA,IAA7C;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAAJ,IAAA;EAC3D,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMa,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMI,YAAY,GAAGF,CAAC,CAACG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3D,IAAIF,YAAY,IAAIP,WAAW,EAAE;MAC7BA,WAAW,CAACO,YAAY,CAAC;IAC7B;EACJ,CAAC;EAED,oBACIZ,OAAA;IACIe,SAAS,EAAEjB,UAAU,CACjB,4HAA4H,EAC5HS,cAAc,GAAG,0BAA0B,GAAG,sDAClD,CAAE;IACFS,UAAU,EAAGN,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;IACtCM,WAAW,EAAEA,CAAA,KAAMT,iBAAiB,CAAC,IAAI,CAAE;IAC3CU,WAAW,EAAEA,CAAA,KAAMV,iBAAiB,CAAC,KAAK,CAAE;IAC5CW,MAAM,EAAEV,UAAW;IAAAW,QAAA,EAElB,CAAChB,QAAQ,gBACNJ,OAAA;MAAGe,SAAS,EAAC,uBAAuB;MAAAK,QAAA,EAAC;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,gBAEhExB,OAAA;MAAKe,SAAS,EAAC,gCAAgC;MAAAK,QAAA,gBAC3CpB,OAAA;QACIyB,GAAG,EAAErB,QAAS;QACdsB,GAAG,EAAC,SAAS;QACbX,SAAS,EAAC;MAA2F;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG,CAAC,eACFxB,OAAA;QACIe,SAAS,EAAC,gGAAgG;QAC1GY,OAAO,EAAGjB,CAAC,IAAK;UACZA,CAAC,CAACkB,eAAe,CAAC,CAAC;UACnBtB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAG,CAAC;QACrB,CAAE;QACFuB,KAAK,EAAC,iBAAS;QAAAT,QAAA,eAEfpB,OAAA,CAACH,MAAM;UAACkB,SAAS,EAAC;QAAgD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACrB,EAAA,CA/CIF,aAAa;AAAA6B,EAAA,GAAb7B,aAAa;AAiDnB,eAAeA,aAAa;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}