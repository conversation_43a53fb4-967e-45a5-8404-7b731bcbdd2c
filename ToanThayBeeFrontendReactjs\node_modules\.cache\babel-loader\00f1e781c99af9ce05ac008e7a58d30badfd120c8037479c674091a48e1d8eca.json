{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\nimport { fetchWithCacheIfNeeded } from \"../helpers/fetchWithCacheIfNeeded\";\nexport const fetchPublicExamById = createAsyncThunk(\"examDetail/fetchPublicExamById\", async (id, _ref) => {\n  let {\n    dispatch\n  } = _ref;\n  return await apiHandler(dispatch, examApi.getExamPublic, id, () => {}, false, false);\n});\nexport const saveExamForUser = createAsyncThunk(\"examDetail/saveExamForUser\", async (_ref2, _ref3) => {\n  let {\n    examId\n  } = _ref2;\n  let {\n    dispatch\n  } = _ref3;\n  return await apiHandler(dispatch, examApi.saveExamForUserAPI, {\n    examId\n  }, () => {}, true, false, false, false);\n});\nexport const fetchRelatedExams = createAsyncThunk(\"exams/fetchRelatedExams\", async (id, _ref4) => {\n  let {\n    dispatch\n  } = _ref4;\n  return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => {}, false, false);\n});\n\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\"exams/fetchRelatedExamsIfNeeded\", async (id, _ref5) => {\n  let {\n    dispatch,\n    getState\n  } = _ref5;\n  return await fetchWithCacheIfNeeded({\n    id,\n    stateKey: 'exams',\n    cacheKey: 'relatedExams',\n    dispatch,\n    getState,\n    fetchAction: fetchRelatedExams,\n    // thunk gốc\n    cacheDuration: 300000 // 5 phút\n  });\n});\nconst examDetailSlice = createSlice({\n  name: \"examDetail\",\n  initialState: {\n    exam: null,\n    loading: false,\n    relatedExams: [],\n    loadingRelatedExams: false,\n    view: 'detail'\n  },\n  reducers: {\n    setView: (state, action) => {\n      state.view = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchPublicExamById.pending, state => {\n      state.exam = null;\n      state.loading = true;\n    }).addCase(fetchPublicExamById.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exam = action.payload.data;\n      }\n      state.loading = false;\n    }).addCase(fetchPublicExamById.rejected, state => {\n      state.exam = null;\n      state.loading = false;\n    }).addCase(saveExamForUser.fulfilled, (state, action) => {\n      if (action.payload) {\n        const {\n          examId,\n          isSave\n        } = action.payload;\n        if (state.exam) {\n          state.exam.isSave = isSave;\n        }\n      }\n    }).addCase(fetchRelatedExams.pending, state => {\n      state.loadingRelatedExams = true;\n    }).addCase(fetchRelatedExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.relatedExams = action.payload.data;\n      }\n      state.loadingRelatedExams = false;\n    }).addCase(fetchRelatedExams.rejected, state => {\n      state.loadingRelatedExams = false;\n    }).addCase(fetchRelatedExamsIfNeeded.pending, state => {\n      state.loadingRelatedExams = true;\n    }).addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.relatedExams = action.payload.data;\n      }\n      state.loadingRelatedExams = false;\n    }).addCase(fetchRelatedExamsIfNeeded.rejected, state => {\n      state.loadingRelatedExams = false;\n    });\n  }\n});\nexport const {\n  setView\n} = examDetailSlice.actions;\nexport default examDetailSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialPaginationState", "paginationReducers", "initialFilterState", "filterReducers", "fetchWithCacheIfNeeded", "fetchPublicExamById", "id", "_ref", "dispatch", "getExamPublic", "saveExamForUser", "_ref2", "_ref3", "examId", "saveExamForUserAPI", "fetchRelatedExams", "_ref4", "getRelatedExamAPI", "fetchRelatedExamsIfNeeded", "_ref5", "getState", "stateKey", "cache<PERSON>ey", "fetchAction", "cacheDuration", "examDetailSlice", "name", "initialState", "exam", "loading", "relatedExams", "loadingRelatedExams", "view", "reducers", "<PERSON><PERSON><PERSON><PERSON>", "state", "action", "payload", "extraReducers", "builder", "addCase", "pending", "fulfilled", "data", "rejected", "isSave", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/exam/examDetailSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\r\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\r\nimport { fetchWithCacheIfNeeded } from \"../helpers/fetchWithCacheIfNeeded\";\r\n\r\nexport const fetchPublicExamById = createAsyncThunk(\r\n    \"examDetail/fetchPublicExamById\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamPublic, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const saveExamForUser = createAsyncThunk(\r\n    \"examDetail/saveExamForUser\",\r\n    async ({ examId }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.saveExamForUserAPI, { examId }, () => { }, true, false, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchRelatedExams = createAsyncThunk(\r\n    \"exams/fetchRelatedExams\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\r\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\r\n    \"exams/fetchRelatedExamsIfNeeded\",\r\n    async (id, { dispatch, getState }) => {\r\n        return await fetchWithCacheIfNeeded({\r\n            id,\r\n            stateKey: 'exams',\r\n            cacheKey: 'relatedExams',\r\n            dispatch,\r\n            getState,\r\n            fetchAction: fetchRelatedExams, // thunk gốc\r\n            cacheDuration: 300000, // 5 phút\r\n        });\r\n    }\r\n);\r\n\r\nconst examDetailSlice = createSlice({\r\n    name: \"examDetail\",\r\n    initialState: {\r\n        exam: null,\r\n        loading: false,\r\n        relatedExams: [],\r\n        loadingRelatedExams: false,\r\n        view: 'detail',\r\n    },\r\n    reducers: {\r\n        setView: (state, action) => {\r\n            state.view = action.payload;\r\n        },\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchPublicExamById.pending, (state) => {\r\n                state.exam = null;\r\n                state.loading = true;\r\n            })\r\n            .addCase(fetchPublicExamById.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exam = action.payload.data;\r\n                }\r\n                state.loading = false;\r\n            })\r\n            .addCase(fetchPublicExamById.rejected, (state) => {\r\n                state.exam = null;\r\n                state.loading = false;\r\n            })\r\n            .addCase(saveExamForUser.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    const { examId, isSave } = action.payload;\r\n\r\n                    if (state.exam) {\r\n                        state.exam.isSave = isSave;\r\n                    }\r\n                }\r\n            })\r\n            .addCase(fetchRelatedExams.pending, (state) => {\r\n                state.loadingRelatedExams = true;\r\n            })\r\n            .addCase(fetchRelatedExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.relatedExams = action.payload.data;\r\n                }\r\n                state.loadingRelatedExams = false;\r\n            })\r\n            .addCase(fetchRelatedExams.rejected, (state) => {\r\n                state.loadingRelatedExams = false;\r\n            })\r\n            .addCase(fetchRelatedExamsIfNeeded.pending, (state) => {\r\n                state.loadingRelatedExams = true;\r\n            })\r\n            .addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.relatedExams = action.payload.data;\r\n                }\r\n                state.loadingRelatedExams = false;\r\n            })\r\n            .addCase(fetchRelatedExamsIfNeeded.rejected, (state) => {\r\n                state.loadingRelatedExams = false;\r\n            })\r\n    },\r\n});\r\n\r\nexport const { setView } = examDetailSlice.actions;\r\nexport default examDetailSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,sBAAsB,QAAQ,mCAAmC;AAE1E,OAAO,MAAMC,mBAAmB,GAAGR,gBAAgB,CAC/C,gCAAgC,EAChC,OAAOS,EAAE,EAAAC,IAAA,KAAmB;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAD,IAAA;EACnB,OAAO,MAAMR,UAAU,CAACS,QAAQ,EAAEV,OAAO,CAACW,aAAa,EAAEH,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACzF,CACJ,CAAC;AAED,OAAO,MAAMI,eAAe,GAAGb,gBAAgB,CAC3C,4BAA4B,EAC5B,OAAAc,KAAA,EAAAC,KAAA,KAAoC;EAAA,IAA7B;IAAEC;EAAO,CAAC,GAAAF,KAAA;EAAA,IAAE;IAAEH;EAAS,CAAC,GAAAI,KAAA;EAC3B,OAAO,MAAMb,UAAU,CAACS,QAAQ,EAAEV,OAAO,CAACgB,kBAAkB,EAAE;IAAED;EAAO,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACnH,CACJ,CAAC;AAED,OAAO,MAAME,iBAAiB,GAAGlB,gBAAgB,CAC7C,yBAAyB,EACzB,OAAOS,EAAE,EAAAU,KAAA,KAAmB;EAAA,IAAjB;IAAER;EAAS,CAAC,GAAAQ,KAAA;EACnB,OAAO,MAAMjB,UAAU,CAACS,QAAQ,EAAEV,OAAO,CAACmB,iBAAiB,EAAEX,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC7F,CACJ,CAAC;;AAED;AACA,OAAO,MAAMY,yBAAyB,GAAGrB,gBAAgB,CACrD,iCAAiC,EACjC,OAAOS,EAAE,EAAAa,KAAA,KAA6B;EAAA,IAA3B;IAAEX,QAAQ;IAAEY;EAAS,CAAC,GAAAD,KAAA;EAC7B,OAAO,MAAMf,sBAAsB,CAAC;IAChCE,EAAE;IACFe,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,cAAc;IACxBd,QAAQ;IACRY,QAAQ;IACRG,WAAW,EAAER,iBAAiB;IAAE;IAChCS,aAAa,EAAE,MAAM,CAAE;EAC3B,CAAC,CAAC;AACN,CACJ,CAAC;AAED,MAAMC,eAAe,GAAG7B,WAAW,CAAC;EAChC8B,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAE;IACVC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,KAAK;IACdC,YAAY,EAAE,EAAE;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,IAAI,EAAE;EACV,CAAC;EACDC,QAAQ,EAAE;IACNC,OAAO,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MACxBD,KAAK,CAACH,IAAI,GAAGI,MAAM,CAACC,OAAO;IAC/B;EACJ,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAACnC,mBAAmB,CAACoC,OAAO,EAAGN,KAAK,IAAK;MAC7CA,KAAK,CAACP,IAAI,GAAG,IAAI;MACjBO,KAAK,CAACN,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDW,OAAO,CAACnC,mBAAmB,CAACqC,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MACvD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBF,KAAK,CAACP,IAAI,GAAGQ,MAAM,CAACC,OAAO,CAACM,IAAI;MACpC;MACAR,KAAK,CAACN,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDW,OAAO,CAACnC,mBAAmB,CAACuC,QAAQ,EAAGT,KAAK,IAAK;MAC9CA,KAAK,CAACP,IAAI,GAAG,IAAI;MACjBO,KAAK,CAACN,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDW,OAAO,CAAC9B,eAAe,CAACgC,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MACnD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB,MAAM;UAAExB,MAAM;UAAEgC;QAAO,CAAC,GAAGT,MAAM,CAACC,OAAO;QAEzC,IAAIF,KAAK,CAACP,IAAI,EAAE;UACZO,KAAK,CAACP,IAAI,CAACiB,MAAM,GAAGA,MAAM;QAC9B;MACJ;IACJ,CAAC,CAAC,CACDL,OAAO,CAACzB,iBAAiB,CAAC0B,OAAO,EAAGN,KAAK,IAAK;MAC3CA,KAAK,CAACJ,mBAAmB,GAAG,IAAI;IACpC,CAAC,CAAC,CACDS,OAAO,CAACzB,iBAAiB,CAAC2B,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBF,KAAK,CAACL,YAAY,GAAGM,MAAM,CAACC,OAAO,CAACM,IAAI;MAC5C;MACAR,KAAK,CAACJ,mBAAmB,GAAG,KAAK;IACrC,CAAC,CAAC,CACDS,OAAO,CAACzB,iBAAiB,CAAC6B,QAAQ,EAAGT,KAAK,IAAK;MAC5CA,KAAK,CAACJ,mBAAmB,GAAG,KAAK;IACrC,CAAC,CAAC,CACDS,OAAO,CAACtB,yBAAyB,CAACuB,OAAO,EAAGN,KAAK,IAAK;MACnDA,KAAK,CAACJ,mBAAmB,GAAG,IAAI;IACpC,CAAC,CAAC,CACDS,OAAO,CAACtB,yBAAyB,CAACwB,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MAC7D,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBF,KAAK,CAACL,YAAY,GAAGM,MAAM,CAACC,OAAO,CAACM,IAAI;MAC5C;MACAR,KAAK,CAACJ,mBAAmB,GAAG,KAAK;IACrC,CAAC,CAAC,CACDS,OAAO,CAACtB,yBAAyB,CAAC0B,QAAQ,EAAGT,KAAK,IAAK;MACpDA,KAAK,CAACJ,mBAAmB,GAAG,KAAK;IACrC,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEG;AAAQ,CAAC,GAAGT,eAAe,CAACqB,OAAO;AAClD,eAAerB,eAAe,CAACsB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}