{"ast": null, "code": "import apin8n from \"./apin8n\";\nimport api from \"./api\";\nexport const uploadFile = async Invoice => {\n  const formData = new FormData();\n  formData.append('Invoice', Invoice);\n  const response = await apin8n.post(\"/webhook/ocr-exam\", formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n  return response;\n};\nexport const getAllExamAPI = async _ref => {\n  let {\n    search,\n    page,\n    pageSize,\n    sortOrder\n  } = _ref;\n  const data = {\n    search,\n    page,\n    pageSize,\n    sortOrder\n  };\n  return api.get(\"/v1/admin/exam1\", {\n    params: data\n  });\n};\nexport const getAllQuestionsByExamIdAPI = async examId => {\n  return api.get(\"/v1/admin/exam1/\".concat(examId));\n};\nexport const saveExam1API = async _ref2 => {\n  let {\n    examData,\n    questions,\n    id\n  } = _ref2;\n  const response = await api.put(\"/v1/admin/exam1/\".concat(id), {\n    exam: examData,\n    questions\n  });\n  return response;\n};\nexport const commitExam1API = async id => {\n  return api.post(\"/v1/admin/exam1/\".concat(id, \"/commit\"));\n};", "map": {"version": 3, "names": ["apin8n", "api", "uploadFile", "Invoice", "formData", "FormData", "append", "response", "post", "headers", "getAllExamAPI", "_ref", "search", "page", "pageSize", "sortOrder", "data", "get", "params", "getAllQuestionsByExamIdAPI", "examId", "concat", "saveExam1API", "_ref2", "examData", "questions", "id", "put", "exam", "commitExam1API"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/services/ocrExamApi.js"], "sourcesContent": ["import apin8n from \"./apin8n\";\r\nimport api from \"./api\";\r\n\r\nexport const uploadFile = async (Invoice) => {\r\n    const formData = new FormData();\r\n    formData.append('Invoice', Invoice);\r\n\r\n    const response = await apin8n.post(\r\n        \"/webhook/ocr-exam\",\r\n        formData,\r\n        {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        }\r\n    );\r\n\r\n    return response;\r\n\r\n}\r\n\r\nexport const getAllExamAPI = async ({ search, page, pageSize, sortOrder }) => {\r\n    const data = {\r\n        search,\r\n        page,\r\n        pageSize,\r\n        sortOrder\r\n    };\r\n\r\n    return api.get(\"/v1/admin/exam1\", {\r\n        params: data\r\n    });\r\n}\r\n\r\nexport const getAllQuestionsByExamIdAPI = async (examId) => {\r\n    return api.get(`/v1/admin/exam1/${examId}`);\r\n}\r\n\r\nexport const saveExam1API = async ({ examData, questions, id }) => {\r\n    const response = await api.put(`/v1/admin/exam1/${id}`, {\r\n        exam: examData,\r\n        questions,\r\n    });\r\n\r\n    return response;\r\n}\r\n\r\nexport const commitExam1API = async (id) => {\r\n    return api.post(`/v1/admin/exam1/${id}/commit`);\r\n}\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,OAAOC,GAAG,MAAM,OAAO;AAEvB,OAAO,MAAMC,UAAU,GAAG,MAAOC,OAAO,IAAK;EACzC,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,OAAO,CAAC;EAEnC,MAAMI,QAAQ,GAAG,MAAMP,MAAM,CAACQ,IAAI,CAC9B,mBAAmB,EACnBJ,QAAQ,EACR;IACIK,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CACJ,CAAC;EAED,OAAOF,QAAQ;AAEnB,CAAC;AAED,OAAO,MAAMG,aAAa,GAAG,MAAAC,IAAA,IAAiD;EAAA,IAA1C;IAAEC,MAAM;IAAEC,IAAI;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAAJ,IAAA;EACrE,MAAMK,IAAI,GAAG;IACTJ,MAAM;IACNC,IAAI;IACJC,QAAQ;IACRC;EACJ,CAAC;EAED,OAAOd,GAAG,CAACgB,GAAG,CAAC,iBAAiB,EAAE;IAC9BC,MAAM,EAAEF;EACZ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMG,0BAA0B,GAAG,MAAOC,MAAM,IAAK;EACxD,OAAOnB,GAAG,CAACgB,GAAG,oBAAAI,MAAA,CAAoBD,MAAM,CAAE,CAAC;AAC/C,CAAC;AAED,OAAO,MAAME,YAAY,GAAG,MAAAC,KAAA,IAAuC;EAAA,IAAhC;IAAEC,QAAQ;IAAEC,SAAS;IAAEC;EAAG,CAAC,GAAAH,KAAA;EAC1D,MAAMhB,QAAQ,GAAG,MAAMN,GAAG,CAAC0B,GAAG,oBAAAN,MAAA,CAAoBK,EAAE,GAAI;IACpDE,IAAI,EAAEJ,QAAQ;IACdC;EACJ,CAAC,CAAC;EAEF,OAAOlB,QAAQ;AACnB,CAAC;AAED,OAAO,MAAMsB,cAAc,GAAG,MAAOH,EAAE,IAAK;EACxC,OAAOzB,GAAG,CAACO,IAAI,oBAAAa,MAAA,CAAoBK,EAAE,YAAS,CAAC;AACnD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}