{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\latex\\\\RenderLatex.jsx\";\nimport React from \"react\";\nimport \"katex/dist/katex.min.css\";\nimport { BlockMath, InlineMath } from \"react-katex\";\nimport NoTranslate from \"../utils/NoTranslate\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LatexRenderer = _ref => {\n  let {\n    text,\n    className = '',\n    style\n  } = _ref;\n  if (text === null || text === undefined) return null;\n\n  // Chuyển \\(...\\) => $...$, \\[...\\] => $$...$$\n  const formattedText = text.replace(/\\\\\\(/g, \"$\").replace(/\\\\\\)/g, \"$\").replace(/\\\\\\[/g, \"$$\").replace(/\\\\\\]/g, \"$$\");\n\n  // Kiểm tra xem có chứa LaTeX không\n  const hasLatex = /\\$\\$.*?\\$\\$|\\$.*?\\$/gs.test(formattedText);\n\n  // Nếu không có LaTeX, trả về text thuần\n  if (!hasLatex) {\n    return /*#__PURE__*/_jsxDEV(NoTranslate, {\n      as: \"span\",\n      className: className,\n      style: style,\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Regex phân chia nội dung theo LaTeX inline/block\n  const parts = formattedText.split(/(\\$\\$.*?\\$\\$|\\$.*?\\$)/gs);\n  const katexOptions = {\n    strict: false,\n    // Tắt strict mode để không báo warning với Unicode\n    throwOnError: false,\n    // Không throw error, fallback về text\n    errorColor: '#cc0000',\n    macros: {\n      \"\\\\RR\": \"\\\\mathbb{R}\",\n      \"\\\\NN\": \"\\\\mathbb{N}\",\n      \"\\\\ZZ\": \"\\\\mathbb{Z}\",\n      \"\\\\QQ\": \"\\\\mathbb{Q}\",\n      \"\\\\CC\": \"\\\\mathbb{C}\"\n    }\n  };\n  const elements = parts.map((part, index) => {\n    try {\n      if (part.startsWith(\"$$\") && part.endsWith(\"$$\")) {\n        return /*#__PURE__*/_jsxDEV(BlockMath, {\n          settings: katexOptions,\n          children: part.slice(2, -2)\n        }, \"block-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 24\n        }, this);\n      } else if (part.startsWith(\"$\") && part.endsWith(\"$\")) {\n        return /*#__PURE__*/_jsxDEV(InlineMath, {\n          settings: katexOptions,\n          children: part.slice(1, -1)\n        }, \"inline-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 24\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          children: part\n        }, \"text-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 24\n        }, this);\n      }\n    } catch (error) {\n      console.warn(\"KaTeX render error:\", error);\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        children: part\n      }, \"fallback-\".concat(index), false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 20\n      }, this); // fallback hiển thị text\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(NoTranslate, {\n    as: \"div\",\n    className: className,\n    style: style,\n    children: elements\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 9\n  }, this);\n};\n_c = LatexRenderer;\nexport default LatexRenderer;\nvar _c;\n$RefreshReg$(_c, \"LatexRenderer\");", "map": {"version": 3, "names": ["React", "BlockMath", "InlineMath", "NoTranslate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "text", "className", "style", "undefined", "formattedText", "replace", "hasLatex", "test", "as", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "parts", "split", "katexOptions", "strict", "throwOnError", "errorColor", "macros", "elements", "map", "part", "index", "startsWith", "endsWith", "settings", "slice", "concat", "error", "console", "warn", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/latex/RenderLatex.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport \"katex/dist/katex.min.css\";\r\nimport { BlockMath, InlineMath } from \"react-katex\";\r\nimport NoTranslate from \"../utils/NoTranslate\";\r\n\r\nconst LatexRenderer = ({ text, className = '', style }) => {\r\n    if (text === null || text === undefined) return null;\r\n\r\n    // Chuyển \\(...\\) => $...$, \\[...\\] => $$...$$\r\n    const formattedText = text\r\n        .replace(/\\\\\\(/g, \"$\")\r\n        .replace(/\\\\\\)/g, \"$\")\r\n        .replace(/\\\\\\[/g, \"$$\")\r\n        .replace(/\\\\\\]/g, \"$$\");\r\n\r\n    // Kiểm tra xem có chứa LaTeX không\r\n    const hasLatex = /\\$\\$.*?\\$\\$|\\$.*?\\$/gs.test(formattedText);\r\n\r\n    // Nếu không có LaTeX, tr<PERSON> về text thuần\r\n    if (!hasLatex) {\r\n        return (\r\n            <NoTranslate as=\"span\" className={className} style={style}>\r\n                {text}\r\n            </NoTranslate>\r\n        );\r\n    }\r\n\r\n    // Regex phân chia nội dung theo LaTeX inline/block\r\n    const parts = formattedText.split(/(\\$\\$.*?\\$\\$|\\$.*?\\$)/gs);\r\n\r\n    const katexOptions = {\r\n        strict: false, // Tắt strict mode để không báo warning với Unicode\r\n        throwOnError: false, // Không throw error, fallback về text\r\n        errorColor: '#cc0000',\r\n        macros: {\r\n            \"\\\\RR\": \"\\\\mathbb{R}\",\r\n            \"\\\\NN\": \"\\\\mathbb{N}\",\r\n            \"\\\\ZZ\": \"\\\\mathbb{Z}\",\r\n            \"\\\\QQ\": \"\\\\mathbb{Q}\",\r\n            \"\\\\CC\": \"\\\\mathbb{C}\"\r\n        }\r\n    };\r\n\r\n    const elements = parts.map((part, index) => {\r\n        try {\r\n            if (part.startsWith(\"$$\") && part.endsWith(\"$$\")) {\r\n                return <BlockMath key={`block-${index}`} settings={katexOptions}>{part.slice(2, -2)}</BlockMath>;\r\n            } else if (part.startsWith(\"$\") && part.endsWith(\"$\")) {\r\n                return <InlineMath key={`inline-${index}`} settings={katexOptions}>{part.slice(1, -1)}</InlineMath>;\r\n            } else {\r\n                return <span key={`text-${index}`}>{part}</span>;\r\n            }\r\n        } catch (error) {\r\n            console.warn(\"KaTeX render error:\", error);\r\n            return <span key={`fallback-${index}`}>{part}</span>; // fallback hiển thị text\r\n        }\r\n    });\r\n\r\n\r\n    return (\r\n        <NoTranslate as=\"div\" className={className} style={style}>\r\n            {elements}\r\n        </NoTranslate>\r\n    );\r\n};\r\n\r\nexport default LatexRenderer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,SAASC,SAAS,EAAEC,UAAU,QAAQ,aAAa;AACnD,OAAOC,WAAW,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,aAAa,GAAGC,IAAA,IAAqC;EAAA,IAApC;IAAEC,IAAI;IAAEC,SAAS,GAAG,EAAE;IAAEC;EAAM,CAAC,GAAAH,IAAA;EAClD,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKG,SAAS,EAAE,OAAO,IAAI;;EAEpD;EACA,MAAMC,aAAa,GAAGJ,IAAI,CACrBK,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;;EAE3B;EACA,MAAMC,QAAQ,GAAG,uBAAuB,CAACC,IAAI,CAACH,aAAa,CAAC;;EAE5D;EACA,IAAI,CAACE,QAAQ,EAAE;IACX,oBACIT,OAAA,CAACF,WAAW;MAACa,EAAE,EAAC,MAAM;MAACP,SAAS,EAAEA,SAAU;MAACC,KAAK,EAAEA,KAAM;MAAAO,QAAA,EACrDT;IAAI;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEtB;;EAEA;EACA,MAAMC,KAAK,GAAGV,aAAa,CAACW,KAAK,CAAC,yBAAyB,CAAC;EAE5D,MAAMC,YAAY,GAAG;IACjBC,MAAM,EAAE,KAAK;IAAE;IACfC,YAAY,EAAE,KAAK;IAAE;IACrBC,UAAU,EAAE,SAAS;IACrBC,MAAM,EAAE;MACJ,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE;IACZ;EACJ,CAAC;EAED,MAAMC,QAAQ,GAAGP,KAAK,CAACQ,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IACxC,IAAI;MACA,IAAID,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9C,oBAAO7B,OAAA,CAACJ,SAAS;UAAwBkC,QAAQ,EAAEX,YAAa;UAAAP,QAAA,EAAEc,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAAC,YAAAC,MAAA,CAAnDL,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA0D,CAAC;MACpG,CAAC,MAAM,IAAIU,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;QACnD,oBAAO7B,OAAA,CAACH,UAAU;UAAyBiC,QAAQ,EAAEX,YAAa;UAAAP,QAAA,EAAEc,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAAC,aAAAC,MAAA,CAAnDL,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA2D,CAAC;MACvG,CAAC,MAAM;QACH,oBAAOhB,OAAA;UAAAY,QAAA,EAA6Bc;QAAI,WAAAM,MAAA,CAAdL,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MACpD;IACJ,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACZC,OAAO,CAACC,IAAI,CAAC,qBAAqB,EAAEF,KAAK,CAAC;MAC1C,oBAAOjC,OAAA;QAAAY,QAAA,EAAiCc;MAAI,eAAAM,MAAA,CAAdL,KAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC,CAAC,CAAC;IAC1D;EACJ,CAAC,CAAC;EAGF,oBACIhB,OAAA,CAACF,WAAW;IAACa,EAAE,EAAC,KAAK;IAACP,SAAS,EAAEA,SAAU;IAACC,KAAK,EAAEA,KAAM;IAAAO,QAAA,EACpDY;EAAQ;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEtB,CAAC;AAACoB,EAAA,GA3DInC,aAAa;AA6DnB,eAAeA,aAAa;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}