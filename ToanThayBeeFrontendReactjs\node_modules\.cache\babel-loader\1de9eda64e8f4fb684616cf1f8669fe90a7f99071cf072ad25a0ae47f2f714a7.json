{"ast": null, "code": "// questionUtils.js\nimport { setErrorMessage } from \"../../features/state/stateApiSlice\";\n\n// Hàm validateCorrectAnswer nhận vào:\n// - question: đối tượng câu hỏi (chứa typeOfQuestion)\n// - correctAnswer: giá trị của correctAnswer\n// - dispatch: hàm dispatch từ Redux\n// - setErrorMessage: action creator để thêm lỗi vào errorSlice\nexport const validateCorrectAnswer = (question, correctAnswer, dispatch, content) => {\n  if (content.trim() === \"\") {\n    dispatch(setErrorMessage(\"Nội dung câu hỏi không được để trống!\"));\n    return false;\n  }\n  if (question.typeOfQuestion === null) {\n    dispatch(setErrorMessage(\"Loại câu hỏi không được để trống!\"));\n    return false;\n  }\n  if (correctAnswer.trim() === \"\") {\n    dispatch(setErrorMessage(\"Đáp án không được để trống!\"));\n    return false;\n  } else {\n    if (question.typeOfQuestion === 'TLN' && !/^[-+]?\\d+(\\.\\d+)?$/.test(correctAnswer.replace(\",\", \".\"))) {\n      dispatch(setErrorMessage(\"Đáp án phải là một số!\"));\n      return false;\n    }\n  }\n  if (question.class === null) {\n    dispatch(setErrorMessage(\"Lớp không được để trống!\"));\n    return false;\n  }\n  if (question.class !== null && question.chapter !== null) {\n    if (!question.chapter.startsWith(question.class)) {\n      dispatch(setErrorMessage(\"Chương này không phải là chương của lớp!\"));\n      return false;\n    }\n  }\n  if (question.typeOfQuestion === \"TN\") {\n    if (!/^[A-D]$/.test(correctAnswer.trim())) {\n      dispatch(setErrorMessage(\"Đáp án cho câu hỏi TN phải có dạng một ký tự A, B, C hoặc D!\"));\n      return false;\n    }\n  } else if (question.typeOfQuestion === \"DS\") {\n    const tokens = correctAnswer.trim().split(/\\s+/);\n    if (tokens.length === 0 || !tokens.every(token => token === \"Đ\" || token === \"S\")) {\n      dispatch(setErrorMessage(\"Đáp án cho câu hỏi DS phải có dạng các ký tự 'Đ' hoặc 'S', ví dụ: 'Đ Đ S S'!\"));\n      return false;\n    }\n  }\n  return true;\n};\n\n// Hàm processInput nhận vào:\n// - question: đối tượng câu hỏi (chứa typeOfQuestion)\n// - correctAnswer: đáp án đầu vào\n// - content: chuỗi content chứa nội dung câu hỏi và đáp án\n// Hàm trả về một object chứa:\n// - questionContent: nội dung câu hỏi đã được xử lý (loại bỏ tiền tố \"Câu X.\")\n// - newStatements: mảng đối tượng statement đã xử lý, trong đó có thuộc tính isCorrect dựa vào correctAnswer\nexport const processInput = (question, correctAnswer, content, dispatch) => {\n  // Tách content thành các dòng, loại bỏ khoảng trắng thừa và dòng rỗng\n  const lines = content.split(\"\\n\").map(line => line.trim()).filter(line => line !== \"\");\n  let questionContent = \"\";\n  const statementLines = [];\n  let foundOption = false;\n  if (question.typeOfQuestion === \"TN\") {\n    // Với TN, đáp án có định dạng \"A. ...\", \"B. ...\" (chữ in hoa và dấu chấm)\n    for (let line of lines) {\n      if (/^[A-Z]\\./.test(line)) {\n        foundOption = true;\n        statementLines.push(line.slice(2).trim());\n      } else if (!foundOption) {\n        questionContent += line + \" \";\n      } else {\n        statementLines[statementLines.length - 1] += \" \" + line;\n      }\n    }\n  } else if (question.typeOfQuestion === \"DS\") {\n    // Với DS, đáp án có định dạng \"a) ...\" (chữ thường và dấu ngoặc đơn)\n    for (let line of lines) {\n      if (/^[a-z]\\)/.test(line)) {\n        foundOption = true;\n        statementLines.push(line.slice(2).trim());\n      } else if (!foundOption) {\n        questionContent += line + \" \";\n      } else {\n        statementLines[statementLines.length - 1] += \" \" + line;\n      }\n    }\n  } else if (question.typeOfQuestion === \"TLN\") {\n    questionContent = content;\n  }\n\n  // Loại bỏ tiền tố \"Câu X.\" ở đầu nội dung câu hỏi nếu có\n  questionContent = questionContent.trim().replace(/^Câu\\s*\\d+\\.\\s*/, \"\");\n  if (question.typeOfQuestion === \"DS\" || question.typeOfQuestion === \"TN\") {\n    if (statementLines.length < 2 || statementLines === null || statementLines === undefined) {\n      dispatch(setErrorMessage(\"Mệnh đề không hợp lệ\"));\n      return false;\n    }\n  }\n\n  // Tạo mảng các đối tượng statement từ statementLines\n  const newStatements = statementLines.map(line => ({\n    content: line,\n    isCorrect: false,\n    needImage: false,\n    difficulty: null\n  }));\n\n  // Xử lý correctAnswer theo từng loại câu hỏi\n  if (question.typeOfQuestion === \"DS\") {\n    const correctTokens = correctAnswer.trim().split(/\\s+/);\n    correctTokens.forEach((answer, index) => {\n      newStatements[index].isCorrect = answer === \"Đ\";\n    });\n  } else if (question.typeOfQuestion === \"TN\") {\n    const letter = correctAnswer.trim();\n    const correctIndex = letter.charCodeAt(0) - \"A\".charCodeAt(0);\n    newStatements.forEach((statement, index) => {\n      statement.isCorrect = index === correctIndex;\n    });\n  }\n  return {\n    questionContent,\n    newStatements\n  };\n};\nexport const validateInput = (question, dispatch) => {\n  if (question.class === null) {\n    dispatch(setErrorMessage(\"Lớp không được để trống!\"));\n    return false;\n  }\n  if (question.correctAnswer) {\n    if (!/^[-+]?\\d+(\\.\\d+)?$/.test(question.correctAnswer)) {\n      dispatch(setErrorMessage(\"Đáp án phải là một số!\"));\n      return false;\n    }\n  }\n  if (question.class !== null && question.chapter !== null) {\n    if (!question.chapter.startsWith(question.class)) {\n      dispatch(setErrorMessage(\"Chương này không phải là chương của lớp!\"));\n      return false;\n    }\n  }\n  if (question.typeOfQuestion === 'TN') {\n    if (question.statements.filter(statement => statement.isCorrect === true).length !== 1) {\n      dispatch(setErrorMessage(\"Câu hỏi TN có một đáp án đúng!\"));\n      return false;\n    }\n  }\n  return true;\n};\nexport const processInputForUpdate = question => {\n  var _question$statements, _newQuestion$content, _newQuestion$correctA, _newQuestion$solution, _newQuestion$solution2, _newQuestion$descript, _newQuestion$statemen;\n  const newQuestion = {\n    ...question,\n    statements: (_question$statements = question.statements) === null || _question$statements === void 0 ? void 0 : _question$statements.map(statement => ({\n      ...statement\n    }))\n  };\n  newQuestion.content = (_newQuestion$content = newQuestion.content) === null || _newQuestion$content === void 0 ? void 0 : _newQuestion$content.trim().replace(/^Câu\\s*\\d+\\.\\s*/, \"\");\n  newQuestion.correctAnswer = (_newQuestion$correctA = newQuestion.correctAnswer) === null || _newQuestion$correctA === void 0 ? void 0 : _newQuestion$correctA.trim().replace(\",\", \".\");\n  newQuestion.solution = (_newQuestion$solution = newQuestion.solution) === null || _newQuestion$solution === void 0 ? void 0 : _newQuestion$solution.trim();\n  newQuestion.solutionUrl = (_newQuestion$solution2 = newQuestion.solutionUrl) === null || _newQuestion$solution2 === void 0 ? void 0 : _newQuestion$solution2.trim();\n  newQuestion.description = (_newQuestion$descript = newQuestion.description) === null || _newQuestion$descript === void 0 ? void 0 : _newQuestion$descript.trim();\n  (_newQuestion$statemen = newQuestion.statements) === null || _newQuestion$statemen === void 0 ? void 0 : _newQuestion$statemen.forEach(statement => {\n    var _statement$content;\n    statement.content = (_statement$content = statement.content) === null || _statement$content === void 0 ? void 0 : _statement$content.trim();\n  });\n  return newQuestion;\n};\nexport const normalizeText = text => {\n  return text.normalize(\"NFC\").replace(/[а-яА-Я]/g, c => String.fromCharCode(c.charCodeAt(0) - 848)).replace(/[\\u200B-\\u200D\\uFEFF]/g, \"\").replace(/[“”]/g, '\"').replace(/[‘’]/g, \"'\").replace(/–/g, \"-\").replace(/\\\\section\\*\\{([^}]+)\\}/gi, \"$1\").replace(/^!\\[.*?\\]\\(.*?\\)$/gim, \"\") // Xoá ảnh Markdown\n  .replace(/\\\\begin\\{tabular\\}[\\s\\S]*?\\\\end\\{tabular\\}/gi, \"\").replace(/\\\\end\\{tabular\\}/gi, \"\").replace(/\\\\begin\\{tabular\\}[\\s\\S]*$/gi, \"\").replace(/^\\s*\\\\hline\\s*$/gim, \"\") // ✅ xóa dòng chỉ chứa \\hline\n  .replace(/^\\s*[\\}\\{]?\\s*&.*?\\\\\\\\\\s*$/gim, \"\") // ✅ xóa dòng dạng `} & ... \\\\`\n  .replace(/^\\s*&.*?\\\\\\\\\\s*$/gim, \"\") // ✅ xóa dòng bắt đầu bằng `& ... \\\\`\n  .replace(/^\\s*\\\\?[\\}\\{]?\\s*&.*$/gim, \"\") // ✅ xóa dòng chứa toàn dấu bảng (&)\n  .trim();\n};\nexport const splitCorrectAnswerTN = correctAnswersText => {\n  if (correctAnswersText.trim() === \"\") return [];\n  // Chuyển đổi chuỗi đáp án thành mảng các đáp án\n  correctAnswersText = normalizeText(correctAnswersText);\n  const correctAnswers = correctAnswersText.trim().split(/\\s+/) // tách theo 1 hoặc nhiều khoảng trắng\n  .filter(Boolean) // loại bỏ phần tử rỗng (nếu có)\n  .map(ans => ans.toUpperCase()); // viết hoa toàn bộ\n\n  return correctAnswers;\n};\nexport const splitContentTN = (content, correctAnswersText, dispatch) => {\n  if (content.trim() === \"\" || correctAnswersText.trim() === \"\") return [];\n  content = normalizeText(content);\n  const lines = content.split(\"\\n\").map(line => line.trim()).filter(line => line !== \"\");\n  let questionsTN = [];\n  let questionContent = \"\";\n  let statementLines = [];\n  let foundQuestion = false;\n  let foundStatement = false;\n  let foundAnswerLine = false;\n  let currentSolution = \"\";\n  let index = 0;\n  let countTN = 0;\n  const correctAnswers = splitCorrectAnswerTN(correctAnswersText);\n  for (let line of lines) {\n    // Nếu là dòng bắt đầu câu hỏi mới\n    if (/^([Cc]âu)\\s*\\d+[\\.:]/.test(line)) {\n      if (foundQuestion) {\n        // Push câu trước đó trước khi reset\n        questionsTN.push({\n          questionData: {\n            typeOfQuestion: \"TN\",\n            content: questionContent.trim(),\n            solution: currentSolution.trim(),\n            // thêm lời giải\n            class: null,\n            chapter: null,\n            difficulty: null,\n            imageUrl: null\n          },\n          statements: statementLines\n        });\n        index++;\n      }\n      foundQuestion = true;\n      questionContent = line.replace(/^([Cc]âu)\\s*\\d+[\\.:]\\s*/, \"\");\n      statementLines = [];\n      currentSolution = \"\";\n      foundStatement = false;\n      foundAnswerLine = false;\n    }\n\n    // Nếu là đáp án A. B. C.\n    else if (/^[a-dA-D][\\.\\)]/.test(line)) {\n      const match = line.match(/^([a-dA-D])[\\.\\)]\\s*(.*)$/);\n      if (match) {\n        const [, option, text] = match;\n        foundStatement = true;\n        statementLines.push({\n          order: countTN,\n          imageUrl: null,\n          content: text.trim(),\n          isCorrect: correctAnswers[index] && correctAnswers[index].toUpperCase() === option.toUpperCase()\n        });\n        countTN++;\n      }\n    }\n\n    // Nếu là dòng \"Đáp án: ...\"\n    else if (/^(đáp án|dap an|lời giải|loi giai)\\s*[:：]/i.test(line)) {\n      foundAnswerLine = true;\n      currentSolution += line + \"\\n\"; // ✅ giữ lại dòng này\n    }\n\n    // Nếu là dòng lời giải (sau dòng Đáp án)\n    else if (foundAnswerLine) {\n      currentSolution += line + \"\\n\";\n    }\n\n    // Nội dung phụ của đáp án\n    else if (foundStatement) {\n      statementLines[statementLines.length - 1].content += \" \" + line;\n    }\n\n    // Nội dung phụ của câu hỏi\n    else {\n      questionContent += \" \" + line;\n    }\n  }\n\n  // Thêm câu cuối cùng\n  if (foundQuestion) {\n    questionsTN.push({\n      questionData: {\n        typeOfQuestion: \"TN\",\n        content: questionContent.trim(),\n        solution: currentSolution.trim() // thêm lời giải cuối cùng\n      },\n      statements: statementLines\n    });\n  }\n  if (questionsTN.length !== correctAnswers.length) {\n    dispatch(setErrorMessage(\"S\\u1ED1 l\\u01B0\\u1EE3ng \\u0111\\xE1p \\xE1n kh\\xF4ng kh\\u1EDBp v\\u1EDBi s\\u1ED1 l\\u01B0\\u1EE3ng c\\xE2u h\\u1ECFi! \".concat(questionsTN.length, \" - \").concat(correctAnswers.length)));\n    return [];\n  }\n  return questionsTN;\n};\nexport const splitCorrectAnswerDS = correctAnswersText => {\n  if (correctAnswersText.trim() === \"\") return [];\n  // Chuyển đổi chuỗi đáp án thành mảng các đáp án\n  correctAnswersText = normalizeText(correctAnswersText);\n  const correctAnswers = correctAnswersText.trim().replace(/-/g, \"\").toUpperCase().split(/\\s+/).map(group => group.split(\"\"));\n  return correctAnswers;\n};\nexport const splitContentDS = (content, correctAnswersText, dispatch) => {\n  if (content.trim() === \"\" || correctAnswersText.trim() === \"\") return [];\n  content = normalizeText(content);\n  const lines = content.split(\"\\n\").map(line => line.trim()).filter(line => line !== \"\");\n  const ANSWER_OR_SOLUTION_REGEX = /^(đáp án|dap an|lời giải|loi giai)\\s*[:：]/i;\n  let questionsDS = [];\n  let questionContent = \"\";\n  let statementLines = [];\n  let foundQuestion = false;\n  let foundStatement = false;\n  let foundAnswerLine = false;\n  let currentSolution = \"\";\n  let index = 0;\n  const correctAnswers = splitCorrectAnswerDS(correctAnswersText);\n  for (let line of lines) {\n    // Nhận diện câu hỏi mới\n    if (/^([Cc]âu)\\s*\\d+[\\.:]/.test(line)) {\n      if (foundQuestion) {\n        if (!correctAnswers[index]) {\n          dispatch(setErrorMessage(\"Số lượng đáp án không khớp với số lượng câu hỏi!\"));\n          return false;\n        }\n        if (statementLines.length !== correctAnswers[index].length) {\n          dispatch(setErrorMessage(\"S\\u1ED1 l\\u01B0\\u1EE3ng \\u0111\\xE1p \\xE1n kh\\xF4ng kh\\u1EDBp v\\u1EDBi s\\u1ED1 l\\u01B0\\u1EE3ng m\\u1EC7nh \\u0111\\u1EC1 \\u1EDF C\\xE2u \".concat(index + 1, \"!\")));\n          return false;\n        }\n        questionsDS.push({\n          questionData: {\n            typeOfQuestion: \"DS\",\n            content: questionContent.trim(),\n            solution: currentSolution.trim(),\n            class: null,\n            chapter: null,\n            difficulty: null,\n            imageUrl: null\n          },\n          statements: statementLines\n        });\n        index++;\n      }\n      foundQuestion = true;\n      questionContent = line.replace(/^([Cc]âu)\\s*\\d+[\\.:]\\s*/, \"\");\n      statementLines = [];\n      currentSolution = \"\";\n      foundStatement = false;\n      foundAnswerLine = false;\n    }\n\n    // Dòng mệnh đề\n    else if (/^[a-dA-D][\\.\\)]/.test(line)) {\n      const match = line.match(/^([a-dA-D])[\\.\\)]\\s*(.*)$/);\n      if (match) {\n        var _correctAnswers$index;\n        const [,, text] = match;\n        foundStatement = true;\n        const currentAnswer = (_correctAnswers$index = correctAnswers[index]) === null || _correctAnswers$index === void 0 ? void 0 : _correctAnswers$index[statementLines.length];\n        statementLines.push({\n          imageUrl: null,\n          order: statementLines.length,\n          content: text.trim(),\n          isCorrect: currentAnswer === \"Đ\" || currentAnswer === \"D\"\n        });\n      }\n    }\n\n    // Dòng đáp án hoặc lời giải\n    else if (ANSWER_OR_SOLUTION_REGEX.test(line)) {\n      foundAnswerLine = true;\n      currentSolution += line + \"\\n\"; // ✅ giữ lại dòng này\n    }\n\n    // Lời giải sau dòng đáp án\n    else if (foundAnswerLine) {\n      currentSolution += line + \" \";\n    }\n\n    // Nội dung bổ sung cho mệnh đề\n    else if (foundStatement) {\n      statementLines[statementLines.length - 1].content += \" \" + line;\n    }\n\n    // Nội dung phụ của câu hỏi\n    else {\n      questionContent += \" \" + line;\n    }\n  }\n\n  // Xử lý câu cuối cùng\n  if (foundQuestion) {\n    if (!correctAnswers[index]) {\n      dispatch(setErrorMessage(\"Số lượng đáp án không khớp với số lượng câu hỏi!\"));\n      return [];\n    }\n    if (statementLines.length !== correctAnswers[index].length) {\n      dispatch(setErrorMessage(\"S\\u1ED1 l\\u01B0\\u1EE3ng \\u0111\\xE1p \\xE1n kh\\xF4ng kh\\u1EDBp v\\u1EDBi s\\u1ED1 l\\u01B0\\u1EE3ng m\\u1EC7nh \\u0111\\u1EC1 \\u1EDF C\\xE2u \".concat(index + 1, \"!\")));\n      return [];\n    }\n    questionsDS.push({\n      questionData: {\n        typeOfQuestion: \"DS\",\n        content: questionContent.trim(),\n        solution: currentSolution.trim()\n      },\n      statements: statementLines\n    });\n  }\n  return questionsDS;\n};\nexport const splitCorrectAnswerTLN = correctAnswersText => {\n  if (correctAnswersText.trim() === \"\") return [];\n  // Chuyển đổi chuỗi đáp án thành mảng các đáp án\n  correctAnswersText = normalizeText(correctAnswersText);\n  const correctAnswers = correctAnswersText.trim().replace(/,/g, \".\").split(/\\s+/) // tách theo 1 hoặc nhiều khoảng trắng\n  .filter(Boolean); // loại bỏ chuỗi rỗng (nếu có)\n  // console.log(correctAnswers)\n  return correctAnswers;\n};\nexport const splitContentTLN = (content, correctAnswersText, dispatch) => {\n  if (content.trim() === \"\" || correctAnswersText.trim() === \"\") return [];\n  content = normalizeText(content);\n  const lines = content.split(\"\\n\").map(line => line.trim()).filter(line => line !== \"\");\n  const ANSWER_OR_SOLUTION_REGEX = /^(đáp án|dap an|lời giải|loi giai)\\s*[:：]/i;\n  let questionsTLN = [];\n  let questionContent = \"\";\n  let currentSolution = \"\";\n  let foundQuestion = false;\n  let foundAnswerLine = false;\n  let index = 0;\n  const correctAnswers = splitCorrectAnswerTLN(correctAnswersText);\n  for (let line of lines) {\n    // Dòng bắt đầu câu hỏi mới\n    if (/^([Cc]âu)\\s*\\d+[\\.:]/.test(line)) {\n      if (foundQuestion) {\n        if (index >= correctAnswers.length) {\n          dispatch(setErrorMessage(\"Số lượng đáp án không khớp với số lượng câu hỏi!\"));\n          return false;\n        }\n        questionsTLN.push({\n          questionData: {\n            typeOfQuestion: \"TLN\",\n            content: questionContent.trim(),\n            correctAnswer: correctAnswers[index],\n            solution: currentSolution.trim(),\n            class: null,\n            chapter: null,\n            difficulty: null,\n            imageUrl: null\n          }\n        });\n        index++;\n      }\n      foundQuestion = true;\n      questionContent = line.replace(/^([Cc]âu)\\s*\\d+[\\.:]\\s*/, \"\");\n      currentSolution = \"\";\n      foundAnswerLine = false;\n    }\n\n    // Dòng \"Đáp án\"/\"Lời giải\"\n    else if (ANSWER_OR_SOLUTION_REGEX.test(line)) {\n      foundAnswerLine = true;\n      currentSolution += line + \"\\n\"; // ✅ giữ lại dòng này\n    }\n\n    // Nội dung lời giải (sau dòng Đáp án)\n    else if (foundAnswerLine) {\n      currentSolution += line + \" \";\n    }\n\n    // Nội dung bổ sung cho câu hỏi\n    else {\n      questionContent += \" \" + line;\n    }\n  }\n\n  // Câu cuối cùng\n  if (foundQuestion) {\n    if (index >= correctAnswers.length) {\n      dispatch(setErrorMessage(\"Số lượng đáp án không khớp với số lượng câu hỏi!\"));\n      return [];\n    }\n    questionsTLN.push({\n      questionData: {\n        typeOfQuestion: \"TLN\",\n        content: questionContent.trim(),\n        correctAnswer: correctAnswers[index],\n        solution: currentSolution.trim()\n      }\n    });\n  }\n  return questionsTLN;\n};\nexport const validateExamData = (examData, dispatch) => {\n  if (examData.name.trim() === \"\") {\n    dispatch(setErrorMessage(\"Tên đề thi không được để trống!\"));\n    return false;\n  }\n  if (examData.class === null) {\n    dispatch(setErrorMessage(\"Lớp không được để trống!\"));\n    return false;\n  }\n  if (examData.typeOfExam === null) {\n    dispatch(setErrorMessage(\"Kiểu đề thi không được để trống!\"));\n    return false;\n  }\n  if (examData.year === null) {\n    dispatch(setErrorMessage(\"Năm không được để trống!\"));\n    return false;\n  }\n  if (examData.passRate < 0 || examData.passRate > 100) {\n    dispatch(setErrorMessage(\"Tỷ lệ đạt không hợp lệ!\"));\n    return false;\n  }\n  return true;\n};", "map": {"version": 3, "names": ["setErrorMessage", "validateCorrectAnswer", "question", "<PERSON><PERSON><PERSON><PERSON>", "dispatch", "content", "trim", "typeOfQuestion", "test", "replace", "class", "chapter", "startsWith", "tokens", "split", "length", "every", "token", "processInput", "lines", "map", "line", "filter", "questionContent", "statementLines", "foundOption", "push", "slice", "undefined", "newStatements", "isCorrect", "needImage", "difficulty", "correctTokens", "for<PERSON>ach", "answer", "index", "letter", "correctIndex", "charCodeAt", "statement", "validateInput", "statements", "processInputForUpdate", "_question$statements", "_newQuestion$content", "_newQuestion$correctA", "_newQuestion$solution", "_newQuestion$solution2", "_newQuestion$descript", "_newQuestion$statemen", "newQuestion", "solution", "solutionUrl", "description", "_statement$content", "normalizeText", "text", "normalize", "c", "String", "fromCharCode", "splitCorrectAnswerTN", "correctAnswersText", "correctAnswers", "Boolean", "ans", "toUpperCase", "splitContentTN", "questionsTN", "foundQuestion", "foundStatement", "foundAnswerLine", "currentSolution", "countTN", "questionData", "imageUrl", "match", "option", "order", "concat", "splitCorrectAnswerDS", "group", "splitContentDS", "ANSWER_OR_SOLUTION_REGEX", "questionsDS", "_correctAnswers$index", "currentAnswer", "splitCorrectAnswerTLN", "splitContentTLN", "questionsTLN", "validateExamData", "examData", "name", "typeOfExam", "year", "passRate"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/utils/question/questionUtils.js"], "sourcesContent": ["// questionUtils.js\r\nimport { setErrorMessage } from \"../../features/state/stateApiSlice\";\r\n\r\n// Hàm validateCorrectAnswer nhận vào:\r\n// - question: đối tượng câu hỏi (chứa typeOfQuestion)\r\n// - correctAnswer: giá trị của correctAnswer\r\n// - dispatch: hàm dispatch từ Redux\r\n// - setErrorMessage: action creator để thêm lỗi vào errorSlice\r\nexport const validateCorrectAnswer = (question, correctAnswer, dispatch, content) => {\r\n    if (content.trim() === \"\") {\r\n        dispatch(setErrorMessage(\"Nội dung câu hỏi không được để trống!\"));\r\n        return false;\r\n    }\r\n    if (question.typeOfQuestion === null) {\r\n        dispatch(setErrorMessage(\"Loại câu hỏi không được để trống!\"));\r\n        return false;\r\n    }\r\n    if (correctAnswer.trim() === \"\") {\r\n        dispatch(setErrorMessage(\"Đáp án không được để trống!\"));\r\n        return false;\r\n\r\n    } else {\r\n        if (question.typeOfQuestion === 'TLN' && !/^[-+]?\\d+(\\.\\d+)?$/.test(correctAnswer.replace(\",\", \".\"))) {\r\n            dispatch(setErrorMessage(\"Đáp án phải là một số!\"));\r\n            return false;\r\n        }\r\n    }\r\n\r\n    if (question.class === null) {\r\n        dispatch(setErrorMessage(\"Lớp không được để trống!\"));\r\n        return false;\r\n    }\r\n\r\n    if (question.class !== null && question.chapter !== null) {\r\n        if (!question.chapter.startsWith(question.class)) {\r\n            dispatch(setErrorMessage(\"Chương này không phải là chương của lớp!\"));\r\n            return false;\r\n        }\r\n    }\r\n\r\n    if (question.typeOfQuestion === \"TN\") {\r\n        if (!/^[A-D]$/.test(correctAnswer.trim())) {\r\n            dispatch(setErrorMessage(\"Đáp án cho câu hỏi TN phải có dạng một ký tự A, B, C hoặc D!\"));\r\n            return false;\r\n        }\r\n    } else if (question.typeOfQuestion === \"DS\") {\r\n        const tokens = correctAnswer.trim().split(/\\s+/);\r\n        if (tokens.length === 0 || !tokens.every((token) => token === \"Đ\" || token === \"S\")) {\r\n            dispatch(setErrorMessage(\"Đáp án cho câu hỏi DS phải có dạng các ký tự 'Đ' hoặc 'S', ví dụ: 'Đ Đ S S'!\"));\r\n            return false;\r\n        }\r\n    }\r\n\r\n\r\n    return true;\r\n};\r\n\r\n// Hàm processInput nhận vào:\r\n// - question: đối tượng câu hỏi (chứa typeOfQuestion)\r\n// - correctAnswer: đáp án đầu vào\r\n// - content: chuỗi content chứa nội dung câu hỏi và đáp án\r\n// Hàm trả về một object chứa:\r\n// - questionContent: nội dung câu hỏi đã được xử lý (loại bỏ tiền tố \"Câu X.\")\r\n// - newStatements: mảng đối tượng statement đã xử lý, trong đó có thuộc tính isCorrect dựa vào correctAnswer\r\nexport const processInput = (question, correctAnswer, content, dispatch) => {\r\n    // Tách content thành các dòng, loại bỏ khoảng trắng thừa và dòng rỗng\r\n    const lines = content\r\n        .split(\"\\n\")\r\n        .map((line) => line.trim())\r\n        .filter((line) => line !== \"\");\r\n\r\n    let questionContent = \"\";\r\n    const statementLines = [];\r\n    let foundOption = false;\r\n\r\n    if (question.typeOfQuestion === \"TN\") {\r\n        // Với TN, đáp án có định dạng \"A. ...\", \"B. ...\" (chữ in hoa và dấu chấm)\r\n        for (let line of lines) {\r\n            if (/^[A-Z]\\./.test(line)) {\r\n                foundOption = true;\r\n                statementLines.push(line.slice(2).trim());\r\n            } else if (!foundOption) {\r\n                questionContent += line + \" \";\r\n            } else {\r\n                statementLines[statementLines.length - 1] += \" \" + line;\r\n            }\r\n        }\r\n    } else if (question.typeOfQuestion === \"DS\") {\r\n        // Với DS, đáp án có định dạng \"a) ...\" (chữ thường và dấu ngoặc đơn)\r\n        for (let line of lines) {\r\n            if (/^[a-z]\\)/.test(line)) {\r\n                foundOption = true;\r\n                statementLines.push(line.slice(2).trim());\r\n            } else if (!foundOption) {\r\n                questionContent += line + \" \";\r\n            } else {\r\n                statementLines[statementLines.length - 1] += \" \" + line;\r\n            }\r\n        }\r\n    } else if (question.typeOfQuestion === \"TLN\") {\r\n        questionContent = content;\r\n    }\r\n\r\n    // Loại bỏ tiền tố \"Câu X.\" ở đầu nội dung câu hỏi nếu có\r\n    questionContent = questionContent.trim().replace(/^Câu\\s*\\d+\\.\\s*/, \"\");\r\n\r\n    if (question.typeOfQuestion === \"DS\" || question.typeOfQuestion === \"TN\") {\r\n        if (statementLines.length < 2 || statementLines === null || statementLines === undefined) {\r\n            dispatch(setErrorMessage(\"Mệnh đề không hợp lệ\"));\r\n            return false;\r\n        }\r\n    }\r\n\r\n    // Tạo mảng các đối tượng statement từ statementLines\r\n    const newStatements = statementLines.map((line) => ({\r\n        content: line,\r\n        isCorrect: false,\r\n        needImage: false,\r\n        difficulty: null,\r\n    }));\r\n\r\n    // Xử lý correctAnswer theo từng loại câu hỏi\r\n    if (question.typeOfQuestion === \"DS\") {\r\n        const correctTokens = correctAnswer.trim().split(/\\s+/);\r\n        correctTokens.forEach((answer, index) => {\r\n            newStatements[index].isCorrect = answer === \"Đ\";\r\n        });\r\n    } else if (question.typeOfQuestion === \"TN\") {\r\n        const letter = correctAnswer.trim();\r\n        const correctIndex = letter.charCodeAt(0) - \"A\".charCodeAt(0);\r\n        newStatements.forEach((statement, index) => {\r\n            statement.isCorrect = index === correctIndex;\r\n        });\r\n    }\r\n\r\n    return { questionContent, newStatements };\r\n};\r\n\r\n\r\nexport const validateInput = (question, dispatch) => {\r\n    if (question.class === null) {\r\n        dispatch(setErrorMessage(\"Lớp không được để trống!\"));\r\n        return false;\r\n    }\r\n    if (question.correctAnswer) {\r\n        if (!/^[-+]?\\d+(\\.\\d+)?$/.test(question.correctAnswer)) {\r\n            dispatch(setErrorMessage(\"Đáp án phải là một số!\"));\r\n            return false;\r\n        }\r\n    }\r\n\r\n    if (question.class !== null && question.chapter !== null) {\r\n        if (!question.chapter.startsWith(question.class)) {\r\n            dispatch(setErrorMessage(\"Chương này không phải là chương của lớp!\"));\r\n            return false;\r\n        }\r\n    }\r\n\r\n    if (question.typeOfQuestion === 'TN') {\r\n        if (question.statements.filter(statement => statement.isCorrect === true).length !== 1) {\r\n            dispatch(setErrorMessage(\"Câu hỏi TN có một đáp án đúng!\"));\r\n            return false;\r\n        }\r\n    }\r\n\r\n    return true;\r\n}\r\n\r\nexport const processInputForUpdate = (question) => {\r\n    const newQuestion = {\r\n        ...question,\r\n        statements: question.statements?.map(statement => ({ ...statement }))\r\n    };\r\n\r\n    newQuestion.content = newQuestion.content?.trim().replace(/^Câu\\s*\\d+\\.\\s*/, \"\");\r\n    newQuestion.correctAnswer = newQuestion.correctAnswer?.trim().replace(\",\", \".\");\r\n    newQuestion.solution = newQuestion.solution?.trim();\r\n    newQuestion.solutionUrl = newQuestion.solutionUrl?.trim();\r\n    newQuestion.description = newQuestion.description?.trim();\r\n\r\n    newQuestion.statements?.forEach((statement) => {\r\n        statement.content = statement.content?.trim();\r\n    });\r\n\r\n    return newQuestion;\r\n};\r\n\r\nexport const normalizeText = (text) => {\r\n    return text\r\n        .normalize(\"NFC\")\r\n        .replace(/[а-яА-Я]/g, (c) => String.fromCharCode(c.charCodeAt(0) - 848))\r\n        .replace(/[\\u200B-\\u200D\\uFEFF]/g, \"\")\r\n        .replace(/[“”]/g, '\"')\r\n        .replace(/[‘’]/g, \"'\")\r\n        .replace(/–/g, \"-\")\r\n        .replace(/\\\\section\\*\\{([^}]+)\\}/gi, \"$1\")\r\n        .replace(/^!\\[.*?\\]\\(.*?\\)$/gim, \"\") // Xoá ảnh Markdown\r\n        .replace(/\\\\begin\\{tabular\\}[\\s\\S]*?\\\\end\\{tabular\\}/gi, \"\")\r\n        .replace(/\\\\end\\{tabular\\}/gi, \"\")\r\n        .replace(/\\\\begin\\{tabular\\}[\\s\\S]*$/gi, \"\")\r\n        .replace(/^\\s*\\\\hline\\s*$/gim, \"\")                    // ✅ xóa dòng chỉ chứa \\hline\r\n        .replace(/^\\s*[\\}\\{]?\\s*&.*?\\\\\\\\\\s*$/gim, \"\")         // ✅ xóa dòng dạng `} & ... \\\\`\r\n        .replace(/^\\s*&.*?\\\\\\\\\\s*$/gim, \"\")                   // ✅ xóa dòng bắt đầu bằng `& ... \\\\`\r\n        .replace(/^\\s*\\\\?[\\}\\{]?\\s*&.*$/gim, \"\")              // ✅ xóa dòng chứa toàn dấu bảng (&)\r\n        .trim();\r\n};\r\n\r\n\r\nexport const splitCorrectAnswerTN = (correctAnswersText) => {\r\n    if (correctAnswersText.trim() === \"\") return [];\r\n    // Chuyển đổi chuỗi đáp án thành mảng các đáp án\r\n    correctAnswersText = normalizeText(correctAnswersText);\r\n\r\n    const correctAnswers = correctAnswersText\r\n        .trim()\r\n        .split(/\\s+/)        // tách theo 1 hoặc nhiều khoảng trắng\r\n        .filter(Boolean)     // loại bỏ phần tử rỗng (nếu có)\r\n        .map(ans => ans.toUpperCase());  // viết hoa toàn bộ\r\n\r\n    return correctAnswers;\r\n};\r\n\r\n\r\nexport const splitContentTN = (content, correctAnswersText, dispatch) => {\r\n    if (content.trim() === \"\" || correctAnswersText.trim() === \"\")\r\n        return []\r\n\r\n    content = normalizeText(content);\r\n    const lines = content\r\n        .split(\"\\n\")\r\n        .map((line) => line.trim())\r\n        .filter((line) => line !== \"\");\r\n\r\n    let questionsTN = [];\r\n    let questionContent = \"\";\r\n    let statementLines = [];\r\n    let foundQuestion = false;\r\n    let foundStatement = false;\r\n    let foundAnswerLine = false;\r\n    let currentSolution = \"\";\r\n    let index = 0;\r\n    let countTN = 0;\r\n\r\n    const correctAnswers = splitCorrectAnswerTN(correctAnswersText);\r\n\r\n    for (let line of lines) {\r\n        // Nếu là dòng bắt đầu câu hỏi mới\r\n        if (/^([Cc]âu)\\s*\\d+[\\.:]/.test(line)) {\r\n            if (foundQuestion) {\r\n                // Push câu trước đó trước khi reset\r\n                questionsTN.push({\r\n                    questionData: {\r\n                        typeOfQuestion: \"TN\",\r\n                        content: questionContent.trim(),\r\n                        solution: currentSolution.trim(), // thêm lời giải\r\n                        class: null,\r\n                        chapter: null,\r\n                        difficulty: null,\r\n                        imageUrl: null,\r\n                    },\r\n                    statements: statementLines,\r\n                });\r\n                index++;\r\n            }\r\n\r\n            foundQuestion = true;\r\n            questionContent = line.replace(/^([Cc]âu)\\s*\\d+[\\.:]\\s*/, \"\");\r\n            statementLines = [];\r\n            currentSolution = \"\";\r\n            foundStatement = false;\r\n            foundAnswerLine = false;\r\n        }\r\n\r\n        // Nếu là đáp án A. B. C.\r\n        else if (/^[a-dA-D][\\.\\)]/.test(line)) {\r\n            const match = line.match(/^([a-dA-D])[\\.\\)]\\s*(.*)$/);\r\n            if (match) {\r\n                const [, option, text] = match;\r\n                foundStatement = true;\r\n                statementLines.push({\r\n                    order: countTN,\r\n                    imageUrl: null,\r\n                    content: text.trim(),\r\n                    isCorrect:\r\n                        correctAnswers[index] &&\r\n                        correctAnswers[index].toUpperCase() === option.toUpperCase(),\r\n                });\r\n                countTN++;\r\n            }\r\n        }\r\n\r\n        // Nếu là dòng \"Đáp án: ...\"\r\n        else if (/^(đáp án|dap an|lời giải|loi giai)\\s*[:：]/i.test(line)) {\r\n            foundAnswerLine = true;\r\n            currentSolution += line + \"\\n\"; // ✅ giữ lại dòng này\r\n        }\r\n\r\n        // Nếu là dòng lời giải (sau dòng Đáp án)\r\n        else if (foundAnswerLine) {\r\n            currentSolution += line + \"\\n\";\r\n        }\r\n\r\n        // Nội dung phụ của đáp án\r\n        else if (foundStatement) {\r\n            statementLines[statementLines.length - 1].content += \" \" + line;\r\n        }\r\n\r\n        // Nội dung phụ của câu hỏi\r\n        else {\r\n            questionContent += \" \" + line;\r\n        }\r\n    }\r\n\r\n    // Thêm câu cuối cùng\r\n    if (foundQuestion) {\r\n        questionsTN.push({\r\n            questionData: {\r\n                typeOfQuestion: \"TN\",\r\n                content: questionContent.trim(),\r\n                solution: currentSolution.trim(), // thêm lời giải cuối cùng\r\n            },\r\n            statements: statementLines,\r\n        });\r\n    }\r\n\r\n    if (questionsTN.length !== correctAnswers.length) {\r\n        dispatch(\r\n            setErrorMessage(\r\n                `Số lượng đáp án không khớp với số lượng câu hỏi! ${questionsTN.length} - ${correctAnswers.length}`\r\n            )\r\n        );\r\n        return [];\r\n    }\r\n\r\n    return questionsTN\r\n};\r\n\r\n\r\nexport const splitCorrectAnswerDS = (correctAnswersText) => {\r\n    if (correctAnswersText.trim() === \"\") return [];\r\n    // Chuyển đổi chuỗi đáp án thành mảng các đáp án\r\n    correctAnswersText = normalizeText(correctAnswersText);\r\n\r\n    const correctAnswers = correctAnswersText\r\n        .trim()\r\n        .replace(/-/g, \"\")\r\n        .toUpperCase()\r\n        .split(/\\s+/)\r\n        .map(group => group.split(\"\"));\r\n\r\n    return correctAnswers\r\n}\r\n\r\n\r\nexport const splitContentDS = (content, correctAnswersText, dispatch) => {\r\n    if (content.trim() === \"\" || correctAnswersText.trim() === \"\") return []\r\n\r\n    content = normalizeText(content);\r\n\r\n    const lines = content\r\n        .split(\"\\n\")\r\n        .map((line) => line.trim())\r\n        .filter((line) => line !== \"\");\r\n\r\n    const ANSWER_OR_SOLUTION_REGEX = /^(đáp án|dap an|lời giải|loi giai)\\s*[:：]/i;\r\n\r\n    let questionsDS = [];\r\n    let questionContent = \"\";\r\n    let statementLines = [];\r\n    let foundQuestion = false;\r\n    let foundStatement = false;\r\n    let foundAnswerLine = false;\r\n    let currentSolution = \"\";\r\n    let index = 0;\r\n\r\n    const correctAnswers = splitCorrectAnswerDS(correctAnswersText);\r\n\r\n    for (let line of lines) {\r\n        // Nhận diện câu hỏi mới\r\n        if (/^([Cc]âu)\\s*\\d+[\\.:]/.test(line)) {\r\n            if (foundQuestion) {\r\n                if (!correctAnswers[index]) {\r\n                    dispatch(setErrorMessage(\"Số lượng đáp án không khớp với số lượng câu hỏi!\"));\r\n                    return false;\r\n                }\r\n                if (statementLines.length !== correctAnswers[index].length) {\r\n                    dispatch(setErrorMessage(`Số lượng đáp án không khớp với số lượng mệnh đề ở Câu ${index + 1}!`));\r\n                    return false;\r\n                }\r\n\r\n                questionsDS.push({\r\n                    questionData: {\r\n                        typeOfQuestion: \"DS\",\r\n                        content: questionContent.trim(),\r\n                        solution: currentSolution.trim(),\r\n                        class: null,\r\n                        chapter: null,\r\n                        difficulty: null,\r\n                        imageUrl: null,\r\n                    },\r\n                    statements: statementLines,\r\n                });\r\n                index++;\r\n            }\r\n\r\n            foundQuestion = true;\r\n            questionContent = line.replace(/^([Cc]âu)\\s*\\d+[\\.:]\\s*/, \"\");\r\n            statementLines = [];\r\n            currentSolution = \"\";\r\n            foundStatement = false;\r\n            foundAnswerLine = false;\r\n        }\r\n\r\n        // Dòng mệnh đề\r\n        else if (/^[a-dA-D][\\.\\)]/.test(line)) {\r\n            const match = line.match(/^([a-dA-D])[\\.\\)]\\s*(.*)$/);\r\n            if (match) {\r\n                const [, , text] = match;\r\n                foundStatement = true;\r\n                const currentAnswer = correctAnswers[index]?.[statementLines.length];\r\n\r\n                statementLines.push({\r\n                    imageUrl: null,\r\n                    order: statementLines.length,\r\n                    content: text.trim(),\r\n                    isCorrect: currentAnswer === \"Đ\" || currentAnswer === \"D\",\r\n                });\r\n            }\r\n        }\r\n\r\n        // Dòng đáp án hoặc lời giải\r\n        else if (ANSWER_OR_SOLUTION_REGEX.test(line)) {\r\n            foundAnswerLine = true;\r\n            currentSolution += line + \"\\n\"; // ✅ giữ lại dòng này\r\n        }\r\n\r\n        // Lời giải sau dòng đáp án\r\n        else if (foundAnswerLine) {\r\n            currentSolution += line + \" \";\r\n        }\r\n\r\n        // Nội dung bổ sung cho mệnh đề\r\n        else if (foundStatement) {\r\n            statementLines[statementLines.length - 1].content += \" \" + line;\r\n        }\r\n\r\n        // Nội dung phụ của câu hỏi\r\n        else {\r\n            questionContent += \" \" + line;\r\n        }\r\n    }\r\n\r\n    // Xử lý câu cuối cùng\r\n    if (foundQuestion) {\r\n        if (!correctAnswers[index]) {\r\n            dispatch(setErrorMessage(\"Số lượng đáp án không khớp với số lượng câu hỏi!\"));\r\n            return [];\r\n        }\r\n        if (statementLines.length !== correctAnswers[index].length) {\r\n            dispatch(setErrorMessage(`Số lượng đáp án không khớp với số lượng mệnh đề ở Câu ${index + 1}!`));\r\n            return [];\r\n        }\r\n\r\n        questionsDS.push({\r\n            questionData: {\r\n                typeOfQuestion: \"DS\",\r\n                content: questionContent.trim(),\r\n                solution: currentSolution.trim(),\r\n            },\r\n            statements: statementLines,\r\n        });\r\n    }\r\n\r\n    return questionsDS\r\n};\r\n\r\n\r\nexport const splitCorrectAnswerTLN = (correctAnswersText) => {\r\n    if (correctAnswersText.trim() === \"\") return [];\r\n    // Chuyển đổi chuỗi đáp án thành mảng các đáp án\r\n    correctAnswersText = normalizeText(correctAnswersText);\r\n    const correctAnswers = correctAnswersText\r\n        .trim()\r\n        .replace(/,/g, \".\")\r\n        .split(/\\s+/)      // tách theo 1 hoặc nhiều khoảng trắng\r\n        .filter(Boolean);  // loại bỏ chuỗi rỗng (nếu có)\r\n    // console.log(correctAnswers)\r\n    return correctAnswers;\r\n}\r\n\r\nexport const splitContentTLN = (content, correctAnswersText, dispatch) => {\r\n    if (content.trim() === \"\" || correctAnswersText.trim() === \"\") return [];\r\n\r\n    content = normalizeText(content);\r\n    const lines = content\r\n        .split(\"\\n\")\r\n        .map((line) => line.trim())\r\n        .filter((line) => line !== \"\");\r\n\r\n    const ANSWER_OR_SOLUTION_REGEX = /^(đáp án|dap an|lời giải|loi giai)\\s*[:：]/i;\r\n\r\n    let questionsTLN = [];\r\n    let questionContent = \"\";\r\n    let currentSolution = \"\";\r\n    let foundQuestion = false;\r\n    let foundAnswerLine = false;\r\n    let index = 0;\r\n\r\n    const correctAnswers = splitCorrectAnswerTLN(correctAnswersText);\r\n\r\n    for (let line of lines) {\r\n        // Dòng bắt đầu câu hỏi mới\r\n        if (/^([Cc]âu)\\s*\\d+[\\.:]/.test(line)) {\r\n            if (foundQuestion) {\r\n                if (index >= correctAnswers.length) {\r\n                    dispatch(setErrorMessage(\"Số lượng đáp án không khớp với số lượng câu hỏi!\"));\r\n                    return false;\r\n                }\r\n\r\n                questionsTLN.push({\r\n                    questionData: {\r\n                        typeOfQuestion: \"TLN\",\r\n                        content: questionContent.trim(),\r\n                        correctAnswer: correctAnswers[index],\r\n                        solution: currentSolution.trim(),\r\n                        class: null,\r\n                        chapter: null,\r\n                        difficulty: null,\r\n                        imageUrl: null,\r\n                    },\r\n                });\r\n\r\n                index++;\r\n            }\r\n\r\n            foundQuestion = true;\r\n            questionContent = line.replace(/^([Cc]âu)\\s*\\d+[\\.:]\\s*/, \"\");\r\n            currentSolution = \"\";\r\n            foundAnswerLine = false;\r\n        }\r\n\r\n        // Dòng \"Đáp án\"/\"Lời giải\"\r\n        else if (ANSWER_OR_SOLUTION_REGEX.test(line)) {\r\n            foundAnswerLine = true;\r\n            currentSolution += line + \"\\n\"; // ✅ giữ lại dòng này\r\n        }\r\n\r\n        // Nội dung lời giải (sau dòng Đáp án)\r\n        else if (foundAnswerLine) {\r\n            currentSolution += line + \" \";\r\n        }\r\n\r\n        // Nội dung bổ sung cho câu hỏi\r\n        else {\r\n            questionContent += \" \" + line;\r\n        }\r\n    }\r\n\r\n    // Câu cuối cùng\r\n    if (foundQuestion) {\r\n        if (index >= correctAnswers.length) {\r\n            dispatch(setErrorMessage(\"Số lượng đáp án không khớp với số lượng câu hỏi!\"));\r\n            return [];\r\n        }\r\n\r\n        questionsTLN.push({\r\n            questionData: {\r\n                typeOfQuestion: \"TLN\",\r\n                content: questionContent.trim(),\r\n                correctAnswer: correctAnswers[index],\r\n                solution: currentSolution.trim(),\r\n            },\r\n        });\r\n    }\r\n\r\n    return questionsTLN;\r\n};\r\n\r\n\r\n\r\n\r\nexport const validateExamData = (examData, dispatch) => {\r\n    if (examData.name.trim() === \"\") {\r\n        dispatch(setErrorMessage(\"Tên đề thi không được để trống!\"));\r\n        return false;\r\n    }\r\n    if (examData.class === null) {\r\n        dispatch(setErrorMessage(\"Lớp không được để trống!\"));\r\n        return false;\r\n    }\r\n    if (examData.typeOfExam === null) {\r\n        dispatch(setErrorMessage(\"Kiểu đề thi không được để trống!\"));\r\n        return false;\r\n    }\r\n\r\n    if (examData.year === null) {\r\n        dispatch(setErrorMessage(\"Năm không được để trống!\"));\r\n        return false;\r\n    }\r\n\r\n    if (examData.passRate < 0 || examData.passRate > 100) {\r\n        dispatch(setErrorMessage(\"Tỷ lệ đạt không hợp lệ!\"));\r\n        return false;\r\n    }\r\n\r\n    return true;\r\n}"], "mappings": "AAAA;AACA,SAASA,eAAe,QAAQ,oCAAoC;;AAEpE;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EACjF,IAAIA,OAAO,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACvBF,QAAQ,CAACJ,eAAe,CAAC,uCAAuC,CAAC,CAAC;IAClE,OAAO,KAAK;EAChB;EACA,IAAIE,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAE;IAClCH,QAAQ,CAACJ,eAAe,CAAC,mCAAmC,CAAC,CAAC;IAC9D,OAAO,KAAK;EAChB;EACA,IAAIG,aAAa,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAC7BF,QAAQ,CAACJ,eAAe,CAAC,6BAA6B,CAAC,CAAC;IACxD,OAAO,KAAK;EAEhB,CAAC,MAAM;IACH,IAAIE,QAAQ,CAACK,cAAc,KAAK,KAAK,IAAI,CAAC,oBAAoB,CAACC,IAAI,CAACL,aAAa,CAACM,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE;MAClGL,QAAQ,CAACJ,eAAe,CAAC,wBAAwB,CAAC,CAAC;MACnD,OAAO,KAAK;IAChB;EACJ;EAEA,IAAIE,QAAQ,CAACQ,KAAK,KAAK,IAAI,EAAE;IACzBN,QAAQ,CAACJ,eAAe,CAAC,0BAA0B,CAAC,CAAC;IACrD,OAAO,KAAK;EAChB;EAEA,IAAIE,QAAQ,CAACQ,KAAK,KAAK,IAAI,IAAIR,QAAQ,CAACS,OAAO,KAAK,IAAI,EAAE;IACtD,IAAI,CAACT,QAAQ,CAACS,OAAO,CAACC,UAAU,CAACV,QAAQ,CAACQ,KAAK,CAAC,EAAE;MAC9CN,QAAQ,CAACJ,eAAe,CAAC,0CAA0C,CAAC,CAAC;MACrE,OAAO,KAAK;IAChB;EACJ;EAEA,IAAIE,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAE;IAClC,IAAI,CAAC,SAAS,CAACC,IAAI,CAACL,aAAa,CAACG,IAAI,CAAC,CAAC,CAAC,EAAE;MACvCF,QAAQ,CAACJ,eAAe,CAAC,8DAA8D,CAAC,CAAC;MACzF,OAAO,KAAK;IAChB;EACJ,CAAC,MAAM,IAAIE,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAE;IACzC,MAAMM,MAAM,GAAGV,aAAa,CAACG,IAAI,CAAC,CAAC,CAACQ,KAAK,CAAC,KAAK,CAAC;IAChD,IAAID,MAAM,CAACE,MAAM,KAAK,CAAC,IAAI,CAACF,MAAM,CAACG,KAAK,CAAEC,KAAK,IAAKA,KAAK,KAAK,GAAG,IAAIA,KAAK,KAAK,GAAG,CAAC,EAAE;MACjFb,QAAQ,CAACJ,eAAe,CAAC,8EAA8E,CAAC,CAAC;MACzG,OAAO,KAAK;IAChB;EACJ;EAGA,OAAO,IAAI;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkB,YAAY,GAAGA,CAAChB,QAAQ,EAAEC,aAAa,EAAEE,OAAO,EAAED,QAAQ,KAAK;EACxE;EACA,MAAMe,KAAK,GAAGd,OAAO,CAChBS,KAAK,CAAC,IAAI,CAAC,CACXM,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,CAC1BgB,MAAM,CAAED,IAAI,IAAKA,IAAI,KAAK,EAAE,CAAC;EAElC,IAAIE,eAAe,GAAG,EAAE;EACxB,MAAMC,cAAc,GAAG,EAAE;EACzB,IAAIC,WAAW,GAAG,KAAK;EAEvB,IAAIvB,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAE;IAClC;IACA,KAAK,IAAIc,IAAI,IAAIF,KAAK,EAAE;MACpB,IAAI,UAAU,CAACX,IAAI,CAACa,IAAI,CAAC,EAAE;QACvBI,WAAW,GAAG,IAAI;QAClBD,cAAc,CAACE,IAAI,CAACL,IAAI,CAACM,KAAK,CAAC,CAAC,CAAC,CAACrB,IAAI,CAAC,CAAC,CAAC;MAC7C,CAAC,MAAM,IAAI,CAACmB,WAAW,EAAE;QACrBF,eAAe,IAAIF,IAAI,GAAG,GAAG;MACjC,CAAC,MAAM;QACHG,cAAc,CAACA,cAAc,CAACT,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,GAAGM,IAAI;MAC3D;IACJ;EACJ,CAAC,MAAM,IAAInB,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAE;IACzC;IACA,KAAK,IAAIc,IAAI,IAAIF,KAAK,EAAE;MACpB,IAAI,UAAU,CAACX,IAAI,CAACa,IAAI,CAAC,EAAE;QACvBI,WAAW,GAAG,IAAI;QAClBD,cAAc,CAACE,IAAI,CAACL,IAAI,CAACM,KAAK,CAAC,CAAC,CAAC,CAACrB,IAAI,CAAC,CAAC,CAAC;MAC7C,CAAC,MAAM,IAAI,CAACmB,WAAW,EAAE;QACrBF,eAAe,IAAIF,IAAI,GAAG,GAAG;MACjC,CAAC,MAAM;QACHG,cAAc,CAACA,cAAc,CAACT,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,GAAGM,IAAI;MAC3D;IACJ;EACJ,CAAC,MAAM,IAAInB,QAAQ,CAACK,cAAc,KAAK,KAAK,EAAE;IAC1CgB,eAAe,GAAGlB,OAAO;EAC7B;;EAEA;EACAkB,eAAe,GAAGA,eAAe,CAACjB,IAAI,CAAC,CAAC,CAACG,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;EAEvE,IAAIP,QAAQ,CAACK,cAAc,KAAK,IAAI,IAAIL,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAE;IACtE,IAAIiB,cAAc,CAACT,MAAM,GAAG,CAAC,IAAIS,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKI,SAAS,EAAE;MACtFxB,QAAQ,CAACJ,eAAe,CAAC,sBAAsB,CAAC,CAAC;MACjD,OAAO,KAAK;IAChB;EACJ;;EAEA;EACA,MAAM6B,aAAa,GAAGL,cAAc,CAACJ,GAAG,CAAEC,IAAI,KAAM;IAChDhB,OAAO,EAAEgB,IAAI;IACbS,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC;;EAEH;EACA,IAAI9B,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAE;IAClC,MAAM0B,aAAa,GAAG9B,aAAa,CAACG,IAAI,CAAC,CAAC,CAACQ,KAAK,CAAC,KAAK,CAAC;IACvDmB,aAAa,CAACC,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;MACrCP,aAAa,CAACO,KAAK,CAAC,CAACN,SAAS,GAAGK,MAAM,KAAK,GAAG;IACnD,CAAC,CAAC;EACN,CAAC,MAAM,IAAIjC,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAE;IACzC,MAAM8B,MAAM,GAAGlC,aAAa,CAACG,IAAI,CAAC,CAAC;IACnC,MAAMgC,YAAY,GAAGD,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;IAC7DV,aAAa,CAACK,OAAO,CAAC,CAACM,SAAS,EAAEJ,KAAK,KAAK;MACxCI,SAAS,CAACV,SAAS,GAAGM,KAAK,KAAKE,YAAY;IAChD,CAAC,CAAC;EACN;EAEA,OAAO;IAAEf,eAAe;IAAEM;EAAc,CAAC;AAC7C,CAAC;AAGD,OAAO,MAAMY,aAAa,GAAGA,CAACvC,QAAQ,EAAEE,QAAQ,KAAK;EACjD,IAAIF,QAAQ,CAACQ,KAAK,KAAK,IAAI,EAAE;IACzBN,QAAQ,CAACJ,eAAe,CAAC,0BAA0B,CAAC,CAAC;IACrD,OAAO,KAAK;EAChB;EACA,IAAIE,QAAQ,CAACC,aAAa,EAAE;IACxB,IAAI,CAAC,oBAAoB,CAACK,IAAI,CAACN,QAAQ,CAACC,aAAa,CAAC,EAAE;MACpDC,QAAQ,CAACJ,eAAe,CAAC,wBAAwB,CAAC,CAAC;MACnD,OAAO,KAAK;IAChB;EACJ;EAEA,IAAIE,QAAQ,CAACQ,KAAK,KAAK,IAAI,IAAIR,QAAQ,CAACS,OAAO,KAAK,IAAI,EAAE;IACtD,IAAI,CAACT,QAAQ,CAACS,OAAO,CAACC,UAAU,CAACV,QAAQ,CAACQ,KAAK,CAAC,EAAE;MAC9CN,QAAQ,CAACJ,eAAe,CAAC,0CAA0C,CAAC,CAAC;MACrE,OAAO,KAAK;IAChB;EACJ;EAEA,IAAIE,QAAQ,CAACK,cAAc,KAAK,IAAI,EAAE;IAClC,IAAIL,QAAQ,CAACwC,UAAU,CAACpB,MAAM,CAACkB,SAAS,IAAIA,SAAS,CAACV,SAAS,KAAK,IAAI,CAAC,CAACf,MAAM,KAAK,CAAC,EAAE;MACpFX,QAAQ,CAACJ,eAAe,CAAC,gCAAgC,CAAC,CAAC;MAC3D,OAAO,KAAK;IAChB;EACJ;EAEA,OAAO,IAAI;AACf,CAAC;AAED,OAAO,MAAM2C,qBAAqB,GAAIzC,QAAQ,IAAK;EAAA,IAAA0C,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC/C,MAAMC,WAAW,GAAG;IAChB,GAAGjD,QAAQ;IACXwC,UAAU,GAAAE,oBAAA,GAAE1C,QAAQ,CAACwC,UAAU,cAAAE,oBAAA,uBAAnBA,oBAAA,CAAqBxB,GAAG,CAACoB,SAAS,KAAK;MAAE,GAAGA;IAAU,CAAC,CAAC;EACxE,CAAC;EAEDW,WAAW,CAAC9C,OAAO,IAAAwC,oBAAA,GAAGM,WAAW,CAAC9C,OAAO,cAAAwC,oBAAA,uBAAnBA,oBAAA,CAAqBvC,IAAI,CAAC,CAAC,CAACG,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;EAChF0C,WAAW,CAAChD,aAAa,IAAA2C,qBAAA,GAAGK,WAAW,CAAChD,aAAa,cAAA2C,qBAAA,uBAAzBA,qBAAA,CAA2BxC,IAAI,CAAC,CAAC,CAACG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC/E0C,WAAW,CAACC,QAAQ,IAAAL,qBAAA,GAAGI,WAAW,CAACC,QAAQ,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBzC,IAAI,CAAC,CAAC;EACnD6C,WAAW,CAACE,WAAW,IAAAL,sBAAA,GAAGG,WAAW,CAACE,WAAW,cAAAL,sBAAA,uBAAvBA,sBAAA,CAAyB1C,IAAI,CAAC,CAAC;EACzD6C,WAAW,CAACG,WAAW,IAAAL,qBAAA,GAAGE,WAAW,CAACG,WAAW,cAAAL,qBAAA,uBAAvBA,qBAAA,CAAyB3C,IAAI,CAAC,CAAC;EAEzD,CAAA4C,qBAAA,GAAAC,WAAW,CAACT,UAAU,cAAAQ,qBAAA,uBAAtBA,qBAAA,CAAwBhB,OAAO,CAAEM,SAAS,IAAK;IAAA,IAAAe,kBAAA;IAC3Cf,SAAS,CAACnC,OAAO,IAAAkD,kBAAA,GAAGf,SAAS,CAACnC,OAAO,cAAAkD,kBAAA,uBAAjBA,kBAAA,CAAmBjD,IAAI,CAAC,CAAC;EACjD,CAAC,CAAC;EAEF,OAAO6C,WAAW;AACtB,CAAC;AAED,OAAO,MAAMK,aAAa,GAAIC,IAAI,IAAK;EACnC,OAAOA,IAAI,CACNC,SAAS,CAAC,KAAK,CAAC,CAChBjD,OAAO,CAAC,WAAW,EAAGkD,CAAC,IAAKC,MAAM,CAACC,YAAY,CAACF,CAAC,CAACpB,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CACvE9B,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,CACrCA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBA,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC,CACzCA,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;EAAA,CACpCA,OAAO,CAAC,8CAA8C,EAAE,EAAE,CAAC,CAC3DA,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CACjCA,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC,CAC3CA,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAoB;EAAA,CACrDA,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC,CAAS;EAAA,CACrDA,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAmB;EAAA,CACrDA,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAc;EAAA,CACrDH,IAAI,CAAC,CAAC;AACf,CAAC;AAGD,OAAO,MAAMwD,oBAAoB,GAAIC,kBAAkB,IAAK;EACxD,IAAIA,kBAAkB,CAACzD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE;EAC/C;EACAyD,kBAAkB,GAAGP,aAAa,CAACO,kBAAkB,CAAC;EAEtD,MAAMC,cAAc,GAAGD,kBAAkB,CACpCzD,IAAI,CAAC,CAAC,CACNQ,KAAK,CAAC,KAAK,CAAC,CAAQ;EAAA,CACpBQ,MAAM,CAAC2C,OAAO,CAAC,CAAK;EAAA,CACpB7C,GAAG,CAAC8C,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAE;;EAErC,OAAOH,cAAc;AACzB,CAAC;AAGD,OAAO,MAAMI,cAAc,GAAGA,CAAC/D,OAAO,EAAE0D,kBAAkB,EAAE3D,QAAQ,KAAK;EACrE,IAAIC,OAAO,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIyD,kBAAkB,CAACzD,IAAI,CAAC,CAAC,KAAK,EAAE,EACzD,OAAO,EAAE;EAEbD,OAAO,GAAGmD,aAAa,CAACnD,OAAO,CAAC;EAChC,MAAMc,KAAK,GAAGd,OAAO,CAChBS,KAAK,CAAC,IAAI,CAAC,CACXM,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,CAC1BgB,MAAM,CAAED,IAAI,IAAKA,IAAI,KAAK,EAAE,CAAC;EAElC,IAAIgD,WAAW,GAAG,EAAE;EACpB,IAAI9C,eAAe,GAAG,EAAE;EACxB,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAI8C,aAAa,GAAG,KAAK;EACzB,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,eAAe,GAAG,KAAK;EAC3B,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIrC,KAAK,GAAG,CAAC;EACb,IAAIsC,OAAO,GAAG,CAAC;EAEf,MAAMV,cAAc,GAAGF,oBAAoB,CAACC,kBAAkB,CAAC;EAE/D,KAAK,IAAI1C,IAAI,IAAIF,KAAK,EAAE;IACpB;IACA,IAAI,sBAAsB,CAACX,IAAI,CAACa,IAAI,CAAC,EAAE;MACnC,IAAIiD,aAAa,EAAE;QACf;QACAD,WAAW,CAAC3C,IAAI,CAAC;UACbiD,YAAY,EAAE;YACVpE,cAAc,EAAE,IAAI;YACpBF,OAAO,EAAEkB,eAAe,CAACjB,IAAI,CAAC,CAAC;YAC/B8C,QAAQ,EAAEqB,eAAe,CAACnE,IAAI,CAAC,CAAC;YAAE;YAClCI,KAAK,EAAE,IAAI;YACXC,OAAO,EAAE,IAAI;YACbqB,UAAU,EAAE,IAAI;YAChB4C,QAAQ,EAAE;UACd,CAAC;UACDlC,UAAU,EAAElB;QAChB,CAAC,CAAC;QACFY,KAAK,EAAE;MACX;MAEAkC,aAAa,GAAG,IAAI;MACpB/C,eAAe,GAAGF,IAAI,CAACZ,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC;MAC7De,cAAc,GAAG,EAAE;MACnBiD,eAAe,GAAG,EAAE;MACpBF,cAAc,GAAG,KAAK;MACtBC,eAAe,GAAG,KAAK;IAC3B;;IAEA;IAAA,KACK,IAAI,iBAAiB,CAAChE,IAAI,CAACa,IAAI,CAAC,EAAE;MACnC,MAAMwD,KAAK,GAAGxD,IAAI,CAACwD,KAAK,CAAC,2BAA2B,CAAC;MACrD,IAAIA,KAAK,EAAE;QACP,MAAM,GAAGC,MAAM,EAAErB,IAAI,CAAC,GAAGoB,KAAK;QAC9BN,cAAc,GAAG,IAAI;QACrB/C,cAAc,CAACE,IAAI,CAAC;UAChBqD,KAAK,EAAEL,OAAO;UACdE,QAAQ,EAAE,IAAI;UACdvE,OAAO,EAAEoD,IAAI,CAACnD,IAAI,CAAC,CAAC;UACpBwB,SAAS,EACLkC,cAAc,CAAC5B,KAAK,CAAC,IACrB4B,cAAc,CAAC5B,KAAK,CAAC,CAAC+B,WAAW,CAAC,CAAC,KAAKW,MAAM,CAACX,WAAW,CAAC;QACnE,CAAC,CAAC;QACFO,OAAO,EAAE;MACb;IACJ;;IAEA;IAAA,KACK,IAAI,4CAA4C,CAAClE,IAAI,CAACa,IAAI,CAAC,EAAE;MAC9DmD,eAAe,GAAG,IAAI;MACtBC,eAAe,IAAIpD,IAAI,GAAG,IAAI,CAAC,CAAC;IACpC;;IAEA;IAAA,KACK,IAAImD,eAAe,EAAE;MACtBC,eAAe,IAAIpD,IAAI,GAAG,IAAI;IAClC;;IAEA;IAAA,KACK,IAAIkD,cAAc,EAAE;MACrB/C,cAAc,CAACA,cAAc,CAACT,MAAM,GAAG,CAAC,CAAC,CAACV,OAAO,IAAI,GAAG,GAAGgB,IAAI;IACnE;;IAEA;IAAA,KACK;MACDE,eAAe,IAAI,GAAG,GAAGF,IAAI;IACjC;EACJ;;EAEA;EACA,IAAIiD,aAAa,EAAE;IACfD,WAAW,CAAC3C,IAAI,CAAC;MACbiD,YAAY,EAAE;QACVpE,cAAc,EAAE,IAAI;QACpBF,OAAO,EAAEkB,eAAe,CAACjB,IAAI,CAAC,CAAC;QAC/B8C,QAAQ,EAAEqB,eAAe,CAACnE,IAAI,CAAC,CAAC,CAAE;MACtC,CAAC;MACDoC,UAAU,EAAElB;IAChB,CAAC,CAAC;EACN;EAEA,IAAI6C,WAAW,CAACtD,MAAM,KAAKiD,cAAc,CAACjD,MAAM,EAAE;IAC9CX,QAAQ,CACJJ,eAAe,mHAAAgF,MAAA,CACyCX,WAAW,CAACtD,MAAM,SAAAiE,MAAA,CAAMhB,cAAc,CAACjD,MAAM,CACrG,CACJ,CAAC;IACD,OAAO,EAAE;EACb;EAEA,OAAOsD,WAAW;AACtB,CAAC;AAGD,OAAO,MAAMY,oBAAoB,GAAIlB,kBAAkB,IAAK;EACxD,IAAIA,kBAAkB,CAACzD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE;EAC/C;EACAyD,kBAAkB,GAAGP,aAAa,CAACO,kBAAkB,CAAC;EAEtD,MAAMC,cAAc,GAAGD,kBAAkB,CACpCzD,IAAI,CAAC,CAAC,CACNG,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CACjB0D,WAAW,CAAC,CAAC,CACbrD,KAAK,CAAC,KAAK,CAAC,CACZM,GAAG,CAAC8D,KAAK,IAAIA,KAAK,CAACpE,KAAK,CAAC,EAAE,CAAC,CAAC;EAElC,OAAOkD,cAAc;AACzB,CAAC;AAGD,OAAO,MAAMmB,cAAc,GAAGA,CAAC9E,OAAO,EAAE0D,kBAAkB,EAAE3D,QAAQ,KAAK;EACrE,IAAIC,OAAO,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIyD,kBAAkB,CAACzD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE;EAExED,OAAO,GAAGmD,aAAa,CAACnD,OAAO,CAAC;EAEhC,MAAMc,KAAK,GAAGd,OAAO,CAChBS,KAAK,CAAC,IAAI,CAAC,CACXM,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,CAC1BgB,MAAM,CAAED,IAAI,IAAKA,IAAI,KAAK,EAAE,CAAC;EAElC,MAAM+D,wBAAwB,GAAG,4CAA4C;EAE7E,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAI9D,eAAe,GAAG,EAAE;EACxB,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAI8C,aAAa,GAAG,KAAK;EACzB,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,eAAe,GAAG,KAAK;EAC3B,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIrC,KAAK,GAAG,CAAC;EAEb,MAAM4B,cAAc,GAAGiB,oBAAoB,CAAClB,kBAAkB,CAAC;EAE/D,KAAK,IAAI1C,IAAI,IAAIF,KAAK,EAAE;IACpB;IACA,IAAI,sBAAsB,CAACX,IAAI,CAACa,IAAI,CAAC,EAAE;MACnC,IAAIiD,aAAa,EAAE;QACf,IAAI,CAACN,cAAc,CAAC5B,KAAK,CAAC,EAAE;UACxBhC,QAAQ,CAACJ,eAAe,CAAC,kDAAkD,CAAC,CAAC;UAC7E,OAAO,KAAK;QAChB;QACA,IAAIwB,cAAc,CAACT,MAAM,KAAKiD,cAAc,CAAC5B,KAAK,CAAC,CAACrB,MAAM,EAAE;UACxDX,QAAQ,CAACJ,eAAe,uIAAAgF,MAAA,CAA0D5C,KAAK,GAAG,CAAC,MAAG,CAAC,CAAC;UAChG,OAAO,KAAK;QAChB;QAEAiD,WAAW,CAAC3D,IAAI,CAAC;UACbiD,YAAY,EAAE;YACVpE,cAAc,EAAE,IAAI;YACpBF,OAAO,EAAEkB,eAAe,CAACjB,IAAI,CAAC,CAAC;YAC/B8C,QAAQ,EAAEqB,eAAe,CAACnE,IAAI,CAAC,CAAC;YAChCI,KAAK,EAAE,IAAI;YACXC,OAAO,EAAE,IAAI;YACbqB,UAAU,EAAE,IAAI;YAChB4C,QAAQ,EAAE;UACd,CAAC;UACDlC,UAAU,EAAElB;QAChB,CAAC,CAAC;QACFY,KAAK,EAAE;MACX;MAEAkC,aAAa,GAAG,IAAI;MACpB/C,eAAe,GAAGF,IAAI,CAACZ,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC;MAC7De,cAAc,GAAG,EAAE;MACnBiD,eAAe,GAAG,EAAE;MACpBF,cAAc,GAAG,KAAK;MACtBC,eAAe,GAAG,KAAK;IAC3B;;IAEA;IAAA,KACK,IAAI,iBAAiB,CAAChE,IAAI,CAACa,IAAI,CAAC,EAAE;MACnC,MAAMwD,KAAK,GAAGxD,IAAI,CAACwD,KAAK,CAAC,2BAA2B,CAAC;MACrD,IAAIA,KAAK,EAAE;QAAA,IAAAS,qBAAA;QACP,MAAM,IAAK7B,IAAI,CAAC,GAAGoB,KAAK;QACxBN,cAAc,GAAG,IAAI;QACrB,MAAMgB,aAAa,IAAAD,qBAAA,GAAGtB,cAAc,CAAC5B,KAAK,CAAC,cAAAkD,qBAAA,uBAArBA,qBAAA,CAAwB9D,cAAc,CAACT,MAAM,CAAC;QAEpES,cAAc,CAACE,IAAI,CAAC;UAChBkD,QAAQ,EAAE,IAAI;UACdG,KAAK,EAAEvD,cAAc,CAACT,MAAM;UAC5BV,OAAO,EAAEoD,IAAI,CAACnD,IAAI,CAAC,CAAC;UACpBwB,SAAS,EAAEyD,aAAa,KAAK,GAAG,IAAIA,aAAa,KAAK;QAC1D,CAAC,CAAC;MACN;IACJ;;IAEA;IAAA,KACK,IAAIH,wBAAwB,CAAC5E,IAAI,CAACa,IAAI,CAAC,EAAE;MAC1CmD,eAAe,GAAG,IAAI;MACtBC,eAAe,IAAIpD,IAAI,GAAG,IAAI,CAAC,CAAC;IACpC;;IAEA;IAAA,KACK,IAAImD,eAAe,EAAE;MACtBC,eAAe,IAAIpD,IAAI,GAAG,GAAG;IACjC;;IAEA;IAAA,KACK,IAAIkD,cAAc,EAAE;MACrB/C,cAAc,CAACA,cAAc,CAACT,MAAM,GAAG,CAAC,CAAC,CAACV,OAAO,IAAI,GAAG,GAAGgB,IAAI;IACnE;;IAEA;IAAA,KACK;MACDE,eAAe,IAAI,GAAG,GAAGF,IAAI;IACjC;EACJ;;EAEA;EACA,IAAIiD,aAAa,EAAE;IACf,IAAI,CAACN,cAAc,CAAC5B,KAAK,CAAC,EAAE;MACxBhC,QAAQ,CAACJ,eAAe,CAAC,kDAAkD,CAAC,CAAC;MAC7E,OAAO,EAAE;IACb;IACA,IAAIwB,cAAc,CAACT,MAAM,KAAKiD,cAAc,CAAC5B,KAAK,CAAC,CAACrB,MAAM,EAAE;MACxDX,QAAQ,CAACJ,eAAe,uIAAAgF,MAAA,CAA0D5C,KAAK,GAAG,CAAC,MAAG,CAAC,CAAC;MAChG,OAAO,EAAE;IACb;IAEAiD,WAAW,CAAC3D,IAAI,CAAC;MACbiD,YAAY,EAAE;QACVpE,cAAc,EAAE,IAAI;QACpBF,OAAO,EAAEkB,eAAe,CAACjB,IAAI,CAAC,CAAC;QAC/B8C,QAAQ,EAAEqB,eAAe,CAACnE,IAAI,CAAC;MACnC,CAAC;MACDoC,UAAU,EAAElB;IAChB,CAAC,CAAC;EACN;EAEA,OAAO6D,WAAW;AACtB,CAAC;AAGD,OAAO,MAAMG,qBAAqB,GAAIzB,kBAAkB,IAAK;EACzD,IAAIA,kBAAkB,CAACzD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE;EAC/C;EACAyD,kBAAkB,GAAGP,aAAa,CAACO,kBAAkB,CAAC;EACtD,MAAMC,cAAc,GAAGD,kBAAkB,CACpCzD,IAAI,CAAC,CAAC,CACNG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBK,KAAK,CAAC,KAAK,CAAC,CAAM;EAAA,CAClBQ,MAAM,CAAC2C,OAAO,CAAC,CAAC,CAAE;EACvB;EACA,OAAOD,cAAc;AACzB,CAAC;AAED,OAAO,MAAMyB,eAAe,GAAGA,CAACpF,OAAO,EAAE0D,kBAAkB,EAAE3D,QAAQ,KAAK;EACtE,IAAIC,OAAO,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIyD,kBAAkB,CAACzD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE;EAExED,OAAO,GAAGmD,aAAa,CAACnD,OAAO,CAAC;EAChC,MAAMc,KAAK,GAAGd,OAAO,CAChBS,KAAK,CAAC,IAAI,CAAC,CACXM,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,CAC1BgB,MAAM,CAAED,IAAI,IAAKA,IAAI,KAAK,EAAE,CAAC;EAElC,MAAM+D,wBAAwB,GAAG,4CAA4C;EAE7E,IAAIM,YAAY,GAAG,EAAE;EACrB,IAAInE,eAAe,GAAG,EAAE;EACxB,IAAIkD,eAAe,GAAG,EAAE;EACxB,IAAIH,aAAa,GAAG,KAAK;EACzB,IAAIE,eAAe,GAAG,KAAK;EAC3B,IAAIpC,KAAK,GAAG,CAAC;EAEb,MAAM4B,cAAc,GAAGwB,qBAAqB,CAACzB,kBAAkB,CAAC;EAEhE,KAAK,IAAI1C,IAAI,IAAIF,KAAK,EAAE;IACpB;IACA,IAAI,sBAAsB,CAACX,IAAI,CAACa,IAAI,CAAC,EAAE;MACnC,IAAIiD,aAAa,EAAE;QACf,IAAIlC,KAAK,IAAI4B,cAAc,CAACjD,MAAM,EAAE;UAChCX,QAAQ,CAACJ,eAAe,CAAC,kDAAkD,CAAC,CAAC;UAC7E,OAAO,KAAK;QAChB;QAEA0F,YAAY,CAAChE,IAAI,CAAC;UACdiD,YAAY,EAAE;YACVpE,cAAc,EAAE,KAAK;YACrBF,OAAO,EAAEkB,eAAe,CAACjB,IAAI,CAAC,CAAC;YAC/BH,aAAa,EAAE6D,cAAc,CAAC5B,KAAK,CAAC;YACpCgB,QAAQ,EAAEqB,eAAe,CAACnE,IAAI,CAAC,CAAC;YAChCI,KAAK,EAAE,IAAI;YACXC,OAAO,EAAE,IAAI;YACbqB,UAAU,EAAE,IAAI;YAChB4C,QAAQ,EAAE;UACd;QACJ,CAAC,CAAC;QAEFxC,KAAK,EAAE;MACX;MAEAkC,aAAa,GAAG,IAAI;MACpB/C,eAAe,GAAGF,IAAI,CAACZ,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC;MAC7DgE,eAAe,GAAG,EAAE;MACpBD,eAAe,GAAG,KAAK;IAC3B;;IAEA;IAAA,KACK,IAAIY,wBAAwB,CAAC5E,IAAI,CAACa,IAAI,CAAC,EAAE;MAC1CmD,eAAe,GAAG,IAAI;MACtBC,eAAe,IAAIpD,IAAI,GAAG,IAAI,CAAC,CAAC;IACpC;;IAEA;IAAA,KACK,IAAImD,eAAe,EAAE;MACtBC,eAAe,IAAIpD,IAAI,GAAG,GAAG;IACjC;;IAEA;IAAA,KACK;MACDE,eAAe,IAAI,GAAG,GAAGF,IAAI;IACjC;EACJ;;EAEA;EACA,IAAIiD,aAAa,EAAE;IACf,IAAIlC,KAAK,IAAI4B,cAAc,CAACjD,MAAM,EAAE;MAChCX,QAAQ,CAACJ,eAAe,CAAC,kDAAkD,CAAC,CAAC;MAC7E,OAAO,EAAE;IACb;IAEA0F,YAAY,CAAChE,IAAI,CAAC;MACdiD,YAAY,EAAE;QACVpE,cAAc,EAAE,KAAK;QACrBF,OAAO,EAAEkB,eAAe,CAACjB,IAAI,CAAC,CAAC;QAC/BH,aAAa,EAAE6D,cAAc,CAAC5B,KAAK,CAAC;QACpCgB,QAAQ,EAAEqB,eAAe,CAACnE,IAAI,CAAC;MACnC;IACJ,CAAC,CAAC;EACN;EAEA,OAAOoF,YAAY;AACvB,CAAC;AAKD,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,QAAQ,EAAExF,QAAQ,KAAK;EACpD,IAAIwF,QAAQ,CAACC,IAAI,CAACvF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAC7BF,QAAQ,CAACJ,eAAe,CAAC,iCAAiC,CAAC,CAAC;IAC5D,OAAO,KAAK;EAChB;EACA,IAAI4F,QAAQ,CAAClF,KAAK,KAAK,IAAI,EAAE;IACzBN,QAAQ,CAACJ,eAAe,CAAC,0BAA0B,CAAC,CAAC;IACrD,OAAO,KAAK;EAChB;EACA,IAAI4F,QAAQ,CAACE,UAAU,KAAK,IAAI,EAAE;IAC9B1F,QAAQ,CAACJ,eAAe,CAAC,kCAAkC,CAAC,CAAC;IAC7D,OAAO,KAAK;EAChB;EAEA,IAAI4F,QAAQ,CAACG,IAAI,KAAK,IAAI,EAAE;IACxB3F,QAAQ,CAACJ,eAAe,CAAC,0BAA0B,CAAC,CAAC;IACrD,OAAO,KAAK;EAChB;EAEA,IAAI4F,QAAQ,CAACI,QAAQ,GAAG,CAAC,IAAIJ,QAAQ,CAACI,QAAQ,GAAG,GAAG,EAAE;IAClD5F,QAAQ,CAACJ,eAAe,CAAC,yBAAyB,CAAC,CAAC;IACpD,OAAO,KAAK;EAChB;EAEA,OAAO,IAAI;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}