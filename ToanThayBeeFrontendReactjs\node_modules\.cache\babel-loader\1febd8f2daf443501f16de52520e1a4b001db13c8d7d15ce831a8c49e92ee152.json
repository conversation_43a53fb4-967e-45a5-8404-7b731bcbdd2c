{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\sidebar\\\\AdminSidebar.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { BeeMathLogo } from '../logo/BeeMathLogo';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { toggleDropdown, toggleExamDropdown, toggleCloseSidebar } from '../../features/sidebar/sidebarSlice';\nimport { logout } from '../../features/auth/authSlice';\nimport HeaderSidebar from './HeaderSidebar';\nimport UserSidebar from './UserSidebar';\nimport UserType from 'src/constants/UserType';\nimport { ChevronLeft, ChevronRight, Users, FileText, CreditCard, Newspaper, Globe, LogOut, School, Code, Trophy, ClipboardList, FileSearch, Bot } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MenuSidebar = _ref => {\n  _s();\n  let {\n    onClick,\n    route,\n    Icon,\n    text,\n    icon2,\n    role = []\n  } = _ref;\n  const location = useLocation();\n  const closeSidebar = useSelector(state => {\n    var _state$sidebar;\n    return (_state$sidebar = state.sidebar) === null || _state$sidebar === void 0 ? void 0 : _state$sidebar.closeSidebar;\n  }); // Fix lỗi undefined\n\n  const user = useSelector(state => state.auth.user);\n  if (role.length > 0 && !role.includes(user === null || user === void 0 ? void 0 : user.userType)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center px-3\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClick,\n      className: \"flex items-center rounded-lg\\n        \".concat(closeSidebar ? 'justify-center w-10 h-10' : 'justify-start gap-4 w-full p-2', \"\\n        \").concat(location.pathname.includes(route) ? 'bg-[#253f61] text-white' : 'bg-white text-[#253f61] hover:bg-[#f0f4fa] hover:text-[#253f61]'),\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        className: \"w-5 h-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), !closeSidebar && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between flex-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-medium font-bevietnam leading-none whitespace-nowrap flex-shrink-0\",\n          children: text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 25\n        }, this), icon2]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 9\n  }, this);\n};\n_s(MenuSidebar, \"nztBiLvnxF6t+MbQAmzYijn9huE=\", false, function () {\n  return [useLocation, useSelector, useSelector];\n});\n_c = MenuSidebar;\nconst AdminSidebar = () => {\n  _s2();\n  const dropdown = useSelector(state => state.sidebar.dropdownOpen);\n  const tuitionDropdown = useSelector(state => state.sidebar.tuitionDropdownOpen);\n  const closeSidebar = useSelector(state => state.sidebar.closeSidebar);\n  const examDropdown = useSelector(state => state.sidebar.examDropdownOpen);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    await dispatch(logout());\n    navigate('/login');\n  };\n  const icon5 = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 20 20\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M10.4916 10.8333L8.57496 12.7416C8.49685 12.8191 8.43486 12.9113 8.39255 13.0128C8.35024 13.1144 8.32846 13.2233 8.32846 13.3333C8.32846 13.4433 8.35024 13.5522 8.39255 13.6538C8.43486 13.7553 8.49685 13.8475 8.57496 13.925C8.65243 14.0031 8.7446 14.0651 8.84615 14.1074C8.9477 14.1497 9.05662 14.1715 9.16663 14.1715C9.27664 14.1715 9.38556 14.1497 9.48711 14.1074C9.58866 14.0651 9.68082 14.0031 9.75829 13.925L13.0916 10.5916C13.1675 10.5124 13.227 10.4189 13.2666 10.3166C13.35 10.1138 13.35 9.8862 13.2666 9.68331C13.227 9.58102 13.1675 9.48757 13.0916 9.40831L9.75829 6.07498C9.68059 5.99728 9.58835 5.93565 9.48683 5.8936C9.38532 5.85155 9.27651 5.8299 9.16663 5.8299C9.05674 5.8299 8.94794 5.85155 8.84642 5.8936C8.7449 5.93565 8.65266 5.99728 8.57496 6.07498C8.49726 6.15268 8.43563 6.24492 8.39358 6.34644C8.35153 6.44796 8.32988 6.55677 8.32988 6.66665C8.32988 6.77653 8.35153 6.88534 8.39358 6.98686C8.43563 7.08837 8.49726 7.18062 8.57496 7.25831L10.4916 9.16665H2.49996C2.27895 9.16665 2.06698 9.25445 1.9107 9.41073C1.75442 9.56701 1.66663 9.77897 1.66663 9.99998C1.66663 10.221 1.75442 10.433 1.9107 10.5892C2.06698 10.7455 2.27895 10.8333 2.49996 10.8333H10.4916ZM9.99996 1.66665C8.44254 1.65969 6.91433 2.08933 5.58868 2.90681C4.26304 3.72429 3.193 4.89691 2.49996 6.29165C2.4005 6.49056 2.38414 6.72083 2.45446 6.93181C2.52479 7.14279 2.67605 7.31719 2.87496 7.41665C3.07387 7.5161 3.30415 7.53247 3.51512 7.46214C3.7261 7.39182 3.9005 7.24056 3.99996 7.04165C4.52679 5.97775 5.32815 5.07383 6.32125 4.4233C7.31435 3.77278 8.46314 3.39925 9.64892 3.34131C10.8347 3.28337 12.0144 3.54313 13.0662 4.09374C14.118 4.64435 15.0037 5.46584 15.6317 6.47331C16.2598 7.48078 16.6074 8.63769 16.6386 9.82447C16.6699 11.0112 16.3837 12.1848 15.8096 13.224C15.2354 14.2631 14.3942 15.1301 13.3729 15.7353C12.3516 16.3406 11.1871 16.6621 9.99996 16.6666C8.75736 16.672 7.53842 16.327 6.48304 15.671C5.42765 15.0151 4.57859 14.0749 4.03329 12.9583C3.93384 12.7594 3.75944 12.6081 3.54846 12.5378C3.33748 12.4675 3.10721 12.4839 2.90829 12.5833C2.70938 12.6828 2.55812 12.8572 2.4878 13.0681C2.41747 13.2791 2.43384 13.5094 2.53329 13.7083C3.19398 15.0379 4.19789 16.1668 5.44119 16.9784C6.68448 17.7899 8.122 18.2545 9.60506 18.3241C11.0881 18.3938 12.5629 18.066 13.8767 17.3746C15.1906 16.6832 16.2959 15.6533 17.0784 14.3915C17.8608 13.1297 18.2919 11.6818 18.327 10.1975C18.3622 8.7132 18.0002 7.24647 17.2785 5.94901C16.5568 4.65154 15.5015 3.57045 14.2219 2.81757C12.9422 2.06469 11.4847 1.66735 9.99996 1.66665V1.66665Z\",\n        fill: \"#71839B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed left-0 flex flex-col min-h-screen justify-between bg-white \".concat(closeSidebar ? '' : 'w-[16rem]', \" p-[1.25rem] shadow-[0px_1px_8px_2px_rgba(20,20,20,0.08)] z-60\"),\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => dispatch(toggleCloseSidebar()),\n      className: \"absolute top-1/2 -right-4 transform -translate-y-1/2 bg-white border rounded-full p-1 shadow-md hover:bg-gray-100 transition\",\n      children: closeSidebar ? /*#__PURE__*/_jsxDEV(ChevronRight, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 33\n      }, this) : /*#__PURE__*/_jsxDEV(ChevronLeft, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 62\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-col w-full justify-start items-start gap-5 inline-flex\",\n      children: [/*#__PURE__*/_jsxDEV(HeaderSidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-3 w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-3 overflow-y-auto max-h-[calc(100vh-200px)]\",\n          children: [/*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/student-management'),\n            route: '/admin/student-management',\n            Icon: Users,\n            text: 'Học sinh',\n            role: [UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT, UserType.ASSISTANT]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/class-management'),\n            route: '/admin/class-management',\n            Icon: School,\n            text: 'Lớp học',\n            role: [UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT, UserType.ASSISTANT]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/question-management'),\n            route: '/admin/question-management',\n            Icon: ClipboardList,\n            text: 'Câu hỏi',\n            role: [UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/question-report-management'),\n            route: '/admin/question-report-management',\n            Icon: FileSearch,\n            text: 'Báo cáo câu hỏi',\n            role: [UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/exam-management'),\n            route: '/admin/exam-management',\n            Icon: FileText,\n            text: 'Đề thi',\n            role: [UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/AI/exam-management'),\n            route: '/admin/AI/exam-management',\n            Icon: Bot,\n            text: 'Đề thi tạo bởi AI',\n            role: [UserType.ADMIN]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/code-management'),\n            route: '/admin/code-management',\n            Icon: Code,\n            text: 'Code',\n            role: [UserType.ADMIN]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/achievement-management'),\n            route: '/admin/achievement-management',\n            Icon: Trophy,\n            text: 'Thành tích',\n            role: [UserType.ADMIN, UserType.TEACHER, UserType.MARKETING]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/tuition-payment'),\n            route: '/admin/tuition-payment',\n            Icon: CreditCard,\n            text: 'Học phí',\n            role: [UserType.ADMIN, UserType.TEACHER, UserType.HUMANRESOURCEMANAGEMENT]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/article-management'),\n            route: '/admin/article-management',\n            Icon: Newspaper,\n            text: 'Bài viết',\n            role: [UserType.ADMIN]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(MenuSidebar, {\n            onClick: () => navigate('/admin/homepage-management'),\n            route: '/admin/homepage-management',\n            Icon: Globe,\n            text: 'Trang chủ',\n            role: [UserType.ADMIN, UserType.MARKETING]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-col w-full justify-start items-start gap-3 inline-flex\",\n      children: [/*#__PURE__*/_jsxDEV(MenuSidebar, {\n        onClick: handleLogout,\n        route: '/login',\n        Icon: LogOut,\n        text: 'Đăng xuất'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(UserSidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 9\n  }, this);\n};\n_s2(AdminSidebar, \"jt2D7WXr4Sz/bQzup18+mfhPlNQ=\", false, function () {\n  return [useSelector, useSelector, useSelector, useSelector, useDispatch, useNavigate];\n});\n_c2 = AdminSidebar;\nexport default AdminSidebar;\nvar _c, _c2;\n$RefreshReg$(_c, \"MenuSidebar\");\n$RefreshReg$(_c2, \"AdminSidebar\");", "map": {"version": 3, "names": ["BeeMathLogo", "Link", "useLocation", "useNavigate", "useSelector", "useDispatch", "toggleDropdown", "toggleExamDropdown", "toggleCloseSidebar", "logout", "Header<PERSON><PERSON><PERSON>", "UserSidebar", "UserType", "ChevronLeft", "ChevronRight", "Users", "FileText", "CreditCard", "Newspaper", "Globe", "LogOut", "School", "Code", "Trophy", "ClipboardList", "FileSearch", "Bot", "jsxDEV", "_jsxDEV", "MenuSidebar", "_ref", "_s", "onClick", "route", "Icon", "text", "icon2", "role", "location", "closeSidebar", "state", "_state$sidebar", "sidebar", "user", "auth", "length", "includes", "userType", "className", "children", "concat", "pathname", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AdminSidebar", "_s2", "dropdown", "dropdownOpen", "tuitionDropdown", "tuitionDropdownOpen", "examDropdown", "examDropdownOpen", "dispatch", "navigate", "handleLogout", "icon5", "width", "height", "viewBox", "fill", "xmlns", "d", "size", "ADMIN", "TEACHER", "CLASSMANAGEMENT", "HUMANRESOURCEMANAGEMENT", "ASSISTANT", "MARKETING", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/sidebar/AdminSidebar.jsx"], "sourcesContent": ["import { <PERSON><PERSON>ath<PERSON>ogo } from '../logo/BeeMathLogo';\r\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { toggleDropdown, toggleExamDropdown, toggleCloseSidebar } from '../../features/sidebar/sidebarSlice';\r\nimport { logout } from '../../features/auth/authSlice';\r\nimport HeaderSidebar from './HeaderSidebar';\r\nimport UserSidebar from './UserSidebar';\r\nimport UserType from 'src/constants/UserType';\r\nimport { ChevronLeft, ChevronRight, Users, FileText, CreditCard, Newspaper, Globe, LogOut, School, Code, Trophy, ClipboardList, FileSearch, Bot } from \"lucide-react\";\r\n\r\nconst MenuSidebar = ({ onClick, route, Icon, text, icon2, role = [] }) => {\r\n    const location = useLocation();\r\n    const closeSidebar = useSelector(state => state.sidebar?.closeSidebar); // Fix lỗi undefined\r\n\r\n    const user = useSelector((state) => state.auth.user);\r\n    if (role.length > 0 && !role.includes(user?.userType)) {\r\n        return null;\r\n    }\r\n\r\n    return (\r\n        <div className=\"flex items-center justify-center px-3\"\r\n        >\r\n            <button\r\n                onClick={onClick}\r\n                className={`flex items-center rounded-lg\r\n        ${closeSidebar ? 'justify-center w-10 h-10' : 'justify-start gap-4 w-full p-2'}\r\n        ${location.pathname.includes(route)\r\n                        ? 'bg-[#253f61] text-white'\r\n                        : 'bg-white text-[#253f61] hover:bg-[#f0f4fa] hover:text-[#253f61]'}`}\r\n            >\r\n                <Icon className=\"w-5 h-5\" />\r\n                {!closeSidebar && (\r\n                    <div className=\"flex items-center justify-between flex-row\">\r\n                        <div className=\"text-sm font-medium font-bevietnam leading-none whitespace-nowrap flex-shrink-0\">\r\n                            {text}\r\n                        </div>\r\n                        {icon2}\r\n                    </div>\r\n\r\n                )}\r\n            </button>\r\n        </div>\r\n    );\r\n}\r\n\r\nconst AdminSidebar = () => {\r\n    const dropdown = useSelector(state => state.sidebar.dropdownOpen);\r\n    const tuitionDropdown = useSelector(state => state.sidebar.tuitionDropdownOpen);\r\n    const closeSidebar = useSelector(state => state.sidebar.closeSidebar);\r\n    const examDropdown = useSelector(state => state.sidebar.examDropdownOpen);\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n\r\n    const handleLogout = async () => {\r\n        await dispatch(logout());\r\n        navigate('/login');\r\n    };\r\n\r\n    const icon5 = (\r\n        <div data-svg-wrapper className=\"relative\">\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M10.4916 10.8333L8.57496 12.7416C8.49685 12.8191 8.43486 12.9113 8.39255 13.0128C8.35024 13.1144 8.32846 13.2233 8.32846 13.3333C8.32846 13.4433 8.35024 13.5522 8.39255 13.6538C8.43486 13.7553 8.49685 13.8475 8.57496 13.925C8.65243 14.0031 8.7446 14.0651 8.84615 14.1074C8.9477 14.1497 9.05662 14.1715 9.16663 14.1715C9.27664 14.1715 9.38556 14.1497 9.48711 14.1074C9.58866 14.0651 9.68082 14.0031 9.75829 13.925L13.0916 10.5916C13.1675 10.5124 13.227 10.4189 13.2666 10.3166C13.35 10.1138 13.35 9.8862 13.2666 9.68331C13.227 9.58102 13.1675 9.48757 13.0916 9.40831L9.75829 6.07498C9.68059 5.99728 9.58835 5.93565 9.48683 5.8936C9.38532 5.85155 9.27651 5.8299 9.16663 5.8299C9.05674 5.8299 8.94794 5.85155 8.84642 5.8936C8.7449 5.93565 8.65266 5.99728 8.57496 6.07498C8.49726 6.15268 8.43563 6.24492 8.39358 6.34644C8.35153 6.44796 8.32988 6.55677 8.32988 6.66665C8.32988 6.77653 8.35153 6.88534 8.39358 6.98686C8.43563 7.08837 8.49726 7.18062 8.57496 7.25831L10.4916 9.16665H2.49996C2.27895 9.16665 2.06698 9.25445 1.9107 9.41073C1.75442 9.56701 1.66663 9.77897 1.66663 9.99998C1.66663 10.221 1.75442 10.433 1.9107 10.5892C2.06698 10.7455 2.27895 10.8333 2.49996 10.8333H10.4916ZM9.99996 1.66665C8.44254 1.65969 6.91433 2.08933 5.58868 2.90681C4.26304 3.72429 3.193 4.89691 2.49996 6.29165C2.4005 6.49056 2.38414 6.72083 2.45446 6.93181C2.52479 7.14279 2.67605 7.31719 2.87496 7.41665C3.07387 7.5161 3.30415 7.53247 3.51512 7.46214C3.7261 7.39182 3.9005 7.24056 3.99996 7.04165C4.52679 5.97775 5.32815 5.07383 6.32125 4.4233C7.31435 3.77278 8.46314 3.39925 9.64892 3.34131C10.8347 3.28337 12.0144 3.54313 13.0662 4.09374C14.118 4.64435 15.0037 5.46584 15.6317 6.47331C16.2598 7.48078 16.6074 8.63769 16.6386 9.82447C16.6699 11.0112 16.3837 12.1848 15.8096 13.224C15.2354 14.2631 14.3942 15.1301 13.3729 15.7353C12.3516 16.3406 11.1871 16.6621 9.99996 16.6666C8.75736 16.672 7.53842 16.327 6.48304 15.671C5.42765 15.0151 4.57859 14.0749 4.03329 12.9583C3.93384 12.7594 3.75944 12.6081 3.54846 12.5378C3.33748 12.4675 3.10721 12.4839 2.90829 12.5833C2.70938 12.6828 2.55812 12.8572 2.4878 13.0681C2.41747 13.2791 2.43384 13.5094 2.53329 13.7083C3.19398 15.0379 4.19789 16.1668 5.44119 16.9784C6.68448 17.7899 8.122 18.2545 9.60506 18.3241C11.0881 18.3938 12.5629 18.066 13.8767 17.3746C15.1906 16.6832 16.2959 15.6533 17.0784 14.3915C17.8608 13.1297 18.2919 11.6818 18.327 10.1975C18.3622 8.7132 18.0002 7.24647 17.2785 5.94901C16.5568 4.65154 15.5015 3.57045 14.2219 2.81757C12.9422 2.06469 11.4847 1.66735 9.99996 1.66665V1.66665Z\" fill=\"#71839B\" />\r\n            </svg>\r\n        </div>\r\n    )\r\n\r\n    return (\r\n        <div className={`fixed left-0 flex flex-col min-h-screen justify-between bg-white ${closeSidebar ? '' : 'w-[16rem]'} p-[1.25rem] shadow-[0px_1px_8px_2px_rgba(20,20,20,0.08)] z-60`}>\r\n            <button\r\n                onClick={() => dispatch(toggleCloseSidebar())}\r\n                className=\"absolute top-1/2 -right-4 transform -translate-y-1/2 bg-white border rounded-full p-1 shadow-md hover:bg-gray-100 transition\"\r\n            >\r\n                {closeSidebar ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}\r\n            </button>\r\n\r\n            <div className=\"flex-col w-full justify-start items-start gap-5 inline-flex\">\r\n                <HeaderSidebar />\r\n                <div className=\"flex flex-col gap-3 w-full\">\r\n                    {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon1} text={'Tổng quan'} /> */}\r\n\r\n                    <div className=\"flex flex-col gap-3 overflow-y-auto max-h-[calc(100vh-200px)]\">\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/student-management')}\r\n                            route={'/admin/student-management'}\r\n                            Icon={Users}\r\n                            text={'Học sinh'}\r\n                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT, UserType.ASSISTANT]}\r\n\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/class-management')}\r\n                            route={'/admin/class-management'}\r\n                            Icon={School}\r\n                            text={'Lớp học'}\r\n                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT, UserType.ASSISTANT]}\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/question-management')}\r\n                            route={'/admin/question-management'}\r\n                            Icon={ClipboardList}\r\n                            text={'Câu hỏi'}\r\n                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT]}\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/question-report-management')}\r\n                            route={'/admin/question-report-management'}\r\n                            Icon={FileSearch}\r\n                            text={'Báo cáo câu hỏi'}\r\n                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT]}\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/exam-management')}\r\n                            route={'/admin/exam-management'}\r\n                            Icon={FileText}\r\n                            text={'Đề thi'}\r\n                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT]}\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/AI/exam-management')}\r\n                            route={'/admin/AI/exam-management'}\r\n                            Icon={Bot}\r\n                            text={'Đề thi tạo bởi AI'}\r\n                            role={[UserType.ADMIN]}\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/code-management')}\r\n                            route={'/admin/code-management'}\r\n                            Icon={Code}\r\n                            text={'Code'}\r\n                            role={[UserType.ADMIN]}\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/achievement-management')}\r\n                            route={'/admin/achievement-management'}\r\n                            Icon={Trophy}\r\n                            text={'Thành tích'}\r\n                            role={[UserType.ADMIN, UserType.TEACHER, UserType.MARKETING]}\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/tuition-payment')}\r\n                            route={'/admin/tuition-payment'}\r\n                            Icon={CreditCard}\r\n                            text={'Học phí'}\r\n                            role={[UserType.ADMIN, UserType.TEACHER, UserType.HUMANRESOURCEMANAGEMENT]}\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/article-management')}\r\n                            route={'/admin/article-management'}\r\n                            Icon={Newspaper}\r\n                            text={'Bài viết'}\r\n                            role={[UserType.ADMIN]}\r\n                        />\r\n                        <MenuSidebar\r\n                            onClick={() => navigate('/admin/homepage-management')}\r\n                            route={'/admin/homepage-management'}\r\n                            Icon={Globe}\r\n                            text={'Trang chủ'}\r\n                            role={[UserType.ADMIN, UserType.MARKETING]}\r\n                        />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div className=\"flex-col w-full justify-start items-start gap-3 inline-flex\">\r\n                {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon3} text={'Thông báo'} icon2={notification} /> */}\r\n                {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon4} text={'Trợ giúp'} /> */}\r\n                <MenuSidebar onClick={handleLogout} route={'/login'} Icon={LogOut} text={'Đăng xuất'} />\r\n                <UserSidebar />\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default AdminSidebar;\r\n"], "mappings": ";;;AAAA,SAASA,WAAW,QAAQ,qBAAqB;AACjD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,cAAc,EAAEC,kBAAkB,EAAEC,kBAAkB,QAAQ,qCAAqC;AAC5G,SAASC,MAAM,QAAQ,+BAA+B;AACtD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,SAASC,WAAW,EAAEC,YAAY,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,aAAa,EAAEC,UAAU,EAAEC,GAAG,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtK,MAAMC,WAAW,GAAGC,IAAA,IAAsD;EAAAC,EAAA;EAAA,IAArD;IAAEC,OAAO;IAAEC,KAAK;IAAEC,IAAI;IAAEC,IAAI;IAAEC,KAAK;IAAEC,IAAI,GAAG;EAAG,CAAC,GAAAP,IAAA;EACjE,MAAMQ,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,YAAY,GAAGnC,WAAW,CAACoC,KAAK;IAAA,IAAAC,cAAA;IAAA,QAAAA,cAAA,GAAID,KAAK,CAACE,OAAO,cAAAD,cAAA,uBAAbA,cAAA,CAAeF,YAAY;EAAA,EAAC,CAAC,CAAC;;EAExE,MAAMI,IAAI,GAAGvC,WAAW,CAAEoC,KAAK,IAAKA,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC;EACpD,IAAIN,IAAI,CAACQ,MAAM,GAAG,CAAC,IAAI,CAACR,IAAI,CAACS,QAAQ,CAACH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,QAAQ,CAAC,EAAE;IACnD,OAAO,IAAI;EACf;EAEA,oBACInB,OAAA;IAAKoB,SAAS,EAAC,uCAAuC;IAAAC,QAAA,eAElDrB,OAAA;MACII,OAAO,EAAEA,OAAQ;MACjBgB,SAAS,2CAAAE,MAAA,CACfX,YAAY,GAAG,0BAA0B,GAAG,gCAAgC,gBAAAW,MAAA,CAC5EZ,QAAQ,CAACa,QAAQ,CAACL,QAAQ,CAACb,KAAK,CAAC,GACjB,yBAAyB,GACzB,iEAAiE,CAAG;MAAAgB,QAAA,gBAE9ErB,OAAA,CAACM,IAAI;QAACc,SAAS,EAAC;MAAS;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC3B,CAAChB,YAAY,iBACVX,OAAA;QAAKoB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACvDrB,OAAA;UAAKoB,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC3Fd;QAAI;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACLnB,KAAK;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAER;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAAAxB,EAAA,CAjCKF,WAAW;EAAA,QACI3B,WAAW,EACPE,WAAW,EAEnBA,WAAW;AAAA;AAAAoD,EAAA,GAJtB3B,WAAW;AAmCjB,MAAM4B,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAMC,QAAQ,GAAGvD,WAAW,CAACoC,KAAK,IAAIA,KAAK,CAACE,OAAO,CAACkB,YAAY,CAAC;EACjE,MAAMC,eAAe,GAAGzD,WAAW,CAACoC,KAAK,IAAIA,KAAK,CAACE,OAAO,CAACoB,mBAAmB,CAAC;EAC/E,MAAMvB,YAAY,GAAGnC,WAAW,CAACoC,KAAK,IAAIA,KAAK,CAACE,OAAO,CAACH,YAAY,CAAC;EACrE,MAAMwB,YAAY,GAAG3D,WAAW,CAACoC,KAAK,IAAIA,KAAK,CAACE,OAAO,CAACsB,gBAAgB,CAAC;EACzE,MAAMC,QAAQ,GAAG5D,WAAW,CAAC,CAAC;EAC9B,MAAM6D,QAAQ,GAAG/D,WAAW,CAAC,CAAC;EAE9B,MAAMgE,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMF,QAAQ,CAACxD,MAAM,CAAC,CAAC,CAAC;IACxByD,QAAQ,CAAC,QAAQ,CAAC;EACtB,CAAC;EAED,MAAME,KAAK,gBACPxC,OAAA;IAAK,wBAAgB;IAACoB,SAAS,EAAC,UAAU;IAAAC,QAAA,eACtCrB,OAAA;MAAKyC,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC,4BAA4B;MAAAxB,QAAA,eAC1FrB,OAAA;QAAM8C,CAAC,EAAC,q+EAAq+E;QAACF,IAAI,EAAC;MAAS;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9/E;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,oBACI3B,OAAA;IAAKoB,SAAS,sEAAAE,MAAA,CAAsEX,YAAY,GAAG,EAAE,GAAG,WAAW,mEAAiE;IAAAU,QAAA,gBAChLrB,OAAA;MACII,OAAO,EAAEA,CAAA,KAAMiC,QAAQ,CAACzD,kBAAkB,CAAC,CAAC,CAAE;MAC9CwC,SAAS,EAAC,8HAA8H;MAAAC,QAAA,EAEvIV,YAAY,gBAAGX,OAAA,CAACd,YAAY;QAAC6D,IAAI,EAAE;MAAG;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAG3B,OAAA,CAACf,WAAW;QAAC8D,IAAI,EAAE;MAAG;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAET3B,OAAA;MAAKoB,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBACxErB,OAAA,CAAClB,aAAa;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjB3B,OAAA;QAAKoB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eAGvCrB,OAAA;UAAKoB,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC1ErB,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,2BAA2B,CAAE;YACrDjC,KAAK,EAAE,2BAA4B;YACnCC,IAAI,EAAEnB,KAAM;YACZoB,IAAI,EAAE,UAAW;YACjBE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK,EAAEhE,QAAQ,CAACiE,OAAO,EAAEjE,QAAQ,CAACkE,eAAe,EAAElE,QAAQ,CAACmE,uBAAuB,EAAEnE,QAAQ,CAACoE,SAAS;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5H,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,yBAAyB,CAAE;YACnDjC,KAAK,EAAE,yBAA0B;YACjCC,IAAI,EAAEb,MAAO;YACbc,IAAI,EAAE,SAAU;YAChBE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK,EAAEhE,QAAQ,CAACiE,OAAO,EAAEjE,QAAQ,CAACkE,eAAe,EAAElE,QAAQ,CAACmE,uBAAuB,EAAEnE,QAAQ,CAACoE,SAAS;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5H,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,4BAA4B,CAAE;YACtDjC,KAAK,EAAE,4BAA6B;YACpCC,IAAI,EAAEV,aAAc;YACpBW,IAAI,EAAE,SAAU;YAChBE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK,EAAEhE,QAAQ,CAACiE,OAAO,EAAEjE,QAAQ,CAACkE,eAAe;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,mCAAmC,CAAE;YAC7DjC,KAAK,EAAE,mCAAoC;YAC3CC,IAAI,EAAET,UAAW;YACjBU,IAAI,EAAE,iBAAkB;YACxBE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK,EAAEhE,QAAQ,CAACiE,OAAO,EAAEjE,QAAQ,CAACkE,eAAe;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,wBAAwB,CAAE;YAClDjC,KAAK,EAAE,wBAAyB;YAChCC,IAAI,EAAElB,QAAS;YACfmB,IAAI,EAAE,QAAS;YACfE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK,EAAEhE,QAAQ,CAACiE,OAAO,EAAEjE,QAAQ,CAACkE,eAAe;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,2BAA2B,CAAE;YACrDjC,KAAK,EAAE,2BAA4B;YACnCC,IAAI,EAAER,GAAI;YACVS,IAAI,EAAE,mBAAoB;YAC1BE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,wBAAwB,CAAE;YAClDjC,KAAK,EAAE,wBAAyB;YAChCC,IAAI,EAAEZ,IAAK;YACXa,IAAI,EAAE,MAAO;YACbE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,+BAA+B,CAAE;YACzDjC,KAAK,EAAE,+BAAgC;YACvCC,IAAI,EAAEX,MAAO;YACbY,IAAI,EAAE,YAAa;YACnBE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK,EAAEhE,QAAQ,CAACiE,OAAO,EAAEjE,QAAQ,CAACqE,SAAS;UAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,wBAAwB,CAAE;YAClDjC,KAAK,EAAE,wBAAyB;YAChCC,IAAI,EAAEjB,UAAW;YACjBkB,IAAI,EAAE,SAAU;YAChBE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK,EAAEhE,QAAQ,CAACiE,OAAO,EAAEjE,QAAQ,CAACmE,uBAAuB;UAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,2BAA2B,CAAE;YACrDjC,KAAK,EAAE,2BAA4B;YACnCC,IAAI,EAAEhB,SAAU;YAChBiB,IAAI,EAAE,UAAW;YACjBE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF3B,OAAA,CAACC,WAAW;YACRG,OAAO,EAAEA,CAAA,KAAMkC,QAAQ,CAAC,4BAA4B,CAAE;YACtDjC,KAAK,EAAE,4BAA6B;YACpCC,IAAI,EAAEf,KAAM;YACZgB,IAAI,EAAE,WAAY;YAClBE,IAAI,EAAE,CAACzB,QAAQ,CAACgE,KAAK,EAAEhE,QAAQ,CAACqE,SAAS;UAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN3B,OAAA;MAAKoB,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAGxErB,OAAA,CAACC,WAAW;QAACG,OAAO,EAAEmC,YAAa;QAAClC,KAAK,EAAE,QAAS;QAACC,IAAI,EAAEd,MAAO;QAACe,IAAI,EAAE;MAAY;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxF3B,OAAA,CAACjB,WAAW;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAG,GAAA,CA7HKD,YAAY;EAAA,QACGrD,WAAW,EACJA,WAAW,EACdA,WAAW,EACXA,WAAW,EACfC,WAAW,EACXF,WAAW;AAAA;AAAA+E,GAAA,GAN1BzB,YAAY;AA+HlB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAA0B,GAAA;AAAAC,YAAA,CAAA3B,EAAA;AAAA2B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}