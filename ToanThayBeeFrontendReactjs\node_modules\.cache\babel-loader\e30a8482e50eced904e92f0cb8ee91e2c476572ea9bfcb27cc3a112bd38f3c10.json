{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\latex\\\\RenderLatex.jsx\";\nimport React from \"react\";\nimport \"katex/dist/katex.min.css\";\nimport { BlockMath, InlineMath } from \"react-katex\";\nimport NoTranslate from \"../utils/NoTranslate\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LatexRenderer = _ref => {\n  let {\n    text,\n    className = '',\n    style\n  } = _ref;\n  if (text === null || text === undefined) return null;\n\n  // Debug: Log text để kiểm tra\n  console.log('LatexRenderer text:', text);\n\n  // Chuyển \\(...\\) => $...$, \\[...\\] => $$...$$\n  const formattedText = text.replace(/\\\\\\(/g, \"$\").replace(/\\\\\\)/g, \"$\").replace(/\\\\\\[/g, \"$$\").replace(/\\\\\\]/g, \"$$\");\n  console.log('Formatted text:', formattedText);\n\n  // Kiểm tra xem có chứa LaTeX không\n  const hasLatex = /\\$\\$.*?\\$\\$|\\$.*?\\$/gs.test(formattedText);\n\n  // Nếu không có LaTeX, trả về text thuần\n  if (!hasLatex) {\n    return /*#__PURE__*/_jsxDEV(NoTranslate, {\n      as: \"span\",\n      className: className,\n      style: style,\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Cấu hình KaTeX để tắt warnings với Unicode\n  const katexOptions = {\n    strict: false,\n    // Tắt strict mode hoàn toàn\n    throwOnError: false,\n    errorColor: '#cc0000',\n    macros: {\n      \"\\\\RR\": \"\\\\mathbb{R}\",\n      \"\\\\NN\": \"\\\\mathbb{N}\",\n      \"\\\\ZZ\": \"\\\\mathbb{Z}\",\n      \"\\\\QQ\": \"\\\\mathbb{Q}\",\n      \"\\\\CC\": \"\\\\mathbb{C}\"\n    }\n  };\n\n  // Regex phân chia nội dung theo LaTeX inline/block\n  const parts = formattedText.split(/(\\$\\$.*?\\$\\$|\\$.*?\\$)/gs);\n  const elements = parts.map((part, index) => {\n    try {\n      if (part.startsWith(\"$$\") && part.endsWith(\"$$\")) {\n        const mathContent = part.slice(2, -2);\n        console.log('Block math content:', mathContent);\n        return /*#__PURE__*/_jsxDEV(BlockMath, {\n          settings: katexOptions,\n          children: mathContent\n        }, \"block-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 24\n        }, this);\n      } else if (part.startsWith(\"$\") && part.endsWith(\"$\")) {\n        const mathContent = part.slice(1, -1);\n        console.log('Inline math content:', mathContent);\n        return /*#__PURE__*/_jsxDEV(InlineMath, {\n          settings: katexOptions,\n          children: mathContent\n        }, \"inline-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 24\n        }, this);\n      } else {\n        // Text thuần, không qua KaTeX\n        console.log('Plain text:', part);\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          children: part\n        }, \"text-\".concat(index), false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 24\n        }, this);\n      }\n    } catch (error) {\n      console.warn(\"KaTeX render error:\", error);\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        children: part\n      }, \"fallback-\".concat(index), false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 20\n      }, this); // fallback hiển thị text\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(NoTranslate, {\n    as: \"div\",\n    className: className,\n    style: style,\n    children: elements\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 9\n  }, this);\n};\n_c = LatexRenderer;\nexport default LatexRenderer;\nvar _c;\n$RefreshReg$(_c, \"LatexRenderer\");", "map": {"version": 3, "names": ["React", "BlockMath", "InlineMath", "NoTranslate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "text", "className", "style", "undefined", "console", "log", "formattedText", "replace", "hasLatex", "test", "as", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "katexOptions", "strict", "throwOnError", "errorColor", "macros", "parts", "split", "elements", "map", "part", "index", "startsWith", "endsWith", "<PERSON><PERSON><PERSON><PERSON>", "slice", "settings", "concat", "error", "warn", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/latex/RenderLatex.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport \"katex/dist/katex.min.css\";\r\nimport { BlockMath, InlineMath } from \"react-katex\";\r\nimport NoTranslate from \"../utils/NoTranslate\";\r\n\r\nconst LatexRenderer = ({ text, className = '', style }) => {\r\n    if (text === null || text === undefined) return null;\r\n\r\n    // Debug: Log text để kiểm tra\r\n    console.log('LatexRenderer text:', text);\r\n\r\n    // Chuyển \\(...\\) => $...$, \\[...\\] => $$...$$\r\n    const formattedText = text\r\n        .replace(/\\\\\\(/g, \"$\")\r\n        .replace(/\\\\\\)/g, \"$\")\r\n        .replace(/\\\\\\[/g, \"$$\")\r\n        .replace(/\\\\\\]/g, \"$$\");\r\n\r\n    console.log('Formatted text:', formattedText);\r\n\r\n    // Kiểm tra xem có chứa LaTeX không\r\n    const hasLatex = /\\$\\$.*?\\$\\$|\\$.*?\\$/gs.test(formattedText);\r\n\r\n    // Nếu không có LaTeX, trả về text thuần\r\n    if (!hasLatex) {\r\n        return (\r\n            <NoTranslate as=\"span\" className={className} style={style}>\r\n                {text}\r\n            </NoTranslate>\r\n        );\r\n    }\r\n\r\n    // Cấu hình KaTeX để tắt warnings với Unicode\r\n    const katexOptions = {\r\n        strict: false, // Tắt strict mode hoàn toàn\r\n        throwOnError: false,\r\n        errorColor: '#cc0000',\r\n        macros: {\r\n            \"\\\\RR\": \"\\\\mathbb{R}\",\r\n            \"\\\\NN\": \"\\\\mathbb{N}\",\r\n            \"\\\\ZZ\": \"\\\\mathbb{Z}\",\r\n            \"\\\\QQ\": \"\\\\mathbb{Q}\",\r\n            \"\\\\CC\": \"\\\\mathbb{C}\"\r\n        }\r\n    };\r\n\r\n    // Regex phân chia nội dung theo LaTeX inline/block\r\n    const parts = formattedText.split(/(\\$\\$.*?\\$\\$|\\$.*?\\$)/gs);\r\n\r\n    const elements = parts.map((part, index) => {\r\n        try {\r\n            if (part.startsWith(\"$$\") && part.endsWith(\"$$\")) {\r\n                const mathContent = part.slice(2, -2);\r\n                console.log('Block math content:', mathContent);\r\n                return <BlockMath key={`block-${index}`} settings={katexOptions}>{mathContent}</BlockMath>;\r\n            } else if (part.startsWith(\"$\") && part.endsWith(\"$\")) {\r\n                const mathContent = part.slice(1, -1);\r\n                console.log('Inline math content:', mathContent);\r\n                return <InlineMath key={`inline-${index}`} settings={katexOptions}>{mathContent}</InlineMath>;\r\n            } else {\r\n                // Text thuần, không qua KaTeX\r\n                console.log('Plain text:', part);\r\n                return <span key={`text-${index}`}>{part}</span>;\r\n            }\r\n        } catch (error) {\r\n            console.warn(\"KaTeX render error:\", error);\r\n            return <span key={`fallback-${index}`}>{part}</span>; // fallback hiển thị text\r\n        }\r\n    });\r\n\r\n\r\n    return (\r\n        <NoTranslate as=\"div\" className={className} style={style}>\r\n            {elements}\r\n        </NoTranslate>\r\n    );\r\n};\r\n\r\nexport default LatexRenderer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,SAASC,SAAS,EAAEC,UAAU,QAAQ,aAAa;AACnD,OAAOC,WAAW,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,aAAa,GAAGC,IAAA,IAAqC;EAAA,IAApC;IAAEC,IAAI;IAAEC,SAAS,GAAG,EAAE;IAAEC;EAAM,CAAC,GAAAH,IAAA;EAClD,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKG,SAAS,EAAE,OAAO,IAAI;;EAEpD;EACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEL,IAAI,CAAC;;EAExC;EACA,MAAMM,aAAa,GAAGN,IAAI,CACrBO,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;EAE3BH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,aAAa,CAAC;;EAE7C;EACA,MAAME,QAAQ,GAAG,uBAAuB,CAACC,IAAI,CAACH,aAAa,CAAC;;EAE5D;EACA,IAAI,CAACE,QAAQ,EAAE;IACX,oBACIX,OAAA,CAACF,WAAW;MAACe,EAAE,EAAC,MAAM;MAACT,SAAS,EAAEA,SAAU;MAACC,KAAK,EAAEA,KAAM;MAAAS,QAAA,EACrDX;IAAI;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEtB;;EAEA;EACA,MAAMC,YAAY,GAAG;IACjBC,MAAM,EAAE,KAAK;IAAE;IACfC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,SAAS;IACrBC,MAAM,EAAE;MACJ,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE,aAAa;MACrB,MAAM,EAAE;IACZ;EACJ,CAAC;;EAED;EACA,MAAMC,KAAK,GAAGf,aAAa,CAACgB,KAAK,CAAC,yBAAyB,CAAC;EAE5D,MAAMC,QAAQ,GAAGF,KAAK,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IACxC,IAAI;MACA,IAAID,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9C,MAAMC,WAAW,GAAGJ,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC1B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwB,WAAW,CAAC;QAC/C,oBAAOhC,OAAA,CAACJ,SAAS;UAAwBsC,QAAQ,EAAEf,YAAa;UAAAL,QAAA,EAAEkB;QAAW,YAAAG,MAAA,CAA7CN,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoD,CAAC;MAC9F,CAAC,MAAM,IAAIU,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;QACnD,MAAMC,WAAW,GAAGJ,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC1B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEwB,WAAW,CAAC;QAChD,oBAAOhC,OAAA,CAACH,UAAU;UAAyBqC,QAAQ,EAAEf,YAAa;UAAAL,QAAA,EAAEkB;QAAW,aAAAG,MAAA,CAA7CN,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqD,CAAC;MACjG,CAAC,MAAM;QACH;QACAX,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEoB,IAAI,CAAC;QAChC,oBAAO5B,OAAA;UAAAc,QAAA,EAA6Bc;QAAI,WAAAO,MAAA,CAAdN,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MACpD;IACJ,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACZ7B,OAAO,CAAC8B,IAAI,CAAC,qBAAqB,EAAED,KAAK,CAAC;MAC1C,oBAAOpC,OAAA;QAAAc,QAAA,EAAiCc;MAAI,eAAAO,MAAA,CAAdN,KAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC,CAAC,CAAC;IAC1D;EACJ,CAAC,CAAC;EAGF,oBACIlB,OAAA,CAACF,WAAW;IAACe,EAAE,EAAC,KAAK;IAACT,SAAS,EAAEA,SAAU;IAACC,KAAK,EAAEA,KAAM;IAAAS,QAAA,EACpDY;EAAQ;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEtB,CAAC;AAACoB,EAAA,GAvEIrC,aAAa;AAyEnB,eAAeA,aAAa;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}