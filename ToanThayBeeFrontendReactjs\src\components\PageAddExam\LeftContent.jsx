// Optimized Form Panel Component
import { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    setExamData,
    postExam,
    nextStep,
    prevStep,
    setExamImage,
    setExamFile,
    setQuestionTNContent,
    setQuestionDSContent,
    setQuestionTLNContent,
    setCorrectAnswerTN,
    setCorrectAnswerDS,
    setCorrectAnswerTLN,
    setQuestions,
    setSelectedIndex,
} from "src/features/addExam/addExamSlice";
import DropMenuBarAdmin from "src/components/dropMenu/OptionBarAdmin";
import SuggestInputBarAdmin from "src/components/input/suggestInputBarAdmin";
import CompactStepHeader from "./CompactStepHeader";
import { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, CheckCircle, ChevronRight, ChevronLeft, Plus, Save, Trash2, Info } from "lucide-react";
import ImageUpload from "src/components/image/UploadImage";
import UploadPdf from "src/components/UploadPdf";
import LoadingSpinner from "src/components/loading/LoadingSpinner";
import NavigateBar from "./NavigateBar";
import SolutionEditor from "./SolutionEditor";
import { normalizeText, validateExamData } from "src/utils/question/questionUtils";
import ImageDropZone from "src/components/image/ImageDropZone";
import TextArea from "src/components/input/TextArea";

const Step1Form = () => {
    const dispatch = useDispatch();
    const { examData, examImage, examFile } = useSelector((state) => state.addExam);
    const { codes } = useSelector(state => state.codes);

    const updateExamData = (field, value) => {
        dispatch(setExamData({ field, value }));
    };


    return (
        <div className="space-y-3 p-3">
            {/* Compact Name & Type Row */}
            <div className="grid grid-cols-2 gap-2">
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        Tên đề thi <span className="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        value={examData.name || ''}
                        onChange={(e) => updateExamData('name', e.target.value)}
                        className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Nhập tên đề thi"
                    />
                </div>
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        Kiểu đề <span className="text-red-500">*</span>
                    </label>
                    <DropMenuBarAdmin
                        selectedOption={examData.typeOfExam}
                        onChange={(option) => updateExamData('typeOfExam', option)}
                        options={Array.isArray(codes["exam type"]) ? codes["exam type"] : []}
                        className="text-xs"
                    />
                </div>
            </div>

            {/* Compact Class & Year Row */}
            <div className="grid grid-cols-2 gap-2">
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        Lớp <span className="text-red-500">*</span>
                    </label>
                    <DropMenuBarAdmin
                        selectedOption={examData.class}
                        onChange={(option) => updateExamData('class', option)}
                        options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                        className="text-xs"
                    />
                </div>
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        Năm <span className="text-red-500">*</span>
                    </label>
                    <DropMenuBarAdmin
                        selectedOption={examData.year}
                        onChange={(option) => updateExamData('year', option)}
                        options={Array.isArray(codes["year"]) ? codes["year"] : []}
                        className="text-xs"
                    />
                </div>
            </div>

            {/* Compact Duration & Pass Rate Row */}
            <div className="grid grid-cols-2 gap-2">
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        <Clock className="w-3 h-3 inline mr-1" />
                        Thời gian (phút)
                    </label>
                    <input
                        type="number"
                        value={examData.testDuration || ''}
                        onChange={(e) => updateExamData('testDuration', e.target.value)}
                        className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="90"
                    />
                </div>
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        <Users className="w-3 h-3 inline mr-1" />
                        Điểm đạt (%)
                    </label>
                    <input
                        type="number"
                        value={examData.passRate || ''}
                        onChange={(e) => updateExamData('passRate', e.target.value)}
                        className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="50"
                    />
                </div>
            </div>

            {/* Chapter (conditional) */}
            {examData.typeOfExam === "OT" && (
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        <BookOpen className="w-3 h-3 inline mr-1" />
                        Chương
                    </label>
                    <SuggestInputBarAdmin
                        selectedOption={examData.chapter}
                        onChange={(option) => updateExamData('chapter', option)}
                        options={Array.isArray(codes["chapter"]) ? codes["chapter"] : []}
                        className="text-xs"
                    />
                </div>
            )}
            <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Link lời giải</label>
                <textarea
                    value={examData.solutionUrl}
                    onChange={(e) => updateExamData('solutionUrl', e.target.value)}
                    className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Nhập link lời giải vd: youtube, ..."
                />
            </div>
            {/* Compact Description */}
            <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Mô tả</label>
                <textarea
                    value={examData.description || ''}
                    onChange={(e) => updateExamData('description', e.target.value)}
                    rows={2}
                    className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Mô tả ngắn về đề thi..."
                />
            </div>

            {/* Compact Checkboxes */}
            <div className="flex items-center gap-3">
                <label className="flex items-center text-xs">
                    <input
                        type="checkbox"
                        checked={examData.public || false}
                        onChange={(e) => updateExamData('public', e.target.checked)}
                        className="form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="ml-1 text-gray-700">Công khai</span>
                </label>
                <label className="flex items-center text-xs">
                    <input
                        type="checkbox"
                        checked={examData.isClassroomExam || false}
                        onChange={(e) => updateExamData('isClassroomExam', e.target.checked)}
                        className="form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="ml-1 text-gray-700">Đề thi lớp</span>
                </label>
            </div>

            {/* Compact File Uploads */}
            <div className="flex flex-col gap-2">
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        <ImageIcon className="w-3 h-3 inline mr-1" />
                        Ảnh đề thi
                    </label>
                    <ImageUpload
                        image={examImage}
                        setImage={(img) => dispatch(setExamImage(img))}
                        inputId="exam-image-compact"
                        compact={true}
                    />
                </div>
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        <Upload className="w-3 h-3 inline mr-1" />
                        File PDF
                    </label>
                    <UploadPdf
                        setPdf={(pdf) => dispatch(setExamFile(pdf))}
                        deleteButton={false}
                        compact={true}
                    />
                </div>
            </div>
        </div>
    )
}

const ButtonAddQuestion = ({ text, onClick }) => {
    return (
        <button
            onClick={onClick}
            className="w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs">
            <Plus className="w-3 h-3 inline mr-1" />
            {text}
        </button>
    )
}



const AddQuestionForm = ({ questionContent, correctAnswerContent, handleContentChange, handleCorrectAnswerChange, hintAnswer, hintContent }) => {
    return (
        <div className="p-3 flex flex-col gap-4">
            <TextArea
                value={correctAnswerContent}
                onChange={handleCorrectAnswerChange}
                placeholder="Nhập đáp án"
                label="Đáp án"
                Icon={CheckCircle}
                hint={hintAnswer}
            />
            <TextArea
                value={questionContent}
                onChange={handleContentChange}
                placeholder="Nhập nội dung câu hỏi"
                label="Câu hỏi"
                Icon={Plus}
                hint={hintContent}
                buttonFilterText={{
                    text: "Lọc",
                    onClick: () => {
                        handleContentChange({
                            target: {
                                value: normalizeText(questionContent)
                            }
                        });
                    }
                }}
            />
        </div>
    )
}


const AddTNQuestion = () => {
    const dispatch = useDispatch();
    const { questionTNContent, correctAnswerTN } = useSelector((state) => state.addExam);

    const handleContentChange = (e) => {
        dispatch(setQuestionTNContent(e.target.value));
    };

    const handleCorrectAnswerChange = (e) => {
        dispatch(setCorrectAnswerTN(e.target.value));
    };

    return (
        <AddQuestionForm
            questionContent={questionTNContent}
            correctAnswerContent={correctAnswerTN}
            handleContentChange={handleContentChange}
            handleCorrectAnswerChange={handleCorrectAnswerChange}
            hintAnswer="Đáp án trắc nghiệm: A b c D ..."
            hintContent="Nội dung trắc nghiệm: Paste cả câu hỏi mệnh đề và lời giải"
        />
    )
}

const AddDSQuestion = () => {
    const dispatch = useDispatch();
    const { questionDSContent, correctAnswerDS } = useSelector((state) => state.addExam);

    const handleContentChange = (e) => {
        dispatch(setQuestionDSContent(e.target.value));
    };

    const handleCorrectAnswerChange = (e) => {
        dispatch(setCorrectAnswerDS(e.target.value));
    };

    return (
        <AddQuestionForm
            questionContent={questionDSContent}
            correctAnswerContent={correctAnswerDS}
            handleContentChange={handleContentChange}
            handleCorrectAnswerChange={handleCorrectAnswerChange}
            hintAnswer="Đáp án đúng sai: ĐĐSS dsss DSDS ..."
            hintContent="Nội dung câu hỏi: Paste cả câu hỏi mệnh đề và lời giải"
        />
    )
}

const AddTLNQuestion = () => {
    const dispatch = useDispatch();
    const { questionTLNContent, correctAnswerTLN } = useSelector((state) => state.addExam);

    const handleContentChange = (e) => {
        dispatch(setQuestionTLNContent(e.target.value));
    };

    const handleCorrectAnswerChange = (e) => {
        dispatch(setCorrectAnswerTLN(e.target.value));
    };

    return (
        <AddQuestionForm
            questionContent={questionTLNContent}
            correctAnswerContent={correctAnswerTLN}
            handleContentChange={handleContentChange}
            handleCorrectAnswerChange={handleCorrectAnswerChange}
            hintAnswer="Đáp án trả lời ngắn: 3,14 1.5 3,2"
            hintContent="Nội dung câu hỏi: Paste cả câu hỏi mệnh đề và lời giải"
        />
    )
}

const Step2Form = () => {
    const [isViewAdd, setIsViewAdd] = useState(true);
    const [view, setView] = useState('TN');
    const { questionTNContent, questionDSContent, questionTLNContent, correctAnswerTN, correctAnswerDS, correctAnswerTLN } = useSelector((state) => state.addExam);

    useEffect(() => {
        if (!isViewAdd) return
        if (questionTNContent.trim() !== "" || correctAnswerTN.trim() !== "") {
            setIsViewAdd(false);
            setView('TN')
        } else if (questionDSContent.trim() !== "" || correctAnswerDS.trim() !== "") {
            setIsViewAdd(false);
            setView('DS')
        } else if (questionTLNContent.trim() !== "" || correctAnswerTLN.trim() !== "") {
            setIsViewAdd(false);
            setView('TLN')
        }
    }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN]);

    return (
        <div className="space-y-3">
            {isViewAdd ? (
                <div className="text-center py-4 px-3">
                    <FileText className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                    <h3 className="text-sm font-medium text-gray-900 mb-1">Thêm câu hỏi</h3>
                    <p className="text-xs text-gray-600 mb-3">
                        Tạo câu hỏi cho đề thi của bạn
                    </p>
                    <div className="space-y-2">
                        <ButtonAddQuestion
                            text="Thêm câu trắc nghiệm"
                            onClick={() => {
                                setIsViewAdd(false);
                                setView('TN')
                            }}
                        />
                        <ButtonAddQuestion
                            text="Thêm câu đúng sai"
                            onClick={() => {
                                setIsViewAdd(false);
                                setView('DS')
                            }}
                        />
                        <ButtonAddQuestion
                            text="Thêm câu trả lời ngắn"
                            onClick={() => {
                                setIsViewAdd(false);
                                setView('TLN')
                            }}
                        />
                    </div>
                </div>
            ) : (
                <NavigateBar
                    list={[{
                        id: 1,
                        name: 'Trắc nghiệm',
                        value: 'TN'
                    },
                    {
                        id: 2,
                        name: 'Đúng sai',
                        value: 'DS'
                    },
                    {
                        id: 3,
                        name: 'Trả lời ngắn',
                        value: 'TLN'
                    }
                    ]}
                    active={view}
                    setActive={setView}
                />
            )}

            {view === 'TN' && !isViewAdd && (
                <AddTNQuestion />
            )}
            {view === 'DS' && !isViewAdd && (
                <AddDSQuestion />
            )}
            {view === 'TLN' && !isViewAdd && (
                <AddTLNQuestion />
            )}
        </div>
    )
}

const ListQuestions = ({ count, title, onClick, i }) => {
    return (
        <div className="flex  flex-row w-full justify-start items-center border-b border-[#e3e4e5] pb-2">
            <div className="text-[#090a0a] text-xs font-bold font-bevietnam leading-loose whitespace-nowrap mr-4 flex-shrink-0 min-w-[6.2rem]">
                {title}:
            </div>
            <div className="flex flex-row gap-5 w-full overflow-x-auto min-h-max ">
                {Array.from({ length: count }).map((_, index) => (
                    <div
                        key={index + title}
                        onClick={() => onClick(index)}
                        className={`cursor-pointer border text-xs border-[#e3e4e5] rounded flex justify-center whitespace-nowrap items-center gap-2.5 ${i === index ? 'bg-[#253f61] text-white' : 'bg-white text-[#253f61]'} px-2 py-1`}>
                        Câu hỏi {index + 1}
                    </div>
                ))}
            </div>
        </div>
    )
}

const Step3Form = () => {
    const { questions, selectedIndex, view } = useSelector((state) => state.addExam);
    const dispatch = useDispatch();
    const [questionCount, setQuestionCount] = useState({
        TN: 0,
        DS: 0,
        TLN: 0
    });
    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];
    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];
    const { codes } = useSelector((state) => state.codes);
    const [optionChapter, setOptionChapter] = useState([]);

    useEffect(() => {
        if (questions) {
            const counts = questions.reduce(
                (acc, q) => {
                    const type = q.questionData.typeOfQuestion;
                    if (acc[type] !== undefined) acc[type]++;
                    return acc;
                },
                { TN: 0, DS: 0, TLN: 0 }
            );

            setQuestionCount(counts);
        }
    }, [questions]);


    useEffect(() => {
        if (Array.isArray(codes["chapter"])) {
            if (questions[selectedIndex]?.questionData?.class && questions[selectedIndex]?.questionData?.class.trim() !== "") {
                setOptionChapter(
                    codes["chapter"].filter((code) => code.code.startsWith(questions[selectedIndex]?.questionData?.class) && code.code.length === 5)
                );
            } else {
                setOptionChapter(codes["chapter"].filter((code) => code.code.length === 5));
            }
        } else {
            setOptionChapter([]);
        }
    }, [codes, questions, selectedIndex]);

    const handleQuestionChange = (e, field) => {
        const newQuestions = questions.map((question, qIndex) => {
            if (qIndex === selectedIndex) {
                return {
                    ...question,
                    questionData: {
                        ...question.questionData,
                        [field]: e.target.value,
                    }
                };
            }
            return question;
        });
        dispatch(setQuestions(newQuestions));
    };
    const handleSolutionQuestionChange = (newSolution) => {
        const newQuestions = questions.map((question, qIndex) => {
            if (qIndex === selectedIndex) {
                return {
                    ...question,
                    questionData: {
                        ...question.questionData,
                        solution: newSolution,
                    }
                };
            }
            return question;
        });
        dispatch(setQuestions(newQuestions));
    };

    const handleStatementChange = (index, value, field) => {
        const newQuestions = questions.map((question, qIndex) => {
            if (qIndex === selectedIndex) {
                return {
                    ...question,
                    statements: question.statements.map((stmt, sIndex) => {
                        if (sIndex === index) {
                            return { ...stmt, [field]: value };
                        }
                        return stmt;
                    })
                };
            }
            return question;
        });
        dispatch(setQuestions(newQuestions));
    };


    return (
        <div className="space-y-3 p-3 w-full">
            <ListQuestions count={questionCount.TN} title={'Trắc nghiệm'} onClick={(index) => dispatch(setSelectedIndex(index))} i={selectedIndex} />
            <ListQuestions count={questionCount.DS} title={'Đúng sai'} onClick={(index) => dispatch(setSelectedIndex(index + questionCount.TN))} i={selectedIndex - questionCount.TN} />
            <ListQuestions count={questionCount.TLN} title={'Trả lời ngắn'} onClick={(index) => dispatch(setSelectedIndex(index + questionCount.DS + questionCount.TN))} i={selectedIndex - (questionCount.DS + questionCount.TN)} />

            {questions && questions[selectedIndex] && (
                <div className="space-y-3 w-full">
                    <div className="flex flex-col gap-2">
                        <h3 className="text-sm font-medium text-gray-900 mb-1">Phân loại</h3>
                        <div className="flex flex-row gap-2">
                            <DropMenuBarAdmin
                                selectedOption={questions[selectedIndex].questionData.class}
                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}
                                options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                                className="text-xs"
                            />
                            <SuggestInputBarAdmin
                                selectedOption={questions[selectedIndex].questionData.chapter}
                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}
                                options={optionChapter}
                                className="text-xs"
                            />
                            <DropMenuBarAdmin
                                selectedOption={questions[selectedIndex].questionData.difficulty}
                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}
                                options={Array.isArray(codes["difficulty"]) ? codes["difficulty"] : []}
                                className="text-xs"
                            />
                        </div>
                    </div>
                    <hr className=" bg-gray-200"></hr>
                    <div className="flex flex-col gap-2">
                        <h3 className="text-sm font-medium text-gray-900 mb-1">Thông tin câu hỏi</h3>
                        <div className="space-y-2">
                            <TextArea
                                value={questions[selectedIndex].questionData.content}
                                onChange={(e) => handleQuestionChange(e, 'content')}
                                placeholder="Nhập nội dung câu hỏi"
                                label="Câu hỏi"
                            />
                            {(view === 'image' || questions[selectedIndex].questionData.imageUrl) && (
                                <ImageDropZone
                                    imageUrl={questions[selectedIndex].questionData.imageUrl}
                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}
                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}
                                />
                            )}
                            {questions[selectedIndex].questionData.typeOfQuestion !== 'TLN' && (
                                <div className="space-y-2">
                                    {questions[selectedIndex].statements.map((statement, index) => (
                                        <div key={index} className="flex flex-col gap-2 items-center w-full">
                                            <div className="flex flex-row gap-2 items-center w-full">
                                                <p className="text-xs font-bold whitespace-nowrap">
                                                    {questions[selectedIndex].questionData.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}
                                                </p>
                                                <TextArea
                                                    value={statement.content}
                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}
                                                    placeholder="Nhập nội dung mệnh đề"
                                                />
                                            </div>
                                            {(view === 'image' || statement.imageUrl) && (
                                                <ImageDropZone
                                                    imageUrl={statement.imageUrl}
                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}
                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}
                                                />
                                            )}

                                        </div>
                                    ))}
                                </div>
                            )}
                            {questions[selectedIndex].questionData.typeOfQuestion === 'TLN' && (
                                <div className="space-y-2">
                                    <TextArea
                                        value={questions[selectedIndex].questionData.correctAnswer}
                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}
                                        placeholder="Nhập đáp án"
                                        label="Đáp án"
                                        Icon={CheckCircle}
                                    />
                                </div>
                            )}
                            {/* <TextArea
                                value={questions[selectedIndex].questionData.solution}
                                onChange={(e) => handleQuestionChange(e, 'solution')}
                                placeholder="Nhập lời giải"
                                label="Lời giải"
                                Icon={CheckCircle}
                            /> */}
                            <SolutionEditor
                                solution={questions[selectedIndex].questionData.solution}
                                onSolutionChange={handleSolutionQuestionChange}
                            />
                        </div>
                    </div>

                </div>
            )}
        </div>
    )
}


const LeftContent = () => {
    const dispatch = useDispatch();
    const { step, examData, loading, examImage, examFile, questions } = useSelector((state) => state.addExam);
    const handleNext = () => {
        if (step < 3) dispatch(nextStep());
    };

    const handlePrev = () => {
        if (step > 1) dispatch(prevStep());
    };

    const handleSubmit = async () => {
        if (!validateExamData(examData, dispatch)) return;

        await dispatch(postExam({
            examData,
            examImage,
            questions: questions || [],
            examFile,
        })).unwrap();
        // Handle success
    };

    return (
        <div className="flex flex-col h-[calc(100vh_-_42px)]">
            {/* Compact Step Header */}
            <CompactStepHeader />

            {/* Scrollable Form Content */}
            <div className="flex-1 overflow-y-auto">
                {/* Step 1: Basic Information */}
                {step === 1 && (
                    <Step1Form />
                )}

                {/* Step 2: Questions */}
                {step === 2 && (
                    <Step2Form />
                )}

                {/* Step 3: Confirmation */}
                {step === 3 && (
                    <Step3Form />
                )}
            </div>

            {/* Compact Navigation Footer */}
            <div className="border-t border-gray-200 p-2 bg-white">
                <div className="flex justify-between items-center">
                    <button
                        onClick={handlePrev}
                        disabled={step === 1}
                        className="flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <ChevronLeft className="w-3 h-3" />
                        Quay lại
                    </button>

                    {step < 3 ? (
                        <button
                            onClick={handleNext}
                            className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs"
                        >
                            Tiếp theo
                            <ChevronRight className="w-3 h-3" />
                        </button>
                    ) : (
                        <button
                            onClick={handleSubmit}
                            disabled={loading}
                            className="flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs"
                        >
                            {loading ? (
                                <>
                                    <LoadingSpinner minHeight="min-h-0" />
                                    Đang tạo...
                                </>
                            ) : (
                                <>
                                    <Save className="w-3 h-3" />
                                    Tạo đề thi
                                </>
                            )}
                        </button>
                    )}
                </div>
            </div>
        </div >
    );
};

export default LeftContent;