// Optimized Form Panel Component
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchCodesByType } from "src/features/code/codeSlice";
import { setExamData, postExam, nextStep, prevStep } from "src/features/addExam/addExamSlice";
import DropMenuBarAdmin from "src/components/dropMenu/OptionBarAdmin";
import SuggestInputBarAdmin from "src/components/input/suggestInputBarAdmin";
import CompactStepHeader from "./CompactStepHeader";
import { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, CheckCircle, ChevronRight, ChevronLeft, Plus, Save } from "lucide-react";
import ImageUpload from "src/components/image/UploadImage";
import UploadPdf from "src/components/UploadPdf";
import LoadingSpinner from "src/components/loading/LoadingSpinner";


const LeftContent = () => {
    const dispatch = useDispatch();
    const { step, examData, loading } = useSelector((state) => state.addExam);
    const { codes } = useSelector(state => state.codes);

    // Load codes on mount
    useEffect(() => {
        dispatch(fetchCodesByType(["chapter", "difficulty", "grade", "exam type", "year"]));
    }, [dispatch]);

    const updateExamData = (field, value) => {
        console.log('Updating exam data:', field, value);
        dispatch(setExamData({ field, value }));
    };

    const handleNext = () => {
        if (step < 3) dispatch(nextStep());
    };

    const handlePrev = () => {
        if (step > 1) dispatch(prevStep());
    };

    const handleSubmit = async () => {
        try {
            await dispatch(postExam({
                examData,
                examImage: null,
                questions: [],
                questionImages: [],
                statementImages: [],
                solutionImages: [],
                examFile: null
            })).unwrap();
            // Handle success
        } catch (error) {
            console.error('Error creating exam:', error);
        }
    };

    return (
        <div className="flex flex-col h-full">
            {/* Compact Step Header */}
            <CompactStepHeader />

            {/* Scrollable Form Content */}
            <div className="flex-1 overflow-y-auto">
                <div className="p-3 space-y-3">
                    {/* Step 1: Basic Information */}
                    {step === 1 && (
                        <div className="space-y-3">
                            {/* Compact Name & Type Row */}
                            <div className="grid grid-cols-2 gap-2">
                                <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">
                                        Tên đề thi <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        value={examData.name || ''}
                                        onChange={(e) => updateExamData('name', e.target.value)}
                                        className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Nhập tên đề thi"
                                    />
                                </div>
                                <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">
                                        Kiểu đề <span className="text-red-500">*</span>
                                    </label>
                                    <DropMenuBarAdmin
                                        selectedOption={examData.typeOfExam}
                                        onChange={(option) => updateExamData('typeOfExam', option)}
                                        options={Array.isArray(codes["exam type"]) ? codes["exam type"] : []}
                                        className="text-xs"
                                    />
                                </div>
                            </div>

                            {/* Compact Class & Year Row */}
                            <div className="grid grid-cols-2 gap-2">
                                <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">
                                        Lớp <span className="text-red-500">*</span>
                                    </label>
                                    <DropMenuBarAdmin
                                        selectedOption={examData.class}
                                        onChange={(option) => updateExamData('class', option)}
                                        options={Array.isArray(codes["grade"]) ? codes["grade"] : []}
                                        className="text-xs"
                                    />
                                </div>
                                <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">
                                        Năm <span className="text-red-500">*</span>
                                    </label>
                                    <DropMenuBarAdmin
                                        selectedOption={examData.year}
                                        onChange={(option) => updateExamData('year', option)}
                                        options={Array.isArray(codes["year"]) ? codes["year"] : []}
                                        className="text-xs"
                                    />
                                </div>
                            </div>

                            {/* Compact Duration & Pass Rate Row */}
                            <div className="grid grid-cols-2 gap-2">
                                <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">
                                        <Clock className="w-3 h-3 inline mr-1" />
                                        Thời gian (phút)
                                    </label>
                                    <input
                                        type="number"
                                        value={examData.testDuration || ''}
                                        onChange={(e) => updateExamData('testDuration', e.target.value)}
                                        className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="90"
                                    />
                                </div>
                                <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">
                                        <Users className="w-3 h-3 inline mr-1" />
                                        Điểm đạt (%)
                                    </label>
                                    <input
                                        type="number"
                                        value={examData.passRate || ''}
                                        onChange={(e) => updateExamData('passRate', e.target.value)}
                                        className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="50"
                                    />
                                </div>
                            </div>

                            {/* Chapter (conditional) */}
                            {examData.typeOfExam === "OT" && (
                                <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">
                                        <BookOpen className="w-3 h-3 inline mr-1" />
                                        Chương
                                    </label>
                                    <SuggestInputBarAdmin
                                        selectedOption={examData.chapter}
                                        onChange={(option) => updateExamData('chapter', option)}
                                        options={Array.isArray(codes["chapter"]) ? codes["chapter"] : []}
                                        className="text-xs"
                                    />
                                </div>
                            )}

                            {/* Compact Description */}
                            <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">Mô tả</label>
                                <textarea
                                    value={examData.description || ''}
                                    onChange={(e) => updateExamData('description', e.target.value)}
                                    rows={2}
                                    className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Mô tả ngắn về đề thi..."
                                />
                            </div>

                            {/* Compact Checkboxes */}
                            <div className="flex items-center gap-3">
                                <label className="flex items-center text-xs">
                                    <input
                                        type="checkbox"
                                        checked={examData.public || false}
                                        onChange={(e) => updateExamData('public', e.target.checked)}
                                        className="form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500"
                                    />
                                    <span className="ml-1 text-gray-700">Công khai</span>
                                </label>
                                <label className="flex items-center text-xs">
                                    <input
                                        type="checkbox"
                                        checked={examData.isClassroomExam || false}
                                        onChange={(e) => updateExamData('isClassroomExam', e.target.checked)}
                                        className="form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500"
                                    />
                                    <span className="ml-1 text-gray-700">Đề thi lớp</span>
                                </label>
                            </div>

                            {/* Compact File Uploads */}
                            <div className="grid grid-cols-2 gap-2">
                                <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">
                                        <ImageIcon className="w-3 h-3 inline mr-1" />
                                        Ảnh đề thi
                                    </label>
                                    <div className="border-2 border-dashed border-gray-300 rounded p-2 text-center">
                                        <ImageUpload
                                            image={examData.imageUrl}
                                            setImage={(img) => updateExamData('imageUrl', img)}
                                            inputId="exam-image-compact"
                                            compact={true}
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">
                                        <Upload className="w-3 h-3 inline mr-1" />
                                        File PDF
                                    </label>
                                    <div className="border-2 border-dashed border-gray-300 rounded p-2 text-center">
                                        <UploadPdf
                                            setPdf={(pdf) => updateExamData('pdfFile', pdf)}
                                            deleteButton={false}
                                            compact={true}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Step 2: Questions */}
                    {step === 2 && (
                        <div className="space-y-3">
                            <div className="text-center py-4">
                                <FileText className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                                <h3 className="text-sm font-medium text-gray-900 mb-1">Thêm câu hỏi</h3>
                                <p className="text-xs text-gray-600 mb-3">
                                    Tạo câu hỏi cho đề thi của bạn
                                </p>
                                <div className="space-y-2">
                                    <button className="w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs">
                                        <Plus className="w-3 h-3 inline mr-1" />
                                        Thêm câu trắc nghiệm
                                    </button>
                                    <button className="w-full px-3 py-2 bg-green-50 border border-green-200 rounded hover:bg-green-100 transition-colors text-xs">
                                        <Plus className="w-3 h-3 inline mr-1" />
                                        Thêm câu đúng sai
                                    </button>
                                    <button className="w-full px-3 py-2 bg-purple-50 border border-purple-200 rounded hover:bg-purple-100 transition-colors text-xs">
                                        <Plus className="w-3 h-3 inline mr-1" />
                                        Thêm câu trả lời ngắn
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Step 3: Confirmation */}
                    {step === 3 && (
                        <div className="space-y-3">
                            <div className="text-center py-3">
                                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                </div>
                                <h3 className="text-sm font-medium text-gray-900 mb-1">Xác nhận tạo đề thi</h3>
                                <p className="text-xs text-gray-600">
                                    Kiểm tra thông tin và hoàn tất
                                </p>
                            </div>

                            {/* Compact Summary */}
                            <div className="bg-gray-50 rounded p-2">
                                <h4 className="font-medium text-gray-900 mb-2 text-xs">Thông tin tóm tắt</h4>
                                <div className="grid grid-cols-2 gap-1 text-xs">
                                    <div>
                                        <span className="text-gray-600">Tên:</span>
                                        <span className="ml-1 font-medium">{examData.name || "Chưa nhập"}</span>
                                    </div>
                                    <div>
                                        <span className="text-gray-600">Kiểu:</span>
                                        <span className="ml-1 font-medium">{examData.typeOfExam || "Chưa chọn"}</span>
                                    </div>
                                    <div>
                                        <span className="text-gray-600">Lớp:</span>
                                        <span className="ml-1 font-medium">{examData.class || "Chưa chọn"}</span>
                                    </div>
                                    <div>
                                        <span className="text-gray-600">Năm:</span>
                                        <span className="ml-1 font-medium">{examData.year || "Chưa chọn"}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Validation Errors */}
                            {(!examData.name || !examData.typeOfExam || !examData.class || !examData.year) && (
                                <div className="bg-red-50 border border-red-200 rounded p-2">
                                    <h5 className="font-medium text-red-800 mb-1 text-xs">Thiếu thông tin:</h5>
                                    <ul className="text-xs text-red-700 space-y-0.5">
                                        {!examData.name && <li>• Tên đề thi</li>}
                                        {!examData.typeOfExam && <li>• Kiểu đề</li>}
                                        {!examData.class && <li>• Lớp</li>}
                                        {!examData.year && <li>• Năm</li>}
                                    </ul>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>

            {/* Compact Navigation Footer */}
            <div className="border-t border-gray-200 p-2 bg-white">
                <div className="flex justify-between items-center">
                    <button
                        onClick={handlePrev}
                        disabled={step === 1}
                        className="flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <ChevronLeft className="w-3 h-3" />
                        Quay lại
                    </button>

                    {step < 3 ? (
                        <button
                            onClick={handleNext}
                            className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs"
                        >
                            Tiếp theo
                            <ChevronRight className="w-3 h-3" />
                        </button>
                    ) : (
                        <button
                            onClick={handleSubmit}
                            disabled={loading || !examData.name || !examData.typeOfExam || !examData.class || !examData.year}
                            className="flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs"
                        >
                            {loading ? (
                                <>
                                    <LoadingSpinner size="sm" />
                                    Đang tạo...
                                </>
                            ) : (
                                <>
                                    <Save className="w-3 h-3" />
                                    Tạo đề thi
                                </>
                            )}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default LeftContent;