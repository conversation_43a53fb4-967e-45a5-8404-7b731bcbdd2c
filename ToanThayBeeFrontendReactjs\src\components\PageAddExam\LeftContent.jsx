import { useSelector, useDispatch } from "react-redux";
import { postExam, setExamData } from "src/features/addExam/addExamSlice";
import { CheckCircle, Edit, FileText } from "lucide-react"; // dùng icon lucide-react

const HeaderStep = () => {
    const { step } = useSelector((state) => state.addExam);

    const stepInfo = [
        { id: 1, label: "Thông tin đề thi", icon: <Edit className="w-5 h-5 mr-2" /> },
        { id: 2, label: "Soạn câu hỏi", icon: <FileText className="w-5 h-5 mr-2" /> },
        { id: 3, label: "Hoàn tất & đăng đề", icon: <CheckCircle className="w-5 h-5 mr-2" /> }
    ];

    return (
        <div className="space-y-3">
            {stepInfo.map((item) => (
                <div key={item.id} className={`flex items-center ${step === item.id ? "text-blue-500" : "text-gray-500"}`}>
                    {item.icon}
                    <span>{item.label}</span>
                </div>
            ))}
        </div>
    );
};

export const LeftContent = () => {
    const dispatch = useDispatch();
    const { examData } = useSelector((state) => state.addExam);

    return (
        <div className="flex-1 border-r bg-white h-[calc(100vh-69px)] border-gray-300 overflow-y-auto p-6 shadow-sm">
            <HeaderStep />
        </div>
    );
};

export default LeftContent;
