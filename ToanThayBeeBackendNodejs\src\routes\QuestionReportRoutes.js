import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as QuestionReportController from '../controllers/QuestionReportController.js'

const router = express.Router()

router.get('/v1/admin/question-report',
    requireRoles(Roles.JustClassManagement),
    async<PERSON><PERSON><PERSON>(QuestionReportController.getQuestionReport)
)
router.get('/v1/admin/question-report/:id',
    requireRoles(Roles.JustClassManagement),
    async<PERSON><PERSON><PERSON>(QuestionReportController.getQuestionReportById)
)
router.post('/v1/user/question-report',
    requireRoles(Roles.JustStudent),
    as<PERSON><PERSON><PERSON><PERSON>(QuestionReportController.postQuestionReport)
)
router.delete('/v1/admin/question-report/:id',
    requireRoles(Roles.JustClassManagement),
    as<PERSON><PERSON><PERSON><PERSON>(QuestionReportController.deleteQuestionReport)
)

export default router