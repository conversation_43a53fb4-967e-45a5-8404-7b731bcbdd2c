import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as LearningItemController from '../controllers/LearningItemController.js'
import uploadPDF from '../middlewares/pdfGoogleUpload.js'

const router = express.Router()



router.get('/v1/user/learning-item/uncompleted',
    requireRoles(Roles.JustStudent),
    asyncHandler(LearningItemController.getUncompletedLearningItem)
)


router.get('/v1/user/weekend/learning-item',
    requireRoles(Roles.JustStudent),
    asyncHandler(LearningItemController.getLearningItemWeekend)
)

router.get('/v1/user/day/learning-item',
    requireRoles(Roles.JustStudent),
    async<PERSON>and<PERSON>(LearningItemController.getLearningItemDay)
)

router.get('/v1/user/month/learning-item',
    requireRoles(Roles.JustStudent),
    as<PERSON><PERSON><PERSON><PERSON>(LearningItemController.getLearningItemMonth),
)

router.get('/v1/user/learning-item/:id',
    requireRoles(Roles.JustStudent),
    asyncHandler(LearningItemController.getLearningItemById)
)

router.get('/v1/user/learning-item/lesson/:lessonId',
    requireRoles(Roles.JustStudent),
    asyncHandler(LearningItemController.getLearningItemByLesson)
)

router.post('/v1/admin/learning-item',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(LearningItemController.postLearningItem)
)

router.post('/v1/admin/learning-item/:id/upload-pdf',
    requireRoles(Roles.AllExceptMarketingStudent),
    uploadPDF.single('pdf'),
    asyncHandler(LearningItemController.uploadLearningItemPdf)
)
router.put('/v1/admin/learning-item/:id',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(LearningItemController.putLearningItem)
)
router.put('/v1/user/learning-item/:learningItemId/mark',
    requireRoles(Roles.JustStudent),
    asyncHandler(LearningItemController.markLearningItem)
)
router.delete('/v1/admin/learning-item/:id',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(LearningItemController.deleteLearningItem)
)

export default router