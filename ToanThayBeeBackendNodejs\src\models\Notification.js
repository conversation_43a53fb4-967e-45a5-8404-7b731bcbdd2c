'use strict'
import { Model } from 'sequelize'

export default (sequelize, DataTypes) => {
  class Notification extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define associations
      Notification.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user'
      });
    }
  }
  
  Notification.init({
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'Loại thông báo: SYSTEM, CLASS, EXAM, LESSON, etc.'
    },
    isRead: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    relatedId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'ID của đối tượng liên quan (lớp, bài thi, bài học, etc.)'
    },
    relatedType: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: 'Loại đối tượng liên quan: CLASS, EXAM, LESSON, etc.'
    },
    actionUrl: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'URL để chuyển hướng khi người dùng nhấp vào thông báo'
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE
  }, {
    sequelize,
    modelName: 'Notification',
    tableName: 'notification'
  })
  
  return Notification
}
