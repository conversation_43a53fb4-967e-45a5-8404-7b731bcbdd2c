'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('tuitionPayment', 'isCustom', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '<PERSON><PERSON><PERSON> dấu kho<PERSON>n học phí được tạo thủ công (true) hoặc tự động (false)'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('tuitionPayment', 'isCustom');
  }
};
