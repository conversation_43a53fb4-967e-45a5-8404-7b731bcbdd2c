{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\ArticlePostPage.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState, useRef, useCallback, useMemo } from \"react\";\nimport AdminLayout from \"../../layouts/AdminLayout\";\nimport MarkdownEditor from \"../../components/latex/MarkDownEditer\";\nimport MarkdownPreview from \"@uiw/react-markdown-preview\";\nimport remarkMath from \"remark-math\";\nimport rehypeKatex from \"rehype-katex\";\nimport \"@uiw/react-markdown-preview/markdown.css\";\nimport \"katex/dist/katex.min.css\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { postArticle, fetchArticleById, putArticle } from \"../../features/article/articleSlice\";\nimport { fetchImages, postImage, deleteImage } from \"../../features/image/imageSlice\";\nimport { setSuccessMessage, setErrorMessage } from \"../../features/state/stateApiSlice\";\nimport \"../../styles/markdown-preview.css\";\nimport DropMenuBarAdmin from \"../../components/dropMenu/OptionBarAdmin\";\nimport { fetchCodesByType } from \"../../features/code/codeSlice\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ArticlePostPage = () => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    id\n  } = useParams();\n  const {\n    images\n  } = useSelector(state => state.images);\n  const {\n    article\n  } = useSelector(state => state.articles);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const [isEditing, setIsEditing] = useState(false);\n  const [articleId, setArticleId] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    title: \"\",\n    type: \"\",\n    class: \"\",\n    chapter: \"\",\n    author: \"\",\n    content: \"\"\n  });\n  const [isUploading, setIsUploading] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  // We don't need selectedImage state as we're directly inserting images\n  const [showImageGallery, setShowImageGallery] = useState(false);\n  const editorRef = useRef(null);\n  const previewRef = useRef(null);\n  const [optionChapter, setOptionChapter] = useState([]);\n\n  // Function to replace LaTeX delimiters for preview\n  const processContentForPreview = useMemo(() => {\n    if (!formData.content) return \"\";\n\n    // Replace \\( \\) with $ $ for inline math\n    // Replace \\[ \\] with $$ $$ for block math\n    let processedContent = formData.content.replace(/\\\\\\(/g, \"$\").replace(/\\\\\\)/g, \"$\").replace(/\\\\\\[/g, \"$$\").replace(/\\\\\\]/g, \"$$\");\n    return processedContent;\n  }, [formData.content]);\n\n  // Fetch images from the article folder when component mounts\n  useEffect(() => {\n    dispatch(fetchImages(\"article\"));\n    dispatch(fetchCodesByType([\"chapter\", \"grade\", \"article type\"]));\n  }, [dispatch]);\n\n  // Filter chapter options based on selected class\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (formData.class && formData.class.trim() !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(formData.class)));\n      } else {\n        setOptionChapter(codes[\"chapter\"]);\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, formData.class]);\n\n  // Check if we're in edit mode based on URL parameter\n  useEffect(() => {\n    if (id) {\n      setIsEditing(true);\n      setArticleId(id);\n    }\n  }, [id]);\n\n  // If editing an existing article, populate the form with article data\n  useEffect(() => {\n    if (articleId) {\n      dispatch(fetchArticleById(articleId));\n    }\n  }, [articleId, dispatch]);\n  useEffect(() => {\n    if (article && isEditing) {\n      // Make sure we're setting the exact values from the article\n      // This is important for the dropdown menus to work correctly\n      setFormData({\n        name: article.name || \"\",\n        title: article.title || \"\",\n        type: article.type || \"\",\n        class: article.class || \"\",\n        chapter: article.chapter || \"\",\n        author: article.author || \"\",\n        content: article.content || \"\"\n      });\n\n      // Log the values to help with debugging\n      console.log(\"Article data loaded:\", {\n        type: article.type,\n        class: article.class,\n        chapter: article.chapter\n      });\n    }\n  }, [article, isEditing]);\n\n  // Function to scroll preview to bottom\n  const scrollPreviewToBottom = useCallback(() => {\n    if (previewRef.current) {\n      const previewContainer = previewRef.current;\n      previewContainer.scrollTop = previewContainer.scrollHeight;\n    }\n  }, []);\n\n  // Scroll preview to bottom when content changes\n  useEffect(() => {\n    // Use a short delay to ensure the content has been rendered\n    const timer = setTimeout(scrollPreviewToBottom, 100);\n    return () => clearTimeout(timer);\n  }, [formData.content, scrollPreviewToBottom]);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Validate required fields\n  const validateForm = () => {\n    const requiredFields = ['name', 'type', 'title', 'content', 'author'];\n    const missingFields = [];\n    requiredFields.forEach(field => {\n      if (!formData[field] || formData[field].trim() === '') {\n        missingFields.push(field);\n      }\n    });\n    if (missingFields.length > 0) {\n      const fieldNames = {\n        name: 'Tên bài viết',\n        type: 'Loại bài viết',\n        title: 'Tiêu đề',\n        content: 'Nội dung',\n        author: 'Tác giả'\n      };\n      const missingFieldNames = missingFields.map(field => fieldNames[field]);\n      dispatch(setErrorMessage(\"Vui l\\xF2ng \\u0111i\\u1EC1n \\u0111\\u1EA7y \\u0111\\u1EE7 c\\xE1c tr\\u01B0\\u1EDDng: \".concat(missingFieldNames.join(', '))));\n      return false;\n    }\n    return true;\n  };\n\n  // Filter out null, undefined, or empty string values from formData\n  const filterNonNullValues = data => {\n    const filteredData = {};\n    Object.keys(data).forEach(key => {\n      // Keep the value if it's not null, undefined, or an empty string\n      if (data[key] !== null && data[key] !== undefined && data[key] !== '') {\n        filteredData[key] = data[key];\n      }\n    });\n    return filteredData;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form before submission\n    if (!validateForm()) {\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Filter out null values before submitting\n      const filteredData = filterNonNullValues(formData);\n\n      // Log the filtered data for debugging\n      console.log('Filtered form data:', filteredData);\n      if (isEditing && articleId) {\n        await dispatch(putArticle({\n          articleId,\n          data: filteredData\n        })).unwrap();\n        dispatch(setSuccessMessage(\"Cập nhật bài viết thành công\"));\n        navigate(\"/admin/article-management\");\n      } else {\n        await dispatch(postArticle(filteredData)).unwrap();\n        dispatch(setSuccessMessage(\"Đăng bài viết thành công\"));\n        navigate(\"/admin/article-management\");\n      }\n    } catch (error) {\n      dispatch(setErrorMessage(error.message || \"Có lỗi xảy ra khi lưu bài viết\"));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Handle image upload\n  const handleImageUpload = async e => {\n    const file = e.target.files[0];\n    if (!file) return;\n\n    // Check if file is an image\n    if (!file.type.startsWith('image/')) {\n      dispatch(setErrorMessage(\"Vui lòng chọn file hình ảnh\"));\n      return;\n    }\n    setIsUploading(true);\n    try {\n      const result = await dispatch(postImage({\n        image: file,\n        folder: \"article\"\n      })).unwrap();\n      if (result && result.file) {\n        insertImageToEditor(result.file);\n        dispatch(setSuccessMessage(\"Tải lên hình ảnh thành công\"));\n        // dispatch(fetchImages(\"article\"));\n      }\n    } catch (error) {\n      dispatch(setErrorMessage(\"Lỗi khi tải lên hình ảnh: \" + error.message));\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  // Insert image URL to editor content at cursor position or at the end\n  const insertImageToEditor = imageUrl => {\n    const imageMarkdown = \"![image](\".concat(imageUrl, \")\");\n    const currentContent = formData.content;\n\n    // Insert at the end if we don't have cursor position info\n    const newContent = currentContent + \"\\n\" + imageMarkdown + \"\\n\";\n    setFormData({\n      ...formData,\n      content: newContent\n    });\n\n    // Scroll preview to bottom after a short delay to allow rendering\n    setTimeout(scrollPreviewToBottom, 100);\n  };\n\n  // Handle selecting an image from the gallery\n  const handleSelectImage = imageUrl => {\n    insertImageToEditor(imageUrl);\n    setShowImageGallery(false);\n  };\n\n  // Handle deleting an image\n  const handleDeleteImage = async imageUrl => {\n    if (window.confirm(\"Bạn có chắc chắn muốn xóa hình ảnh này?\")) {\n      try {\n        await dispatch(deleteImage(imageUrl)).unwrap();\n        dispatch(setSuccessMessage(\"Xóa hình ảnh thành công\"));\n        // Refresh the image list\n        // dispatch(fetchImages(\"article\"));\n      } catch (error) {\n        dispatch(setErrorMessage(\"Lỗi khi xóa hình ảnh: \" + error.message));\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-slate-700\",\n          children: isEditing ? \"✏️ Chỉnh sửa bài viết\" : \"📝 Đăng bài viết lý thuyết\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 21\n        }, this), isEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setIsEditing(false);\n            setArticleId(null);\n            setFormData({\n              name: \"\",\n              title: \"\",\n              type: \"\",\n              class: \"\",\n              chapter: \"\",\n              author: \"\",\n              content: \"\"\n            });\n          },\n          className: \"bg-gray-500 text-white px-4 py-1 rounded hover:bg-gray-600\",\n          children: \"T\\u1EA1o m\\u1EDBi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"T\\xEAn b\\xE0i vi\\u1EBFt\",\n            value: formData.name,\n            onChange: handleChange,\n            className: \"w-full border rounded px-3 py-2\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"title\",\n            placeholder: \"Ti\\xEAu \\u0111\\u1EC1 b\\xE0i vi\\u1EBFt\",\n            value: formData.title,\n            onChange: handleChange,\n            className: \"w-full border rounded px-3 py-2\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"text-gray-700 font-medium\",\n              children: \"Lo\\u1EA1i b\\xE0i vi\\u1EBFt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n              selectedOption: formData.type,\n              onChange: option => setFormData({\n                ...formData,\n                type: option\n              }),\n              options: Array.isArray(codes[\"article type\"]) ? codes[\"article type\"] : [],\n              placeholder: \"Ch\\u1ECDn lo\\u1EA1i b\\xE0i vi\\u1EBFt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"text-gray-700 font-medium\",\n              children: \"L\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n              selectedOption: formData.class,\n              onChange: option => setFormData({\n                ...formData,\n                class: option\n              }),\n              options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n              placeholder: \"Ch\\u1ECDn l\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"text-gray-700 font-medium\",\n              children: \"Ch\\u01B0\\u01A1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n              selectedOption: formData.chapter,\n              onChange: option => setFormData({\n                ...formData,\n                chapter: option\n              }),\n              options: optionChapter,\n              placeholder: \"Ch\\u1ECDn ch\\u01B0\\u01A1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"author\",\n          placeholder: \"T\\xE1c gi\\u1EA3\",\n          value: formData.author,\n          onChange: handleChange,\n          className: \"w-full border rounded px-3 py-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            accept: \"image/*\",\n            id: \"image-upload\",\n            className: \"hidden\",\n            onChange: handleImageUpload,\n            disabled: isUploading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"image-upload\",\n            className: \"bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded cursor-pointer \".concat(isUploading ? 'opacity-50 cursor-not-allowed' : ''),\n            children: isUploading ? \"Đang tải lên...\" : \"Tải lên hình ảnh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowImageGallery(!showImageGallery),\n            className: \"bg-indigo-500 hover:bg-indigo-600 text-white px-3 py-1 rounded\",\n            children: showImageGallery ? \"Ẩn thư viện ảnh\" : \"Hiện thư viện ảnh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 21\n        }, this), showImageGallery && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border rounded-md p-4 bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold mb-2\",\n            children: \"Th\\u01B0 vi\\u1EC7n \\u1EA3nh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 29\n          }, this), images[\"article\"] && images[\"article\"].length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n            children: images[\"article\"].map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: image,\n                alt: \"Gallery image \".concat(index),\n                className: \"w-full h-24 object-cover rounded border cursor-pointer hover:opacity-90\",\n                onClick: () => handleSelectImage(image)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleDeleteImage(image),\n                className: \"absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 45\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 41\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 33\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"Kh\\xF4ng c\\xF3 h\\xECnh \\u1EA3nh n\\xE0o trong th\\u01B0 vi\\u1EC7n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col h-[600px]  lg:flex-row gap-4\",\n          style: {\n            minHeight: \"600px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 border h-full rounded-md overflow-hidden flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-100 px-4 py-2 border-b\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold\",\n                children: \"So\\u1EA1n th\\u1EA3o n\\u1ED9i dung\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 h-full overflow-auto\",\n              ref: editorRef,\n              children: /*#__PURE__*/_jsxDEV(MarkdownEditor, {\n                value: formData.content,\n                setValue: val => setFormData({\n                  ...formData,\n                  content: val\n                }),\n                height: \"100%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 bg-white border rounded-md overflow-hidden flex flex-col\",\n            style: {\n              minHeight: \"600px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-100 px-4 py-2 border-b\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-slate-700\",\n                children: \"\\uD83D\\uDC41\\uFE0F Xem tr\\u01B0\\u1EDBc b\\xE0i vi\\u1EBFt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 custom-markdown-preview overflow-auto flex-1\",\n              ref: previewRef,\n              children: /*#__PURE__*/_jsxDEV(MarkdownPreview, {\n                source: processContentForPreview,\n                remarkPlugins: [remarkMath],\n                rehypePlugins: [[rehypeKatex, {\n                  strict: false,\n                  throwOnError: false,\n                  errorColor: '#cc0000'\n                }]],\n                className: \"markdown-preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-sky-600 text-white px-6 py-2 rounded hover:bg-sky-700\",\n            disabled: isUploading || isSubmitting,\n            children: isSubmitting ? \"Đang lưu...\" : isEditing ? \"Cập nhật\" : \"Đăng bài\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => navigate(\"/admin/article-management\"),\n            className: \"bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600\",\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 270,\n    columnNumber: 9\n  }, this);\n};\n_s(ArticlePostPage, \"IzZ91SRtmBy+MjZ3g40v+3WnKvM=\", false, function () {\n  return [useDispatch, useNavigate, useParams, useSelector, useSelector, useSelector];\n});\n_c = ArticlePostPage;\nexport default ArticlePostPage;\nvar _c;\n$RefreshReg$(_c, \"ArticlePostPage\");", "map": {"version": 3, "names": ["useEffect", "useState", "useRef", "useCallback", "useMemo", "AdminLayout", "MarkdownEditor", "MarkdownPreview", "remarkMath", "rehypeKatex", "useDispatch", "useSelector", "postArticle", "fetchArticleById", "putArticle", "fetchImages", "postImage", "deleteImage", "setSuccessMessage", "setErrorMessage", "DropMenuBarAdmin", "fetchCodesByType", "useParams", "useNavigate", "jsxDEV", "_jsxDEV", "ArticlePostPage", "_s", "dispatch", "navigate", "id", "images", "state", "article", "articles", "codes", "isEditing", "setIsEditing", "articleId", "setArticleId", "formData", "setFormData", "name", "title", "type", "class", "chapter", "author", "content", "isUploading", "setIsUploading", "isSubmitting", "setIsSubmitting", "showImageGallery", "setShowImageGallery", "editor<PERSON><PERSON>", "previewRef", "optionChapter", "setOptionChapter", "processContentForPreview", "processedContent", "replace", "Array", "isArray", "trim", "filter", "code", "startsWith", "console", "log", "scrollPreviewToBottom", "current", "previewContainer", "scrollTop", "scrollHeight", "timer", "setTimeout", "clearTimeout", "handleChange", "e", "value", "target", "validateForm", "requiredFields", "missingFields", "for<PERSON>ach", "field", "push", "length", "fieldNames", "missingFieldNames", "map", "concat", "join", "filter<PERSON>on<PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "filteredData", "Object", "keys", "key", "undefined", "handleSubmit", "preventDefault", "unwrap", "error", "message", "handleImageUpload", "file", "files", "result", "image", "folder", "insertImageToEditor", "imageUrl", "imageMarkdown", "currentC<PERSON>nt", "newContent", "handleSelectImage", "handleDeleteImage", "window", "confirm", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "placeholder", "onChange", "required", "selectedOption", "option", "options", "accept", "disabled", "htmlFor", "index", "src", "alt", "style", "minHeight", "ref", "setValue", "val", "height", "source", "remarkPlugins", "rehypePlugins", "strict", "throwOnError", "errorColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/ArticlePostPage.jsx"], "sourcesContent": ["import { useEffect, useState, useRef, useCallback, useMemo } from \"react\";\r\nimport AdminLayout from \"../../layouts/AdminLayout\";\r\nimport MarkdownEditor from \"../../components/latex/MarkDownEditer\";\r\nimport MarkdownPreview from \"@uiw/react-markdown-preview\";\r\nimport remarkMath from \"remark-math\";\r\nimport rehypeKatex from \"rehype-katex\";\r\nimport \"@uiw/react-markdown-preview/markdown.css\";\r\nimport \"katex/dist/katex.min.css\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { postArticle, fetchArticleById, putArticle } from \"../../features/article/articleSlice\";\r\nimport { fetchImages, postImage, deleteImage } from \"../../features/image/imageSlice\";\r\nimport { setSuccessMessage, setErrorMessage } from \"../../features/state/stateApiSlice\";\r\nimport \"../../styles/markdown-preview.css\";\r\nimport DropMenuBarAdmin from \"../../components/dropMenu/OptionBarAdmin\";\r\nimport { fetchCodesByType } from \"../../features/code/codeSlice\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\n\r\nconst ArticlePostPage = () => {\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const { id } = useParams();\r\n    const { images } = useSelector(state => state.images);\r\n    const { article } = useSelector(state => state.articles);\r\n    const { codes } = useSelector(state => state.codes);\r\n    const [isEditing, setIsEditing] = useState(false);\r\n    const [articleId, setArticleId] = useState(null);\r\n    const [formData, setFormData] = useState({\r\n        name: \"\",\r\n        title: \"\",\r\n        type: \"\",\r\n        class: \"\",\r\n        chapter: \"\",\r\n        author: \"\",\r\n        content: \"\"\r\n    });\r\n    const [isUploading, setIsUploading] = useState(false);\r\n    const [isSubmitting, setIsSubmitting] = useState(false);\r\n    // We don't need selectedImage state as we're directly inserting images\r\n    const [showImageGallery, setShowImageGallery] = useState(false);\r\n    const editorRef = useRef(null);\r\n    const previewRef = useRef(null);\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n\r\n    // Function to replace LaTeX delimiters for preview\r\n    const processContentForPreview = useMemo(() => {\r\n        if (!formData.content) return \"\";\r\n\r\n        // Replace \\( \\) with $ $ for inline math\r\n        // Replace \\[ \\] with $$ $$ for block math\r\n        let processedContent = formData.content\r\n            .replace(/\\\\\\(/g, \"$\")\r\n            .replace(/\\\\\\)/g, \"$\")\r\n            .replace(/\\\\\\[/g, \"$$\")\r\n            .replace(/\\\\\\]/g, \"$$\");\r\n\r\n        return processedContent;\r\n    }, [formData.content]);\r\n\r\n    // Fetch images from the article folder when component mounts\r\n    useEffect(() => {\r\n        dispatch(fetchImages(\"article\"));\r\n        dispatch(fetchCodesByType([\"chapter\", \"grade\", \"article type\"]));\r\n    }, [dispatch]);\r\n\r\n    // Filter chapter options based on selected class\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (formData.class && formData.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(formData.class))\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"]);\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, formData.class]);\r\n\r\n    // Check if we're in edit mode based on URL parameter\r\n    useEffect(() => {\r\n        if (id) {\r\n            setIsEditing(true);\r\n            setArticleId(id);\r\n        }\r\n    }, [id]);\r\n\r\n    // If editing an existing article, populate the form with article data\r\n    useEffect(() => {\r\n        if (articleId) {\r\n            dispatch(fetchArticleById(articleId));\r\n        }\r\n    }, [articleId, dispatch]);\r\n\r\n    useEffect(() => {\r\n        if (article && isEditing) {\r\n            // Make sure we're setting the exact values from the article\r\n            // This is important for the dropdown menus to work correctly\r\n            setFormData({\r\n                name: article.name || \"\",\r\n                title: article.title || \"\",\r\n                type: article.type || \"\",\r\n                class: article.class || \"\",\r\n                chapter: article.chapter || \"\",\r\n                author: article.author || \"\",\r\n                content: article.content || \"\"\r\n            });\r\n\r\n            // Log the values to help with debugging\r\n            console.log(\"Article data loaded:\", {\r\n                type: article.type,\r\n                class: article.class,\r\n                chapter: article.chapter\r\n            });\r\n        }\r\n    }, [article, isEditing]);\r\n\r\n    // Function to scroll preview to bottom\r\n    const scrollPreviewToBottom = useCallback(() => {\r\n        if (previewRef.current) {\r\n            const previewContainer = previewRef.current;\r\n            previewContainer.scrollTop = previewContainer.scrollHeight;\r\n        }\r\n    }, []);\r\n\r\n    // Scroll preview to bottom when content changes\r\n    useEffect(() => {\r\n        // Use a short delay to ensure the content has been rendered\r\n        const timer = setTimeout(scrollPreviewToBottom, 100);\r\n        return () => clearTimeout(timer);\r\n    }, [formData.content, scrollPreviewToBottom]);\r\n\r\n    const handleChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setFormData({ ...formData, [name]: value });\r\n    };\r\n\r\n    // Validate required fields\r\n    const validateForm = () => {\r\n        const requiredFields = ['name', 'type', 'title', 'content', 'author'];\r\n        const missingFields = [];\r\n\r\n        requiredFields.forEach(field => {\r\n            if (!formData[field] || formData[field].trim() === '') {\r\n                missingFields.push(field);\r\n            }\r\n        });\r\n\r\n        if (missingFields.length > 0) {\r\n            const fieldNames = {\r\n                name: 'Tên bài viết',\r\n                type: 'Loại bài viết',\r\n                title: 'Tiêu đề',\r\n                content: 'Nội dung',\r\n                author: 'Tác giả'\r\n            };\r\n\r\n            const missingFieldNames = missingFields.map(field => fieldNames[field]);\r\n            dispatch(setErrorMessage(`Vui lòng điền đầy đủ các trường: ${missingFieldNames.join(', ')}`));\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    };\r\n\r\n    // Filter out null, undefined, or empty string values from formData\r\n    const filterNonNullValues = (data) => {\r\n        const filteredData = {};\r\n        Object.keys(data).forEach(key => {\r\n            // Keep the value if it's not null, undefined, or an empty string\r\n            if (data[key] !== null && data[key] !== undefined && data[key] !== '') {\r\n                filteredData[key] = data[key];\r\n            }\r\n        });\r\n        return filteredData;\r\n    };\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n\r\n        // Validate form before submission\r\n        if (!validateForm()) {\r\n            return;\r\n        }\r\n\r\n        setIsSubmitting(true);\r\n        try {\r\n            // Filter out null values before submitting\r\n            const filteredData = filterNonNullValues(formData);\r\n\r\n            // Log the filtered data for debugging\r\n            console.log('Filtered form data:', filteredData);\r\n\r\n            if (isEditing && articleId) {\r\n                await dispatch(putArticle({ articleId, data: filteredData })).unwrap();\r\n                dispatch(setSuccessMessage(\"Cập nhật bài viết thành công\"));\r\n                navigate(\"/admin/article-management\");\r\n            } else {\r\n                await dispatch(postArticle(filteredData)).unwrap();\r\n                dispatch(setSuccessMessage(\"Đăng bài viết thành công\"));\r\n                navigate(\"/admin/article-management\");\r\n            }\r\n        } catch (error) {\r\n            dispatch(setErrorMessage(error.message || \"Có lỗi xảy ra khi lưu bài viết\"));\r\n        } finally {\r\n            setIsSubmitting(false);\r\n        }\r\n    };\r\n\r\n    // Handle image upload\r\n    const handleImageUpload = async (e) => {\r\n        const file = e.target.files[0];\r\n        if (!file) return;\r\n\r\n        // Check if file is an image\r\n        if (!file.type.startsWith('image/')) {\r\n            dispatch(setErrorMessage(\"Vui lòng chọn file hình ảnh\"));\r\n            return;\r\n        }\r\n\r\n        setIsUploading(true);\r\n        try {\r\n            const result = await dispatch(postImage({ image: file, folder: \"article\" })).unwrap();\r\n            if (result && result.file) {\r\n                insertImageToEditor(result.file);\r\n                dispatch(setSuccessMessage(\"Tải lên hình ảnh thành công\"));\r\n                // dispatch(fetchImages(\"article\"));\r\n            }\r\n        } catch (error) {\r\n            dispatch(setErrorMessage(\"Lỗi khi tải lên hình ảnh: \" + error.message));\r\n        } finally {\r\n            setIsUploading(false);\r\n        }\r\n    };\r\n\r\n    // Insert image URL to editor content at cursor position or at the end\r\n    const insertImageToEditor = (imageUrl) => {\r\n        const imageMarkdown = `![image](${imageUrl})`;\r\n        const currentContent = formData.content;\r\n\r\n        // Insert at the end if we don't have cursor position info\r\n        const newContent = currentContent + \"\\n\" + imageMarkdown + \"\\n\";\r\n        setFormData({ ...formData, content: newContent });\r\n\r\n        // Scroll preview to bottom after a short delay to allow rendering\r\n        setTimeout(scrollPreviewToBottom, 100);\r\n    };\r\n\r\n    // Handle selecting an image from the gallery\r\n    const handleSelectImage = (imageUrl) => {\r\n        insertImageToEditor(imageUrl);\r\n        setShowImageGallery(false);\r\n    };\r\n\r\n    // Handle deleting an image\r\n    const handleDeleteImage = async (imageUrl) => {\r\n        if (window.confirm(\"Bạn có chắc chắn muốn xóa hình ảnh này?\")) {\r\n            try {\r\n                await dispatch(deleteImage(imageUrl)).unwrap();\r\n                dispatch(setSuccessMessage(\"Xóa hình ảnh thành công\"));\r\n                // Refresh the image list\r\n                // dispatch(fetchImages(\"article\"));\r\n            } catch (error) {\r\n                dispatch(setErrorMessage(\"Lỗi khi xóa hình ảnh: \" + error.message));\r\n            }\r\n        }\r\n    };\r\n\r\n    return (\r\n        <AdminLayout>\r\n            <div className=\"p-4 space-y-4\">\r\n                <div className=\"flex justify-between items-center\">\r\n                    <h2 className=\"text-2xl font-bold text-slate-700\">\r\n                        {isEditing ? \"✏️ Chỉnh sửa bài viết\" : \"📝 Đăng bài viết lý thuyết\"}\r\n                    </h2>\r\n                    {isEditing && (\r\n                        <button\r\n                            onClick={() => {\r\n                                setIsEditing(false);\r\n                                setArticleId(null);\r\n                                setFormData({\r\n                                    name: \"\",\r\n                                    title: \"\",\r\n                                    type: \"\",\r\n                                    class: \"\",\r\n                                    chapter: \"\",\r\n                                    author: \"\",\r\n                                    content: \"\"\r\n                                });\r\n                            }}\r\n                            className=\"bg-gray-500 text-white px-4 py-1 rounded hover:bg-gray-600\"\r\n                        >\r\n                            Tạo mới\r\n                        </button>\r\n                    )}\r\n                </div>\r\n\r\n                <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        <input\r\n                            type=\"text\"\r\n                            name=\"name\"\r\n                            placeholder=\"Tên bài viết\"\r\n                            value={formData.name}\r\n                            onChange={handleChange}\r\n                            className=\"w-full border rounded px-3 py-2\"\r\n                            required\r\n                        />\r\n                        <input\r\n                            type=\"text\"\r\n                            name=\"title\"\r\n                            placeholder=\"Tiêu đề bài viết\"\r\n                            value={formData.title}\r\n                            onChange={handleChange}\r\n                            className=\"w-full border rounded px-3 py-2\"\r\n                            required\r\n                        />\r\n                    </div>\r\n\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                        <div className=\"flex flex-col gap-2\">\r\n                            <label className=\"text-gray-700 font-medium\">Loại bài viết</label>\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={formData.type}\r\n                                onChange={(option) => setFormData({ ...formData, type: option })}\r\n                                options={Array.isArray(codes[\"article type\"]) ? codes[\"article type\"] : []}\r\n                                placeholder=\"Chọn loại bài viết\"\r\n                            />\r\n                        </div>\r\n                        <div className=\"flex flex-col gap-2\">\r\n                            <label className=\"text-gray-700 font-medium\">Lớp</label>\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={formData.class}\r\n                                onChange={(option) => setFormData({ ...formData, class: option })}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                placeholder=\"Chọn lớp\"\r\n                            />\r\n                        </div>\r\n                        <div className=\"flex flex-col gap-2\">\r\n                            <label className=\"text-gray-700 font-medium\">Chương</label>\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={formData.chapter}\r\n                                onChange={(option) => setFormData({ ...formData, chapter: option })}\r\n                                options={optionChapter}\r\n                                placeholder=\"Chọn chương\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                    <input\r\n                        type=\"text\"\r\n                        name=\"author\"\r\n                        placeholder=\"Tác giả\"\r\n                        value={formData.author}\r\n                        onChange={handleChange}\r\n                        className=\"w-full border rounded px-3 py-2\"\r\n                    />\r\n\r\n                    {/* Image Management Controls */}\r\n                    <div className=\"flex flex-wrap gap-2 items-center\">\r\n                        <input\r\n                            type=\"file\"\r\n                            accept=\"image/*\"\r\n                            id=\"image-upload\"\r\n                            className=\"hidden\"\r\n                            onChange={handleImageUpload}\r\n                            disabled={isUploading}\r\n                        />\r\n                        <label\r\n                            htmlFor=\"image-upload\"\r\n                            className={`bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded cursor-pointer ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}\r\n                        >\r\n                            {isUploading ? \"Đang tải lên...\" : \"Tải lên hình ảnh\"}\r\n                        </label>\r\n\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={() => setShowImageGallery(!showImageGallery)}\r\n                            className=\"bg-indigo-500 hover:bg-indigo-600 text-white px-3 py-1 rounded\"\r\n                        >\r\n                            {showImageGallery ? \"Ẩn thư viện ảnh\" : \"Hiện thư viện ảnh\"}\r\n                        </button>\r\n                    </div>\r\n\r\n                    {/* Image Gallery */}\r\n                    {showImageGallery && (\r\n                        <div className=\"border rounded-md p-4 bg-gray-50\">\r\n                            <h3 className=\"font-semibold mb-2\">Thư viện ảnh</h3>\r\n                            {images[\"article\"] && images[\"article\"].length > 0 ? (\r\n                                <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4\">\r\n                                    {images[\"article\"].map((image, index) => (\r\n                                        <div key={index} className=\"relative group\">\r\n                                            <img\r\n                                                src={image}\r\n                                                alt={`Gallery image ${index}`}\r\n                                                className=\"w-full h-24 object-cover rounded border cursor-pointer hover:opacity-90\"\r\n                                                onClick={() => handleSelectImage(image)}\r\n                                            />\r\n                                            <button\r\n                                                type=\"button\"\r\n                                                onClick={() => handleDeleteImage(image)}\r\n                                                className=\"absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                                            >\r\n                                                ×\r\n                                            </button>\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            ) : (\r\n                                <p className=\"text-gray-500\">Không có hình ảnh nào trong thư viện</p>\r\n                            )}\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* Split View: Editor and Preview */}\r\n                    <div className=\"flex flex-col h-[600px]  lg:flex-row gap-4\" style={{ minHeight: \"600px\" }}>\r\n                        {/* Editor Section */}\r\n                        <div className=\"flex-1 border h-full rounded-md overflow-hidden flex flex-col\">\r\n                            <div className=\"bg-gray-100 px-4 py-2 border-b\">\r\n                                <h3 className=\"font-semibold\">Soạn thảo nội dung</h3>\r\n                            </div>\r\n                            <div className=\"flex-1 h-full overflow-auto\" ref={editorRef}>\r\n                                <MarkdownEditor\r\n                                    value={formData.content}\r\n                                    setValue={(val) => setFormData({ ...formData, content: val })}\r\n                                    height=\"100%\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Preview Section */}\r\n                        <div className=\"flex-1 bg-white border rounded-md overflow-hidden flex flex-col\" style={{ minHeight: \"600px\" }}>\r\n                            <div className=\"bg-gray-100 px-4 py-2 border-b\">\r\n                                <h3 className=\"font-semibold text-slate-700\">👁️ Xem trước bài viết</h3>\r\n                            </div>\r\n                            <div\r\n                                className=\"p-4 custom-markdown-preview overflow-auto flex-1\"\r\n                                ref={previewRef}\r\n                            >\r\n                                <MarkdownPreview\r\n                                    source={processContentForPreview}\r\n                                    remarkPlugins={[remarkMath]}\r\n                                    rehypePlugins={[\r\n                                        [rehypeKatex, {\r\n                                            strict: false,\r\n                                            throwOnError: false,\r\n                                            errorColor: '#cc0000'\r\n                                        }]\r\n                                    ]}\r\n                                    className=\"markdown-preview\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"flex justify-end gap-4\">\r\n                        <button\r\n                            type=\"submit\"\r\n                            className=\"bg-sky-600 text-white px-6 py-2 rounded hover:bg-sky-700\"\r\n                            disabled={isUploading || isSubmitting}\r\n                        >\r\n                            {isSubmitting ? \"Đang lưu...\" : isEditing ? \"Cập nhật\" : \"Đăng bài\"}\r\n                        </button>\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={() => navigate(\"/admin/article-management\")}\r\n                            className=\"bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600\"\r\n                        >\r\n                            Hủy\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </AdminLayout>\r\n    );\r\n};\r\n\r\nexport default ArticlePostPage;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACzE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,0CAA0C;AACjD,OAAO,0BAA0B;AACjC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,qCAAqC;AAC/F,SAASC,WAAW,EAAEC,SAAS,EAAEC,WAAW,QAAQ,iCAAiC;AACrF,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,oCAAoC;AACvF,OAAO,mCAAmC;AAC1C,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEO;EAAG,CAAC,GAAGR,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAES;EAAO,CAAC,GAAGpB,WAAW,CAACqB,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC;EACrD,MAAM;IAAEE;EAAQ,CAAC,GAAGtB,WAAW,CAACqB,KAAK,IAAIA,KAAK,CAACE,QAAQ,CAAC;EACxD,MAAM;IAAEC;EAAM,CAAC,GAAGxB,WAAW,CAACqB,KAAK,IAAIA,KAAK,CAACG,KAAK,CAAC;EACnD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC;IACrCyC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD;EACA,MAAM,CAACoD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMsD,SAAS,GAAGrD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMsD,UAAU,GAAGtD,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM0D,wBAAwB,GAAGvD,OAAO,CAAC,MAAM;IAC3C,IAAI,CAACoC,QAAQ,CAACQ,OAAO,EAAE,OAAO,EAAE;;IAEhC;IACA;IACA,IAAIY,gBAAgB,GAAGpB,QAAQ,CAACQ,OAAO,CAClCa,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;IAE3B,OAAOD,gBAAgB;EAC3B,CAAC,EAAE,CAACpB,QAAQ,CAACQ,OAAO,CAAC,CAAC;;EAEtB;EACAhD,SAAS,CAAC,MAAM;IACZ4B,QAAQ,CAACb,WAAW,CAAC,SAAS,CAAC,CAAC;IAChCa,QAAQ,CAACP,gBAAgB,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;EACpE,CAAC,EAAE,CAACO,QAAQ,CAAC,CAAC;;EAEd;EACA5B,SAAS,CAAC,MAAM;IACZ,IAAI8D,KAAK,CAACC,OAAO,CAAC5B,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAIK,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACK,KAAK,CAACmB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAChDN,gBAAgB,CACZvB,KAAK,CAAC,SAAS,CAAC,CAAC8B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC3B,QAAQ,CAACK,KAAK,CAAC,CAC1E,CAAC;MACL,CAAC,MAAM;QACHa,gBAAgB,CAACvB,KAAK,CAAC,SAAS,CAAC,CAAC;MACtC;IACJ,CAAC,MAAM;MACHuB,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACvB,KAAK,EAAEK,QAAQ,CAACK,KAAK,CAAC,CAAC;;EAE3B;EACA7C,SAAS,CAAC,MAAM;IACZ,IAAI8B,EAAE,EAAE;MACJO,YAAY,CAAC,IAAI,CAAC;MAClBE,YAAY,CAACT,EAAE,CAAC;IACpB;EACJ,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;;EAER;EACA9B,SAAS,CAAC,MAAM;IACZ,IAAIsC,SAAS,EAAE;MACXV,QAAQ,CAACf,gBAAgB,CAACyB,SAAS,CAAC,CAAC;IACzC;EACJ,CAAC,EAAE,CAACA,SAAS,EAAEV,QAAQ,CAAC,CAAC;EAEzB5B,SAAS,CAAC,MAAM;IACZ,IAAIiC,OAAO,IAAIG,SAAS,EAAE;MACtB;MACA;MACAK,WAAW,CAAC;QACRC,IAAI,EAAET,OAAO,CAACS,IAAI,IAAI,EAAE;QACxBC,KAAK,EAAEV,OAAO,CAACU,KAAK,IAAI,EAAE;QAC1BC,IAAI,EAAEX,OAAO,CAACW,IAAI,IAAI,EAAE;QACxBC,KAAK,EAAEZ,OAAO,CAACY,KAAK,IAAI,EAAE;QAC1BC,OAAO,EAAEb,OAAO,CAACa,OAAO,IAAI,EAAE;QAC9BC,MAAM,EAAEd,OAAO,CAACc,MAAM,IAAI,EAAE;QAC5BC,OAAO,EAAEf,OAAO,CAACe,OAAO,IAAI;MAChC,CAAC,CAAC;;MAEF;MACAoB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAChCzB,IAAI,EAAEX,OAAO,CAACW,IAAI;QAClBC,KAAK,EAAEZ,OAAO,CAACY,KAAK;QACpBC,OAAO,EAAEb,OAAO,CAACa;MACrB,CAAC,CAAC;IACN;EACJ,CAAC,EAAE,CAACb,OAAO,EAAEG,SAAS,CAAC,CAAC;;EAExB;EACA,MAAMkC,qBAAqB,GAAGnE,WAAW,CAAC,MAAM;IAC5C,IAAIqD,UAAU,CAACe,OAAO,EAAE;MACpB,MAAMC,gBAAgB,GAAGhB,UAAU,CAACe,OAAO;MAC3CC,gBAAgB,CAACC,SAAS,GAAGD,gBAAgB,CAACE,YAAY;IAC9D;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1E,SAAS,CAAC,MAAM;IACZ;IACA,MAAM2E,KAAK,GAAGC,UAAU,CAACN,qBAAqB,EAAE,GAAG,CAAC;IACpD,OAAO,MAAMO,YAAY,CAACF,KAAK,CAAC;EACpC,CAAC,EAAE,CAACnC,QAAQ,CAACQ,OAAO,EAAEsB,qBAAqB,CAAC,CAAC;EAE7C,MAAMQ,YAAY,GAAIC,CAAC,IAAK;IACxB,MAAM;MAAErC,IAAI;MAAEsC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCxC,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGsC;IAAM,CAAC,CAAC;EAC/C,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,cAAc,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;IACrE,MAAMC,aAAa,GAAG,EAAE;IAExBD,cAAc,CAACE,OAAO,CAACC,KAAK,IAAI;MAC5B,IAAI,CAAC9C,QAAQ,CAAC8C,KAAK,CAAC,IAAI9C,QAAQ,CAAC8C,KAAK,CAAC,CAACtB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACnDoB,aAAa,CAACG,IAAI,CAACD,KAAK,CAAC;MAC7B;IACJ,CAAC,CAAC;IAEF,IAAIF,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMC,UAAU,GAAG;QACf/C,IAAI,EAAE,cAAc;QACpBE,IAAI,EAAE,eAAe;QACrBD,KAAK,EAAE,SAAS;QAChBK,OAAO,EAAE,UAAU;QACnBD,MAAM,EAAE;MACZ,CAAC;MAED,MAAM2C,iBAAiB,GAAGN,aAAa,CAACO,GAAG,CAACL,KAAK,IAAIG,UAAU,CAACH,KAAK,CAAC,CAAC;MACvE1D,QAAQ,CAACT,eAAe,mFAAAyE,MAAA,CAAqCF,iBAAiB,CAACG,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;MAC7F,OAAO,KAAK;IAChB;IAEA,OAAO,IAAI;EACf,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IAClC,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvBC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACV,OAAO,CAACc,GAAG,IAAI;MAC7B;MACA,IAAIJ,IAAI,CAACI,GAAG,CAAC,KAAK,IAAI,IAAIJ,IAAI,CAACI,GAAG,CAAC,KAAKC,SAAS,IAAIL,IAAI,CAACI,GAAG,CAAC,KAAK,EAAE,EAAE;QACnEH,YAAY,CAACG,GAAG,CAAC,GAAGJ,IAAI,CAACI,GAAG,CAAC;MACjC;IACJ,CAAC,CAAC;IACF,OAAOH,YAAY;EACvB,CAAC;EAED,MAAMK,YAAY,GAAG,MAAOtB,CAAC,IAAK;IAC9BA,CAAC,CAACuB,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAACpB,YAAY,CAAC,CAAC,EAAE;MACjB;IACJ;IAEA9B,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACA;MACA,MAAM4C,YAAY,GAAGF,mBAAmB,CAACtD,QAAQ,CAAC;;MAElD;MACA4B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2B,YAAY,CAAC;MAEhD,IAAI5D,SAAS,IAAIE,SAAS,EAAE;QACxB,MAAMV,QAAQ,CAACd,UAAU,CAAC;UAAEwB,SAAS;UAAEyD,IAAI,EAAEC;QAAa,CAAC,CAAC,CAAC,CAACO,MAAM,CAAC,CAAC;QACtE3E,QAAQ,CAACV,iBAAiB,CAAC,8BAA8B,CAAC,CAAC;QAC3DW,QAAQ,CAAC,2BAA2B,CAAC;MACzC,CAAC,MAAM;QACH,MAAMD,QAAQ,CAAChB,WAAW,CAACoF,YAAY,CAAC,CAAC,CAACO,MAAM,CAAC,CAAC;QAClD3E,QAAQ,CAACV,iBAAiB,CAAC,0BAA0B,CAAC,CAAC;QACvDW,QAAQ,CAAC,2BAA2B,CAAC;MACzC;IACJ,CAAC,CAAC,OAAO2E,KAAK,EAAE;MACZ5E,QAAQ,CAACT,eAAe,CAACqF,KAAK,CAACC,OAAO,IAAI,gCAAgC,CAAC,CAAC;IAChF,CAAC,SAAS;MACNrD,eAAe,CAAC,KAAK,CAAC;IAC1B;EACJ,CAAC;;EAED;EACA,MAAMsD,iBAAiB,GAAG,MAAO3B,CAAC,IAAK;IACnC,MAAM4B,IAAI,GAAG5B,CAAC,CAACE,MAAM,CAAC2B,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACD,IAAI,EAAE;;IAEX;IACA,IAAI,CAACA,IAAI,CAAC/D,IAAI,CAACuB,UAAU,CAAC,QAAQ,CAAC,EAAE;MACjCvC,QAAQ,CAACT,eAAe,CAAC,6BAA6B,CAAC,CAAC;MACxD;IACJ;IAEA+B,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACA,MAAM2D,MAAM,GAAG,MAAMjF,QAAQ,CAACZ,SAAS,CAAC;QAAE8F,KAAK,EAAEH,IAAI;QAAEI,MAAM,EAAE;MAAU,CAAC,CAAC,CAAC,CAACR,MAAM,CAAC,CAAC;MACrF,IAAIM,MAAM,IAAIA,MAAM,CAACF,IAAI,EAAE;QACvBK,mBAAmB,CAACH,MAAM,CAACF,IAAI,CAAC;QAChC/E,QAAQ,CAACV,iBAAiB,CAAC,6BAA6B,CAAC,CAAC;QAC1D;MACJ;IACJ,CAAC,CAAC,OAAOsF,KAAK,EAAE;MACZ5E,QAAQ,CAACT,eAAe,CAAC,4BAA4B,GAAGqF,KAAK,CAACC,OAAO,CAAC,CAAC;IAC3E,CAAC,SAAS;MACNvD,cAAc,CAAC,KAAK,CAAC;IACzB;EACJ,CAAC;;EAED;EACA,MAAM8D,mBAAmB,GAAIC,QAAQ,IAAK;IACtC,MAAMC,aAAa,eAAAtB,MAAA,CAAeqB,QAAQ,MAAG;IAC7C,MAAME,cAAc,GAAG3E,QAAQ,CAACQ,OAAO;;IAEvC;IACA,MAAMoE,UAAU,GAAGD,cAAc,GAAG,IAAI,GAAGD,aAAa,GAAG,IAAI;IAC/DzE,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEQ,OAAO,EAAEoE;IAAW,CAAC,CAAC;;IAEjD;IACAxC,UAAU,CAACN,qBAAqB,EAAE,GAAG,CAAC;EAC1C,CAAC;;EAED;EACA,MAAM+C,iBAAiB,GAAIJ,QAAQ,IAAK;IACpCD,mBAAmB,CAACC,QAAQ,CAAC;IAC7B3D,mBAAmB,CAAC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMgE,iBAAiB,GAAG,MAAOL,QAAQ,IAAK;IAC1C,IAAIM,MAAM,CAACC,OAAO,CAAC,yCAAyC,CAAC,EAAE;MAC3D,IAAI;QACA,MAAM5F,QAAQ,CAACX,WAAW,CAACgG,QAAQ,CAAC,CAAC,CAACV,MAAM,CAAC,CAAC;QAC9C3E,QAAQ,CAACV,iBAAiB,CAAC,yBAAyB,CAAC,CAAC;QACtD;QACA;MACJ,CAAC,CAAC,OAAOsF,KAAK,EAAE;QACZ5E,QAAQ,CAACT,eAAe,CAAC,wBAAwB,GAAGqF,KAAK,CAACC,OAAO,CAAC,CAAC;MACvE;IACJ;EACJ,CAAC;EAED,oBACIhF,OAAA,CAACpB,WAAW;IAAAoH,QAAA,eACRhG,OAAA;MAAKiG,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAC1BhG,OAAA;QAAKiG,SAAS,EAAC,mCAAmC;QAAAD,QAAA,gBAC9ChG,OAAA;UAAIiG,SAAS,EAAC,mCAAmC;UAAAD,QAAA,EAC5CrF,SAAS,GAAG,uBAAuB,GAAG;QAA4B;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,EACJ1F,SAAS,iBACNX,OAAA;UACIsG,OAAO,EAAEA,CAAA,KAAM;YACX1F,YAAY,CAAC,KAAK,CAAC;YACnBE,YAAY,CAAC,IAAI,CAAC;YAClBE,WAAW,CAAC;cACRC,IAAI,EAAE,EAAE;cACRC,KAAK,EAAE,EAAE;cACTC,IAAI,EAAE,EAAE;cACRC,KAAK,EAAE,EAAE;cACTC,OAAO,EAAE,EAAE;cACXC,MAAM,EAAE,EAAE;cACVC,OAAO,EAAE;YACb,CAAC,CAAC;UACN,CAAE;UACF0E,SAAS,EAAC,4DAA4D;UAAAD,QAAA,EACzE;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAENrG,OAAA;QAAMuG,QAAQ,EAAE3B,YAAa;QAACqB,SAAS,EAAC,WAAW;QAAAD,QAAA,gBAC/ChG,OAAA;UAAKiG,SAAS,EAAC,uCAAuC;UAAAD,QAAA,gBAClDhG,OAAA;YACImB,IAAI,EAAC,MAAM;YACXF,IAAI,EAAC,MAAM;YACXuF,WAAW,EAAC,yBAAc;YAC1BjD,KAAK,EAAExC,QAAQ,CAACE,IAAK;YACrBwF,QAAQ,EAAEpD,YAAa;YACvB4C,SAAS,EAAC,iCAAiC;YAC3CS,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFrG,OAAA;YACImB,IAAI,EAAC,MAAM;YACXF,IAAI,EAAC,OAAO;YACZuF,WAAW,EAAC,uCAAkB;YAC9BjD,KAAK,EAAExC,QAAQ,CAACG,KAAM;YACtBuF,QAAQ,EAAEpD,YAAa;YACvB4C,SAAS,EAAC,iCAAiC;YAC3CS,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrG,OAAA;UAAKiG,SAAS,EAAC,uCAAuC;UAAAD,QAAA,gBAClDhG,OAAA;YAAKiG,SAAS,EAAC,qBAAqB;YAAAD,QAAA,gBAChChG,OAAA;cAAOiG,SAAS,EAAC,2BAA2B;cAAAD,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClErG,OAAA,CAACL,gBAAgB;cACbgH,cAAc,EAAE5F,QAAQ,CAACI,IAAK;cAC9BsF,QAAQ,EAAGG,MAAM,IAAK5F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,IAAI,EAAEyF;cAAO,CAAC,CAAE;cACjEC,OAAO,EAAExE,KAAK,CAACC,OAAO,CAAC5B,KAAK,CAAC,cAAc,CAAC,CAAC,GAAGA,KAAK,CAAC,cAAc,CAAC,GAAG,EAAG;cAC3E8F,WAAW,EAAC;YAAoB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNrG,OAAA;YAAKiG,SAAS,EAAC,qBAAqB;YAAAD,QAAA,gBAChChG,OAAA;cAAOiG,SAAS,EAAC,2BAA2B;cAAAD,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxDrG,OAAA,CAACL,gBAAgB;cACbgH,cAAc,EAAE5F,QAAQ,CAACK,KAAM;cAC/BqF,QAAQ,EAAGG,MAAM,IAAK5F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,KAAK,EAAEwF;cAAO,CAAC,CAAE;cAClEC,OAAO,EAAExE,KAAK,CAACC,OAAO,CAAC5B,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;cAC7D8F,WAAW,EAAC;YAAU;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNrG,OAAA;YAAKiG,SAAS,EAAC,qBAAqB;YAAAD,QAAA,gBAChChG,OAAA;cAAOiG,SAAS,EAAC,2BAA2B;cAAAD,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DrG,OAAA,CAACL,gBAAgB;cACbgH,cAAc,EAAE5F,QAAQ,CAACM,OAAQ;cACjCoF,QAAQ,EAAGG,MAAM,IAAK5F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEM,OAAO,EAAEuF;cAAO,CAAC,CAAE;cACpEC,OAAO,EAAE7E,aAAc;cACvBwE,WAAW,EAAC;YAAa;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrG,OAAA;UACImB,IAAI,EAAC,MAAM;UACXF,IAAI,EAAC,QAAQ;UACbuF,WAAW,EAAC,iBAAS;UACrBjD,KAAK,EAAExC,QAAQ,CAACO,MAAO;UACvBmF,QAAQ,EAAEpD,YAAa;UACvB4C,SAAS,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eAGFrG,OAAA;UAAKiG,SAAS,EAAC,mCAAmC;UAAAD,QAAA,gBAC9ChG,OAAA;YACImB,IAAI,EAAC,MAAM;YACX2F,MAAM,EAAC,SAAS;YAChBzG,EAAE,EAAC,cAAc;YACjB4F,SAAS,EAAC,QAAQ;YAClBQ,QAAQ,EAAExB,iBAAkB;YAC5B8B,QAAQ,EAAEvF;UAAY;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACFrG,OAAA;YACIgH,OAAO,EAAC,cAAc;YACtBf,SAAS,mFAAA9B,MAAA,CAAmF3C,WAAW,GAAG,+BAA+B,GAAG,EAAE,CAAG;YAAAwE,QAAA,EAEhJxE,WAAW,GAAG,iBAAiB,GAAG;UAAkB;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAERrG,OAAA;YACImB,IAAI,EAAC,QAAQ;YACbmF,OAAO,EAAEA,CAAA,KAAMzE,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtDqE,SAAS,EAAC,gEAAgE;YAAAD,QAAA,EAEzEpE,gBAAgB,GAAG,iBAAiB,GAAG;UAAmB;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGLzE,gBAAgB,iBACb5B,OAAA;UAAKiG,SAAS,EAAC,kCAAkC;UAAAD,QAAA,gBAC7ChG,OAAA;YAAIiG,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnD/F,MAAM,CAAC,SAAS,CAAC,IAAIA,MAAM,CAAC,SAAS,CAAC,CAACyD,MAAM,GAAG,CAAC,gBAC9C/D,OAAA;YAAKiG,SAAS,EAAC,qEAAqE;YAAAD,QAAA,EAC/E1F,MAAM,CAAC,SAAS,CAAC,CAAC4D,GAAG,CAAC,CAACmB,KAAK,EAAE4B,KAAK,kBAChCjH,OAAA;cAAiBiG,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBACvChG,OAAA;gBACIkH,GAAG,EAAE7B,KAAM;gBACX8B,GAAG,mBAAAhD,MAAA,CAAmB8C,KAAK,CAAG;gBAC9BhB,SAAS,EAAC,yEAAyE;gBACnFK,OAAO,EAAEA,CAAA,KAAMV,iBAAiB,CAACP,KAAK;cAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACFrG,OAAA;gBACImB,IAAI,EAAC,QAAQ;gBACbmF,OAAO,EAAEA,CAAA,KAAMT,iBAAiB,CAACR,KAAK,CAAE;gBACxCY,SAAS,EAAC,yJAAyJ;gBAAAD,QAAA,EACtK;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAbHY,KAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,gBAENrG,OAAA;YAAGiG,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACvE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR,eAGDrG,OAAA;UAAKiG,SAAS,EAAC,4CAA4C;UAACmB,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAQ,CAAE;UAAArB,QAAA,gBAEtFhG,OAAA;YAAKiG,SAAS,EAAC,+DAA+D;YAAAD,QAAA,gBAC1EhG,OAAA;cAAKiG,SAAS,EAAC,gCAAgC;cAAAD,QAAA,eAC3ChG,OAAA;gBAAIiG,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNrG,OAAA;cAAKiG,SAAS,EAAC,6BAA6B;cAACqB,GAAG,EAAExF,SAAU;cAAAkE,QAAA,eACxDhG,OAAA,CAACnB,cAAc;gBACX0E,KAAK,EAAExC,QAAQ,CAACQ,OAAQ;gBACxBgG,QAAQ,EAAGC,GAAG,IAAKxG,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEQ,OAAO,EAAEiG;gBAAI,CAAC,CAAE;gBAC9DC,MAAM,EAAC;cAAM;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNrG,OAAA;YAAKiG,SAAS,EAAC,iEAAiE;YAACmB,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAQ,CAAE;YAAArB,QAAA,gBAC3GhG,OAAA;cAAKiG,SAAS,EAAC,gCAAgC;cAAAD,QAAA,eAC3ChG,OAAA;gBAAIiG,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNrG,OAAA;cACIiG,SAAS,EAAC,kDAAkD;cAC5DqB,GAAG,EAAEvF,UAAW;cAAAiE,QAAA,eAEhBhG,OAAA,CAAClB,eAAe;gBACZ4I,MAAM,EAAExF,wBAAyB;gBACjCyF,aAAa,EAAE,CAAC5I,UAAU,CAAE;gBAC5B6I,aAAa,EAAE,CACX,CAAC5I,WAAW,EAAE;kBACV6I,MAAM,EAAE,KAAK;kBACbC,YAAY,EAAE,KAAK;kBACnBC,UAAU,EAAE;gBAChB,CAAC,CAAC,CACJ;gBACF9B,SAAS,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrG,OAAA;UAAKiG,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACnChG,OAAA;YACImB,IAAI,EAAC,QAAQ;YACb8E,SAAS,EAAC,0DAA0D;YACpEc,QAAQ,EAAEvF,WAAW,IAAIE,YAAa;YAAAsE,QAAA,EAErCtE,YAAY,GAAG,aAAa,GAAGf,SAAS,GAAG,UAAU,GAAG;UAAU;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACTrG,OAAA;YACImB,IAAI,EAAC,QAAQ;YACbmF,OAAO,EAAEA,CAAA,KAAMlG,QAAQ,CAAC,2BAA2B,CAAE;YACrD6F,SAAS,EAAC,4DAA4D;YAAAD,QAAA,EACzE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAACnG,EAAA,CA1cID,eAAe;EAAA,QACAhB,WAAW,EACXa,WAAW,EACbD,SAAS,EACLX,WAAW,EACVA,WAAW,EACbA,WAAW;AAAA;AAAA8I,EAAA,GAN3B/H,eAAe;AA4crB,eAAeA,eAAe;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}