import db from '../models/index.js';
import { Op } from 'sequelize';
import * as classService from './class.service.js';

const { Lesson, LearningItem } = db;

export const getLessonByClassId = async (classId, { transaction = null, includeLearningItems = false } = {}) => {
    const options = {
        where: { classId }
    };

    if (transaction) {
        options.transaction = transaction;
    }

    if (includeLearningItems) {
        options.include = [{
            model: db.LearningItem,
            as: 'learningItems',
            attributes: ['id'],
        }];
    }

    const lessons = await db.Lesson.findAll(options);

    return lessons;
};

export const findLessonById = async (lessonId, { transaction = null, includeLearningItems = false } = {}) => {
    const options = {
        where: { id: lessonId }
    };
    if (transaction) {
        options.transaction = transaction;
    }
    if (includeLearningItems) {
        options.include = [{
            model: db.LearningItem,
            as: 'learningItems',
            attributes: ['id', 'name', 'url', 'typeOfLearningItem', 'deadline'],
        }];
    }
    const lesson = await db.Lesson.findOne(options);
    if (!lesson) {
        throw new Error("Không tìm thấy buổi học với ID đã cho.");
    }
    return lesson;
};


export const findBTVN = async (lessonId) => {
    let btvn = null;

    try {
        // Lấy thông tin buổi học hiện tại
        const currentLesson = await Lesson.findByPk(lessonId, {
            attributes: ['id', 'day', 'classId']
        });

        if (currentLesson) {
            // Tìm buổi học gần nhất trước đó trong cùng lớp
            const previousLesson = await Lesson.findOne({
                where: {
                    classId: currentLesson.classId,
                    day: {
                        [Op.lt]: currentLesson.day // Ngày nhỏ hơn (trước đó)
                    }
                },
                order: [['day', 'DESC']], // Sắp xếp giảm dần để lấy gần nhất
                attributes: ['id', 'day', 'name']
            });

            if (previousLesson) {
                // Tìm learning item có type DOC trong buổi học gần nhất trước đó
                const btvnLearningItem = await LearningItem.findOne({
                    where: {
                        lessonId: previousLesson.id,
                        typeOfLearningItem: 'BTVN'
                    },
                    attributes: ['id', 'name', 'url', 'typeOfLearningItem', 'deadline'],
                    order: [['createdAt', 'ASC']] // Lấy item đầu tiên nếu có nhiều
                });

                if (btvnLearningItem && btvnLearningItem.url) {
                    btvn = {
                        examId: btvnLearningItem.url,
                        learningItemId: btvnLearningItem.id
                    }
                }
            }
        }
    } catch (error) {
        console.error('Error finding previous lesson DOC item:', error);
        // Không throw error, chỉ log và tiếp tục
    }

    return btvn;
};

export const deleteLessons = async (lessonIds, transaction = null) => {
    try {
        const options = { where: { id: { [Op.in]: lessonIds } } };

        if (transaction) {
            options.transaction = transaction;
        }

        const deleted = await db.Lesson.destroy(options);

        if (!deleted) {
            throw new Error("Xóa buổi học không thành công!");
        }

        return true;
    } catch (error) {
        console.error("Xóa buổi học thất bại:", error);
        throw error;
    }
};


export const deleteLesson = async (lessonId, transaction = null) => {
    try {
        const options = { where: { id: lessonId } };

        if (transaction) {
            options.transaction = transaction;
        }

        const deleted = await db.Lesson.destroy(options);

        if (!deleted) {
            throw new Error("Xóa buổi học không thành công!");
        }

        return true;
    } catch (error) {
        console.error("Xóa buổi học thất bại:", error);
        throw error;
    }
};
