// Socket event constants
export const EVENTS = {
  // Connection events
  CONNECTION: 'connection',
  DISCONNECT: 'disconnect',

  // Exam events
  JOIN_EXAM: 'join_exam',
  EXAM_STARTED: 'exam_started',
  EXAM_ERROR: 'exam_error',
  SUBMIT_EXAM: 'submit_exam',
  EXAM_SUBMITTED: 'exam_submitted',
  SUBMIT_ERROR: 'submit_error',

  // Answer events
  SELECT_ANSWER: 'select_answer',
  ANSWER_SAVED: 'answer_saved',
  ANSWER_ERROR: 'answer_error',

  // Score calculation
  CALCULATE_SCORE: 'calculate_score',
  SCORE_CALCULATION_ERROR: 'score_calculation_error',

  // Admin events
  ADMIN_JOIN_EXAM_TRACKING: 'admin_join_exam_tracking',
  ADMIN_STUDENT_ANSWER: 'admin_student_answer',
  ADMIN_SCORE_CALCULATED: 'admin_score_calculated',
  ADMIN_USER_LOG: 'admin_user_log',

  // Cheating detection
  USER_LOG: 'user_log',
  CHEATING_WARNING: 'cheating_warning',

  // Time management
  REQUEST_TIME: 'request_time',
  EXAM_TIMER: 'exam_timer',
  EXAM_AUTO_SUBMITTED: 'exam_auto_submitted',

  // Notifications
  SEND_NOTIFICATION: 'send_notification',
  EXAM_NOTIFICATION: 'exam_notification',

  // User notifications
  USER_AUTHENTICATED: 'user_authenticated',
  NEW_NOTIFICATION: 'new_notification',
  NOTIFICATION_COUNT: 'notification_count',
  NOTIFICATION_MARKED_READ: 'notification_marked_read',
  ALL_NOTIFICATIONS_MARKED_READ: 'all_notifications_marked_read'
};

// Room prefix constants
export const ROOMS = {
  ADMIN_EXAM: 'exam-admin-',
  EXAM: 'exam-',
  USER: 'user-'
};

// Answer type constants
export const ANSWER_TYPES = {
  MULTIPLE_CHOICE: 'TN',
  TRUE_FALSE: 'DS',
  SHORT_ANSWER: 'TLN'
};

// Prefix for multiple choice answers
export const PREFIX_TN = ["A", "B", "C", "D"];
