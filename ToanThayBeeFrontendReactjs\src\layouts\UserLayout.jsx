import Header from "../components/header/Header";
import UnpaidTuitionModal from "../components/UnpaidTuitionModal";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { checkTuitionPaymentNotPaid, setShowUnpaidModal } from "../features/tuition/tuitionSlice";

const UserLayout = ({ children }) => {
    const [headerHeight, setHeaderHeight] = useState(0);
    const dispatch = useDispatch();
    const { isFirstTimeCheckTuition, tuitionPaymentNotPaid, showUnpaidModal } = useSelector((state) => state.tuition);

    useEffect(() => {
        if (!isFirstTimeCheckTuition) return;
        dispatch(checkTuitionPaymentNotPaid());
    }, [dispatch, isFirstTimeCheckTuition]);

    useEffect(() => {
        // Get the header height after it's rendered
        const header = document.querySelector('header');
        if (header) {
            setHeaderHeight(header.offsetHeight);

            // Update header height on window resize
            const handleResize = () => {
                setHeaderHeight(header.offsetHeight);
            };

            window.addEventListener('resize', handleResize);
            return () => window.removeEventListener('resize', handleResize);
        }
    }, []);

    return (
        <div className="flex flex-row w-full bg-gray-50">
            <Header />
            <div className="flex flex-col w-full justify-center sm:mt-0" style={{ paddingTop: `${headerHeight}px` }}>
                {children}
            </div>

            {/* Unpaid Tuition Modal */}
            <UnpaidTuitionModal
                isOpen={showUnpaidModal}
                onClose={() => dispatch(setShowUnpaidModal(false))}
                unpaidPayments={tuitionPaymentNotPaid}
            />
        </div>
    );
}


export default UserLayout;