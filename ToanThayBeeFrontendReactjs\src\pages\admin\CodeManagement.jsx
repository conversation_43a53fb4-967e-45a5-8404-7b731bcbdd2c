import { useEffect } from "react";
import AdminLayout from "../../layouts/AdminLayout";
import CodeTable from "../../components/table/CodeTable";
import { useSelector, useDispatch } from "react-redux";
import { fetchAllCodes } from "../../features/code/codeSlice";
import FunctionBarAdmin from "../../components/bar/FunctionBarAdmin";
import AdminModal from "../../components/modal/AdminModal";
import AddCodeModal from "../../components/modal/AddCodeModal";
import { setIsAddView } from "../../features/filter/filterSlice";
import { useState } from "react";
import { setCurrentPage, setLimit, setSearch } from "src/features/code/codeSlice";


const CodeManagement = () => {
    const dispatch = useDispatch();
    const { allCodes } = useSelector(state => state.codes);
    const { search } = useSelector(state => state.codes);
    const { isAddView, isFilterVIew } = useSelector(state => state.filter);
    const { page, pageSize, total, sortOrder, totalPages } = useSelector(state => state.codes.pagination);

    useEffect(() => {
        dispatch(fetchAllCodes({ search, page, pageSize, sortOrder }));
    }, [dispatch, search, page, pageSize, sortOrder]);

    return (
        <AdminLayout>
            <AdminModal isOpen={isAddView} headerText={'Tạo mã mới'} onClose={() => dispatch(setIsAddView(false))} >
                <AddCodeModal onClose={() => dispatch(setIsAddView(false))} fetchCodes={fetchAllCodes} />
            </AdminModal>

            <div className="text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9">
                Danh sách mã
            </div>
            <FunctionBarAdmin
                currentPage={page}
                totalItems={total}
                totalPages={totalPages}
                limit={pageSize}
                setLimit={(newLimit) => dispatch(setLimit(newLimit))}
                setCurrentPage={(newPage) => dispatch(setCurrentPage(newPage))}
                setSearch={(value) => dispatch(setSearch(value))}
            />
            <CodeTable codes={allCodes} />
        </AdminLayout>
    );
}

export default CodeManagement;