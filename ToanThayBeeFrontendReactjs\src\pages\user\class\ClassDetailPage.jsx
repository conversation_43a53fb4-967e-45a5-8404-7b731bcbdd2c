import UserLayout from "../../../layouts/UserLayout"
import { useDispatch, useSelector } from "react-redux"
import { fetchLessonLearningItemInClass } from "../../../features/class/classSlice"
import { useEffect, useState } from "react"
import { useParams } from "react-router-dom"
import SlideShow from "../../../components/image/SlideShow"
import QrCode from "../../../components/QrCode"
import { fetchCodesByType } from "../../../features/code/codeSlice"
import { useNavigate } from "react-router-dom"
import { joinClass } from "../../../features/class/classSlice"
import LoadingSpinner from "../../../components/loading/LoadingSpinner"
import LearningItemIcon from "../../../components/image/LearningItemIcon"
import {
    Calendar,
    Users,
    BookOpen,
    Clock,
    ChevronDown,
    ChevronUp,
    QrCode as QrCodeIcon,
    ArrowRight,
    Home,
    AlertTriangle
} from "lucide-react"
import NotFoundLayout from "src/layouts/404NotFound"
import ClassBanner from "../../../components/banner/ClassBanner"

const ClassDetailPage = () => {
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const { classCode } = useParams()
    const { classDetail } = useSelector(state => state.classes)
    const { codes } = useSelector(state => state.codes)
    const { loading } = useSelector(state => state.classes)
    const [loadingJoin, setLoadingJoin] = useState(false)
    const currentUrl = window.location.href;
    const images = classDetail?.slide?.slideImages?.map(img => img.imageUrl) || []
    const [openLessons, setOpenLessons] = useState([]);

    const toggleLesson = (index) => {
        setOpenLessons((prev) =>
            prev.includes(index)
                ? prev.filter((i) => i !== index) // nếu đang mở thì đóng lại
                : [...prev, index]               // nếu đang đóng thì mở ra
        );
    };

    const handleClicked = async () => {
        if (classDetail?.userStatus == "JS") {
            navigate(`/class/${classCode}/learning`)
        } else if (classDetail?.userStatus == "WS") {
            return
        } else {
            setLoadingJoin(true)
            await dispatch(joinClass(classCode))
            setLoadingJoin(false)
        }
    }

    useEffect(() => {
        if (classCode) dispatch(fetchLessonLearningItemInClass(classCode))
    }, [dispatch, classCode])

    useEffect(() => {
        dispatch(fetchCodesByType(['dow', 'duration']))
    }, [dispatch])

    // Xác định trạng thái nút tham gia
    const getJoinButtonStyles = () => {
        if (classDetail?.userStatus === 'JS') {
            return {
                bg: 'bg-green-600 hover:bg-green-700',
                text: 'Vào học ngay',
                icon: <ArrowRight size={18} className="ml-2" />
            };
        } else if (classDetail?.userStatus === 'WS') {
            return {
                bg: 'bg-amber-500 hover:bg-amber-600',
                text: 'Chờ phê duyệt',
                icon: <Clock size={18} className="ml-2" />
            };
        } else {
            return {
                bg: 'bg-sky-600 hover:bg-sky-700',
                text: 'Tham gia lớp học',
                icon: <Users size={18} className="ml-2" />
            };
        }
    };

    const buttonStyle = getJoinButtonStyles();

    return (
        <UserLayout>
            <NotFoundLayout
                isLoading={loading}
                textLoading="Đang tải thông tin lớp học..."
                isFound={classDetail ? true : false}
                message="Không tìm thấy lớp học với mã này"
                backLink={`/class`}
                backText="Quay lại danh sách lớp"
                className="flex flex-col min-h-screen justify-center items-start gap-4 p-4 bg-gray-50"
            >
                {/* Banner */}
                <div className="w-full">
                    {images && images.length > 0 ? (
                        <SlideShow
                            images={images}
                            interval={5000}
                            text={classDetail?.name}
                            h="h-[20rem]"
                        />
                    ) : (
                        <ClassBanner
                            className={classDetail?.name}
                            title={classDetail?.name || "Lớp học"}
                            subtitle="Chào mừng bạn đến với lớp học trực tuyến"
                            studentCount={classDetail?.joinedStudentCount || 0}
                            lessonCount={classDetail?.lessons?.length || 0}
                        />
                    )}
                </div>

                <div className="w-full h-full flex flex-col-reverse lg:flex-row justify-center items-start gap-6">
                    {/* Main Content */}
                    <div className="flex-1 w-full inline-flex flex-col justify-start items-start gap-6">
                        {/* Description Card */}
                        <div className="w-full p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
                            <h2 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                                <BookOpen size={22} className="mr-2 text-sky-600" />
                                Mô tả
                            </h2>
                            <p className="text-gray-700 leading-relaxed">
                                {classDetail?.description || "Không có mô tả cho lớp học này."}
                            </p>
                        </div>

                        {/* Lessons Card */}
                        <div className="w-full p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
                            <h2 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                                <Calendar size={22} className="mr-2 text-sky-600" />
                                Nội dung
                            </h2>
                            <p className="text-gray-700 leading-relaxed mb-4">Đây chỉ là phần xem trước vui lòng "vào học ngay" để xem chi tiết</p>

                            {classDetail?.lessons?.length > 0 ? (
                                <div className="">
                                    {classDetail?.lessons?.map((lesson, index) => (
                                        <div key={index} className="rounded-lg overflow-hidden">
                                            {/* Lesson Header */}
                                            <div
                                                onClick={() => toggleLesson(index)}
                                                className={`cursor-pointer py-2 px-4 flex justify-between items-center transition-colors duration-300 ${openLessons.includes(index)
                                                    ? 'bg-sky-700 text-white'
                                                    : 'bg-white text-gray-800 hover:bg-gray-100'
                                                    }`}
                                            >
                                                <div className="font-medium flex items-center">
                                                    <span className="mr-2 text-md">{lesson.name}</span>
                                                    {lesson.learningItems?.length > 0 && (
                                                        <span className="bg-white text-sky-700 text-xs px-2 py-1 rounded-full">
                                                            {lesson.learningItems.length} mục
                                                        </span>
                                                    )}
                                                </div>

                                                {openLessons.includes(index) ? (
                                                    <ChevronUp size={20} />
                                                ) : (
                                                    <ChevronDown size={20} />
                                                )}
                                            </div>

                                            {/* Lesson Content */}
                                            <div
                                                className={`transition-all duration-300 ease-in-out overflow-hidden ${openLessons.includes(index)
                                                    ? "max-h-96 opacity-100"
                                                    : "max-h-0 opacity-0"
                                                    }`}
                                            >
                                                {lesson.learningItems?.map((learningItem, i) => (
                                                    <div
                                                        key={i}
                                                        className="pl-8 pr-4 py-2 border-t border-gray-200 flex items-center hover:bg-gray-50"
                                                    >
                                                        <LearningItemIcon type={learningItem.typeOfLearningItem} size="w-4 h-4" />
                                                        <span className="ml-3 text-sm text-gray-700">{learningItem.name}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center py-8 text-gray-500">
                                    <AlertTriangle size={40} className="mb-2 text-amber-500" />
                                    <p>Không có buổi học nào</p>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Sidebar */}
                    <div className="w-full lg:w-1/3 flex flex-col gap-6">
                        {/* Class Info Card */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                                <BookOpen size={20} className="mr-2 text-sky-600" />
                                Thông tin lớp học
                            </h2>

                            <div className="space-y-4">
                                {/* Class Code */}
                                <div className="flex items-center text-gray-700">
                                    <div className="bg-sky-100 p-2 rounded-full mr-3">
                                        <QrCodeIcon size={18} className="text-sky-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500">Mã lớp</p>
                                        <p className="font-medium">{classDetail?.class_code}</p>
                                    </div>
                                </div>

                                {/* Student Count */}
                                <div className="flex items-center text-gray-700">
                                    <div className="bg-green-100 p-2 rounded-full mr-3">
                                        <Users size={18} className="text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500">Số người tham gia</p>
                                        <p className="font-medium">{classDetail?.joinedStudentCount} học sinh</p>
                                    </div>
                                </div>

                                {/* Lesson Count */}
                                <div className="flex items-center text-gray-700">
                                    <div className="bg-amber-100 p-2 rounded-full mr-3">
                                        <Calendar size={18} className="text-amber-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500">Số buổi học</p>
                                        <p className="font-medium">{classDetail?.lessons?.length || 0} buổi</p>
                                    </div>
                                </div>

                                {/* Schedule */}
                                <div className="flex items-center text-gray-700">
                                    <div className="bg-purple-100 p-2 rounded-full mr-3">
                                        <Clock size={18} className="text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500">Lịch học</p>
                                        {classDetail?.dayOfWeek1 && classDetail?.startTime1 && classDetail?.endTime1 && (
                                            <p className="font-medium">
                                                {codes['dow']?.find((code) => code.code === classDetail?.dayOfWeek1)?.description} {classDetail?.startTime1.slice(0, 5)} - {classDetail?.endTime1.slice(0, 5)}
                                            </p>
                                        )}

                                        {classDetail?.dayOfWeek2 && classDetail?.startTime2 && classDetail?.endTime2 && (
                                            <p className="font-medium">
                                                {codes['dow']?.find((code) => code.code === classDetail?.dayOfWeek2)?.description} {classDetail?.startTime2.slice(0, 5)} - {classDetail?.endTime2.slice(0, 5)}
                                            </p>
                                        )}

                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* QR Code Card */}
                        <div className="bg-white rounded-lg shadow-md p-6 flex flex-col items-center">
                            <h3 className="text-lg font-medium text-gray-800 mb-4">QR lớp học</h3>
                            <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-200 mb-3">
                                <QrCode url={currentUrl} size={150} />
                            </div>
                            <p className="text-sm text-gray-500 text-center">
                                Quét mã QR để truy cập nhanh vào trang lớp học
                            </p>
                        </div>

                        {/* Action Buttons */}
                        <div className="space-y-3">
                            <button
                                onClick={handleClicked}
                                className={`w-full py-3 px-4 rounded-lg ${buttonStyle.bg} text-white font-medium transition-colors duration-200 flex items-center justify-center`}
                                disabled={classDetail?.userStatus === 'WS'}
                            >
                                {loadingJoin ? (
                                    <LoadingSpinner size="1.25rem" color="text-white" minHeight="min-h-0" />
                                ) : (
                                    <>
                                        {buttonStyle.text}
                                        {buttonStyle.icon}
                                    </>
                                )}
                            </button>

                            <button
                                onClick={() => navigate('/class')}
                                className="w-full py-3 px-4 rounded-lg bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center"
                            >
                                <Home size={18} className="mr-2" />
                                Quay lại danh sách lớp
                            </button>
                        </div>
                    </div>
                </div>
            </NotFoundLayout>
        </UserLayout>
    )
}

export default ClassDetailPage
