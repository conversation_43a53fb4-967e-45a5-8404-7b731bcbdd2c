import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as notificationApi from "../../services/notificationApi";
import { apiHandler } from "../../utils/apiHandler";
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';
import { getCachedOrFetch, clearCacheWithPrefix } from "../../utils/cacheManager";

// Thunk to fetch notifications list
export const fetchNotifications = createAsyncThunk(
  "notifications/fetchNotifications",
  async ({ limit = 10, offset = 0, isRead }, { dispatch }) => {
    // console.log('fetchNotifications', { limit, offset, isRead });

    return await apiHandler(
      dispatch,
      notificationApi.getNotificationsAPI,
      { limit, offset, isRead },
      () => { },
      false,
      false,
      false,
      false
    );
  }
);

// Cache key for unread count
const UNREAD_COUNT_CACHE_KEY = 'notifications/unreadCount';
// Cache TTL for unread count (30 seconds)
const UNREAD_COUNT_CACHE_TTL = 30 * 1000;

// Thunk to fetch unread notification count
export const fetchUnreadCount = createAsyncThunk(
  "notifications/fetchUnreadCount",
  async (_, { dispatch }) => {
    // Use cached value if available, otherwise fetch from API
    return await getCachedOrFetch(
      UNREAD_COUNT_CACHE_KEY,
      async () => {
        return await apiHandler(
          dispatch,
          notificationApi.getUnreadCountAPI,
          null,
          () => { },
          false,
          false,
          false,
          false
        );
      },
      UNREAD_COUNT_CACHE_TTL
    );
  }
);

// Thunk to mark notifications as read
export const markAsRead = createAsyncThunk(
  "notifications/markAsRead",
  async (notificationIds, { dispatch }) => {
    // Clear unread count cache when marking notifications as read
    clearCacheWithPrefix(UNREAD_COUNT_CACHE_KEY);

    return await apiHandler(
      dispatch,
      notificationApi.markAsReadAPI,
      notificationIds,
      () => { },
      false,
      false,
      false,
      false
    );
  }
);

// Thunk to mark all notifications as read
export const markAllAsRead = createAsyncThunk(
  "notifications/markAllAsRead",
  async (_, { dispatch }) => {
    // Clear unread count cache when marking all notifications as read
    clearCacheWithPrefix(UNREAD_COUNT_CACHE_KEY);

    return await apiHandler(
      dispatch,
      notificationApi.markAllAsReadAPI,
      null,
      () => { },
      false,
      false,
      false,
      false
    );
  }
);

// Thunk to delete a notification
export const deleteNotification = createAsyncThunk(
  "notifications/deleteNotification",
  async (id, { dispatch }) => {
    // Clear unread count cache when deleting a notification
    clearCacheWithPrefix(UNREAD_COUNT_CACHE_KEY);

    return await apiHandler(
      dispatch,
      notificationApi.deleteNotificationAPI,
      id,
      () => { },
      true,
      false,
      false,
      false
    );
  }
);

// Thunk to delete multiple notifications
export const deleteNotifications = createAsyncThunk(
  "notifications/deleteNotifications",
  async (notificationIds, { dispatch }) => {
    // Clear unread count cache when deleting multiple notifications
    clearCacheWithPrefix(UNREAD_COUNT_CACHE_KEY);

    return await apiHandler(
      dispatch,
      notificationApi.deleteNotificationsAPI,
      notificationIds,
      () => { },
      true,
      false,
      false,
      false
    );
  }
);

// Notification slice
const notificationSlice = createSlice({
  name: "notifications",
  initialState: {
    notifications: [],
    unreadCount: 0,
    loading: false,
    hasMore: true,
    totalItems: 0,
    didInit: false
  },
  reducers: {
    // Add new notification received from socket
    addNotification: (state, action) => {
      state.notifications.unshift(action.payload);
      state.unreadCount += 1;
      // Clear cache when adding a new notification
      clearCacheWithPrefix(UNREAD_COUNT_CACHE_KEY);
    },
    // Update unread notification count from socket
    updateUnreadCount: (state, action) => {
      state.unreadCount = action.payload;
      // Clear cache when updating unread count
      clearCacheWithPrefix(UNREAD_COUNT_CACHE_KEY);
    },
    resetDidInit: (state) => {
      state.didInit = false;
    },
    // Reset state
    resetNotifications: (state) => {
      state.notifications = [];
      state.hasMore = true;
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchNotifications
      .addCase(fetchNotifications.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        if (action.payload) {
          const { data, totalItems, limit } = action.payload;

          if (data && Array.isArray(data)) {
            // Transform API data to match our notification format
            const formattedNotifications = data.map(notification => {
              // Map notification type to icon type
              let type = 'system';
              if (notification.type) {
                const upperType = notification.type.toUpperCase();
                if (upperType === 'EXAM') type = 'exam';
                else if (upperType === 'CLASS') type = 'reminder';
                else if (upperType === 'RESULT') type = 'result';
                else if (upperType === 'TUITION') type = 'tuition';
                else if (upperType.includes('EXAM')) type = 'exam';
                else if (upperType.includes('CLASS')) type = 'reminder';
                else if (upperType.includes('RESULT')) type = 'result';
                else if (upperType.includes('TUITION')) type = 'tuition';
              }

              return {
                id: notification.id,
                title: notification.title || 'Notification',
                message: notification.content,
                time: formatDistanceToNow(new Date(notification.createdAt), {
                  addSuffix: true,
                  locale: vi
                }),
                isRead: notification.isRead,
                link: notification.actionUrl,
                type: type,
                createdAt: notification.createdAt,
                relatedId: notification.relatedId,
                relatedType: notification.relatedType,
                updatedAt: notification.updatedAt,
                userId: notification.userId
              };
            });

            // If it's the first page, replace the list
            if (action.meta.arg.offset === 0) {
              state.notifications = formattedNotifications;
            } else {
              // Otherwise, add to the current list
              state.notifications = [...state.notifications, ...formattedNotifications];
            }
            if (!state.didInit) {
              state.totalItems = totalItems;
              state.didInit = true;
            }
            state.hasMore = data.length === limit;
          }
        }
        state.loading = false;
      })
      .addCase(fetchNotifications.rejected, (state) => {
        state.loading = false;
      })

      // Handle fetchUnreadCount
      .addCase(fetchUnreadCount.fulfilled, (state, action) => {
        if (action.payload && action.payload.data.count !== undefined) {
          // console.log('fetchUnreadCount', action.payload.data.count);
          state.unreadCount = action.payload.data.count;
        }
      
      })

      // Handle markAsRead
      .addCase(markAsRead.fulfilled, (state, action) => {
        // console.log('markAsRead', action.payload);
        if (action.payload) {
          // console.log('markAsRead', action.payload);

          // Update read status for notifications - using "mutating" approach
          for (let i = 0; i < state.notifications.length; i++) {
            if (action.payload.data.notificationIds.includes(state.notifications[i].id)) {
              state.notifications[i].isRead = true;
            }
          }
          // console.log('markAsRead', action.payload.data.unreadCount);

          // Update unread count

          // If API doesn't return unreadCount, recalculate
          state.unreadCount = action.payload.data.unreadCount

        }
      })

      // Handle markAllAsRead
      .addCase(markAllAsRead.fulfilled, (state) => {
        // Mark all notifications as read - using "mutating" approach
        for (let i = 0; i < state.notifications.length; i++) {
          state.notifications[i].isRead = true;
        }
        state.unreadCount = 0;
      })

      // Handle deleteNotification
      .addCase(deleteNotification.fulfilled, (state, action) => {
        // console.log('deleteNotification', action.payload);
        if (action.payload && action.payload.data) {
          const { id, unreadCount } = action.payload.data;

          // console.log('deleteNotification', action.payload.data);

          // Find and remove the notification
          const index = state.notifications.findIndex(n => n.id == id);
          // console.log('deleteNotification', index);
          if (index !== -1) {
            // Remove the notification using splice (mutating approach)
            state.notifications.splice(index, 1);
          }

          // Update unread count from API response
          if (unreadCount !== undefined) {
            state.unreadCount = unreadCount;
          }
          
          state.totalItems -= 1;
          // console.log('deleteNotification', state.notifications, state.totalItems);
        }
      })
      // Handle deleteNotifications
      .addCase(deleteNotifications.fulfilled, (state, action) => {
        if (action.payload && action.payload.data && action.payload.data.notificationIds) {
          const { notificationIds, unreadCount } = action.payload.data;

          // Remove notifications from list - using "mutating" approach
          // We need to remove items from the end to avoid index shifting issues
          for (let i = state.notifications.length - 1; i >= 0; i--) {
            if (notificationIds.includes(state.notifications[i].id)) {
              state.notifications.splice(i, 1);
            }
          }

          // Update unread count from API response
          if (unreadCount !== undefined) {
            state.unreadCount = unreadCount;
          }
          state.totalItems -= notificationIds.length;
        }
      });
  },
});

export const { addNotification, updateUnreadCount, resetNotifications, resetDidInit } = notificationSlice.actions;
export default notificationSlice.reducer;
