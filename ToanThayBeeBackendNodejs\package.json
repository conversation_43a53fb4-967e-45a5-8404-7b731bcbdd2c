{"name": "toan_thay_bee_backend", "version": "1.0.0", "main": "index.js", "engines": {"node": ">=22.11.0"}, "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node ./src/index.js", "dev": "nodemon --exec babel-node ./src/index.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "firebase": "^11.3.1", "googleapis": "^149.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "node-cron": "^4.0.7", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "remove-accents": "^0.5.0", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2", "socket.io": "^4.8.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.9", "@babel/node": "^7.26.0", "@babel/preset-env": "^7.26.9", "nodemon": "^3.1.9"}}