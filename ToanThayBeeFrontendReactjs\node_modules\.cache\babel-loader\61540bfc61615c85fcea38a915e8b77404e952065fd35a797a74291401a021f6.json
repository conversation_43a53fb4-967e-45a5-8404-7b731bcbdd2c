{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport { getImagesAPI, postImageAPI, deleteImageAPI, getImagesFoldersAPI, getPdfsAPI, uploadMultipleImagesAPI } from \"../../services/imageApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nexport const fetchImages = createAsyncThunk(\"images/fetchImages\", async (folder, _ref) => {\n  let {\n    dispatch\n  } = _ref;\n  return await apiHandler(dispatch, getImagesAPI, {\n    folder\n  }, () => {}, true, false);\n});\nexport const uploadMultipleImages = createAsyncThunk('examAI/uploadMultipleImages', async (files, _ref2) => {\n  let {\n    dispatch\n  } = _ref2;\n  return apiHandler(dispatch, uploadMultipleImagesAPI, files, null, true, false, false, false);\n});\nexport const fetchPdfs = createAsyncThunk(\"images/fetchPdfs\", async (folder, _ref3) => {\n  let {\n    dispatch\n  } = _ref3;\n  return await apiHandler(dispatch, getPdfsAPI, {\n    folder\n  }, () => {}, true, false);\n});\nexport const fetchImagesFolders = createAsyncThunk(\"images/fetchImagesFolders\", async (folders, _ref4) => {\n  let {\n    dispatch\n  } = _ref4;\n  return await apiHandler(dispatch, getImagesFoldersAPI, {\n    folders\n  }, () => {}, false, false);\n});\nexport const postImage = createAsyncThunk(\"images/postImage\", async (_ref5, _ref6) => {\n  let {\n    image,\n    folder\n  } = _ref5;\n  let {\n    dispatch\n  } = _ref6;\n  return await apiHandler(dispatch, postImageAPI, {\n    image,\n    folder\n  }, () => {}, true, false);\n});\nexport const deleteImage = createAsyncThunk(\"images/deleteImage\", async (imageUrl, _ref7) => {\n  let {\n    dispatch\n  } = _ref7;\n  return await apiHandler(dispatch, deleteImageAPI, {\n    imageUrl\n  }, () => {}, true, false);\n});\nconst imageSlice = createSlice({\n  name: \"images\",\n  initialState: {\n    images: {},\n    imagesHome: [],\n    imageUpload: null,\n    pdfs: []\n  },\n  reducers: {},\n  extraReducers: builder => {\n    builder.addCase(fetchImages.pending, state => {\n      state.images = {};\n    }).addCase(fetchImages.fulfilled, (state, action) => {\n      if (action.payload) {\n        const folder = action.payload.folder;\n        state.images[folder] = action.payload.images;\n      }\n    }).addCase(fetchPdfs.pending, state => {\n      state.pdfs = [];\n    }).addCase(fetchPdfs.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.pdfs = action.payload.pdfs;\n      }\n    }).addCase(fetchImagesFolders.pending, state => {\n      state.imagesHome = [];\n    }).addCase(fetchImagesFolders.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.imagesHome = action.payload.images;\n      }\n    }).addCase(postImage.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.imageUpload = action.payload.file;\n\n        // Check if the folder exists in state.images\n        const folder = action.payload.folder;\n        if (folder) {\n          // Initialize the folder array if it doesn't exist\n          if (!state.images[folder]) {\n            state.images[folder] = [];\n          }\n\n          // Add the file to the folder array\n          if (Array.isArray(state.images[folder])) {\n            state.images[folder].push(action.payload.file);\n          }\n        }\n      }\n    }).addCase(deleteImage.fulfilled, (state, action) => {\n      const payload = action.payload;\n      if (!payload) return;\n      const {\n        file: image,\n        folder\n      } = payload;\n      if (folder && state.images[folder]) {\n        // Có folder: chỉ xoá trong folder đó\n        state.images[folder] = state.images[folder].filter(img => img !== image);\n      } else {\n        // Không có folder: tìm và xoá ở mọi folder con\n        Object.keys(state.images).forEach(key => {\n          state.images[key] = state.images[key].filter(img => img !== image);\n        });\n      }\n    });\n  }\n});\nexport default imageSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "getImagesAPI", "postImageAPI", "deleteImageAPI", "getImagesFoldersAPI", "getPdfsAPI", "uploadMultipleImagesAPI", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchImages", "folder", "_ref", "dispatch", "uploadMultipleImages", "files", "_ref2", "fetchPdfs", "_ref3", "fetchImagesFolders", "folders", "_ref4", "postImage", "_ref5", "_ref6", "image", "deleteImage", "imageUrl", "_ref7", "imageSlice", "name", "initialState", "images", "imagesHome", "imageUpload", "pdfs", "reducers", "extraReducers", "builder", "addCase", "pending", "state", "fulfilled", "action", "payload", "file", "Array", "isArray", "push", "filter", "img", "Object", "keys", "for<PERSON>ach", "key", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/image/imageSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport { getImagesAPI, postImageAPI, deleteImageAPI, getImagesFoldersAPI, getPdfsAPI, uploadMultipleImagesAPI } from \"../../services/imageApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\n\r\nexport const fetchImages = createAsyncThunk(\r\n    \"images/fetchImages\",\r\n    async (folder, { dispatch }) => {\r\n        return await apiHandler(dispatch, getImagesAPI, { folder }, () => {\r\n        }, true, false);\r\n    }\r\n);\r\n\r\nexport const uploadMultipleImages = createAsyncThunk(\r\n    'examAI/uploadMultipleImages',\r\n    async (files, { dispatch }) => {\r\n        return apiHandler(dispatch, uploadMultipleImagesAPI, files, null, true, false, false, false);\r\n    }\r\n);\r\n\r\n\r\nexport const fetchPdfs = createAsyncThunk(\r\n    \"images/fetchPdfs\",\r\n    async (folder, { dispatch }) => {\r\n        return await apiHandler(dispatch, getPdfsAPI, { folder }, () => {\r\n        }, true, false);\r\n    }\r\n);\r\n\r\nexport const fetchImagesFolders = createAsyncThunk(\r\n    \"images/fetchImagesFolders\",\r\n    async (folders, { dispatch }) => {\r\n        return await apiHandler(dispatch, getImagesFoldersAPI, { folders }, () => {\r\n        }, false, false);\r\n    }\r\n);\r\n\r\n\r\nexport const postImage = createAsyncThunk(\r\n    \"images/postImage\",\r\n    async ({ image, folder }, { dispatch }) => {\r\n        return await apiHandler(dispatch, postImageAPI, { image, folder }, () => {\r\n        }, true, false);\r\n    }\r\n);\r\n\r\nexport const deleteImage = createAsyncThunk(\r\n    \"images/deleteImage\",\r\n    async (imageUrl, { dispatch }) => {\r\n        return await apiHandler(dispatch, deleteImageAPI, { imageUrl }, () => { }, true, false);\r\n    }\r\n);\r\n\r\nconst imageSlice = createSlice({\r\n    name: \"images\",\r\n    initialState: {\r\n        images: {},\r\n        imagesHome: [],\r\n        imageUpload: null,\r\n        pdfs: []\r\n    },\r\n    reducers: {\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchImages.pending, (state) => {\r\n                state.images = {};\r\n            })\r\n            .addCase(fetchImages.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    const folder = action.payload.folder\r\n                    state.images[folder] = action.payload.images;\r\n                }\r\n            })\r\n            .addCase(fetchPdfs.pending, (state) => {\r\n                state.pdfs = [];\r\n            })\r\n            .addCase(fetchPdfs.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.pdfs = action.payload.pdfs;\r\n                }\r\n            })\r\n            .addCase(fetchImagesFolders.pending, (state) => {\r\n                state.imagesHome = [];\r\n            })\r\n            .addCase(fetchImagesFolders.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.imagesHome = action.payload.images;\r\n                }\r\n            })\r\n            .addCase(postImage.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.imageUpload = action.payload.file;\r\n\r\n                    // Check if the folder exists in state.images\r\n                    const folder = action.payload.folder;\r\n                    if (folder) {\r\n                        // Initialize the folder array if it doesn't exist\r\n                        if (!state.images[folder]) {\r\n                            state.images[folder] = [];\r\n                        }\r\n\r\n                        // Add the file to the folder array\r\n                        if (Array.isArray(state.images[folder])) {\r\n                            state.images[folder].push(action.payload.file);\r\n                        }\r\n                    }\r\n                }\r\n            })\r\n            .addCase(deleteImage.fulfilled, (state, action) => {\r\n                const payload = action.payload;\r\n                if (!payload) return;\r\n\r\n                const { file: image, folder } = payload;\r\n\r\n                if (folder && state.images[folder]) {\r\n                    // Có folder: chỉ xoá trong folder đó\r\n                    state.images[folder] = state.images[folder].filter(img => img !== image);\r\n                } else {\r\n                    // Không có folder: tìm và xoá ở mọi folder con\r\n                    Object.keys(state.images).forEach(key => {\r\n                        state.images[key] = state.images[key].filter(img => img !== image);\r\n                    });\r\n                }\r\n            });\r\n\r\n    },\r\n});\r\n\r\nexport default imageSlice.reducer\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,UAAU,EAAEC,uBAAuB,QAAQ,yBAAyB;AAC9I,SAASC,UAAU,QAAQ,wBAAwB;AAEnD,OAAO,MAAMC,WAAW,GAAGR,gBAAgB,CACvC,oBAAoB,EACpB,OAAOS,MAAM,EAAAC,IAAA,KAAmB;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAD,IAAA;EACvB,OAAO,MAAMH,UAAU,CAACI,QAAQ,EAAEV,YAAY,EAAE;IAAEQ;EAAO,CAAC,EAAE,MAAM,CAClE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACnB,CACJ,CAAC;AAED,OAAO,MAAMG,oBAAoB,GAAGZ,gBAAgB,CAChD,6BAA6B,EAC7B,OAAOa,KAAK,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEH;EAAS,CAAC,GAAAG,KAAA;EACtB,OAAOP,UAAU,CAACI,QAAQ,EAAEL,uBAAuB,EAAEO,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAChG,CACJ,CAAC;AAGD,OAAO,MAAME,SAAS,GAAGf,gBAAgB,CACrC,kBAAkB,EAClB,OAAOS,MAAM,EAAAO,KAAA,KAAmB;EAAA,IAAjB;IAAEL;EAAS,CAAC,GAAAK,KAAA;EACvB,OAAO,MAAMT,UAAU,CAACI,QAAQ,EAAEN,UAAU,EAAE;IAAEI;EAAO,CAAC,EAAE,MAAM,CAChE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACnB,CACJ,CAAC;AAED,OAAO,MAAMQ,kBAAkB,GAAGjB,gBAAgB,CAC9C,2BAA2B,EAC3B,OAAOkB,OAAO,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAER;EAAS,CAAC,GAAAQ,KAAA;EACxB,OAAO,MAAMZ,UAAU,CAACI,QAAQ,EAAEP,mBAAmB,EAAE;IAAEc;EAAQ,CAAC,EAAE,MAAM,CAC1E,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACpB,CACJ,CAAC;AAGD,OAAO,MAAME,SAAS,GAAGpB,gBAAgB,CACrC,kBAAkB,EAClB,OAAAqB,KAAA,EAAAC,KAAA,KAA2C;EAAA,IAApC;IAAEC,KAAK;IAAEd;EAAO,CAAC,GAAAY,KAAA;EAAA,IAAE;IAAEV;EAAS,CAAC,GAAAW,KAAA;EAClC,OAAO,MAAMf,UAAU,CAACI,QAAQ,EAAET,YAAY,EAAE;IAAEqB,KAAK;IAAEd;EAAO,CAAC,EAAE,MAAM,CACzE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACnB,CACJ,CAAC;AAED,OAAO,MAAMe,WAAW,GAAGxB,gBAAgB,CACvC,oBAAoB,EACpB,OAAOyB,QAAQ,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEf;EAAS,CAAC,GAAAe,KAAA;EACzB,OAAO,MAAMnB,UAAU,CAACI,QAAQ,EAAER,cAAc,EAAE;IAAEsB;EAAS,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AAC3F,CACJ,CAAC;AAED,MAAME,UAAU,GAAG5B,WAAW,CAAC;EAC3B6B,IAAI,EAAE,QAAQ;EACdC,YAAY,EAAE;IACVC,MAAM,EAAE,CAAC,CAAC;IACVC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAE;EACV,CAAC;EACDC,QAAQ,EAAE,CACV,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC7B,WAAW,CAAC8B,OAAO,EAAGC,KAAK,IAAK;MACrCA,KAAK,CAACT,MAAM,GAAG,CAAC,CAAC;IACrB,CAAC,CAAC,CACDO,OAAO,CAAC7B,WAAW,CAACgC,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MAC/C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB,MAAMjC,MAAM,GAAGgC,MAAM,CAACC,OAAO,CAACjC,MAAM;QACpC8B,KAAK,CAACT,MAAM,CAACrB,MAAM,CAAC,GAAGgC,MAAM,CAACC,OAAO,CAACZ,MAAM;MAChD;IACJ,CAAC,CAAC,CACDO,OAAO,CAACtB,SAAS,CAACuB,OAAO,EAAGC,KAAK,IAAK;MACnCA,KAAK,CAACN,IAAI,GAAG,EAAE;IACnB,CAAC,CAAC,CACDI,OAAO,CAACtB,SAAS,CAACyB,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MAC7C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBH,KAAK,CAACN,IAAI,GAAGQ,MAAM,CAACC,OAAO,CAACT,IAAI;MACpC;IACJ,CAAC,CAAC,CACDI,OAAO,CAACpB,kBAAkB,CAACqB,OAAO,EAAGC,KAAK,IAAK;MAC5CA,KAAK,CAACR,UAAU,GAAG,EAAE;IACzB,CAAC,CAAC,CACDM,OAAO,CAACpB,kBAAkB,CAACuB,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MACtD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBH,KAAK,CAACR,UAAU,GAAGU,MAAM,CAACC,OAAO,CAACZ,MAAM;MAC5C;IACJ,CAAC,CAAC,CACDO,OAAO,CAACjB,SAAS,CAACoB,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MAC7C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBH,KAAK,CAACP,WAAW,GAAGS,MAAM,CAACC,OAAO,CAACC,IAAI;;QAEvC;QACA,MAAMlC,MAAM,GAAGgC,MAAM,CAACC,OAAO,CAACjC,MAAM;QACpC,IAAIA,MAAM,EAAE;UACR;UACA,IAAI,CAAC8B,KAAK,CAACT,MAAM,CAACrB,MAAM,CAAC,EAAE;YACvB8B,KAAK,CAACT,MAAM,CAACrB,MAAM,CAAC,GAAG,EAAE;UAC7B;;UAEA;UACA,IAAImC,KAAK,CAACC,OAAO,CAACN,KAAK,CAACT,MAAM,CAACrB,MAAM,CAAC,CAAC,EAAE;YACrC8B,KAAK,CAACT,MAAM,CAACrB,MAAM,CAAC,CAACqC,IAAI,CAACL,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC;UAClD;QACJ;MACJ;IACJ,CAAC,CAAC,CACDN,OAAO,CAACb,WAAW,CAACgB,SAAS,EAAE,CAACD,KAAK,EAAEE,MAAM,KAAK;MAC/C,MAAMC,OAAO,GAAGD,MAAM,CAACC,OAAO;MAC9B,IAAI,CAACA,OAAO,EAAE;MAEd,MAAM;QAAEC,IAAI,EAAEpB,KAAK;QAAEd;MAAO,CAAC,GAAGiC,OAAO;MAEvC,IAAIjC,MAAM,IAAI8B,KAAK,CAACT,MAAM,CAACrB,MAAM,CAAC,EAAE;QAChC;QACA8B,KAAK,CAACT,MAAM,CAACrB,MAAM,CAAC,GAAG8B,KAAK,CAACT,MAAM,CAACrB,MAAM,CAAC,CAACsC,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKzB,KAAK,CAAC;MAC5E,CAAC,MAAM;QACH;QACA0B,MAAM,CAACC,IAAI,CAACX,KAAK,CAACT,MAAM,CAAC,CAACqB,OAAO,CAACC,GAAG,IAAI;UACrCb,KAAK,CAACT,MAAM,CAACsB,GAAG,CAAC,GAAGb,KAAK,CAACT,MAAM,CAACsB,GAAG,CAAC,CAACL,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKzB,KAAK,CAAC;QACtE,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EAEV;AACJ,CAAC,CAAC;AAEF,eAAeI,UAAU,CAAC0B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}