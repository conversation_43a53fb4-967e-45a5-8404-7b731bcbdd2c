// index.js
import express from 'express';
import http from 'http';
import cors from 'cors';
import dotenv from 'dotenv';
import cookieParser from 'cookie-parser';
import { AppRoute } from './routes/AppRoute.js';
import db from './models/index.js';
import os from 'os';
import path from 'path';
import { initializeSocketIO } from './socket/index.js';
import { scheduleUserDeactivation } from './services/cronJobs.js';

dotenv.config();

const app = express();
const server = http.createServer(app); // gộp socket + express
const NODE_ENV = process.env.NODE_ENV || 'production';

const port = process.env.PORT || 3000;
// const hostname= '*************'
const frontendUrl = process.env.FRONTEND_URL || 'https://toanthaybee.edu.vn';
const ngrokUrl = process.env.NGROK_URL || 'https://4e04-14-191-32-178.ngrok-free.app';

// C<PERSON>u hình middleware
app.use("/images", express.static(path.join(path.resolve(), "public")));
app.use(cookieParser());
const allowedOrigins = [
    frontendUrl,
    'http://localhost:4000',
    'https://toanthaybee.edu.vn',
    'https://toan-thay-bee-frontend-reactjs-d5fo-gdj9v4lpy.vercel.app',
    ngrokUrl
].filter(Boolean); // loại bỏ undefined nếu có

if (NODE_ENV === 'development') {
    allowedOrigins.push('http://localhost:4000'); // Thêm localhost cho môi trường phát triển
}

const corsOptions = {
    origin: function (origin, callback) {
        if (!origin || allowedOrigins.includes(origin)) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS: ' + origin));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Origin', 'Accept'],
    exposedHeaders: ['Content-Range', 'X-Content-Range', 'Authorization'],
    maxAge: 600, // Cache CORS preflight requests for 10 minutes
    preflightContinue: false,
    optionsSuccessStatus: 204
};

// Thêm middleware CORS trước các routes
app.use(cors(corsOptions));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check
app.get("/healthcheck", async (req, res) => {
    try {
        await db.sequelize.authenticate();
        return res.status(200).json({
            status: "OK",
            message: "Service is running",
            timestamp: new Date().toISOString(),
            database: "Connected",
            memoryUsage: `${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(2)} MB`,
            uptime: `${process.uptime().toFixed(2)} seconds`,
            cpuUsage: os.loadavg()
        });
    } catch (error) {
        return res.status(500).json({
            status: "ERROR",
            message: "Service is down",
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// Gắn route
AppRoute(app);

// -------------------------
// 🧠 Socket.IO setup
// -------------------------
// Initialize Socket.IO with the HTTP server and allowed origins
// const io = initializeSocketIO(server, allowedOrigins);

// // Make io available to the Express app
// app.set('io', io);



// Khởi chạy cron jobs
// scheduleUserDeactivation();

// Khởi chạy server
server.listen(port, () => {
    console.log(`🚀 Server running at port ${port}`);
});
