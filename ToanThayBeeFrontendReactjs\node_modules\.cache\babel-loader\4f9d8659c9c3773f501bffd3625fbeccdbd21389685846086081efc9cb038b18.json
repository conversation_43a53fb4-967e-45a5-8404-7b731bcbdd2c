{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\RightContent.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const RightContent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 overflow-y-auto p-4\",\n    children: \"Right Content\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = RightContent;\nexport default RightContent;\nvar _c;\n$RefreshReg$(_c, \"RightContent\");", "map": {"version": 3, "names": ["RightContent", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/RightContent.jsx"], "sourcesContent": ["export const RightContent = () => {\r\n    return (\r\n        <div className=\"flex-1 overflow-y-auto p-4\">\r\n            Right Content\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default RightContent;"], "mappings": ";;AAAA,OAAO,MAAMA,YAAY,GAAGA,CAAA,KAAM;EAC9B,oBACIC,OAAA;IAAKC,SAAS,EAAC,4BAA4B;IAAAC,QAAA,EAAC;EAE5C;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAEd,CAAC;AAAAC,EAAA,GANYR,YAAY;AAQzB,eAAeA,YAAY;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}