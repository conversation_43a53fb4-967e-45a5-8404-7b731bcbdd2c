'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('classTuition', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      classId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'class',
          key: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
      },
      month: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Tháng áp dụng học phí (định dạng YYYY-MM)'
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: 'Số tiền học phí'
      },
      note: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '<PERSON>hi chú về học phí'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Thêm index cho cột month và classId để tối ưu truy vấn
    await queryInterface.addIndex('classTuition', ['classId', 'month'], {
      name: 'idx_class_tuition_class_month',
      unique: true
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('classTuition');
  }
};
