{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport TextArea from \"src/components/input/TextArea\";\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\nimport { CheckCircle } from \"lucide-react\";\nimport { setQuestions } from \"src/features/questionsExam/questionsExamSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DetailQuestionView = () => {\n  _s();\n  const {\n    questionsExam,\n    selectedId,\n    view\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const dispatch = useDispatch();\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [question, setQuestion] = useState(null);\n  useEffect(() => {\n    // console.log(selectedId);\n\n    if (selectedId && (questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.length) > 0) {\n      const selectedQuestion = questionsExam === null || questionsExam === void 0 ? void 0 : questionsExam.find(q => q.id === selectedId);\n      setQuestion(selectedQuestion);\n    }\n  }, [selectedId, questionsExam]);\n  const handleQuestionChange = (e, field) => {\n    const updatedQuestion = {\n      ...question,\n      [field]: e.target.value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleStatementChange = (index, value, field) => {\n    const updatedStatements = [...question.statements];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...question,\n      statements: updatedStatements\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const handleSolutionQuestionChange = value => {\n    const updatedQuestion = {\n      ...question,\n      solution: value\n    };\n    dispatch(setQuestions(updatedQuestion));\n  };\n  const [optionChapter, setOptionChapter] = useState([]);\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (question !== null && question !== void 0 && question.class && (question === null || question === void 0 ? void 0 : question.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.startsWith(question === null || question === void 0 ? void 0 : question.class) && code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, question === null || question === void 0 ? void 0 : question.class]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: question && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Ph\\xE2n lo\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.class,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'class'),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: question.chapter,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'chapter'),\n            options: optionChapter,\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: question.difficulty,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'difficulty'),\n            options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextArea, {\n            value: question.content,\n            onChange: e => handleQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 29\n          }, this), (view === 'image' || question.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: question.imageUrl,\n            onImageDrop: image => handleQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 33\n          }, this), question.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: question.statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n                  value: statement.content,\n                  onChange: e => handleStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 45\n              }, this), (view === 'image' || statement.imageUrl) && /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 49\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 41\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 33\n          }, this), question.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: question.correctAnswer,\n              onChange: e => handleQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n            solution: question.solution,\n            onSolutionChange: handleSolutionQuestionChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 9\n  }, this);\n};\n_s(DetailQuestionView, \"AtX1aa+NXfezjHwEa8coxZtMvMs=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = DetailQuestionView;\nconst LeftContent = () => {\n  _s2();\n  const [view, setView] = useState('questionDetail');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xs font-semibold text-gray-900\",\n          children: \"Chi ti\\u1EBFt c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setView('questionDetail'),\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md\\n   \".concat(view === 'questionDetail' ? 'bg-sky-600 hover:bg-sky-700 text-white' : 'bg-white text-black hover:bg-gray-200'),\n          children: \"C\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 13\n    }, this), view === 'questionDetail' && /*#__PURE__*/_jsxDEV(DetailQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 43\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 9\n  }, this);\n};\n_s2(LeftContent, \"wSdNEyYiwjPO5HRovtTjK+yTCo8=\");\n_c2 = LeftContent;\nexport default LeftContent;\nvar _c, _c2;\n$RefreshReg$(_c, \"DetailQuestionView\");\n$RefreshReg$(_c2, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useState", "useSelector", "useDispatch", "DropMenuBarAdmin", "SuggestInputBarAdmin", "TextArea", "ImageDropZone", "SolutionEditor", "setQuestion", "CheckCircle", "setQuestions", "jsxDEV", "_jsxDEV", "DetailQuestionView", "_s", "questionsExam", "selectedId", "view", "state", "codes", "dispatch", "prefixTN", "prefixDS", "question", "length", "selectedQuestion", "find", "q", "id", "handleQuestionChange", "e", "field", "updatedQuestion", "target", "value", "handleStatementChange", "index", "updatedStatements", "statements", "handleSolutionQuestionChange", "solution", "optionChapter", "setOptionChapter", "Array", "isArray", "class", "trim", "filter", "code", "startsWith", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selectedOption", "onChange", "option", "options", "chapter", "difficulty", "content", "placeholder", "label", "imageUrl", "onImageDrop", "image", "onImageRemove", "typeOfQuestion", "map", "statement", "<PERSON><PERSON><PERSON><PERSON>", "Icon", "onSolutionChange", "_c", "LeftContent", "_s2", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "concat", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/LeftContent.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport TextArea from \"src/components/input/TextArea\";\r\nimport ImageDropZone from \"src/components/image/ImageDropZone\";\r\nimport SolutionEditor from \"src/components/PageAddExam/SolutionEditor\";\r\nimport { setQuestion } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport { CheckCircle } from \"lucide-react\";\r\nimport { setQuestions } from \"src/features/questionsExam/questionsExamSlice\";\r\n\r\nconst DetailQuestionView = () => {\r\n    const { questionsExam, selectedId, view } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const dispatch = useDispatch();\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n\r\n    const [question, setQuestion] = useState(null);\r\n\r\n    useEffect(() => {\r\n        // console.log(selectedId);\r\n\r\n        if (selectedId && questionsExam?.length > 0) {\r\n            const selectedQuestion = questionsExam?.find(q => q.id === selectedId);\r\n            setQuestion(selectedQuestion);\r\n        }\r\n    }, [selectedId, questionsExam]);\r\n\r\n    const handleQuestionChange = (e, field) => {\r\n        const updatedQuestion = { ...question, [field]: e.target.value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleStatementChange = (index, value, field) => {\r\n        const updatedStatements = [...question.statements];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...question, statements: updatedStatements };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const handleSolutionQuestionChange = (value) => {\r\n        const updatedQuestion = { ...question, solution: value };\r\n        dispatch(setQuestions(updatedQuestion));\r\n    };\r\n\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (question?.class && question?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(question?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, question?.class]);\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            {question && (\r\n                <div className=\"space-y-3 w-full\">\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                        <div className=\"flex flex-row gap-2\">\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.class}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <SuggestInputBarAdmin\r\n                                selectedOption={question.chapter}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}\r\n                                options={optionChapter}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={question.difficulty}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                                options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\" bg-gray-200\"></hr>\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                        <div className=\"space-y-2\">\r\n                            <TextArea\r\n                                value={question.content}\r\n                                onChange={(e) => handleQuestionChange(e, 'content')}\r\n                                placeholder=\"Nhập nội dung câu hỏi\"\r\n                                label=\"Câu hỏi\"\r\n                            />\r\n                            {(view === 'image' || question.imageUrl) && (\r\n                                <ImageDropZone\r\n                                    imageUrl={question.imageUrl}\r\n                                    onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                                    onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                                />\r\n                            )}\r\n                            {question.typeOfQuestion !== 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    {question.statements.map((statement, index) => (\r\n                                        <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                            <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                                <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                    {question.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                                </p>\r\n                                                <TextArea\r\n                                                    value={statement.content}\r\n                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}\r\n                                                    placeholder=\"Nhập nội dung mệnh đề\"\r\n                                                />\r\n                                            </div>\r\n                                            {(view === 'image' || statement.imageUrl) && (\r\n                                                <ImageDropZone\r\n                                                    imageUrl={statement.imageUrl}\r\n                                                    onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}\r\n                                                    onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}\r\n                                                />\r\n                                            )}\r\n\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            )}\r\n                            {question.typeOfQuestion === 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    <TextArea\r\n                                        value={question.correctAnswer}\r\n                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}\r\n                                        placeholder=\"Nhập đáp án\"\r\n                                        label=\"Đáp án\"\r\n                                        Icon={CheckCircle}\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            {/* <TextArea\r\n                                        value={question.solution}\r\n                                        onChange={(e) => handleQuestionChange(e, 'solution')}\r\n                                        placeholder=\"Nhập lời giải\"\r\n                                        label=\"Lời giải\"\r\n                                        Icon={CheckCircle}\r\n                                    /> */}\r\n                            <SolutionEditor\r\n                                solution={question.solution}\r\n                                onSolutionChange={handleSolutionQuestionChange}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst LeftContent = () => {\r\n    const [view, setView] = useState('questionDetail');\r\n\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_138px)] overflow-y-auto\">\r\n            {/* Compact Preview Header */}\r\n            <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-3 py-2 h-10\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <h2 className=\"text-xs font-semibold text-gray-900\">Chi tiết câu hỏi</h2>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                    <button\r\n                        onClick={() => setView('questionDetail')}\r\n                        className={`text-xs flex items-center gap-2 px-2 py-1 rounded-md\r\n   ${view === 'questionDetail' ? 'bg-sky-600 hover:bg-sky-700 text-white' : 'bg-white text-black hover:bg-gray-200'}`}\r\n                    >\r\n                        Câu hỏi\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            {view === 'questionDetail' && <DetailQuestionView />}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nexport default LeftContent;"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SAASC,WAAW,QAAQ,+CAA+C;AAC3E,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,YAAY,QAAQ,+CAA+C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC,aAAa;IAAEC,UAAU;IAAEC;EAAK,CAAC,GAAGhB,WAAW,CAAEiB,KAAK,IAAKA,KAAK,CAACH,aAAa,CAAC;EACvF,MAAM;IAAEI;EAAM,CAAC,GAAGlB,WAAW,CAAEiB,KAAK,IAAKA,KAAK,CAACC,KAAK,CAAC;EACrD,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAE7E,MAAM,CAACC,QAAQ,EAAEf,WAAW,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACZ;;IAEA,IAAIiB,UAAU,IAAI,CAAAD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAES,MAAM,IAAG,CAAC,EAAE;MACzC,MAAMC,gBAAgB,GAAGV,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKZ,UAAU,CAAC;MACtER,WAAW,CAACiB,gBAAgB,CAAC;IACjC;EACJ,CAAC,EAAE,CAACT,UAAU,EAAED,aAAa,CAAC,CAAC;EAE/B,MAAMc,oBAAoB,GAAGA,CAACC,CAAC,EAAEC,KAAK,KAAK;IACvC,MAAMC,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAE,CAACQ,KAAK,GAAGD,CAAC,CAACG,MAAM,CAACC;IAAM,CAAC;IAChEd,QAAQ,CAACV,YAAY,CAACsB,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAACC,KAAK,EAAEF,KAAK,EAAEH,KAAK,KAAK;IACnD,MAAMM,iBAAiB,GAAG,CAAC,GAAGd,QAAQ,CAACe,UAAU,CAAC;IAClDD,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACL,KAAK,GAAGG;IAAM,CAAC;IAC1E,MAAMF,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEe,UAAU,EAAED;IAAkB,CAAC;IACtEjB,QAAQ,CAACV,YAAY,CAACsB,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMO,4BAA4B,GAAIL,KAAK,IAAK;IAC5C,MAAMF,eAAe,GAAG;MAAE,GAAGT,QAAQ;MAAEiB,QAAQ,EAAEN;IAAM,CAAC;IACxDd,QAAQ,CAACV,YAAY,CAACsB,eAAe,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAEtDD,SAAS,CAAC,MAAM;IACZ,IAAI4C,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAII,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEsB,KAAK,IAAI,CAAAtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,KAAK,CAACC,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QAClDJ,gBAAgB,CACZvB,KAAK,CAAC,SAAS,CAAC,CAAC4B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC1B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,KAAK,CAAC,IAAIG,IAAI,CAACA,IAAI,CAACxB,MAAM,KAAK,CAAC,CACrG,CAAC;MACL,CAAC,MAAM;QACHkB,gBAAgB,CAACvB,KAAK,CAAC,SAAS,CAAC,CAAC4B,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACxB,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHkB,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACvB,KAAK,EAAEI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,KAAK,CAAC,CAAC;EAE5B,oBACIjC,OAAA;IAAKsC,SAAS,EAAC,sBAAsB;IAAAC,QAAA,EAChC5B,QAAQ,iBACLX,OAAA;MAAKsC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BvC,OAAA;QAAKsC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCvC,OAAA;UAAIsC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE3C,OAAA;UAAKsC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChCvC,OAAA,CAACT,gBAAgB;YACbqD,cAAc,EAAEjC,QAAQ,CAACsB,KAAM;YAC/BY,QAAQ,EAAGC,MAAM,IAAK7B,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEwB;cAAO;YAAE,CAAC,EAAE,OAAO,CAAE;YACnFC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;YAC7D+B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF3C,OAAA,CAACR,oBAAoB;YACjBoD,cAAc,EAAEjC,QAAQ,CAACqC,OAAQ;YACjCH,QAAQ,EAAGC,MAAM,IAAK7B,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEwB;cAAO;YAAE,CAAC,EAAE,SAAS,CAAE;YACrFC,OAAO,EAAElB,aAAc;YACvBS,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF3C,OAAA,CAACT,gBAAgB;YACbqD,cAAc,EAAEjC,QAAQ,CAACsC,UAAW;YACpCJ,QAAQ,EAAGC,MAAM,IAAK7B,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEwB;cAAO;YAAE,CAAC,EAAE,YAAY,CAAE;YACxFC,OAAO,EAAEhB,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;YACvE+B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN3C,OAAA;QAAIsC,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClC3C,OAAA;QAAKsC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCvC,OAAA;UAAIsC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E3C,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBvC,OAAA,CAACP,QAAQ;YACL6B,KAAK,EAAEX,QAAQ,CAACuC,OAAQ;YACxBL,QAAQ,EAAG3B,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,SAAS,CAAE;YACpDiC,WAAW,EAAC,yCAAuB;YACnCC,KAAK,EAAC;UAAS;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EACD,CAACtC,IAAI,KAAK,OAAO,IAAIM,QAAQ,CAAC0C,QAAQ,kBACnCrD,OAAA,CAACN,aAAa;YACV2D,QAAQ,EAAE1C,QAAQ,CAAC0C,QAAS;YAC5BC,WAAW,EAAGC,KAAK,IAAKtC,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAEiC;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YACvFC,aAAa,EAAEA,CAAA,KAAMvC,oBAAoB,CAAC;cAAEI,MAAM,EAAE;gBAAEC,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CACJ,EACAhC,QAAQ,CAAC8C,cAAc,KAAK,KAAK,iBAC9BzD,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrB5B,QAAQ,CAACe,UAAU,CAACgC,GAAG,CAAC,CAACC,SAAS,EAAEnC,KAAK,kBACtCxB,OAAA;cAAiBsC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChEvC,OAAA;gBAAKsC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpDvC,OAAA;kBAAGsC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7C5B,QAAQ,CAAC8C,cAAc,KAAK,IAAI,GAAGhD,QAAQ,CAACe,KAAK,CAAC,GAAGd,QAAQ,CAACc,KAAK;gBAAC;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACJ3C,OAAA,CAACP,QAAQ;kBACL6B,KAAK,EAAEqC,SAAS,CAACT,OAAQ;kBACzBL,QAAQ,EAAG3B,CAAC,IAAKK,qBAAqB,CAACC,KAAK,EAAEN,CAAC,CAACG,MAAM,CAACC,KAAK,EAAE,SAAS,CAAE;kBACzE6B,WAAW,EAAC;gBAAuB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACL,CAACtC,IAAI,KAAK,OAAO,IAAIsD,SAAS,CAACN,QAAQ,kBACpCrD,OAAA,CAACN,aAAa;gBACV2D,QAAQ,EAAEM,SAAS,CAACN,QAAS;gBAC7BC,WAAW,EAAGC,KAAK,IAAKhC,qBAAqB,CAACC,KAAK,EAAE+B,KAAK,EAAE,UAAU,CAAE;gBACxEC,aAAa,EAAEA,CAAA,KAAMjC,qBAAqB,CAACC,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACJ;YAAA,GAjBKnB,KAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAhC,QAAQ,CAAC8C,cAAc,KAAK,KAAK,iBAC9BzD,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtBvC,OAAA,CAACP,QAAQ;cACL6B,KAAK,EAAEX,QAAQ,CAACiD,aAAc;cAC9Bf,QAAQ,EAAG3B,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE,eAAe,CAAE;cAC1DiC,WAAW,EAAC,6BAAa;cACzBC,KAAK,EAAC,mBAAQ;cACdS,IAAI,EAAEhE;YAAY;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAQD3C,OAAA,CAACL,cAAc;YACXiC,QAAQ,EAAEjB,QAAQ,CAACiB,QAAS;YAC5BkC,gBAAgB,EAAEnC;UAA6B;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAzC,EAAA,CAtJKD,kBAAkB;EAAA,QACwBZ,WAAW,EACrCA,WAAW,EACZC,WAAW;AAAA;AAAAyE,EAAA,GAH1B9D,kBAAkB;AAyJxB,MAAM+D,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM,CAAC5D,IAAI,EAAE6D,OAAO,CAAC,GAAG9E,QAAQ,CAAC,gBAAgB,CAAC;EAGlD,oBACIY,OAAA;IAAKsC,SAAS,EAAC,uDAAuD;IAAAC,QAAA,gBAElEvC,OAAA;MAAKsC,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBAC/FvC,OAAA;QAAKsC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCvC,OAAA;UAAIsC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACN3C,OAAA;QAAKsC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCvC,OAAA;UACImE,OAAO,EAAEA,CAAA,KAAMD,OAAO,CAAC,gBAAgB,CAAE;UACzC5B,SAAS,8DAAA8B,MAAA,CAC5B/D,IAAI,KAAK,gBAAgB,GAAG,wCAAwC,GAAG,uCAAuC,CAAG;UAAAkC,QAAA,EACjG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACLtC,IAAI,KAAK,gBAAgB,iBAAIL,OAAA,CAACC,kBAAkB;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnD,CAAC;AAEd,CAAC;AAAAsB,GAAA,CAxBKD,WAAW;AAAAK,GAAA,GAAXL,WAAW;AA2BjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAM,GAAA;AAAAC,YAAA,CAAAP,EAAA;AAAAO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}