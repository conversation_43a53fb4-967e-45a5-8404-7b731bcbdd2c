import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { debounce } from 'lodash';
import { X } from 'lucide-react';
import { findUsers } from '../features/user/userSlice';

/**
 * Component for searching and selecting user
 * 
 * @param {Object} props
 * @param {string} props.value - The current search term
 * @param {string} props.selectedUserId - The ID of the selected user
 * @param {function} props.onChange - Callback when search term changes
 * @param {function} props.onSelect - Callback when a user is selected
 * @param {function} props.onClear - Callback when selection is cleared
 * @param {string} props.placeholder - Placeholder text for the input
 * @param {string} props.className - Additional CSS classes
 */
const UserSearchInput = ({
    value = '',
    selectedUserId = '',
    onChange,
    onSelect,
    onClear,
    placeholder = 'Tìm kiếm người dùng...',
    className = 'w-full'
}) => {
    const dispatch = useDispatch();
    const [searchTerm, setSearchTerm] = useState(value);
    const [showDropdown, setShowDropdown] = useState(false);
    const { foundUsers } = useSelector((state) => state.users);
    const dropdownRef = useRef(null);

    // Handle user search with debounce
    const handleUserSearch = useCallback(
        debounce((searchTerm) => {
            dispatch(findUsers( searchTerm ))
        }, 1000),
        [dispatch]
    );

    // Handle input change
    const handleInputChange = (e) => {
        const value = e.target.value;
        setSearchTerm(value);
        if (onChange) onChange(value);
        handleUserSearch(value);
        setShowDropdown(true);
    };

    // Handle user selection
    const handleSelectUser = (userItem) => {
        if (onSelect) onSelect(userItem);
        setShowDropdown(false);
    };

    // Handle clear selection
    const handleClearSelection = () => {
        setSearchTerm('');
        if (onClear) onClear();
    };

    // Update local state when props change
    useEffect(() => {
        setSearchTerm(value);
    }, [value]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <div className={`relative ${className}`} ref={dropdownRef}>
            <input
                type="text"
                placeholder={placeholder}
                value={searchTerm}
                onChange={handleInputChange}
                onFocus={() => setShowDropdown(true)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
            {showDropdown && (
                foundUsers.length > 0 ? (
                    <div className="absolute z-60 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        {foundUsers.map((userItem) => (
                            <div
                                key={userItem.id}
                                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => handleSelectUser(userItem)}
                            >
                                <div className="font-medium">{userItem.lastName + " " + userItem.firstName}</div>
                                <div className="text-xs text-gray-500">
                                    Id: {userItem.id} | Khối {userItem.class} | Trường {userItem.highSchool} | PH: {userItem.phone}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="absolute z-60 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        <div className="px-4 py-2 text-gray-500">Không tìm thấy người dùng</div>
                    </div>
                )
            )}
            {selectedUserId && (
                <button
                    onClick={handleClearSelection}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                    <X size={16} />
                </button>
            )}
        </div>
    );
};

export default UserSearchInput;
