{"ast": null, "code": "import React, { createContext, useContext, useEffect, useState, useCallback, useMemo, useRef, memo, useReducer, cloneElement, forwardRef } from 'react';\nimport { createPortal, unstable_batchedUpdates } from 'react-dom';\nimport { useUniqueId, getEventCoordinates, getWindow, isDocument, isHTMLElement, isSVGElement, canUseDOM, isWindow, isNode, getOwnerDocument, add, isKeyboardEvent, subtract, useLazyMemo, useInterval, usePrevious, useLatestValue, useEvent, useIsomorphicLayoutEffect, useNodeRef, findFirstFocusableNode, CSS } from '@dnd-kit/utilities';\nimport { useAnnouncement, HiddenText, LiveRegion } from '@dnd-kit/accessibility';\nconst DndMonitorContext = /*#__PURE__*/createContext(null);\nfunction useDndMonitor(listener) {\n  const registerListener = useContext(DndMonitorContext);\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error('useDndMonitor must be used within a children of <DndContext>');\n    }\n    const unsubscribe = registerListener(listener);\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\nfunction useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set());\n  const registerListener = useCallback(listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  }, [listeners]);\n  const dispatch = useCallback(_ref => {\n    let {\n      type,\n      event\n    } = _ref;\n    listeners.forEach(listener => {\n      var _listener$type;\n      return (_listener$type = listener[type]) == null ? void 0 : _listener$type.call(listener, event);\n    });\n  }, [listeners]);\n  return [dispatch, registerListener];\n}\nconst defaultScreenReaderInstructions = {\n  draggable: \"\\n    To pick up a draggable item, press the space bar.\\n    While dragging, use the arrow keys to move the item.\\n    Press space again to drop the item in its new position, or press escape to cancel.\\n  \"\n};\nconst defaultAnnouncements = {\n  onDragStart(_ref) {\n    let {\n      active\n    } = _ref;\n    return \"Picked up draggable item \" + active.id + \".\";\n  },\n  onDragOver(_ref2) {\n    let {\n      active,\n      over\n    } = _ref2;\n    if (over) {\n      return \"Draggable item \" + active.id + \" was moved over droppable area \" + over.id + \".\";\n    }\n    return \"Draggable item \" + active.id + \" is no longer over a droppable area.\";\n  },\n  onDragEnd(_ref3) {\n    let {\n      active,\n      over\n    } = _ref3;\n    if (over) {\n      return \"Draggable item \" + active.id + \" was dropped over droppable area \" + over.id;\n    }\n    return \"Draggable item \" + active.id + \" was dropped.\";\n  },\n  onDragCancel(_ref4) {\n    let {\n      active\n    } = _ref4;\n    return \"Dragging was cancelled. Draggable item \" + active.id + \" was dropped.\";\n  }\n};\nfunction Accessibility(_ref) {\n  let {\n    announcements = defaultAnnouncements,\n    container,\n    hiddenTextDescribedById,\n    screenReaderInstructions = defaultScreenReaderInstructions\n  } = _ref;\n  const {\n    announce,\n    announcement\n  } = useAnnouncement();\n  const liveRegionId = useUniqueId(\"DndLiveRegion\");\n  const [mounted, setMounted] = useState(false);\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n  useDndMonitor(useMemo(() => ({\n    onDragStart(_ref2) {\n      let {\n        active\n      } = _ref2;\n      announce(announcements.onDragStart({\n        active\n      }));\n    },\n    onDragMove(_ref3) {\n      let {\n        active,\n        over\n      } = _ref3;\n      if (announcements.onDragMove) {\n        announce(announcements.onDragMove({\n          active,\n          over\n        }));\n      }\n    },\n    onDragOver(_ref4) {\n      let {\n        active,\n        over\n      } = _ref4;\n      announce(announcements.onDragOver({\n        active,\n        over\n      }));\n    },\n    onDragEnd(_ref5) {\n      let {\n        active,\n        over\n      } = _ref5;\n      announce(announcements.onDragEnd({\n        active,\n        over\n      }));\n    },\n    onDragCancel(_ref6) {\n      let {\n        active,\n        over\n      } = _ref6;\n      announce(announcements.onDragCancel({\n        active,\n        over\n      }));\n    }\n  }), [announce, announcements]));\n  if (!mounted) {\n    return null;\n  }\n  const markup = React.createElement(React.Fragment, null, React.createElement(HiddenText, {\n    id: hiddenTextDescribedById,\n    value: screenReaderInstructions.draggable\n  }), React.createElement(LiveRegion, {\n    id: liveRegionId,\n    announcement: announcement\n  }));\n  return container ? createPortal(markup, container) : markup;\n}\nvar Action;\n(function (Action) {\n  Action[\"DragStart\"] = \"dragStart\";\n  Action[\"DragMove\"] = \"dragMove\";\n  Action[\"DragEnd\"] = \"dragEnd\";\n  Action[\"DragCancel\"] = \"dragCancel\";\n  Action[\"DragOver\"] = \"dragOver\";\n  Action[\"RegisterDroppable\"] = \"registerDroppable\";\n  Action[\"SetDroppableDisabled\"] = \"setDroppableDisabled\";\n  Action[\"UnregisterDroppable\"] = \"unregisterDroppable\";\n})(Action || (Action = {}));\nfunction noop() {}\nfunction useSensor(sensor, options) {\n  return useMemo(() => ({\n    sensor,\n    options: options != null ? options : {}\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [sensor, options]);\n}\nfunction useSensors() {\n  for (var _len = arguments.length, sensors = new Array(_len), _key = 0; _key < _len; _key++) {\n    sensors[_key] = arguments[_key];\n  }\n  return useMemo(() => [...sensors].filter(sensor => sensor != null),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...sensors]);\n}\nconst defaultCoordinates = /*#__PURE__*/Object.freeze({\n  x: 0,\n  y: 0\n});\n\n/**\r\n * Returns the distance between two points\r\n */\nfunction distanceBetween(p1, p2) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\nfunction getRelativeTransformOrigin(event, rect) {\n  const eventCoordinates = getEventCoordinates(event);\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n  const transformOrigin = {\n    x: (eventCoordinates.x - rect.left) / rect.width * 100,\n    y: (eventCoordinates.y - rect.top) / rect.height * 100\n  };\n  return transformOrigin.x + \"% \" + transformOrigin.y + \"%\";\n}\n\n/**\r\n * Sort collisions from smallest to greatest value\r\n */\nfunction sortCollisionsAsc(_ref, _ref2) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref2;\n  return a - b;\n}\n/**\r\n * Sort collisions from greatest to smallest value\r\n */\n\nfunction sortCollisionsDesc(_ref3, _ref4) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref3;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref4;\n  return b - a;\n}\n/**\r\n * Returns the coordinates of the corners of a given rectangle:\r\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\r\n */\n\nfunction cornersOfRectangle(_ref5) {\n  let {\n    left,\n    top,\n    height,\n    width\n  } = _ref5;\n  return [{\n    x: left,\n    y: top\n  }, {\n    x: left + width,\n    y: top\n  }, {\n    x: left,\n    y: top + height\n  }, {\n    x: left + width,\n    y: top + height\n  }];\n}\nfunction getFirstCollision(collisions, property) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n  const [firstCollision] = collisions;\n  return property ? firstCollision[property] : firstCollision;\n}\n\n/**\r\n * Returns the coordinates of the center of a given ClientRect\r\n */\n\nfunction centerOfRectangle(rect, left, top) {\n  if (left === void 0) {\n    left = rect.left;\n  }\n  if (top === void 0) {\n    top = rect.top;\n  }\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5\n  };\n}\n/**\r\n * Returns the closest rectangles from an array of rectangles to the center of a given\r\n * rectangle.\r\n */\n\nconst closestCenter = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const centerRect = centerOfRectangle(collisionRect, collisionRect.left, collisionRect.top);\n  const collisions = [];\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: distBetween\n        }\n      });\n    }\n  }\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the closest rectangles from an array of rectangles to the corners of\r\n * another rectangle.\r\n */\n\nconst closestCorners = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions = [];\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the intersecting rectangle area between two rectangles\r\n */\n\nfunction getIntersectionRatio(entry, target) {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  } // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n\n  return 0;\n}\n/**\r\n * Returns the rectangles that has the greatest intersection area with a given\r\n * rectangle in an array of rectangles.\r\n */\n\nconst rectIntersection = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const collisions = [];\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {\n            droppableContainer,\n            value: intersectionRatio\n          }\n        });\n      }\n    }\n  }\n  return collisions.sort(sortCollisionsDesc);\n};\n\n/**\r\n * Check if a given point is contained within a bounding rectangle\r\n */\n\nfunction isPointWithinRect(point, rect) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = rect;\n  return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;\n}\n/**\r\n * Returns the rectangles that the pointer is hovering over\r\n */\n\nconst pointerWithin = _ref => {\n  let {\n    droppableContainers,\n    droppableRects,\n    pointerCoordinates\n  } = _ref;\n  if (!pointerCoordinates) {\n    return [];\n  }\n  const collisions = [];\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\r\n       * with the pointer coordinates. In order to sort the\r\n       * colliding rectangles, we measure the distance between\r\n       * the pointer and the corners of the intersecting rectangle\r\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n  return collisions.sort(sortCollisionsAsc);\n};\nfunction adjustScale(transform, rect1, rect2) {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1\n  };\n}\nfunction getRectDelta(rect1, rect2) {\n  return rect1 && rect2 ? {\n    x: rect1.left - rect2.left,\n    y: rect1.top - rect2.top\n  } : defaultCoordinates;\n}\nfunction createRectAdjustmentFn(modifier) {\n  return function adjustClientRect(rect) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n    return adjustments.reduce((acc, adjustment) => ({\n      ...acc,\n      top: acc.top + modifier * adjustment.y,\n      bottom: acc.bottom + modifier * adjustment.y,\n      left: acc.left + modifier * adjustment.x,\n      right: acc.right + modifier * adjustment.x\n    }), {\n      ...rect\n    });\n  };\n}\nconst getAdjustedRect = /*#__PURE__*/createRectAdjustmentFn(1);\nfunction parseTransform(transform) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n  return null;\n}\nfunction inverseTransform(rect, transform, transformOrigin) {\n  const parsedTransform = parseTransform(transform);\n  if (!parsedTransform) {\n    return rect;\n  }\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\nconst defaultOptions = {\n  ignoreTransform: false\n};\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n */\n\nfunction getClientRect(element, options) {\n  if (options === void 0) {\n    options = defaultOptions;\n  }\n  let rect = element.getBoundingClientRect();\n  if (options.ignoreTransform) {\n    const {\n      transform,\n      transformOrigin\n    } = getWindow(element).getComputedStyle(element);\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n  const {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  } = rect;\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  };\n}\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n *\r\n * @remarks\r\n * The ClientRect returned by this method does not take into account transforms\r\n * applied to the element it measures.\r\n *\r\n */\n\nfunction getTransformAgnosticClientRect(element) {\n  return getClientRect(element, {\n    ignoreTransform: true\n  });\n}\nfunction getWindowClientRect(element) {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\nfunction isFixed(node, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = getWindow(node).getComputedStyle(node);\n  }\n  return computedStyle.position === 'fixed';\n}\nfunction isScrollable(element, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = getWindow(element).getComputedStyle(element);\n  }\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\nfunction getScrollableAncestors(element, limit) {\n  const scrollParents = [];\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n    if (!node) {\n      return scrollParents;\n    }\n    if (isDocument(node) && node.scrollingElement != null && !scrollParents.includes(node.scrollingElement)) {\n      scrollParents.push(node.scrollingElement);\n      return scrollParents;\n    }\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n    const computedStyle = getWindow(element).getComputedStyle(node);\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n    return findScrollableAncestors(node.parentNode);\n  }\n  if (!element) {\n    return scrollParents;\n  }\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\nfunction getScrollableElement(element) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n  if (isWindow(element)) {\n    return element;\n  }\n  if (!isNode(element)) {\n    return null;\n  }\n  if (isDocument(element) || element === getOwnerDocument(element).scrollingElement) {\n    return window;\n  }\n  if (isHTMLElement(element)) {\n    return element;\n  }\n  return null;\n}\nfunction getScrollXCoordinate(element) {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n  return element.scrollLeft;\n}\nfunction getScrollYCoordinate(element) {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n  return element.scrollTop;\n}\nfunction getScrollCoordinates(element) {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element)\n  };\n}\nvar Direction;\n(function (Direction) {\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\nfunction isDocumentScrollingElement(element) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n  return element === document.scrollingElement;\n}\nfunction getScrollPosition(scrollingContainer) {\n  const minScroll = {\n    x: 0,\n    y: 0\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer) ? {\n    height: window.innerHeight,\n    width: window.innerWidth\n  } : {\n    height: scrollingContainer.clientHeight,\n    width: scrollingContainer.clientWidth\n  };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height\n  };\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll\n  };\n}\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nfunction getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, _ref, acceleration, thresholdPercentage) {\n  let {\n    top,\n    left,\n    right,\n    bottom\n  } = _ref;\n  if (acceleration === void 0) {\n    acceleration = 10;\n  }\n  if (thresholdPercentage === void 0) {\n    thresholdPercentage = defaultThreshold;\n  }\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollContainer);\n  const direction = {\n    x: 0,\n    y: 0\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold.height - top) / threshold.height);\n  } else if (!isBottom && bottom >= scrollContainerRect.bottom - threshold.height) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold.height - bottom) / threshold.height);\n  }\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold.width - right) / threshold.width);\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold.width - left) / threshold.width);\n  }\n  return {\n    direction,\n    speed\n  };\n}\nfunction getScrollElementRect(element) {\n  if (element === document.scrollingElement) {\n    const {\n      innerWidth,\n      innerHeight\n    } = window;\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight\n    };\n  }\n  const {\n    top,\n    left,\n    right,\n    bottom\n  } = element.getBoundingClientRect();\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight\n  };\n}\nfunction getScrollOffsets(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\nfunction getScrollXOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\nfunction getScrollYOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\nfunction scrollIntoViewIfNeeded(element, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n  if (!element) {\n    return;\n  }\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n  if (!firstScrollableAncestor) {\n    return;\n  }\n  if (bottom <= 0 || right <= 0 || top >= window.innerHeight || left >= window.innerWidth) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center'\n    });\n  }\n}\nconst properties = [['x', ['left', 'right'], getScrollXOffset], ['y', ['top', 'bottom'], getScrollYOffset]];\nclass Rect {\n  constructor(rect, element) {\n    this.rect = void 0;\n    this.width = void 0;\n    this.height = void 0;\n    this.top = void 0;\n    this.bottom = void 0;\n    this.right = void 0;\n    this.left = void 0;\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n    this.rect = {\n      ...rect\n    };\n    this.width = rect.width;\n    this.height = rect.height;\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true\n        });\n      }\n    }\n    Object.defineProperty(this, 'rect', {\n      enumerable: false\n    });\n  }\n}\nclass Listeners {\n  constructor(target) {\n    this.target = void 0;\n    this.listeners = [];\n    this.removeAll = () => {\n      this.listeners.forEach(listener => {\n        var _this$target;\n        return (_this$target = this.target) == null ? void 0 : _this$target.removeEventListener(...listener);\n      });\n    };\n    this.target = target;\n  }\n  add(eventName, handler, options) {\n    var _this$target2;\n    (_this$target2 = this.target) == null ? void 0 : _this$target2.addEventListener(eventName, handler, options);\n    this.listeners.push([eventName, handler, options]);\n  }\n}\nfunction getEventListenerTarget(target) {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n  const {\n    EventTarget\n  } = getWindow(target);\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\nfunction hasExceededDistance(delta, measurement) {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n  return false;\n}\nvar EventName;\n(function (EventName) {\n  EventName[\"Click\"] = \"click\";\n  EventName[\"DragStart\"] = \"dragstart\";\n  EventName[\"Keydown\"] = \"keydown\";\n  EventName[\"ContextMenu\"] = \"contextmenu\";\n  EventName[\"Resize\"] = \"resize\";\n  EventName[\"SelectionChange\"] = \"selectionchange\";\n  EventName[\"VisibilityChange\"] = \"visibilitychange\";\n})(EventName || (EventName = {}));\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\nvar KeyboardCode;\n(function (KeyboardCode) {\n  KeyboardCode[\"Space\"] = \"Space\";\n  KeyboardCode[\"Down\"] = \"ArrowDown\";\n  KeyboardCode[\"Right\"] = \"ArrowRight\";\n  KeyboardCode[\"Left\"] = \"ArrowLeft\";\n  KeyboardCode[\"Up\"] = \"ArrowUp\";\n  KeyboardCode[\"Esc\"] = \"Escape\";\n  KeyboardCode[\"Enter\"] = \"Enter\";\n  KeyboardCode[\"Tab\"] = \"Tab\";\n})(KeyboardCode || (KeyboardCode = {}));\nconst defaultKeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab]\n};\nconst defaultKeyboardCoordinateGetter = (event, _ref) => {\n  let {\n    currentCoordinates\n  } = _ref;\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25\n      };\n  }\n  return undefined;\n};\nclass KeyboardSensor {\n  constructor(props) {\n    this.props = void 0;\n    this.autoScrollEnabled = false;\n    this.referenceCoordinates = void 0;\n    this.listeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    const {\n      event: {\n        target\n      }\n    } = props;\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.attach();\n  }\n  attach() {\n    this.handleStart();\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n  handleStart() {\n    const {\n      activeNode,\n      onStart\n    } = this.props;\n    const node = activeNode.node.current;\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n    onStart(defaultCoordinates);\n  }\n  handleKeyDown(event) {\n    if (isKeyboardEvent(event)) {\n      const {\n        active,\n        context,\n        options\n      } = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth'\n      } = options;\n      const {\n        code\n      } = event;\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n      const {\n        collisionRect\n      } = context.current;\n      const currentCoordinates = collisionRect ? {\n        x: collisionRect.left,\n        y: collisionRect.top\n      } : defaultCoordinates;\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates\n      });\n      if (newCoordinates) {\n        const coordinatesDelta = subtract(newCoordinates, currentCoordinates);\n        const scrollDelta = {\n          x: 0,\n          y: 0\n        };\n        const {\n          scrollableAncestors\n        } = context.current;\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {\n            isTop,\n            isRight,\n            isLeft,\n            isBottom,\n            maxScroll,\n            minScroll\n          } = getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n          const clampedCoordinates = {\n            x: Math.min(direction === KeyboardCode.Right ? scrollElementRect.right - scrollElementRect.width / 2 : scrollElementRect.right, Math.max(direction === KeyboardCode.Right ? scrollElementRect.left : scrollElementRect.left + scrollElementRect.width / 2, newCoordinates.x)),\n            y: Math.min(direction === KeyboardCode.Down ? scrollElementRect.bottom - scrollElementRect.height / 2 : scrollElementRect.bottom, Math.max(direction === KeyboardCode.Down ? scrollElementRect.top : scrollElementRect.top + scrollElementRect.height / 2, newCoordinates.y))\n          };\n          const canScrollX = direction === KeyboardCode.Right && !isRight || direction === KeyboardCode.Left && !isLeft;\n          const canScrollY = direction === KeyboardCode.Down && !isBottom || direction === KeyboardCode.Up && !isTop;\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates = scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Right && newScrollCoordinates <= maxScroll.x || direction === KeyboardCode.Left && newScrollCoordinates >= minScroll.x;\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x = direction === KeyboardCode.Right ? scrollContainer.scrollLeft - maxScroll.x : scrollContainer.scrollLeft - minScroll.x;\n            }\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates = scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Down && newScrollCoordinates <= maxScroll.y || direction === KeyboardCode.Up && newScrollCoordinates >= minScroll.y;\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y = direction === KeyboardCode.Down ? scrollContainer.scrollTop - maxScroll.y : scrollContainer.scrollTop - minScroll.y;\n            }\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior\n              });\n            }\n            break;\n          }\n        }\n        this.handleMove(event, add(subtract(newCoordinates, this.referenceCoordinates), scrollDelta));\n      }\n    }\n  }\n  handleMove(event, coordinates) {\n    const {\n      onMove\n    } = this.props;\n    event.preventDefault();\n    onMove(coordinates);\n  }\n  handleEnd(event) {\n    const {\n      onEnd\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n  handleCancel(event) {\n    const {\n      onCancel\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n}\nKeyboardSensor.activators = [{\n  eventName: 'onKeyDown',\n  handler: (event, _ref, _ref2) => {\n    let {\n      keyboardCodes = defaultKeyboardCodes,\n      onActivation\n    } = _ref;\n    let {\n      active\n    } = _ref2;\n    const {\n      code\n    } = event.nativeEvent;\n    if (keyboardCodes.start.includes(code)) {\n      const activator = active.activatorNode.current;\n      if (activator && event.target !== activator) {\n        return false;\n      }\n      event.preventDefault();\n      onActivation == null ? void 0 : onActivation({\n        event: event.nativeEvent\n      });\n      return true;\n    }\n    return false;\n  }\n}];\nfunction isDistanceConstraint(constraint) {\n  return Boolean(constraint && 'distance' in constraint);\n}\nfunction isDelayConstraint(constraint) {\n  return Boolean(constraint && 'delay' in constraint);\n}\nclass AbstractPointerSensor {\n  constructor(props, events, listenerTarget) {\n    var _getEventCoordinates;\n    if (listenerTarget === void 0) {\n      listenerTarget = getEventListenerTarget(props.event.target);\n    }\n    this.props = void 0;\n    this.events = void 0;\n    this.autoScrollEnabled = true;\n    this.document = void 0;\n    this.activated = false;\n    this.initialCoordinates = void 0;\n    this.timeoutId = null;\n    this.listeners = void 0;\n    this.documentListeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    this.events = events;\n    const {\n      event\n    } = props;\n    const {\n      target\n    } = event;\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = (_getEventCoordinates = getEventCoordinates(event)) != null ? _getEventCoordinates : defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n    this.attach();\n  }\n  attach() {\n    const {\n      events,\n      props: {\n        options: {\n          activationConstraint,\n          bypassActivationConstraint\n        }\n      }\n    } = this;\n    this.listeners.add(events.move.name, this.handleMove, {\n      passive: false\n    });\n    this.listeners.add(events.end.name, this.handleEnd);\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n    if (activationConstraint) {\n      if (bypassActivationConstraint != null && bypassActivationConstraint({\n        event: this.props.event,\n        activeNode: this.props.activeNode,\n        options: this.props.options\n      })) {\n        return this.handleStart();\n      }\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(this.handleStart, activationConstraint.delay);\n        this.handlePending(activationConstraint);\n        return;\n      }\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n    this.handleStart();\n  }\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll(); // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n\n    setTimeout(this.documentListeners.removeAll, 50);\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n  handlePending(constraint, offset) {\n    const {\n      active,\n      onPending\n    } = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n  handleStart() {\n    const {\n      initialCoordinates\n    } = this;\n    const {\n      onStart\n    } = this.props;\n    if (initialCoordinates) {\n      this.activated = true; // Stop propagation of click events once activation constraints are met\n\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true\n      }); // Remove any text selection from the document\n\n      this.removeTextSelection(); // Prevent further text selection while dragging\n\n      this.documentListeners.add(EventName.SelectionChange, this.removeTextSelection);\n      onStart(initialCoordinates);\n    }\n  }\n  handleMove(event) {\n    var _getEventCoordinates2;\n    const {\n      activated,\n      initialCoordinates,\n      props\n    } = this;\n    const {\n      onMove,\n      options: {\n        activationConstraint\n      }\n    } = props;\n    if (!initialCoordinates) {\n      return;\n    }\n    const coordinates = (_getEventCoordinates2 = getEventCoordinates(event)) != null ? _getEventCoordinates2 : defaultCoordinates;\n    const delta = subtract(initialCoordinates, coordinates); // Constraint validation\n\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (activationConstraint.tolerance != null && hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    onMove(coordinates);\n  }\n  handleEnd() {\n    const {\n      onAbort,\n      onEnd\n    } = this.props;\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onEnd();\n  }\n  handleCancel() {\n    const {\n      onAbort,\n      onCancel\n    } = this.props;\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onCancel();\n  }\n  handleKeydown(event) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n  removeTextSelection() {\n    var _this$document$getSel;\n    (_this$document$getSel = this.document.getSelection()) == null ? void 0 : _this$document$getSel.removeAllRanges();\n  }\n}\nconst events = {\n  cancel: {\n    name: 'pointercancel'\n  },\n  move: {\n    name: 'pointermove'\n  },\n  end: {\n    name: 'pointerup'\n  }\n};\nclass PointerSensor extends AbstractPointerSensor {\n  constructor(props) {\n    const {\n      event\n    } = props; // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n\n    const listenerTarget = getOwnerDocument(event.target);\n    super(props, events, listenerTarget);\n  }\n}\nPointerSensor.activators = [{\n  eventName: 'onPointerDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    if (!event.isPrimary || event.button !== 0) {\n      return false;\n    }\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\nconst events$1 = {\n  move: {\n    name: 'mousemove'\n  },\n  end: {\n    name: 'mouseup'\n  }\n};\nvar MouseButton;\n(function (MouseButton) {\n  MouseButton[MouseButton[\"RightClick\"] = 2] = \"RightClick\";\n})(MouseButton || (MouseButton = {}));\nclass MouseSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$1, getOwnerDocument(props.event.target));\n  }\n}\nMouseSensor.activators = [{\n  eventName: 'onMouseDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    if (event.button === MouseButton.RightClick) {\n      return false;\n    }\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\nconst events$2 = {\n  cancel: {\n    name: 'touchcancel'\n  },\n  move: {\n    name: 'touchmove'\n  },\n  end: {\n    name: 'touchend'\n  }\n};\nclass TouchSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$2);\n  }\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events$2.move.name, noop, {\n      capture: false,\n      passive: false\n    });\n    return function teardown() {\n      window.removeEventListener(events$2.move.name, noop);\n    }; // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n\n    function noop() {}\n  }\n}\nTouchSensor.activators = [{\n  eventName: 'onTouchStart',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    const {\n      touches\n    } = event;\n    if (touches.length > 1) {\n      return false;\n    }\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\nvar AutoScrollActivator;\n(function (AutoScrollActivator) {\n  AutoScrollActivator[AutoScrollActivator[\"Pointer\"] = 0] = \"Pointer\";\n  AutoScrollActivator[AutoScrollActivator[\"DraggableRect\"] = 1] = \"DraggableRect\";\n})(AutoScrollActivator || (AutoScrollActivator = {}));\nvar TraversalOrder;\n(function (TraversalOrder) {\n  TraversalOrder[TraversalOrder[\"TreeOrder\"] = 0] = \"TreeOrder\";\n  TraversalOrder[TraversalOrder[\"ReversedTreeOrder\"] = 1] = \"ReversedTreeOrder\";\n})(TraversalOrder || (TraversalOrder = {}));\nfunction useAutoScroller(_ref) {\n  let {\n    acceleration,\n    activator = AutoScrollActivator.Pointer,\n    canScroll,\n    draggingRect,\n    enabled,\n    interval = 5,\n    order = TraversalOrder.TreeOrder,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    delta,\n    threshold\n  } = _ref;\n  const scrollIntent = useScrollIntent({\n    delta,\n    disabled: !enabled\n  });\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef({\n    x: 0,\n    y: 0\n  });\n  const scrollDirection = useRef({\n    x: 0,\n    y: 0\n  });\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates ? {\n          top: pointerCoordinates.y,\n          bottom: pointerCoordinates.y,\n          left: pointerCoordinates.x,\n          right: pointerCoordinates.x\n        } : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n    if (!scrollContainer) {\n      return;\n    }\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(() => order === TraversalOrder.TreeOrder ? [...scrollableAncestors].reverse() : scrollableAncestors, [order, scrollableAncestors]);\n  useEffect(() => {\n    if (!enabled || !scrollableAncestors.length || !rect) {\n      clearAutoScrollInterval();\n      return;\n    }\n    for (const scrollContainer of sortedScrollableAncestors) {\n      if ((canScroll == null ? void 0 : canScroll(scrollContainer)) === false) {\n        continue;\n      }\n      const index = scrollableAncestors.indexOf(scrollContainer);\n      const scrollContainerRect = scrollableAncestorRects[index];\n      if (!scrollContainerRect) {\n        continue;\n      }\n      const {\n        direction,\n        speed\n      } = getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, rect, acceleration, threshold);\n      for (const axis of ['x', 'y']) {\n        if (!scrollIntent[axis][direction[axis]]) {\n          speed[axis] = 0;\n          direction[axis] = 0;\n        }\n      }\n      if (speed.x > 0 || speed.y > 0) {\n        clearAutoScrollInterval();\n        scrollContainerRef.current = scrollContainer;\n        setAutoScrollInterval(autoScroll, interval);\n        scrollSpeed.current = speed;\n        scrollDirection.current = direction;\n        return;\n      }\n    }\n    scrollSpeed.current = {\n      x: 0,\n      y: 0\n    };\n    scrollDirection.current = {\n      x: 0,\n      y: 0\n    };\n    clearAutoScrollInterval();\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [acceleration, autoScroll, canScroll, clearAutoScrollInterval, enabled, interval,\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(rect),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(scrollIntent), setAutoScrollInterval, scrollableAncestors, sortedScrollableAncestors, scrollableAncestorRects,\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(threshold)]);\n}\nconst defaultScrollIntent = {\n  x: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  },\n  y: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  }\n};\nfunction useScrollIntent(_ref2) {\n  let {\n    delta,\n    disabled\n  } = _ref2;\n  const previousDelta = usePrevious(delta);\n  return useLazyMemo(previousIntent => {\n    if (disabled || !previousDelta || !previousIntent) {\n      // Reset scroll intent tracking when auto-scrolling is disabled\n      return defaultScrollIntent;\n    }\n    const direction = {\n      x: Math.sign(delta.x - previousDelta.x),\n      y: Math.sign(delta.y - previousDelta.y)\n    }; // Keep track of the user intent to scroll in each direction for both axis\n\n    return {\n      x: {\n        [Direction.Backward]: previousIntent.x[Direction.Backward] || direction.x === -1,\n        [Direction.Forward]: previousIntent.x[Direction.Forward] || direction.x === 1\n      },\n      y: {\n        [Direction.Backward]: previousIntent.y[Direction.Backward] || direction.y === -1,\n        [Direction.Forward]: previousIntent.y[Direction.Forward] || direction.y === 1\n      }\n    };\n  }, [disabled, delta, previousDelta]);\n}\nfunction useCachedNode(draggableNodes, id) {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n  return useLazyMemo(cachedNode => {\n    var _ref;\n    if (id == null) {\n      return null;\n    } // In some cases, the draggable node can unmount while dragging\n    // This is the case for virtualized lists. In those situations,\n    // we fall back to the last known value for that node.\n\n    return (_ref = node != null ? node : cachedNode) != null ? _ref : null;\n  }, [node, id]);\n}\nfunction useCombineActivators(sensors, getSyntheticHandler) {\n  return useMemo(() => sensors.reduce((accumulator, sensor) => {\n    const {\n      sensor: Sensor\n    } = sensor;\n    const sensorActivators = Sensor.activators.map(activator => ({\n      eventName: activator.eventName,\n      handler: getSyntheticHandler(activator.handler, sensor)\n    }));\n    return [...accumulator, ...sensorActivators];\n  }, []), [sensors, getSyntheticHandler]);\n}\nvar MeasuringStrategy;\n(function (MeasuringStrategy) {\n  MeasuringStrategy[MeasuringStrategy[\"Always\"] = 0] = \"Always\";\n  MeasuringStrategy[MeasuringStrategy[\"BeforeDragging\"] = 1] = \"BeforeDragging\";\n  MeasuringStrategy[MeasuringStrategy[\"WhileDragging\"] = 2] = \"WhileDragging\";\n})(MeasuringStrategy || (MeasuringStrategy = {}));\nvar MeasuringFrequency;\n(function (MeasuringFrequency) {\n  MeasuringFrequency[\"Optimized\"] = \"optimized\";\n})(MeasuringFrequency || (MeasuringFrequency = {}));\nconst defaultValue = /*#__PURE__*/new Map();\nfunction useDroppableMeasuring(containers, _ref) {\n  let {\n    dragging,\n    dependencies,\n    config\n  } = _ref;\n  const [queue, setQueue] = useState(null);\n  const {\n    frequency,\n    measure,\n    strategy\n  } = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(function (ids) {\n    if (ids === void 0) {\n      ids = [];\n    }\n    if (disabledRef.current) {\n      return;\n    }\n    setQueue(value => {\n      if (value === null) {\n        return ids;\n      }\n      return value.concat(ids.filter(id => !value.includes(id)));\n    });\n  }, [disabledRef]);\n  const timeoutId = useRef(null);\n  const droppableRects = useLazyMemo(previousValue => {\n    if (disabled && !dragging) {\n      return defaultValue;\n    }\n    if (!previousValue || previousValue === defaultValue || containersRef.current !== containers || queue != null) {\n      const map = new Map();\n      for (let container of containers) {\n        if (!container) {\n          continue;\n        }\n        if (queue && queue.length > 0 && !queue.includes(container.id) && container.rect.current) {\n          // This container does not need to be re-measured\n          map.set(container.id, container.rect.current);\n          continue;\n        }\n        const node = container.node.current;\n        const rect = node ? new Rect(measure(node), node) : null;\n        container.rect.current = rect;\n        if (rect) {\n          map.set(container.id, rect);\n        }\n      }\n      return map;\n    }\n    return previousValue;\n  }, [containers, queue, dragging, disabled, measure]);\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n    measureDroppableContainers();\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [dragging, disabled]);\n  useEffect(() => {\n    if (queue && queue.length > 0) {\n      setQueue(null);\n    }\n  },\n  //eslint-disable-next-line react-hooks/exhaustive-deps\n  [JSON.stringify(queue)]);\n  useEffect(() => {\n    if (disabled || typeof frequency !== 'number' || timeoutId.current !== null) {\n      return;\n    }\n    timeoutId.current = setTimeout(() => {\n      measureDroppableContainers();\n      timeoutId.current = null;\n    }, frequency);\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [frequency, disabled, measureDroppableContainers, ...dependencies]);\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null\n  };\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\nfunction useInitialValue(value, computeFn) {\n  return useLazyMemo(previousValue => {\n    if (!value) {\n      return null;\n    }\n    if (previousValue) {\n      return previousValue;\n    }\n    return typeof computeFn === 'function' ? computeFn(value) : value;\n  }, [computeFn, value]);\n}\nfunction useInitialRect(node, measure) {\n  return useInitialValue(node, measure);\n}\n\n/**\r\n * Returns a new MutationObserver instance.\r\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useMutationObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.MutationObserver === 'undefined') {\n      return undefined;\n    }\n    const {\n      MutationObserver\n    } = window;\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n  useEffect(() => {\n    return () => mutationObserver == null ? void 0 : mutationObserver.disconnect();\n  }, [mutationObserver]);\n  return mutationObserver;\n}\n\n/**\r\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\r\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useResizeObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.ResizeObserver === 'undefined') {\n      return undefined;\n    }\n    const {\n      ResizeObserver\n    } = window;\n    return new ResizeObserver(handleResize);\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [disabled]);\n  useEffect(() => {\n    return () => resizeObserver == null ? void 0 : resizeObserver.disconnect();\n  }, [resizeObserver]);\n  return resizeObserver;\n}\nfunction defaultMeasure(element) {\n  return new Rect(getClientRect(element), element);\n}\nfunction useRect(element, measure, fallbackRect) {\n  if (measure === void 0) {\n    measure = defaultMeasure;\n  }\n  const [rect, setRect] = useState(null);\n  function measureRect() {\n    setRect(currentRect => {\n      if (!element) {\n        return null;\n      }\n      if (element.isConnected === false) {\n        var _ref;\n\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return (_ref = currentRect != null ? currentRect : fallbackRect) != null ? _ref : null;\n      }\n      const newRect = measure(element);\n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n      return newRect;\n    });\n  }\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n      for (const record of records) {\n        const {\n          type,\n          target\n        } = record;\n        if (type === 'childList' && target instanceof HTMLElement && target.contains(element)) {\n          measureRect();\n          break;\n        }\n      }\n    }\n  });\n  const resizeObserver = useResizeObserver({\n    callback: measureRect\n  });\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n    if (element) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(element);\n      mutationObserver == null ? void 0 : mutationObserver.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    }\n  }, [element]);\n  return rect;\n}\nfunction useRectDelta(rect) {\n  const initialRect = useInitialValue(rect);\n  return getRectDelta(rect, initialRect);\n}\nconst defaultValue$1 = [];\nfunction useScrollableAncestors(node) {\n  const previousNode = useRef(node);\n  const ancestors = useLazyMemo(previousValue => {\n    if (!node) {\n      return defaultValue$1;\n    }\n    if (previousValue && previousValue !== defaultValue$1 && node && previousNode.current && node.parentNode === previousNode.current.parentNode) {\n      return previousValue;\n    }\n    return getScrollableAncestors(node);\n  }, [node]);\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n  return ancestors;\n}\nfunction useScrollOffsets(elements) {\n  const [scrollCoordinates, setScrollCoordinates] = useState(null);\n  const prevElements = useRef(elements); // To-do: Throttle the handleScroll callback\n\n  const handleScroll = useCallback(event => {\n    const scrollingElement = getScrollableElement(event.target);\n    if (!scrollingElement) {\n      return;\n    }\n    setScrollCoordinates(scrollCoordinates => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n      scrollCoordinates.set(scrollingElement, getScrollCoordinates(scrollingElement));\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n  useEffect(() => {\n    const previousElements = prevElements.current;\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n      const entries = elements.map(element => {\n        const scrollableElement = getScrollableElement(element);\n        if (scrollableElement) {\n          scrollableElement.addEventListener('scroll', handleScroll, {\n            passive: true\n          });\n          return [scrollableElement, getScrollCoordinates(scrollableElement)];\n        }\n        return null;\n      }).filter(entry => entry != null);\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n      prevElements.current = elements;\n    }\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n    function cleanup(elements) {\n      elements.forEach(element => {\n        const scrollableElement = getScrollableElement(element);\n        scrollableElement == null ? void 0 : scrollableElement.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates ? Array.from(scrollCoordinates.values()).reduce((acc, coordinates) => add(acc, coordinates), defaultCoordinates) : getScrollOffsets(elements);\n    }\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\nfunction useScrollOffsetsDelta(scrollOffsets, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [];\n  }\n  const initialScrollOffsets = useRef(null);\n  useEffect(() => {\n    initialScrollOffsets.current = null;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  dependencies);\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n  return initialScrollOffsets.current ? subtract(scrollOffsets, initialScrollOffsets.current) : defaultCoordinates;\n}\nfunction useSensorSetup(sensors) {\n  useEffect(() => {\n    if (!canUseDOM) {\n      return;\n    }\n    const teardownFns = sensors.map(_ref => {\n      let {\n        sensor\n      } = _ref;\n      return sensor.setup == null ? void 0 : sensor.setup();\n    });\n    return () => {\n      for (const teardown of teardownFns) {\n        teardown == null ? void 0 : teardown();\n      }\n    };\n  },\n  // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  sensors.map(_ref2 => {\n    let {\n      sensor\n    } = _ref2;\n    return sensor;\n  }));\n}\nfunction useSyntheticListeners(listeners, id) {\n  return useMemo(() => {\n    return listeners.reduce((acc, _ref) => {\n      let {\n        eventName,\n        handler\n      } = _ref;\n      acc[eventName] = event => {\n        handler(event, id);\n      };\n      return acc;\n    }, {});\n  }, [listeners, id]);\n}\nfunction useWindowRect(element) {\n  return useMemo(() => element ? getWindowClientRect(element) : null, [element]);\n}\nconst defaultValue$2 = [];\nfunction useRects(elements, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(firstElement ? getWindow(firstElement) : null);\n  const [rects, setRects] = useState(defaultValue$2);\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue$2;\n      }\n      return elements.map(element => isDocumentScrollingElement(element) ? windowRect : new Rect(measure(element), element));\n    });\n  }\n  const resizeObserver = useResizeObserver({\n    callback: measureRects\n  });\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n    measureRects();\n    elements.forEach(element => resizeObserver == null ? void 0 : resizeObserver.observe(element));\n  }, [elements]);\n  return rects;\n}\nfunction getMeasurableNode(node) {\n  if (!node) {\n    return null;\n  }\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\nfunction useDragOverlayMeasuring(_ref) {\n  let {\n    measure\n  } = _ref;\n  const [rect, setRect] = useState(null);\n  const handleResize = useCallback(entries => {\n    for (const {\n      target\n    } of entries) {\n      if (isHTMLElement(target)) {\n        setRect(rect => {\n          const newRect = measure(target);\n          return rect ? {\n            ...rect,\n            width: newRect.width,\n            height: newRect.height\n          } : newRect;\n        });\n        break;\n      }\n    }\n  }, [measure]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize\n  });\n  const handleNodeChange = useCallback(element => {\n    const node = getMeasurableNode(element);\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n    if (node) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(node);\n    }\n    setRect(node ? measure(node) : null);\n  }, [measure, resizeObserver]);\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n  return useMemo(() => ({\n    nodeRef,\n    rect,\n    setRef\n  }), [rect, nodeRef, setRef]);\n}\nconst defaultSensors = [{\n  sensor: PointerSensor,\n  options: {}\n}, {\n  sensor: KeyboardSensor,\n  options: {}\n}];\nconst defaultData = {\n  current: {}\n};\nconst defaultMeasuringConfiguration = {\n  draggable: {\n    measure: getTransformAgnosticClientRect\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized\n  },\n  dragOverlay: {\n    measure: getClientRect\n  }\n};\nclass DroppableContainersMap extends Map {\n  get(id) {\n    var _super$get;\n    return id != null ? (_super$get = super.get(id)) != null ? _super$get : undefined : undefined;\n  }\n  toArray() {\n    return Array.from(this.values());\n  }\n  getEnabled() {\n    return this.toArray().filter(_ref => {\n      let {\n        disabled\n      } = _ref;\n      return !disabled;\n    });\n  }\n  getNodeFor(id) {\n    var _this$get$node$curren, _this$get;\n    return (_this$get$node$curren = (_this$get = this.get(id)) == null ? void 0 : _this$get.node.current) != null ? _this$get$node$curren : undefined;\n  }\n}\nconst defaultPublicContext = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: /*#__PURE__*/new Map(),\n  droppableRects: /*#__PURE__*/new Map(),\n  droppableContainers: /*#__PURE__*/new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null\n    },\n    rect: null,\n    setRef: noop\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false\n};\nconst defaultInternalContext = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: ''\n  },\n  dispatch: noop,\n  draggableNodes: /*#__PURE__*/new Map(),\n  over: null,\n  measureDroppableContainers: noop\n};\nconst InternalContext = /*#__PURE__*/createContext(defaultInternalContext);\nconst PublicContext = /*#__PURE__*/createContext(defaultPublicContext);\nfunction getInitialState() {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {\n        x: 0,\n        y: 0\n      },\n      nodes: new Map(),\n      translate: {\n        x: 0,\n        y: 0\n      }\n    },\n    droppable: {\n      containers: new DroppableContainersMap()\n    }\n  };\n}\nfunction reducer(state, action) {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active\n        }\n      };\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y\n          }\n        }\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {\n            x: 0,\n            y: 0\n          },\n          translate: {\n            x: 0,\n            y: 0\n          }\n        }\n      };\n    case Action.RegisterDroppable:\n      {\n        const {\n          element\n        } = action;\n        const {\n          id\n        } = element;\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, element);\n        return {\n          ...state,\n          droppable: {\n            ...state.droppable,\n            containers\n          }\n        };\n      }\n    case Action.SetDroppableDisabled:\n      {\n        const {\n          id,\n          key,\n          disabled\n        } = action;\n        const element = state.droppable.containers.get(id);\n        if (!element || key !== element.key) {\n          return state;\n        }\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, {\n          ...element,\n          disabled\n        });\n        return {\n          ...state,\n          droppable: {\n            ...state.droppable,\n            containers\n          }\n        };\n      }\n    case Action.UnregisterDroppable:\n      {\n        const {\n          id,\n          key\n        } = action;\n        const element = state.droppable.containers.get(id);\n        if (!element || key !== element.key) {\n          return state;\n        }\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.delete(id);\n        return {\n          ...state,\n          droppable: {\n            ...state.droppable,\n            containers\n          }\n        };\n      }\n    default:\n      {\n        return state;\n      }\n  }\n}\nfunction RestoreFocus(_ref) {\n  let {\n    disabled\n  } = _ref;\n  const {\n    active,\n    activatorEvent,\n    draggableNodes\n  } = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active == null ? void 0 : active.id); // Restore keyboard focus on the activator node\n\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n      const draggableNode = draggableNodes.get(previousActiveId);\n      if (!draggableNode) {\n        return;\n      }\n      const {\n        activatorNode,\n        node\n      } = draggableNode;\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n          const focusableNode = findFirstFocusableNode(element);\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [activatorEvent, disabled, draggableNodes, previousActiveId, previousActivatorEvent]);\n  return null;\n}\nfunction applyModifiers(modifiers, _ref) {\n  let {\n    transform,\n    ...args\n  } = _ref;\n  return modifiers != null && modifiers.length ? modifiers.reduce((accumulator, modifier) => {\n    return modifier({\n      transform: accumulator,\n      ...args\n    });\n  }, transform) : transform;\n}\nfunction useMeasuringConfiguration(config) {\n  return useMemo(() => ({\n    draggable: {\n      ...defaultMeasuringConfiguration.draggable,\n      ...(config == null ? void 0 : config.draggable)\n    },\n    droppable: {\n      ...defaultMeasuringConfiguration.droppable,\n      ...(config == null ? void 0 : config.droppable)\n    },\n    dragOverlay: {\n      ...defaultMeasuringConfiguration.dragOverlay,\n      ...(config == null ? void 0 : config.dragOverlay)\n    }\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [config == null ? void 0 : config.draggable, config == null ? void 0 : config.droppable, config == null ? void 0 : config.dragOverlay]);\n}\nfunction useLayoutShiftScrollCompensation(_ref) {\n  let {\n    activeNode,\n    measure,\n    initialRect,\n    config = true\n  } = _ref;\n  const initialized = useRef(false);\n  const {\n    x,\n    y\n  } = typeof config === 'boolean' ? {\n    x: config,\n    y: config\n  } : config;\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    } // Get the most up to date node ref for the active draggable\n\n    const node = activeNode == null ? void 0 : activeNode.node.current;\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n    if (!x) {\n      rectDelta.x = 0;\n    }\n    if (!y) {\n      rectDelta.y = 0;\n    } // Only perform layout shift scroll compensation once\n\n    initialized.current = true;\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\nconst ActiveDraggableContext = /*#__PURE__*/createContext({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1\n});\nvar Status;\n(function (Status) {\n  Status[Status[\"Uninitialized\"] = 0] = \"Uninitialized\";\n  Status[Status[\"Initializing\"] = 1] = \"Initializing\";\n  Status[Status[\"Initialized\"] = 2] = \"Initialized\";\n})(Status || (Status = {}));\nconst DndContext = /*#__PURE__*/memo(function DndContext(_ref) {\n  var _sensorContext$curren, _dragOverlay$nodeRef$, _dragOverlay$rect, _over$rect;\n  let {\n    id,\n    accessibility,\n    autoScroll = true,\n    children,\n    sensors = defaultSensors,\n    collisionDetection = rectIntersection,\n    measuring,\n    modifiers,\n    ...props\n  } = _ref;\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] = useDndMonitorProvider();\n  const [status, setStatus] = useState(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {\n      active: activeId,\n      nodes: draggableNodes,\n      translate\n    },\n    droppable: {\n      containers: droppableContainers\n    }\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef({\n    initial: null,\n    translated: null\n  });\n  const active = useMemo(() => {\n    var _node$data;\n    return activeId != null ? {\n      id: activeId,\n      // It's possible for the active node to unmount while dragging\n      data: (_node$data = node == null ? void 0 : node.data) != null ? _node$data : defaultData,\n      rect: activeRects\n    } : null;\n  }, [activeId, node]);\n  const activeRef = useRef(null);\n  const [activeSensor, setActiveSensor] = useState(null);\n  const [activatorEvent, setActivatorEvent] = useState(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(\"DndDescribedBy\", id);\n  const enabledDroppableContainers = useMemo(() => droppableContainers.getEnabled(), [droppableContainers]);\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled\n  } = useDroppableMeasuring(enabledDroppableContainers, {\n    dragging: isInitialized,\n    dependencies: [translate.x, translate.y],\n    config: measuringConfiguration.droppable\n  });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(() => activatorEvent ? getEventCoordinates(activatorEvent) : null, [activatorEvent]);\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(activeNode, measuringConfiguration.draggable.measure);\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure\n  });\n  const activeNodeRect = useRect(activeNode, measuringConfiguration.draggable.measure, initialActiveNodeRect);\n  const containerNodeRect = useRect(activeNode ? activeNode.parentElement : null);\n  const sensorContext = useRef({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null\n  });\n  const overNode = droppableContainers.getNodeFor((_sensorContext$curren = sensorContext.current.over) == null ? void 0 : _sensorContext$curren.id);\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure\n  }); // Use the rect of the drag overlay if it is mounted\n\n  const draggingNode = (_dragOverlay$nodeRef$ = dragOverlay.nodeRef.current) != null ? _dragOverlay$nodeRef$ : activeNode;\n  const draggingNodeRect = isInitialized ? (_dragOverlay$rect = dragOverlay.rect) != null ? _dragOverlay$rect : activeNodeRect : null;\n  const usesDragOverlay = Boolean(dragOverlay.nodeRef.current && dragOverlay.rect); // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect); // Get the window rect of the dragging node\n\n  const windowRect = useWindowRect(draggingNode ? getWindow(draggingNode) : null); // Get scrollable ancestors of the dragging node\n\n  const scrollableAncestors = useScrollableAncestors(isInitialized ? overNode != null ? overNode : activeNode : null);\n  const scrollableAncestorRects = useRects(scrollableAncestors); // Apply modifiers\n\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  });\n  const pointerCoordinates = activationCoordinates ? add(activationCoordinates, translate) : null;\n  const scrollOffsets = useScrollOffsets(scrollableAncestors); // Represents the scroll delta since dragging was initiated\n\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets); // Represents the scroll delta since the last time the active node rect was measured\n\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [activeNodeRect]);\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n  const collisionRect = draggingNodeRect ? getAdjustedRect(draggingNodeRect, modifiedTranslate) : null;\n  const collisions = active && collisionRect ? collisionDetection({\n    active,\n    collisionRect,\n    droppableRects,\n    droppableContainers: enabledDroppableContainers,\n    pointerCoordinates\n  }) : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState(null); // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n\n  const appliedTranslate = usesDragOverlay ? modifiedTranslate : add(modifiedTranslate, activeNodeScrollDelta);\n  const transform = adjustScale(appliedTranslate, (_over$rect = over == null ? void 0 : over.rect) != null ? _over$rect : null, activeNodeRect);\n  const activeSensorRef = useRef(null);\n  const instantiateSensor = useCallback((event, _ref2) => {\n    let {\n      sensor: Sensor,\n      options\n    } = _ref2;\n    if (activeRef.current == null) {\n      return;\n    }\n    const activeNode = draggableNodes.get(activeRef.current);\n    if (!activeNode) {\n      return;\n    }\n    const activatorEvent = event.nativeEvent;\n    const sensorInstance = new Sensor({\n      active: activeRef.current,\n      activeNode,\n      event: activatorEvent,\n      options,\n      // Sensors need to be instantiated with refs for arguments that change over time\n      // otherwise they are frozen in time with the stale arguments\n      context: sensorContext,\n      onAbort(id) {\n        const draggableNode = draggableNodes.get(id);\n        if (!draggableNode) {\n          return;\n        }\n        const {\n          onDragAbort\n        } = latestProps.current;\n        const event = {\n          id\n        };\n        onDragAbort == null ? void 0 : onDragAbort(event);\n        dispatchMonitorEvent({\n          type: 'onDragAbort',\n          event\n        });\n      },\n      onPending(id, constraint, initialCoordinates, offset) {\n        const draggableNode = draggableNodes.get(id);\n        if (!draggableNode) {\n          return;\n        }\n        const {\n          onDragPending\n        } = latestProps.current;\n        const event = {\n          id,\n          constraint,\n          initialCoordinates,\n          offset\n        };\n        onDragPending == null ? void 0 : onDragPending(event);\n        dispatchMonitorEvent({\n          type: 'onDragPending',\n          event\n        });\n      },\n      onStart(initialCoordinates) {\n        const id = activeRef.current;\n        if (id == null) {\n          return;\n        }\n        const draggableNode = draggableNodes.get(id);\n        if (!draggableNode) {\n          return;\n        }\n        const {\n          onDragStart\n        } = latestProps.current;\n        const event = {\n          activatorEvent,\n          active: {\n            id,\n            data: draggableNode.data,\n            rect: activeRects\n          }\n        };\n        unstable_batchedUpdates(() => {\n          onDragStart == null ? void 0 : onDragStart(event);\n          setStatus(Status.Initializing);\n          dispatch({\n            type: Action.DragStart,\n            initialCoordinates,\n            active: id\n          });\n          dispatchMonitorEvent({\n            type: 'onDragStart',\n            event\n          });\n          setActiveSensor(activeSensorRef.current);\n          setActivatorEvent(activatorEvent);\n        });\n      },\n      onMove(coordinates) {\n        dispatch({\n          type: Action.DragMove,\n          coordinates\n        });\n      },\n      onEnd: createHandler(Action.DragEnd),\n      onCancel: createHandler(Action.DragCancel)\n    });\n    activeSensorRef.current = sensorInstance;\n    function createHandler(type) {\n      return async function handler() {\n        const {\n          active,\n          collisions,\n          over,\n          scrollAdjustedTranslate\n        } = sensorContext.current;\n        let event = null;\n        if (active && scrollAdjustedTranslate) {\n          const {\n            cancelDrop\n          } = latestProps.current;\n          event = {\n            activatorEvent,\n            active: active,\n            collisions,\n            delta: scrollAdjustedTranslate,\n            over\n          };\n          if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n            const shouldCancel = await Promise.resolve(cancelDrop(event));\n            if (shouldCancel) {\n              type = Action.DragCancel;\n            }\n          }\n        }\n        activeRef.current = null;\n        unstable_batchedUpdates(() => {\n          dispatch({\n            type\n          });\n          setStatus(Status.Uninitialized);\n          setOver(null);\n          setActiveSensor(null);\n          setActivatorEvent(null);\n          activeSensorRef.current = null;\n          const eventName = type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n          if (event) {\n            const handler = latestProps.current[eventName];\n            handler == null ? void 0 : handler(event);\n            dispatchMonitorEvent({\n              type: eventName,\n              event\n            });\n          }\n        });\n      };\n    }\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes]);\n  const bindActivatorToSensorInstantiator = useCallback((handler, sensor) => {\n    return (event, active) => {\n      const nativeEvent = event.nativeEvent;\n      const activeDraggableNode = draggableNodes.get(active);\n      if (\n      // Another sensor is already instantiating\n      activeRef.current !== null ||\n      // No active draggable\n      !activeDraggableNode ||\n      // Event has already been captured\n      nativeEvent.dndKit || nativeEvent.defaultPrevented) {\n        return;\n      }\n      const activationContext = {\n        active: activeDraggableNode\n      };\n      const shouldActivate = handler(event, sensor.options, activationContext);\n      if (shouldActivate === true) {\n        nativeEvent.dndKit = {\n          capturedBy: sensor.sensor\n        };\n        activeRef.current = active;\n        instantiateSensor(event, sensor);\n      }\n    };\n  }, [draggableNodes, instantiateSensor]);\n  const activators = useCombineActivators(sensors, bindActivatorToSensorInstantiator);\n  useSensorSetup(sensors);\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n  useEffect(() => {\n    const {\n      onDragMove\n    } = latestProps.current;\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      over\n    } = sensorContext.current;\n    if (!active || !activatorEvent) {\n      return;\n    }\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    unstable_batchedUpdates(() => {\n      onDragMove == null ? void 0 : onDragMove(event);\n      dispatchMonitorEvent({\n        type: 'onDragMove',\n        event\n      });\n    });\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]);\n  useEffect(() => {\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      droppableContainers,\n      scrollAdjustedTranslate\n    } = sensorContext.current;\n    if (!active || activeRef.current == null || !activatorEvent || !scrollAdjustedTranslate) {\n      return;\n    }\n    const {\n      onDragOver\n    } = latestProps.current;\n    const overContainer = droppableContainers.get(overId);\n    const over = overContainer && overContainer.rect.current ? {\n      id: overContainer.id,\n      rect: overContainer.rect.current,\n      data: overContainer.data,\n      disabled: overContainer.disabled\n    } : null;\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    unstable_batchedUpdates(() => {\n      setOver(over);\n      onDragOver == null ? void 0 : onDragOver(event);\n      dispatchMonitorEvent({\n        type: 'onDragOver',\n        event\n      });\n    });\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [overId]);\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate\n    };\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect\n    };\n  }, [active, activeNode, collisions, collisionRect, draggableNodes, draggingNode, draggingNodeRect, droppableRects, droppableContainers, over, scrollableAncestors, scrollAdjustedTranslate]);\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects\n  });\n  const publicContext = useMemo(() => {\n    const context = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect\n    };\n    return context;\n  }, [active, activeNode, activeNodeRect, activatorEvent, collisions, containerNodeRect, dragOverlay, draggableNodes, droppableContainers, droppableRects, over, measureDroppableContainers, scrollableAncestors, scrollableAncestorRects, measuringConfiguration, measuringScheduled, windowRect]);\n  const internalContext = useMemo(() => {\n    const context = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers\n    };\n    return context;\n  }, [activatorEvent, activators, active, activeNodeRect, dispatch, draggableDescribedById, draggableNodes, over, measureDroppableContainers]);\n  return React.createElement(DndMonitorContext.Provider, {\n    value: registerMonitorListener\n  }, React.createElement(InternalContext.Provider, {\n    value: internalContext\n  }, React.createElement(PublicContext.Provider, {\n    value: publicContext\n  }, React.createElement(ActiveDraggableContext.Provider, {\n    value: transform\n  }, children)), React.createElement(RestoreFocus, {\n    disabled: (accessibility == null ? void 0 : accessibility.restoreFocus) === false\n  })), React.createElement(Accessibility, {\n    ...accessibility,\n    hiddenTextDescribedById: draggableDescribedById\n  }));\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll = (activeSensor == null ? void 0 : activeSensor.autoScrollEnabled) === false;\n    const autoScrollGloballyDisabled = typeof autoScroll === 'object' ? autoScroll.enabled === false : autoScroll === false;\n    const enabled = isInitialized && !activeSensorDisablesAutoscroll && !autoScrollGloballyDisabled;\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled\n      };\n    }\n    return {\n      enabled\n    };\n  }\n});\nconst NullContext = /*#__PURE__*/createContext(null);\nconst defaultRole = 'button';\nconst ID_PREFIX = 'Draggable';\nfunction useDraggable(_ref) {\n  let {\n    id,\n    data,\n    disabled = false,\n    attributes\n  } = _ref;\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0\n  } = attributes != null ? attributes : {};\n  const isDragging = (active == null ? void 0 : active.id) === id;\n  const transform = useContext(isDragging ? ActiveDraggableContext : NullContext);\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n  useIsomorphicLayoutEffect(() => {\n    draggableNodes.set(id, {\n      id,\n      key,\n      node,\n      activatorNode,\n      data: dataRef\n    });\n    return () => {\n      const node = draggableNodes.get(id);\n      if (node && node.key === key) {\n        draggableNodes.delete(id);\n      }\n    };\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes, id]);\n  const memoizedAttributes = useMemo(() => ({\n    role,\n    tabIndex,\n    'aria-disabled': disabled,\n    'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n    'aria-roledescription': roleDescription,\n    'aria-describedby': ariaDescribedById.draggable\n  }), [disabled, role, tabIndex, isDragging, roleDescription, ariaDescribedById.draggable]);\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform\n  };\n}\nfunction useDndContext() {\n  return useContext(PublicContext);\n}\nconst ID_PREFIX$1 = 'Droppable';\nconst defaultResizeObserverConfig = {\n  timeout: 25\n};\nfunction useDroppable(_ref) {\n  let {\n    data,\n    disabled = false,\n    id,\n    resizeObserverConfig\n  } = _ref;\n  const key = useUniqueId(ID_PREFIX$1);\n  const {\n    active,\n    dispatch,\n    over,\n    measureDroppableContainers\n  } = useContext(InternalContext);\n  const previous = useRef({\n    disabled\n  });\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef(null);\n  const callbackId = useRef(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig\n  };\n  const ids = useLatestValue(updateMeasurementsFor != null ? updateMeasurementsFor : id);\n  const handleResize = useCallback(() => {\n    if (!resizeObserverConnected.current) {\n      // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n      // assuming the element is rendered and displayed.\n      resizeObserverConnected.current = true;\n      return;\n    }\n    if (callbackId.current != null) {\n      clearTimeout(callbackId.current);\n    }\n    callbackId.current = setTimeout(() => {\n      measureDroppableContainers(Array.isArray(ids.current) ? ids.current : [ids.current]);\n      callbackId.current = null;\n    }, resizeObserverTimeout);\n  },\n  //eslint-disable-next-line react-hooks/exhaustive-deps\n  [resizeObserverTimeout]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active\n  });\n  const handleNodeChange = useCallback((newElement, previousElement) => {\n    if (!resizeObserver) {\n      return;\n    }\n    if (previousElement) {\n      resizeObserver.unobserve(previousElement);\n      resizeObserverConnected.current = false;\n    }\n    if (newElement) {\n      resizeObserver.observe(newElement);\n    }\n  }, [resizeObserver]);\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n  useEffect(() => {\n    dispatch({\n      type: Action.RegisterDroppable,\n      element: {\n        id,\n        key,\n        disabled,\n        node: nodeRef,\n        rect,\n        data: dataRef\n      }\n    });\n    return () => dispatch({\n      type: Action.UnregisterDroppable,\n      key,\n      id\n    });\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [id]);\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled\n      });\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n  return {\n    active,\n    rect,\n    isOver: (over == null ? void 0 : over.id) === id,\n    node: nodeRef,\n    over,\n    setNodeRef\n  };\n}\nfunction AnimationManager(_ref) {\n  let {\n    animation,\n    children\n  } = _ref;\n  const [clonedChildren, setClonedChildren] = useState(null);\n  const [element, setElement] = useState(null);\n  const previousChildren = usePrevious(children);\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n    const key = clonedChildren == null ? void 0 : clonedChildren.key;\n    const id = clonedChildren == null ? void 0 : clonedChildren.props.id;\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n  return React.createElement(React.Fragment, null, children, clonedChildren ? cloneElement(clonedChildren, {\n    ref: setElement\n  }) : null);\n}\nconst defaultTransform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction NullifiedContextProvider(_ref) {\n  let {\n    children\n  } = _ref;\n  return React.createElement(InternalContext.Provider, {\n    value: defaultInternalContext\n  }, React.createElement(ActiveDraggableContext.Provider, {\n    value: defaultTransform\n  }, children));\n}\nconst baseStyles = {\n  position: 'fixed',\n  touchAction: 'none'\n};\nconst defaultTransition = activatorEvent => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\nconst PositionedOverlay = /*#__PURE__*/forwardRef((_ref, ref) => {\n  let {\n    as,\n    activatorEvent,\n    adjustScale,\n    children,\n    className,\n    rect,\n    style,\n    transform,\n    transition = defaultTransition\n  } = _ref;\n  if (!rect) {\n    return null;\n  }\n  const scaleAdjustedTransform = adjustScale ? transform : {\n    ...transform,\n    scaleX: 1,\n    scaleY: 1\n  };\n  const styles = {\n    ...baseStyles,\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    left: rect.left,\n    transform: CSS.Transform.toString(scaleAdjustedTransform),\n    transformOrigin: adjustScale && activatorEvent ? getRelativeTransformOrigin(activatorEvent, rect) : undefined,\n    transition: typeof transition === 'function' ? transition(activatorEvent) : transition,\n    ...style\n  };\n  return React.createElement(as, {\n    className,\n    style: styles,\n    ref\n  }, children);\n});\nconst defaultDropAnimationSideEffects = options => _ref => {\n  let {\n    active,\n    dragOverlay\n  } = _ref;\n  const originalStyles = {};\n  const {\n    styles,\n    className\n  } = options;\n  if (styles != null && styles.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n  if (styles != null && styles.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n  if (className != null && className.active) {\n    active.node.classList.add(className.active);\n  }\n  if (className != null && className.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n    if (className != null && className.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\nconst defaultKeyframeResolver = _ref2 => {\n  let {\n    transform: {\n      initial,\n      final\n    }\n  } = _ref2;\n  return [{\n    transform: CSS.Transform.toString(initial)\n  }, {\n    transform: CSS.Transform.toString(final)\n  }];\n};\nconst defaultDropAnimationConfiguration = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: /*#__PURE__*/defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0'\n      }\n    }\n  })\n};\nfunction useDropAnimation(_ref3) {\n  let {\n    config,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  } = _ref3;\n  return useEvent((id, node) => {\n    if (config === null) {\n      return;\n    }\n    const activeDraggable = draggableNodes.get(id);\n    if (!activeDraggable) {\n      return;\n    }\n    const activeNode = activeDraggable.node.current;\n    if (!activeNode) {\n      return;\n    }\n    const measurableNode = getMeasurableNode(node);\n    if (!measurableNode) {\n      return;\n    }\n    const {\n      transform\n    } = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n    if (!parsedTransform) {\n      return;\n    }\n    const animation = typeof config === 'function' ? config : createDefaultDropAnimation(config);\n    scrollIntoViewIfNeeded(activeNode, measuringConfiguration.draggable.measure);\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode)\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode)\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform\n    });\n  });\n}\nfunction createDefaultDropAnimation(options) {\n  const {\n    duration,\n    easing,\n    sideEffects,\n    keyframes\n  } = {\n    ...defaultDropAnimationConfiguration,\n    ...options\n  };\n  return _ref4 => {\n    let {\n      active,\n      dragOverlay,\n      transform,\n      ...rest\n    } = _ref4;\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top\n    };\n    const scale = {\n      scaleX: transform.scaleX !== 1 ? active.rect.width * transform.scaleX / dragOverlay.rect.width : 1,\n      scaleY: transform.scaleY !== 1 ? active.rect.height * transform.scaleY / dragOverlay.rect.height : 1\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale\n    };\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {\n        initial: transform,\n        final: finalTransform\n      }\n    });\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n    const cleanup = sideEffects == null ? void 0 : sideEffects({\n      active,\n      dragOverlay,\n      ...rest\n    });\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards'\n    });\n    return new Promise(resolve => {\n      animation.onfinish = () => {\n        cleanup == null ? void 0 : cleanup();\n        resolve();\n      };\n    });\n  };\n}\nlet key = 0;\nfunction useKey(id) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n    key++;\n    return key;\n  }, [id]);\n}\nconst DragOverlay = /*#__PURE__*/React.memo(_ref => {\n  let {\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999\n  } = _ref;\n  const {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggableNodes,\n    droppableContainers,\n    dragOverlay,\n    over,\n    measuringConfiguration,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  } = useDndContext();\n  const transform = useContext(ActiveDraggableContext);\n  const key = useKey(active == null ? void 0 : active.id);\n  const modifiedTransform = applyModifiers(modifiers, {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect: dragOverlay.rect,\n    over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    transform,\n    windowRect\n  });\n  const initialRect = useInitialValue(activeNodeRect);\n  const dropAnimation = useDropAnimation({\n    config: dropAnimationConfig,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  }); // We need to wait for the active node to be measured before connecting the drag overlay ref\n  // otherwise collisions can be computed against a mispositioned drag overlay\n\n  const ref = initialRect ? dragOverlay.setRef : undefined;\n  return React.createElement(NullifiedContextProvider, null, React.createElement(AnimationManager, {\n    animation: dropAnimation\n  }, active && key ? React.createElement(PositionedOverlay, {\n    key: key,\n    id: active.id,\n    ref: ref,\n    as: wrapperElement,\n    activatorEvent: activatorEvent,\n    adjustScale: adjustScale,\n    className: className,\n    transition: transition,\n    rect: initialRect,\n    style: {\n      zIndex,\n      ...style\n    },\n    transform: modifiedTransform\n  }, children) : null));\n});\nexport { AutoScrollActivator, DndContext, DragOverlay, KeyboardCode, KeyboardSensor, MeasuringFrequency, MeasuringStrategy, MouseSensor, PointerSensor, TouchSensor, TraversalOrder, applyModifiers, closestCenter, closestCorners, defaultAnnouncements, defaultCoordinates, defaultDropAnimationConfiguration as defaultDropAnimation, defaultDropAnimationSideEffects, defaultKeyboardCoordinateGetter, defaultScreenReaderInstructions, getClientRect, getFirstCollision, getScrollableAncestors, pointerWithin, rectIntersection, useDndContext, useDndMonitor, useDraggable, useDroppable, useSensor, useSensors };", "map": {"version": 3, "names": ["DndMonitorContext", "createContext", "useDndMonitor", "listener", "registerListener", "useContext", "useEffect", "Error", "unsubscribe", "useDndMonitorProvider", "listeners", "useState", "Set", "useCallback", "add", "delete", "dispatch", "_ref", "type", "event", "for<PERSON>ach", "_listener$type", "call", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "onDragStart", "active", "id", "onDragOver", "_ref2", "over", "onDragEnd", "_ref3", "onDragCancel", "_ref4", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "announcement", "useAnnouncement", "liveRegionId", "useUniqueId", "mounted", "setMounted", "useMemo", "onDragMove", "_ref5", "_ref6", "markup", "React", "createElement", "Fragment", "HiddenText", "value", "LiveRegion", "createPortal", "Action", "noop", "useSensor", "sensor", "options", "useSensors", "sensors", "Array", "_len", "_key", "arguments", "filter", "defaultCoordinates", "Object", "freeze", "x", "y", "distanceBetween", "p1", "p2", "Math", "sqrt", "pow", "getRelativeTransformOrigin", "rect", "eventCoordinates", "getEventCoordinates", "transform<PERSON><PERSON>in", "left", "width", "top", "height", "sortCollisionsAsc", "data", "a", "b", "sortCollisionsDesc", "cornersOfRectangle", "getFirstCollision", "collisions", "property", "length", "firstCollision", "centerOfRectangle", "closestCenter", "collisionRect", "droppableRects", "droppableContainers", "centerRect", "droppableContainer", "get", "distBetween", "push", "sort", "closestCorners", "corners", "rectCorners", "distances", "reduce", "accumulator", "corner", "index", "effectiveDistance", "Number", "toFixed", "getIntersectionRatio", "entry", "target", "max", "right", "min", "bottom", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "rectIntersection", "isPointWithinRect", "point", "pointer<PERSON><PERSON><PERSON>", "pointerCoordinates", "adjustScale", "transform", "rect1", "rect2", "scaleX", "scaleY", "getRectDelta", "createRectAdjustmentFn", "modifier", "adjustClientRect", "adjustments", "acc", "adjustment", "getAdjustedRect", "parseTransform", "startsWith", "transformArray", "slice", "split", "inverseTransform", "parsedTransform", "translateX", "translateY", "parseFloat", "indexOf", "w", "h", "defaultOptions", "ignoreTransform", "getClientRect", "element", "getBoundingClientRect", "getWindow", "getComputedStyle", "getTransformAgnosticClientRect", "getWindowClientRect", "innerWidth", "innerHeight", "isFixed", "node", "computedStyle", "position", "isScrollable", "overflowRegex", "properties", "some", "test", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "isDocument", "scrollingElement", "includes", "isHTMLElement", "isSVGElement", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "canUseDOM", "isWindow", "isNode", "getOwnerDocument", "window", "getScrollXCoordinate", "scrollX", "scrollLeft", "getScrollYCoordinate", "scrollY", "scrollTop", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "document", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "clientHeight", "clientWidth", "maxScroll", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "Backward", "abs", "Forward", "getScrollElementRect", "getScrollOffsets", "scrollableAncestors", "getScrollXOffset", "getScrollYOffset", "scrollIntoViewIfNeeded", "measure", "scrollIntoView", "block", "inline", "Rect", "constructor", "scrollOffsets", "axis", "keys", "getScrollOffset", "key", "defineProperty", "currentOffsets", "scrollOffsetsDeltla", "enumerable", "Listeners", "removeAll", "_this$target", "removeEventListener", "eventName", "handler", "_this$target2", "addEventListener", "getEventListenerTarget", "EventTarget", "hasExceededDistance", "delta", "measurement", "dx", "dy", "EventName", "preventDefault", "stopPropagation", "KeyboardCode", "defaultKeyboardCodes", "start", "Space", "Enter", "cancel", "Esc", "end", "Tab", "defaultKeyboardCoordinateGetter", "currentCoordinates", "code", "Right", "Left", "Down", "Up", "undefined", "KeyboardSensor", "props", "autoScrollEnabled", "referenceCoordinates", "windowListeners", "handleKeyDown", "bind", "handleCancel", "attach", "handleStart", "Resize", "VisibilityChange", "setTimeout", "Keydown", "activeNode", "onStart", "current", "isKeyboardEvent", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "handleEnd", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "subtract", "scrollDelta", "scrollElementRect", "clampedCoordinates", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "scrollTo", "behavior", "scrollBy", "handleMove", "coordinates", "onMove", "onEnd", "detach", "onCancel", "activators", "onActivation", "nativeEvent", "activator", "activatorNode", "isDistanceConstraint", "constraint", "Boolean", "isDelayConstraint", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "activated", "initialCoordinates", "timeoutId", "documentListeners", "_getEventCoordinates", "handleKeydown", "removeTextSelection", "activationConstraint", "bypassActivationConstraint", "move", "name", "passive", "DragStart", "ContextMenu", "delay", "handlePending", "clearTimeout", "offset", "onPending", "Click", "capture", "SelectionChange", "_getEventCoordinates2", "tolerance", "distance", "cancelable", "onAbort", "_this$document$getSel", "getSelection", "removeAllRanges", "PointerSensor", "isPrimary", "button", "events$1", "MouseB<PERSON>on", "MouseSensor", "RightClick", "events$2", "TouchSensor", "setup", "teardown", "touches", "AutoScrollActivator", "TraversalOrder", "useAutoScroller", "Pointer", "canScroll", "draggingRect", "enabled", "interval", "order", "TreeOrder", "scrollableAncestorRects", "scrollIntent", "useScrollIntent", "disabled", "setAutoScrollInterval", "clearAutoScrollInterval", "useInterval", "scrollSpeed", "useRef", "scrollDirection", "DraggableRect", "scrollContainerRef", "autoScroll", "sortedScrollableAncestors", "reverse", "JSON", "stringify", "defaultScrollIntent", "previousDel<PERSON>", "usePrevious", "useLazyMemo", "previousIntent", "sign", "useCachedNode", "draggableNodes", "draggableNode", "cachedNode", "useCombineActivators", "getSyntheticHandler", "Sensor", "sensorActivators", "map", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "Map", "useDroppableMeasuring", "containers", "dragging", "dependencies", "config", "queue", "setQueue", "frequency", "strategy", "containersRef", "isDisabled", "disabledRef", "useLatestValue", "measureDroppableContainers", "ids", "concat", "previousValue", "set", "measuringScheduled", "Always", "BeforeDragging", "useInitialValue", "computeFn", "useInitialRect", "useMutationObserver", "callback", "handleMutations", "useEvent", "mutationObserver", "MutationObserver", "disconnect", "useResizeObserver", "handleResize", "resizeObserver", "ResizeObserver", "defaultMeasure", "useRect", "fallbackRect", "setRect", "measureRect", "currentRect", "isConnected", "newRect", "records", "record", "HTMLElement", "contains", "useIsomorphicLayoutEffect", "observe", "body", "childList", "subtree", "useRectDelta", "initialRect", "defaultValue$1", "useScrollableAncestors", "previousNode", "ancestors", "useScrollOffsets", "elements", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "previousElements", "cleanup", "entries", "scrollableElement", "from", "values", "useScrollOffsetsDelta", "initialScrollOffsets", "hasScrollOffsets", "useSensorSetup", "teardownFns", "useSyntheticListeners", "useWindowRect", "defaultValue$2", "useRects", "firstElement", "windowRect", "rects", "setRects", "measureRects", "getMeasurableNode", "children", "<PERSON><PERSON><PERSON><PERSON>", "useDragOverlayMeasuring", "handleNodeChange", "nodeRef", "setRef", "useNodeRef", "defaultSensors", "defaultData", "defaultMeasuringConfiguration", "droppable", "WhileDragging", "Optimized", "dragOverlay", "DroppableContainersMap", "_super$get", "toArray", "getEnabled", "getNodeFor", "_this$get$node$curren", "_this$get", "defaultPublicContext", "activatorEvent", "activeNodeRect", "containerNodeRect", "measuringConfiguration", "defaultInternalContext", "ariaDescribedById", "InternalContext", "PublicContext", "getInitialState", "nodes", "translate", "reducer", "state", "action", "<PERSON><PERSON><PERSON><PERSON>", "DragEnd", "DragCancel", "RegisterDroppable", "SetDroppableDisabled", "UnregisterDroppable", "RestoreFocus", "previousActivatorEvent", "previousActiveId", "activeElement", "requestAnimationFrame", "focusableNode", "findFirstFocusableNode", "focus", "applyModifiers", "modifiers", "args", "useMeasuringConfiguration", "useLayoutShiftScrollCompensation", "initialized", "rectD<PERSON><PERSON>", "ActiveDraggableContext", "Status", "DndContext", "memo", "accessibility", "collisionDetection", "measuring", "store", "useReducer", "dispatchMonitorEvent", "registerMonitorListener", "status", "setStatus", "Uninitialized", "isInitialized", "Initialized", "activeId", "activeRects", "initial", "translated", "_node$data", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "draggableDescribedById", "enabledDroppableContainers", "activationCoordinates", "autoScrollOptions", "getAutoScrollerOptions", "initialActiveNodeRect", "layoutShiftCompensation", "parentElement", "sensorContext", "draggingNode", "draggingNodeRect", "scrollAdjustedTranslate", "overNode", "_sensorContext$curren", "_dragOverlay$nodeRef$", "_dragOverlay$rect", "usesDragOverlay", "nodeRectDelta", "modifiedTranslate", "overlayNodeRect", "scrollAdjustment", "activeNodeScrollDelta", "overId", "setOver", "appliedTranslate", "_over$rect", "activeSensorRef", "instantiateSensor", "sensorInstance", "onDragAbort", "onDragPending", "unstable_batchedUpdates", "Initializing", "createHandler", "cancelDrop", "shouldCancel", "Promise", "resolve", "bindActivatorToSensorInstantiator", "activeDraggableNode", "dndKit", "defaultPrevented", "activationContext", "shouldActivate", "capturedBy", "over<PERSON><PERSON><PERSON>", "publicContext", "internalContext", "Provider", "restoreFocus", "activeSensorDisablesAutoscroll", "autoScrollGloballyDisabled", "NullContext", "defaultRole", "ID_PREFIX", "useDraggable", "attributes", "role", "roleDescription", "tabIndex", "isDragging", "setNodeRef", "setActivatorNodeRef", "dataRef", "memoizedAttributes", "useDndContext", "ID_PREFIX$1", "defaultResizeObserverConfig", "timeout", "useDroppable", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "isArray", "newElement", "previousElement", "unobserve", "isOver", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "cloneElement", "ref", "defaultTransform", "NullifiedContextProvider", "baseStyles", "touchAction", "defaultTransition", "isKeyboardActivator", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "CSS", "Transform", "toString", "defaultDropAnimationSideEffects", "originalStyles", "getPropertyValue", "setProperty", "classList", "remove", "defaultKeyframeResolver", "final", "defaultDropAnimationConfiguration", "duration", "easing", "keyframes", "sideEffects", "opacity", "useDropAnimation", "activeDraggable", "measurableNode", "createDefaultDropAnimation", "rest", "scale", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "animate", "fill", "onfinish", "useKey", "DragOverlay", "dropAnimation", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform"], "sources": ["C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DndMonitor\\context.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DndMonitor\\useDndMonitor.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DndMonitor\\useDndMonitorProvider.tsx", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\Accessibility\\defaults.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\Accessibility\\Accessibility.tsx", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\store\\actions.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\other\\noop.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\useSensor.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\useSensors.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\coordinates\\constants.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\coordinates\\distanceBetweenPoints.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\coordinates\\getRelativeTransformOrigin.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\algorithms\\helpers.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\algorithms\\closestCenter.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\algorithms\\closestCorners.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\algorithms\\rectIntersection.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\algorithms\\pointerWithin.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\rect\\adjustScale.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\rect\\getRectDelta.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\rect\\rectAdjustment.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\transform\\parseTransform.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\transform\\inverseTransform.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\rect\\getRect.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\rect\\getWindowClientRect.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\isFixed.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\isScrollable.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\getScrollableAncestors.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\getScrollableElement.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\getScrollCoordinates.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\types\\direction.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\documentScrollingElement.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\getScrollPosition.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\getScrollDirectionAndSpeed.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\getScrollElementRect.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\getScrollOffsets.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\scroll\\scrollIntoViewIfNeeded.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\rect\\Rect.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\utilities\\Listeners.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\utilities\\getEventListenerTarget.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\utilities\\hasExceededDistance.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\events.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\keyboard\\types.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\keyboard\\defaults.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\keyboard\\KeyboardSensor.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\pointer\\AbstractPointerSensor.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\pointer\\PointerSensor.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\mouse\\MouseSensor.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\sensors\\touch\\TouchSensor.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useAutoScroller.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useCachedNode.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useCombineActivators.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useDroppableMeasuring.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useInitialValue.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useInitialRect.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useMutationObserver.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useResizeObserver.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useRect.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useRectDelta.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useScrollableAncestors.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useScrollOffsets.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useScrollOffsetsDelta.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useSensorSetup.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useSyntheticListeners.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useWindowRect.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useRects.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\utilities\\nodes\\getMeasurableNode.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\utilities\\useDragOverlayMeasuring.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DndContext\\defaults.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\store\\constructors.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\store\\context.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\store\\reducer.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\Accessibility\\components\\RestoreFocus.tsx", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\modifiers\\applyModifiers.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DndContext\\hooks\\useMeasuringConfiguration.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DndContext\\hooks\\useLayoutShiftScrollCompensation.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DndContext\\DndContext.tsx", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\useDraggable.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\useDndContext.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\hooks\\useDroppable.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DragOverlay\\components\\AnimationManager\\AnimationManager.tsx", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DragOverlay\\components\\NullifiedContextProvider\\NullifiedContextProvider.tsx", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DragOverlay\\components\\PositionedOverlay\\PositionedOverlay.tsx", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DragOverlay\\hooks\\useDropAnimation.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DragOverlay\\hooks\\useKey.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\core\\src\\components\\DragOverlay\\DragOverlay.tsx"], "sourcesContent": ["import {createContext} from 'react';\n\nimport type {RegisterListener} from './types';\n\nexport const DndMonitorContext = createContext<RegisterListener | null>(null);\n", "import {useContext, useEffect} from 'react';\n\nimport {DndMonitorContext} from './context';\nimport type {DndMonitorListener} from './types';\n\nexport function useDndMonitor(listener: DndMonitorListener) {\n  const registerListener = useContext(DndMonitorContext);\n\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error(\n        'useDndMonitor must be used within a children of <DndContext>'\n      );\n    }\n\n    const unsubscribe = registerListener(listener);\n\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n", "import {useCallback, useState} from 'react';\n\nimport type {DndMonitorListener, DndMonitorEvent} from './types';\n\nexport function useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set<DndMonitorListener>());\n\n  const registerListener = useCallback(\n    (listener) => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    },\n    [listeners]\n  );\n\n  const dispatch = useCallback(\n    ({type, event}: DndMonitorEvent) => {\n      listeners.forEach((listener) => listener[type]?.(event as any));\n    },\n    [listeners]\n  );\n\n  return [dispatch, registerListener] as const;\n}\n", "import type {Announcements, ScreenReaderInstructions} from './types';\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  `,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  onDragStart({active}) {\n    return `Picked up draggable item ${active.id}.`;\n  },\n  onDragOver({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was moved over droppable area ${over.id}.`;\n    }\n\n    return `Draggable item ${active.id} is no longer over a droppable area.`;\n  },\n  onDragEnd({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was dropped over droppable area ${over.id}`;\n    }\n\n    return `Draggable item ${active.id} was dropped.`;\n  },\n  onDragCancel({active}) {\n    return `Dragging was cancelled. Draggable item ${active.id} was dropped.`;\n  },\n};\n", "import React, {useEffect, useMemo, useState} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useUniqueId} from '@dnd-kit/utilities';\nimport {HiddenText, LiveRegion, useAnnouncement} from '@dnd-kit/accessibility';\n\nimport {DndMonitorListener, useDndMonitor} from '../DndMonitor';\n\nimport type {Announcements, ScreenReaderInstructions} from './types';\nimport {\n  defaultAnnouncements,\n  defaultScreenReaderInstructions,\n} from './defaults';\n\ninterface Props {\n  announcements?: Announcements;\n  container?: Element;\n  screenReaderInstructions?: ScreenReaderInstructions;\n  hiddenTextDescribedById: string;\n}\n\nexport function Accessibility({\n  announcements = defaultAnnouncements,\n  container,\n  hiddenTextDescribedById,\n  screenReaderInstructions = defaultScreenReaderInstructions,\n}: Props) {\n  const {announce, announcement} = useAnnouncement();\n  const liveRegionId = useUniqueId(`DndLiveRegion`);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useDndMonitor(\n    useMemo<DndMonitorListener>(\n      () => ({\n        onDragStart({active}) {\n          announce(announcements.onDragStart({active}));\n        },\n        onDragMove({active, over}) {\n          if (announcements.onDragMove) {\n            announce(announcements.onDragMove({active, over}));\n          }\n        },\n        onDragOver({active, over}) {\n          announce(announcements.onDragOver({active, over}));\n        },\n        onDragEnd({active, over}) {\n          announce(announcements.onDragEnd({active, over}));\n        },\n        onDragCancel({active, over}) {\n          announce(announcements.onDragCancel({active, over}));\n        },\n      }),\n      [announce, announcements]\n    )\n  );\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = (\n    <>\n      <HiddenText\n        id={hiddenTextDescribedById}\n        value={screenReaderInstructions.draggable}\n      />\n      <LiveRegion id={liveRegionId} announcement={announcement} />\n    </>\n  );\n\n  return container ? createPortal(markup, container) : markup;\n}\n", "import type {Coordinates, UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\nexport enum Action {\n  DragStart = 'dragStart',\n  DragMove = 'dragMove',\n  DragEnd = 'dragEnd',\n  DragCancel = 'dragCancel',\n  DragOver = 'dragOver',\n  RegisterDroppable = 'registerDroppable',\n  SetDroppableDisabled = 'setDroppableDisabled',\n  UnregisterDroppable = 'unregisterDroppable',\n}\n\nexport type Actions =\n  | {\n      type: Action.DragStart;\n      active: UniqueIdentifier;\n      initialCoordinates: Coordinates;\n    }\n  | {type: Action.DragMove; coordinates: Coordinates}\n  | {type: Action.DragEnd}\n  | {type: Action.DragCancel}\n  | {\n      type: Action.RegisterDroppable;\n      element: DroppableContainer;\n    }\n  | {\n      type: Action.SetDroppableDisabled;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n      disabled: boolean;\n    }\n  | {\n      type: Action.UnregisterDroppable;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n    };\n", "export function noop(..._args: any) {}\n", "import {useMemo} from 'react';\n\nimport type {Sensor, SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensor<T extends SensorOptions>(\n  sensor: Sensor<T>,\n  options?: T\n): SensorDescriptor<T> {\n  return useMemo(\n    () => ({\n      sensor,\n      options: options ?? ({} as T),\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [sensor, options]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensors(\n  ...sensors: (SensorDescriptor<any> | undefined | null)[]\n): SensorDescriptor<SensorOptions>[] {\n  return useMemo(\n    () =>\n      [...sensors].filter(\n        (sensor): sensor is SensorDescriptor<any> => sensor != null\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...sensors]\n  );\n}\n", "import type {Coordinates} from '../../types';\n\nexport const defaultCoordinates: Coordinates = Object.freeze({\n  x: 0,\n  y: 0,\n});\n", "import type {Coordinates} from '../../types';\n\n/**\n * Returns the distance between two points\n */\nexport function distanceBetween(p1: Coordinates, p2: Coordinates) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n", "import {getEventCoordinates} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function getRelativeTransformOrigin(\n  event: MouseEvent | TouchEvent | KeyboardEvent,\n  rect: ClientRect\n) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: ((eventCoordinates.x - rect.left) / rect.width) * 100,\n    y: ((eventCoordinates.y - rect.top) / rect.height) * 100,\n  };\n\n  return `${transformOrigin.x}% ${transformOrigin.y}%`;\n}\n", "/* eslint-disable no-redeclare */\nimport type {ClientRect} from '../../types';\n\nimport type {Collision, CollisionDescriptor} from './types';\n\n/**\n * Sort collisions from smallest to greatest value\n */\nexport function sortCollisionsAsc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return a - b;\n}\n\n/**\n * Sort collisions from greatest to smallest value\n */\nexport function sortCollisionsDesc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return b - a;\n}\n\n/**\n * Returns the coordinates of the corners of a given rectangle:\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\n */\nexport function cornersOfRectangle({left, top, height, width}: ClientRect) {\n  return [\n    {\n      x: left,\n      y: top,\n    },\n    {\n      x: left + width,\n      y: top,\n    },\n    {\n      x: left,\n      y: top + height,\n    },\n    {\n      x: left + width,\n      y: top + height,\n    },\n  ];\n}\n\n/**\n * Returns the first collision, or null if there isn't one.\n * If a property is specified, returns the specified property of the first collision.\n */\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined\n): Collision | null;\nexport function getFirstCollision<T extends keyof Collision>(\n  collisions: Collision[] | null | undefined,\n  property: T\n): Collision[T] | null;\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined,\n  property?: keyof Collision\n) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n\n  return property ? firstCollision[property] : firstCollision;\n}\n", "import {distanceBetween} from '../coordinates';\nimport type {Coordinates, ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the coordinates of the center of a given ClientRect\n */\nfunction centerOfRectangle(\n  rect: ClientRect,\n  left = rect.left,\n  top = rect.top\n): Coordinates {\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5,\n  };\n}\n\n/**\n * Returns the closest rectangles from an array of rectangles to the center of a given\n * rectangle.\n */\nexport const closestCenter: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const centerRect = centerOfRectangle(\n    collisionRect,\n    collisionRect.left,\n    collisionRect.top\n  );\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n\n      collisions.push({id, data: {droppableContainer, value: distBetween}});\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the closest rectangles from an array of rectangles to the corners of\n * another rectangle.\n */\nexport const closestCorners: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsDesc} from './helpers';\n\n/**\n * Returns the intersecting rectangle area between two rectangles\n */\nexport function getIntersectionRatio(\n  entry: ClientRect,\n  target: ClientRect\n): number {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio =\n      intersectionArea / (targetArea + entryArea - intersectionArea);\n\n    return Number(intersectionRatio.toFixed(4));\n  }\n\n  // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n  return 0;\n}\n\n/**\n * Returns the rectangles that has the greatest intersection area with a given\n * rectangle in an array of rectangles.\n */\nexport const rectIntersection: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {droppableContainer, value: intersectionRatio},\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Check if a given point is contained within a bounding rectangle\n */\nfunction isPointWithinRect(point: Coordinates, rect: ClientRect): boolean {\n  const {top, left, bottom, right} = rect;\n\n  return (\n    top <= point.y && point.y <= bottom && left <= point.x && point.x <= right\n  );\n}\n\n/**\n * Returns the rectangles that the pointer is hovering over\n */\nexport const pointerWithin: CollisionDetection = ({\n  droppableContainers,\n  droppableRects,\n  pointerCoordinates,\n}) => {\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\n       * with the pointer coordinates. In order to sort the\n       * colliding rectangles, we measure the distance between\n       * the pointer and the corners of the intersecting rectangle\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {Transform} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function adjustScale(\n  transform: Transform,\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Transform {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1,\n  };\n}\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getRectDelta(\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Coordinates {\n  return rect1 && rect2\n    ? {\n        x: rect1.left - rect2.left,\n        y: rect1.top - rect2.top,\n      }\n    : defaultCoordinates;\n}\n", "import type {Coordinates, ClientRect} from '../../types';\n\nexport function createRectAdjustmentFn(modifier: number) {\n  return function adjustClientRect(\n    rect: ClientRect,\n    ...adjustments: Coordinates[]\n  ): ClientRect {\n    return adjustments.reduce<ClientRect>(\n      (acc, adjustment) => ({\n        ...acc,\n        top: acc.top + modifier * adjustment.y,\n        bottom: acc.bottom + modifier * adjustment.y,\n        left: acc.left + modifier * adjustment.x,\n        right: acc.right + modifier * adjustment.x,\n      }),\n      {...rect}\n    );\n  };\n}\n\nexport const getAdjustedRect = createRectAdjustmentFn(1);\n", "import type {Transform} from '@dnd-kit/utilities';\n\nexport function parseTransform(transform: string): Transform | null {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import type {ClientRect} from '../../types';\n\nimport {parseTransform} from './parseTransform';\n\nexport function inverseTransform(\n  rect: ClientRect,\n  transform: string,\n  transformOrigin: string\n): ClientRect {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {inverseTransform} from '../transform';\n\ninterface Options {\n  ignoreTransform?: boolean;\n}\n\nconst defaultOptions: Options = {ignoreTransform: false};\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n */\nexport function getClientRect(\n  element: Element,\n  options: Options = defaultOptions\n) {\n  let rect: ClientRect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {transform, transformOrigin} =\n      getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {top, left, width, height, bottom, right} = rect;\n\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right,\n  };\n}\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n *\n * @remarks\n * The ClientRect returned by this method does not take into account transforms\n * applied to the element it measures.\n *\n */\nexport function getTransformAgnosticClientRect(element: Element): ClientRect {\n  return getClientRect(element, {ignoreTransform: true});\n}\n", "import type {ClientRect} from '../../types';\n\nexport function getWindowClientRect(element: typeof window): ClientRect {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isFixed(\n  node: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(node).getComputedStyle(node)\n): boolean {\n  return computedStyle.position === 'fixed';\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(element).getComputedStyle(\n    element\n  )\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {\n  getWindow,\n  isDocument,\n  isHTMLElement,\n  isSVGElement,\n} from '@dnd-kit/utilities';\n\nimport {isFixed} from './isFixed';\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  limit?: number\n): Element[] {\n  const scrollParents: Element[] = [];\n\n  function findScrollableAncestors(node: Node | null): Element[] {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.includes(node.scrollingElement)\n    ) {\n      scrollParents.push(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {\n  canUseDOM,\n  isHTMLElement,\n  isDocument,\n  getOwnerDocument,\n  isNode,\n  isWindow,\n} from '@dnd-kit/utilities';\n\nexport function getScrollableElement(element: EventTarget | null) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (\n    isDocument(element) ||\n    element === getOwnerDocument(element).scrollingElement\n  ) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n", "import {isWindow} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\n\nexport function getScrollXCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\n\nexport function getScrollYCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\n\nexport function getScrollCoordinates(\n  element: Element | typeof window\n): Coordinates {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element),\n  };\n}\n", "export enum Direction {\n  Forward = 1,\n  Backward = -1,\n}\n", "import {canUseDOM} from '@dnd-kit/utilities';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n", "import {isDocumentScrollingElement} from './documentScrollingElement';\n\nexport function getScrollPosition(scrollingContainer: Element) {\n  const minScroll = {\n    x: 0,\n    y: 0,\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollingContainer.clientHeight,\n        width: scrollingContainer.clientWidth,\n      };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height,\n  };\n\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll,\n  };\n}\n", "import {Direction, ClientRect} from '../../types';\nimport {getScrollPosition} from './getScrollPosition';\n\ninterface PositionalCoordinates\n  extends Pick<ClientRect, 'top' | 'left' | 'right' | 'bottom'> {}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2,\n};\n\nexport function getScrollDirectionAndSpeed(\n  scrollContainer: Element,\n  scrollContainerRect: ClientRect,\n  {top, left, right, bottom}: PositionalCoordinates,\n  acceleration = 10,\n  thresholdPercentage = defaultThreshold\n) {\n  const {isTop, isBottom, isLeft, isRight} = getScrollPosition(scrollContainer);\n\n  const direction = {\n    x: 0,\n    y: 0,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - top) / threshold.height\n      );\n  } else if (\n    !isBottom &&\n    bottom >= scrollContainerRect.bottom - threshold.height\n  ) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - bottom) /\n          threshold.height\n      );\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - right) / threshold.width\n      );\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - left) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "export function getScrollElementRect(element: Element) {\n  if (element === document.scrollingElement) {\n    const {innerWidth, innerHeight} = window;\n\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight,\n    };\n  }\n\n  const {top, left, right, bottom} = element.getBoundingClientRect();\n\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight,\n  };\n}\n", "import {add} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  getScrollCoordinates,\n  getScrollXCoordinate,\n  getScrollYCoordinate,\n} from './getScrollCoordinates';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getScrollOffsets(scrollableAncestors: Element[]): Coordinates {\n  return scrollableAncestors.reduce<Coordinates>((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\n\nexport function getScrollXOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\n\nexport function getScrollYOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n", "import type {ClientRect} from '../../types';\nimport {getClientRect} from '../rect/getRect';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors';\n\nexport function scrollIntoViewIfNeeded(\n  element: HTMLElement | null | undefined,\n  measure: (node: HTMLElement) => ClientRect = getClientRect\n) {\n  if (!element) {\n    return;\n  }\n\n  const {top, left, bottom, right} = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (\n    bottom <= 0 ||\n    right <= 0 ||\n    top >= window.innerHeight ||\n    left >= window.innerWidth\n  ) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center',\n    });\n  }\n}\n", "import type {ClientRect} from '../../types/rect';\nimport {\n  getScrollableAncestors,\n  getScrollOffsets,\n  getScrollXOffset,\n  getScrollYOffset,\n} from '../scroll';\n\nconst properties = [\n  ['x', ['left', 'right'], getScrollXOffset],\n  ['y', ['top', 'bottom'], getScrollYOffset],\n] as const;\n\nexport class Rect {\n  constructor(rect: ClientRect, element: Element) {\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n\n    this.rect = {...rect};\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true,\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {enumerable: false});\n  }\n\n  private rect: ClientRect;\n\n  public width: number;\n\n  public height: number;\n\n  // The below properties are set by the `Object.defineProperty` calls in the constructor\n  // @ts-ignore\n  public top: number;\n  // @ts-ignore\n  public bottom: number;\n  // @ts-ignore\n  public right: number;\n  // @ts-ignore\n  public left: number;\n}\n", "export class Listeners {\n  private listeners: [\n    string,\n    EventListenerOrEventListenerObject,\n    AddEventListenerOptions | boolean | undefined\n  ][] = [];\n\n  constructor(private target: EventTarget | null) {}\n\n  public add<T extends Event>(\n    eventName: string,\n    handler: (event: T) => void,\n    options?: AddEventListenerOptions | boolean\n  ) {\n    this.target?.addEventListener(eventName, handler as EventListener, options);\n    this.listeners.push([eventName, handler as EventListener, options]);\n  }\n\n  public removeAll = () => {\n    this.listeners.forEach((listener) =>\n      this.target?.removeEventListener(...listener)\n    );\n  };\n}\n", "import {getOwnerDocument, getWindow} from '@dnd-kit/utilities';\n\nexport function getEventListenerTarget(\n  target: EventTarget | null\n): EventTarget | Document {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n\n  const {EventTarget} = getWindow(target);\n\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n", "import type {Coordinates, DistanceMeasurement} from '../../types';\n\nexport function hasExceededDistance(\n  delta: Coordinates,\n  measurement: DistanceMeasurement\n): boolean {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n", "export enum EventName {\n  Click = 'click',\n  DragStart = 'dragstart',\n  Keydown = 'keydown',\n  ContextMenu = 'contextmenu',\n  Resize = 'resize',\n  SelectionChange = 'selectionchange',\n  VisibilityChange = 'visibilitychange',\n}\n\nexport function preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nexport function stopPropagation(event: Event) {\n  event.stopPropagation();\n}\n", "import type {Coordinates, UniqueIdentifier} from '../../types';\nimport type {SensorContext} from '../types';\n\nexport enum KeyboardCode {\n  Space = 'Space',\n  Down = 'ArrowDown',\n  Right = 'ArrowRight',\n  Left = 'ArrowLeft',\n  Up = 'ArrowUp',\n  Esc = 'Escape',\n  Enter = 'Enter',\n  Tab = 'Tab',\n}\n\nexport type KeyboardCodes = {\n  start: KeyboardEvent['code'][];\n  cancel: KeyboardEvent['code'][];\n  end: KeyboardEvent['code'][];\n};\n\nexport type KeyboardCoordinateGetter = (\n  event: KeyboardEvent,\n  args: {\n    active: UniqueIdentifier;\n    currentCoordinates: Coordinates;\n    context: SensorContext;\n  }\n) => Coordinates | void;\n", "import {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\n\nexport const defaultKeyboardCodes: KeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab],\n};\n\nexport const defaultKeyboardCoordinateGetter: KeyboardCoordinateGetter = (\n  event,\n  {currentCoordinates}\n) => {\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25,\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25,\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25,\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25,\n      };\n  }\n\n  return undefined;\n};\n", "import {\n  add as getAdjustedCoordinates,\n  subtract as getCoordinates<PERSON><PERSON><PERSON>,\n  getOwnerDocument,\n  getWindow,\n  isKeyboardEvent,\n} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  defaultCoordinates,\n  getScrollPosition,\n  getScrollElementRect,\n} from '../../utilities';\nimport {scrollIntoViewIfNeeded} from '../../utilities/scroll';\nimport {EventName} from '../events';\nimport {Listeners} from '../utilities';\nimport type {\n  Activators,\n  SensorInstance,\n  SensorProps,\n  SensorOptions,\n} from '../types';\n\nimport {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\nimport {\n  defaultKeyboardCodes,\n  defaultKeyboardCoordinateGetter,\n} from './defaults';\n\nexport interface KeyboardSensorOptions extends SensorOptions {\n  keyboardCodes?: KeyboardCodes;\n  coordinateGetter?: KeyboardCoordinateGetter;\n  scrollBehavior?: ScrollBehavior;\n  onActivation?({event}: {event: KeyboardEvent}): void;\n}\n\nexport type KeyboardSensorProps = SensorProps<KeyboardSensorOptions>;\n\nexport class KeyboardSensor implements SensorInstance {\n  public autoScrollEnabled = false;\n  private referenceCoordinates: Coordinates | undefined;\n  private listeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(private props: KeyboardSensorProps) {\n    const {\n      event: {target},\n    } = props;\n\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    this.handleStart();\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  private handleStart() {\n    const {activeNode, onStart} = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  private handleKeyDown(event: Event) {\n    if (isKeyboardEvent(event)) {\n      const {active, context, options} = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth',\n      } = options;\n      const {code} = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {collisionRect} = context.current;\n      const currentCoordinates = collisionRect\n        ? {x: collisionRect.left, y: collisionRect.top}\n        : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates,\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = getCoordinatesDelta(\n          newCoordinates,\n          currentCoordinates\n        );\n        const scrollDelta = {\n          x: 0,\n          y: 0,\n        };\n        const {scrollableAncestors} = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {isTop, isRight, isLeft, isBottom, maxScroll, minScroll} =\n            getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n\n          const clampedCoordinates = {\n            x: Math.min(\n              direction === KeyboardCode.Right\n                ? scrollElementRect.right - scrollElementRect.width / 2\n                : scrollElementRect.right,\n              Math.max(\n                direction === KeyboardCode.Right\n                  ? scrollElementRect.left\n                  : scrollElementRect.left + scrollElementRect.width / 2,\n                newCoordinates.x\n              )\n            ),\n            y: Math.min(\n              direction === KeyboardCode.Down\n                ? scrollElementRect.bottom - scrollElementRect.height / 2\n                : scrollElementRect.bottom,\n              Math.max(\n                direction === KeyboardCode.Down\n                  ? scrollElementRect.top\n                  : scrollElementRect.top + scrollElementRect.height / 2,\n                newCoordinates.y\n              )\n            ),\n          };\n\n          const canScrollX =\n            (direction === KeyboardCode.Right && !isRight) ||\n            (direction === KeyboardCode.Left && !isLeft);\n          const canScrollY =\n            (direction === KeyboardCode.Down && !isBottom) ||\n            (direction === KeyboardCode.Up && !isTop);\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates =\n              scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Right &&\n                newScrollCoordinates <= maxScroll.x) ||\n              (direction === KeyboardCode.Left &&\n                newScrollCoordinates >= minScroll.x);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x =\n                direction === KeyboardCode.Right\n                  ? scrollContainer.scrollLeft - maxScroll.x\n                  : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior,\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates =\n              scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Down &&\n                newScrollCoordinates <= maxScroll.y) ||\n              (direction === KeyboardCode.Up &&\n                newScrollCoordinates >= minScroll.y);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y =\n                direction === KeyboardCode.Down\n                  ? scrollContainer.scrollTop - maxScroll.y\n                  : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior,\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(\n          event,\n          getAdjustedCoordinates(\n            getCoordinatesDelta(newCoordinates, this.referenceCoordinates),\n            scrollDelta\n          )\n        );\n      }\n    }\n  }\n\n  private handleMove(event: Event, coordinates: Coordinates) {\n    const {onMove} = this.props;\n\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  private handleEnd(event: Event) {\n    const {onEnd} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel(event: Event) {\n    const {onCancel} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n  static activators: Activators<KeyboardSensorOptions> = [\n    {\n      eventName: 'onKeyDown' as const,\n      handler: (\n        event: React.KeyboardEvent,\n        {keyboardCodes = defaultKeyboardCodes, onActivation},\n        {active}\n      ) => {\n        const {code} = event.nativeEvent;\n\n        if (keyboardCodes.start.includes(code)) {\n          const activator = active.activatorNode.current;\n\n          if (activator && event.target !== activator) {\n            return false;\n          }\n\n          event.preventDefault();\n\n          onActivation?.({event: event.nativeEvent});\n\n          return true;\n        }\n\n        return false;\n      },\n    },\n  ];\n}\n", "import {\n  subtract as getCoordina<PERSON><PERSON><PERSON><PERSON>,\n  getEventCoordinates,\n  getOwnerDocument,\n  getWindow,\n} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\nimport {\n  getEventListenerTarget,\n  hasExceededDistance,\n  Listeners,\n} from '../utilities';\nimport {EventName, preventDefault, stopPropagation} from '../events';\nimport {KeyboardCode} from '../keyboard';\nimport type {SensorInstance, SensorProps, SensorOptions} from '../types';\nimport type {Coordinates, DistanceMeasurement} from '../../types';\n\ninterface DistanceConstraint {\n  distance: DistanceMeasurement;\n  tolerance?: DistanceMeasurement;\n}\n\ninterface DelayConstraint {\n  delay: number;\n  tolerance: DistanceMeasurement;\n}\n\ninterface EventDescriptor {\n  name: keyof DocumentEventMap;\n  passive?: boolean;\n}\n\nexport interface PointerEventHandlers {\n  cancel?: EventDescriptor;\n  move: EventDescriptor;\n  end: EventDescriptor;\n}\n\nexport type PointerActivationConstraint =\n  | DelayConstraint\n  | DistanceConstraint\n  | (DelayConstraint & DistanceConstraint);\n\nfunction isDistanceConstraint(\n  constraint: PointerActivationConstraint\n): constraint is PointerActivationConstraint & DistanceConstraint {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DelayConstraint {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nexport interface AbstractPointerSensorOptions extends SensorOptions {\n  activationConstraint?: PointerActivationConstraint;\n  bypassActivationConstraint?(\n    props: Pick<AbstractPointerSensorProps, 'activeNode' | 'event' | 'options'>\n  ): boolean;\n  onActivation?({event}: {event: Event}): void;\n}\n\nexport type AbstractPointerSensorProps =\n  SensorProps<AbstractPointerSensorOptions>;\n\nexport class AbstractPointerSensor implements SensorInstance {\n  public autoScrollEnabled = true;\n  private document: Document;\n  private activated: boolean = false;\n  private initialCoordinates: Coordinates;\n  private timeoutId: NodeJS.Timeout | null = null;\n  private listeners: Listeners;\n  private documentListeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(\n    private props: AbstractPointerSensorProps,\n    private events: PointerEventHandlers,\n    listenerTarget = getEventListenerTarget(props.event.target)\n  ) {\n    const {event} = props;\n    const {target} = event;\n\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    const {\n      events,\n      props: {\n        options: {activationConstraint, bypassActivationConstraint},\n      },\n    } = this;\n\n    this.listeners.add(events.move.name, this.handleMove, {passive: false});\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (\n        bypassActivationConstraint?.({\n          event: this.props.event,\n          activeNode: this.props.activeNode,\n          options: this.props.options,\n        })\n      ) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(\n          this.handleStart,\n          activationConstraint.delay\n        );\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n\n    // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  private handlePending(\n    constraint: PointerActivationConstraint,\n    offset?: Coordinates | undefined\n  ): void {\n    const {active, onPending} = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  private handleStart() {\n    const {initialCoordinates} = this;\n    const {onStart} = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true;\n\n      // Stop propagation of click events once activation constraints are met\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true,\n      });\n\n      // Remove any text selection from the document\n      this.removeTextSelection();\n\n      // Prevent further text selection while dragging\n      this.documentListeners.add(\n        EventName.SelectionChange,\n        this.removeTextSelection\n      );\n\n      onStart(initialCoordinates);\n    }\n  }\n\n  private handleMove(event: Event) {\n    const {activated, initialCoordinates, props} = this;\n    const {\n      onMove,\n      options: {activationConstraint},\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    const delta = getCoordinatesDelta(initialCoordinates, coordinates);\n\n    // Constraint validation\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (\n          activationConstraint.tolerance != null &&\n          hasExceededDistance(delta, activationConstraint.tolerance)\n        ) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  private handleEnd() {\n    const {onAbort, onEnd} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onEnd();\n  }\n\n  private handleCancel() {\n    const {onAbort, onCancel} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onCancel();\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  private removeTextSelection() {\n    this.document.getSelection()?.removeAllRanges();\n  }\n}\n", "import type {PointerEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  AbstractPointerSensorOptions,\n  PointerEventHandlers,\n} from './AbstractPointerSensor';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'pointercancel'},\n  move: {name: 'pointermove'},\n  end: {name: 'pointerup'},\n};\n\nexport interface PointerSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type PointerSensorProps = SensorProps<PointerSensorOptions>;\n\nexport class PointerSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    const {event} = props;\n    // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n    const listenerTarget = getOwnerDocument(event.target);\n\n    super(props, events, listenerTarget);\n  }\n\n  static activators = [\n    {\n      eventName: 'onPointerDown' as const,\n      handler: (\n        {nativeEvent: event}: PointerEvent,\n        {onActivation}: PointerSensorOptions\n      ) => {\n        if (!event.isPrimary || event.button !== 0) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {MouseEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  PointerEventHandlers,\n  AbstractPointerSensorOptions,\n} from '../pointer';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'mousemove'},\n  end: {name: 'mouseup'},\n};\n\nenum MouseButton {\n  RightClick = 2,\n}\n\nexport interface MouseSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type MouseSensorProps = SensorProps<MouseSensorOptions>;\n\nexport class MouseSensor extends AbstractPointerSensor {\n  constructor(props: MouseSensorProps) {\n    super(props, events, getOwnerDocument(props.event.target));\n  }\n\n  static activators = [\n    {\n      eventName: 'onMouseDown' as const,\n      handler: (\n        {nativeEvent: event}: MouseEvent,\n        {onActivation}: MouseSensorOptions\n      ) => {\n        if (event.button === MouseButton.RightClick) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {TouchEvent} from 'react';\n\nimport {\n  AbstractPointerSensor,\n  PointerSensorProps,\n  PointerEventHandlers,\n  PointerSensorOptions,\n} from '../pointer';\nimport type {SensorProps} from '../types';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'touchcancel'},\n  move: {name: 'touchmove'},\n  end: {name: 'touchend'},\n};\n\nexport interface TouchSensorOptions extends PointerSensorOptions {}\n\nexport type TouchSensorProps = SensorProps<TouchSensorOptions>;\n\nexport class TouchSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    super(props, events);\n  }\n\n  static activators = [\n    {\n      eventName: 'onTouchStart' as const,\n      handler: (\n        {nativeEvent: event}: TouchEvent,\n        {onActivation}: TouchSensorOptions\n      ) => {\n        const {touches} = event;\n\n        if (touches.length > 1) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events.move.name, noop, {\n      capture: false,\n      passive: false,\n    });\n\n    return function teardown() {\n      window.removeEventListener(events.move.name, noop);\n    };\n\n    // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n    function noop() {}\n  }\n}\n", "import {useCallback, useEffect, useMemo, useRef} from 'react';\nimport {useInterval, useLazyMemo, usePrevious} from '@dnd-kit/utilities';\n\nimport {getScrollDirectionAndSpeed} from '../../utilities';\nimport {Direction} from '../../types';\nimport type {Coordinates, ClientRect} from '../../types';\n\nexport type ScrollAncestorSortingFn = (ancestors: Element[]) => Element[];\n\nexport enum AutoScrollActivator {\n  Pointer,\n  DraggableRect,\n}\n\nexport interface Options {\n  acceleration?: number;\n  activator?: AutoScrollActivator;\n  canScroll?: CanScroll;\n  enabled?: boolean;\n  interval?: number;\n  layoutShiftCompensation?:\n    | boolean\n    | {\n        x: boolean;\n        y: boolean;\n      };\n  order?: TraversalOrder;\n  threshold?: {\n    x: number;\n    y: number;\n  };\n}\n\ninterface Arguments extends Options {\n  draggingRect: ClientRect | null;\n  enabled: boolean;\n  pointerCoordinates: Coordinates | null;\n  scrollableAncestors: Element[];\n  scrollableAncestorRects: ClientRect[];\n  delta: Coordinates;\n}\n\nexport type CanScroll = (element: Element) => boolean;\n\nexport enum TraversalOrder {\n  TreeOrder,\n  ReversedTreeOrder,\n}\n\ninterface ScrollDirection {\n  x: 0 | Direction;\n  y: 0 | Direction;\n}\n\nexport function useAutoScroller({\n  acceleration,\n  activator = AutoScrollActivator.Pointer,\n  canScroll,\n  draggingRect,\n  enabled,\n  interval = 5,\n  order = TraversalOrder.TreeOrder,\n  pointerCoordinates,\n  scrollableAncestors,\n  scrollableAncestorRects,\n  delta,\n  threshold,\n}: Arguments) {\n  const scrollIntent = useScrollIntent({delta, disabled: !enabled});\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef<Coordinates>({x: 0, y: 0});\n  const scrollDirection = useRef<ScrollDirection>({x: 0, y: 0});\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates\n          ? {\n              top: pointerCoordinates.y,\n              bottom: pointerCoordinates.y,\n              left: pointerCoordinates.x,\n              right: pointerCoordinates.x,\n            }\n          : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef<Element | null>(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(\n    () =>\n      order === TraversalOrder.TreeOrder\n        ? [...scrollableAncestors].reverse()\n        : scrollableAncestors,\n    [order, scrollableAncestors]\n  );\n\n  useEffect(\n    () => {\n      if (!enabled || !scrollableAncestors.length || !rect) {\n        clearAutoScrollInterval();\n        return;\n      }\n\n      for (const scrollContainer of sortedScrollableAncestors) {\n        if (canScroll?.(scrollContainer) === false) {\n          continue;\n        }\n\n        const index = scrollableAncestors.indexOf(scrollContainer);\n        const scrollContainerRect = scrollableAncestorRects[index];\n\n        if (!scrollContainerRect) {\n          continue;\n        }\n\n        const {direction, speed} = getScrollDirectionAndSpeed(\n          scrollContainer,\n          scrollContainerRect,\n          rect,\n          acceleration,\n          threshold\n        );\n\n        for (const axis of ['x', 'y'] as const) {\n          if (!scrollIntent[axis][direction[axis] as Direction]) {\n            speed[axis] = 0;\n            direction[axis] = 0;\n          }\n        }\n\n        if (speed.x > 0 || speed.y > 0) {\n          clearAutoScrollInterval();\n\n          scrollContainerRef.current = scrollContainer;\n          setAutoScrollInterval(autoScroll, interval);\n\n          scrollSpeed.current = speed;\n          scrollDirection.current = direction;\n\n          return;\n        }\n      }\n\n      scrollSpeed.current = {x: 0, y: 0};\n      scrollDirection.current = {x: 0, y: 0};\n      clearAutoScrollInterval();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      acceleration,\n      autoScroll,\n      canScroll,\n      clearAutoScrollInterval,\n      enabled,\n      interval,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(rect),\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(scrollIntent),\n      setAutoScrollInterval,\n      scrollableAncestors,\n      sortedScrollableAncestors,\n      scrollableAncestorRects,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(threshold),\n    ]\n  );\n}\n\ninterface ScrollIntent {\n  x: Record<Direction, boolean>;\n  y: Record<Direction, boolean>;\n}\n\nconst defaultScrollIntent: ScrollIntent = {\n  x: {[Direction.Backward]: false, [Direction.Forward]: false},\n  y: {[Direction.Backward]: false, [Direction.Forward]: false},\n};\n\nfunction useScrollIntent({\n  delta,\n  disabled,\n}: {\n  delta: Coordinates;\n  disabled: boolean;\n}): ScrollIntent {\n  const previousDelta = usePrevious(delta);\n\n  return useLazyMemo<ScrollIntent>(\n    (previousIntent) => {\n      if (disabled || !previousDelta || !previousIntent) {\n        // Reset scroll intent tracking when auto-scrolling is disabled\n        return defaultScrollIntent;\n      }\n\n      const direction = {\n        x: Math.sign(delta.x - previousDelta.x),\n        y: Math.sign(delta.y - previousDelta.y),\n      };\n\n      // Keep track of the user intent to scroll in each direction for both axis\n      return {\n        x: {\n          [Direction.Backward]:\n            previousIntent.x[Direction.Backward] || direction.x === -1,\n          [Direction.Forward]:\n            previousIntent.x[Direction.Forward] || direction.x === 1,\n        },\n        y: {\n          [Direction.Backward]:\n            previousIntent.y[Direction.Backward] || direction.y === -1,\n          [Direction.Forward]:\n            previousIntent.y[Direction.Forward] || direction.y === 1,\n        },\n      };\n    },\n    [disabled, delta, previousDelta]\n  );\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\nimport type {DraggableNode, DraggableNodes} from '../../store';\nimport type {UniqueIdentifier} from '../../types';\n\nexport function useCachedNode(\n  draggableNodes: DraggableNodes,\n  id: UniqueIdentifier | null\n): DraggableNode['node']['current'] {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n\n  return useLazyMemo(\n    (cachedNode) => {\n      if (id == null) {\n        return null;\n      }\n\n      // In some cases, the draggable node can unmount while dragging\n      // This is the case for virtualized lists. In those situations,\n      // we fall back to the last known value for that node.\n      return node ?? cachedNode ?? null;\n    },\n    [node, id]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorActivatorFunction, SensorDescriptor} from '../../sensors';\nimport type {\n  SyntheticListener,\n  SyntheticListeners,\n} from './useSyntheticListeners';\n\nexport function useCombineActivators(\n  sensors: SensorDescriptor<any>[],\n  getSyntheticHandler: (\n    handler: SensorActivatorFunction<any>,\n    sensor: SensorDescriptor<any>\n  ) => SyntheticListener['handler']\n): SyntheticListeners {\n  return useMemo(\n    () =>\n      sensors.reduce<SyntheticListeners>((accumulator, sensor) => {\n        const {sensor: Sensor} = sensor;\n\n        const sensorActivators = Sensor.activators.map((activator) => ({\n          eventName: activator.eventName,\n          handler: getSyntheticHandler(activator.handler, sensor),\n        }));\n\n        return [...accumulator, ...sensorActivators];\n      }, []),\n    [sensors, getSyntheticHandler]\n  );\n}\n", "import {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLatestValue, useLazyMemo} from '@dnd-kit/utilities';\n\nimport {Rect} from '../../utilities/rect';\nimport type {DroppableContainer, RectMap} from '../../store/types';\nimport type {ClientRect, UniqueIdentifier} from '../../types';\n\ninterface Arguments {\n  dragging: boolean;\n  dependencies: any[];\n  config: DroppableMeasuring;\n}\n\nexport enum MeasuringStrategy {\n  Always,\n  BeforeDragging,\n  WhileDragging,\n}\n\nexport enum MeasuringFrequency {\n  Optimized = 'optimized',\n}\n\ntype MeasuringFunction = (element: HTMLElement) => ClientRect;\n\nexport interface DroppableMeasuring {\n  measure: MeasuringFunction;\n  strategy: MeasuringStrategy;\n  frequency: MeasuringFrequency | number;\n}\n\nconst defaultValue: RectMap = new Map();\n\nexport function useDroppableMeasuring(\n  containers: DroppableContainer[],\n  {dragging, dependencies, config}: Arguments\n) {\n  const [queue, setQueue] = useState<UniqueIdentifier[] | null>(null);\n  const {frequency, measure, strategy} = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(\n    (ids: UniqueIdentifier[] = []) => {\n      if (disabledRef.current) {\n        return;\n      }\n\n      setQueue((value) => {\n        if (value === null) {\n          return ids;\n        }\n\n        return value.concat(ids.filter((id) => !value.includes(id)));\n      });\n    },\n    [disabledRef]\n  );\n  const timeoutId = useRef<NodeJS.Timeout | null>(null);\n  const droppableRects = useLazyMemo<RectMap>(\n    (previousValue) => {\n      if (disabled && !dragging) {\n        return defaultValue;\n      }\n\n      if (\n        !previousValue ||\n        previousValue === defaultValue ||\n        containersRef.current !== containers ||\n        queue != null\n      ) {\n        const map: RectMap = new Map();\n\n        for (let container of containers) {\n          if (!container) {\n            continue;\n          }\n\n          if (\n            queue &&\n            queue.length > 0 &&\n            !queue.includes(container.id) &&\n            container.rect.current\n          ) {\n            // This container does not need to be re-measured\n            map.set(container.id, container.rect.current);\n            continue;\n          }\n\n          const node = container.node.current;\n          const rect = node ? new Rect(measure(node), node) : null;\n\n          container.rect.current = rect;\n\n          if (rect) {\n            map.set(container.id, rect);\n          }\n        }\n\n        return map;\n      }\n\n      return previousValue;\n    },\n    [containers, queue, dragging, disabled, measure]\n  );\n\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n\n  useEffect(\n    () => {\n      if (disabled) {\n        return;\n      }\n\n      measureDroppableContainers();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [dragging, disabled]\n  );\n\n  useEffect(\n    () => {\n      if (queue && queue.length > 0) {\n        setQueue(null);\n      }\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(queue)]\n  );\n\n  useEffect(\n    () => {\n      if (\n        disabled ||\n        typeof frequency !== 'number' ||\n        timeoutId.current !== null\n      ) {\n        return;\n      }\n\n      timeoutId.current = setTimeout(() => {\n        measureDroppableContainers();\n        timeoutId.current = null;\n      }, frequency);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [frequency, disabled, measureDroppableContainers, ...dependencies]\n  );\n\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null,\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\ntype AnyFunction = (...args: any) => any;\n\nexport function useInitialValue<\n  T,\n  U extends AnyFunction | undefined = undefined\n>(\n  value: T | null,\n  computeFn?: U\n): U extends AnyFunction ? ReturnType<U> | null : T | null {\n  return useLazyMemo(\n    (previousValue) => {\n      if (!value) {\n        return null;\n      }\n\n      if (previousValue) {\n        return previousValue;\n      }\n\n      return typeof computeFn === 'function' ? computeFn(value) : value;\n    },\n    [computeFn, value]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {useInitialValue} from './useInitialValue';\n\nexport function useInitialRect(\n  node: HTMLElement | null,\n  measure: (node: HTMLElement) => ClientRect\n) {\n  return useInitialValue(node, measure);\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: MutationCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new MutationObserver instance.\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useMutationObserver({callback, disabled}: Arguments) {\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (\n      disabled ||\n      typeof window === 'undefined' ||\n      typeof window.MutationObserver === 'undefined'\n    ) {\n      return undefined;\n    }\n\n    const {MutationObserver} = window;\n\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n\n  useEffect(() => {\n    return () => mutationObserver?.disconnect();\n  }, [mutationObserver]);\n\n  return mutationObserver;\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: ResizeObserverCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useResizeObserver({callback, disabled}: Arguments) {\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(\n    () => {\n      if (\n        disabled ||\n        typeof window === 'undefined' ||\n        typeof window.ResizeObserver === 'undefined'\n      ) {\n        return undefined;\n      }\n\n      const {ResizeObserver} = window;\n\n      return new ResizeObserver(handleResize);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [disabled]\n  );\n\n  useEffect(() => {\n    return () => resizeObserver?.disconnect();\n  }, [resizeObserver]);\n\n  return resizeObserver;\n}\n", "import {useState} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {getClientRect, Rect} from '../../utilities';\n\nimport {useMutationObserver} from './useMutationObserver';\nimport {useResizeObserver} from './useResizeObserver';\n\nfunction defaultMeasure(element: HTMLElement) {\n  return new Rect(getClientRect(element), element);\n}\n\nexport function useRect(\n  element: HTMLElement | null,\n  measure: (element: HTMLElement) => ClientRect = defaultMeasure,\n  fallbackRect?: ClientRect | null\n) {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n\n  function measureRect() {\n    setRect((currentRect): ClientRect | null => {\n      if (!element) {\n        return null;\n      }\n  \n      if (element.isConnected === false) {\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return currentRect ?? fallbackRect ?? null;\n      }\n  \n      const newRect = measure(element);\n  \n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n  \n      return newRect;\n    });\n  }\n  \n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {type, target} = record;\n\n        if (\n          type === 'childList' &&\n          target instanceof HTMLElement &&\n          target.contains(element)\n        ) {\n          measureRect();\n          break;\n        }\n      }\n    },\n  });\n  const resizeObserver = useResizeObserver({callback: measureRect});\n\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver?.observe(element);\n      mutationObserver?.observe(document.body, {\n        childList: true,\n        subtree: true,\n      });\n    } else {\n      resizeObserver?.disconnect();\n      mutationObserver?.disconnect();\n    }\n  }, [element]);\n\n  return rect;\n}\n", "import type {ClientRect} from '../../types';\nimport {getRectDelta} from '../../utilities';\n\nimport {useInitialValue} from './useInitialValue';\n\nexport function useRectDelta(rect: ClientRect | null) {\n  const initialRect = useInitialValue(rect);\n\n  return getRectDelta(rect, initialRect);\n}\n", "import {useEffect, useRef} from 'react';\nimport {useLazyMemo} from '@dnd-kit/utilities';\n\nimport {getScrollableAncestors} from '../../utilities';\n\nconst defaultValue: Element[] = [];\n\nexport function useScrollableAncestors(node: HTMLElement | null) {\n  const previousNode = useRef(node);\n\n  const ancestors = useLazyMemo<Element[]>(\n    (previousValue) => {\n      if (!node) {\n        return defaultValue;\n      }\n\n      if (\n        previousValue &&\n        previousValue !== defaultValue &&\n        node &&\n        previousNode.current &&\n        node.parentNode === previousNode.current.parentNode\n      ) {\n        return previousValue;\n      }\n\n      return getScrollableAncestors(node);\n    },\n    [node]\n  );\n\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n\n  return ancestors;\n}\n", "import {useState, useCallback, useMemo, useRef, useEffect} from 'react';\nimport {add} from '@dnd-kit/utilities';\n\nimport {\n  defaultCoordinates,\n  getScrollableElement,\n  getScrollCoordinates,\n  getScrollOffsets,\n} from '../../utilities';\nimport type {Coordinates} from '../../types';\n\ntype ScrollCoordinates = Map<HTMLElement | Window, Coordinates>;\n\nexport function useScrollOffsets(elements: Element[]): Coordinates {\n  const [\n    scrollCoordinates,\n    setScrollCoordinates,\n  ] = useState<ScrollCoordinates | null>(null);\n  const prevElements = useRef(elements);\n\n  // To-do: Throttle the handleScroll callback\n  const handleScroll = useCallback((event: Event) => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates((scrollCoordinates) => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(\n        scrollingElement,\n        getScrollCoordinates(scrollingElement)\n      );\n\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n\n      const entries = elements\n        .map((element) => {\n          const scrollableElement = getScrollableElement(element);\n\n          if (scrollableElement) {\n            scrollableElement.addEventListener('scroll', handleScroll, {\n              passive: true,\n            });\n\n            return [\n              scrollableElement,\n              getScrollCoordinates(scrollableElement),\n            ] as const;\n          }\n\n          return null;\n        })\n        .filter(\n          (\n            entry\n          ): entry is [\n            HTMLElement | (Window & typeof globalThis),\n            Coordinates\n          ] => entry != null\n        );\n\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements: Element[]) {\n      elements.forEach((element) => {\n        const scrollableElement = getScrollableElement(element);\n\n        scrollableElement?.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates\n        ? Array.from(scrollCoordinates.values()).reduce(\n            (acc, coordinates) => add(acc, coordinates),\n            defaultCoordinates\n          )\n        : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n", "import {useEffect, useRef} from 'react';\nimport {Coordinates, subtract} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\n\nexport function useScrollOffsetsDelta(\n  scrollOffsets: Coordinates,\n  dependencies: any[] = []\n) {\n  const initialScrollOffsets = useRef<Coordinates | null>(null);\n\n  useEffect(\n    () => {\n      initialScrollOffsets.current = null;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    dependencies\n  );\n\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n\n  return initialScrollOffsets.current\n    ? subtract(scrollOffsets, initialScrollOffsets.current)\n    : defaultCoordinates;\n}\n", "import {useEffect} from 'react';\nimport {canUseDOM} from '@dnd-kit/utilities';\n\nimport type {SensorDescriptor} from '../../sensors';\n\nexport function useSensorSetup(sensors: SensorDescriptor<any>[]) {\n  useEffect(\n    () => {\n      if (!canUseDOM) {\n        return;\n      }\n\n      const teardownFns = sensors.map(({sensor}) => sensor.setup?.());\n\n      return () => {\n        for (const teardown of teardownFns) {\n          teardown?.();\n        }\n      };\n    },\n    // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    sensors.map(({sensor}) => sensor)\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SyntheticEventName, UniqueIdentifier} from '../../types';\n\nexport type SyntheticListener = {\n  eventName: SyntheticEventName;\n  handler: (event: React.SyntheticEvent, id: UniqueIdentifier) => void;\n};\n\nexport type SyntheticListeners = SyntheticListener[];\n\nexport type SyntheticListenerMap = Record<string, Function>;\n\nexport function useSyntheticListeners(\n  listeners: SyntheticListeners,\n  id: UniqueIdentifier\n): SyntheticListenerMap {\n  return useMemo(() => {\n    return listeners.reduce<SyntheticListenerMap>(\n      (acc, {eventName, handler}) => {\n        acc[eventName] = (event: React.SyntheticEvent) => {\n          handler(event, id);\n        };\n\n        return acc;\n      },\n      {} as SyntheticListenerMap\n    );\n  }, [listeners, id]);\n}\n", "import {useMemo} from 'react';\n\nimport {getWindowClientRect} from '../../utilities/rect';\n\nexport function useWindowRect(element: typeof window | null) {\n  return useMemo(() => (element ? getWindowClientRect(element) : null), [\n    element,\n  ]);\n}\n", "import {useState} from 'react';\nimport {getWindow, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {Rect, getClientRect} from '../../utilities/rect';\nimport {isDocumentScrollingElement} from '../../utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {useWindowRect} from './useWindowRect';\n\nconst defaultValue: Rect[] = [];\n\nexport function useRects(\n  elements: Element[],\n  measure: (element: Element) => ClientRect = getClientRect\n): ClientRect[] {\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(\n    firstElement ? getWindow(firstElement) : null\n  );\n  const [rects, setRects] = useState<ClientRect[]>(defaultValue);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue;\n      }\n\n      return elements.map((element) =>\n        isDocumentScrollingElement(element)\n          ? (windowRect as ClientRect)\n          : new Rect(measure(element), element)\n      );\n    });\n  }\n\n  const resizeObserver = useResizeObserver({callback: measureRects});\n\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver?.disconnect();\n    measureRects();\n    elements.forEach((element) => resizeObserver?.observe(element));\n  }, [elements]);\n\n  return rects;\n}\n", "import {isHTMLElement} from '@dnd-kit/utilities';\n\nexport function getMeasurableNode(\n  node: HTMLElement | undefined | null\n): HTMLElement | null {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n", "import {useMemo, useCallback, useState} from 'react';\nimport {isHTMLElement, useNodeRef} from '@dnd-kit/utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {getMeasurableNode} from '../../utilities/nodes';\nimport type {PublicContextDescriptor} from '../../store';\nimport type {ClientRect} from '../../types';\n\ninterface Arguments {\n  measure(element: HTMLElement): ClientRect;\n}\n\nexport function useDragOverlayMeasuring({\n  measure,\n}: Arguments): PublicContextDescriptor['dragOverlay'] {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n  const handleResize = useCallback(\n    (entries: ResizeObserverEntry[]) => {\n      for (const {target} of entries) {\n        if (isHTMLElement(target)) {\n          setRect((rect) => {\n            const newRect = measure(target);\n\n            return rect\n              ? {...rect, width: newRect.width, height: newRect.height}\n              : newRect;\n          });\n          break;\n        }\n      }\n    },\n    [measure]\n  );\n  const resizeObserver = useResizeObserver({callback: handleResize});\n  const handleNodeChange = useCallback(\n    (element) => {\n      const node = getMeasurableNode(element);\n\n      resizeObserver?.disconnect();\n\n      if (node) {\n        resizeObserver?.observe(node);\n      }\n\n      setRect(node ? measure(node) : null);\n    },\n    [measure, resizeObserver]\n  );\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n\n  return useMemo(\n    () => ({\n      nodeRef,\n      rect,\n      setRef,\n    }),\n    [rect, nodeRef, setRef]\n  );\n}\n", "import type {DeepRequired} from '@dnd-kit/utilities';\n\nimport type {DataRef} from '../../store/types';\nimport {KeyboardSensor, PointerSensor} from '../../sensors';\nimport {MeasuringStrategy, MeasuringFrequency} from '../../hooks/utilities';\nimport {\n  getClientRect,\n  getTransformAgnosticClientRect,\n} from '../../utilities/rect';\n\nimport type {MeasuringConfiguration} from './types';\n\nexport const defaultSensors = [\n  {sensor: PointerSensor, options: {}},\n  {sensor: KeyboardSensor, options: {}},\n];\n\nexport const defaultData: DataRef = {current: {}};\n\nexport const defaultMeasuringConfiguration: DeepRequired<MeasuringConfiguration> = {\n  draggable: {\n    measure: getTransformAgnosticClientRect,\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized,\n  },\n  dragOverlay: {\n    measure: getClientRect,\n  },\n};\n", "import type {UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\ntype Identifier = UniqueIdentifier | null | undefined;\n\nexport class DroppableContainersMap extends Map<\n  UniqueIdentifier,\n  DroppableContainer\n> {\n  get(id: Identifier) {\n    return id != null ? super.get(id) ?? undefined : undefined;\n  }\n\n  toArray(): DroppableContainer[] {\n    return Array.from(this.values());\n  }\n\n  getEnabled(): DroppableContainer[] {\n    return this.toArray().filter(({disabled}) => !disabled);\n  }\n\n  getNodeFor(id: Identifier) {\n    return this.get(id)?.node.current ?? undefined;\n  }\n}\n", "import {createContext} from 'react';\n\nimport {noop} from '../utilities/other';\nimport {defaultMeasuringConfiguration} from '../components/DndContext/defaults';\nimport {DroppableContainersMap} from './constructors';\nimport type {InternalContextDescriptor, PublicContextDescriptor} from './types';\n\nexport const defaultPublicContext: PublicContextDescriptor = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: new Map(),\n  droppableRects: new Map(),\n  droppableContainers: new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null,\n    },\n    rect: null,\n    setRef: noop,\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false,\n};\n\nexport const defaultInternalContext: InternalContextDescriptor = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: '',\n  },\n  dispatch: noop,\n  draggableNodes: new Map(),\n  over: null,\n  measureDroppableContainers: noop,\n};\n\nexport const InternalContext = createContext<InternalContextDescriptor>(\n  defaultInternalContext\n);\n\nexport const PublicContext = createContext<PublicContextDescriptor>(\n  defaultPublicContext\n);\n", "import {Action, Actions} from './actions';\nimport {DroppableContainersMap} from './constructors';\nimport type {State} from './types';\n\nexport function getInitialState(): State {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {x: 0, y: 0},\n      nodes: new Map(),\n      translate: {x: 0, y: 0},\n    },\n    droppable: {\n      containers: new DroppableContainersMap(),\n    },\n  };\n}\n\nexport function reducer(state: State, action: Actions): State {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active,\n        },\n      };\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y,\n          },\n        },\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {x: 0, y: 0},\n          translate: {x: 0, y: 0},\n        },\n      };\n\n    case Action.RegisterDroppable: {\n      const {element} = action;\n      const {id} = element;\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, element);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.SetDroppableDisabled: {\n      const {id, key, disabled} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, {\n        ...element,\n        disabled,\n      });\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.UnregisterDroppable: {\n      const {id, key} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.delete(id);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    default: {\n      return state;\n    }\n  }\n}\n", "import {useContext, useEffect} from 'react';\nimport {\n  findFirstFocusableNode,\n  isKeyboardEvent,\n  usePrevious,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext} from '../../../store';\n\ninterface Props {\n  disabled: boolean;\n}\n\nexport function RestoreFocus({disabled}: Props) {\n  const {active, activatorEvent, draggableNodes} = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active?.id);\n\n  // Restore keyboard focus on the activator node\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {activatorNode, node} = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [\n    activatorEvent,\n    disabled,\n    draggableNodes,\n    previousActiveId,\n    previousActivatorEvent,\n  ]);\n\n  return null;\n}\n", "import type {FirstArgument, Transform} from '@dnd-kit/utilities';\n\nimport type {Modifiers, Modifier} from './types';\n\nexport function applyModifiers(\n  modifiers: Modifiers | undefined,\n  {transform, ...args}: FirstArgument<Modifier>\n): Transform {\n  return modifiers?.length\n    ? modifiers.reduce<Transform>((accumulator, modifier) => {\n        return modifier({\n          transform: accumulator,\n          ...args,\n        });\n      }, transform)\n    : transform;\n}\n", "import {useMemo} from 'react';\nimport type {DeepRequired} from '@dnd-kit/utilities';\n\nimport {defaultMeasuringConfiguration} from '../defaults';\nimport type {MeasuringConfiguration} from '../types';\n\nexport function useMeasuringConfiguration(\n  config: MeasuringConfiguration | undefined\n): DeepRequired<MeasuringConfiguration> {\n  return useMemo(\n    () => ({\n      draggable: {\n        ...defaultMeasuringConfiguration.draggable,\n        ...config?.draggable,\n      },\n      droppable: {\n        ...defaultMeasuringConfiguration.droppable,\n        ...config?.droppable,\n      },\n      dragOverlay: {\n        ...defaultMeasuringConfiguration.dragOverlay,\n        ...config?.dragOverlay,\n      },\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [config?.draggable, config?.droppable, config?.dragOverlay]\n  );\n}\n", "import {useRef} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport {getRectDelta} from '../../../utilities/rect';\nimport {getFirstScrollableAncestor} from '../../../utilities/scroll';\nimport type {ClientRect} from '../../../types';\nimport type {DraggableNode} from '../../../store';\nimport type {MeasuringFunction} from '../types';\n\ninterface Options {\n  activeNode: DraggableNode | null | undefined;\n  config: boolean | {x: boolean; y: boolean} | undefined;\n  initialRect: ClientRect | null;\n  measure: MeasuringFunction;\n}\n\nexport function useLayoutShiftScrollCompensation({\n  activeNode,\n  measure,\n  initialRect,\n  config = true,\n}: Options) {\n  const initialized = useRef(false);\n  const {x, y} = typeof config === 'boolean' ? {x: config, y: config} : config;\n\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    }\n\n    // Get the most up to date node ref for the active draggable\n    const node = activeNode?.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    }\n\n    // Only perform layout shift scroll compensation once\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x,\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n", "import React, {\n  memo,\n  createContext,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {unstable_batchedUpdates} from 'react-dom';\nimport {\n  add,\n  getEventCoordinates,\n  getWindow,\n  useLatestValue,\n  useIsomorphicLayoutEffect,\n  useUniqueId,\n} from '@dnd-kit/utilities';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {\n  Action,\n  PublicContext,\n  InternalContext,\n  PublicContextDescriptor,\n  InternalContextDescriptor,\n  getInitialState,\n  reducer,\n} from '../../store';\nimport {DndMonitorContext, useDndMonitorProvider} from '../DndMonitor';\nimport {\n  useAutoScroller,\n  useCachedNode,\n  useCombineActivators,\n  useDragOverlayMeasuring,\n  useDroppableMeasuring,\n  useInitialRect,\n  useRect,\n  useRectDelta,\n  useRects,\n  useScrollableAncestors,\n  useScrollOffsets,\n  useScrollOffsetsDelta,\n  useSensorSetup,\n  useWindowRect,\n} from '../../hooks/utilities';\nimport type {AutoScrollOptions, SyntheticListener} from '../../hooks/utilities';\nimport type {\n  Sensor,\n  SensorContext,\n  SensorDescriptor,\n  SensorActivatorFunction,\n  SensorInstance,\n} from '../../sensors';\nimport {\n  adjustScale,\n  CollisionDetection,\n  defaultCoordinates,\n  getAdjustedRect,\n  getFirstCollision,\n  rectIntersection,\n} from '../../utilities';\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport type {Active, Over} from '../../store/types';\nimport type {\n  DragStartEvent,\n  DragCancelEvent,\n  DragEndEvent,\n  DragMoveEvent,\n  DragOverEvent,\n  UniqueIdentifier,\n  DragPendingEvent,\n  DragAbortEvent,\n} from '../../types';\nimport {\n  Accessibility,\n  Announcements,\n  RestoreFocus,\n  ScreenReaderInstructions,\n} from '../Accessibility';\n\nimport {defaultData, defaultSensors} from './defaults';\nimport {\n  useLayoutShiftScrollCompensation,\n  useMeasuringConfiguration,\n} from './hooks';\nimport type {MeasuringConfiguration} from './types';\n\nexport interface Props {\n  id?: string;\n  accessibility?: {\n    announcements?: Announcements;\n    container?: Element;\n    restoreFocus?: boolean;\n    screenReaderInstructions?: ScreenReaderInstructions;\n  };\n  autoScroll?: boolean | AutoScrollOptions;\n  cancelDrop?: CancelDrop;\n  children?: React.ReactNode;\n  collisionDetection?: CollisionDetection;\n  measuring?: MeasuringConfiguration;\n  modifiers?: Modifiers;\n  sensors?: SensorDescriptor<any>[];\n  onDragAbort?(event: DragAbortEvent): void;\n  onDragPending?(event: DragPendingEvent): void;\n  onDragStart?(event: DragStartEvent): void;\n  onDragMove?(event: DragMoveEvent): void;\n  onDragOver?(event: DragOverEvent): void;\n  onDragEnd?(event: DragEndEvent): void;\n  onDragCancel?(event: DragCancelEvent): void;\n}\n\nexport interface CancelDropArguments extends DragEndEvent {}\n\nexport type CancelDrop = (\n  args: CancelDropArguments\n) => boolean | Promise<boolean>;\n\ninterface DndEvent extends Event {\n  dndKit?: {\n    capturedBy: Sensor<any>;\n  };\n}\n\nexport const ActiveDraggableContext = createContext<Transform>({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1,\n});\n\nenum Status {\n  Uninitialized,\n  Initializing,\n  Initialized,\n}\n\nexport const DndContext = memo(function DndContext({\n  id,\n  accessibility,\n  autoScroll = true,\n  children,\n  sensors = defaultSensors,\n  collisionDetection = rectIntersection,\n  measuring,\n  modifiers,\n  ...props\n}: Props) {\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] =\n    useDndMonitorProvider();\n  const [status, setStatus] = useState<Status>(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {active: activeId, nodes: draggableNodes, translate},\n    droppable: {containers: droppableContainers},\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef<Active['rect']['current']>({\n    initial: null,\n    translated: null,\n  });\n  const active = useMemo<Active | null>(\n    () =>\n      activeId != null\n        ? {\n            id: activeId,\n            // It's possible for the active node to unmount while dragging\n            data: node?.data ?? defaultData,\n            rect: activeRects,\n          }\n        : null,\n    [activeId, node]\n  );\n  const activeRef = useRef<UniqueIdentifier | null>(null);\n  const [activeSensor, setActiveSensor] = useState<SensorInstance | null>(null);\n  const [activatorEvent, setActivatorEvent] = useState<Event | null>(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(`DndDescribedBy`, id);\n  const enabledDroppableContainers = useMemo(\n    () => droppableContainers.getEnabled(),\n    [droppableContainers]\n  );\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {droppableRects, measureDroppableContainers, measuringScheduled} =\n    useDroppableMeasuring(enabledDroppableContainers, {\n      dragging: isInitialized,\n      dependencies: [translate.x, translate.y],\n      config: measuringConfiguration.droppable,\n    });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(\n    () => (activatorEvent ? getEventCoordinates(activatorEvent) : null),\n    [activatorEvent]\n  );\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(\n    activeNode,\n    measuringConfiguration.draggable.measure\n  );\n\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure,\n  });\n\n  const activeNodeRect = useRect(\n    activeNode,\n    measuringConfiguration.draggable.measure,\n    initialActiveNodeRect\n  );\n  const containerNodeRect = useRect(\n    activeNode ? activeNode.parentElement : null\n  );\n  const sensorContext = useRef<SensorContext>({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null,\n  });\n  const overNode = droppableContainers.getNodeFor(\n    sensorContext.current.over?.id\n  );\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure,\n  });\n\n  // Use the rect of the drag overlay if it is mounted\n  const draggingNode = dragOverlay.nodeRef.current ?? activeNode;\n  const draggingNodeRect = isInitialized\n    ? dragOverlay.rect ?? activeNodeRect\n    : null;\n  const usesDragOverlay = Boolean(\n    dragOverlay.nodeRef.current && dragOverlay.rect\n  );\n  // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect);\n\n  // Get the window rect of the dragging node\n  const windowRect = useWindowRect(\n    draggingNode ? getWindow(draggingNode) : null\n  );\n\n  // Get scrollable ancestors of the dragging node\n  const scrollableAncestors = useScrollableAncestors(\n    isInitialized ? overNode ?? activeNode : null\n  );\n  const scrollableAncestorRects = useRects(scrollableAncestors);\n\n  // Apply modifiers\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1,\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect,\n  });\n\n  const pointerCoordinates = activationCoordinates\n    ? add(activationCoordinates, translate)\n    : null;\n\n  const scrollOffsets = useScrollOffsets(scrollableAncestors);\n  // Represents the scroll delta since dragging was initiated\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets);\n  // Represents the scroll delta since the last time the active node rect was measured\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [\n    activeNodeRect,\n  ]);\n\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n\n  const collisionRect = draggingNodeRect\n    ? getAdjustedRect(draggingNodeRect, modifiedTranslate)\n    : null;\n\n  const collisions =\n    active && collisionRect\n      ? collisionDetection({\n          active,\n          collisionRect,\n          droppableRects,\n          droppableContainers: enabledDroppableContainers,\n          pointerCoordinates,\n        })\n      : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState<Over | null>(null);\n\n  // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n  const appliedTranslate = usesDragOverlay\n    ? modifiedTranslate\n    : add(modifiedTranslate, activeNodeScrollDelta);\n\n  const transform = adjustScale(\n    appliedTranslate,\n    over?.rect ?? null,\n    activeNodeRect\n  );\n\n  const activeSensorRef = useRef<SensorInstance | null>(null);\n  const instantiateSensor = useCallback(\n    (\n      event: React.SyntheticEvent,\n      {sensor: Sensor, options}: SensorDescriptor<any>\n    ) => {\n      if (activeRef.current == null) {\n        return;\n      }\n\n      const activeNode = draggableNodes.get(activeRef.current);\n\n      if (!activeNode) {\n        return;\n      }\n\n      const activatorEvent = event.nativeEvent;\n\n      const sensorInstance = new Sensor({\n        active: activeRef.current,\n        activeNode,\n        event: activatorEvent,\n        options,\n        // Sensors need to be instantiated with refs for arguments that change over time\n        // otherwise they are frozen in time with the stale arguments\n        context: sensorContext,\n        onAbort(id) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragAbort} = latestProps.current;\n          const event: DragAbortEvent = {id};\n          onDragAbort?.(event);\n          dispatchMonitorEvent({type: 'onDragAbort', event});\n        },\n        onPending(id, constraint, initialCoordinates, offset) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragPending} = latestProps.current;\n          const event: DragPendingEvent = {\n            id,\n            constraint,\n            initialCoordinates,\n            offset,\n          };\n\n          onDragPending?.(event);\n          dispatchMonitorEvent({type: 'onDragPending', event});\n        },\n        onStart(initialCoordinates) {\n          const id = activeRef.current;\n\n          if (id == null) {\n            return;\n          }\n\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragStart} = latestProps.current;\n          const event: DragStartEvent = {\n            activatorEvent,\n            active: {id, data: draggableNode.data, rect: activeRects},\n          };\n\n          unstable_batchedUpdates(() => {\n            onDragStart?.(event);\n            setStatus(Status.Initializing);\n            dispatch({\n              type: Action.DragStart,\n              initialCoordinates,\n              active: id,\n            });\n            dispatchMonitorEvent({type: 'onDragStart', event});\n            setActiveSensor(activeSensorRef.current);\n            setActivatorEvent(activatorEvent);\n          });\n        },\n        onMove(coordinates) {\n          dispatch({\n            type: Action.DragMove,\n            coordinates,\n          });\n        },\n        onEnd: createHandler(Action.DragEnd),\n        onCancel: createHandler(Action.DragCancel),\n      });\n\n      activeSensorRef.current = sensorInstance;\n\n      function createHandler(type: Action.DragEnd | Action.DragCancel) {\n        return async function handler() {\n          const {active, collisions, over, scrollAdjustedTranslate} =\n            sensorContext.current;\n          let event: DragEndEvent | null = null;\n\n          if (active && scrollAdjustedTranslate) {\n            const {cancelDrop} = latestProps.current;\n\n            event = {\n              activatorEvent,\n              active: active,\n              collisions,\n              delta: scrollAdjustedTranslate,\n              over,\n            };\n\n            if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n              const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n              if (shouldCancel) {\n                type = Action.DragCancel;\n              }\n            }\n          }\n\n          activeRef.current = null;\n\n          unstable_batchedUpdates(() => {\n            dispatch({type});\n            setStatus(Status.Uninitialized);\n            setOver(null);\n            setActiveSensor(null);\n            setActivatorEvent(null);\n            activeSensorRef.current = null;\n\n            const eventName =\n              type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n            if (event) {\n              const handler = latestProps.current[eventName];\n\n              handler?.(event);\n              dispatchMonitorEvent({type: eventName, event});\n            }\n          });\n        };\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes]\n  );\n\n  const bindActivatorToSensorInstantiator = useCallback(\n    (\n      handler: SensorActivatorFunction<any>,\n      sensor: SensorDescriptor<any>\n    ): SyntheticListener['handler'] => {\n      return (event, active) => {\n        const nativeEvent = event.nativeEvent as DndEvent;\n        const activeDraggableNode = draggableNodes.get(active);\n\n        if (\n          // Another sensor is already instantiating\n          activeRef.current !== null ||\n          // No active draggable\n          !activeDraggableNode ||\n          // Event has already been captured\n          nativeEvent.dndKit ||\n          nativeEvent.defaultPrevented\n        ) {\n          return;\n        }\n\n        const activationContext = {\n          active: activeDraggableNode,\n        };\n        const shouldActivate = handler(\n          event,\n          sensor.options,\n          activationContext\n        );\n\n        if (shouldActivate === true) {\n          nativeEvent.dndKit = {\n            capturedBy: sensor.sensor,\n          };\n\n          activeRef.current = active;\n          instantiateSensor(event, sensor);\n        }\n      };\n    },\n    [draggableNodes, instantiateSensor]\n  );\n\n  const activators = useCombineActivators(\n    sensors,\n    bindActivatorToSensorInstantiator\n  );\n\n  useSensorSetup(sensors);\n\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n\n  useEffect(\n    () => {\n      const {onDragMove} = latestProps.current;\n      const {active, activatorEvent, collisions, over} = sensorContext.current;\n\n      if (!active || !activatorEvent) {\n        return;\n      }\n\n      const event: DragMoveEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        onDragMove?.(event);\n        dispatchMonitorEvent({type: 'onDragMove', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]\n  );\n\n  useEffect(\n    () => {\n      const {\n        active,\n        activatorEvent,\n        collisions,\n        droppableContainers,\n        scrollAdjustedTranslate,\n      } = sensorContext.current;\n\n      if (\n        !active ||\n        activeRef.current == null ||\n        !activatorEvent ||\n        !scrollAdjustedTranslate\n      ) {\n        return;\n      }\n\n      const {onDragOver} = latestProps.current;\n      const overContainer = droppableContainers.get(overId);\n      const over =\n        overContainer && overContainer.rect.current\n          ? {\n              id: overContainer.id,\n              rect: overContainer.rect.current,\n              data: overContainer.data,\n              disabled: overContainer.disabled,\n            }\n          : null;\n      const event: DragOverEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        setOver(over);\n        onDragOver?.(event);\n        dispatchMonitorEvent({type: 'onDragOver', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [overId]\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate,\n    };\n\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect,\n    };\n  }, [\n    active,\n    activeNode,\n    collisions,\n    collisionRect,\n    draggableNodes,\n    draggingNode,\n    draggingNodeRect,\n    droppableRects,\n    droppableContainers,\n    over,\n    scrollableAncestors,\n    scrollAdjustedTranslate,\n  ]);\n\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n  });\n\n  const publicContext = useMemo(() => {\n    const context: PublicContextDescriptor = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect,\n    };\n\n    return context;\n  }, [\n    active,\n    activeNode,\n    activeNodeRect,\n    activatorEvent,\n    collisions,\n    containerNodeRect,\n    dragOverlay,\n    draggableNodes,\n    droppableContainers,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    measuringConfiguration,\n    measuringScheduled,\n    windowRect,\n  ]);\n\n  const internalContext = useMemo(() => {\n    const context: InternalContextDescriptor = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById,\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers,\n    };\n\n    return context;\n  }, [\n    activatorEvent,\n    activators,\n    active,\n    activeNodeRect,\n    dispatch,\n    draggableDescribedById,\n    draggableNodes,\n    over,\n    measureDroppableContainers,\n  ]);\n\n  return (\n    <DndMonitorContext.Provider value={registerMonitorListener}>\n      <InternalContext.Provider value={internalContext}>\n        <PublicContext.Provider value={publicContext}>\n          <ActiveDraggableContext.Provider value={transform}>\n            {children}\n          </ActiveDraggableContext.Provider>\n        </PublicContext.Provider>\n        <RestoreFocus disabled={accessibility?.restoreFocus === false} />\n      </InternalContext.Provider>\n      <Accessibility\n        {...accessibility}\n        hiddenTextDescribedById={draggableDescribedById}\n      />\n    </DndMonitorContext.Provider>\n  );\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll =\n      activeSensor?.autoScrollEnabled === false;\n    const autoScrollGloballyDisabled =\n      typeof autoScroll === 'object'\n        ? autoScroll.enabled === false\n        : autoScroll === false;\n    const enabled =\n      isInitialized &&\n      !activeSensorDisablesAutoscroll &&\n      !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled,\n      };\n    }\n\n    return {enabled};\n  }\n});\n", "import {createContext, useContext, useMemo} from 'react';\nimport {\n  Transform,\n  useNodeRef,\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Data} from '../store';\nimport type {UniqueIdentifier} from '../types';\nimport {ActiveDraggableContext} from '../components/DndContext';\nimport {useSyntheticListeners, SyntheticListenerMap} from './utilities';\n\nexport interface UseDraggableArguments {\n  id: UniqueIdentifier;\n  data?: Data;\n  disabled?: boolean;\n  attributes?: {\n    role?: string;\n    roleDescription?: string;\n    tabIndex?: number;\n  };\n}\n\nexport interface DraggableAttributes {\n  role: string;\n  tabIndex: number;\n  'aria-disabled': boolean;\n  'aria-pressed': boolean | undefined;\n  'aria-roledescription': string;\n  'aria-describedby': string;\n}\n\nexport type DraggableSyntheticListeners = SyntheticListenerMap | undefined;\n\nconst NullContext = createContext<any>(null);\n\nconst defaultRole = 'button';\n\nconst ID_PREFIX = 'Draggable';\n\nexport function useDraggable({\n  id,\n  data,\n  disabled = false,\n  attributes,\n}: UseDraggableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over,\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0,\n  } = attributes ?? {};\n  const isDragging = active?.id === id;\n  const transform: Transform | null = useContext(\n    isDragging ? ActiveDraggableContext : NullContext\n  );\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      draggableNodes.set(id, {id, key, node, activatorNode, data: dataRef});\n\n      return () => {\n        const node = draggableNodes.get(id);\n\n        if (node && node.key === key) {\n          draggableNodes.delete(id);\n        }\n      };\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes, id]\n  );\n\n  const memoizedAttributes: DraggableAttributes = useMemo(\n    () => ({\n      role,\n      tabIndex,\n      'aria-disabled': disabled,\n      'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n      'aria-roledescription': roleDescription,\n      'aria-describedby': ariaDescribedById.draggable,\n    }),\n    [\n      disabled,\n      role,\n      tabIndex,\n      isDragging,\n      roleDescription,\n      ariaDescribedById.draggable,\n    ]\n  );\n\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n  };\n}\n", "import {ContextType, useContext} from 'react';\nimport {PublicContext} from '../store';\n\nexport function useDndContext() {\n  return useContext(PublicContext);\n}\n\nexport type UseDndContextReturnValue = ContextType<typeof PublicContext>;\n", "import {useCallback, useContext, useEffect, useRef} from 'react';\nimport {useLatestValue, useNodeRef, useUniqueId} from '@dnd-kit/utilities';\n\nimport {InternalContext, Action, Data} from '../store';\nimport type {ClientRect, UniqueIdentifier} from '../types';\n\nimport {useResizeObserver} from './utilities';\n\ninterface ResizeObserverConfig {\n  /** Whether the ResizeObserver should be disabled entirely */\n  disabled?: boolean;\n  /** Resize events may affect the layout and position of other droppable containers.\n   * Specify an array of `UniqueIdentifier` of droppable containers that should also be re-measured\n   * when this droppable container resizes. Specifying an empty array re-measures all droppable containers.\n   */\n  updateMeasurementsFor?: UniqueIdentifier[];\n  /** Represents the debounce timeout between when resize events are observed and when elements are re-measured */\n  timeout?: number;\n}\n\nexport interface UseDroppableArguments {\n  id: UniqueIdentifier;\n  disabled?: boolean;\n  data?: Data;\n  resizeObserverConfig?: ResizeObserverConfig;\n}\n\nconst ID_PREFIX = 'Droppable';\n\nconst defaultResizeObserverConfig = {\n  timeout: 25,\n};\n\nexport function useDroppable({\n  data,\n  disabled = false,\n  id,\n  resizeObserverConfig,\n}: UseDroppableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {active, dispatch, over, measureDroppableContainers} =\n    useContext(InternalContext);\n  const previous = useRef({disabled});\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef<ClientRect | null>(null);\n  const callbackId = useRef<NodeJS.Timeout | null>(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout,\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig,\n  };\n  const ids = useLatestValue(updateMeasurementsFor ?? id);\n  const handleResize = useCallback(\n    () => {\n      if (!resizeObserverConnected.current) {\n        // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n        // assuming the element is rendered and displayed.\n        resizeObserverConnected.current = true;\n        return;\n      }\n\n      if (callbackId.current != null) {\n        clearTimeout(callbackId.current);\n      }\n\n      callbackId.current = setTimeout(() => {\n        measureDroppableContainers(\n          Array.isArray(ids.current) ? ids.current : [ids.current]\n        );\n        callbackId.current = null;\n      }, resizeObserverTimeout);\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [resizeObserverTimeout]\n  );\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active,\n  });\n  const handleNodeChange = useCallback(\n    (newElement: HTMLElement | null, previousElement: HTMLElement | null) => {\n      if (!resizeObserver) {\n        return;\n      }\n\n      if (previousElement) {\n        resizeObserver.unobserve(previousElement);\n        resizeObserverConnected.current = false;\n      }\n\n      if (newElement) {\n        resizeObserver.observe(newElement);\n      }\n    },\n    [resizeObserver]\n  );\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n\n  useEffect(\n    () => {\n      dispatch({\n        type: Action.RegisterDroppable,\n        element: {\n          id,\n          key,\n          disabled,\n          node: nodeRef,\n          rect,\n          data: dataRef,\n        },\n      });\n\n      return () =>\n        dispatch({\n          type: Action.UnregisterDroppable,\n          key,\n          id,\n        });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [id]\n  );\n\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled,\n      });\n\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n\n  return {\n    active,\n    rect,\n    isOver: over?.id === id,\n    node: nodeRef,\n    over,\n    setNodeRef,\n  };\n}\n", "import React, {cloneElement, useState} from 'react';\nimport {useIsomorphicLayoutEffect, usePrevious} from '@dnd-kit/utilities';\n\nimport type {UniqueIdentifier} from '../../../../types';\n\nexport type Animation = (\n  key: UniqueIdentifier,\n  node: HTMLElement\n) => Promise<void> | void;\n\nexport interface Props {\n  animation: Animation;\n  children: React.ReactElement | null;\n}\n\nexport function AnimationManager({animation, children}: Props) {\n  const [\n    clonedChildren,\n    setClonedChildren,\n  ] = useState<React.ReactElement | null>(null);\n  const [element, setElement] = useState<HTMLElement | null>(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren?.key;\n    const id = clonedChildren?.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n\n  return (\n    <>\n      {children}\n      {clonedChildren ? cloneElement(clonedChildren, {ref: setElement}) : null}\n    </>\n  );\n}\n", "import React from 'react';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {InternalContext, defaultInternalContext} from '../../../../store';\nimport {ActiveDraggableContext} from '../../../DndContext';\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nconst defaultTransform: Transform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport function NullifiedContextProvider({children}: Props) {\n  return (\n    <InternalContext.Provider value={defaultInternalContext}>\n      <ActiveDraggableContext.Provider value={defaultTransform}>\n        {children}\n      </ActiveDraggableContext.Provider>\n    </InternalContext.Provider>\n  );\n}\n", "import React, {forwardRef} from 'react';\nimport {CSS, isKeyboardEvent} from '@dnd-kit/utilities';\n\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {getRelativeTransformOrigin} from '../../../../utilities';\nimport type {ClientRect, UniqueIdentifier} from '../../../../types';\n\ntype TransitionGetter = (\n  activatorEvent: Event | null\n) => React.CSSProperties['transition'] | undefined;\n\nexport interface Props {\n  as: keyof JSX.IntrinsicElements;\n  activatorEvent: Event | null;\n  adjustScale?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  id: UniqueIdentifier;\n  rect: ClientRect | null;\n  style?: React.CSSProperties;\n  transition?: string | TransitionGetter;\n  transform: Transform;\n}\n\nconst baseStyles: React.CSSProperties = {\n  position: 'fixed',\n  touchAction: 'none',\n};\n\nconst defaultTransition: TransitionGetter = (activatorEvent) => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nexport const PositionedOverlay = forwardRef<HTMLElement, Props>(\n  (\n    {\n      as,\n      activatorEvent,\n      adjustScale,\n      children,\n      className,\n      rect,\n      style,\n      transform,\n      transition = defaultTransition,\n    },\n    ref\n  ) => {\n    if (!rect) {\n      return null;\n    }\n\n    const scaleAdjustedTransform = adjustScale\n      ? transform\n      : {\n          ...transform,\n          scaleX: 1,\n          scaleY: 1,\n        };\n    const styles: React.CSSProperties | undefined = {\n      ...baseStyles,\n      width: rect.width,\n      height: rect.height,\n      top: rect.top,\n      left: rect.left,\n      transform: CSS.Transform.toString(scaleAdjustedTransform),\n      transformOrigin:\n        adjustScale && activatorEvent\n          ? getRelativeTransformOrigin(\n              activatorEvent as MouseEvent | KeyboardEvent | TouchEvent,\n              rect\n            )\n          : undefined,\n      transition:\n        typeof transition === 'function'\n          ? transition(activatorEvent)\n          : transition,\n      ...style,\n    };\n\n    return React.createElement(\n      as,\n      {\n        className,\n        style: styles,\n        ref,\n      },\n      children\n    );\n  }\n);\n", "import {CSS, useEvent, getWindow} from '@dnd-kit/utilities';\nimport type {DeepRequired, Transform} from '@dnd-kit/utilities';\n\nimport type {\n  Active,\n  DraggableNode,\n  DraggableNodes,\n  DroppableContainers,\n} from '../../../store';\nimport type {ClientRect, UniqueIdentifier} from '../../../types';\nimport {getMeasurableNode} from '../../../utilities/nodes';\nimport {scrollIntoViewIfNeeded} from '../../../utilities/scroll';\nimport {parseTransform} from '../../../utilities/transform';\nimport type {MeasuringConfiguration} from '../../DndContext';\nimport type {Animation} from '../components';\n\ninterface SharedParameters {\n  active: {\n    id: UniqueIdentifier;\n    data: Active['data'];\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  dragOverlay: {\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n}\n\nexport interface KeyframeResolverParameters extends SharedParameters {\n  transform: {\n    initial: Transform;\n    final: Transform;\n  };\n}\n\nexport type KeyframeResolver = (\n  parameters: KeyframeResolverParameters\n) => Keyframe[];\n\nexport interface DropAnimationOptions {\n  keyframes?: KeyframeResolver;\n  duration?: number;\n  easing?: string;\n  sideEffects?: DropAnimationSideEffects | null;\n}\n\nexport type DropAnimation = DropAnimationFunction | DropAnimationOptions;\n\ninterface Arguments {\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n  config?: DropAnimation | null;\n}\n\nexport interface DropAnimationFunctionArguments extends SharedParameters {\n  transform: Transform;\n}\n\nexport type DropAnimationFunction = (\n  args: DropAnimationFunctionArguments\n) => Promise<void> | void;\n\ntype CleanupFunction = () => void;\n\nexport interface DropAnimationSideEffectsParameters extends SharedParameters {}\n\nexport type DropAnimationSideEffects = (\n  parameters: DropAnimationSideEffectsParameters\n) => CleanupFunction | void;\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\ntype Styles = ExtractStringProperties<CSSStyleDeclaration>;\n\ninterface DefaultDropAnimationSideEffectsOptions {\n  className?: {\n    active?: string;\n    dragOverlay?: string;\n  };\n  styles?: {\n    active?: Styles;\n    dragOverlay?: Styles;\n  };\n}\n\nexport const defaultDropAnimationSideEffects = (\n  options: DefaultDropAnimationSideEffectsOptions\n): DropAnimationSideEffects => ({active, dragOverlay}) => {\n  const originalStyles: Record<string, string> = {};\n  const {styles, className} = options;\n\n  if (styles?.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles?.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className?.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className?.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className?.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver: KeyframeResolver = ({\n  transform: {initial, final},\n}) => [\n  {\n    transform: CSS.Transform.toString(initial),\n  },\n  {\n    transform: CSS.Transform.toString(final),\n  },\n];\n\nexport const defaultDropAnimationConfiguration: Required<DropAnimationOptions> = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0',\n      },\n    },\n  }),\n};\n\nexport function useDropAnimation({\n  config,\n  draggableNodes,\n  droppableContainers,\n  measuringConfiguration,\n}: Arguments) {\n  return useEvent<Animation>((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable: DraggableNode | undefined = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n    const {transform} = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation: DropAnimationFunction =\n      typeof config === 'function'\n        ? config\n        : createDefaultDropAnimation(config);\n\n    scrollIntoViewIfNeeded(\n      activeNode,\n      measuringConfiguration.draggable.measure\n    );\n\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode),\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode),\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform,\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(\n  options: DropAnimationOptions | undefined\n): DropAnimationFunction {\n  const {duration, easing, sideEffects, keyframes} = {\n    ...defaultDropAnimationConfiguration,\n    ...options,\n  };\n\n  return ({active, dragOverlay, transform, ...rest}) => {\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top,\n    };\n\n    const scale = {\n      scaleX:\n        transform.scaleX !== 1\n          ? (active.rect.width * transform.scaleX) / dragOverlay.rect.width\n          : 1,\n      scaleY:\n        transform.scaleY !== 1\n          ? (active.rect.height * transform.scaleY) / dragOverlay.rect.height\n          : 1,\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale,\n    };\n\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {initial: transform, final: finalTransform},\n    });\n\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects?.({active, dragOverlay, ...rest});\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards',\n    });\n\n    return new Promise((resolve) => {\n      animation.onfinish = () => {\n        cleanup?.();\n        resolve();\n      };\n    });\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {UniqueIdentifier} from '../../../types';\n\nlet key = 0;\n\nexport function useKey(id: UniqueIdentifier | undefined) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n", "import React, {useContext} from 'react';\n\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport {ActiveDraggableContext} from '../DndContext';\nimport {useDndContext} from '../../hooks';\nimport {useInitialValue} from '../../hooks/utilities';\n\nimport {\n  AnimationManager,\n  NullifiedContextProvider,\n  PositionedOverlay,\n} from './components';\nimport type {PositionedOverlayProps} from './components';\n\nimport {useDropAnimation, useKey} from './hooks';\nimport type {DropAnimation} from './hooks';\n\nexport interface Props\n  extends Pick<\n    PositionedOverlayProps,\n    'adjustScale' | 'children' | 'className' | 'style' | 'transition'\n  > {\n  dropAnimation?: DropAnimation | null | undefined;\n  modifiers?: Modifiers;\n  wrapperElement?: keyof JSX.IntrinsicElements;\n  zIndex?: number;\n}\n\nexport const DragOverlay = React.memo(\n  ({\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999,\n  }: Props) => {\n    const {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggableNodes,\n      droppableContainers,\n      dragOverlay,\n      over,\n      measuringConfiguration,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      windowRect,\n    } = useDndContext();\n    const transform = useContext(ActiveDraggableContext);\n    const key = useKey(active?.id);\n    const modifiedTransform = applyModifiers(modifiers, {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggingNodeRect: dragOverlay.rect,\n      over,\n      overlayNodeRect: dragOverlay.rect,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      transform,\n      windowRect,\n    });\n    const initialRect = useInitialValue(activeNodeRect);\n    const dropAnimation = useDropAnimation({\n      config: dropAnimationConfig,\n      draggableNodes,\n      droppableContainers,\n      measuringConfiguration,\n    });\n    // We need to wait for the active node to be measured before connecting the drag overlay ref\n    // otherwise collisions can be computed against a mispositioned drag overlay\n    const ref = initialRect ? dragOverlay.setRef : undefined;\n\n    return (\n      <NullifiedContextProvider>\n        <AnimationManager animation={dropAnimation}>\n          {active && key ? (\n            <PositionedOverlay\n              key={key}\n              id={active.id}\n              ref={ref}\n              as={wrapperElement}\n              activatorEvent={activatorEvent}\n              adjustScale={adjustScale}\n              className={className}\n              transition={transition}\n              rect={initialRect}\n              style={{\n                zIndex,\n                ...style,\n              }}\n              transform={modifiedTransform}\n            >\n              {children}\n            </PositionedOverlay>\n          ) : null}\n        </AnimationManager>\n      </NullifiedContextProvider>\n    );\n  }\n);\n"], "mappings": ";;;;AAIO,MAAMA,iBAAiB,gBAAGC,aAAa,CAA0B,IAA1B,CAAvC;SCCSC,cAAcC,QAAA;EAC5B,MAAMC,gBAAgB,GAAGC,UAAU,CAACL,iBAAD,CAAnC;EAEAM,SAAS,CAAC;IACR,IAAI,CAACF,gBAAL,EAAuB;MACrB,MAAM,IAAIG,KAAJ,CACJ,8DADI,CAAN;;IAKF,MAAMC,WAAW,GAAGJ,gBAAgB,CAACD,QAAD,CAApC;IAEA,OAAOK,WAAP;GATO,EAUN,CAACL,QAAD,EAAWC,gBAAX,CAVM,CAAT;AAWD;SCfeK,sBAAA;EACd,MAAM,CAACC,SAAD,IAAcC,QAAQ,CAAC,MAAM,IAAIC,GAAJ,EAAP,CAA5B;EAEA,MAAMR,gBAAgB,GAAGS,WAAW,CACjCV,QAAD;IACEO,SAAS,CAACI,GAAV,CAAcX,QAAd;IACA,OAAO,MAAMO,SAAS,CAACK,MAAV,CAAiBZ,QAAjB,CAAb;GAHgC,EAKlC,CAACO,SAAD,CALkC,CAApC;EAQA,MAAMM,QAAQ,GAAGH,WAAW,CAC1BI,IAAA;QAAC;MAACC,IAAD;MAAOC;;IACNT,SAAS,CAACU,OAAV,CAAmBjB,QAAD;MAAA,IAAAkB,cAAA;MAAA,QAAAA,cAAA,GAAclB,QAAQ,CAACe,IAAD,CAAtB,qBAAcG,cAAA,CAAAC,IAAA,CAAAnB,QAAQ,EAASgB,KAAT,CAAtB;KAAlB;GAFwB,EAI1B,CAACT,SAAD,CAJ0B,CAA5B;EAOA,OAAO,CAACM,QAAD,EAAWZ,gBAAX,CAAP;AACD;MCrBYmB,+BAA+B,GAA6B;EACvEC,SAAS;AAD8D,CAAlE;AAQP,MAAaC,oBAAoB,GAAkB;EACjDC,WAAWA,CAAAT,IAAA;QAAC;MAACU;;IACX,qCAAmCA,MAAM,CAACC,EAA1C;GAF+C;EAIjDC,UAAUA,CAAAC,KAAA;QAAC;MAACH,MAAD;MAASI;;IAClB,IAAIA,IAAJ,EAAU;MACR,2BAAyBJ,MAAM,CAACC,EAAhC,uCAAoEG,IAAI,CAACH,EAAzE;;IAGF,2BAAyBD,MAAM,CAACC,EAAhC;GAT+C;EAWjDI,SAASA,CAAAC,KAAA;QAAC;MAACN,MAAD;MAASI;;IACjB,IAAIA,IAAJ,EAAU;MACR,2BAAyBJ,MAAM,CAACC,EAAhC,yCAAsEG,IAAI,CAACH,EAA3E;;IAGF,2BAAyBD,MAAM,CAACC,EAAhC;GAhB+C;EAkBjDM,YAAYA,CAAAC,KAAA;QAAC;MAACR;;IACZ,mDAAiDA,MAAM,CAACC,EAAxD;;AAnB+C,CAA5C;SCUSQ,cAAAnB,IAAA;MAAc;IAC5BoB,aAAa,GAAGZ,oBADY;IAE5Ba,SAF4B;IAG5BC,uBAH4B;IAI5BC,wBAAwB,GAAGjB;;EAE3B,MAAM;IAACkB,QAAD;IAAWC;MAAgBC,eAAe,EAAhD;EACA,MAAMC,YAAY,GAAGC,WAAW,iBAAhC;EACA,MAAM,CAACC,OAAD,EAAUC,UAAV,IAAwBpC,QAAQ,CAAC,KAAD,CAAtC;EAEAL,SAAS,CAAC;IACRyC,UAAU,CAAC,IAAD,CAAV;GADO,EAEN,EAFM,CAAT;EAIA7C,aAAa,CACX8C,OAAO,CACL,OAAO;IACLtB,WAAWA,CAAAI,KAAA;UAAC;QAACH;;MACXc,QAAQ,CAACJ,aAAa,CAACX,WAAd,CAA0B;QAACC;OAA3B,CAAD,CAAR;KAFG;IAILsB,UAAUA,CAAAhB,KAAA;UAAC;QAACN,MAAD;QAASI;;MAClB,IAAIM,aAAa,CAACY,UAAlB,EAA8B;QAC5BR,QAAQ,CAACJ,aAAa,CAACY,UAAd,CAAyB;UAACtB,MAAD;UAASI;SAAlC,CAAD,CAAR;;KANC;IASLF,UAAUA,CAAAM,KAAA;UAAC;QAACR,MAAD;QAASI;;MAClBU,QAAQ,CAACJ,aAAa,CAACR,UAAd,CAAyB;QAACF,MAAD;QAASI;OAAlC,CAAD,CAAR;KAVG;IAYLC,SAASA,CAAAkB,KAAA;UAAC;QAACvB,MAAD;QAASI;;MACjBU,QAAQ,CAACJ,aAAa,CAACL,SAAd,CAAwB;QAACL,MAAD;QAASI;OAAjC,CAAD,CAAR;KAbG;IAeLG,YAAYA,CAAAiB,KAAA;UAAC;QAACxB,MAAD;QAASI;;MACpBU,QAAQ,CAACJ,aAAa,CAACH,YAAd,CAA2B;QAACP,MAAD;QAASI;OAApC,CAAD,CAAR;;GAhBJ,CADK,EAoBL,CAACU,QAAD,EAAWJ,aAAX,CApBK,CADI,CAAb;EAyBA,IAAI,CAACS,OAAL,EAAc;IACZ,OAAO,IAAP;;EAGF,MAAMM,MAAM,GACVC,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAE,QAAA,QACEF,KAAA,CAAAC,aAAA,CAACE,UAAD;IACE5B,EAAE,EAAEW,uBAAA;IACJkB,KAAK,EAAEjB,wBAAwB,CAAChB;GAFlC,CADF,EAKE6B,KAAA,CAAAC,aAAA,CAACI,UAAD;IAAY9B,EAAE,EAAEgB,YAAA;IAAcF,YAAY,EAAEA;GAA5C,CALF,CADF;EAUA,OAAOJ,SAAS,GAAGqB,YAAY,CAACP,MAAD,EAASd,SAAT,CAAf,GAAqCc,MAArD;AACD;ACvED,IAAYQ,MAAZ;AAAA,WAAYA,MAAA;EACVA,MAAA;EACAA,MAAA;EACAA,MAAA;EACAA,MAAA;EACAA,MAAA;EACAA,MAAA;EACAA,MAAA;EACAA,MAAA;AACD,CATD,EAAYA,MAAM,KAANA,MAAM,MAAlB;SCHgBC,KAAA;SCIAC,UACdC,MAAA,EACAC,OAAA;EAEA,OAAOhB,OAAO,CACZ,OAAO;IACLe,MADK;IAELC,OAAO,EAAEA,OAAF,WAAEA,OAAF,GAAc;GAFvB,CADY;EAAA;EAMZ,CAACD,MAAD,EAASC,OAAT,CANY,CAAd;AAQD;SCZeC,WAAA;oCACXC,OAAA,OAAAC,KAAA,CAAAC,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;IAAAH,OAAA,CAAAG,IAAA,IAAAC,SAAA,CAAAD,IAAA;;EAEH,OAAOrB,OAAO,CACZ,MACE,CAAC,GAAGkB,OAAJ,EAAaK,MAAb,CACGR,MAAD,IAA6CA,MAAM,IAAI,IADzD,CAFU;EAAA;EAMZ,CAAC,GAAGG,OAAJ,CANY,CAAd;AAQD;MCbYM,kBAAkB,gBAAgBC,MAAM,CAACC,MAAP,CAAc;EAC3DC,CAAC,EAAE,CADwD;EAE3DC,CAAC,EAAE;AAFwD,CAAd,CAAxC;;ACAP;;;AAGA,SAAgBC,gBAAgBC,EAAA,EAAiBC,EAAA;EAC/C,OAAOC,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACH,CAAH,GAAOI,EAAE,CAACJ,CAAnB,EAAsB,CAAtB,IAA2BK,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACF,CAAH,GAAOG,EAAE,CAACH,CAAnB,EAAsB,CAAtB,CAArC,CAAP;AACD;SCJeO,2BACdhE,KAAA,EACAiE,IAAA;EAEA,MAAMC,gBAAgB,GAAGC,mBAAmB,CAACnE,KAAD,CAA5C;EAEA,IAAI,CAACkE,gBAAL,EAAuB;IACrB,OAAO,KAAP;;EAGF,MAAME,eAAe,GAAG;IACtBZ,CAAC,EAAG,CAACU,gBAAgB,CAACV,CAAjB,GAAqBS,IAAI,CAACI,IAA3B,IAAmCJ,IAAI,CAACK,KAAzC,GAAkD,GAD/B;IAEtBb,CAAC,EAAG,CAACS,gBAAgB,CAACT,CAAjB,GAAqBQ,IAAI,CAACM,GAA3B,IAAkCN,IAAI,CAACO,MAAxC,GAAkD;GAFvD;EAKA,OAAUJ,eAAe,CAACZ,CAA1B,UAAgCY,eAAe,CAACX,CAAhD;AACD;;ACdD;;;AAGA,SAAgBgB,kBAAA3E,IAAA,EAAAa,KAAA;MACd;IAAC+D,IAAI,EAAE;MAACpC,KAAK,EAAEqC;;;MACf;IAACD,IAAI,EAAE;MAACpC,KAAK,EAAEsC;;;EAEf,OAAOD,CAAC,GAAGC,CAAX;AACD;AAED;;;;AAGA,SAAgBC,mBAAA/D,KAAA,EAAAE,KAAA;MACd;IAAC0D,IAAI,EAAE;MAACpC,KAAK,EAAEqC;;;MACf;IAACD,IAAI,EAAE;MAACpC,KAAK,EAAEsC;;;EAEf,OAAOA,CAAC,GAAGD,CAAX;AACD;AAED;;;;;AAIA,SAAgBG,mBAAA/C,KAAA;MAAmB;IAACsC,IAAD;IAAOE,GAAP;IAAYC,MAAZ;IAAoBF;;EACrD,OAAO,CACL;IACEd,CAAC,EAAEa,IADL;IAEEZ,CAAC,EAAEc;GAHA,EAKL;IACEf,CAAC,EAAEa,IAAI,GAAGC,KADZ;IAEEb,CAAC,EAAEc;GAPA,EASL;IACEf,CAAC,EAAEa,IADL;IAEEZ,CAAC,EAAEc,GAAG,GAAGC;GAXN,EAaL;IACEhB,CAAC,EAAEa,IAAI,GAAGC,KADZ;IAEEb,CAAC,EAAEc,GAAG,GAAGC;GAfN,CAAP;AAkBD;AAaD,SAAgBO,kBACdC,UAAA,EACAC,QAAA;EAEA,IAAI,CAACD,UAAD,IAAeA,UAAU,CAACE,MAAX,KAAsB,CAAzC,EAA4C;IAC1C,OAAO,IAAP;;EAGF,MAAM,CAACC,cAAD,IAAmBH,UAAzB;EAEA,OAAOC,QAAQ,GAAGE,cAAc,CAACF,QAAD,CAAjB,GAA8BE,cAA7C;AACD;;AClED;;;;AAGA,SAASC,iBAATA,CACEnB,IADF,EAEEI,IAFF,EAGEE,GAHF;MAEEF,IAAA;IAAAA,IAAA,GAAOJ,IAAI,CAACI,IAAA;;MACZE,GAAA;IAAAA,GAAA,GAAMN,IAAI,CAACM,GAAA;;EAEX,OAAO;IACLf,CAAC,EAAEa,IAAI,GAAGJ,IAAI,CAACK,KAAL,GAAa,GADlB;IAELb,CAAC,EAAEc,GAAG,GAAGN,IAAI,CAACO,MAAL,GAAc;GAFzB;AAID;AAED;;;;;AAIA,MAAaa,aAAa,GAAuBvF,IAAA;MAAC;IAChDwF,aADgD;IAEhDC,cAFgD;IAGhDC;;EAEA,MAAMC,UAAU,GAAGL,iBAAiB,CAClCE,aADkC,EAElCA,aAAa,CAACjB,IAFoB,EAGlCiB,aAAa,CAACf,GAHoB,CAApC;EAKA,MAAMS,UAAU,GAA0B,EAA1C;EAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,EAAsD;IACpD,MAAM;MAAC/E;QAAMiF,kBAAb;IACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBlF,EAAnB,CAAb;IAEA,IAAIwD,IAAJ,EAAU;MACR,MAAM2B,WAAW,GAAGlC,eAAe,CAAC0B,iBAAiB,CAACnB,IAAD,CAAlB,EAA0BwB,UAA1B,CAAnC;MAEAT,UAAU,CAACa,IAAX,CAAgB;QAACpF,EAAD;QAAKiE,IAAI,EAAE;UAACgB,kBAAD;UAAqBpD,KAAK,EAAEsD;;OAAvD;;;EAIJ,OAAOZ,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAxBM;;ACnBP;;;;;AAIA,MAAasB,cAAc,GAAuBjG,IAAA;MAAC;IACjDwF,aADiD;IAEjDC,cAFiD;IAGjDC;;EAEA,MAAMQ,OAAO,GAAGlB,kBAAkB,CAACQ,aAAD,CAAlC;EACA,MAAMN,UAAU,GAA0B,EAA1C;EAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,EAAsD;IACpD,MAAM;MAAC/E;QAAMiF,kBAAb;IACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBlF,EAAnB,CAAb;IAEA,IAAIwD,IAAJ,EAAU;MACR,MAAMgC,WAAW,GAAGnB,kBAAkB,CAACb,IAAD,CAAtC;MACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd,EAAsBC,KAAtB;QAC/B,OAAOF,WAAW,GAAG1C,eAAe,CAACuC,WAAW,CAACK,KAAD,CAAZ,EAAqBD,MAArB,CAApC;OADgB,EAEf,CAFe,CAAlB;MAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;MAEAzB,UAAU,CAACa,IAAX,CAAgB;QACdpF,EADc;QAEdiE,IAAI,EAAE;UAACgB,kBAAD;UAAqBpD,KAAK,EAAEiE;;OAFpC;;;EAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CA3BM;;ACJP;;;;AAGA,SAAgBiC,qBACdC,KAAA,EACAC,MAAA;EAEA,MAAMrC,GAAG,GAAGV,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACrC,GAAhB,EAAqBoC,KAAK,CAACpC,GAA3B,CAAZ;EACA,MAAMF,IAAI,GAAGR,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACvC,IAAhB,EAAsBsC,KAAK,CAACtC,IAA5B,CAAb;EACA,MAAMyC,KAAK,GAAGjD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACvC,IAAP,GAAcuC,MAAM,CAACtC,KAA9B,EAAqCqC,KAAK,CAACtC,IAAN,GAAasC,KAAK,CAACrC,KAAxD,CAAd;EACA,MAAM0C,MAAM,GAAGnD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACrC,GAAP,GAAaqC,MAAM,CAACpC,MAA7B,EAAqCmC,KAAK,CAACpC,GAAN,GAAYoC,KAAK,CAACnC,MAAvD,CAAf;EACA,MAAMF,KAAK,GAAGwC,KAAK,GAAGzC,IAAtB;EACA,MAAMG,MAAM,GAAGwC,MAAM,GAAGzC,GAAxB;EAEA,IAAIF,IAAI,GAAGyC,KAAP,IAAgBvC,GAAG,GAAGyC,MAA1B,EAAkC;IAChC,MAAMC,UAAU,GAAGL,MAAM,CAACtC,KAAP,GAAesC,MAAM,CAACpC,MAAzC;IACA,MAAM0C,SAAS,GAAGP,KAAK,CAACrC,KAAN,GAAcqC,KAAK,CAACnC,MAAtC;IACA,MAAM2C,gBAAgB,GAAG7C,KAAK,GAAGE,MAAjC;IACA,MAAM4C,iBAAiB,GACrBD,gBAAgB,IAAIF,UAAU,GAAGC,SAAb,GAAyBC,gBAA7B,CADlB;IAGA,OAAOX,MAAM,CAACY,iBAAiB,CAACX,OAAlB,CAA0B,CAA1B,CAAD,CAAb;;;EAIF,OAAO,CAAP;AACD;AAED;;;;;AAIA,MAAaY,gBAAgB,GAAuBvH,IAAA;MAAC;IACnDwF,aADmD;IAEnDC,cAFmD;IAGnDC;;EAEA,MAAMR,UAAU,GAA0B,EAA1C;EAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,EAAsD;IACpD,MAAM;MAAC/E;QAAMiF,kBAAb;IACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBlF,EAAnB,CAAb;IAEA,IAAIwD,IAAJ,EAAU;MACR,MAAMmD,iBAAiB,GAAGV,oBAAoB,CAACzC,IAAD,EAAOqB,aAAP,CAA9C;MAEA,IAAI8B,iBAAiB,GAAG,CAAxB,EAA2B;QACzBpC,UAAU,CAACa,IAAX,CAAgB;UACdpF,EADc;UAEdiE,IAAI,EAAE;YAACgB,kBAAD;YAAqBpD,KAAK,EAAE8E;;SAFpC;;;;EAQN,OAAOpC,UAAU,CAACc,IAAX,CAAgBjB,kBAAhB,CAAP;AACD,CAxBM;;AC/BP;;;;AAGA,SAASyC,iBAATA,CAA2BC,KAA3B,EAA+CtD,IAA/C;EACE,MAAM;IAACM,GAAD;IAAMF,IAAN;IAAY2C,MAAZ;IAAoBF;MAAS7C,IAAnC;EAEA,OACEM,GAAG,IAAIgD,KAAK,CAAC9D,CAAb,IAAkB8D,KAAK,CAAC9D,CAAN,IAAWuD,MAA7B,IAAuC3C,IAAI,IAAIkD,KAAK,CAAC/D,CAArD,IAA0D+D,KAAK,CAAC/D,CAAN,IAAWsD,KADvE;AAGD;AAED;;;;AAGA,MAAaU,aAAa,GAAuB1H,IAAA;MAAC;IAChD0F,mBADgD;IAEhDD,cAFgD;IAGhDkC;;EAEA,IAAI,CAACA,kBAAL,EAAyB;IACvB,OAAO,EAAP;;EAGF,MAAMzC,UAAU,GAA0B,EAA1C;EAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,EAAsD;IACpD,MAAM;MAAC/E;QAAMiF,kBAAb;IACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBlF,EAAnB,CAAb;IAEA,IAAIwD,IAAI,IAAIqD,iBAAiB,CAACG,kBAAD,EAAqBxD,IAArB,CAA7B,EAAyD;;;;;;MAMvD,MAAM+B,OAAO,GAAGlB,kBAAkB,CAACb,IAAD,CAAlC;MACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd;QAC/B,OAAOD,WAAW,GAAG1C,eAAe,CAAC+D,kBAAD,EAAqBpB,MAArB,CAApC;OADgB,EAEf,CAFe,CAAlB;MAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;MAEAzB,UAAU,CAACa,IAAX,CAAgB;QACdpF,EADc;QAEdiE,IAAI,EAAE;UAACgB,kBAAD;UAAqBpD,KAAK,EAAEiE;;OAFpC;;;EAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAnCM;SCjBSiD,YACdC,SAAA,EACAC,KAAA,EACAC,KAAA;EAEA,OAAO;IACL,GAAGF,SADE;IAELG,MAAM,EAAEF,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACtD,KAAN,GAAcuD,KAAK,CAACvD,KAArC,GAA6C,CAFhD;IAGLyD,MAAM,EAAEH,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACpD,MAAN,GAAeqD,KAAK,CAACrD,MAAtC,GAA+C;GAHzD;AAKD;SCVewD,aACdJ,KAAA,EACAC,KAAA;EAEA,OAAOD,KAAK,IAAIC,KAAT,GACH;IACErE,CAAC,EAAEoE,KAAK,CAACvD,IAAN,GAAawD,KAAK,CAACxD,IADxB;IAEEZ,CAAC,EAAEmE,KAAK,CAACrD,GAAN,GAAYsD,KAAK,CAACtD;GAHpB,GAKHlB,kBALJ;AAMD;SCXe4E,uBAAuBC,QAAA;EACrC,OAAO,SAASC,gBAATA,CACLlE,IADK;sCAEFmE,WAAA,OAAApF,KAAA,CAAAC,IAAA,OAAAA,IAAA,WAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAAkF,WAAA,CAAAlF,IAAA,QAAAC,SAAA,CAAAD,IAAA;;IAEH,OAAOkF,WAAW,CAACjC,MAAZ,CACL,CAACkC,GAAD,EAAMC,UAAN,MAAsB;MACpB,GAAGD,GADiB;MAEpB9D,GAAG,EAAE8D,GAAG,CAAC9D,GAAJ,GAAU2D,QAAQ,GAAGI,UAAU,CAAC7E,CAFjB;MAGpBuD,MAAM,EAAEqB,GAAG,CAACrB,MAAJ,GAAakB,QAAQ,GAAGI,UAAU,CAAC7E,CAHvB;MAIpBY,IAAI,EAAEgE,GAAG,CAAChE,IAAJ,GAAW6D,QAAQ,GAAGI,UAAU,CAAC9E,CAJnB;MAKpBsD,KAAK,EAAEuB,GAAG,CAACvB,KAAJ,GAAYoB,QAAQ,GAAGI,UAAU,CAAC9E;KAL3C,CADK,EAQL;MAAC,GAAGS;KARC,CAAP;GAJF;AAeD;AAED,MAAasE,eAAe,gBAAGN,sBAAsB,CAAC,CAAD,CAA9C;SClBSO,eAAeb,SAAA;EAC7B,IAAIA,SAAS,CAACc,UAAV,CAAqB,WAArB,CAAJ,EAAuC;IACrC,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;IAEA,OAAO;MACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,EAAD,CADb;MAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,EAAD,CAFb;MAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;MAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD;KAJzB;GAHF,MASO,IAAIf,SAAS,CAACc,UAAV,CAAqB,SAArB,CAAJ,EAAqC;IAC1C,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;IAEA,OAAO;MACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,CAAD,CADb;MAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,CAAD,CAFb;MAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;MAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD;KAJzB;;EAQF,OAAO,IAAP;AACD;SCpBeG,iBACd5E,IAAA,EACA0D,SAAA,EACAvD,eAAA;EAEA,MAAM0E,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;EAEA,IAAI,CAACmB,eAAL,EAAsB;IACpB,OAAO7E,IAAP;;EAGF,MAAM;IAAC6D,MAAD;IAASC,MAAT;IAAiBvE,CAAC,EAAEuF,UAApB;IAAgCtF,CAAC,EAAEuF;MAAcF,eAAvD;EAEA,MAAMtF,CAAC,GAAGS,IAAI,CAACI,IAAL,GAAY0E,UAAZ,GAAyB,CAAC,IAAIjB,MAAL,IAAemB,UAAU,CAAC7E,eAAD,CAA5D;EACA,MAAMX,CAAC,GACLQ,IAAI,CAACM,GAAL,GACAyE,UADA,GAEA,CAAC,IAAIjB,MAAL,IACEkB,UAAU,CAAC7E,eAAe,CAACuE,KAAhB,CAAsBvE,eAAe,CAAC8E,OAAhB,CAAwB,GAAxB,IAA+B,CAArD,CAAD,CAJd;EAKA,MAAMC,CAAC,GAAGrB,MAAM,GAAG7D,IAAI,CAACK,KAAL,GAAawD,MAAhB,GAAyB7D,IAAI,CAACK,KAA9C;EACA,MAAM8E,CAAC,GAAGrB,MAAM,GAAG9D,IAAI,CAACO,MAAL,GAAcuD,MAAjB,GAA0B9D,IAAI,CAACO,MAA/C;EAEA,OAAO;IACLF,KAAK,EAAE6E,CADF;IAEL3E,MAAM,EAAE4E,CAFH;IAGL7E,GAAG,EAAEd,CAHA;IAILqD,KAAK,EAAEtD,CAAC,GAAG2F,CAJN;IAKLnC,MAAM,EAAEvD,CAAC,GAAG2F,CALP;IAML/E,IAAI,EAAEb;GANR;AAQD;ACzBD,MAAM6F,cAAc,GAAY;EAACC,eAAe,EAAE;AAAlB,CAAhC;AAEA;;;;AAGA,SAAgBC,cACdC,OAAA,EACA3G,OAAA;MAAAA,OAAA;IAAAA,OAAA,GAAmBwG,cAAA;;EAEnB,IAAIpF,IAAI,GAAeuF,OAAO,CAACC,qBAAR,EAAvB;EAEA,IAAI5G,OAAO,CAACyG,eAAZ,EAA6B;IAC3B,MAAM;MAAC3B,SAAD;MAAYvD;QAChBsF,SAAS,CAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCH,OAApC,CADF;IAGA,IAAI7B,SAAJ,EAAe;MACb1D,IAAI,GAAG4E,gBAAgB,CAAC5E,IAAD,EAAO0D,SAAP,EAAkBvD,eAAlB,CAAvB;;;EAIJ,MAAM;IAACG,GAAD;IAAMF,IAAN;IAAYC,KAAZ;IAAmBE,MAAnB;IAA2BwC,MAA3B;IAAmCF;MAAS7C,IAAlD;EAEA,OAAO;IACLM,GADK;IAELF,IAFK;IAGLC,KAHK;IAILE,MAJK;IAKLwC,MALK;IAMLF;GANF;AAQD;AAED;;;;;;;;;AAQA,SAAgB8C,+BAA+BJ,OAAA;EAC7C,OAAOD,aAAa,CAACC,OAAD,EAAU;IAACF,eAAe,EAAE;GAA5B,CAApB;AACD;SCjDeO,oBAAoBL,OAAA;EAClC,MAAMlF,KAAK,GAAGkF,OAAO,CAACM,UAAtB;EACA,MAAMtF,MAAM,GAAGgF,OAAO,CAACO,WAAvB;EAEA,OAAO;IACLxF,GAAG,EAAE,CADA;IAELF,IAAI,EAAE,CAFD;IAGLyC,KAAK,EAAExC,KAHF;IAIL0C,MAAM,EAAExC,MAJH;IAKLF,KALK;IAMLE;GANF;AAQD;SCZewF,QACdC,IAAA,EACAC,aAAA;MAAAA,aAAA;IAAAA,aAAA,GAAqCR,SAAS,CAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC;;EAErC,OAAOC,aAAa,CAACC,QAAd,KAA2B,OAAlC;AACD;SCLeC,aACdZ,OAAA,EACAU,aAAA;MAAAA,aAAA;IAAAA,aAAA,GAAqCR,SAAS,CAACF,OAAD,CAAT,CAAmBG,gBAAnB,CACnCH,OADmC;;EAIrC,MAAMa,aAAa,GAAG,uBAAtB;EACA,MAAMC,UAAU,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,WAA1B,CAAnB;EAEA,OAAOA,UAAU,CAACC,IAAX,CAAiBtF,QAAD;IACrB,MAAM3C,KAAK,GAAG4H,aAAa,CAACjF,QAAD,CAA3B;IAEA,OAAO,OAAO3C,KAAP,KAAiB,QAAjB,GAA4B+H,aAAa,CAACG,IAAd,CAAmBlI,KAAnB,CAA5B,GAAwD,KAA/D;GAHK,CAAP;AAKD;SCNemI,uBACdjB,OAAA,EACAkB,KAAA;EAEA,MAAMC,aAAa,GAAc,EAAjC;EAEA,SAASC,uBAATA,CAAiCX,IAAjC;IACE,IAAIS,KAAK,IAAI,IAAT,IAAiBC,aAAa,CAACzF,MAAd,IAAwBwF,KAA7C,EAAoD;MAClD,OAAOC,aAAP;;IAGF,IAAI,CAACV,IAAL,EAAW;MACT,OAAOU,aAAP;;IAGF,IACEE,UAAU,CAACZ,IAAD,CAAV,IACAA,IAAI,CAACa,gBAAL,IAAyB,IADzB,IAEA,CAACH,aAAa,CAACI,QAAd,CAAuBd,IAAI,CAACa,gBAA5B,CAHH,EAIE;MACAH,aAAa,CAAC9E,IAAd,CAAmBoE,IAAI,CAACa,gBAAxB;MAEA,OAAOH,aAAP;;IAGF,IAAI,CAACK,aAAa,CAACf,IAAD,CAAd,IAAwBgB,YAAY,CAAChB,IAAD,CAAxC,EAAgD;MAC9C,OAAOU,aAAP;;IAGF,IAAIA,aAAa,CAACI,QAAd,CAAuBd,IAAvB,CAAJ,EAAkC;MAChC,OAAOU,aAAP;;IAGF,MAAMT,aAAa,GAAGR,SAAS,CAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCM,IAApC,CAAtB;IAEA,IAAIA,IAAI,KAAKT,OAAb,EAAsB;MACpB,IAAIY,YAAY,CAACH,IAAD,EAAOC,aAAP,CAAhB,EAAuC;QACrCS,aAAa,CAAC9E,IAAd,CAAmBoE,IAAnB;;;IAIJ,IAAID,OAAO,CAACC,IAAD,EAAOC,aAAP,CAAX,EAAkC;MAChC,OAAOS,aAAP;;IAGF,OAAOC,uBAAuB,CAACX,IAAI,CAACiB,UAAN,CAA9B;;EAGF,IAAI,CAAC1B,OAAL,EAAc;IACZ,OAAOmB,aAAP;;EAGF,OAAOC,uBAAuB,CAACpB,OAAD,CAA9B;AACD;AAED,SAAgB2B,2BAA2BlB,IAAA;EACzC,MAAM,CAACmB,uBAAD,IAA4BX,sBAAsB,CAACR,IAAD,EAAO,CAAP,CAAxD;EAEA,OAAOmB,uBAAP,WAAOA,uBAAP,GAAkC,IAAlC;AACD;SC5DeC,qBAAqB7B,OAAA;EACnC,IAAI,CAAC8B,SAAD,IAAc,CAAC9B,OAAnB,EAA4B;IAC1B,OAAO,IAAP;;EAGF,IAAI+B,QAAQ,CAAC/B,OAAD,CAAZ,EAAuB;IACrB,OAAOA,OAAP;;EAGF,IAAI,CAACgC,MAAM,CAAChC,OAAD,CAAX,EAAsB;IACpB,OAAO,IAAP;;EAGF,IACEqB,UAAU,CAACrB,OAAD,CAAV,IACAA,OAAO,KAAKiC,gBAAgB,CAACjC,OAAD,CAAhB,CAA0BsB,gBAFxC,EAGE;IACA,OAAOY,MAAP;;EAGF,IAAIV,aAAa,CAACxB,OAAD,CAAjB,EAA4B;IAC1B,OAAOA,OAAP;;EAGF,OAAO,IAAP;AACD;SC9BemC,qBAAqBnC,OAAA;EACnC,IAAI+B,QAAQ,CAAC/B,OAAD,CAAZ,EAAuB;IACrB,OAAOA,OAAO,CAACoC,OAAf;;EAGF,OAAOpC,OAAO,CAACqC,UAAf;AACD;AAED,SAAgBC,qBAAqBtC,OAAA;EACnC,IAAI+B,QAAQ,CAAC/B,OAAD,CAAZ,EAAuB;IACrB,OAAOA,OAAO,CAACuC,OAAf;;EAGF,OAAOvC,OAAO,CAACwC,SAAf;AACD;AAED,SAAgBC,qBACdzC,OAAA;EAEA,OAAO;IACLhG,CAAC,EAAEmI,oBAAoB,CAACnC,OAAD,CADlB;IAEL/F,CAAC,EAAEqI,oBAAoB,CAACtC,OAAD;GAFzB;AAID;AC3BD,IAAY0C,SAAZ;AAAA,WAAYA,SAAA;EACVA,SAAA,CAAAA,SAAA;EACAA,SAAA,CAAAA,SAAA;AACD,CAHD,EAAYA,SAAS,KAATA,SAAS,MAArB;SCEgBC,2BAA2B3C,OAAA;EACzC,IAAI,CAAC8B,SAAD,IAAc,CAAC9B,OAAnB,EAA4B;IAC1B,OAAO,KAAP;;EAGF,OAAOA,OAAO,KAAK4C,QAAQ,CAACtB,gBAA5B;AACD;SCNeuB,kBAAkBC,kBAAA;EAChC,MAAMC,SAAS,GAAG;IAChB/I,CAAC,EAAE,CADa;IAEhBC,CAAC,EAAE;GAFL;EAIA,MAAM+I,UAAU,GAAGL,0BAA0B,CAACG,kBAAD,CAA1B,GACf;IACE9H,MAAM,EAAEkH,MAAM,CAAC3B,WADjB;IAEEzF,KAAK,EAAEoH,MAAM,CAAC5B;GAHD,GAKf;IACEtF,MAAM,EAAE8H,kBAAkB,CAACG,YAD7B;IAEEnI,KAAK,EAAEgI,kBAAkB,CAACI;GAPhC;EASA,MAAMC,SAAS,GAAG;IAChBnJ,CAAC,EAAE8I,kBAAkB,CAACM,WAAnB,GAAiCJ,UAAU,CAAClI,KAD/B;IAEhBb,CAAC,EAAE6I,kBAAkB,CAACO,YAAnB,GAAkCL,UAAU,CAAChI;GAFlD;EAKA,MAAMsI,KAAK,GAAGR,kBAAkB,CAACN,SAAnB,IAAgCO,SAAS,CAAC9I,CAAxD;EACA,MAAMsJ,MAAM,GAAGT,kBAAkB,CAACT,UAAnB,IAAiCU,SAAS,CAAC/I,CAA1D;EACA,MAAMwJ,QAAQ,GAAGV,kBAAkB,CAACN,SAAnB,IAAgCW,SAAS,CAAClJ,CAA3D;EACA,MAAMwJ,OAAO,GAAGX,kBAAkB,CAACT,UAAnB,IAAiCc,SAAS,CAACnJ,CAA3D;EAEA,OAAO;IACLsJ,KADK;IAELC,MAFK;IAGLC,QAHK;IAILC,OAJK;IAKLN,SALK;IAMLJ;GANF;AAQD;AC5BD,MAAMW,gBAAgB,GAAG;EACvB1J,CAAC,EAAE,GADoB;EAEvBC,CAAC,EAAE;AAFoB,CAAzB;AAKA,SAAgB0J,2BACdC,eAAA,EACAC,mBAAA,EAAAvN,IAAA,EAEAwN,YAAA,EACAC,mBAAA;MAFA;IAAChJ,GAAD;IAAMF,IAAN;IAAYyC,KAAZ;IAAmBE;;MACnBsG,YAAA;IAAAA,YAAA,GAAe;;MACfC,mBAAA;IAAAA,mBAAA,GAAsBL,gBAAA;;EAEtB,MAAM;IAACJ,KAAD;IAAQE,QAAR;IAAkBD,MAAlB;IAA0BE;MAAWZ,iBAAiB,CAACe,eAAD,CAA5D;EAEA,MAAMI,SAAS,GAAG;IAChBhK,CAAC,EAAE,CADa;IAEhBC,CAAC,EAAE;GAFL;EAIA,MAAMgK,KAAK,GAAG;IACZjK,CAAC,EAAE,CADS;IAEZC,CAAC,EAAE;GAFL;EAIA,MAAMiK,SAAS,GAAG;IAChBlJ,MAAM,EAAE6I,mBAAmB,CAAC7I,MAApB,GAA6B+I,mBAAmB,CAAC9J,CADzC;IAEhBa,KAAK,EAAE+I,mBAAmB,CAAC/I,KAApB,GAA4BiJ,mBAAmB,CAAC/J;GAFzD;EAKA,IAAI,CAACsJ,KAAD,IAAUvI,GAAG,IAAI8I,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAAzD,EAAiE;;IAE/DgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAACyB,QAAxB;IACAF,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAApC,GAA6CD,GAA9C,IAAqDmJ,SAAS,CAAClJ,MADjE,CAFF;GAHF,MAQO,IACL,CAACwI,QAAD,IACAhG,MAAM,IAAIqG,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAF5C,EAGL;;IAEAgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAAC2B,OAAxB;IACAJ,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAAvC,GAAgDwC,MAAjD,IACE0G,SAAS,CAAClJ,MAFd,CAFF;;EAQF,IAAI,CAACyI,OAAD,IAAYnG,KAAK,IAAIuG,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAA/D,EAAsE;;IAEpEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAAC2B,OAAxB;IACAJ,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAAtC,GAA8CwC,KAA/C,IAAwD4G,SAAS,CAACpJ,KADpE,CAFF;GAHF,MAQO,IAAI,CAACyI,MAAD,IAAW1I,IAAI,IAAIgJ,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAA5D,EAAmE;;IAExEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAACyB,QAAxB;IACAF,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAArC,GAA6CD,IAA9C,IAAsDqJ,SAAS,CAACpJ,KADlE,CAFF;;EAOF,OAAO;IACLkJ,SADK;IAELC;GAFF;AAID;SC7EeK,qBAAqBtE,OAAA;EACnC,IAAIA,OAAO,KAAK4C,QAAQ,CAACtB,gBAAzB,EAA2C;IACzC,MAAM;MAAChB,UAAD;MAAaC;QAAe2B,MAAlC;IAEA,OAAO;MACLnH,GAAG,EAAE,CADA;MAELF,IAAI,EAAE,CAFD;MAGLyC,KAAK,EAAEgD,UAHF;MAIL9C,MAAM,EAAE+C,WAJH;MAKLzF,KAAK,EAAEwF,UALF;MAMLtF,MAAM,EAAEuF;KANV;;EAUF,MAAM;IAACxF,GAAD;IAAMF,IAAN;IAAYyC,KAAZ;IAAmBE;MAAUwC,OAAO,CAACC,qBAAR,EAAnC;EAEA,OAAO;IACLlF,GADK;IAELF,IAFK;IAGLyC,KAHK;IAILE,MAJK;IAKL1C,KAAK,EAAEkF,OAAO,CAACkD,WALV;IAMLlI,MAAM,EAAEgF,OAAO,CAACiD;GANlB;AAQD;SCdesB,iBAAiBC,mBAAA;EAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAwC,CAACkC,GAAD,EAAM4B,IAAN;IAC7C,OAAOtK,GAAG,CAAC0I,GAAD,EAAM4D,oBAAoB,CAAChC,IAAD,CAA1B,CAAV;GADK,EAEJ5G,kBAFI,CAAP;AAGD;AAED,SAAgB4K,iBAAiBD,mBAAA;EAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;IACxC,OAAO5B,GAAG,GAAGsD,oBAAoB,CAAC1B,IAAD,CAAjC;GADK,EAEJ,CAFI,CAAP;AAGD;AAED,SAAgBiE,iBAAiBF,mBAAA;EAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;IACxC,OAAO5B,GAAG,GAAGyD,oBAAoB,CAAC7B,IAAD,CAAjC;GADK,EAEJ,CAFI,CAAP;AAGD;SCtBekE,uBACd3E,OAAA,EACA4E,OAAA;MAAAA,OAAA;IAAAA,OAAA,GAA6C7E,aAAA;;EAE7C,IAAI,CAACC,OAAL,EAAc;IACZ;;EAGF,MAAM;IAACjF,GAAD;IAAMF,IAAN;IAAY2C,MAAZ;IAAoBF;MAASsH,OAAO,CAAC5E,OAAD,CAA1C;EACA,MAAM4B,uBAAuB,GAAGD,0BAA0B,CAAC3B,OAAD,CAA1D;EAEA,IAAI,CAAC4B,uBAAL,EAA8B;IAC5B;;EAGF,IACEpE,MAAM,IAAI,CAAV,IACAF,KAAK,IAAI,CADT,IAEAvC,GAAG,IAAImH,MAAM,CAAC3B,WAFd,IAGA1F,IAAI,IAAIqH,MAAM,CAAC5B,UAJjB,EAKE;IACAN,OAAO,CAAC6E,cAAR,CAAuB;MACrBC,KAAK,EAAE,QADc;MAErBC,MAAM,EAAE;KAFV;;AAKH;ACtBD,MAAMjE,UAAU,GAAG,CACjB,CAAC,GAAD,EAAM,CAAC,MAAD,EAAS,OAAT,CAAN,EAAyB2D,gBAAzB,CADiB,EAEjB,CAAC,GAAD,EAAM,CAAC,KAAD,EAAQ,QAAR,CAAN,EAAyBC,gBAAzB,CAFiB,CAAnB;AAKA,MAAaM,IAAA;EACXC,YAAYxK,IAAA,EAAkBuF,OAAA;SAyBtBvF,IAAA;SAEDK,KAAA;SAEAE,MAAA;SAIAD,GAAA;SAEAyC,MAAA;SAEAF,KAAA;SAEAzC,IAAA;IAtCL,MAAM2J,mBAAmB,GAAGvD,sBAAsB,CAACjB,OAAD,CAAlD;IACA,MAAMkF,aAAa,GAAGX,gBAAgB,CAACC,mBAAD,CAAtC;IAEA,KAAK/J,IAAL,GAAY;MAAC,GAAGA;KAAhB;IACA,KAAKK,KAAL,GAAaL,IAAI,CAACK,KAAlB;IACA,KAAKE,MAAL,GAAcP,IAAI,CAACO,MAAnB;IAEA,KAAK,MAAM,CAACmK,IAAD,EAAOC,IAAP,EAAaC,eAAb,CAAX,IAA4CvE,UAA5C,EAAwD;MACtD,KAAK,MAAMwE,GAAX,IAAkBF,IAAlB,EAAwB;QACtBtL,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4BD,GAA5B,EAAiC;UAC/BnJ,GAAG,EAAEA,CAAA;YACH,MAAMqJ,cAAc,GAAGH,eAAe,CAACb,mBAAD,CAAtC;YACA,MAAMiB,mBAAmB,GAAGP,aAAa,CAACC,IAAD,CAAb,GAAsBK,cAAlD;YAEA,OAAO,KAAK/K,IAAL,CAAU6K,GAAV,IAAiBG,mBAAxB;WAL6B;UAO/BC,UAAU,EAAE;SAPd;;;IAYJ5L,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4B,MAA5B,EAAoC;MAACG,UAAU,EAAE;KAAjD;;;MCpCSC,SAAA;EAOXV,YAAoB7H,MAAA;SAAAA,MAAA;SANZrH,SAAA,GAIF;SAaC6P,SAAA,GAAY;MACjB,KAAK7P,SAAL,CAAeU,OAAf,CAAwBjB,QAAD;QAAA,IAAAqQ,YAAA;QAAA,QAAAA,YAAA,GACrB,KAAKzI,MADgB,qBACrByI,YAAA,CAAaC,mBAAb,CAAiC,GAAGtQ,QAApC,CADqB;OAAvB;;IAZkB,KAAA4H,MAAA,GAAAA,MAAA;;EAEbjH,GAAGA,CACR4P,SADQ,EAERC,OAFQ,EAGR3M,OAHQ;;IAKR,CAAA4M,aAAA,QAAK7I,MAAL,qBAAA6I,aAAA,CAAaC,gBAAb,CAA8BH,SAA9B,EAAyCC,OAAzC,EAAmE3M,OAAnE;IACA,KAAKtD,SAAL,CAAesG,IAAf,CAAoB,CAAC0J,SAAD,EAAYC,OAAZ,EAAsC3M,OAAtC,CAApB;;;SCbY8M,uBACd/I,MAAA;;;;;;EAQA,MAAM;IAACgJ;MAAelG,SAAS,CAAC9C,MAAD,CAA/B;EAEA,OAAOA,MAAM,YAAYgJ,WAAlB,GAAgChJ,MAAhC,GAAyC6E,gBAAgB,CAAC7E,MAAD,CAAhE;AACD;SCZeiJ,oBACdC,KAAA,EACAC,WAAA;EAEA,MAAMC,EAAE,GAAGnM,IAAI,CAAC+J,GAAL,CAASkC,KAAK,CAACtM,CAAf,CAAX;EACA,MAAMyM,EAAE,GAAGpM,IAAI,CAAC+J,GAAL,CAASkC,KAAK,CAACrM,CAAf,CAAX;EAEA,IAAI,OAAOsM,WAAP,KAAuB,QAA3B,EAAqC;IACnC,OAAOlM,IAAI,CAACC,IAAL,CAAUkM,EAAE,IAAI,CAAN,GAAUC,EAAE,IAAI,CAA1B,IAA+BF,WAAtC;;EAGF,IAAI,OAAOA,WAAP,IAAsB,OAAOA,WAAjC,EAA8C;IAC5C,OAAOC,EAAE,GAAGD,WAAW,CAACvM,CAAjB,IAAsByM,EAAE,GAAGF,WAAW,CAACtM,CAA9C;;EAGF,IAAI,OAAOsM,WAAX,EAAwB;IACtB,OAAOC,EAAE,GAAGD,WAAW,CAACvM,CAAxB;;EAGF,IAAI,OAAOuM,WAAX,EAAwB;IACtB,OAAOE,EAAE,GAAGF,WAAW,CAACtM,CAAxB;;EAGF,OAAO,KAAP;AACD;AC1BD,IAAYyM,SAAZ;AAAA,WAAYA,SAAA;EACVA,SAAA;EACAA,SAAA;EACAA,SAAA;EACAA,SAAA;EACAA,SAAA;EACAA,SAAA;EACAA,SAAA;AACD,CARD,EAAYA,SAAS,KAATA,SAAS,MAArB;AAUA,SAAgBC,eAAenQ,KAAA;EAC7BA,KAAK,CAACmQ,cAAN;AACD;AAED,SAAgBC,gBAAgBpQ,KAAA;EAC9BA,KAAK,CAACoQ,eAAN;AACD;ICbWC,YAAZ;AAAA,WAAYA,YAAA;EACVA,YAAA;EACAA,YAAA;EACAA,YAAA;EACAA,YAAA;EACAA,YAAA;EACAA,YAAA;EACAA,YAAA;EACAA,YAAA;AACD,CATD,EAAYA,YAAY,KAAZA,YAAY,MAAxB;ACDO,MAAMC,oBAAoB,GAAkB;EACjDC,KAAK,EAAE,CAACF,YAAY,CAACG,KAAd,EAAqBH,YAAY,CAACI,KAAlC,CAD0C;EAEjDC,MAAM,EAAE,CAACL,YAAY,CAACM,GAAd,CAFyC;EAGjDC,GAAG,EAAE,CAACP,YAAY,CAACG,KAAd,EAAqBH,YAAY,CAACI,KAAlC,EAAyCJ,YAAY,CAACQ,GAAtD;AAH4C,CAA5C;AAMP,MAAaC,+BAA+B,GAA6BA,CACvE9Q,KADuE,EAAAF,IAAA;MAEvE;IAACiR;;EAED,QAAQ/Q,KAAK,CAACgR,IAAd;IACE,KAAKX,YAAY,CAACY,KAAlB;MACE,OAAO;QACL,GAAGF,kBADE;QAELvN,CAAC,EAAEuN,kBAAkB,CAACvN,CAAnB,GAAuB;OAF5B;IAIF,KAAK6M,YAAY,CAACa,IAAlB;MACE,OAAO;QACL,GAAGH,kBADE;QAELvN,CAAC,EAAEuN,kBAAkB,CAACvN,CAAnB,GAAuB;OAF5B;IAIF,KAAK6M,YAAY,CAACc,IAAlB;MACE,OAAO;QACL,GAAGJ,kBADE;QAELtN,CAAC,EAAEsN,kBAAkB,CAACtN,CAAnB,GAAuB;OAF5B;IAIF,KAAK4M,YAAY,CAACe,EAAlB;MACE,OAAO;QACL,GAAGL,kBADE;QAELtN,CAAC,EAAEsN,kBAAkB,CAACtN,CAAnB,GAAuB;OAF5B;;EAMJ,OAAO4N,SAAP;AACD,CA5BM;MC+BMC,cAAA;EAMX7C,YAAoB8C,KAAA;SAAAA,KAAA;SALbC,iBAAA,GAAoB;SACnBC,oBAAA;SACAlS,SAAA;SACAmS,eAAA;IAEY,KAAAH,KAAA,GAAAA,KAAA;IAClB,MAAM;MACJvR,KAAK,EAAE;QAAC4G;;QACN2K,KAFJ;IAIA,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKhS,SAAL,GAAiB,IAAI4P,SAAJ,CAAc1D,gBAAgB,CAAC7E,MAAD,CAA9B,CAAjB;IACA,KAAK8K,eAAL,GAAuB,IAAIvC,SAAJ,CAAczF,SAAS,CAAC9C,MAAD,CAAvB,CAAvB;IACA,KAAK+K,aAAL,GAAqB,KAAKA,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArB;IACA,KAAKC,YAAL,GAAoB,KAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;IAEA,KAAKE,MAAL;;EAGMA,MAAMA,CAAA;IACZ,KAAKC,WAAL;IAEA,KAAKL,eAAL,CAAqB/R,GAArB,CAAyBuQ,SAAS,CAAC8B,MAAnC,EAA2C,KAAKH,YAAhD;IACA,KAAKH,eAAL,CAAqB/R,GAArB,CAAyBuQ,SAAS,CAAC+B,gBAAnC,EAAqD,KAAKJ,YAA1D;IAEAK,UAAU,CAAC,MAAM,KAAK3S,SAAL,CAAeI,GAAf,CAAmBuQ,SAAS,CAACiC,OAA7B,EAAsC,KAAKR,aAA3C,CAAP,CAAV;;EAGMI,WAAWA,CAAA;IACjB,MAAM;MAACK,UAAD;MAAaC;QAAW,KAAKd,KAAnC;IACA,MAAMtH,IAAI,GAAGmI,UAAU,CAACnI,IAAX,CAAgBqI,OAA7B;IAEA,IAAIrI,IAAJ,EAAU;MACRkE,sBAAsB,CAAClE,IAAD,CAAtB;;IAGFoI,OAAO,CAAChP,kBAAD,CAAP;;EAGMsO,aAAaA,CAAC3R,KAAD;IACnB,IAAIuS,eAAe,CAACvS,KAAD,CAAnB,EAA4B;MAC1B,MAAM;QAACQ,MAAD;QAASgS,OAAT;QAAkB3P;UAAW,KAAK0O,KAAxC;MACA,MAAM;QACJkB,aAAa,GAAGnC,oBADZ;QAEJoC,gBAAgB,GAAG5B,+BAFf;QAGJ6B,cAAc,GAAG;UACf9P,OAJJ;MAKA,MAAM;QAACmO;UAAQhR,KAAf;MAEA,IAAIyS,aAAa,CAAC7B,GAAd,CAAkB7F,QAAlB,CAA2BiG,IAA3B,CAAJ,EAAsC;QACpC,KAAK4B,SAAL,CAAe5S,KAAf;QACA;;MAGF,IAAIyS,aAAa,CAAC/B,MAAd,CAAqB3F,QAArB,CAA8BiG,IAA9B,CAAJ,EAAyC;QACvC,KAAKa,YAAL,CAAkB7R,KAAlB;QACA;;MAGF,MAAM;QAACsF;UAAiBkN,OAAO,CAACF,OAAhC;MACA,MAAMvB,kBAAkB,GAAGzL,aAAa,GACpC;QAAC9B,CAAC,EAAE8B,aAAa,CAACjB,IAAlB;QAAwBZ,CAAC,EAAE6B,aAAa,CAACf;OADL,GAEpClB,kBAFJ;MAIA,IAAI,CAAC,KAAKoO,oBAAV,EAAgC;QAC9B,KAAKA,oBAAL,GAA4BV,kBAA5B;;MAGF,MAAM8B,cAAc,GAAGH,gBAAgB,CAAC1S,KAAD,EAAQ;QAC7CQ,MAD6C;QAE7CgS,OAAO,EAAEA,OAAO,CAACF,OAF4B;QAG7CvB;OAHqC,CAAvC;MAMA,IAAI8B,cAAJ,EAAoB;QAClB,MAAMC,gBAAgB,GAAGC,QAAmB,CAC1CF,cAD0C,EAE1C9B,kBAF0C,CAA5C;QAIA,MAAMiC,WAAW,GAAG;UAClBxP,CAAC,EAAE,CADe;UAElBC,CAAC,EAAE;SAFL;QAIA,MAAM;UAACuK;YAAuBwE,OAAO,CAACF,OAAtC;QAEA,KAAK,MAAMlF,eAAX,IAA8BY,mBAA9B,EAAmD;UACjD,MAAMR,SAAS,GAAGxN,KAAK,CAACgR,IAAxB;UACA,MAAM;YAAClE,KAAD;YAAQG,OAAR;YAAiBF,MAAjB;YAAyBC,QAAzB;YAAmCL,SAAnC;YAA8CJ;cAClDF,iBAAiB,CAACe,eAAD,CADnB;UAEA,MAAM6F,iBAAiB,GAAGnF,oBAAoB,CAACV,eAAD,CAA9C;UAEA,MAAM8F,kBAAkB,GAAG;YACzB1P,CAAC,EAAEK,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK6C,YAAY,CAACY,KAA3B,GACIgC,iBAAiB,CAACnM,KAAlB,GAA0BmM,iBAAiB,CAAC3O,KAAlB,GAA0B,CADxD,GAEI2O,iBAAiB,CAACnM,KAHrB,EAIDjD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK6C,YAAY,CAACY,KAA3B,GACIgC,iBAAiB,CAAC5O,IADtB,GAEI4O,iBAAiB,CAAC5O,IAAlB,GAAyB4O,iBAAiB,CAAC3O,KAAlB,GAA0B,CAHzD,EAIEuO,cAAc,CAACrP,CAJjB,CAJC,CADsB;YAYzBC,CAAC,EAAEI,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK6C,YAAY,CAACc,IAA3B,GACI8B,iBAAiB,CAACjM,MAAlB,GAA2BiM,iBAAiB,CAACzO,MAAlB,GAA2B,CAD1D,GAEIyO,iBAAiB,CAACjM,MAHrB,EAIDnD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK6C,YAAY,CAACc,IAA3B,GACI8B,iBAAiB,CAAC1O,GADtB,GAEI0O,iBAAiB,CAAC1O,GAAlB,GAAwB0O,iBAAiB,CAACzO,MAAlB,GAA2B,CAHzD,EAIEqO,cAAc,CAACpP,CAJjB,CAJC;WAZL;UAyBA,MAAM0P,UAAU,GACb3F,SAAS,KAAK6C,YAAY,CAACY,KAA3B,IAAoC,CAAChE,OAAtC,IACCO,SAAS,KAAK6C,YAAY,CAACa,IAA3B,IAAmC,CAACnE,MAFvC;UAGA,MAAMqG,UAAU,GACb5F,SAAS,KAAK6C,YAAY,CAACc,IAA3B,IAAmC,CAACnE,QAArC,IACCQ,SAAS,KAAK6C,YAAY,CAACe,EAA3B,IAAiC,CAACtE,KAFrC;UAIA,IAAIqG,UAAU,IAAID,kBAAkB,CAAC1P,CAAnB,KAAyBqP,cAAc,CAACrP,CAA1D,EAA6D;YAC3D,MAAM6P,oBAAoB,GACxBjG,eAAe,CAACvB,UAAhB,GAA6BiH,gBAAgB,CAACtP,CADhD;YAEA,MAAM8P,yBAAyB,GAC5B9F,SAAS,KAAK6C,YAAY,CAACY,KAA3B,IACCoC,oBAAoB,IAAI1G,SAAS,CAACnJ,CADpC,IAECgK,SAAS,KAAK6C,YAAY,CAACa,IAA3B,IACCmC,oBAAoB,IAAI9G,SAAS,CAAC/I,CAJtC;YAMA,IAAI8P,yBAAyB,IAAI,CAACR,gBAAgB,CAACrP,CAAnD,EAAsD;;;cAGpD2J,eAAe,CAACmG,QAAhB,CAAyB;gBACvBlP,IAAI,EAAEgP,oBADiB;gBAEvBG,QAAQ,EAAEb;eAFZ;cAIA;;YAGF,IAAIW,yBAAJ,EAA+B;cAC7BN,WAAW,CAACxP,CAAZ,GAAgB4J,eAAe,CAACvB,UAAhB,GAA6BwH,oBAA7C;aADF,MAEO;cACLL,WAAW,CAACxP,CAAZ,GACEgK,SAAS,KAAK6C,YAAY,CAACY,KAA3B,GACI7D,eAAe,CAACvB,UAAhB,GAA6Bc,SAAS,CAACnJ,CAD3C,GAEI4J,eAAe,CAACvB,UAAhB,GAA6BU,SAAS,CAAC/I,CAH7C;;YAMF,IAAIwP,WAAW,CAACxP,CAAhB,EAAmB;cACjB4J,eAAe,CAACqG,QAAhB,CAAyB;gBACvBpP,IAAI,EAAE,CAAC2O,WAAW,CAACxP,CADI;gBAEvBgQ,QAAQ,EAAEb;eAFZ;;YAKF;WAlCF,MAmCO,IAAIS,UAAU,IAAIF,kBAAkB,CAACzP,CAAnB,KAAyBoP,cAAc,CAACpP,CAA1D,EAA6D;YAClE,MAAM4P,oBAAoB,GACxBjG,eAAe,CAACpB,SAAhB,GAA4B8G,gBAAgB,CAACrP,CAD/C;YAEA,MAAM6P,yBAAyB,GAC5B9F,SAAS,KAAK6C,YAAY,CAACc,IAA3B,IACCkC,oBAAoB,IAAI1G,SAAS,CAAClJ,CADpC,IAEC+J,SAAS,KAAK6C,YAAY,CAACe,EAA3B,IACCiC,oBAAoB,IAAI9G,SAAS,CAAC9I,CAJtC;YAMA,IAAI6P,yBAAyB,IAAI,CAACR,gBAAgB,CAACtP,CAAnD,EAAsD;;;cAGpD4J,eAAe,CAACmG,QAAhB,CAAyB;gBACvBhP,GAAG,EAAE8O,oBADkB;gBAEvBG,QAAQ,EAAEb;eAFZ;cAIA;;YAGF,IAAIW,yBAAJ,EAA+B;cAC7BN,WAAW,CAACvP,CAAZ,GAAgB2J,eAAe,CAACpB,SAAhB,GAA4BqH,oBAA5C;aADF,MAEO;cACLL,WAAW,CAACvP,CAAZ,GACE+J,SAAS,KAAK6C,YAAY,CAACc,IAA3B,GACI/D,eAAe,CAACpB,SAAhB,GAA4BW,SAAS,CAAClJ,CAD1C,GAEI2J,eAAe,CAACpB,SAAhB,GAA4BO,SAAS,CAAC9I,CAH5C;;YAMF,IAAIuP,WAAW,CAACvP,CAAhB,EAAmB;cACjB2J,eAAe,CAACqG,QAAhB,CAAyB;gBACvBlP,GAAG,EAAE,CAACyO,WAAW,CAACvP,CADK;gBAEvB+P,QAAQ,EAAEb;eAFZ;;YAMF;;;QAIJ,KAAKe,UAAL,CACE1T,KADF,EAEEL,GAAsB,CACpBoT,QAAmB,CAACF,cAAD,EAAiB,KAAKpB,oBAAtB,CADC,EAEpBuB,WAFoB,CAFxB;;;;EAWEU,UAAUA,CAAC1T,KAAD,EAAe2T,WAAf;IAChB,MAAM;MAACC;QAAU,KAAKrC,KAAtB;IAEAvR,KAAK,CAACmQ,cAAN;IACAyD,MAAM,CAACD,WAAD,CAAN;;EAGMf,SAASA,CAAC5S,KAAD;IACf,MAAM;MAAC6T;QAAS,KAAKtC,KAArB;IAEAvR,KAAK,CAACmQ,cAAN;IACA,KAAK2D,MAAL;IACAD,KAAK;;EAGChC,YAAYA,CAAC7R,KAAD;IAClB,MAAM;MAAC+T;QAAY,KAAKxC,KAAxB;IAEAvR,KAAK,CAACmQ,cAAN;IACA,KAAK2D,MAAL;IACAC,QAAQ;;EAGFD,MAAMA,CAAA;IACZ,KAAKvU,SAAL,CAAe6P,SAAf;IACA,KAAKsC,eAAL,CAAqBtC,SAArB;;;AA1OSkC,cAAA,CA6OJ0C,UAAA,GAAgD,CACrD;EACEzE,SAAS,EAAE,WADb;EAEEC,OAAO,EAAEA,CACPxP,KADO,EAAAF,IAAA,EAAAa,KAAA;QAEP;MAAC8R,aAAa,GAAGnC,oBAAjB;MAAuC2D;;QACvC;MAACzT;;IAED,MAAM;MAACwQ;QAAQhR,KAAK,CAACkU,WAArB;IAEA,IAAIzB,aAAa,CAAClC,KAAd,CAAoBxF,QAApB,CAA6BiG,IAA7B,CAAJ,EAAwC;MACtC,MAAMmD,SAAS,GAAG3T,MAAM,CAAC4T,aAAP,CAAqB9B,OAAvC;MAEA,IAAI6B,SAAS,IAAInU,KAAK,CAAC4G,MAAN,KAAiBuN,SAAlC,EAA6C;QAC3C,OAAO,KAAP;;MAGFnU,KAAK,CAACmQ,cAAN;MAEA8D,YAAY,QAAZ,YAAAA,YAAY,CAAG;QAACjU,KAAK,EAAEA,KAAK,CAACkU;OAAjB,CAAZ;MAEA,OAAO,IAAP;;IAGF,OAAO,KAAP;;AAvBJ,CADqD;ACxOzD,SAASG,oBAATA,CACEC,UADF;EAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,cAAcA,UAA7B,CAAd;AACD;AAED,SAASE,iBAATA,CACEF,UADF;EAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,WAAWA,UAA1B,CAAd;AACD;AAaD,MAAaG,qBAAA;EAUXhG,YACU8C,KAAA,EACAmD,MAAA,EACRC,cAAA;;QAAAA,cAAA;MAAAA,cAAA,GAAiBhF,sBAAsB,CAAC4B,KAAK,CAACvR,KAAN,CAAY4G,MAAb;;SAF/B2K,KAAA;SACAmD,MAAA;SAXHlD,iBAAA,GAAoB;SACnBpF,QAAA;SACAwI,SAAA,GAAqB;SACrBC,kBAAA;SACAC,SAAA,GAAmC;SACnCvV,SAAA;SACAwV,iBAAA;SACArD,eAAA;IAGE,KAAAH,KAAA,GAAAA,KAAA;IACA,KAAAmD,MAAA,GAAAA,MAAA;IAGR,MAAM;MAAC1U;QAASuR,KAAhB;IACA,MAAM;MAAC3K;QAAU5G,KAAjB;IAEA,KAAKuR,KAAL,GAAaA,KAAb;IACA,KAAKmD,MAAL,GAAcA,MAAd;IACA,KAAKtI,QAAL,GAAgBX,gBAAgB,CAAC7E,MAAD,CAAhC;IACA,KAAKmO,iBAAL,GAAyB,IAAI5F,SAAJ,CAAc,KAAK/C,QAAnB,CAAzB;IACA,KAAK7M,SAAL,GAAiB,IAAI4P,SAAJ,CAAcwF,cAAd,CAAjB;IACA,KAAKjD,eAAL,GAAuB,IAAIvC,SAAJ,CAAczF,SAAS,CAAC9C,MAAD,CAAvB,CAAvB;IACA,KAAKiO,kBAAL,IAAAG,oBAAA,GAA0B7Q,mBAAmB,CAACnE,KAAD,CAA7C,YAAAgV,oBAAA,GAAwD3R,kBAAxD;IACA,KAAK0O,WAAL,GAAmB,KAAKA,WAAL,CAAiBH,IAAjB,CAAsB,IAAtB,CAAnB;IACA,KAAK8B,UAAL,GAAkB,KAAKA,UAAL,CAAgB9B,IAAhB,CAAqB,IAArB,CAAlB;IACA,KAAKgB,SAAL,GAAiB,KAAKA,SAAL,CAAehB,IAAf,CAAoB,IAApB,CAAjB;IACA,KAAKC,YAAL,GAAoB,KAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;IACA,KAAKqD,aAAL,GAAqB,KAAKA,aAAL,CAAmBrD,IAAnB,CAAwB,IAAxB,CAArB;IACA,KAAKsD,mBAAL,GAA2B,KAAKA,mBAAL,CAAyBtD,IAAzB,CAA8B,IAA9B,CAA3B;IAEA,KAAKE,MAAL;;EAGMA,MAAMA,CAAA;IACZ,MAAM;MACJ4C,MADI;MAEJnD,KAAK,EAAE;QACL1O,OAAO,EAAE;UAACsS,oBAAD;UAAuBC;;;QAEhC,IALJ;IAOA,KAAK7V,SAAL,CAAeI,GAAf,CAAmB+U,MAAM,CAACW,IAAP,CAAYC,IAA/B,EAAqC,KAAK5B,UAA1C,EAAsD;MAAC6B,OAAO,EAAE;KAAhE;IACA,KAAKhW,SAAL,CAAeI,GAAf,CAAmB+U,MAAM,CAAC9D,GAAP,CAAW0E,IAA9B,EAAoC,KAAK1C,SAAzC;IAEA,IAAI8B,MAAM,CAAChE,MAAX,EAAmB;MACjB,KAAKnR,SAAL,CAAeI,GAAf,CAAmB+U,MAAM,CAAChE,MAAP,CAAc4E,IAAjC,EAAuC,KAAKzD,YAA5C;;IAGF,KAAKH,eAAL,CAAqB/R,GAArB,CAAyBuQ,SAAS,CAAC8B,MAAnC,EAA2C,KAAKH,YAAhD;IACA,KAAKH,eAAL,CAAqB/R,GAArB,CAAyBuQ,SAAS,CAACsF,SAAnC,EAA8CrF,cAA9C;IACA,KAAKuB,eAAL,CAAqB/R,GAArB,CAAyBuQ,SAAS,CAAC+B,gBAAnC,EAAqD,KAAKJ,YAA1D;IACA,KAAKH,eAAL,CAAqB/R,GAArB,CAAyBuQ,SAAS,CAACuF,WAAnC,EAAgDtF,cAAhD;IACA,KAAK4E,iBAAL,CAAuBpV,GAAvB,CAA2BuQ,SAAS,CAACiC,OAArC,EAA8C,KAAK8C,aAAnD;IAEA,IAAIE,oBAAJ,EAA0B;MACxB,IACEC,0BADF,YACEA,0BAA0B,CAAG;QAC3BpV,KAAK,EAAE,KAAKuR,KAAL,CAAWvR,KADS;QAE3BoS,UAAU,EAAE,KAAKb,KAAL,CAAWa,UAFI;QAG3BvP,OAAO,EAAE,KAAK0O,KAAL,CAAW1O;OAHI,CAD5B,EAME;QACA,OAAO,KAAKkP,WAAL,EAAP;;MAGF,IAAIyC,iBAAiB,CAACW,oBAAD,CAArB,EAA6C;QAC3C,KAAKL,SAAL,GAAiB5C,UAAU,CACzB,KAAKH,WADoB,EAEzBoD,oBAAoB,CAACO,KAFI,CAA3B;QAIA,KAAKC,aAAL,CAAmBR,oBAAnB;QACA;;MAGF,IAAId,oBAAoB,CAACc,oBAAD,CAAxB,EAAgD;QAC9C,KAAKQ,aAAL,CAAmBR,oBAAnB;QACA;;;IAIJ,KAAKpD,WAAL;;EAGM+B,MAAMA,CAAA;IACZ,KAAKvU,SAAL,CAAe6P,SAAf;IACA,KAAKsC,eAAL,CAAqBtC,SAArB;;;IAIA8C,UAAU,CAAC,KAAK6C,iBAAL,CAAuB3F,SAAxB,EAAmC,EAAnC,CAAV;IAEA,IAAI,KAAK0F,SAAL,KAAmB,IAAvB,EAA6B;MAC3Bc,YAAY,CAAC,KAAKd,SAAN,CAAZ;MACA,KAAKA,SAAL,GAAiB,IAAjB;;;EAIIa,aAAaA,CACnBrB,UADmB,EAEnBuB,MAFmB;IAInB,MAAM;MAACrV,MAAD;MAASsV;QAAa,KAAKvE,KAAjC;IACAuE,SAAS,CAACtV,MAAD,EAAS8T,UAAT,EAAqB,KAAKO,kBAA1B,EAA8CgB,MAA9C,CAAT;;EAGM9D,WAAWA,CAAA;IACjB,MAAM;MAAC8C;QAAsB,IAA7B;IACA,MAAM;MAACxC;QAAW,KAAKd,KAAvB;IAEA,IAAIsD,kBAAJ,EAAwB;MACtB,KAAKD,SAAL,GAAiB,IAAjB,CADsB;;MAItB,KAAKG,iBAAL,CAAuBpV,GAAvB,CAA2BuQ,SAAS,CAAC6F,KAArC,EAA4C3F,eAA5C,EAA6D;QAC3D4F,OAAO,EAAE;OADX,EAJsB;;MAStB,KAAKd,mBAAL,GATsB;;MAYtB,KAAKH,iBAAL,CAAuBpV,GAAvB,CACEuQ,SAAS,CAAC+F,eADZ,EAEE,KAAKf,mBAFP;MAKA7C,OAAO,CAACwC,kBAAD,CAAP;;;EAIInB,UAAUA,CAAC1T,KAAD;;IAChB,MAAM;MAAC4U,SAAD;MAAYC,kBAAZ;MAAgCtD;QAAS,IAA/C;IACA,MAAM;MACJqC,MADI;MAEJ/Q,OAAO,EAAE;QAACsS;;QACR5D,KAHJ;IAKA,IAAI,CAACsD,kBAAL,EAAyB;MACvB;;IAGF,MAAMlB,WAAW,IAAAuC,qBAAA,GAAG/R,mBAAmB,CAACnE,KAAD,CAAtB,YAAAkW,qBAAA,GAAiC7S,kBAAlD;IACA,MAAMyM,KAAK,GAAGiD,QAAmB,CAAC8B,kBAAD,EAAqBlB,WAArB,CAAjC;;IAGA,IAAI,CAACiB,SAAD,IAAcO,oBAAlB,EAAwC;MACtC,IAAId,oBAAoB,CAACc,oBAAD,CAAxB,EAAgD;QAC9C,IACEA,oBAAoB,CAACgB,SAArB,IAAkC,IAAlC,IACAtG,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACgB,SAA7B,CAFrB,EAGE;UACA,OAAO,KAAKtE,YAAL,EAAP;;QAGF,IAAIhC,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACiB,QAA7B,CAAvB,EAA+D;UAC7D,OAAO,KAAKrE,WAAL,EAAP;;;MAIJ,IAAIyC,iBAAiB,CAACW,oBAAD,CAArB,EAA6C;QAC3C,IAAItF,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACgB,SAA7B,CAAvB,EAAgE;UAC9D,OAAO,KAAKtE,YAAL,EAAP;;;MAIJ,KAAK8D,aAAL,CAAmBR,oBAAnB,EAAyCrF,KAAzC;MACA;;IAGF,IAAI9P,KAAK,CAACqW,UAAV,EAAsB;MACpBrW,KAAK,CAACmQ,cAAN;;IAGFyD,MAAM,CAACD,WAAD,CAAN;;EAGMf,SAASA,CAAA;IACf,MAAM;MAAC0D,OAAD;MAAUzC;QAAS,KAAKtC,KAA9B;IAEA,KAAKuC,MAAL;IACA,IAAI,CAAC,KAAKc,SAAV,EAAqB;MACnB0B,OAAO,CAAC,KAAK/E,KAAL,CAAW/Q,MAAZ,CAAP;;IAEFqT,KAAK;;EAGChC,YAAYA,CAAA;IAClB,MAAM;MAACyE,OAAD;MAAUvC;QAAY,KAAKxC,KAAjC;IAEA,KAAKuC,MAAL;IACA,IAAI,CAAC,KAAKc,SAAV,EAAqB;MACnB0B,OAAO,CAAC,KAAK/E,KAAL,CAAW/Q,MAAZ,CAAP;;IAEFuT,QAAQ;;EAGFkB,aAAaA,CAACjV,KAAD;IACnB,IAAIA,KAAK,CAACgR,IAAN,KAAeX,YAAY,CAACM,GAAhC,EAAqC;MACnC,KAAKkB,YAAL;;;EAIIqD,mBAAmBA,CAAA;;IACzB,CAAAqB,qBAAA,QAAKnK,QAAL,CAAcoK,YAAd,uBAAAD,qBAAA,CAA8BE,eAA9B;;;ACtQJ,MAAM/B,MAAM,GAAyB;EACnChE,MAAM,EAAE;IAAC4E,IAAI,EAAE;GADoB;EAEnCD,IAAI,EAAE;IAACC,IAAI,EAAE;GAFsB;EAGnC1E,GAAG,EAAE;IAAC0E,IAAI,EAAE;;AAHuB,CAArC;AAUA,MAAaoB,aAAA,SAAsBjC,qBAAA;EACjChG,YAAY8C,KAAA;IACV,MAAM;MAACvR;QAASuR,KAAhB;;;IAGA,MAAMoD,cAAc,GAAGlJ,gBAAgB,CAACzL,KAAK,CAAC4G,MAAP,CAAvC;IAEA,MAAM2K,KAAN,EAAamD,MAAb,EAAqBC,cAArB;;;AAPS+B,aAAA,CAUJ1C,UAAA,GAAa,CAClB;EACEzE,SAAS,EAAE,eADb;EAEEC,OAAO,EAAEA,CAAA1P,IAAA,EAAAa,KAAA;QACP;MAACuT,WAAW,EAAElU;;QACd;MAACiU;;IAED,IAAI,CAACjU,KAAK,CAAC2W,SAAP,IAAoB3W,KAAK,CAAC4W,MAAN,KAAiB,CAAzC,EAA4C;MAC1C,OAAO,KAAP;;IAGF3C,YAAY,QAAZ,YAAAA,YAAY,CAAG;MAACjU;KAAJ,CAAZ;IAEA,OAAO,IAAP;;AAZJ,CADkB;ACpBtB,MAAM6W,QAAM,GAAyB;EACnCxB,IAAI,EAAE;IAACC,IAAI,EAAE;GADsB;EAEnC1E,GAAG,EAAE;IAAC0E,IAAI,EAAE;;AAFuB,CAArC;AAKA,IAAKwB,WAAL;AAAA,WAAKA,WAAA;EACHA,WAAA,CAAAA,WAAA;AACD,CAFD,EAAKA,WAAW,KAAXA,WAAW,MAAhB;AAQA,MAAaC,WAAA,SAAoBtC,qBAAA;EAC/BhG,YAAY8C,KAAA;IACV,MAAMA,KAAN,EAAasF,QAAb,EAAqBpL,gBAAgB,CAAC8F,KAAK,CAACvR,KAAN,CAAY4G,MAAb,CAArC;;;AAFSmQ,WAAA,CAKJ/C,UAAA,GAAa,CAClB;EACEzE,SAAS,EAAE,aADb;EAEEC,OAAO,EAAEA,CAAA1P,IAAA,EAAAa,KAAA;QACP;MAACuT,WAAW,EAAElU;;QACd;MAACiU;;IAED,IAAIjU,KAAK,CAAC4W,MAAN,KAAiBE,WAAW,CAACE,UAAjC,EAA6C;MAC3C,OAAO,KAAP;;IAGF/C,YAAY,QAAZ,YAAAA,YAAY,CAAG;MAACjU;KAAJ,CAAZ;IAEA,OAAO,IAAP;;AAZJ,CADkB;AClBtB,MAAMiX,QAAM,GAAyB;EACnCvG,MAAM,EAAE;IAAC4E,IAAI,EAAE;GADoB;EAEnCD,IAAI,EAAE;IAACC,IAAI,EAAE;GAFsB;EAGnC1E,GAAG,EAAE;IAAC0E,IAAI,EAAE;;AAHuB,CAArC;AAUA,MAAa4B,WAAA,SAAoBzC,qBAAA;EAC/BhG,YAAY8C,KAAA;IACV,MAAMA,KAAN,EAAa0F,QAAb;;EAuBU,OAALE,KAAKA,CAAA;;;;IAIVzL,MAAM,CAACgE,gBAAP,CAAwBuH,QAAM,CAAC5B,IAAP,CAAYC,IAApC,EAA0C5S,IAA1C,EAAgD;MAC9CsT,OAAO,EAAE,KADqC;MAE9CT,OAAO,EAAE;KAFX;IAKA,OAAO,SAAS6B,QAATA,CAAA;MACL1L,MAAM,CAAC4D,mBAAP,CAA2B2H,QAAM,CAAC5B,IAAP,CAAYC,IAAvC,EAA6C5S,IAA7C;KADF;;;IAMA,SAASA,IAATA,CAAA;;;AAxCSwU,WAAA,CAKJlD,UAAA,GAAa,CAClB;EACEzE,SAAS,EAAE,cADb;EAEEC,OAAO,EAAEA,CAAA1P,IAAA,EAAAa,KAAA;QACP;MAACuT,WAAW,EAAElU;;QACd;MAACiU;;IAED,MAAM;MAACoD;QAAWrX,KAAlB;IAEA,IAAIqX,OAAO,CAACnS,MAAR,GAAiB,CAArB,EAAwB;MACtB,OAAO,KAAP;;IAGF+O,YAAY,QAAZ,YAAAA,YAAY,CAAG;MAACjU;KAAJ,CAAZ;IAEA,OAAO,IAAP;;AAdJ,CADkB;IChBVsX,mBAAZ;AAAA,WAAYA,mBAAA;EACVA,mBAAA,CAAAA,mBAAA;EACAA,mBAAA,CAAAA,mBAAA;AACD,CAHD,EAAYA,mBAAmB,KAAnBA,mBAAmB,MAA/B;AAmCA,IAAYC,cAAZ;AAAA,WAAYA,cAAA;EACVA,cAAA,CAAAA,cAAA;EACAA,cAAA,CAAAA,cAAA;AACD,CAHD,EAAYA,cAAc,KAAdA,cAAc,MAA1B;AAUA,SAAgBC,gBAAA1X,IAAA;MAAgB;IAC9BwN,YAD8B;IAE9B6G,SAAS,GAAGmD,mBAAmB,CAACG,OAFF;IAG9BC,SAH8B;IAI9BC,YAJ8B;IAK9BC,OAL8B;IAM9BC,QAAQ,GAAG,CANmB;IAO9BC,KAAK,GAAGP,cAAc,CAACQ,SAPO;IAQ9BtQ,kBAR8B;IAS9BuG,mBAT8B;IAU9BgK,uBAV8B;IAW9BlI,KAX8B;IAY9BpC;;EAEA,MAAMuK,YAAY,GAAGC,eAAe,CAAC;IAACpI,KAAD;IAAQqI,QAAQ,EAAE,CAACP;GAApB,CAApC;EACA,MAAM,CAACQ,qBAAD,EAAwBC,uBAAxB,IAAmDC,WAAW,EAApE;EACA,MAAMC,WAAW,GAAGC,MAAM,CAAc;IAAChV,CAAC,EAAE,CAAJ;IAAOC,CAAC,EAAE;GAAxB,CAA1B;EACA,MAAMgV,eAAe,GAAGD,MAAM,CAAkB;IAAChV,CAAC,EAAE,CAAJ;IAAOC,CAAC,EAAE;GAA5B,CAA9B;EACA,MAAMQ,IAAI,GAAGpC,OAAO,CAAC;IACnB,QAAQsS,SAAR;MACE,KAAKmD,mBAAmB,CAACG,OAAzB;QACE,OAAOhQ,kBAAkB,GACrB;UACElD,GAAG,EAAEkD,kBAAkB,CAAChE,CAD1B;UAEEuD,MAAM,EAAES,kBAAkB,CAAChE,CAF7B;UAGEY,IAAI,EAAEoD,kBAAkB,CAACjE,CAH3B;UAIEsD,KAAK,EAAEW,kBAAkB,CAACjE;SALP,GAOrB,IAPJ;MAQF,KAAK8T,mBAAmB,CAACoB,aAAzB;QACE,OAAOf,YAAP;;GAZc,EAcjB,CAACxD,SAAD,EAAYwD,YAAZ,EAA0BlQ,kBAA1B,CAdiB,CAApB;EAeA,MAAMkR,kBAAkB,GAAGH,MAAM,CAAiB,IAAjB,CAAjC;EACA,MAAMI,UAAU,GAAGlZ,WAAW,CAAC;IAC7B,MAAM0N,eAAe,GAAGuL,kBAAkB,CAACrG,OAA3C;IAEA,IAAI,CAAClF,eAAL,EAAsB;MACpB;;IAGF,MAAMvB,UAAU,GAAG0M,WAAW,CAACjG,OAAZ,CAAoB9O,CAApB,GAAwBiV,eAAe,CAACnG,OAAhB,CAAwB9O,CAAnE;IACA,MAAMwI,SAAS,GAAGuM,WAAW,CAACjG,OAAZ,CAAoB7O,CAApB,GAAwBgV,eAAe,CAACnG,OAAhB,CAAwB7O,CAAlE;IAEA2J,eAAe,CAACqG,QAAhB,CAAyB5H,UAAzB,EAAqCG,SAArC;GAV4B,EAW3B,EAX2B,CAA9B;EAYA,MAAM6M,yBAAyB,GAAGhX,OAAO,CACvC,MACEiW,KAAK,KAAKP,cAAc,CAACQ,SAAzB,GACI,CAAC,GAAG/J,mBAAJ,EAAyB8K,OAAzB,EADJ,GAEI9K,mBAJiC,EAKvC,CAAC8J,KAAD,EAAQ9J,mBAAR,CALuC,CAAzC;EAQA7O,SAAS,CACP;IACE,IAAI,CAACyY,OAAD,IAAY,CAAC5J,mBAAmB,CAAC9I,MAAjC,IAA2C,CAACjB,IAAhD,EAAsD;MACpDoU,uBAAuB;MACvB;;IAGF,KAAK,MAAMjL,eAAX,IAA8ByL,yBAA9B,EAAyD;MACvD,IAAI,CAAAnB,SAAS,QAAT,YAAAA,SAAS,CAAGtK,eAAH,CAAT,MAAiC,KAArC,EAA4C;QAC1C;;MAGF,MAAM9G,KAAK,GAAG0H,mBAAmB,CAAC9E,OAApB,CAA4BkE,eAA5B,CAAd;MACA,MAAMC,mBAAmB,GAAG2K,uBAAuB,CAAC1R,KAAD,CAAnD;MAEA,IAAI,CAAC+G,mBAAL,EAA0B;QACxB;;MAGF,MAAM;QAACG,SAAD;QAAYC;UAASN,0BAA0B,CACnDC,eADmD,EAEnDC,mBAFmD,EAGnDpJ,IAHmD,EAInDqJ,YAJmD,EAKnDI,SALmD,CAArD;MAQA,KAAK,MAAMiB,IAAX,IAAmB,CAAC,GAAD,EAAM,GAAN,CAAnB,EAAwC;QACtC,IAAI,CAACsJ,YAAY,CAACtJ,IAAD,CAAZ,CAAmBnB,SAAS,CAACmB,IAAD,CAA5B,CAAL,EAAuD;UACrDlB,KAAK,CAACkB,IAAD,CAAL,GAAc,CAAd;UACAnB,SAAS,CAACmB,IAAD,CAAT,GAAkB,CAAlB;;;MAIJ,IAAIlB,KAAK,CAACjK,CAAN,GAAU,CAAV,IAAeiK,KAAK,CAAChK,CAAN,GAAU,CAA7B,EAAgC;QAC9B4U,uBAAuB;QAEvBM,kBAAkB,CAACrG,OAAnB,GAA6BlF,eAA7B;QACAgL,qBAAqB,CAACQ,UAAD,EAAaf,QAAb,CAArB;QAEAU,WAAW,CAACjG,OAAZ,GAAsB7E,KAAtB;QACAgL,eAAe,CAACnG,OAAhB,GAA0B9E,SAA1B;QAEA;;;IAIJ+K,WAAW,CAACjG,OAAZ,GAAsB;MAAC9O,CAAC,EAAE,CAAJ;MAAOC,CAAC,EAAE;KAAhC;IACAgV,eAAe,CAACnG,OAAhB,GAA0B;MAAC9O,CAAC,EAAE,CAAJ;MAAOC,CAAC,EAAE;KAApC;IACA4U,uBAAuB;GAjDlB;EAAA;EAoDP,CACE/K,YADF,EAEEsL,UAFF,EAGElB,SAHF,EAIEW,uBAJF,EAKET,OALF,EAMEC,QANF;EAAA;EAQEkB,IAAI,CAACC,SAAL,CAAe/U,IAAf,CARF;EAAA;EAUE8U,IAAI,CAACC,SAAL,CAAef,YAAf,CAVF,EAWEG,qBAXF,EAYEpK,mBAZF,EAaE6K,yBAbF,EAcEb,uBAdF;EAAA;EAgBEe,IAAI,CAACC,SAAL,CAAetL,SAAf,CAhBF,CApDO,CAAT;AAuED;AAOD,MAAMuL,mBAAmB,GAAiB;EACxCzV,CAAC,EAAE;IAAC,CAAC0I,SAAS,CAACyB,QAAX,GAAsB,KAAvB;IAA8B,CAACzB,SAAS,CAAC2B,OAAX,GAAqB;GADd;EAExCpK,CAAC,EAAE;IAAC,CAACyI,SAAS,CAACyB,QAAX,GAAsB,KAAvB;IAA8B,CAACzB,SAAS,CAAC2B,OAAX,GAAqB;;AAFd,CAA1C;AAKA,SAASqK,eAATA,CAAAvX,KAAA;MAAyB;IACvBmP,KADuB;IAEvBqI;;EAKA,MAAMe,aAAa,GAAGC,WAAW,CAACrJ,KAAD,CAAjC;EAEA,OAAOsJ,WAAW,CACfC,cAAD;IACE,IAAIlB,QAAQ,IAAI,CAACe,aAAb,IAA8B,CAACG,cAAnC,EAAmD;;MAEjD,OAAOJ,mBAAP;;IAGF,MAAMzL,SAAS,GAAG;MAChBhK,CAAC,EAAEK,IAAI,CAACyV,IAAL,CAAUxJ,KAAK,CAACtM,CAAN,GAAU0V,aAAa,CAAC1V,CAAlC,CADa;MAEhBC,CAAC,EAAEI,IAAI,CAACyV,IAAL,CAAUxJ,KAAK,CAACrM,CAAN,GAAUyV,aAAa,CAACzV,CAAlC;KAFL;;IAMA,OAAO;MACLD,CAAC,EAAE;QACD,CAAC0I,SAAS,CAACyB,QAAX,GACE0L,cAAc,CAAC7V,CAAf,CAAiB0I,SAAS,CAACyB,QAA3B,KAAwCH,SAAS,CAAChK,CAAV,KAAgB,CAAC,CAF1D;QAGD,CAAC0I,SAAS,CAAC2B,OAAX,GACEwL,cAAc,CAAC7V,CAAf,CAAiB0I,SAAS,CAAC2B,OAA3B,KAAuCL,SAAS,CAAChK,CAAV,KAAgB;OALtD;MAOLC,CAAC,EAAE;QACD,CAACyI,SAAS,CAACyB,QAAX,GACE0L,cAAc,CAAC5V,CAAf,CAAiByI,SAAS,CAACyB,QAA3B,KAAwCH,SAAS,CAAC/J,CAAV,KAAgB,CAAC,CAF1D;QAGD,CAACyI,SAAS,CAAC2B,OAAX,GACEwL,cAAc,CAAC5V,CAAf,CAAiByI,SAAS,CAAC2B,OAA3B,KAAuCL,SAAS,CAAC/J,CAAV,KAAgB;;KAX7D;GAbc,EA4BhB,CAAC0U,QAAD,EAAWrI,KAAX,EAAkBoJ,aAAlB,CA5BgB,CAAlB;AA8BD;SCjOeK,cACdC,cAAA,EACA/Y,EAAA;EAEA,MAAMgZ,aAAa,GAAGhZ,EAAE,IAAI,IAAN,GAAa+Y,cAAc,CAAC7T,GAAf,CAAmBlF,EAAnB,CAAb,GAAsC4Q,SAA5D;EACA,MAAMpH,IAAI,GAAGwP,aAAa,GAAGA,aAAa,CAACxP,IAAd,CAAmBqI,OAAtB,GAAgC,IAA1D;EAEA,OAAO8G,WAAW,CACfM,UAAD;;IACE,IAAIjZ,EAAE,IAAI,IAAV,EAAgB;MACd,OAAO,IAAP;;;;;IAMF,QAAAX,IAAA,GAAOmK,IAAP,WAAOA,IAAP,GAAeyP,UAAf,YAAA5Z,IAAA,GAA6B,IAA7B;GATc,EAWhB,CAACmK,IAAD,EAAOxJ,EAAP,CAXgB,CAAlB;AAaD;SCjBekZ,qBACd5W,OAAA,EACA6W,mBAAA;EAKA,OAAO/X,OAAO,CACZ,MACEkB,OAAO,CAACoD,MAAR,CAAmC,CAACC,WAAD,EAAcxD,MAAd;IACjC,MAAM;MAACA,MAAM,EAAEiX;QAAUjX,MAAzB;IAEA,MAAMkX,gBAAgB,GAAGD,MAAM,CAAC7F,UAAP,CAAkB+F,GAAlB,CAAuB5F,SAAD,KAAgB;MAC7D5E,SAAS,EAAE4E,SAAS,CAAC5E,SADwC;MAE7DC,OAAO,EAAEoK,mBAAmB,CAACzF,SAAS,CAAC3E,OAAX,EAAoB5M,MAApB;KAFiB,CAAtB,CAAzB;IAKA,OAAO,CAAC,GAAGwD,WAAJ,EAAiB,GAAG0T,gBAApB,CAAP;GARF,EASG,EATH,CAFU,EAYZ,CAAC/W,OAAD,EAAU6W,mBAAV,CAZY,CAAd;AAcD;IChBWI,iBAAZ;AAAA,WAAYA,iBAAA;EACVA,iBAAA,CAAAA,iBAAA;EACAA,iBAAA,CAAAA,iBAAA;EACAA,iBAAA,CAAAA,iBAAA;AACD,CAJD,EAAYA,iBAAiB,KAAjBA,iBAAiB,MAA7B;AAMA,IAAYC,kBAAZ;AAAA,WAAYA,kBAAA;EACVA,kBAAA;AACD,CAFD,EAAYA,kBAAkB,KAAlBA,kBAAkB,MAA9B;AAYA,MAAMC,YAAY,gBAAY,IAAIC,GAAJ,EAA9B;AAEA,SAAgBC,sBACdC,UAAA,EAAAva,IAAA;MACA;IAACwa,QAAD;IAAWC,YAAX;IAAyBC;;EAEzB,MAAM,CAACC,KAAD,EAAQC,QAAR,IAAoBlb,QAAQ,CAA4B,IAA5B,CAAlC;EACA,MAAM;IAACmb,SAAD;IAAYvM,OAAZ;IAAqBwM;MAAYJ,MAAvC;EACA,MAAMK,aAAa,GAAGrC,MAAM,CAAC6B,UAAD,CAA5B;EACA,MAAMlC,QAAQ,GAAG2C,UAAU,EAA3B;EACA,MAAMC,WAAW,GAAGC,cAAc,CAAC7C,QAAD,CAAlC;EACA,MAAM8C,0BAA0B,GAAGvb,WAAW,CAC5C,UAACwb,GAAD;QAACA,GAAA;MAAAA,GAAA,GAA0B;;IACzB,IAAIH,WAAW,CAACzI,OAAhB,EAAyB;MACvB;;IAGFoI,QAAQ,CAAEpY,KAAD;MACP,IAAIA,KAAK,KAAK,IAAd,EAAoB;QAClB,OAAO4Y,GAAP;;MAGF,OAAO5Y,KAAK,CAAC6Y,MAAN,CAAaD,GAAG,CAAC9X,MAAJ,CAAY3C,EAAD,IAAQ,CAAC6B,KAAK,CAACyI,QAAN,CAAetK,EAAf,CAApB,CAAb,CAAP;KALM,CAAR;GAN0C,EAc5C,CAACsa,WAAD,CAd4C,CAA9C;EAgBA,MAAMjG,SAAS,GAAG0D,MAAM,CAAwB,IAAxB,CAAxB;EACA,MAAMjT,cAAc,GAAG6T,WAAW,CAC/BgC,aAAD;IACE,IAAIjD,QAAQ,IAAI,CAACmC,QAAjB,EAA2B;MACzB,OAAOJ,YAAP;;IAGF,IACE,CAACkB,aAAD,IACAA,aAAa,KAAKlB,YADlB,IAEAW,aAAa,CAACvI,OAAd,KAA0B+H,UAF1B,IAGAI,KAAK,IAAI,IAJX,EAKE;MACA,MAAMV,GAAG,GAAY,IAAII,GAAJ,EAArB;MAEA,KAAK,IAAIhZ,SAAT,IAAsBkZ,UAAtB,EAAkC;QAChC,IAAI,CAAClZ,SAAL,EAAgB;UACd;;QAGF,IACEsZ,KAAK,IACLA,KAAK,CAACvV,MAAN,GAAe,CADf,IAEA,CAACuV,KAAK,CAAC1P,QAAN,CAAe5J,SAAS,CAACV,EAAzB,CAFD,IAGAU,SAAS,CAAC8C,IAAV,CAAeqO,OAJjB,EAKE;;UAEAyH,GAAG,CAACsB,GAAJ,CAAQla,SAAS,CAACV,EAAlB,EAAsBU,SAAS,CAAC8C,IAAV,CAAeqO,OAArC;UACA;;QAGF,MAAMrI,IAAI,GAAG9I,SAAS,CAAC8I,IAAV,CAAeqI,OAA5B;QACA,MAAMrO,IAAI,GAAGgG,IAAI,GAAG,IAAIuE,IAAJ,CAASJ,OAAO,CAACnE,IAAD,CAAhB,EAAwBA,IAAxB,CAAH,GAAmC,IAApD;QAEA9I,SAAS,CAAC8C,IAAV,CAAeqO,OAAf,GAAyBrO,IAAzB;QAEA,IAAIA,IAAJ,EAAU;UACR8V,GAAG,CAACsB,GAAJ,CAAQla,SAAS,CAACV,EAAlB,EAAsBwD,IAAtB;;;MAIJ,OAAO8V,GAAP;;IAGF,OAAOqB,aAAP;GA3C8B,EA6ChC,CAACf,UAAD,EAAaI,KAAb,EAAoBH,QAApB,EAA8BnC,QAA9B,EAAwC/J,OAAxC,CA7CgC,CAAlC;EAgDAjP,SAAS,CAAC;IACR0b,aAAa,CAACvI,OAAd,GAAwB+H,UAAxB;GADO,EAEN,CAACA,UAAD,CAFM,CAAT;EAIAlb,SAAS,CACP;IACE,IAAIgZ,QAAJ,EAAc;MACZ;;IAGF8C,0BAA0B;GANrB;EAAA;EASP,CAACX,QAAD,EAAWnC,QAAX,CATO,CAAT;EAYAhZ,SAAS,CACP;IACE,IAAIsb,KAAK,IAAIA,KAAK,CAACvV,MAAN,GAAe,CAA5B,EAA+B;MAC7BwV,QAAQ,CAAC,IAAD,CAAR;;GAHG;EAAA;EAOP,CAAC3B,IAAI,CAACC,SAAL,CAAeyB,KAAf,CAAD,CAPO,CAAT;EAUAtb,SAAS,CACP;IACE,IACEgZ,QAAQ,IACR,OAAOwC,SAAP,KAAqB,QADrB,IAEA7F,SAAS,CAACxC,OAAV,KAAsB,IAHxB,EAIE;MACA;;IAGFwC,SAAS,CAACxC,OAAV,GAAoBJ,UAAU,CAAC;MAC7B+I,0BAA0B;MAC1BnG,SAAS,CAACxC,OAAV,GAAoB,IAApB;KAF4B,EAG3BqI,SAH2B,CAA9B;GAVK;EAAA;EAgBP,CAACA,SAAD,EAAYxC,QAAZ,EAAsB8C,0BAAtB,EAAkD,GAAGV,YAArD,CAhBO,CAAT;EAmBA,OAAO;IACLhV,cADK;IAEL0V,0BAFK;IAGLK,kBAAkB,EAAEb,KAAK,IAAI;GAH/B;EAMA,SAASK,UAATA,CAAA;IACE,QAAQF,QAAR;MACE,KAAKZ,iBAAiB,CAACuB,MAAvB;QACE,OAAO,KAAP;MACF,KAAKvB,iBAAiB,CAACwB,cAAvB;QACE,OAAOlB,QAAP;MACF;QACE,OAAO,CAACA,QAAR;;;AAGP;SCpKemB,gBAIdnZ,KAAA,EACAoZ,SAAA;EAEA,OAAOtC,WAAW,CACfgC,aAAD;IACE,IAAI,CAAC9Y,KAAL,EAAY;MACV,OAAO,IAAP;;IAGF,IAAI8Y,aAAJ,EAAmB;MACjB,OAAOA,aAAP;;IAGF,OAAO,OAAOM,SAAP,KAAqB,UAArB,GAAkCA,SAAS,CAACpZ,KAAD,CAA3C,GAAqDA,KAA5D;GAVc,EAYhB,CAACoZ,SAAD,EAAYpZ,KAAZ,CAZgB,CAAlB;AAcD;SCtBeqZ,eACd1R,IAAA,EACAmE,OAAA;EAEA,OAAOqN,eAAe,CAACxR,IAAD,EAAOmE,OAAP,CAAtB;AACD;;ACAD;;;;;AAIA,SAAgBwN,oBAAA9b,IAAA;MAAoB;IAAC+b,QAAD;IAAW1D;;EAC7C,MAAM2D,eAAe,GAAGC,QAAQ,CAACF,QAAD,CAAhC;EACA,MAAMG,gBAAgB,GAAGna,OAAO,CAAC;IAC/B,IACEsW,QAAQ,IACR,OAAOzM,MAAP,KAAkB,WADlB,IAEA,OAAOA,MAAM,CAACuQ,gBAAd,KAAmC,WAHrC,EAIE;MACA,OAAO5K,SAAP;;IAGF,MAAM;MAAC4K;QAAoBvQ,MAA3B;IAEA,OAAO,IAAIuQ,gBAAJ,CAAqBH,eAArB,CAAP;GAX8B,EAY7B,CAACA,eAAD,EAAkB3D,QAAlB,CAZ6B,CAAhC;EAcAhZ,SAAS,CAAC;IACR,OAAO,MAAM6c,gBAAN,oBAAMA,gBAAgB,CAAEE,UAAlB,EAAb;GADO,EAEN,CAACF,gBAAD,CAFM,CAAT;EAIA,OAAOA,gBAAP;AACD;;ACzBD;;;;;AAIA,SAAgBG,kBAAArc,IAAA;MAAkB;IAAC+b,QAAD;IAAW1D;;EAC3C,MAAMiE,YAAY,GAAGL,QAAQ,CAACF,QAAD,CAA7B;EACA,MAAMQ,cAAc,GAAGxa,OAAO,CAC5B;IACE,IACEsW,QAAQ,IACR,OAAOzM,MAAP,KAAkB,WADlB,IAEA,OAAOA,MAAM,CAAC4Q,cAAd,KAAiC,WAHnC,EAIE;MACA,OAAOjL,SAAP;;IAGF,MAAM;MAACiL;QAAkB5Q,MAAzB;IAEA,OAAO,IAAI4Q,cAAJ,CAAmBF,YAAnB,CAAP;GAZ0B;EAAA;EAe5B,CAACjE,QAAD,CAf4B,CAA9B;EAkBAhZ,SAAS,CAAC;IACR,OAAO,MAAMkd,cAAN,oBAAMA,cAAc,CAAEH,UAAhB,EAAb;GADO,EAEN,CAACG,cAAD,CAFM,CAAT;EAIA,OAAOA,cAAP;AACD;AC5BD,SAASE,cAATA,CAAwB/S,OAAxB;EACE,OAAO,IAAIgF,IAAJ,CAASjF,aAAa,CAACC,OAAD,CAAtB,EAAiCA,OAAjC,CAAP;AACD;AAED,SAAgBgT,QACdhT,OAAA,EACA4E,OAAA,EACAqO,YAAA;MADArO,OAAA;IAAAA,OAAA,GAAgDmO,cAAA;;EAGhD,MAAM,CAACtY,IAAD,EAAOyY,OAAP,IAAkBld,QAAQ,CAAoB,IAApB,CAAhC;EAEA,SAASmd,WAATA,CAAA;IACED,OAAO,CAAEE,WAAD;MACN,IAAI,CAACpT,OAAL,EAAc;QACZ,OAAO,IAAP;;MAGF,IAAIA,OAAO,CAACqT,WAAR,KAAwB,KAA5B,EAAmC;QAAA,IAAA/c,IAAA;;;;QAGjC,QAAAA,IAAA,GAAO8c,WAAP,WAAOA,WAAP,GAAsBH,YAAtB,YAAA3c,IAAA,GAAsC,IAAtC;;MAGF,MAAMgd,OAAO,GAAG1O,OAAO,CAAC5E,OAAD,CAAvB;MAEA,IAAIuP,IAAI,CAACC,SAAL,CAAe4D,WAAf,MAAgC7D,IAAI,CAACC,SAAL,CAAe8D,OAAf,CAApC,EAA6D;QAC3D,OAAOF,WAAP;;MAGF,OAAOE,OAAP;KAjBK,CAAP;;EAqBF,MAAMd,gBAAgB,GAAGJ,mBAAmB,CAAC;IAC3CC,QAAQA,CAACkB,OAAD;MACN,IAAI,CAACvT,OAAL,EAAc;QACZ;;MAGF,KAAK,MAAMwT,MAAX,IAAqBD,OAArB,EAA8B;QAC5B,MAAM;UAAChd,IAAD;UAAO6G;YAAUoW,MAAvB;QAEA,IACEjd,IAAI,KAAK,WAAT,IACA6G,MAAM,YAAYqW,WADlB,IAEArW,MAAM,CAACsW,QAAP,CAAgB1T,OAAhB,CAHF,EAIE;UACAmT,WAAW;UACX;;;;GAfoC,CAA5C;EAoBA,MAAMN,cAAc,GAAGF,iBAAiB,CAAC;IAACN,QAAQ,EAAEc;GAAZ,CAAxC;EAEAQ,yBAAyB,CAAC;IACxBR,WAAW;IAEX,IAAInT,OAAJ,EAAa;MACX6S,cAAc,QAAd,YAAAA,cAAc,CAAEe,OAAhB,CAAwB5T,OAAxB;MACAwS,gBAAgB,QAAhB,YAAAA,gBAAgB,CAAEoB,OAAlB,CAA0BhR,QAAQ,CAACiR,IAAnC,EAAyC;QACvCC,SAAS,EAAE,IAD4B;QAEvCC,OAAO,EAAE;OAFX;KAFF,MAMO;MACLlB,cAAc,QAAd,YAAAA,cAAc,CAAEH,UAAhB;MACAF,gBAAgB,QAAhB,YAAAA,gBAAgB,CAAEE,UAAlB;;GAXqB,EAatB,CAAC1S,OAAD,CAbsB,CAAzB;EAeA,OAAOvF,IAAP;AACD;SC3EeuZ,aAAavZ,IAAA;EAC3B,MAAMwZ,WAAW,GAAGhC,eAAe,CAACxX,IAAD,CAAnC;EAEA,OAAO+D,YAAY,CAAC/D,IAAD,EAAOwZ,WAAP,CAAnB;AACD;ACJD,MAAMC,cAAY,GAAc,EAAhC;AAEA,SAAgBC,uBAAuB1T,IAAA;EACrC,MAAM2T,YAAY,GAAGpF,MAAM,CAACvO,IAAD,CAA3B;EAEA,MAAM4T,SAAS,GAAGzE,WAAW,CAC1BgC,aAAD;IACE,IAAI,CAACnR,IAAL,EAAW;MACT,OAAOyT,cAAP;;IAGF,IACEtC,aAAa,IACbA,aAAa,KAAKsC,cADlB,IAEAzT,IAFA,IAGA2T,YAAY,CAACtL,OAHb,IAIArI,IAAI,CAACiB,UAAL,KAAoB0S,YAAY,CAACtL,OAAb,CAAqBpH,UAL3C,EAME;MACA,OAAOkQ,aAAP;;IAGF,OAAO3Q,sBAAsB,CAACR,IAAD,CAA7B;GAhByB,EAkB3B,CAACA,IAAD,CAlB2B,CAA7B;EAqBA9K,SAAS,CAAC;IACRye,YAAY,CAACtL,OAAb,GAAuBrI,IAAvB;GADO,EAEN,CAACA,IAAD,CAFM,CAAT;EAIA,OAAO4T,SAAP;AACD;SCvBeC,iBAAiBC,QAAA;EAC/B,MAAM,CACJC,iBADI,EAEJC,oBAFI,IAGFze,QAAQ,CAA2B,IAA3B,CAHZ;EAIA,MAAM0e,YAAY,GAAG1F,MAAM,CAACuF,QAAD,CAA3B;;EAGA,MAAMI,YAAY,GAAGze,WAAW,CAAEM,KAAD;IAC/B,MAAM8K,gBAAgB,GAAGO,oBAAoB,CAACrL,KAAK,CAAC4G,MAAP,CAA7C;IAEA,IAAI,CAACkE,gBAAL,EAAuB;MACrB;;IAGFmT,oBAAoB,CAAED,iBAAD;MACnB,IAAI,CAACA,iBAAL,EAAwB;QACtB,OAAO,IAAP;;MAGFA,iBAAiB,CAAC3C,GAAlB,CACEvQ,gBADF,EAEEmB,oBAAoB,CAACnB,gBAAD,CAFtB;MAKA,OAAO,IAAIqP,GAAJ,CAAQ6D,iBAAR,CAAP;KAVkB,CAApB;GAP8B,EAmB7B,EAnB6B,CAAhC;EAqBA7e,SAAS,CAAC;IACR,MAAMif,gBAAgB,GAAGF,YAAY,CAAC5L,OAAtC;IAEA,IAAIyL,QAAQ,KAAKK,gBAAjB,EAAmC;MACjCC,OAAO,CAACD,gBAAD,CAAP;MAEA,MAAME,OAAO,GAAGP,QAAQ,CACrBhE,GADa,CACRvQ,OAAD;QACH,MAAM+U,iBAAiB,GAAGlT,oBAAoB,CAAC7B,OAAD,CAA9C;QAEA,IAAI+U,iBAAJ,EAAuB;UACrBA,iBAAiB,CAAC7O,gBAAlB,CAAmC,QAAnC,EAA6CyO,YAA7C,EAA2D;YACzD5I,OAAO,EAAE;WADX;UAIA,OAAO,CACLgJ,iBADK,EAELtS,oBAAoB,CAACsS,iBAAD,CAFf,CAAP;;QAMF,OAAO,IAAP;OAfY,EAiBbnb,MAjBa,CAmBVuD,KADF,IAKKA,KAAK,IAAI,IAvBF,CAAhB;MA0BAsX,oBAAoB,CAACK,OAAO,CAACpZ,MAAR,GAAiB,IAAIiV,GAAJ,CAAQmE,OAAR,CAAjB,GAAoC,IAArC,CAApB;MAEAJ,YAAY,CAAC5L,OAAb,GAAuByL,QAAvB;;IAGF,OAAO;MACLM,OAAO,CAACN,QAAD,CAAP;MACAM,OAAO,CAACD,gBAAD,CAAP;KAFF;IAKA,SAASC,OAATA,CAAiBN,QAAjB;MACEA,QAAQ,CAAC9d,OAAT,CAAkBuJ,OAAD;QACf,MAAM+U,iBAAiB,GAAGlT,oBAAoB,CAAC7B,OAAD,CAA9C;QAEA+U,iBAAiB,QAAjB,YAAAA,iBAAiB,CAAEjP,mBAAnB,CAAuC,QAAvC,EAAiD6O,YAAjD;OAHF;;GA3CK,EAiDN,CAACA,YAAD,EAAeJ,QAAf,CAjDM,CAAT;EAmDA,OAAOlc,OAAO,CAAC;IACb,IAAIkc,QAAQ,CAAC7Y,MAAb,EAAqB;MACnB,OAAO8Y,iBAAiB,GACpBhb,KAAK,CAACwb,IAAN,CAAWR,iBAAiB,CAACS,MAAlB,EAAX,EAAuCtY,MAAvC,CACE,CAACkC,GAAD,EAAMsL,WAAN,KAAsBhU,GAAG,CAAC0I,GAAD,EAAMsL,WAAN,CAD3B,EAEEtQ,kBAFF,CADoB,GAKpB0K,gBAAgB,CAACgQ,QAAD,CALpB;;IAQF,OAAO1a,kBAAP;GAVY,EAWX,CAAC0a,QAAD,EAAWC,iBAAX,CAXW,CAAd;AAYD;SCpGeU,sBACdhQ,aAAA,EACA6L,YAAA;MAAAA,YAAA;IAAAA,YAAA,GAAsB;;EAEtB,MAAMoE,oBAAoB,GAAGnG,MAAM,CAAqB,IAArB,CAAnC;EAEArZ,SAAS,CACP;IACEwf,oBAAoB,CAACrM,OAArB,GAA+B,IAA/B;GAFK;EAAA;EAKPiI,YALO,CAAT;EAQApb,SAAS,CAAC;IACR,MAAMyf,gBAAgB,GAAGlQ,aAAa,KAAKrL,kBAA3C;IAEA,IAAIub,gBAAgB,IAAI,CAACD,oBAAoB,CAACrM,OAA9C,EAAuD;MACrDqM,oBAAoB,CAACrM,OAArB,GAA+B5D,aAA/B;;IAGF,IAAI,CAACkQ,gBAAD,IAAqBD,oBAAoB,CAACrM,OAA9C,EAAuD;MACrDqM,oBAAoB,CAACrM,OAArB,GAA+B,IAA/B;;GARK,EAUN,CAAC5D,aAAD,CAVM,CAAT;EAYA,OAAOiQ,oBAAoB,CAACrM,OAArB,GACHS,QAAQ,CAACrE,aAAD,EAAgBiQ,oBAAoB,CAACrM,OAArC,CADL,GAEHjP,kBAFJ;AAGD;SC7Bewb,eAAe9b,OAAA;EAC7B5D,SAAS,CACP;IACE,IAAI,CAACmM,SAAL,EAAgB;MACd;;IAGF,MAAMwT,WAAW,GAAG/b,OAAO,CAACgX,GAAR,CAAYja,IAAA;MAAA,IAAC;QAAC8C;OAAF,GAAA9C,IAAA;MAAA,OAAc8C,MAAM,CAACuU,KAArB,oBAAcvU,MAAM,CAACuU,KAAP,EAAd;KAAZ,CAApB;IAEA,OAAO;MACL,KAAK,MAAMC,QAAX,IAAuB0H,WAAvB,EAAoC;QAClC1H,QAAQ,QAAR,YAAAA,QAAQ;;KAFZ;GARK;EAAA;;EAgBPrU,OAAO,CAACgX,GAAR,CAAYpZ,KAAA;IAAA,IAAC;MAACiC;KAAF,GAAAjC,KAAA;IAAA,OAAciC,MAAd;GAAZ,CAhBO,CAAT;AAkBD;SCXemc,sBACdxf,SAAA,EACAkB,EAAA;EAEA,OAAOoB,OAAO,CAAC;IACb,OAAOtC,SAAS,CAAC4G,MAAV,CACL,CAACkC,GAAD,EAAAvI,IAAA;UAAM;QAACyP,SAAD;QAAYC;;MAChBnH,GAAG,CAACkH,SAAD,CAAH,GAAkBvP,KAAD;QACfwP,OAAO,CAACxP,KAAD,EAAQS,EAAR,CAAP;OADF;MAIA,OAAO4H,GAAP;KANG,EAQL,EARK,CAAP;GADY,EAWX,CAAC9I,SAAD,EAAYkB,EAAZ,CAXW,CAAd;AAYD;SCzBeue,cAAcxV,OAAA;EAC5B,OAAO3H,OAAO,CAAC,MAAO2H,OAAO,GAAGK,mBAAmB,CAACL,OAAD,CAAtB,GAAkC,IAAjD,EAAwD,CACpEA,OADoE,CAAxD,CAAd;AAGD;ACED,MAAMyV,cAAY,GAAW,EAA7B;AAEA,SAAgBC,SACdnB,QAAA,EACA3P,OAAA;MAAAA,OAAA;IAAAA,OAAA,GAA4C7E,aAAA;;EAE5C,MAAM,CAAC4V,YAAD,IAAiBpB,QAAvB;EACA,MAAMqB,UAAU,GAAGJ,aAAa,CAC9BG,YAAY,GAAGzV,SAAS,CAACyV,YAAD,CAAZ,GAA6B,IADX,CAAhC;EAGA,MAAM,CAACE,KAAD,EAAQC,QAAR,IAAoB9f,QAAQ,CAAeyf,cAAf,CAAlC;EAEA,SAASM,YAATA,CAAA;IACED,QAAQ,CAAC;MACP,IAAI,CAACvB,QAAQ,CAAC7Y,MAAd,EAAsB;QACpB,OAAO+Z,cAAP;;MAGF,OAAOlB,QAAQ,CAAChE,GAAT,CAAcvQ,OAAD,IAClB2C,0BAA0B,CAAC3C,OAAD,CAA1B,GACK4V,UADL,GAEI,IAAI5Q,IAAJ,CAASJ,OAAO,CAAC5E,OAAD,CAAhB,EAA2BA,OAA3B,CAHC,CAAP;KALM,CAAR;;EAaF,MAAM6S,cAAc,GAAGF,iBAAiB,CAAC;IAACN,QAAQ,EAAE0D;GAAZ,CAAxC;EAEApC,yBAAyB,CAAC;IACxBd,cAAc,QAAd,YAAAA,cAAc,CAAEH,UAAhB;IACAqD,YAAY;IACZxB,QAAQ,CAAC9d,OAAT,CAAkBuJ,OAAD,IAAa6S,cAAb,oBAAaA,cAAc,CAAEe,OAAhB,CAAwB5T,OAAxB,CAA9B;GAHuB,EAItB,CAACuU,QAAD,CAJsB,CAAzB;EAMA,OAAOsB,KAAP;AACD;SC3CeG,kBACdvV,IAAA;EAEA,IAAI,CAACA,IAAL,EAAW;IACT,OAAO,IAAP;;EAGF,IAAIA,IAAI,CAACwV,QAAL,CAAcva,MAAd,GAAuB,CAA3B,EAA8B;IAC5B,OAAO+E,IAAP;;EAEF,MAAMyV,UAAU,GAAGzV,IAAI,CAACwV,QAAL,CAAc,CAAd,CAAnB;EAEA,OAAOzU,aAAa,CAAC0U,UAAD,CAAb,GAA4BA,UAA5B,GAAyCzV,IAAhD;AACD;SCHe0V,wBAAA7f,IAAA;MAAwB;IACtCsO;;EAEA,MAAM,CAACnK,IAAD,EAAOyY,OAAP,IAAkBld,QAAQ,CAAoB,IAApB,CAAhC;EACA,MAAM4c,YAAY,GAAG1c,WAAW,CAC7B4e,OAAD;IACE,KAAK,MAAM;MAAC1X;KAAZ,IAAuB0X,OAAvB,EAAgC;MAC9B,IAAItT,aAAa,CAACpE,MAAD,CAAjB,EAA2B;QACzB8V,OAAO,CAAEzY,IAAD;UACN,MAAM6Y,OAAO,GAAG1O,OAAO,CAACxH,MAAD,CAAvB;UAEA,OAAO3C,IAAI,GACP;YAAC,GAAGA,IAAJ;YAAUK,KAAK,EAAEwY,OAAO,CAACxY,KAAzB;YAAgCE,MAAM,EAAEsY,OAAO,CAACtY;WADzC,GAEPsY,OAFJ;SAHK,CAAP;QAOA;;;GAXwB,EAe9B,CAAC1O,OAAD,CAf8B,CAAhC;EAiBA,MAAMiO,cAAc,GAAGF,iBAAiB,CAAC;IAACN,QAAQ,EAAEO;GAAZ,CAAxC;EACA,MAAMwD,gBAAgB,GAAGlgB,WAAW,CACjC8J,OAAD;IACE,MAAMS,IAAI,GAAGuV,iBAAiB,CAAChW,OAAD,CAA9B;IAEA6S,cAAc,QAAd,YAAAA,cAAc,CAAEH,UAAhB;IAEA,IAAIjS,IAAJ,EAAU;MACRoS,cAAc,QAAd,YAAAA,cAAc,CAAEe,OAAhB,CAAwBnT,IAAxB;;IAGFyS,OAAO,CAACzS,IAAI,GAAGmE,OAAO,CAACnE,IAAD,CAAV,GAAmB,IAAxB,CAAP;GAVgC,EAYlC,CAACmE,OAAD,EAAUiO,cAAV,CAZkC,CAApC;EAcA,MAAM,CAACwD,OAAD,EAAUC,MAAV,IAAoBC,UAAU,CAACH,gBAAD,CAApC;EAEA,OAAO/d,OAAO,CACZ,OAAO;IACLge,OADK;IAEL5b,IAFK;IAGL6b;GAHF,CADY,EAMZ,CAAC7b,IAAD,EAAO4b,OAAP,EAAgBC,MAAhB,CANY,CAAd;AAQD;AC9CM,MAAME,cAAc,GAAG,CAC5B;EAACpd,MAAM,EAAE8T,aAAT;EAAwB7T,OAAO,EAAE;AAAjC,CAD4B,EAE5B;EAACD,MAAM,EAAE0O,cAAT;EAAyBzO,OAAO,EAAE;AAAlC,CAF4B,CAAvB;AAKP,MAAaod,WAAW,GAAY;EAAC3N,OAAO,EAAE;AAAV,CAA7B;AAEP,MAAa4N,6BAA6B,GAAyC;EACjF7f,SAAS,EAAE;IACT+N,OAAO,EAAExE;GAFsE;EAIjFuW,SAAS,EAAE;IACT/R,OAAO,EAAExE,8BADA;IAETgR,QAAQ,EAAEZ,iBAAiB,CAACoG,aAFnB;IAGTzF,SAAS,EAAEV,kBAAkB,CAACoG;GAPiD;EASjFC,WAAW,EAAE;IACXlS,OAAO,EAAE7E;;AAVsE,CAA5E;MCdMgX,sBAAA,SAA+BpG,GAAA;EAI1CxU,GAAGA,CAAClF,EAAD;;IACD,OAAOA,EAAE,IAAI,IAAN,IAAA+f,UAAA,GAAa,MAAM7a,GAAN,CAAUlF,EAAV,CAAb,YAAA+f,UAAA,GAA8BnP,SAA9B,GAA0CA,SAAjD;;EAGFoP,OAAOA,CAAA;IACL,OAAOzd,KAAK,CAACwb,IAAN,CAAW,KAAKC,MAAL,EAAX,CAAP;;EAGFiC,UAAUA,CAAA;IACR,OAAO,KAAKD,OAAL,GAAerd,MAAf,CAAsBtD,IAAA;MAAA,IAAC;QAACqY;OAAF,GAAArY,IAAA;MAAA,OAAgB,CAACqY,QAAjB;KAAtB,CAAP;;EAGFwI,UAAUA,CAAClgB,EAAD;;IACR,QAAAmgB,qBAAA,IAAAC,SAAA,GAAO,KAAKlb,GAAL,CAASlF,EAAT,CAAP,qBAAOogB,SAAA,CAAc5W,IAAd,CAAmBqI,OAA1B,YAAAsO,qBAAA,GAAqCvP,SAArC;;;ACfG,MAAMyP,oBAAoB,GAA4B;EAC3DC,cAAc,EAAE,IAD2C;EAE3DvgB,MAAM,EAAE,IAFmD;EAG3D4R,UAAU,EAAE,IAH+C;EAI3D4O,cAAc,EAAE,IAJ2C;EAK3Dhc,UAAU,EAAE,IAL+C;EAM3Dic,iBAAiB,EAAE,IANwC;EAO3DzH,cAAc,eAAE,IAAIW,GAAJ,EAP2C;EAQ3D5U,cAAc,eAAE,IAAI4U,GAAJ,EAR2C;EAS3D3U,mBAAmB,eAAE,IAAI+a,sBAAJ,EATsC;EAU3D3f,IAAI,EAAE,IAVqD;EAW3D0f,WAAW,EAAE;IACXT,OAAO,EAAE;MACPvN,OAAO,EAAE;KAFA;IAIXrO,IAAI,EAAE,IAJK;IAKX6b,MAAM,EAAEpd;GAhBiD;EAkB3DsL,mBAAmB,EAAE,EAlBsC;EAmB3DgK,uBAAuB,EAAE,EAnBkC;EAoB3DkJ,sBAAsB,EAAEhB,6BApBmC;EAqB3DjF,0BAA0B,EAAEvY,IArB+B;EAsB3D0c,UAAU,EAAE,IAtB+C;EAuB3D9D,kBAAkB,EAAE;AAvBuC,CAAtD;AA0BP,MAAa6F,sBAAsB,GAA8B;EAC/DJ,cAAc,EAAE,IAD+C;EAE/D/M,UAAU,EAAE,EAFmD;EAG/DxT,MAAM,EAAE,IAHuD;EAI/DwgB,cAAc,EAAE,IAJ+C;EAK/DI,iBAAiB,EAAE;IACjB/gB,SAAS,EAAE;GANkD;EAQ/DR,QAAQ,EAAE6C,IARqD;EAS/D8W,cAAc,eAAE,IAAIW,GAAJ,EAT+C;EAU/DvZ,IAAI,EAAE,IAVyD;EAW/Dqa,0BAA0B,EAAEvY;AAXmC,CAA1D;AAcP,MAAa2e,eAAe,gBAAGviB,aAAa,CAC1CqiB,sBAD0C,CAArC;AAIP,MAAaG,aAAa,gBAAGxiB,aAAa,CACxCgiB,oBADwC,CAAnC;SC/CSS,gBAAA;EACd,OAAO;IACLlhB,SAAS,EAAE;MACTG,MAAM,EAAE,IADC;MAETqU,kBAAkB,EAAE;QAACrR,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;OAFrB;MAGT+d,KAAK,EAAE,IAAIrH,GAAJ,EAHE;MAITsH,SAAS,EAAE;QAACje,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;;KALlB;IAOL0c,SAAS,EAAE;MACT9F,UAAU,EAAE,IAAIkG,sBAAJ;;GARhB;AAWD;AAED,SAAgBmB,QAAQC,KAAA,EAAcC,MAAA;EACpC,QAAQA,MAAM,CAAC7hB,IAAf;IACE,KAAK0C,MAAM,CAAC+S,SAAZ;MACE,OAAO;QACL,GAAGmM,KADE;QAELthB,SAAS,EAAE;UACT,GAAGshB,KAAK,CAACthB,SADA;UAETwU,kBAAkB,EAAE+M,MAAM,CAAC/M,kBAFlB;UAGTrU,MAAM,EAAEohB,MAAM,CAACphB;;OALnB;IAQF,KAAKiC,MAAM,CAACof,QAAZ;MACE,IAAIF,KAAK,CAACthB,SAAN,CAAgBG,MAAhB,IAA0B,IAA9B,EAAoC;QAClC,OAAOmhB,KAAP;;MAGF,OAAO;QACL,GAAGA,KADE;QAELthB,SAAS,EAAE;UACT,GAAGshB,KAAK,CAACthB,SADA;UAETohB,SAAS,EAAE;YACTje,CAAC,EAAEoe,MAAM,CAACjO,WAAP,CAAmBnQ,CAAnB,GAAuBme,KAAK,CAACthB,SAAN,CAAgBwU,kBAAhB,CAAmCrR,CADpD;YAETC,CAAC,EAAEme,MAAM,CAACjO,WAAP,CAAmBlQ,CAAnB,GAAuBke,KAAK,CAACthB,SAAN,CAAgBwU,kBAAhB,CAAmCpR;;;OANnE;IAUF,KAAKhB,MAAM,CAACqf,OAAZ;IACA,KAAKrf,MAAM,CAACsf,UAAZ;MACE,OAAO;QACL,GAAGJ,KADE;QAELthB,SAAS,EAAE;UACT,GAAGshB,KAAK,CAACthB,SADA;UAETG,MAAM,EAAE,IAFC;UAGTqU,kBAAkB,EAAE;YAACrR,CAAC,EAAE,CAAJ;YAAOC,CAAC,EAAE;WAHrB;UAITge,SAAS,EAAE;YAACje,CAAC,EAAE,CAAJ;YAAOC,CAAC,EAAE;;;OANzB;IAUF,KAAKhB,MAAM,CAACuf,iBAAZ;MAA+B;QAC7B,MAAM;UAACxY;YAAWoY,MAAlB;QACA,MAAM;UAACnhB;YAAM+I,OAAb;QACA,MAAM6Q,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BoB,KAAK,CAACxB,SAAN,CAAgB9F,UAA3C,CAAnB;QACAA,UAAU,CAACgB,GAAX,CAAe5a,EAAf,EAAmB+I,OAAnB;QAEA,OAAO;UACL,GAAGmY,KADE;UAELxB,SAAS,EAAE;YACT,GAAGwB,KAAK,CAACxB,SADA;YAET9F;;SAJJ;;IASF,KAAK5X,MAAM,CAACwf,oBAAZ;MAAkC;QAChC,MAAM;UAACxhB,EAAD;UAAKqO,GAAL;UAAUqJ;YAAYyJ,MAA5B;QACA,MAAMpY,OAAO,GAAGmY,KAAK,CAACxB,SAAN,CAAgB9F,UAAhB,CAA2B1U,GAA3B,CAA+BlF,EAA/B,CAAhB;QAEA,IAAI,CAAC+I,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;UACnC,OAAO6S,KAAP;;QAGF,MAAMtH,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BoB,KAAK,CAACxB,SAAN,CAAgB9F,UAA3C,CAAnB;QACAA,UAAU,CAACgB,GAAX,CAAe5a,EAAf,EAAmB;UACjB,GAAG+I,OADc;UAEjB2O;SAFF;QAKA,OAAO;UACL,GAAGwJ,KADE;UAELxB,SAAS,EAAE;YACT,GAAGwB,KAAK,CAACxB,SADA;YAET9F;;SAJJ;;IASF,KAAK5X,MAAM,CAACyf,mBAAZ;MAAiC;QAC/B,MAAM;UAACzhB,EAAD;UAAKqO;YAAO8S,MAAlB;QACA,MAAMpY,OAAO,GAAGmY,KAAK,CAACxB,SAAN,CAAgB9F,UAAhB,CAA2B1U,GAA3B,CAA+BlF,EAA/B,CAAhB;QAEA,IAAI,CAAC+I,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;UACnC,OAAO6S,KAAP;;QAGF,MAAMtH,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BoB,KAAK,CAACxB,SAAN,CAAgB9F,UAA3C,CAAnB;QACAA,UAAU,CAACza,MAAX,CAAkBa,EAAlB;QAEA,OAAO;UACL,GAAGkhB,KADE;UAELxB,SAAS,EAAE;YACT,GAAGwB,KAAK,CAACxB,SADA;YAET9F;;SAJJ;;IASF;MAAS;QACP,OAAOsH,KAAP;;;AAGL;SCzGeQ,aAAAriB,IAAA;MAAa;IAACqY;;EAC5B,MAAM;IAAC3X,MAAD;IAASugB,cAAT;IAAyBvH;MAAkBta,UAAU,CAACmiB,eAAD,CAA3D;EACA,MAAMe,sBAAsB,GAAGjJ,WAAW,CAAC4H,cAAD,CAA1C;EACA,MAAMsB,gBAAgB,GAAGlJ,WAAW,CAAC3Y,MAAD,oBAACA,MAAM,CAAEC,EAAT,CAApC;;EAGAtB,SAAS,CAAC;IACR,IAAIgZ,QAAJ,EAAc;MACZ;;IAGF,IAAI,CAAC4I,cAAD,IAAmBqB,sBAAnB,IAA6CC,gBAAgB,IAAI,IAArE,EAA2E;MACzE,IAAI,CAAC9P,eAAe,CAAC6P,sBAAD,CAApB,EAA8C;QAC5C;;MAGF,IAAIhW,QAAQ,CAACkW,aAAT,KAA2BF,sBAAsB,CAACxb,MAAtD,EAA8D;;QAE5D;;MAGF,MAAM6S,aAAa,GAAGD,cAAc,CAAC7T,GAAf,CAAmB0c,gBAAnB,CAAtB;MAEA,IAAI,CAAC5I,aAAL,EAAoB;QAClB;;MAGF,MAAM;QAACrF,aAAD;QAAgBnK;UAAQwP,aAA9B;MAEA,IAAI,CAACrF,aAAa,CAAC9B,OAAf,IAA0B,CAACrI,IAAI,CAACqI,OAApC,EAA6C;QAC3C;;MAGFiQ,qBAAqB,CAAC;QACpB,KAAK,MAAM/Y,OAAX,IAAsB,CAAC4K,aAAa,CAAC9B,OAAf,EAAwBrI,IAAI,CAACqI,OAA7B,CAAtB,EAA6D;UAC3D,IAAI,CAAC9I,OAAL,EAAc;YACZ;;UAGF,MAAMgZ,aAAa,GAAGC,sBAAsB,CAACjZ,OAAD,CAA5C;UAEA,IAAIgZ,aAAJ,EAAmB;YACjBA,aAAa,CAACE,KAAd;YACA;;;OAVe,CAArB;;GA3BK,EA0CN,CACD3B,cADC,EAED5I,QAFC,EAGDqB,cAHC,EAID6I,gBAJC,EAKDD,sBALC,CA1CM,CAAT;EAkDA,OAAO,IAAP;AACD;SClEeO,eACdC,SAAA,EAAA9iB,IAAA;MACA;IAAC6H,SAAD;IAAY,GAAGkb;;EAEf,OAAOD,SAAS,QAAT,IAAAA,SAAS,CAAE1d,MAAX,GACH0d,SAAS,CAACzc,MAAV,CAA4B,CAACC,WAAD,EAAc8B,QAAd;IAC1B,OAAOA,QAAQ,CAAC;MACdP,SAAS,EAAEvB,WADG;MAEd,GAAGyc;KAFU,CAAf;GADF,EAKGlb,SALH,CADG,GAOHA,SAPJ;AAQD;SCVemb,0BACdtI,MAAA;EAEA,OAAO3Y,OAAO,CACZ,OAAO;IACLxB,SAAS,EAAE;MACT,GAAG6f,6BAA6B,CAAC7f,SADxB;MAET,IAAGma,MAAH,oBAAGA,MAAM,CAAEna,SAAX;KAHG;IAKL8f,SAAS,EAAE;MACT,GAAGD,6BAA6B,CAACC,SADxB;MAET,IAAG3F,MAAH,oBAAGA,MAAM,CAAE2F,SAAX;KAPG;IASLG,WAAW,EAAE;MACX,GAAGJ,6BAA6B,CAACI,WADtB;MAEX,IAAG9F,MAAH,oBAAGA,MAAM,CAAE8F,WAAX;;GAXJ,CADY;EAAA;EAgBZ,CAAC9F,MAAD,oBAACA,MAAM,CAAEna,SAAT,EAAoBma,MAApB,oBAAoBA,MAAM,CAAE2F,SAA5B,EAAuC3F,MAAvC,oBAAuCA,MAAM,CAAE8F,WAA/C,CAhBY,CAAd;AAkBD;SCXeyC,iCAAAjjB,IAAA;MAAiC;IAC/CsS,UAD+C;IAE/ChE,OAF+C;IAG/CqP,WAH+C;IAI/CjD,MAAM,GAAG;;EAET,MAAMwI,WAAW,GAAGxK,MAAM,CAAC,KAAD,CAA1B;EACA,MAAM;IAAChV,CAAD;IAAIC;MAAK,OAAO+W,MAAP,KAAkB,SAAlB,GAA8B;IAAChX,CAAC,EAAEgX,MAAJ;IAAY/W,CAAC,EAAE+W;GAA7C,GAAuDA,MAAtE;EAEA2C,yBAAyB,CAAC;IACxB,MAAMhF,QAAQ,GAAG,CAAC3U,CAAD,IAAM,CAACC,CAAxB;IAEA,IAAI0U,QAAQ,IAAI,CAAC/F,UAAjB,EAA6B;MAC3B4Q,WAAW,CAAC1Q,OAAZ,GAAsB,KAAtB;MACA;;IAGF,IAAI0Q,WAAW,CAAC1Q,OAAZ,IAAuB,CAACmL,WAA5B,EAAyC;;;MAGvC;;;IAIF,MAAMxT,IAAI,GAAGmI,UAAH,oBAAGA,UAAU,CAAEnI,IAAZ,CAAiBqI,OAA9B;IAEA,IAAI,CAACrI,IAAD,IAASA,IAAI,CAAC4S,WAAL,KAAqB,KAAlC,EAAyC;;;MAGvC;;IAGF,MAAM5Y,IAAI,GAAGmK,OAAO,CAACnE,IAAD,CAApB;IACA,MAAMgZ,SAAS,GAAGjb,YAAY,CAAC/D,IAAD,EAAOwZ,WAAP,CAA9B;IAEA,IAAI,CAACja,CAAL,EAAQ;MACNyf,SAAS,CAACzf,CAAV,GAAc,CAAd;;IAGF,IAAI,CAACC,CAAL,EAAQ;MACNwf,SAAS,CAACxf,CAAV,GAAc,CAAd;;;IAIFuf,WAAW,CAAC1Q,OAAZ,GAAsB,IAAtB;IAEA,IAAIzO,IAAI,CAAC+J,GAAL,CAASqV,SAAS,CAACzf,CAAnB,IAAwB,CAAxB,IAA6BK,IAAI,CAAC+J,GAAL,CAASqV,SAAS,CAACxf,CAAnB,IAAwB,CAAzD,EAA4D;MAC1D,MAAM2H,uBAAuB,GAAGD,0BAA0B,CAAClB,IAAD,CAA1D;MAEA,IAAImB,uBAAJ,EAA6B;QAC3BA,uBAAuB,CAACqI,QAAxB,CAAiC;UAC/BlP,GAAG,EAAE0e,SAAS,CAACxf,CADgB;UAE/BY,IAAI,EAAE4e,SAAS,CAACzf;SAFlB;;;GAzCmB,EA+CtB,CAAC4O,UAAD,EAAa5O,CAAb,EAAgBC,CAAhB,EAAmBga,WAAnB,EAAgCrP,OAAhC,CA/CsB,CAAzB;AAgDD;ACoDM,MAAM8U,sBAAsB,gBAAGpkB,aAAa,CAAY;EAC7D,GAAGuE,kBAD0D;EAE7DyE,MAAM,EAAE,CAFqD;EAG7DC,MAAM,EAAE;AAHqD,CAAZ,CAA5C;AAMP,IAAKob,MAAL;AAAA,WAAKA,MAAA;EACHA,MAAA,CAAAA,MAAA;EACAA,MAAA,CAAAA,MAAA;EACAA,MAAA,CAAAA,MAAA;AACD,CAJD,EAAKA,MAAM,KAANA,MAAM,MAAX;AAMA,MAAaC,UAAU,gBAAGC,IAAI,CAAC,SAASD,UAATA,CAAAtjB,IAAA;;MAAoB;IACjDW,EADiD;IAEjD6iB,aAFiD;IAGjD1K,UAAU,GAAG,IAHoC;IAIjD6G,QAJiD;IAKjD1c,OAAO,GAAGid,cALuC;IAMjDuD,kBAAkB,GAAGlc,gBAN4B;IAOjDmc,SAPiD;IAQjDZ,SARiD;IASjD,GAAGrR;;EAEH,MAAMkS,KAAK,GAAGC,UAAU,CAAChC,OAAD,EAAUrQ,SAAV,EAAqBkQ,eAArB,CAAxB;EACA,MAAM,CAACI,KAAD,EAAQ9hB,QAAR,IAAoB4jB,KAA1B;EACA,MAAM,CAACE,oBAAD,EAAuBC,uBAAvB,IACJtkB,qBAAqB,EADvB;EAEA,MAAM,CAACukB,MAAD,EAASC,SAAT,IAAsBtkB,QAAQ,CAAS2jB,MAAM,CAACY,aAAhB,CAApC;EACA,MAAMC,aAAa,GAAGH,MAAM,KAAKV,MAAM,CAACc,WAAxC;EACA,MAAM;IACJ5jB,SAAS,EAAE;MAACG,MAAM,EAAE0jB,QAAT;MAAmB1C,KAAK,EAAEhI,cAA1B;MAA0CiI;KADjD;IAEJtB,SAAS,EAAE;MAAC9F,UAAU,EAAE7U;;MACtBmc,KAHJ;EAIA,MAAM1X,IAAI,GAAGia,QAAQ,IAAI,IAAZ,GAAmB1K,cAAc,CAAC7T,GAAf,CAAmBue,QAAnB,CAAnB,GAAkD,IAA/D;EACA,MAAMC,WAAW,GAAG3L,MAAM,CAA4B;IACpD4L,OAAO,EAAE,IAD2C;IAEpDC,UAAU,EAAE;GAFY,CAA1B;EAIA,MAAM7jB,MAAM,GAAGqB,OAAO,CACpB;IAAA,IAAAyiB,UAAA;IAAA,OACEJ,QAAQ,IAAI,IAAZ,GACI;MACEzjB,EAAE,EAAEyjB,QADN;;MAGExf,IAAI,GAAA4f,UAAA,GAAEra,IAAF,oBAAEA,IAAI,CAAEvF,IAAR,YAAA4f,UAAA,GAAgBrE,WAHtB;MAIEhc,IAAI,EAAEkgB;KALZ,GAOI,IARN;GADoB,EAUpB,CAACD,QAAD,EAAWja,IAAX,CAVoB,CAAtB;EAYA,MAAMsa,SAAS,GAAG/L,MAAM,CAA0B,IAA1B,CAAxB;EACA,MAAM,CAACgM,YAAD,EAAeC,eAAf,IAAkCjlB,QAAQ,CAAwB,IAAxB,CAAhD;EACA,MAAM,CAACuhB,cAAD,EAAiB2D,iBAAjB,IAAsCllB,QAAQ,CAAe,IAAf,CAApD;EACA,MAAMmlB,WAAW,GAAG3J,cAAc,CAACzJ,KAAD,EAAQjO,MAAM,CAACmb,MAAP,CAAclN,KAAd,CAAR,CAAlC;EACA,MAAMqT,sBAAsB,GAAGljB,WAAW,mBAAmBjB,EAAnB,CAA1C;EACA,MAAMokB,0BAA0B,GAAGhjB,OAAO,CACxC,MAAM2D,mBAAmB,CAACkb,UAApB,EADkC,EAExC,CAAClb,mBAAD,CAFwC,CAA1C;EAIA,MAAM0b,sBAAsB,GAAG4B,yBAAyB,CAACU,SAAD,CAAxD;EACA,MAAM;IAACje,cAAD;IAAiB0V,0BAAjB;IAA6CK;MACjDlB,qBAAqB,CAACyK,0BAAD,EAA6B;IAChDvK,QAAQ,EAAE0J,aADsC;IAEhDzJ,YAAY,EAAE,CAACkH,SAAS,CAACje,CAAX,EAAcie,SAAS,CAAChe,CAAxB,CAFkC;IAGhD+W,MAAM,EAAE0G,sBAAsB,CAACf;GAHZ,CADvB;EAMA,MAAM/N,UAAU,GAAGmH,aAAa,CAACC,cAAD,EAAiB0K,QAAjB,CAAhC;EACA,MAAMY,qBAAqB,GAAGjjB,OAAO,CACnC,MAAOkf,cAAc,GAAG5c,mBAAmB,CAAC4c,cAAD,CAAtB,GAAyC,IAD3B,EAEnC,CAACA,cAAD,CAFmC,CAArC;EAIA,MAAMgE,iBAAiB,GAAGC,sBAAsB,EAAhD;EACA,MAAMC,qBAAqB,GAAGtJ,cAAc,CAC1CvJ,UAD0C,EAE1C8O,sBAAsB,CAAC7gB,SAAvB,CAAiC+N,OAFS,CAA5C;EAKA2U,gCAAgC,CAAC;IAC/B3Q,UAAU,EAAE8R,QAAQ,IAAI,IAAZ,GAAmB1K,cAAc,CAAC7T,GAAf,CAAmBue,QAAnB,CAAnB,GAAkD,IAD/B;IAE/B1J,MAAM,EAAEuK,iBAAiB,CAACG,uBAFK;IAG/BzH,WAAW,EAAEwH,qBAHkB;IAI/B7W,OAAO,EAAE8S,sBAAsB,CAAC7gB,SAAvB,CAAiC+N;GAJZ,CAAhC;EAOA,MAAM4S,cAAc,GAAGxE,OAAO,CAC5BpK,UAD4B,EAE5B8O,sBAAsB,CAAC7gB,SAAvB,CAAiC+N,OAFL,EAG5B6W,qBAH4B,CAA9B;EAKA,MAAMhE,iBAAiB,GAAGzE,OAAO,CAC/BpK,UAAU,GAAGA,UAAU,CAAC+S,aAAd,GAA8B,IADT,CAAjC;EAGA,MAAMC,aAAa,GAAG5M,MAAM,CAAgB;IAC1CuI,cAAc,EAAE,IAD0B;IAE1CvgB,MAAM,EAAE,IAFkC;IAG1C4R,UAH0C;IAI1C9M,aAAa,EAAE,IAJ2B;IAK1CN,UAAU,EAAE,IAL8B;IAM1CO,cAN0C;IAO1CiU,cAP0C;IAQ1C6L,YAAY,EAAE,IAR4B;IAS1CC,gBAAgB,EAAE,IATwB;IAU1C9f,mBAV0C;IAW1C5E,IAAI,EAAE,IAXoC;IAY1CoN,mBAAmB,EAAE,EAZqB;IAa1CuX,uBAAuB,EAAE;GAbC,CAA5B;EAeA,MAAMC,QAAQ,GAAGhgB,mBAAmB,CAACmb,UAApB,EAAA8E,qBAAA,GACfL,aAAa,CAAC9S,OAAd,CAAsB1R,IADP,qBACf6kB,qBAAA,CAA4BhlB,EADb,CAAjB;EAGA,MAAM6f,WAAW,GAAGX,uBAAuB,CAAC;IAC1CvR,OAAO,EAAE8S,sBAAsB,CAACZ,WAAvB,CAAmClS;GADH,CAA3C;;EAKA,MAAMiX,YAAY,IAAAK,qBAAA,GAAGpF,WAAW,CAACT,OAAZ,CAAoBvN,OAAvB,YAAAoT,qBAAA,GAAkCtT,UAApD;EACA,MAAMkT,gBAAgB,GAAGtB,aAAa,IAAA2B,iBAAA,GAClCrF,WAAW,CAACrc,IADsB,YAAA0hB,iBAAA,GACd3E,cADc,GAElC,IAFJ;EAGA,MAAM4E,eAAe,GAAGrR,OAAO,CAC7B+L,WAAW,CAACT,OAAZ,CAAoBvN,OAApB,IAA+BgO,WAAW,CAACrc,IADd,CAA/B;;;EAKA,MAAM4hB,aAAa,GAAGrI,YAAY,CAACoI,eAAe,GAAG,IAAH,GAAU5E,cAA1B,CAAlC;;EAGA,MAAM5B,UAAU,GAAGJ,aAAa,CAC9BqG,YAAY,GAAG3b,SAAS,CAAC2b,YAAD,CAAZ,GAA6B,IADX,CAAhC;;EAKA,MAAMrX,mBAAmB,GAAG2P,sBAAsB,CAChDqG,aAAa,GAAGwB,QAAH,WAAGA,QAAH,GAAepT,UAAf,GAA4B,IADO,CAAlD;EAGA,MAAM4F,uBAAuB,GAAGkH,QAAQ,CAAClR,mBAAD,CAAxC;;EAGA,MAAM8X,iBAAiB,GAAGnD,cAAc,CAACC,SAAD,EAAY;IAClDjb,SAAS,EAAE;MACTnE,CAAC,EAAEie,SAAS,CAACje,CAAV,GAAcqiB,aAAa,CAACriB,CADtB;MAETC,CAAC,EAAEge,SAAS,CAAChe,CAAV,GAAcoiB,aAAa,CAACpiB,CAFtB;MAGTqE,MAAM,EAAE,CAHC;MAITC,MAAM,EAAE;KALwC;IAOlDgZ,cAPkD;IAQlDvgB,MARkD;IASlDwgB,cATkD;IAUlDC,iBAVkD;IAWlDqE,gBAXkD;IAYlD1kB,IAAI,EAAEwkB,aAAa,CAAC9S,OAAd,CAAsB1R,IAZsB;IAalDmlB,eAAe,EAAEzF,WAAW,CAACrc,IAbqB;IAclD+J,mBAdkD;IAelDgK,uBAfkD;IAgBlDoH;GAhBsC,CAAxC;EAmBA,MAAM3X,kBAAkB,GAAGqd,qBAAqB,GAC5CnlB,GAAG,CAACmlB,qBAAD,EAAwBrD,SAAxB,CADyC,GAE5C,IAFJ;EAIA,MAAM/S,aAAa,GAAGoP,gBAAgB,CAAC9P,mBAAD,CAAtC;;EAEA,MAAMgY,gBAAgB,GAAGtH,qBAAqB,CAAChQ,aAAD,CAA9C;;EAEA,MAAMuX,qBAAqB,GAAGvH,qBAAqB,CAAChQ,aAAD,EAAgB,CACjEsS,cADiE,CAAhB,CAAnD;EAIA,MAAMuE,uBAAuB,GAAG5lB,GAAG,CAACmmB,iBAAD,EAAoBE,gBAApB,CAAnC;EAEA,MAAM1gB,aAAa,GAAGggB,gBAAgB,GAClC/c,eAAe,CAAC+c,gBAAD,EAAmBQ,iBAAnB,CADmB,GAElC,IAFJ;EAIA,MAAM9gB,UAAU,GACdxE,MAAM,IAAI8E,aAAV,GACIie,kBAAkB,CAAC;IACjB/iB,MADiB;IAEjB8E,aAFiB;IAGjBC,cAHiB;IAIjBC,mBAAmB,EAAEqf,0BAJJ;IAKjBpd;GALgB,CADtB,GAQI,IATN;EAUA,MAAMye,MAAM,GAAGnhB,iBAAiB,CAACC,UAAD,EAAa,IAAb,CAAhC;EACA,MAAM,CAACpE,IAAD,EAAOulB,OAAP,IAAkB3mB,QAAQ,CAAc,IAAd,CAAhC;;;EAIA,MAAM4mB,gBAAgB,GAAGR,eAAe,GACpCE,iBADoC,GAEpCnmB,GAAG,CAACmmB,iBAAD,EAAoBG,qBAApB,CAFP;EAIA,MAAMte,SAAS,GAAGD,WAAW,CAC3B0e,gBAD2B,GAAAC,UAAA,GAE3BzlB,IAF2B,oBAE3BA,IAAI,CAAEqD,IAFqB,YAAAoiB,UAAA,GAEb,IAFa,EAG3BrF,cAH2B,CAA7B;EAMA,MAAMsF,eAAe,GAAG9N,MAAM,CAAwB,IAAxB,CAA9B;EACA,MAAM+N,iBAAiB,GAAG7mB,WAAW,CACnC,CACEM,KADF,EAAAW,KAAA;QAEE;MAACiC,MAAM,EAAEiX,MAAT;MAAiBhX;;IAEjB,IAAI0hB,SAAS,CAACjS,OAAV,IAAqB,IAAzB,EAA+B;MAC7B;;IAGF,MAAMF,UAAU,GAAGoH,cAAc,CAAC7T,GAAf,CAAmB4e,SAAS,CAACjS,OAA7B,CAAnB;IAEA,IAAI,CAACF,UAAL,EAAiB;MACf;;IAGF,MAAM2O,cAAc,GAAG/gB,KAAK,CAACkU,WAA7B;IAEA,MAAMsS,cAAc,GAAG,IAAI3M,MAAJ,CAAW;MAChCrZ,MAAM,EAAE+jB,SAAS,CAACjS,OADc;MAEhCF,UAFgC;MAGhCpS,KAAK,EAAE+gB,cAHyB;MAIhCle,OAJgC;;;MAOhC2P,OAAO,EAAE4S,aAPuB;MAQhC9O,OAAOA,CAAC7V,EAAD;QACL,MAAMgZ,aAAa,GAAGD,cAAc,CAAC7T,GAAf,CAAmBlF,EAAnB,CAAtB;QAEA,IAAI,CAACgZ,aAAL,EAAoB;UAClB;;QAGF,MAAM;UAACgN;YAAe9B,WAAW,CAACrS,OAAlC;QACA,MAAMtS,KAAK,GAAmB;UAACS;SAA/B;QACAgmB,WAAW,QAAX,YAAAA,WAAW,CAAGzmB,KAAH,CAAX;QACA2jB,oBAAoB,CAAC;UAAC5jB,IAAI,EAAE,aAAP;UAAsBC;SAAvB,CAApB;OAlB8B;MAoBhC8V,SAASA,CAACrV,EAAD,EAAK6T,UAAL,EAAiBO,kBAAjB,EAAqCgB,MAArC;QACP,MAAM4D,aAAa,GAAGD,cAAc,CAAC7T,GAAf,CAAmBlF,EAAnB,CAAtB;QAEA,IAAI,CAACgZ,aAAL,EAAoB;UAClB;;QAGF,MAAM;UAACiN;YAAiB/B,WAAW,CAACrS,OAApC;QACA,MAAMtS,KAAK,GAAqB;UAC9BS,EAD8B;UAE9B6T,UAF8B;UAG9BO,kBAH8B;UAI9BgB;SAJF;QAOA6Q,aAAa,QAAb,YAAAA,aAAa,CAAG1mB,KAAH,CAAb;QACA2jB,oBAAoB,CAAC;UAAC5jB,IAAI,EAAE,eAAP;UAAwBC;SAAzB,CAApB;OApC8B;MAsChCqS,OAAOA,CAACwC,kBAAD;QACL,MAAMpU,EAAE,GAAG8jB,SAAS,CAACjS,OAArB;QAEA,IAAI7R,EAAE,IAAI,IAAV,EAAgB;UACd;;QAGF,MAAMgZ,aAAa,GAAGD,cAAc,CAAC7T,GAAf,CAAmBlF,EAAnB,CAAtB;QAEA,IAAI,CAACgZ,aAAL,EAAoB;UAClB;;QAGF,MAAM;UAAClZ;YAAeokB,WAAW,CAACrS,OAAlC;QACA,MAAMtS,KAAK,GAAmB;UAC5B+gB,cAD4B;UAE5BvgB,MAAM,EAAE;YAACC,EAAD;YAAKiE,IAAI,EAAE+U,aAAa,CAAC/U,IAAzB;YAA+BT,IAAI,EAAEkgB;;SAF/C;QAKAwC,uBAAuB,CAAC;UACtBpmB,WAAW,QAAX,YAAAA,WAAW,CAAGP,KAAH,CAAX;UACA8jB,SAAS,CAACX,MAAM,CAACyD,YAAR,CAAT;UACA/mB,QAAQ,CAAC;YACPE,IAAI,EAAE0C,MAAM,CAAC+S,SADN;YAEPX,kBAFO;YAGPrU,MAAM,EAAEC;WAHF,CAAR;UAKAkjB,oBAAoB,CAAC;YAAC5jB,IAAI,EAAE,aAAP;YAAsBC;WAAvB,CAApB;UACAykB,eAAe,CAAC6B,eAAe,CAAChU,OAAjB,CAAf;UACAoS,iBAAiB,CAAC3D,cAAD,CAAjB;SAVqB,CAAvB;OAzD8B;MAsEhCnN,MAAMA,CAACD,WAAD;QACJ9T,QAAQ,CAAC;UACPE,IAAI,EAAE0C,MAAM,CAACof,QADN;UAEPlO;SAFM,CAAR;OAvE8B;MA4EhCE,KAAK,EAAEgT,aAAa,CAACpkB,MAAM,CAACqf,OAAR,CA5EY;MA6EhC/N,QAAQ,EAAE8S,aAAa,CAACpkB,MAAM,CAACsf,UAAR;KA7EF,CAAvB;IAgFAuE,eAAe,CAAChU,OAAhB,GAA0BkU,cAA1B;IAEA,SAASK,aAATA,CAAuB9mB,IAAvB;MACE,OAAO,eAAeyP,OAAfA,CAAA;QACL,MAAM;UAAChP,MAAD;UAASwE,UAAT;UAAqBpE,IAArB;UAA2B2kB;YAC/BH,aAAa,CAAC9S,OADhB;QAEA,IAAItS,KAAK,GAAwB,IAAjC;QAEA,IAAIQ,MAAM,IAAI+kB,uBAAd,EAAuC;UACrC,MAAM;YAACuB;cAAcnC,WAAW,CAACrS,OAAjC;UAEAtS,KAAK,GAAG;YACN+gB,cADM;YAENvgB,MAAM,EAAEA,MAFF;YAGNwE,UAHM;YAIN8K,KAAK,EAAEyV,uBAJD;YAKN3kB;WALF;UAQA,IAAIb,IAAI,KAAK0C,MAAM,CAACqf,OAAhB,IAA2B,OAAOgF,UAAP,KAAsB,UAArD,EAAiE;YAC/D,MAAMC,YAAY,GAAG,MAAMC,OAAO,CAACC,OAAR,CAAgBH,UAAU,CAAC9mB,KAAD,CAA1B,CAA3B;YAEA,IAAI+mB,YAAJ,EAAkB;cAChBhnB,IAAI,GAAG0C,MAAM,CAACsf,UAAd;;;;QAKNwC,SAAS,CAACjS,OAAV,GAAoB,IAApB;QAEAqU,uBAAuB,CAAC;UACtB9mB,QAAQ,CAAC;YAACE;WAAF,CAAR;UACA+jB,SAAS,CAACX,MAAM,CAACY,aAAR,CAAT;UACAoC,OAAO,CAAC,IAAD,CAAP;UACA1B,eAAe,CAAC,IAAD,CAAf;UACAC,iBAAiB,CAAC,IAAD,CAAjB;UACA4B,eAAe,CAAChU,OAAhB,GAA0B,IAA1B;UAEA,MAAM/C,SAAS,GACbxP,IAAI,KAAK0C,MAAM,CAACqf,OAAhB,GAA0B,WAA1B,GAAwC,cAD1C;UAGA,IAAI9hB,KAAJ,EAAW;YACT,MAAMwP,OAAO,GAAGmV,WAAW,CAACrS,OAAZ,CAAoB/C,SAApB,CAAhB;YAEAC,OAAO,QAAP,YAAAA,OAAO,CAAGxP,KAAH,CAAP;YACA2jB,oBAAoB,CAAC;cAAC5jB,IAAI,EAAEwP,SAAP;cAAkBvP;aAAnB,CAApB;;SAfmB,CAAvB;OA3BF;;GApG+B;EAAA;EAqJnC,CAACwZ,cAAD,CArJmC,CAArC;EAwJA,MAAM0N,iCAAiC,GAAGxnB,WAAW,CACnD,CACE8P,OADF,EAEE5M,MAFF;IAIE,OAAO,CAAC5C,KAAD,EAAQQ,MAAR;MACL,MAAM0T,WAAW,GAAGlU,KAAK,CAACkU,WAA1B;MACA,MAAMiT,mBAAmB,GAAG3N,cAAc,CAAC7T,GAAf,CAAmBnF,MAAnB,CAA5B;MAEA;MAAA;MAEE+jB,SAAS,CAACjS,OAAV,KAAsB,IAAtB;MAAA;MAEA,CAAC6U,mBAFD;MAAA;MAIAjT,WAAW,CAACkT,MAJZ,IAKAlT,WAAW,CAACmT,gBAPd,EAQE;QACA;;MAGF,MAAMC,iBAAiB,GAAG;QACxB9mB,MAAM,EAAE2mB;OADV;MAGA,MAAMI,cAAc,GAAG/X,OAAO,CAC5BxP,KAD4B,EAE5B4C,MAAM,CAACC,OAFqB,EAG5BykB,iBAH4B,CAA9B;MAMA,IAAIC,cAAc,KAAK,IAAvB,EAA6B;QAC3BrT,WAAW,CAACkT,MAAZ,GAAqB;UACnBI,UAAU,EAAE5kB,MAAM,CAACA;SADrB;QAIA2hB,SAAS,CAACjS,OAAV,GAAoB9R,MAApB;QACA+lB,iBAAiB,CAACvmB,KAAD,EAAQ4C,MAAR,CAAjB;;KA/BJ;GALiD,EAwCnD,CAAC4W,cAAD,EAAiB+M,iBAAjB,CAxCmD,CAArD;EA2CA,MAAMvS,UAAU,GAAG2F,oBAAoB,CACrC5W,OADqC,EAErCmkB,iCAFqC,CAAvC;EAKArI,cAAc,CAAC9b,OAAD,CAAd;EAEAoa,yBAAyB,CAAC;IACxB,IAAI6D,cAAc,IAAI6C,MAAM,KAAKV,MAAM,CAACyD,YAAxC,EAAsD;MACpD9C,SAAS,CAACX,MAAM,CAACc,WAAR,CAAT;;GAFqB,EAItB,CAACjD,cAAD,EAAiB6C,MAAjB,CAJsB,CAAzB;EAMA1kB,SAAS,CACP;IACE,MAAM;MAAC2C;QAAc6iB,WAAW,CAACrS,OAAjC;IACA,MAAM;MAAC9R,MAAD;MAASugB,cAAT;MAAyB/b,UAAzB;MAAqCpE;QAAQwkB,aAAa,CAAC9S,OAAjE;IAEA,IAAI,CAAC9R,MAAD,IAAW,CAACugB,cAAhB,EAAgC;MAC9B;;IAGF,MAAM/gB,KAAK,GAAkB;MAC3BQ,MAD2B;MAE3BugB,cAF2B;MAG3B/b,UAH2B;MAI3B8K,KAAK,EAAE;QACLtM,CAAC,EAAE+hB,uBAAuB,CAAC/hB,CADtB;QAELC,CAAC,EAAE8hB,uBAAuB,CAAC9hB;OANF;MAQ3B7C;KARF;IAWA+lB,uBAAuB,CAAC;MACtB7kB,UAAU,QAAV,YAAAA,UAAU,CAAG9B,KAAH,CAAV;MACA2jB,oBAAoB,CAAC;QAAC5jB,IAAI,EAAE,YAAP;QAAqBC;OAAtB,CAApB;KAFqB,CAAvB;GApBK;EAAA;EA0BP,CAACulB,uBAAuB,CAAC/hB,CAAzB,EAA4B+hB,uBAAuB,CAAC9hB,CAApD,CA1BO,CAAT;EA6BAtE,SAAS,CACP;IACE,MAAM;MACJqB,MADI;MAEJugB,cAFI;MAGJ/b,UAHI;MAIJQ,mBAJI;MAKJ+f;QACEH,aAAa,CAAC9S,OANlB;IAQA,IACE,CAAC9R,MAAD,IACA+jB,SAAS,CAACjS,OAAV,IAAqB,IADrB,IAEA,CAACyO,cAFD,IAGA,CAACwE,uBAJH,EAKE;MACA;;IAGF,MAAM;MAAC7kB;QAAcikB,WAAW,CAACrS,OAAjC;IACA,MAAMmV,aAAa,GAAGjiB,mBAAmB,CAACG,GAApB,CAAwBugB,MAAxB,CAAtB;IACA,MAAMtlB,IAAI,GACR6mB,aAAa,IAAIA,aAAa,CAACxjB,IAAd,CAAmBqO,OAApC,GACI;MACE7R,EAAE,EAAEgnB,aAAa,CAAChnB,EADpB;MAEEwD,IAAI,EAAEwjB,aAAa,CAACxjB,IAAd,CAAmBqO,OAF3B;MAGE5N,IAAI,EAAE+iB,aAAa,CAAC/iB,IAHtB;MAIEyT,QAAQ,EAAEsP,aAAa,CAACtP;KAL9B,GAOI,IARN;IASA,MAAMnY,KAAK,GAAkB;MAC3BQ,MAD2B;MAE3BugB,cAF2B;MAG3B/b,UAH2B;MAI3B8K,KAAK,EAAE;QACLtM,CAAC,EAAE+hB,uBAAuB,CAAC/hB,CADtB;QAELC,CAAC,EAAE8hB,uBAAuB,CAAC9hB;OANF;MAQ3B7C;KARF;IAWA+lB,uBAAuB,CAAC;MACtBR,OAAO,CAACvlB,IAAD,CAAP;MACAF,UAAU,QAAV,YAAAA,UAAU,CAAGV,KAAH,CAAV;MACA2jB,oBAAoB,CAAC;QAAC5jB,IAAI,EAAE,YAAP;QAAqBC;OAAtB,CAApB;KAHqB,CAAvB;GAzCK;EAAA;EAgDP,CAACkmB,MAAD,CAhDO,CAAT;EAmDA/I,yBAAyB,CAAC;IACxBiI,aAAa,CAAC9S,OAAd,GAAwB;MACtByO,cADsB;MAEtBvgB,MAFsB;MAGtB4R,UAHsB;MAItB9M,aAJsB;MAKtBN,UALsB;MAMtBO,cANsB;MAOtBiU,cAPsB;MAQtB6L,YARsB;MAStBC,gBATsB;MAUtB9f,mBAVsB;MAWtB5E,IAXsB;MAYtBoN,mBAZsB;MAatBuX;KAbF;IAgBApB,WAAW,CAAC7R,OAAZ,GAAsB;MACpB8R,OAAO,EAAEkB,gBADW;MAEpBjB,UAAU,EAAE/e;KAFd;GAjBuB,EAqBtB,CACD9E,MADC,EAED4R,UAFC,EAGDpN,UAHC,EAIDM,aAJC,EAKDkU,cALC,EAMD6L,YANC,EAODC,gBAPC,EAQD/f,cARC,EASDC,mBATC,EAUD5E,IAVC,EAWDoN,mBAXC,EAYDuX,uBAZC,CArBsB,CAAzB;EAoCA/N,eAAe,CAAC;IACd,GAAGuN,iBADW;IAEdjV,KAAK,EAAE2R,SAFO;IAGd9J,YAAY,EAAErS,aAHA;IAIdmC,kBAJc;IAKduG,mBALc;IAMdgK;GANa,CAAf;EASA,MAAM0P,aAAa,GAAG7lB,OAAO,CAAC;IAC5B,MAAM2Q,OAAO,GAA4B;MACvChS,MADuC;MAEvC4R,UAFuC;MAGvC4O,cAHuC;MAIvCD,cAJuC;MAKvC/b,UALuC;MAMvCic,iBANuC;MAOvCX,WAPuC;MAQvC9G,cARuC;MASvChU,mBATuC;MAUvCD,cAVuC;MAWvC3E,IAXuC;MAYvCqa,0BAZuC;MAavCjN,mBAbuC;MAcvCgK,uBAduC;MAevCkJ,sBAfuC;MAgBvC5F,kBAhBuC;MAiBvC8D;KAjBF;IAoBA,OAAO5M,OAAP;GArB2B,EAsB1B,CACDhS,MADC,EAED4R,UAFC,EAGD4O,cAHC,EAIDD,cAJC,EAKD/b,UALC,EAMDic,iBANC,EAODX,WAPC,EAQD9G,cARC,EASDhU,mBATC,EAUDD,cAVC,EAWD3E,IAXC,EAYDqa,0BAZC,EAaDjN,mBAbC,EAcDgK,uBAdC,EAeDkJ,sBAfC,EAgBD5F,kBAhBC,EAiBD8D,UAjBC,CAtB0B,CAA7B;EA0CA,MAAMuI,eAAe,GAAG9lB,OAAO,CAAC;IAC9B,MAAM2Q,OAAO,GAA8B;MACzCuO,cADyC;MAEzC/M,UAFyC;MAGzCxT,MAHyC;MAIzCwgB,cAJyC;MAKzCI,iBAAiB,EAAE;QACjB/gB,SAAS,EAAEukB;OAN4B;MAQzC/kB,QARyC;MASzC2Z,cATyC;MAUzC5Y,IAVyC;MAWzCqa;KAXF;IAcA,OAAOzI,OAAP;GAf6B,EAgB5B,CACDuO,cADC,EAED/M,UAFC,EAGDxT,MAHC,EAIDwgB,cAJC,EAKDnhB,QALC,EAMD+kB,sBANC,EAODpL,cAPC,EAQD5Y,IARC,EASDqa,0BATC,CAhB4B,CAA/B;EA4BA,OACE/Y,KAAA,CAAAC,aAAA,CAACtD,iBAAiB,CAAC+oB,QAAnB;IAA4BtlB,KAAK,EAAEshB;GAAnC,EACE1hB,KAAA,CAAAC,aAAA,CAACkf,eAAe,CAACuG,QAAjB;IAA0BtlB,KAAK,EAAEqlB;GAAjC,EACEzlB,KAAA,CAAAC,aAAA,CAACmf,aAAa,CAACsG,QAAf;IAAwBtlB,KAAK,EAAEolB;GAA/B,EACExlB,KAAA,CAAAC,aAAA,CAAC+gB,sBAAsB,CAAC0E,QAAxB;IAAiCtlB,KAAK,EAAEqF;GAAxC,EACG8X,QADH,CADF,CADF,EAMEvd,KAAA,CAAAC,aAAA,CAACggB,YAAD;IAAchK,QAAQ,EAAE,CAAAmL,aAAa,QAAb,YAAAA,aAAa,CAAEuE,YAAf,MAAgC;GAAxD,CANF,CADF,EASE3lB,KAAA,CAAAC,aAAA,CAAClB,aAAD;IAAA,GACMqiB,aAAA;IACJliB,uBAAuB,EAAEwjB;GAF3B,CATF,CADF;EAiBA,SAASI,sBAATA,CAAA;IACE,MAAM8C,8BAA8B,GAClC,CAAAtD,YAAY,QAAZ,YAAAA,YAAY,CAAEhT,iBAAd,MAAoC,KADtC;IAEA,MAAMuW,0BAA0B,GAC9B,OAAOnP,UAAP,KAAsB,QAAtB,GACIA,UAAU,CAAChB,OAAX,KAAuB,KAD3B,GAEIgB,UAAU,KAAK,KAHrB;IAIA,MAAMhB,OAAO,GACXoM,aAAa,IACb,CAAC8D,8BADD,IAEA,CAACC,0BAHH;IAKA,IAAI,OAAOnP,UAAP,KAAsB,QAA1B,EAAoC;MAClC,OAAO;QACL,GAAGA,UADE;QAELhB;OAFF;;IAMF,OAAO;MAACA;KAAR;;AAEH,CAtnB6B,CAAvB;ACrGP,MAAMoQ,WAAW,gBAAGlpB,aAAa,CAAM,IAAN,CAAjC;AAEA,MAAMmpB,WAAW,GAAG,QAApB;AAEA,MAAMC,SAAS,GAAG,WAAlB;AAEA,SAAgBC,aAAAroB,IAAA;MAAa;IAC3BW,EAD2B;IAE3BiE,IAF2B;IAG3ByT,QAAQ,GAAG,KAHgB;IAI3BiQ;;EAEA,MAAMtZ,GAAG,GAAGpN,WAAW,CAACwmB,SAAD,CAAvB;EACA,MAAM;IACJlU,UADI;IAEJ+M,cAFI;IAGJvgB,MAHI;IAIJwgB,cAJI;IAKJI,iBALI;IAMJ5H,cANI;IAOJ5Y;MACE1B,UAAU,CAACmiB,eAAD,CARd;EASA,MAAM;IACJgH,IAAI,GAAGJ,WADH;IAEJK,eAAe,GAAG,WAFd;IAGJC,QAAQ,GAAG;MACTH,UAJE,WAIFA,UAJE,GAIY,EAJlB;EAKA,MAAMI,UAAU,GAAG,CAAAhoB,MAAM,QAAN,YAAAA,MAAM,CAAEC,EAAR,MAAeA,EAAlC;EACA,MAAMkH,SAAS,GAAqBzI,UAAU,CAC5CspB,UAAU,GAAGtF,sBAAH,GAA4B8E,WADM,CAA9C;EAGA,MAAM,CAAC/d,IAAD,EAAOwe,UAAP,IAAqB1I,UAAU,EAArC;EACA,MAAM,CAAC3L,aAAD,EAAgBsU,mBAAhB,IAAuC3I,UAAU,EAAvD;EACA,MAAMxgB,SAAS,GAAGwf,qBAAqB,CAAC/K,UAAD,EAAavT,EAAb,CAAvC;EACA,MAAMkoB,OAAO,GAAG3N,cAAc,CAACtW,IAAD,CAA9B;EAEAyY,yBAAyB,CACvB;IACE3D,cAAc,CAAC6B,GAAf,CAAmB5a,EAAnB,EAAuB;MAACA,EAAD;MAAKqO,GAAL;MAAU7E,IAAV;MAAgBmK,aAAhB;MAA+B1P,IAAI,EAAEikB;KAA5D;IAEA,OAAO;MACL,MAAM1e,IAAI,GAAGuP,cAAc,CAAC7T,GAAf,CAAmBlF,EAAnB,CAAb;MAEA,IAAIwJ,IAAI,IAAIA,IAAI,CAAC6E,GAAL,KAAaA,GAAzB,EAA8B;QAC5B0K,cAAc,CAAC5Z,MAAf,CAAsBa,EAAtB;;KAJJ;GAJqB;EAAA;EAavB,CAAC+Y,cAAD,EAAiB/Y,EAAjB,CAbuB,CAAzB;EAgBA,MAAMmoB,kBAAkB,GAAwB/mB,OAAO,CACrD,OAAO;IACLwmB,IADK;IAELE,QAFK;IAGL,iBAAiBpQ,QAHZ;IAIL,gBAAgBqQ,UAAU,IAAIH,IAAI,KAAKJ,WAAvB,GAAqC,IAArC,GAA4C5W,SAJvD;IAKL,wBAAwBiX,eALnB;IAML,oBAAoBlH,iBAAiB,CAAC/gB;GANxC,CADqD,EASrD,CACE8X,QADF,EAEEkQ,IAFF,EAGEE,QAHF,EAIEC,UAJF,EAKEF,eALF,EAMElH,iBAAiB,CAAC/gB,SANpB,CATqD,CAAvD;EAmBA,OAAO;IACLG,MADK;IAELugB,cAFK;IAGLC,cAHK;IAILoH,UAAU,EAAEQ,kBAJP;IAKLJ,UALK;IAMLjpB,SAAS,EAAE4Y,QAAQ,GAAG9G,SAAH,GAAe9R,SAN7B;IAOL0K,IAPK;IAQLrJ,IARK;IASL6nB,UATK;IAULC,mBAVK;IAWL/gB;GAXF;AAaD;SCrHekhB,cAAA;EACd,OAAO3pB,UAAU,CAACoiB,aAAD,CAAjB;AACD;ACsBD,MAAMwH,WAAS,GAAG,WAAlB;AAEA,MAAMC,2BAA2B,GAAG;EAClCC,OAAO,EAAE;AADyB,CAApC;AAIA,SAAgBC,aAAAnpB,IAAA;MAAa;IAC3B4E,IAD2B;IAE3ByT,QAAQ,GAAG,KAFgB;IAG3B1X,EAH2B;IAI3ByoB;;EAEA,MAAMpa,GAAG,GAAGpN,WAAW,CAAConB,WAAD,CAAvB;EACA,MAAM;IAACtoB,MAAD;IAASX,QAAT;IAAmBe,IAAnB;IAAyBqa;MAC7B/b,UAAU,CAACmiB,eAAD,CADZ;EAEA,MAAM8H,QAAQ,GAAG3Q,MAAM,CAAC;IAACL;GAAF,CAAvB;EACA,MAAMiR,uBAAuB,GAAG5Q,MAAM,CAAC,KAAD,CAAtC;EACA,MAAMvU,IAAI,GAAGuU,MAAM,CAAoB,IAApB,CAAnB;EACA,MAAM6Q,UAAU,GAAG7Q,MAAM,CAAwB,IAAxB,CAAzB;EACA,MAAM;IACJL,QAAQ,EAAEmR,sBADN;IAEJC,qBAFI;IAGJP,OAAO,EAAEQ;MACP;IACF,GAAGT,2BADD;IAEF,GAAGG;GANL;EAQA,MAAMhO,GAAG,GAAGF,cAAc,CAACuO,qBAAD,WAACA,qBAAD,GAA0B9oB,EAA1B,CAA1B;EACA,MAAM2b,YAAY,GAAG1c,WAAW,CAC9B;IACE,IAAI,CAAC0pB,uBAAuB,CAAC9W,OAA7B,EAAsC;;;MAGpC8W,uBAAuB,CAAC9W,OAAxB,GAAkC,IAAlC;MACA;;IAGF,IAAI+W,UAAU,CAAC/W,OAAX,IAAsB,IAA1B,EAAgC;MAC9BsD,YAAY,CAACyT,UAAU,CAAC/W,OAAZ,CAAZ;;IAGF+W,UAAU,CAAC/W,OAAX,GAAqBJ,UAAU,CAAC;MAC9B+I,0BAA0B,CACxBjY,KAAK,CAACymB,OAAN,CAAcvO,GAAG,CAAC5I,OAAlB,IAA6B4I,GAAG,CAAC5I,OAAjC,GAA2C,CAAC4I,GAAG,CAAC5I,OAAL,CADnB,CAA1B;MAGA+W,UAAU,CAAC/W,OAAX,GAAqB,IAArB;KAJ6B,EAK5BkX,qBAL4B,CAA/B;GAb4B;EAAA;EAqB9B,CAACA,qBAAD,CArB8B,CAAhC;EAuBA,MAAMnN,cAAc,GAAGF,iBAAiB,CAAC;IACvCN,QAAQ,EAAEO,YAD6B;IAEvCjE,QAAQ,EAAEmR,sBAAsB,IAAI,CAAC9oB;GAFC,CAAxC;EAIA,MAAMof,gBAAgB,GAAGlgB,WAAW,CAClC,CAACgqB,UAAD,EAAiCC,eAAjC;IACE,IAAI,CAACtN,cAAL,EAAqB;MACnB;;IAGF,IAAIsN,eAAJ,EAAqB;MACnBtN,cAAc,CAACuN,SAAf,CAAyBD,eAAzB;MACAP,uBAAuB,CAAC9W,OAAxB,GAAkC,KAAlC;;IAGF,IAAIoX,UAAJ,EAAgB;MACdrN,cAAc,CAACe,OAAf,CAAuBsM,UAAvB;;GAZ8B,EAelC,CAACrN,cAAD,CAfkC,CAApC;EAiBA,MAAM,CAACwD,OAAD,EAAU4I,UAAV,IAAwB1I,UAAU,CAACH,gBAAD,CAAxC;EACA,MAAM+I,OAAO,GAAG3N,cAAc,CAACtW,IAAD,CAA9B;EAEAvF,SAAS,CAAC;IACR,IAAI,CAACkd,cAAD,IAAmB,CAACwD,OAAO,CAACvN,OAAhC,EAAyC;MACvC;;IAGF+J,cAAc,CAACH,UAAf;IACAkN,uBAAuB,CAAC9W,OAAxB,GAAkC,KAAlC;IACA+J,cAAc,CAACe,OAAf,CAAuByC,OAAO,CAACvN,OAA/B;GAPO,EAQN,CAACuN,OAAD,EAAUxD,cAAV,CARM,CAAT;EAUAld,SAAS,CACP;IACEU,QAAQ,CAAC;MACPE,IAAI,EAAE0C,MAAM,CAACuf,iBADN;MAEPxY,OAAO,EAAE;QACP/I,EADO;QAEPqO,GAFO;QAGPqJ,QAHO;QAIPlO,IAAI,EAAE4V,OAJC;QAKP5b,IALO;QAMPS,IAAI,EAAEikB;;KARF,CAAR;IAYA,OAAO,MACL9oB,QAAQ,CAAC;MACPE,IAAI,EAAE0C,MAAM,CAACyf,mBADN;MAEPpT,GAFO;MAGPrO;KAHM,CADV;GAdK;EAAA;EAsBP,CAACA,EAAD,CAtBO,CAAT;EAyBAtB,SAAS,CAAC;IACR,IAAIgZ,QAAQ,KAAKgR,QAAQ,CAAC7W,OAAT,CAAiB6F,QAAlC,EAA4C;MAC1CtY,QAAQ,CAAC;QACPE,IAAI,EAAE0C,MAAM,CAACwf,oBADN;QAEPxhB,EAFO;QAGPqO,GAHO;QAIPqJ;OAJM,CAAR;MAOAgR,QAAQ,CAAC7W,OAAT,CAAiB6F,QAAjB,GAA4BA,QAA5B;;GATK,EAWN,CAAC1X,EAAD,EAAKqO,GAAL,EAAUqJ,QAAV,EAAoBtY,QAApB,CAXM,CAAT;EAaA,OAAO;IACLW,MADK;IAELyD,IAFK;IAGL4lB,MAAM,EAAE,CAAAjpB,IAAI,QAAJ,YAAAA,IAAI,CAAEH,EAAN,MAAaA,EAHhB;IAILwJ,IAAI,EAAE4V,OAJD;IAKLjf,IALK;IAML6nB;GANF;AAQD;SC/IeqB,iBAAAhqB,IAAA;MAAiB;IAACiqB,SAAD;IAAYtK;;EAC3C,MAAM,CACJuK,cADI,EAEJC,iBAFI,IAGFzqB,QAAQ,CAA4B,IAA5B,CAHZ;EAIA,MAAM,CAACgK,OAAD,EAAU0gB,UAAV,IAAwB1qB,QAAQ,CAAqB,IAArB,CAAtC;EACA,MAAM2qB,gBAAgB,GAAGhR,WAAW,CAACsG,QAAD,CAApC;EAEA,IAAI,CAACA,QAAD,IAAa,CAACuK,cAAd,IAAgCG,gBAApC,EAAsD;IACpDF,iBAAiB,CAACE,gBAAD,CAAjB;;EAGFhN,yBAAyB,CAAC;IACxB,IAAI,CAAC3T,OAAL,EAAc;MACZ;;IAGF,MAAMsF,GAAG,GAAGkb,cAAH,oBAAGA,cAAc,CAAElb,GAA5B;IACA,MAAMrO,EAAE,GAAGupB,cAAH,oBAAGA,cAAc,CAAEzY,KAAhB,CAAsB9Q,EAAjC;IAEA,IAAIqO,GAAG,IAAI,IAAP,IAAerO,EAAE,IAAI,IAAzB,EAA+B;MAC7BwpB,iBAAiB,CAAC,IAAD,CAAjB;MACA;;IAGFjD,OAAO,CAACC,OAAR,CAAgB8C,SAAS,CAACtpB,EAAD,EAAK+I,OAAL,CAAzB,EAAwC4gB,IAAxC,CAA6C;MAC3CH,iBAAiB,CAAC,IAAD,CAAjB;KADF;GAbuB,EAgBtB,CAACF,SAAD,EAAYC,cAAZ,EAA4BxgB,OAA5B,CAhBsB,CAAzB;EAkBA,OACEtH,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAE,QAAA,QACGqd,QADH,EAEGuK,cAAc,GAAGK,YAAY,CAACL,cAAD,EAAiB;IAACM,GAAG,EAAEJ;GAAvB,CAAf,GAAqD,IAFtE,CADF;AAMD;ACzCD,MAAMK,gBAAgB,GAAc;EAClC/mB,CAAC,EAAE,CAD+B;EAElCC,CAAC,EAAE,CAF+B;EAGlCqE,MAAM,EAAE,CAH0B;EAIlCC,MAAM,EAAE;AAJ0B,CAApC;AAOA,SAAgByiB,yBAAA1qB,IAAA;MAAyB;IAAC2f;;EACxC,OACEvd,KAAA,CAAAC,aAAA,CAACkf,eAAe,CAACuG,QAAjB;IAA0BtlB,KAAK,EAAE6e;GAAjC,EACEjf,KAAA,CAAAC,aAAA,CAAC+gB,sBAAsB,CAAC0E,QAAxB;IAAiCtlB,KAAK,EAAEioB;GAAxC,EACG9K,QADH,CADF,CADF;AAOD;ACAD,MAAMgL,UAAU,GAAwB;EACtCtgB,QAAQ,EAAE,OAD4B;EAEtCugB,WAAW,EAAE;AAFyB,CAAxC;AAKA,MAAMC,iBAAiB,GAAsB5J,cAAD;EAC1C,MAAM6J,mBAAmB,GAAGrY,eAAe,CAACwO,cAAD,CAA3C;EAEA,OAAO6J,mBAAmB,GAAG,sBAAH,GAA4BvZ,SAAtD;AACD,CAJD;AAMA,MAAawZ,iBAAiB,gBAAGC,UAAU,CACzC,CAAAhrB,IAAA,EAYEwqB,GAZF;MACE;IACES,EADF;IAEEhK,cAFF;IAGErZ,WAHF;IAIE+X,QAJF;IAKEuL,SALF;IAME/mB,IANF;IAOEgnB,KAPF;IAQEtjB,SARF;IASEujB,UAAU,GAAGP;;EAIf,IAAI,CAAC1mB,IAAL,EAAW;IACT,OAAO,IAAP;;EAGF,MAAMknB,sBAAsB,GAAGzjB,WAAW,GACtCC,SADsC,GAEtC;IACE,GAAGA,SADL;IAEEG,MAAM,EAAE,CAFV;IAGEC,MAAM,EAAE;GALd;EAOA,MAAMqjB,MAAM,GAAoC;IAC9C,GAAGX,UAD2C;IAE9CnmB,KAAK,EAAEL,IAAI,CAACK,KAFkC;IAG9CE,MAAM,EAAEP,IAAI,CAACO,MAHiC;IAI9CD,GAAG,EAAEN,IAAI,CAACM,GAJoC;IAK9CF,IAAI,EAAEJ,IAAI,CAACI,IALmC;IAM9CsD,SAAS,EAAE0jB,GAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBJ,sBAAvB,CANmC;IAO9C/mB,eAAe,EACbsD,WAAW,IAAIqZ,cAAf,GACI/c,0BAA0B,CACxB+c,cADwB,EAExB9c,IAFwB,CAD9B,GAKIoN,SAbwC;IAc9C6Z,UAAU,EACR,OAAOA,UAAP,KAAsB,UAAtB,GACIA,UAAU,CAACnK,cAAD,CADd,GAEImK,UAjBwC;IAkB9C,GAAGD;GAlBL;EAqBA,OAAO/oB,KAAK,CAACC,aAAN,CACL4oB,EADK,EAEL;IACEC,SADF;IAEEC,KAAK,EAAEG,MAFT;IAGEd;GALG,EAOL7K,QAPK,CAAP;AASD,CAxDwC,CAApC;MCwDM+L,+BAA+B,GAC1C3oB,OAD6C,IAEhB/C,IAAA;MAAC;IAACU,MAAD;IAAS8f;;EACvC,MAAMmL,cAAc,GAA2B,EAA/C;EACA,MAAM;IAACL,MAAD;IAASJ;MAAanoB,OAA5B;EAEA,IAAIuoB,MAAJ,YAAIA,MAAM,CAAE5qB,MAAZ,EAAoB;IAClB,KAAK,MAAM,CAACsO,GAAD,EAAMxM,KAAN,CAAX,IAA2BgB,MAAM,CAACgb,OAAP,CAAe8M,MAAM,CAAC5qB,MAAtB,CAA3B,EAA0D;MACxD,IAAI8B,KAAK,KAAK+O,SAAd,EAAyB;QACvB;;MAGFoa,cAAc,CAAC3c,GAAD,CAAd,GAAsBtO,MAAM,CAACyJ,IAAP,CAAYghB,KAAZ,CAAkBS,gBAAlB,CAAmC5c,GAAnC,CAAtB;MACAtO,MAAM,CAACyJ,IAAP,CAAYghB,KAAZ,CAAkBU,WAAlB,CAA8B7c,GAA9B,EAAmCxM,KAAnC;;;EAIJ,IAAI8oB,MAAJ,YAAIA,MAAM,CAAE9K,WAAZ,EAAyB;IACvB,KAAK,MAAM,CAACxR,GAAD,EAAMxM,KAAN,CAAX,IAA2BgB,MAAM,CAACgb,OAAP,CAAe8M,MAAM,CAAC9K,WAAtB,CAA3B,EAA+D;MAC7D,IAAIhe,KAAK,KAAK+O,SAAd,EAAyB;QACvB;;MAGFiP,WAAW,CAACrW,IAAZ,CAAiBghB,KAAjB,CAAuBU,WAAvB,CAAmC7c,GAAnC,EAAwCxM,KAAxC;;;EAIJ,IAAI0oB,SAAJ,YAAIA,SAAS,CAAExqB,MAAf,EAAuB;IACrBA,MAAM,CAACyJ,IAAP,CAAY2hB,SAAZ,CAAsBjsB,GAAtB,CAA0BqrB,SAAS,CAACxqB,MAApC;;EAGF,IAAIwqB,SAAJ,YAAIA,SAAS,CAAE1K,WAAf,EAA4B;IAC1BA,WAAW,CAACrW,IAAZ,CAAiB2hB,SAAjB,CAA2BjsB,GAA3B,CAA+BqrB,SAAS,CAAC1K,WAAzC;;EAGF,OAAO,SAASjC,OAATA,CAAA;IACL,KAAK,MAAM,CAACvP,GAAD,EAAMxM,KAAN,CAAX,IAA2BgB,MAAM,CAACgb,OAAP,CAAemN,cAAf,CAA3B,EAA2D;MACzDjrB,MAAM,CAACyJ,IAAP,CAAYghB,KAAZ,CAAkBU,WAAlB,CAA8B7c,GAA9B,EAAmCxM,KAAnC;;IAGF,IAAI0oB,SAAJ,YAAIA,SAAS,CAAExqB,MAAf,EAAuB;MACrBA,MAAM,CAACyJ,IAAP,CAAY2hB,SAAZ,CAAsBC,MAAtB,CAA6Bb,SAAS,CAACxqB,MAAvC;;GANJ;AASD,CA5CM;AA8CP,MAAMsrB,uBAAuB,GAAqBnrB,KAAA;EAAA,IAAC;IACjDgH,SAAS,EAAE;MAACyc,OAAD;MAAU2H;;GAD2B,GAAAprB,KAAA;EAAA,OAE5C,CACJ;IACEgH,SAAS,EAAE0jB,GAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBnH,OAAvB;GAFT,EAIJ;IACEzc,SAAS,EAAE0jB,GAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBQ,KAAvB;GALT,CAF4C;AAAA,CAAlD;AAWA,MAAaC,iCAAiC,GAAmC;EAC/EC,QAAQ,EAAE,GADqE;EAE/EC,MAAM,EAAE,MAFuE;EAG/EC,SAAS,EAAEL,uBAHoE;EAI/EM,WAAW,eAAEZ,+BAA+B,CAAC;IAC3CJ,MAAM,EAAE;MACN5qB,MAAM,EAAE;QACN6rB,OAAO,EAAE;;;GAH6B;AAJmC,CAA1E;AAaP,SAAgBC,iBAAAxrB,KAAA;MAAiB;IAC/B0Z,MAD+B;IAE/BhB,cAF+B;IAG/BhU,mBAH+B;IAI/B0b;;EAEA,OAAOnF,QAAQ,CAAY,CAACtb,EAAD,EAAKwJ,IAAL;IACzB,IAAIuQ,MAAM,KAAK,IAAf,EAAqB;MACnB;;IAGF,MAAM+R,eAAe,GAA8B/S,cAAc,CAAC7T,GAAf,CAAmBlF,EAAnB,CAAnD;IAEA,IAAI,CAAC8rB,eAAL,EAAsB;MACpB;;IAGF,MAAMna,UAAU,GAAGma,eAAe,CAACtiB,IAAhB,CAAqBqI,OAAxC;IAEA,IAAI,CAACF,UAAL,EAAiB;MACf;;IAGF,MAAMoa,cAAc,GAAGhN,iBAAiB,CAACvV,IAAD,CAAxC;IAEA,IAAI,CAACuiB,cAAL,EAAqB;MACnB;;IAEF,MAAM;MAAC7kB;QAAa+B,SAAS,CAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC,CAApB;IACA,MAAMnB,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;IAEA,IAAI,CAACmB,eAAL,EAAsB;MACpB;;IAGF,MAAMihB,SAAS,GACb,OAAOvP,MAAP,KAAkB,UAAlB,GACIA,MADJ,GAEIiS,0BAA0B,CAACjS,MAAD,CAHhC;IAKArM,sBAAsB,CACpBiE,UADoB,EAEpB8O,sBAAsB,CAAC7gB,SAAvB,CAAiC+N,OAFb,CAAtB;IAKA,OAAO2b,SAAS,CAAC;MACfvpB,MAAM,EAAE;QACNC,EADM;QAENiE,IAAI,EAAE6nB,eAAe,CAAC7nB,IAFhB;QAGNuF,IAAI,EAAEmI,UAHA;QAINnO,IAAI,EAAEid,sBAAsB,CAAC7gB,SAAvB,CAAiC+N,OAAjC,CAAyCgE,UAAzC;OALO;MAOfoH,cAPe;MAQf8G,WAAW,EAAE;QACXrW,IADW;QAEXhG,IAAI,EAAEid,sBAAsB,CAACZ,WAAvB,CAAmClS,OAAnC,CAA2Coe,cAA3C;OAVO;MAYfhnB,mBAZe;MAaf0b,sBAbe;MAcfvZ,SAAS,EAAEmB;KAdG,CAAhB;GAvCa,CAAf;AAwDD;AAED,SAAS2jB,0BAATA,CACE5pB,OADF;EAGE,MAAM;IAACopB,QAAD;IAAWC,MAAX;IAAmBE,WAAnB;IAAgCD;MAAa;IACjD,GAAGH,iCAD8C;IAEjD,GAAGnpB;GAFL;EAKA,OAAO7B,KAAA;QAAC;MAACR,MAAD;MAAS8f,WAAT;MAAsB3Y,SAAtB;MAAiC,GAAG+kB;;IAC1C,IAAI,CAACT,QAAL,EAAe;;MAEb;;IAGF,MAAMnc,KAAK,GAAG;MACZtM,CAAC,EAAE8c,WAAW,CAACrc,IAAZ,CAAiBI,IAAjB,GAAwB7D,MAAM,CAACyD,IAAP,CAAYI,IAD3B;MAEZZ,CAAC,EAAE6c,WAAW,CAACrc,IAAZ,CAAiBM,GAAjB,GAAuB/D,MAAM,CAACyD,IAAP,CAAYM;KAFxC;IAKA,MAAMooB,KAAK,GAAG;MACZ7kB,MAAM,EACJH,SAAS,CAACG,MAAV,KAAqB,CAArB,GACKtH,MAAM,CAACyD,IAAP,CAAYK,KAAZ,GAAoBqD,SAAS,CAACG,MAA/B,GAAyCwY,WAAW,CAACrc,IAAZ,CAAiBK,KAD9D,GAEI,CAJM;MAKZyD,MAAM,EACJJ,SAAS,CAACI,MAAV,KAAqB,CAArB,GACKvH,MAAM,CAACyD,IAAP,CAAYO,MAAZ,GAAqBmD,SAAS,CAACI,MAAhC,GAA0CuY,WAAW,CAACrc,IAAZ,CAAiBO,MAD/D,GAEI;KARR;IAUA,MAAMooB,cAAc,GAAG;MACrBppB,CAAC,EAAEmE,SAAS,CAACnE,CAAV,GAAcsM,KAAK,CAACtM,CADF;MAErBC,CAAC,EAAEkE,SAAS,CAAClE,CAAV,GAAcqM,KAAK,CAACrM,CAFF;MAGrB,GAAGkpB;KAHL;IAMA,MAAME,kBAAkB,GAAGV,SAAS,CAAC;MACnC,GAAGO,IADgC;MAEnClsB,MAFmC;MAGnC8f,WAHmC;MAInC3Y,SAAS,EAAE;QAACyc,OAAO,EAAEzc,SAAV;QAAqBokB,KAAK,EAAEa;;KAJL,CAApC;IAOA,MAAM,CAACE,aAAD,IAAkBD,kBAAxB;IACA,MAAME,YAAY,GAAGF,kBAAkB,CAACA,kBAAkB,CAAC3nB,MAAnB,GAA4B,CAA7B,CAAvC;IAEA,IAAI6T,IAAI,CAACC,SAAL,CAAe8T,aAAf,MAAkC/T,IAAI,CAACC,SAAL,CAAe+T,YAAf,CAAtC,EAAoE;;MAElE;;IAGF,MAAM1O,OAAO,GAAG+N,WAAH,oBAAGA,WAAW,CAAG;MAAC5rB,MAAD;MAAS8f,WAAT;MAAsB,GAAGoM;KAA5B,CAA3B;IACA,MAAM3C,SAAS,GAAGzJ,WAAW,CAACrW,IAAZ,CAAiB+iB,OAAjB,CAAyBH,kBAAzB,EAA6C;MAC7DZ,QAD6D;MAE7DC,MAF6D;MAG7De,IAAI,EAAE;KAHU,CAAlB;IAMA,OAAO,IAAIjG,OAAJ,CAAaC,OAAD;MACjB8C,SAAS,CAACmD,QAAV,GAAqB;QACnB7O,OAAO,QAAP,YAAAA,OAAO;QACP4I,OAAO;OAFT;KADK,CAAP;GAjDF;AAwDD;AC9RD,IAAInY,GAAG,GAAG,CAAV;AAEA,SAAgBqe,OAAO1sB,EAAA;EACrB,OAAOoB,OAAO,CAAC;IACb,IAAIpB,EAAE,IAAI,IAAV,EAAgB;MACd;;IAGFqO,GAAG;IACH,OAAOA,GAAP;GANY,EAOX,CAACrO,EAAD,CAPW,CAAd;AAQD;MCaY2sB,WAAW,gBAAGlrB,KAAK,CAACmhB,IAAN,CACzBvjB,IAAA;MAAC;IACC4H,WAAW,GAAG,KADf;IAEC+X,QAFD;IAGC4N,aAAa,EAAEC,mBAHhB;IAICrC,KAJD;IAKCC,UALD;IAMCtI,SAND;IAOC2K,cAAc,GAAG,KAPlB;IAQCvC,SARD;IASCwC,MAAM,GAAG;;EAET,MAAM;IACJzM,cADI;IAEJvgB,MAFI;IAGJwgB,cAHI;IAIJC,iBAJI;IAKJzH,cALI;IAMJhU,mBANI;IAOJ8a,WAPI;IAQJ1f,IARI;IASJsgB,sBATI;IAUJlT,mBAVI;IAWJgK,uBAXI;IAYJoH;MACEyJ,aAAa,EAbjB;EAcA,MAAMlhB,SAAS,GAAGzI,UAAU,CAACgkB,sBAAD,CAA5B;EACA,MAAMpU,GAAG,GAAGqe,MAAM,CAAC3sB,MAAD,oBAACA,MAAM,CAAEC,EAAT,CAAlB;EACA,MAAMgtB,iBAAiB,GAAG9K,cAAc,CAACC,SAAD,EAAY;IAClD7B,cADkD;IAElDvgB,MAFkD;IAGlDwgB,cAHkD;IAIlDC,iBAJkD;IAKlDqE,gBAAgB,EAAEhF,WAAW,CAACrc,IALoB;IAMlDrD,IANkD;IAOlDmlB,eAAe,EAAEzF,WAAW,CAACrc,IAPqB;IAQlD+J,mBARkD;IASlDgK,uBATkD;IAUlDrQ,SAVkD;IAWlDyX;GAXsC,CAAxC;EAaA,MAAM3B,WAAW,GAAGhC,eAAe,CAACuF,cAAD,CAAnC;EACA,MAAMqM,aAAa,GAAGf,gBAAgB,CAAC;IACrC9R,MAAM,EAAE8S,mBAD6B;IAErC9T,cAFqC;IAGrChU,mBAHqC;IAIrC0b;GAJoC,CAAtC;;;EAQA,MAAMoJ,GAAG,GAAG7M,WAAW,GAAG6C,WAAW,CAACR,MAAf,GAAwBzO,SAA/C;EAEA,OACEnP,KAAA,CAAAC,aAAA,CAACqoB,wBAAD,QACEtoB,KAAA,CAAAC,aAAA,CAAC2nB,gBAAD;IAAkBC,SAAS,EAAEsD;GAA7B,EACG7sB,MAAM,IAAIsO,GAAV,GACC5M,KAAA,CAAAC,aAAA,CAAC0oB,iBAAD;IACE/b,GAAG,EAAEA,GAAA;IACLrO,EAAE,EAAED,MAAM,CAACC,EAAA;IACX6pB,GAAG,EAAEA,GAAA;IACLS,EAAE,EAAEwC,cAAA;IACJxM,cAAc,EAAEA,cAAA;IAChBrZ,WAAW,EAAEA,WAAA;IACbsjB,SAAS,EAAEA,SAAA;IACXE,UAAU,EAAEA,UAAA;IACZjnB,IAAI,EAAEwZ,WAAA;IACNwN,KAAK,EAAE;MACLuC,MADK;MAEL,GAAGvC;;IAELtjB,SAAS,EAAE8lB;GAdb,EAgBGhO,QAhBH,CADD,GAmBG,IApBN,CADF,CADF;AA0BD,CA9EwB,CAApB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}