import React from 'react';
import { useDraggable } from '@dnd-kit/core';

const DraggableImage = ({ imageUrl, index }) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        isDragging,
    } = useDraggable({
        id: `image-${index}`,
        data: {
            type: 'image',
            imageUrl: imageUrl,
        },
    });

    const style = {
        opacity: isDragging ? 0.3 : 1,
        cursor: isDragging ? 'grabbing' : 'grab',
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            {...listeners}
            {...attributes}
            className="mb-6 border-2 border-dashed border-gray-300 rounded-lg p-2 hover:border-blue-400 transition-colors"
        >
            <label className="block text-sm font-medium text-gray-700 mb-2">
                H<PERSON>nh <PERSON>nh {index + 1} (Kéo để thêm vào câu hỏi):
            </label>
            <img 
                src={imageUrl} 
                alt={`image-${index}`} 
                className="w-full h-48 object-contain rounded-lg"
                draggable={false}
            />
        </div>
    );
};

export default DraggableImage;
