{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\AddExamAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport { setShowAddImagesModal } from \"src/features/addExam/addExamSlice\";\nimport { setStep, setExamData, postExam, nextStep, prevStep } from \"src/features/addExam/addExamSlice\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport ImageUpload from \"src/components/image/UploadImage\";\nimport UploadPdf from \"src/components/UploadPdf\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport { fetchImages } from \"src/features/image/imageSlice\";\nimport { ArrowLeft, Save, Eye, FileText, Plus, Trash2, Edit, CheckCircle, ChevronRight, ChevronLeft, Clock, Users, BookOpen, Image as ImageIcon, Upload } from \"lucide-react\";\nimport LeftContent from \"src/components/PageAddExam/LeftContent\";\nimport RightContent from \"src/components/PageAddExam/RightContent\";\nimport AddImagesModal from \"src/components/modal/AddImagesModal\";\n\n// Main Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AddExamAdmin = () => {\n  _s();\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n  const navigate = useNavigate();\n  const {\n    step\n  } = useSelector(state => state.addExam);\n  const {\n    showAddImagesModal\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    dispatch(fetchImages(\"ImageFormN8N\"));\n    dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\n  }, [dispatch]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-50 flex flex-col min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this), showAddImagesModal && /*#__PURE__*/_jsxDEV(AddImagesModal, {\n      showAddImagesModal: showAddImagesModal,\n      folder: \"ImageFormN8N\",\n      setShowAddImagesModal: value => dispatch(setShowAddImagesModal(value))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 36\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 flex flex-col min-h-screen \".concat(closeSidebar ? \"ml-[104px]\" : \"ml-64\"),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border-b border-gray-200 flex justify-between items-center px-3 py-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/admin/exam-management'),\n            className: \"p-1 hover:bg-gray-100 rounded transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(ArrowLeft, {\n              className: \"w-4 h-4 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-sm font-bold text-gray-900\",\n              children: \"T\\u1EA1o \\u0111\\u1EC1 thi m\\u1EDBi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full\",\n            children: [\"B\\u01B0\\u1EDBc \", step, \"/3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-1 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-1/2 border-r border-gray-200 bg-white\",\n          children: /*#__PURE__*/_jsxDEV(LeftContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-1/2\",\n          children: /*#__PURE__*/_jsxDEV(RightContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 9\n  }, this);\n};\n_s(AddExamAdmin, \"DEdF6J05lNeP5LcLc49ig3e9wZA=\", false, function () {\n  return [useSelector, useNavigate, useSelector, useSelector, useDispatch];\n});\n_c = AddExamAdmin;\nexport default AddExamAdmin;\nvar _c;\n$RefreshReg$(_c, \"AddExamAdmin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "useNavigate", "AdminSidebar", "fetchCodesByType", "setShowAddImagesModal", "setStep", "setExamData", "postExam", "nextStep", "prevStep", "DropMenuBarAdmin", "SuggestInputBarAdmin", "ImageUpload", "UploadPdf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "fetchImages", "ArrowLeft", "Save", "Eye", "FileText", "Plus", "Trash2", "Edit", "CheckCircle", "ChevronRight", "ChevronLeft", "Clock", "Users", "BookOpen", "Image", "ImageIcon", "Upload", "LeftContent", "RightContent", "AddImagesModal", "jsxDEV", "_jsxDEV", "AddExamAdmin", "_s", "closeSidebar", "state", "sidebar", "navigate", "step", "addExam", "showAddImagesModal", "dispatch", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "folder", "value", "concat", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/AddExamAdmin.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport { setShowAddImagesModal } from \"src/features/addExam/addExamSlice\";\r\nimport { setStep, setExamData, postExam, nextStep, prevStep } from \"src/features/addExam/addExamSlice\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport ImageUpload from \"src/components/image/UploadImage\";\r\nimport UploadPdf from \"src/components/UploadPdf\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport { fetchImages } from \"src/features/image/imageSlice\";\r\nimport {\r\n    ArrowLeft,\r\n    Save,\r\n    Eye,\r\n    FileText,\r\n    Plus,\r\n    Trash2,\r\n    Edit,\r\n    CheckCircle,\r\n    ChevronRight,\r\n    ChevronLeft,\r\n    Clock,\r\n    Users,\r\n    BookOpen,\r\n    Image as ImageIcon,\r\n    Upload\r\n} from \"lucide-react\";\r\n\r\nimport LeftContent from \"src/components/PageAddExam/LeftContent\";\r\nimport RightContent from \"src/components/PageAddExam/RightContent\";\r\nimport AddImagesModal from \"src/components/modal/AddImagesModal\";\r\n\r\n\r\n// Main Component\r\nexport const AddExamAdmin = () => {\r\n    const { closeSidebar } = useSelector((state) => state.sidebar);\r\n    const navigate = useNavigate();\r\n    const { step } = useSelector((state) => state.addExam);\r\n    const { showAddImagesModal } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n    useEffect(() => {\r\n        dispatch(fetchImages(\"ImageFormN8N\"));\r\n        dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\r\n    }, [dispatch]);\r\n\r\n    return (\r\n        <div className=\"bg-gray-50 flex flex-col min-h-screen\">\r\n            <AdminSidebar />\r\n            {showAddImagesModal && <AddImagesModal showAddImagesModal={showAddImagesModal} folder=\"ImageFormN8N\" setShowAddImagesModal={(value) => dispatch(setShowAddImagesModal(value))} />}\r\n            <div className={`bg-gray-50 flex flex-col min-h-screen ${closeSidebar ? \"ml-[104px]\" : \"ml-64\"}`}>\r\n                {/* Compact Header */}\r\n                <div className=\"bg-white border-b border-gray-200 flex justify-between items-center px-3 py-2\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <button\r\n                            onClick={() => navigate('/admin/exam-management')}\r\n                            className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\r\n                        >\r\n                            <ArrowLeft className=\"w-4 h-4 text-gray-600\" />\r\n                        </button>\r\n                        <div>\r\n                            <h1 className=\"text-sm font-bold text-gray-900\">Tạo đề thi mới</h1>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <div className=\"text-xs text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full\">\r\n                            Bước {step}/3\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Main Content - 2 Column Layout */}\r\n                <div className=\"flex flex-1 overflow-hidden\">\r\n                    {/* Left Panel - Form */}\r\n                    <div className=\"w-1/2 border-r border-gray-200 bg-white\">\r\n                        <LeftContent />\r\n                    </div>\r\n\r\n                    {/* Right Panel - Preview */}\r\n                    <div className=\"w-1/2\">\r\n                        <RightContent />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AddExamAdmin;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,mCAAmC;AACtG,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,cAAc,MAAM,uCAAuC;AAClE,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SACIC,SAAS,EACTC,IAAI,EACJC,GAAG,EACHC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,QAAQ,EACRC,KAAK,IAAIC,SAAS,EAClBC,MAAM,QACH,cAAc;AAErB,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,cAAc,MAAM,qCAAqC;;AAGhE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAa,CAAC,GAAGzC,WAAW,CAAE0C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC9D,MAAMC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2C;EAAK,CAAC,GAAG7C,WAAW,CAAE0C,KAAK,IAAKA,KAAK,CAACI,OAAO,CAAC;EACtD,MAAM;IAAEC;EAAmB,CAAC,GAAG/C,WAAW,CAAE0C,KAAK,IAAKA,KAAK,CAACI,OAAO,CAAC;EACpE,MAAME,QAAQ,GAAG/C,WAAW,CAAC,CAAC;EAC9BF,SAAS,CAAC,MAAM;IACZiD,QAAQ,CAAC/B,WAAW,CAAC,cAAc,CAAC,CAAC;IACrC+B,QAAQ,CAAC5C,gBAAgB,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;EACvF,CAAC,EAAE,CAAC4C,QAAQ,CAAC,CAAC;EAEd,oBACIV,OAAA;IAAKW,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBAClDZ,OAAA,CAACnC,YAAY;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACfP,kBAAkB,iBAAIT,OAAA,CAACF,cAAc;MAACW,kBAAkB,EAAEA,kBAAmB;MAACQ,MAAM,EAAC,cAAc;MAAClD,qBAAqB,EAAGmD,KAAK,IAAKR,QAAQ,CAAC3C,qBAAqB,CAACmD,KAAK,CAAC;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjLhB,OAAA;MAAKW,SAAS,2CAAAQ,MAAA,CAA2ChB,YAAY,GAAG,YAAY,GAAG,OAAO,CAAG;MAAAS,QAAA,gBAE7FZ,OAAA;QAAKW,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC1FZ,OAAA;UAAKW,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpCZ,OAAA;YACIoB,OAAO,EAAEA,CAAA,KAAMd,QAAQ,CAAC,wBAAwB,CAAE;YAClDK,SAAS,EAAC,iDAAiD;YAAAC,QAAA,eAE3DZ,OAAA,CAACpB,SAAS;cAAC+B,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACThB,OAAA;YAAAY,QAAA,eACIZ,OAAA;cAAIW,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNhB,OAAA;UAAKW,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACpCZ,OAAA;YAAKW,SAAS,EAAC,4DAA4D;YAAAC,QAAA,GAAC,iBACnE,EAACL,IAAI,EAAC,IACf;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNhB,OAAA;QAAKW,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAExCZ,OAAA;UAAKW,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACpDZ,OAAA,CAACJ,WAAW;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eAGNhB,OAAA;UAAKW,SAAS,EAAC,OAAO;UAAAC,QAAA,eAClBZ,OAAA,CAACH,YAAY;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACd,EAAA,CAnDWD,YAAY;EAAA,QACIvC,WAAW,EACnBE,WAAW,EACXF,WAAW,EACGA,WAAW,EACzBC,WAAW;AAAA;AAAA0D,EAAA,GALnBpB,YAAY;AAqDzB,eAAeA,YAAY;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}