{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\nexport const fetchExams = createAsyncThunk(\"exams/fetchExams\", async (_ref, _ref2) => {\n  let {\n    search,\n    currentPage,\n    limit,\n    sortOrder\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, examApi.getAllExamAPI, {\n    search,\n    currentPage,\n    limit,\n    sortOrder\n  }, data => {\n    // dispatch(setCurrentPage(data.currentPage));\n    // dispatch(setTotalPages(data.totalPages));\n    // dispatch(setTotalItems(data.totalItems));\n  }, true, false);\n});\nexport const fetchNewestExams = createAsyncThunk(\"exams/fetchNewestExams\", async (_, _ref3) => {\n  let {\n    dispatch\n  } = _ref3;\n  return await apiHandler(dispatch, examApi.getNewestExamAPI, null, () => {}, false, false);\n});\nexport const fetchSavedExam = createAsyncThunk(\"exams/fetchSavedExam\", async (_, _ref4) => {\n  let {\n    dispatch\n  } = _ref4;\n  return await apiHandler(dispatch, examApi.getExamsSavedAPI, null, () => {}, false, false);\n});\nexport const reExamination = createAsyncThunk(\"exams/reExamination\", async (id, _ref5) => {\n  let {\n    dispatch\n  } = _ref5;\n  return await apiHandler(dispatch, examApi.reExamination, id, () => {}, true, false, false, false);\n});\n\n// export const summitExam = createAsyncThunk(\n//     \"exams/summitExam\",\n//     async (attemptId, { dispatch }) => {\n//         return await apiHandler(dispatch, examApi.summitExamAPI, { attemptId }, () => { }, true, false);\n//     }\n// );\n\nexport const fetchPublicExams = createAsyncThunk(\"exams/fetchPublicExams\", async (data, _ref6) => {\n  let {\n    dispatch\n  } = _ref6;\n  return await apiHandler(dispatch, examApi.getAllPublicExamAPI, data, data => {\n    // dispatch(setCurrentPage(data.currentPage));\n    // dispatch(setTotalPages(data.totalPages));\n    // dispatch(setTotalItems(data.totalItems));\n    // dispatch(setLimit(data.limit));\n  }, false, false);\n});\nexport const fetchRelatedExams = createAsyncThunk(\"exams/fetchRelatedExams\", async (id, _ref7) => {\n  let {\n    dispatch\n  } = _ref7;\n  return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => {}, false, false);\n});\n\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\"exams/fetchRelatedExamsIfNeeded\", async (id, _ref8) => {\n  let {\n    dispatch,\n    getState\n  } = _ref8;\n  const state = getState();\n  const {\n    relatedExams,\n    lastFetchedRelatedExams\n  } = state.exams;\n\n  // Kiểm tra xem đã có dữ liệu trong cache chưa\n  const hasData = relatedExams[id] && relatedExams[id].length > 0;\n\n  // Kiểm tra thời gian cache (5 phút = 300000ms)\n  const now = Date.now();\n  const lastFetched = lastFetchedRelatedExams[id] || 0;\n  const isCacheValid = now - lastFetched < 300000;\n\n  // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache\n  if (hasData && isCacheValid) {\n    // Cập nhật state.exams để hiển thị dữ liệu cache\n    return {\n      data: relatedExams[id]\n    };\n  }\n\n  // Nếu không có dữ liệu hoặc cache hết hạn, gọi API\n  return await dispatch(fetchRelatedExams(id)).unwrap();\n});\nexport const fetchExamById = createAsyncThunk(\"exams/fetchExamById\", async (id, _ref9) => {\n  let {\n    dispatch\n  } = _ref9;\n  return await apiHandler(dispatch, examApi.getExamByIdAPI, id, () => {}, true, false);\n});\nexport const findExams = createAsyncThunk(\"exams/findExams\", async (search, _ref10) => {\n  let {\n    dispatch\n  } = _ref10;\n  return await apiHandler(dispatch, examApi.findExamsAPI, search, () => {}, false, false, false, false);\n});\nexport const fetchPublicExamById = createAsyncThunk(\"exams/fetchPublicExamById\", async (id, _ref11) => {\n  let {\n    dispatch\n  } = _ref11;\n  return await apiHandler(dispatch, examApi.getExamPublic, id, () => {}, false, false);\n});\nexport const putExam = createAsyncThunk(\"exams/putExam\", async (_ref12, _ref13) => {\n  let {\n    examId,\n    examData\n  } = _ref12;\n  let {\n    dispatch\n  } = _ref13;\n  return await apiHandler(dispatch, examApi.putExamAPI, {\n    examId,\n    examData\n  }, () => {}, true, false);\n});\nexport const putImageExam = createAsyncThunk(\"exams/putImageExam\", async (_ref14, _ref15) => {\n  let {\n    examId,\n    examImage\n  } = _ref14;\n  let {\n    dispatch\n  } = _ref15;\n  return await apiHandler(dispatch, examApi.putImageExamAPI, {\n    examId,\n    examImage\n  }, () => {}, true, false);\n});\nexport const uploadSolutionPdf = createAsyncThunk(\"exams/uploadSolutionPdf\", async (_ref16, _ref17) => {\n  let {\n    examId,\n    pdfFile\n  } = _ref16;\n  let {\n    dispatch\n  } = _ref17;\n  return await apiHandler(dispatch, examApi.uploadSolutionPdfAPI, {\n    examId,\n    pdfFile\n  }, () => {}, true, false, true, false);\n});\nexport const uploadExamPdf = createAsyncThunk(\"exams/uploadExamPdf\", async (_ref18, _ref19) => {\n  let {\n    examId,\n    pdfFile\n  } = _ref18;\n  let {\n    dispatch\n  } = _ref19;\n  return await apiHandler(dispatch, examApi.uploadExamPdfAPI, {\n    examId,\n    pdfFile\n  }, () => {}, true, false, true, false);\n});\nexport const saveExamForUser = createAsyncThunk(\"exams/saveExamForUser\", async (_ref20, _ref21) => {\n  let {\n    examId\n  } = _ref20;\n  let {\n    dispatch\n  } = _ref21;\n  return await apiHandler(dispatch, examApi.saveExamForUserAPI, {\n    examId\n  }, () => {}, true, false, false, false);\n});\nexport const deleteExam = createAsyncThunk(\"exams/deleteExam\", async (id, _ref22) => {\n  let {\n    dispatch\n  } = _ref22;\n  return await apiHandler(dispatch, examApi.deleteExamAPI, id, () => {}, true, false);\n});\nconst examSlice = createSlice({\n  name: \"exams\",\n  initialState: {\n    exams: [],\n    exam: null,\n    examsSearch: [],\n    relatedExams: {},\n    // Lưu trữ đề thi liên quan theo examId\n    lastFetchedRelatedExams: {},\n    // Lưu thời gian gọi API cuối cùng theo examId\n    loadingExam: false,\n    loadingSubmit: false,\n    loadingUpload1: false,\n    loadingUpload2: false,\n    isSubmit: false,\n    pagination: {\n      ...initialPaginationState\n    },\n    ...initialFilterState\n  },\n  reducers: {\n    setExam: (state, action) => {\n      state.exam = action.payload;\n    },\n    ...paginationReducers,\n    ...filterReducers\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchExams.pending, state => {\n      state.exams = [];\n      state.loading = true;\n    }).addCase(fetchExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n        state.pagination = action.payload.pagination || initialPaginationState;\n      }\n      state.loading = false;\n    }).addCase(fetchExams.rejected, state => {\n      state.loading = false;\n      state.exams = [];\n    }).addCase(fetchNewestExams.pending, state => {\n      state.exams = [];\n    }).addCase(fetchNewestExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n      }\n    }).addCase(fetchSavedExam.pending, state => {\n      state.exams = [];\n      state.loadingExam = true;\n    }).addCase(fetchSavedExam.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n      }\n      state.loadingExam = false;\n    }).addCase(fetchSavedExam.rejected, state => {\n      state.loadingExam = false;\n    }).addCase(fetchRelatedExams.pending, () => {\n      // Không xóa state.exams nữa để tránh làm mất dữ liệu hiện tại\n    }).addCase(fetchRelatedExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        const examId = action.meta.arg; // Lấy examId từ tham số gọi API\n        state.exams = action.payload.data;\n        state.relatedExams[examId] = action.payload.data;\n        state.lastFetchedRelatedExams[examId] = Date.now();\n      }\n    }).addCase(fetchExamById.pending, state => {\n      state.exam = null;\n    }).addCase(fetchExamById.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exam = action.payload.data;\n      }\n    }).addCase(findExams.pending, state => {\n      state.examsSearch = [];\n    }).addCase(findExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.examsSearch = action.payload.data;\n      }\n    }).addCase(fetchPublicExams.pending, state => {\n      state.exams = [];\n      state.loading = true;\n    }).addCase(fetchPublicExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n        state.pagination = action.payload.pagination || initialPaginationState;\n      }\n      state.loading = false;\n    }).addCase(fetchPublicExams.rejected, state => {\n      state.loading = false;\n      state.exams = [];\n    }).addCase(fetchPublicExamById.pending, state => {\n      state.exam = null;\n    }).addCase(fetchPublicExamById.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exam = action.payload.data;\n      }\n    }).addCase(saveExamForUser.fulfilled, (state, action) => {\n      if (action.payload) {\n        var _state$exams$;\n        const {\n          examId,\n          isSave\n        } = action.payload;\n\n        // Trường hợp 1: Khi exams chứa đối tượng có thuộc tính exam (từ API saved exams)\n        if (state.exams.length > 0 && (_state$exams$ = state.exams[0]) !== null && _state$exams$ !== void 0 && _state$exams$.exam) {\n          if (isSave === false) {\n            // Nếu isSave = false, loại bỏ exam khỏi danh sách đã lưu\n            state.exams = state.exams.filter(exam => {\n              var _exam$exam;\n              return ((_exam$exam = exam.exam) === null || _exam$exam === void 0 ? void 0 : _exam$exam.id) !== examId;\n            });\n          } else {\n            // Nếu isSave = true, cập nhật trạng thái\n            state.exams = state.exams.map(exam => {\n              var _exam$exam2;\n              if (((_exam$exam2 = exam.exam) === null || _exam$exam2 === void 0 ? void 0 : _exam$exam2.id) === examId) {\n                return {\n                  ...exam,\n                  isSave: true\n                };\n              }\n              return exam;\n            });\n          }\n          return;\n        }\n\n        // Trường hợp 2: Khi exams chứa danh sách exam thông thường\n        if (state.exams && Array.isArray(state.exams)) {\n          state.exams = state.exams.map(exam => {\n            if (exam.id === examId) {\n              return {\n                ...exam,\n                isSave: isSave\n              };\n            }\n            return exam;\n          });\n        }\n\n        // Cập nhật trạng thái cho exam hiện tại nếu có\n        if (state.exam) {\n          state.exam.isSave = isSave;\n        }\n      }\n    }).addCase(uploadSolutionPdf.pending, state => {\n      state.loadingUpload2 = true;\n    }).addCase(uploadSolutionPdf.fulfilled, (state, action) => {\n      // console.log(action.payload)\n      const pdfUrl = action.payload;\n      if (state.exam) {\n        state.exam.solutionPdfUrl = pdfUrl;\n      }\n      state.loadingUpload2 = false;\n    }).addCase(uploadSolutionPdf.rejected, state => {\n      state.loadingUpload1 = false;\n    }).addCase(uploadExamPdf.pending, state => {\n      state.loadingUpload1 = true;\n    }).addCase(uploadExamPdf.fulfilled, (state, action) => {\n      const pdfUrl = action.payload;\n      if (state.exam) {\n        state.exam.fileUrl = pdfUrl;\n      }\n      state.loadingUpload1 = false;\n    }).addCase(uploadExamPdf.rejected, state => {\n      state.loadingUpload1 = false;\n    }).addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\n      if (action.payload && action.payload.data) {\n        state.exams = action.payload.data;\n      }\n    });\n    // .addCase(summitExam.pending, (state) => {\n    //     state.loadingSubmit = true;\n    // })\n    // .addCase(summitExam.fulfilled, (state, action) => {\n    //     state.loadingSubmit = false;\n    //     state.isSubmit = true;\n    // })\n    // .addCase(summitExam.rejected, (state) => {\n    //     state.loadingSubmit = false;\n    //     state.isSubmit = false;\n    // })\n  }\n});\nexport const {\n  setExam,\n  setCurrentPage,\n  setLimit,\n  setSortOrder,\n  setLoading,\n  setSearch\n} = examSlice.actions;\nexport default examSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialPaginationState", "paginationReducers", "initialFilterState", "filterReducers", "fetchExams", "_ref", "_ref2", "search", "currentPage", "limit", "sortOrder", "dispatch", "getAllExamAPI", "data", "fetchNewestExams", "_", "_ref3", "getNewestExamAPI", "fetchSavedExam", "_ref4", "getExamsSavedAPI", "reExamination", "id", "_ref5", "fetchPublicExams", "_ref6", "getAllPublicExamAPI", "fetchRelatedExams", "_ref7", "getRelatedExamAPI", "fetchRelatedExamsIfNeeded", "_ref8", "getState", "state", "relatedExams", "lastFetchedRelatedExams", "exams", "hasData", "length", "now", "Date", "lastFetched", "isCache<PERSON><PERSON>d", "unwrap", "fetchExamById", "_ref9", "getExamByIdAPI", "findExams", "_ref10", "findExamsAPI", "fetchPublicExamById", "_ref11", "getExamPublic", "putExam", "_ref12", "_ref13", "examId", "examData", "putExamAPI", "putImageExam", "_ref14", "_ref15", "examImage", "putImageExamAPI", "uploadSolutionPdf", "_ref16", "_ref17", "pdfFile", "uploadSolutionPdfAPI", "uploadExamPdf", "_ref18", "_ref19", "uploadExamPdfAPI", "saveExamForUser", "_ref20", "_ref21", "saveExamForUserAPI", "deleteExam", "_ref22", "deleteExamAPI", "examSlice", "name", "initialState", "exam", "examsSearch", "loadingExam", "loadingSubmit", "loadingUpload1", "loadingUpload2", "isSubmit", "pagination", "reducers", "setExam", "action", "payload", "extraReducers", "builder", "addCase", "pending", "loading", "fulfilled", "rejected", "meta", "arg", "_state$exams$", "isSave", "filter", "_exam$exam", "map", "_exam$exam2", "Array", "isArray", "pdfUrl", "solutionPdfUrl", "fileUrl", "setCurrentPage", "setLimit", "setSortOrder", "setLoading", "setSearch", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/exam/examSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\n// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from \"../filter/filterSlice\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\r\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\r\n\r\nexport const fetchExams = createAsyncThunk(\r\n    \"exams/fetchExams\",\r\n    async ({ search, currentPage, limit, sortOrder }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getAllExamAPI, { search, currentPage, limit, sortOrder }, (data) => {\r\n            // dispatch(setCurrentPage(data.currentPage));\r\n            // dispatch(setTotalPages(data.totalPages));\r\n            // dispatch(setTotalItems(data.totalItems));\r\n        }, true, false);\r\n    }\r\n);\r\n\r\nexport const fetchNewestExams = createAsyncThunk(\r\n    \"exams/fetchNewestExams\",\r\n    async (_, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getNewestExamAPI, null, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchSavedExam = createAsyncThunk(\r\n    \"exams/fetchSavedExam\",\r\n    async (_, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamsSavedAPI, null, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const reExamination = createAsyncThunk(\r\n    \"exams/reExamination\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.reExamination, id, () => { }, true, false, false, false);\r\n    }\r\n);\r\n\r\n// export const summitExam = createAsyncThunk(\r\n//     \"exams/summitExam\",\r\n//     async (attemptId, { dispatch }) => {\r\n//         return await apiHandler(dispatch, examApi.summitExamAPI, { attemptId }, () => { }, true, false);\r\n//     }\r\n// );\r\n\r\nexport const fetchPublicExams = createAsyncThunk(\r\n    \"exams/fetchPublicExams\",\r\n    async (data, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getAllPublicExamAPI, data, (data) => {\r\n            // dispatch(setCurrentPage(data.currentPage));\r\n            // dispatch(setTotalPages(data.totalPages));\r\n            // dispatch(setTotalItems(data.totalItems));\r\n            // dispatch(setLimit(data.limit));\r\n        }, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchRelatedExams = createAsyncThunk(\r\n    \"exams/fetchRelatedExams\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\r\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\r\n    \"exams/fetchRelatedExamsIfNeeded\",\r\n    async (id, { dispatch, getState }) => {\r\n        const state = getState();\r\n        const { relatedExams, lastFetchedRelatedExams } = state.exams;\r\n\r\n        // Kiểm tra xem đã có dữ liệu trong cache chưa\r\n        const hasData = relatedExams[id] && relatedExams[id].length > 0;\r\n\r\n        // Kiểm tra thời gian cache (5 phút = 300000ms)\r\n        const now = Date.now();\r\n        const lastFetched = lastFetchedRelatedExams[id] || 0;\r\n        const isCacheValid = now - lastFetched < 300000;\r\n\r\n        // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache\r\n        if (hasData && isCacheValid) {\r\n            // Cập nhật state.exams để hiển thị dữ liệu cache\r\n            return { data: relatedExams[id] };\r\n        }\r\n\r\n        // Nếu không có dữ liệu hoặc cache hết hạn, gọi API\r\n        return await dispatch(fetchRelatedExams(id)).unwrap();\r\n    }\r\n);\r\n\r\nexport const fetchExamById = createAsyncThunk(\r\n    \"exams/fetchExamById\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamByIdAPI, id, () => { }, true, false);\r\n    }\r\n);\r\n\r\nexport const findExams = createAsyncThunk(\r\n    \"exams/findExams\",\r\n    async (search, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.findExamsAPI, search, () => { }, false, false, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchPublicExamById = createAsyncThunk(\r\n    \"exams/fetchPublicExamById\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamPublic, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const putExam = createAsyncThunk(\r\n    \"exams/putExam\",\r\n    async ({ examId, examData }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.putExamAPI, { examId, examData }, () => { }, true, false);\r\n    }\r\n);\r\n\r\nexport const putImageExam = createAsyncThunk(\r\n    \"exams/putImageExam\",\r\n    async ({ examId, examImage }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.putImageExamAPI, { examId, examImage }, () => { }, true, false);\r\n    }\r\n);\r\n\r\n\r\n\r\nexport const uploadSolutionPdf = createAsyncThunk(\r\n    \"exams/uploadSolutionPdf\",\r\n    async ({ examId, pdfFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.uploadSolutionPdfAPI, { examId, pdfFile }, () => { }, true, false, true, false);\r\n    }\r\n);\r\n\r\nexport const uploadExamPdf = createAsyncThunk(\r\n    \"exams/uploadExamPdf\",\r\n    async ({ examId, pdfFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.uploadExamPdfAPI, { examId, pdfFile }, () => { }, true, false, true, false);\r\n    }\r\n);\r\n\r\nexport const saveExamForUser = createAsyncThunk(\r\n    \"exams/saveExamForUser\",\r\n    async ({ examId }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.saveExamForUserAPI, { examId }, () => { }, true, false, false, false);\r\n    }\r\n);\r\n\r\nexport const deleteExam = createAsyncThunk(\r\n    \"exams/deleteExam\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.deleteExamAPI, id, () => { }, true, false);\r\n    }\r\n);\r\n\r\nconst examSlice = createSlice({\r\n    name: \"exams\",\r\n    initialState: {\r\n        exams: [],\r\n        exam: null,\r\n        examsSearch: [],\r\n        relatedExams: {}, // Lưu trữ đề thi liên quan theo examId\r\n        lastFetchedRelatedExams: {}, // Lưu thời gian gọi API cuối cùng theo examId\r\n        loadingExam: false,\r\n        loadingSubmit: false,\r\n        loadingUpload1: false,\r\n        loadingUpload2: false,\r\n        isSubmit: false,\r\n        pagination: { ...initialPaginationState },\r\n        ...initialFilterState,\r\n    },\r\n    reducers: {\r\n        setExam: (state, action) => {\r\n            state.exam = action.payload;\r\n        },\r\n        ...paginationReducers,\r\n        ...filterReducers,\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchExams.pending, (state) => {\r\n                state.exams = [];\r\n                state.loading = true;\r\n            })\r\n            .addCase(fetchExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                    state.pagination = action.payload.pagination || initialPaginationState;\r\n                }\r\n                state.loading = false;\r\n            })\r\n            .addCase(fetchExams.rejected, (state) => {\r\n                state.loading = false;\r\n                state.exams = [];\r\n            })\r\n\r\n            .addCase(fetchNewestExams.pending, (state) => {\r\n                state.exams = [];\r\n            })\r\n            .addCase(fetchNewestExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(fetchSavedExam.pending, (state) => {\r\n                state.exams = [];\r\n                state.loadingExam = true;\r\n            })\r\n            .addCase(fetchSavedExam.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                }\r\n                state.loadingExam = false;\r\n            })\r\n            .addCase(fetchSavedExam.rejected, (state) => {\r\n                state.loadingExam = false;\r\n            })\r\n            .addCase(fetchRelatedExams.pending, () => {\r\n                // Không xóa state.exams nữa để tránh làm mất dữ liệu hiện tại\r\n            })\r\n            .addCase(fetchRelatedExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    const examId = action.meta.arg; // Lấy examId từ tham số gọi API\r\n                    state.exams = action.payload.data;\r\n                    state.relatedExams[examId] = action.payload.data;\r\n                    state.lastFetchedRelatedExams[examId] = Date.now();\r\n                }\r\n            })\r\n            .addCase(fetchExamById.pending, (state) => {\r\n                state.exam = null;\r\n            })\r\n            .addCase(fetchExamById.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exam = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(findExams.pending, (state) => {\r\n                state.examsSearch = [];\r\n            })\r\n            .addCase(findExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.examsSearch = action.payload.data;\r\n                }\r\n            })\r\n\r\n            .addCase(fetchPublicExams.pending, (state) => {\r\n                state.exams = [];\r\n                state.loading = true;\r\n            })\r\n            .addCase(fetchPublicExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                    state.pagination = action.payload.pagination || initialPaginationState;\r\n                }\r\n                state.loading = false;\r\n            })\r\n            .addCase(fetchPublicExams.rejected, (state) => {\r\n                state.loading = false;\r\n                state.exams = [];\r\n            })\r\n\r\n            .addCase(fetchPublicExamById.pending, (state) => {\r\n                state.exam = null;\r\n            })\r\n            .addCase(fetchPublicExamById.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exam = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(saveExamForUser.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    const { examId, isSave } = action.payload;\r\n\r\n                    // Trường hợp 1: Khi exams chứa đối tượng có thuộc tính exam (từ API saved exams)\r\n                    if (state.exams.length > 0 && state.exams[0]?.exam) {\r\n                        if (isSave === false) {\r\n                            // Nếu isSave = false, loại bỏ exam khỏi danh sách đã lưu\r\n                            state.exams = state.exams.filter(exam => exam.exam?.id !== examId);\r\n                        } else {\r\n                            // Nếu isSave = true, cập nhật trạng thái\r\n                            state.exams = state.exams.map(exam => {\r\n                                if (exam.exam?.id === examId) {\r\n                                    return { ...exam, isSave: true };\r\n                                }\r\n                                return exam;\r\n                            });\r\n                        }\r\n                        return;\r\n                    }\r\n\r\n                    // Trường hợp 2: Khi exams chứa danh sách exam thông thường\r\n                    if (state.exams && Array.isArray(state.exams)) {\r\n                        state.exams = state.exams.map(exam => {\r\n                            if (exam.id === examId) {\r\n                                return { ...exam, isSave: isSave };\r\n                            }\r\n                            return exam;\r\n                        });\r\n                    }\r\n\r\n                    // Cập nhật trạng thái cho exam hiện tại nếu có\r\n                    if (state.exam) {\r\n                        state.exam.isSave = isSave;\r\n                    }\r\n                }\r\n            })\r\n            .addCase(uploadSolutionPdf.pending, (state) => {\r\n                state.loadingUpload2 = true;\r\n            })\r\n            .addCase(uploadSolutionPdf.fulfilled, (state, action) => {\r\n                // console.log(action.payload)\r\n                const pdfUrl = action.payload\r\n                if (state.exam) {\r\n                    state.exam.solutionPdfUrl = pdfUrl;\r\n                }\r\n                state.loadingUpload2 = false;\r\n            })\r\n            .addCase(uploadSolutionPdf.rejected, (state) => {\r\n                state.loadingUpload1 = false;\r\n            })\r\n            .addCase(uploadExamPdf.pending, (state) => {\r\n                state.loadingUpload1 = true;\r\n            })\r\n            .addCase(uploadExamPdf.fulfilled, (state, action) => {\r\n                const pdfUrl = action.payload;\r\n                if (state.exam) {\r\n                    state.exam.fileUrl = pdfUrl;\r\n                }\r\n                state.loadingUpload1 = false;\r\n            })\r\n            .addCase(uploadExamPdf.rejected, (state) => {\r\n                state.loadingUpload1 = false;\r\n            })\r\n\r\n            .addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\r\n                if (action.payload && action.payload.data) {\r\n                    state.exams = action.payload.data;\r\n                }\r\n            })\r\n        // .addCase(summitExam.pending, (state) => {\r\n        //     state.loadingSubmit = true;\r\n        // })\r\n        // .addCase(summitExam.fulfilled, (state, action) => {\r\n        //     state.loadingSubmit = false;\r\n        //     state.isSubmit = true;\r\n        // })\r\n        // .addCase(summitExam.rejected, (state) => {\r\n        //     state.loadingSubmit = false;\r\n        //     state.isSubmit = false;\r\n        // })\r\n    }\r\n});\r\n\r\nexport const {\r\n    setExam,\r\n    setCurrentPage,\r\n    setLimit,\r\n    setSortOrder,\r\n    setLoading,\r\n    setSearch\r\n} = examSlice.actions;\r\nexport default examSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD;AACA,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAE5E,OAAO,MAAMC,UAAU,GAAGP,gBAAgB,CACtC,kBAAkB,EAClB,OAAAQ,IAAA,EAAAC,KAAA,KAAmE;EAAA,IAA5D;IAAEC,MAAM;IAAEC,WAAW;IAAEC,KAAK;IAAEC;EAAU,CAAC,GAAAL,IAAA;EAAA,IAAE;IAAEM;EAAS,CAAC,GAAAL,KAAA;EAC1D,OAAO,MAAMP,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACc,aAAa,EAAE;IAAEL,MAAM;IAAEC,WAAW;IAAEC,KAAK;IAAEC;EAAU,CAAC,EAAGG,IAAI,IAAK;IAC1G;IACA;IACA;EAAA,CACH,EAAE,IAAI,EAAE,KAAK,CAAC;AACnB,CACJ,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGjB,gBAAgB,CAC5C,wBAAwB,EACxB,OAAOkB,CAAC,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEL;EAAS,CAAC,GAAAK,KAAA;EAClB,OAAO,MAAMjB,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACmB,gBAAgB,EAAE,IAAI,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC9F,CACJ,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGrB,gBAAgB,CAC1C,sBAAsB,EACtB,OAAOkB,CAAC,EAAAI,KAAA,KAAmB;EAAA,IAAjB;IAAER;EAAS,CAAC,GAAAQ,KAAA;EAClB,OAAO,MAAMpB,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACsB,gBAAgB,EAAE,IAAI,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC9F,CACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGxB,gBAAgB,CACzC,qBAAqB,EACrB,OAAOyB,EAAE,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEZ;EAAS,CAAC,GAAAY,KAAA;EACnB,OAAO,MAAMxB,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACuB,aAAa,EAAEC,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACtG,CACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAME,gBAAgB,GAAG3B,gBAAgB,CAC5C,wBAAwB,EACxB,OAAOgB,IAAI,EAAAY,KAAA,KAAmB;EAAA,IAAjB;IAAEd;EAAS,CAAC,GAAAc,KAAA;EACrB,OAAO,MAAM1B,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC4B,mBAAmB,EAAEb,IAAI,EAAGA,IAAI,IAAK;IAC3E;IACA;IACA;IACA;EAAA,CACH,EAAE,KAAK,EAAE,KAAK,CAAC;AACpB,CACJ,CAAC;AAED,OAAO,MAAMc,iBAAiB,GAAG9B,gBAAgB,CAC7C,yBAAyB,EACzB,OAAOyB,EAAE,EAAAM,KAAA,KAAmB;EAAA,IAAjB;IAAEjB;EAAS,CAAC,GAAAiB,KAAA;EACnB,OAAO,MAAM7B,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC+B,iBAAiB,EAAEP,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC7F,CACJ,CAAC;;AAED;AACA,OAAO,MAAMQ,yBAAyB,GAAGjC,gBAAgB,CACrD,iCAAiC,EACjC,OAAOyB,EAAE,EAAAS,KAAA,KAA6B;EAAA,IAA3B;IAAEpB,QAAQ;IAAEqB;EAAS,CAAC,GAAAD,KAAA;EAC7B,MAAME,KAAK,GAAGD,QAAQ,CAAC,CAAC;EACxB,MAAM;IAAEE,YAAY;IAAEC;EAAwB,CAAC,GAAGF,KAAK,CAACG,KAAK;;EAE7D;EACA,MAAMC,OAAO,GAAGH,YAAY,CAACZ,EAAE,CAAC,IAAIY,YAAY,CAACZ,EAAE,CAAC,CAACgB,MAAM,GAAG,CAAC;;EAE/D;EACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;EACtB,MAAME,WAAW,GAAGN,uBAAuB,CAACb,EAAE,CAAC,IAAI,CAAC;EACpD,MAAMoB,YAAY,GAAGH,GAAG,GAAGE,WAAW,GAAG,MAAM;;EAE/C;EACA,IAAIJ,OAAO,IAAIK,YAAY,EAAE;IACzB;IACA,OAAO;MAAE7B,IAAI,EAAEqB,YAAY,CAACZ,EAAE;IAAE,CAAC;EACrC;;EAEA;EACA,OAAO,MAAMX,QAAQ,CAACgB,iBAAiB,CAACL,EAAE,CAAC,CAAC,CAACqB,MAAM,CAAC,CAAC;AACzD,CACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG/C,gBAAgB,CACzC,qBAAqB,EACrB,OAAOyB,EAAE,EAAAuB,KAAA,KAAmB;EAAA,IAAjB;IAAElC;EAAS,CAAC,GAAAkC,KAAA;EACnB,OAAO,MAAM9C,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACgD,cAAc,EAAExB,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACzF,CACJ,CAAC;AAED,OAAO,MAAMyB,SAAS,GAAGlD,gBAAgB,CACrC,iBAAiB,EACjB,OAAOU,MAAM,EAAAyC,MAAA,KAAmB;EAAA,IAAjB;IAAErC;EAAS,CAAC,GAAAqC,MAAA;EACvB,OAAO,MAAMjD,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACmD,YAAY,EAAE1C,MAAM,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAC1G,CACJ,CAAC;AAED,OAAO,MAAM2C,mBAAmB,GAAGrD,gBAAgB,CAC/C,2BAA2B,EAC3B,OAAOyB,EAAE,EAAA6B,MAAA,KAAmB;EAAA,IAAjB;IAAExC;EAAS,CAAC,GAAAwC,MAAA;EACnB,OAAO,MAAMpD,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACsD,aAAa,EAAE9B,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACzF,CACJ,CAAC;AAED,OAAO,MAAM+B,OAAO,GAAGxD,gBAAgB,CACnC,eAAe,EACf,OAAAyD,MAAA,EAAAC,MAAA,KAA8C;EAAA,IAAvC;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAAH,MAAA;EAAA,IAAE;IAAE3C;EAAS,CAAC,GAAA4C,MAAA;EACrC,OAAO,MAAMxD,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC4D,UAAU,EAAE;IAAEF,MAAM;IAAEC;EAAS,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACvG,CACJ,CAAC;AAED,OAAO,MAAME,YAAY,GAAG9D,gBAAgB,CACxC,oBAAoB,EACpB,OAAA+D,MAAA,EAAAC,MAAA,KAA+C;EAAA,IAAxC;IAAEL,MAAM;IAAEM;EAAU,CAAC,GAAAF,MAAA;EAAA,IAAE;IAAEjD;EAAS,CAAC,GAAAkD,MAAA;EACtC,OAAO,MAAM9D,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACiE,eAAe,EAAE;IAAEP,MAAM;IAAEM;EAAU,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AAC7G,CACJ,CAAC;AAID,OAAO,MAAME,iBAAiB,GAAGnE,gBAAgB,CAC7C,yBAAyB,EACzB,OAAAoE,MAAA,EAAAC,MAAA,KAA6C;EAAA,IAAtC;IAAEV,MAAM;IAAEW;EAAQ,CAAC,GAAAF,MAAA;EAAA,IAAE;IAAEtD;EAAS,CAAC,GAAAuD,MAAA;EACpC,OAAO,MAAMnE,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACsE,oBAAoB,EAAE;IAAEZ,MAAM;IAAEW;EAAQ,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AAC7H,CACJ,CAAC;AAED,OAAO,MAAME,aAAa,GAAGxE,gBAAgB,CACzC,qBAAqB,EACrB,OAAAyE,MAAA,EAAAC,MAAA,KAA6C;EAAA,IAAtC;IAAEf,MAAM;IAAEW;EAAQ,CAAC,GAAAG,MAAA;EAAA,IAAE;IAAE3D;EAAS,CAAC,GAAA4D,MAAA;EACpC,OAAO,MAAMxE,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC0E,gBAAgB,EAAE;IAAEhB,MAAM;IAAEW;EAAQ,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AACzH,CACJ,CAAC;AAED,OAAO,MAAMM,eAAe,GAAG5E,gBAAgB,CAC3C,uBAAuB,EACvB,OAAA6E,MAAA,EAAAC,MAAA,KAAoC;EAAA,IAA7B;IAAEnB;EAAO,CAAC,GAAAkB,MAAA;EAAA,IAAE;IAAE/D;EAAS,CAAC,GAAAgE,MAAA;EAC3B,OAAO,MAAM5E,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC8E,kBAAkB,EAAE;IAAEpB;EAAO,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACnH,CACJ,CAAC;AAED,OAAO,MAAMqB,UAAU,GAAGhF,gBAAgB,CACtC,kBAAkB,EAClB,OAAOyB,EAAE,EAAAwD,MAAA,KAAmB;EAAA,IAAjB;IAAEnE;EAAS,CAAC,GAAAmE,MAAA;EACnB,OAAO,MAAM/E,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACiF,aAAa,EAAEzD,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACxF,CACJ,CAAC;AAED,MAAM0D,SAAS,GAAGpF,WAAW,CAAC;EAC1BqF,IAAI,EAAE,OAAO;EACbC,YAAY,EAAE;IACV9C,KAAK,EAAE,EAAE;IACT+C,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,EAAE;IACflD,YAAY,EAAE,CAAC,CAAC;IAAE;IAClBC,uBAAuB,EAAE,CAAC,CAAC;IAAE;IAC7BkD,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,KAAK;IACrBC,cAAc,EAAE,KAAK;IACrBC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;MAAE,GAAG1F;IAAuB,CAAC;IACzC,GAAGE;EACP,CAAC;EACDyF,QAAQ,EAAE;IACNC,OAAO,EAAEA,CAAC3D,KAAK,EAAE4D,MAAM,KAAK;MACxB5D,KAAK,CAACkD,IAAI,GAAGU,MAAM,CAACC,OAAO;IAC/B,CAAC;IACD,GAAG7F,kBAAkB;IACrB,GAAGE;EACP,CAAC;EACD4F,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC7F,UAAU,CAAC8F,OAAO,EAAGjE,KAAK,IAAK;MACpCA,KAAK,CAACG,KAAK,GAAG,EAAE;MAChBH,KAAK,CAACkE,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDF,OAAO,CAAC7F,UAAU,CAACgG,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MAC9C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB7D,KAAK,CAACG,KAAK,GAAGyD,MAAM,CAACC,OAAO,CAACjF,IAAI;QACjCoB,KAAK,CAACyD,UAAU,GAAGG,MAAM,CAACC,OAAO,CAACJ,UAAU,IAAI1F,sBAAsB;MAC1E;MACAiC,KAAK,CAACkE,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDF,OAAO,CAAC7F,UAAU,CAACiG,QAAQ,EAAGpE,KAAK,IAAK;MACrCA,KAAK,CAACkE,OAAO,GAAG,KAAK;MACrBlE,KAAK,CAACG,KAAK,GAAG,EAAE;IACpB,CAAC,CAAC,CAED6D,OAAO,CAACnF,gBAAgB,CAACoF,OAAO,EAAGjE,KAAK,IAAK;MAC1CA,KAAK,CAACG,KAAK,GAAG,EAAE;IACpB,CAAC,CAAC,CACD6D,OAAO,CAACnF,gBAAgB,CAACsF,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MACpD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB7D,KAAK,CAACG,KAAK,GAAGyD,MAAM,CAACC,OAAO,CAACjF,IAAI;MACrC;IACJ,CAAC,CAAC,CACDoF,OAAO,CAAC/E,cAAc,CAACgF,OAAO,EAAGjE,KAAK,IAAK;MACxCA,KAAK,CAACG,KAAK,GAAG,EAAE;MAChBH,KAAK,CAACoD,WAAW,GAAG,IAAI;IAC5B,CAAC,CAAC,CACDY,OAAO,CAAC/E,cAAc,CAACkF,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MAClD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB7D,KAAK,CAACG,KAAK,GAAGyD,MAAM,CAACC,OAAO,CAACjF,IAAI;MACrC;MACAoB,KAAK,CAACoD,WAAW,GAAG,KAAK;IAC7B,CAAC,CAAC,CACDY,OAAO,CAAC/E,cAAc,CAACmF,QAAQ,EAAGpE,KAAK,IAAK;MACzCA,KAAK,CAACoD,WAAW,GAAG,KAAK;IAC7B,CAAC,CAAC,CACDY,OAAO,CAACtE,iBAAiB,CAACuE,OAAO,EAAE,MAAM;MACtC;IAAA,CACH,CAAC,CACDD,OAAO,CAACtE,iBAAiB,CAACyE,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MACrD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB,MAAMtC,MAAM,GAAGqC,MAAM,CAACS,IAAI,CAACC,GAAG,CAAC,CAAC;QAChCtE,KAAK,CAACG,KAAK,GAAGyD,MAAM,CAACC,OAAO,CAACjF,IAAI;QACjCoB,KAAK,CAACC,YAAY,CAACsB,MAAM,CAAC,GAAGqC,MAAM,CAACC,OAAO,CAACjF,IAAI;QAChDoB,KAAK,CAACE,uBAAuB,CAACqB,MAAM,CAAC,GAAGhB,IAAI,CAACD,GAAG,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC,CACD0D,OAAO,CAACrD,aAAa,CAACsD,OAAO,EAAGjE,KAAK,IAAK;MACvCA,KAAK,CAACkD,IAAI,GAAG,IAAI;IACrB,CAAC,CAAC,CACDc,OAAO,CAACrD,aAAa,CAACwD,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MACjD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB7D,KAAK,CAACkD,IAAI,GAAGU,MAAM,CAACC,OAAO,CAACjF,IAAI;MACpC;IACJ,CAAC,CAAC,CACDoF,OAAO,CAAClD,SAAS,CAACmD,OAAO,EAAGjE,KAAK,IAAK;MACnCA,KAAK,CAACmD,WAAW,GAAG,EAAE;IAC1B,CAAC,CAAC,CACDa,OAAO,CAAClD,SAAS,CAACqD,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MAC7C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB7D,KAAK,CAACmD,WAAW,GAAGS,MAAM,CAACC,OAAO,CAACjF,IAAI;MAC3C;IACJ,CAAC,CAAC,CAEDoF,OAAO,CAACzE,gBAAgB,CAAC0E,OAAO,EAAGjE,KAAK,IAAK;MAC1CA,KAAK,CAACG,KAAK,GAAG,EAAE;MAChBH,KAAK,CAACkE,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDF,OAAO,CAACzE,gBAAgB,CAAC4E,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MACpD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB7D,KAAK,CAACG,KAAK,GAAGyD,MAAM,CAACC,OAAO,CAACjF,IAAI;QACjCoB,KAAK,CAACyD,UAAU,GAAGG,MAAM,CAACC,OAAO,CAACJ,UAAU,IAAI1F,sBAAsB;MAC1E;MACAiC,KAAK,CAACkE,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDF,OAAO,CAACzE,gBAAgB,CAAC6E,QAAQ,EAAGpE,KAAK,IAAK;MAC3CA,KAAK,CAACkE,OAAO,GAAG,KAAK;MACrBlE,KAAK,CAACG,KAAK,GAAG,EAAE;IACpB,CAAC,CAAC,CAED6D,OAAO,CAAC/C,mBAAmB,CAACgD,OAAO,EAAGjE,KAAK,IAAK;MAC7CA,KAAK,CAACkD,IAAI,GAAG,IAAI;IACrB,CAAC,CAAC,CACDc,OAAO,CAAC/C,mBAAmB,CAACkD,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MACvD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB7D,KAAK,CAACkD,IAAI,GAAGU,MAAM,CAACC,OAAO,CAACjF,IAAI;MACpC;IACJ,CAAC,CAAC,CACDoF,OAAO,CAACxB,eAAe,CAAC2B,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MACnD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAAA,IAAAU,aAAA;QAChB,MAAM;UAAEhD,MAAM;UAAEiD;QAAO,CAAC,GAAGZ,MAAM,CAACC,OAAO;;QAEzC;QACA,IAAI7D,KAAK,CAACG,KAAK,CAACE,MAAM,GAAG,CAAC,KAAAkE,aAAA,GAAIvE,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,cAAAoE,aAAA,eAAdA,aAAA,CAAgBrB,IAAI,EAAE;UAChD,IAAIsB,MAAM,KAAK,KAAK,EAAE;YAClB;YACAxE,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAACsE,MAAM,CAACvB,IAAI;cAAA,IAAAwB,UAAA;cAAA,OAAI,EAAAA,UAAA,GAAAxB,IAAI,CAACA,IAAI,cAAAwB,UAAA,uBAATA,UAAA,CAAWrF,EAAE,MAAKkC,MAAM;YAAA,EAAC;UACtE,CAAC,MAAM;YACH;YACAvB,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAACwE,GAAG,CAACzB,IAAI,IAAI;cAAA,IAAA0B,WAAA;cAClC,IAAI,EAAAA,WAAA,GAAA1B,IAAI,CAACA,IAAI,cAAA0B,WAAA,uBAATA,WAAA,CAAWvF,EAAE,MAAKkC,MAAM,EAAE;gBAC1B,OAAO;kBAAE,GAAG2B,IAAI;kBAAEsB,MAAM,EAAE;gBAAK,CAAC;cACpC;cACA,OAAOtB,IAAI;YACf,CAAC,CAAC;UACN;UACA;QACJ;;QAEA;QACA,IAAIlD,KAAK,CAACG,KAAK,IAAI0E,KAAK,CAACC,OAAO,CAAC9E,KAAK,CAACG,KAAK,CAAC,EAAE;UAC3CH,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAACwE,GAAG,CAACzB,IAAI,IAAI;YAClC,IAAIA,IAAI,CAAC7D,EAAE,KAAKkC,MAAM,EAAE;cACpB,OAAO;gBAAE,GAAG2B,IAAI;gBAAEsB,MAAM,EAAEA;cAAO,CAAC;YACtC;YACA,OAAOtB,IAAI;UACf,CAAC,CAAC;QACN;;QAEA;QACA,IAAIlD,KAAK,CAACkD,IAAI,EAAE;UACZlD,KAAK,CAACkD,IAAI,CAACsB,MAAM,GAAGA,MAAM;QAC9B;MACJ;IACJ,CAAC,CAAC,CACDR,OAAO,CAACjC,iBAAiB,CAACkC,OAAO,EAAGjE,KAAK,IAAK;MAC3CA,KAAK,CAACuD,cAAc,GAAG,IAAI;IAC/B,CAAC,CAAC,CACDS,OAAO,CAACjC,iBAAiB,CAACoC,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MACrD;MACA,MAAMmB,MAAM,GAAGnB,MAAM,CAACC,OAAO;MAC7B,IAAI7D,KAAK,CAACkD,IAAI,EAAE;QACZlD,KAAK,CAACkD,IAAI,CAAC8B,cAAc,GAAGD,MAAM;MACtC;MACA/E,KAAK,CAACuD,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CACDS,OAAO,CAACjC,iBAAiB,CAACqC,QAAQ,EAAGpE,KAAK,IAAK;MAC5CA,KAAK,CAACsD,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CACDU,OAAO,CAAC5B,aAAa,CAAC6B,OAAO,EAAGjE,KAAK,IAAK;MACvCA,KAAK,CAACsD,cAAc,GAAG,IAAI;IAC/B,CAAC,CAAC,CACDU,OAAO,CAAC5B,aAAa,CAAC+B,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MACjD,MAAMmB,MAAM,GAAGnB,MAAM,CAACC,OAAO;MAC7B,IAAI7D,KAAK,CAACkD,IAAI,EAAE;QACZlD,KAAK,CAACkD,IAAI,CAAC+B,OAAO,GAAGF,MAAM;MAC/B;MACA/E,KAAK,CAACsD,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CACDU,OAAO,CAAC5B,aAAa,CAACgC,QAAQ,EAAGpE,KAAK,IAAK;MACxCA,KAAK,CAACsD,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CAEDU,OAAO,CAACnE,yBAAyB,CAACsE,SAAS,EAAE,CAACnE,KAAK,EAAE4D,MAAM,KAAK;MAC7D,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAACjF,IAAI,EAAE;QACvCoB,KAAK,CAACG,KAAK,GAAGyD,MAAM,CAACC,OAAO,CAACjF,IAAI;MACrC;IACJ,CAAC,CAAC;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACT+E,OAAO;EACPuB,cAAc;EACdC,QAAQ;EACRC,YAAY;EACZC,UAAU;EACVC;AACJ,CAAC,GAAGvC,SAAS,CAACwC,OAAO;AACrB,eAAexC,SAAS,CAACyC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}