// components/loading/LoadingCommentItem.jsx
const LoadingCommentItem = () => {
    return (
        <div className="flex items-start gap-3 animate-pulse">
            {/* Avatar skeleton */}
            <div className="w-8 h-8 bg-gray-300 rounded-full" />

            {/* Content skeleton */}
            <div className="flex-1 space-y-2">
                <div className="w-24 h-3 bg-gray-300 rounded-md" />
                <div className="w-full h-4 bg-gray-200 rounded-md" />
                <div className="w-2/3 h-4 bg-gray-200 rounded-md" />
            </div>
        </div>
    );
};

export default LoadingCommentItem;
