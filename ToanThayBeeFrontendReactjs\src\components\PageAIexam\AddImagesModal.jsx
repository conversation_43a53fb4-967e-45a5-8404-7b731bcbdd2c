import React, { useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { X, Upload, Image as ImageIcon, Trash2 } from "lucide-react";
import { setShowAddImagesModal } from "../../features/examAI/examAISlice";
import { uploadMultipleImages } from "../../features/image/imageSlice";

const AddImagesModal = () => {
    const dispatch = useDispatch();
    const { showAddImagesModal } = useSelector(state => state.examAI);
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [previewUrls, setPreviewUrls] = useState([]);
    const [pasteIndicator, setPasteIndicator] = useState(false);
    const fileInputRef = useRef(null);
    const { images, loadingUploadImages } = useSelector(state => state.images);

    const handleClose = () => {
        dispatch(setShowAddImagesModal(false));
        setSelectedFiles([]);
        setPreviewUrls([]);
    };

    const handleFileSelect = (e) => {
        const files = Array.from(e.target.files);

        // Limit to 10 files
        const limitedFiles = files.slice(0, 10);

        setSelectedFiles(limitedFiles);

        // Create preview URLs
        const urls = limitedFiles.map(file => URL.createObjectURL(file));
        setPreviewUrls(urls);
    };

    const handleRemoveFile = (index) => {
        const newFiles = selectedFiles.filter((_, i) => i !== index);
        const newUrls = previewUrls.filter((_, i) => i !== index);

        // Revoke the removed URL to prevent memory leaks
        URL.revokeObjectURL(previewUrls[index]);

        setSelectedFiles(newFiles);
        setPreviewUrls(newUrls);
    };

    const handleUpload = async () => {
        if (selectedFiles.length === 0) return;

        try {
            await dispatch(uploadMultipleImages({ files: selectedFiles, folder: 'ImageFormN8N' })).unwrap();
            // Clear selected files after successful upload
            setSelectedFiles([]);
            setPreviewUrls([]);
            // Reset file input
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        } catch (error) {
            console.error('Upload failed:', error);
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();

        const files = Array.from(e.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));
        const limitedFiles = imageFiles.slice(0, 10);

        setSelectedFiles(limitedFiles);

        const urls = limitedFiles.map(file => URL.createObjectURL(file));
        setPreviewUrls(urls);
    };

    const handlePaste = (e) => {
        e.preventDefault();

        const items = Array.from(e.clipboardData.items);
        const imageItems = items.filter(item => item.type.startsWith('image/'));

        if (imageItems.length === 0) return;

        const files = [];
        const urls = [];

        imageItems.forEach((item, index) => {
            const file = item.getAsFile();
            if (file) {
                // Create a new file with a proper name
                const timestamp = Date.now();
                const newFile = new File([file], `pasted-image-${timestamp}-${index}.${file.type.split('/')[1]}`, {
                    type: file.type
                });

                files.push(newFile);
                urls.push(URL.createObjectURL(newFile));
            }
        });

        if (files.length > 0) {
            // Limit to 10 files total
            const limitedFiles = files.slice(0, 10);
            const limitedUrls = urls.slice(0, 10);

            setSelectedFiles(prev => [...prev, ...limitedFiles].slice(0, 10));
            setPreviewUrls(prev => [...prev, ...limitedUrls].slice(0, 10));

            // Show paste indicator
            setPasteIndicator(true);
            setTimeout(() => setPasteIndicator(false), 2000);
        }
    };

    // Add paste event listener when modal is open
    useEffect(() => {
        if (showAddImagesModal) {
            const handleGlobalPaste = (e) => handlePaste(e);
            document.addEventListener('paste', handleGlobalPaste);

            return () => {
                document.removeEventListener('paste', handleGlobalPaste);
            };
        }
    }, [showAddImagesModal]);

    if (!showAddImagesModal) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[61]">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b">
                    <div className="flex items-center gap-3">
                        <h2 className="text-xl font-semibold text-gray-800">Thêm ảnh</h2>
                        {pasteIndicator && (
                            <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium animate-pulse">
                                ✓ Đã paste ảnh
                            </div>
                        )}
                    </div>
                    <button
                        onClick={handleClose}
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                        <X className="w-5 h-5" />
                    </button>
                </div>

                {/* Content */}
                <div className="p-6 overflow-y-auto max-h-[calc(90vh-160px)]">
                    {/* Upload Area */}
                    <div className="mb-6">
                        <div
                            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
                            onDragOver={handleDragOver}
                            onDragEnter={handleDragEnter}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                        >
                            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <p className="text-lg font-medium text-gray-700 mb-2">
                                Kéo thả, paste (Ctrl+V) hoặc click để chọn ảnh
                            </p>
                            <p className="text-sm text-gray-500 mb-4">
                                Tối đa 10 ảnh, định dạng JPG, PNG, GIF
                            </p>
                            <input
                                ref={fileInputRef}
                                type="file"
                                multiple
                                accept="image/*"
                                onChange={handleFileSelect}
                                className="hidden"
                            />
                            <button
                                onClick={() => fileInputRef.current?.click()}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
                            >
                                Chọn ảnh
                            </button>
                        </div>
                    </div>

                    {/* Selected Files Preview */}
                    {selectedFiles.length > 0 && (
                        <div className="mb-6">
                            <h3 className="text-lg font-medium text-gray-800 mb-3">
                                Ảnh đã chọn ({selectedFiles.length}/10)
                            </h3>
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                {previewUrls.map((url, index) => (
                                    <div key={index} className="relative group">
                                        <img
                                            src={url}
                                            alt={`Preview ${index + 1}`}
                                            className="w-full h-32 object-cover rounded-lg border"
                                        />
                                        <button
                                            onClick={() => handleRemoveFile(index)}
                                            className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                        </button>
                                        <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                                            {selectedFiles[index].name.length > 15
                                                ? selectedFiles[index].name.substring(0, 15) + '...'
                                                : selectedFiles[index].name
                                            }
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Existing Images */}
                    <div>
                        <h3 className="text-lg font-medium text-gray-800 mb-3">
                            Ảnh đã tải lên ({images.ImageFormN8N.length})
                        </h3>
                        {images.ImageFormN8N.length > 0 ? (
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                {images.ImageFormN8N.map((image, index) => (
                                    <div key={index} className="relative group">
                                        <img
                                            src={image.ImageFormN8N || image.url || image}
                                            alt={`Uploaded ${index + 1}`}
                                            className="w-full h-32 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity"
                                        />
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8 text-gray-500">
                                <ImageIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
                                <p>Chưa có ảnh nào được tải lên</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between p-6 border-t bg-gray-50">
                    <div className="text-sm text-gray-600">
                        {selectedFiles.length > 0 && `${selectedFiles.length} ảnh đã chọn`}
                    </div>
                    <div className="flex gap-3">
                        <button
                            onClick={handleClose}
                            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                        >
                            Đóng
                        </button>
                        {selectedFiles.length > 0 && (
                            <button
                                onClick={handleUpload}
                                disabled={loadingUploadImages}
                                className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-lg transition-colors flex items-center gap-2"
                            >
                                {loadingUploadImages ? (
                                    <>
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                        Đang tải lên...
                                    </>
                                ) : (
                                    <>
                                        <Upload className="w-4 h-4" />
                                        Tải lên
                                    </>
                                )}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AddImagesModal;
