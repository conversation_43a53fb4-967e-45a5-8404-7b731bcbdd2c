import db from '../../models/index.js';
import { EVENTS, ROOMS } from '../constants.js';
import * as notificationService from '../../services/notification.service.js';

// Map to store user socket connections
const userSocketMap = new Map();

/**
 * Register a user's socket connection
 * @param {string} userId - User ID
 * @param {Object} socket - Socket.io socket
 */
export const registerUserSocket = (userId, socket) => {
  if (!userId) return;

  const userIdStr = userId.toString();
  const currentSocketId = userSocketMap.get(userIdStr);

  // Only register if this is a new socket or different from the current one
  if (currentSocketId !== socket.id) {
    // Store the socket connection for this user
    userSocketMap.set(userIdStr, socket.id);

    // Join user-specific room
    socket.join(`user-${userId}`);
    console.log(`👤 User ${userId} registered with socket ${socket.id}`);
  }
};
/**
 * Handle user authentication and socket registration
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleUserAuthentication = (socket, io, { userId, token }) => {
  try {
    // In a real implementation, you would verify the token here
    // For now, we'll just register the user socket
    registerUserSocket(userId, socket);

    // Emit success event
    socket.emit(EVENTS.USER_AUTHENTICATED, {
      success: true,
      message: 'Socket connection authenticated successfully'
    });

    // Get unread notification count
    notificationService.getUnreadNotificationCount(userId)
      .then(count => {
        socket.emit(EVENTS.NOTIFICATION_COUNT, { count });
        
      })
      .catch(error => {
        console.error('Error getting notification count:', error);
      });

  } catch (error) {
    console.error('Error authenticating user socket:', error);
    socket.emit(EVENTS.USER_AUTHENTICATED, {
      success: false,
      message: 'Failed to authenticate socket connection'
    });
  }
};

/**
 * Send a notification to a specific user
 * @param {Object} io - Socket.io server instance
 * @param {string} userId - User ID
 * @param {Object} notification - Notification data
 */
export const sendNotificationToUser = async (io, userId, notification) => {
  try {
    // Save notification to database
    const savedNotification = await notificationService.createNotification({
      userId,
      title: notification.title,
      content: notification.content,
      type: notification.type || 'SYSTEM',
      relatedId: notification.relatedId,
      relatedType: notification.relatedType,
      actionUrl: notification.actionUrl,
      isRead: false
    });

    // Get unread count
    const unreadCount = await notificationService.getUnreadNotificationCount(userId);

    // Emit to user's room
    io.to(`user-${userId}`).emit(EVENTS.NEW_NOTIFICATION, {
      notification: savedNotification,
      unreadCount
    });

    return savedNotification;
  } catch (error) {
    console.error('Error sending notification to user:', error);
    throw error;
  }
};

/**
 * Send a notification to multiple users
 * @param {Object} io - Socket.io server instance
 * @param {Array} userIds - Array of user IDs
 * @param {Object} notification - Notification data
 */
export const sendNotificationToUsers = async (io, userIds, notification) => {
  try {
    // Create notifications for all users
    const notifications = await notificationService.createNotificationsForUsers(
      userIds,
      {
        title: notification.title,
        content: notification.content,
        type: notification.type || 'SYSTEM',
        relatedId: notification.relatedId,
        relatedType: notification.relatedType,
        actionUrl: notification.actionUrl,
        isRead: false
      }
    );

    // Emit to each user
    for (const userId of userIds) {
      const unreadCount = await notificationService.getUnreadNotificationCount(userId);
      const userNotification = notifications.find(n => n.userId.toString() === userId.toString());

      if (userNotification) {
        io.to(`user-${userId}`).emit(EVENTS.NEW_NOTIFICATION, {
          notification: userNotification,
          unreadCount
        });
      }
    }

    return notifications;
  } catch (error) {
    console.error('Error sending notification to users:', error);
    throw error;
  }
};

/**
 * Handle mark notification as read event
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleMarkNotificationAsRead = async (socket, io, { userId, notificationId }) => {
  try {
    await notificationService.markNotificationsAsRead(userId, [notificationId]);
    const unreadCount = await notificationService.getUnreadNotificationCount(userId);

    socket.emit(EVENTS.NOTIFICATION_MARKED_READ, {
      success: true,
      notificationId,
      unreadCount
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    socket.emit(EVENTS.NOTIFICATION_MARKED_READ, {
      success: false,
      error: error.message
    });
  }
};

/**
 * Handle mark all notifications as read event
 * @param {Object} socket - Socket.io socket
 * @param {Object} io - Socket.io server instance
 * @param {Object} data - Event data
 */
export const handleMarkAllNotificationsAsRead = async (socket, io, { userId }) => {
  try {
    await notificationService.markAllNotificationsAsRead(userId);

    socket.emit(EVENTS.ALL_NOTIFICATIONS_MARKED_READ, {
      success: true,
      unreadCount: 0
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    socket.emit(EVENTS.ALL_NOTIFICATIONS_MARKED_READ, {
      success: false,
      error: error.message
    });
  }
};

/**
 * Handle user disconnect
 * @param {string} socketId - Socket ID
 */
export const handleUserDisconnect = (socketId) => {
  // Remove user from userSocketMap
  for (const [userId, sid] of userSocketMap.entries()) {
    if (sid === socketId) {
      userSocketMap.delete(userId);
      console.log(`👤 User ${userId} disconnected (socket ${socketId})`);
      break;
    }
  }
};
