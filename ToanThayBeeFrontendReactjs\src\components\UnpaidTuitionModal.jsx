import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { X, AlertCircle, CreditCard, Calendar, FileText, CheckCircle, DollarSign } from 'lucide-react';
import PaymentModal from './PaymentModal';

const UnpaidTuitionModal = ({ isOpen, onClose, unpaidPayments }) => {
  const { user } = useSelector((state) => state.auth);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  if (!isOpen || !unpaidPayments || unpaidPayments.length === 0) return null;

  const handleOpenPaymentModal = (payment) => {
    // Format payment data for PaymentModal
    const paymentInfo = {
      id: payment.id,
      month: formatMonth(payment.month),
      amount: "<PERSON>ên hệ anh Triệu Minh để biết số tiền",
      note: payment.note || "<PERSON>hông có ghi chú",
      description: `${user.firstName} ${user.lastName} ${user.highSchool} HP_${formatMonth(payment.month).replace(' ', '_')}_${payment.id}`
    };

    setSelectedPayment(paymentInfo);
    setIsPaymentModalOpen(true);
  };

  const handleClosePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedPayment(null);
  };

  const formatMonth = (monthStr) => {
    if (!monthStr) return 'N/A';
    const [year, month] = monthStr.split('-');
    const monthNames = [
      'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
      'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const formatDate = (dateStr) => {
    if (!dateStr) return 'Chưa có hạn';
    return new Date(dateStr).toLocaleDateString('vi-VN');
  };

  // Helper functions similar to UserTuitionPaymentDetail.jsx
  const getStatusBadge = (isOverdue = true) => {
    return (
      <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
        Quá hạn
      </span>
    );
  };

  const getPaymentStatusIcon = (isOverdue = true) => {
    return (
      <div className="p-3 bg-red-100 rounded-full">
        <AlertCircle className="w-6 h-6 text-red-600" />
      </div>
    );
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          {/* Header - Similar to UserTuitionPaymentDetail */}
          <div className="flex justify-between items-center p-4 sm:p-6 border-b  flex-shrink-0">
            <div className="flex items-center gap-3">
              <div className="p-2  rounded-full">
                <AlertCircle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h2 className="text-xl sm:text-2xl font-bold  flex items-center gap-2">
                  <CreditCard className="" />
                  Thông báo học phí quá hạn
                </h2>
                <p className="text-sm ">
                  Bạn có {unpaidPayments.length} khoản học phí chưa thanh toán
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 transition-colors p-1"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content - Scrollable */}
          <div className="p-4 sm:p-6 overflow-y-auto flex-1">
            <div className="space-y-4">
              {unpaidPayments.map((payment) => (
                <div
                  key={payment.id}
                  className="border border-red-200 rounded-lg p-4 bg-red-50 hover:bg-red-100 transition-colors"
                >
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <CreditCard className="w-5 h-5 text-red-600" />
                        <h3 className="font-semibold text-red-800">
                          Học phí {formatMonth(payment.month)}
                        </h3>
                        {payment.isOverdue && (
                          <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800 border border-red-200">
                            Quá hạn
                          </span>
                        )}
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-gray-500" />
                          <span className="text-gray-600">
                            Hạn: {formatDate(payment.dueDate)}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <FileText className="w-4 h-4 text-gray-500" />
                          <span className="text-gray-600">
                            {payment.note || "Không có ghi chú"}
                          </span>
                        </div>
                      </div>
                    </div>

                    <button
                      onClick={() => handleOpenPaymentModal(payment)}
                      className="flex items-center gap-1 text-sm text-green-600 hover:text-green-700 px-3 py-1.5 bg-green-50 hover:bg-green-100 rounded-md transition-colors"
                      title="Thanh toán"
                    >
                      <FileText size={16} />
                      <span>Thanh toán</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium mb-1">Lưu ý quan trọng:</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Vui lòng thanh toán học phí đúng hạn để tránh ảnh hưởng đến việc học</li>
                    <li>Sau khi chuyển khoản, hãy chụp màn hình biên lai và gửi cho giáo viên</li>
                    <li>Liên hệ: 0399520768 (Zalo) để được hỗ trợ</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 sm:p-6 border-t bg-gray-50 flex justify-between items-center flex-shrink-0">
            <p className="text-sm text-gray-600">
              Tổng: <span className="font-semibold text-red-600">{unpaidPayments.length}</span> khoản chưa thanh toán
            </p>
            <button
              onClick={onClose}
              className="px-4 sm:px-6 py-2 sm:py-3 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm sm:text-base min-h-[44px] sm:min-h-0"
            >
              Đóng
            </button>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={handleClosePaymentModal}
        paymentInfo={selectedPayment}
      />
    </>
  );
};

export default UnpaidTuitionModal;
