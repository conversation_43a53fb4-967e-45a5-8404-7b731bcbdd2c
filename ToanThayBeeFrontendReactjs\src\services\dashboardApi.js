import api from "./api";

/**
 * Dashboard API services for admin statistics
 */

// Get overall dashboard statistics
export const getDashboardStatsAPI = async () => {
    return await api.get("/v1/admin/dashboard/stats");
};

// Get student statistics
export const getStudentStatsAPI = async (params = {}) => {
    return await api.get("/v1/admin/students/stats", { params });
};

// Get class statistics
export const getClassStatsAPI = async (params = {}) => {
    return await api.get("/v1/admin/classes/stats", { params });
};

// Get exam statistics
export const getExamStatsAPI = async (params = {}) => {
    return await api.get("/v1/admin/exams/stats", { params });
};

// Get question statistics
export const getQuestionStatsAPI = async (params = {}) => {
    return await api.get("/v1/admin/questions/stats", { params });
};

// Get article statistics
export const getArticleStatsAPI = async (params = {}) => {
    return await api.get("/v1/admin/articles/stats", { params });
};

// Get recent activities
export const getRecentActivitiesAPI = async (params = { limit: 10 }) => {
    return await api.get("/v1/admin/dashboard/activities", { params });
};

// Get system status
export const getSystemStatusAPI = async () => {
    return await api.get("/v1/admin/dashboard/system-status");
};
