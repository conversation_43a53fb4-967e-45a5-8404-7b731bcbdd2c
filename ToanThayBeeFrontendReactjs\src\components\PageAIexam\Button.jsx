import LoadingSpinner from "../loading/LoadingSpinner";

const Button = ({ onClick, children, type, p, loading, disabled }) => {
    const colorClasses = {
        primary: "bg-slate-600 hover:bg-slate-700",
        secondary: "bg-gray-600 hover:bg-gray-700",
        danger: "bg-red-600 hover:bg-red-700",
        none: "bg-transparent hover:bg-gray-100 text-gray-800",
        save: "bg-emerald-500 hover:bg-emerald-700",
        commit: "bg-orange-500 hover:bg-orange-700",
        disabled: "bg-gray-300 cursor-not-allowed"
    };
    return (
        <button
            onClick={onClick}
            className={`flex text-sm items-center gap-2 ${p ? p : "p-1"} rounded-[0.25rem] text-white ${colorClasses[type] || "bg-blue-600 hover:bg-blue-700"} transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
            disabled={disabled}
        >
            {loading && (
                <LoadingSpinner
                    minHeight="min-h-0"
                />
            )}
            {children}
        </button>
    );
};

export default Button;