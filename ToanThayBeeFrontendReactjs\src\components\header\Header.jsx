import { useSelector, useDispatch } from "react-redux";
import { useState, useRef, useEffect } from "react";
import { BeeMathLogo } from "../logo/BeeMathLogo";
import InputSearch from "../input/InputSearch";
import { logout } from "../../features/auth/authSlice";
import { motion, AnimatePresence } from "framer-motion";
import { useNavigate } from "react-router-dom";
import JoinClassModal from "../modal/JoinClassModal";
import StudentCardModal from "../modal/StudentCardModal";
import NotificationPanel from "../notification/NotificationPanel";
import { FileSearch, Bell, Search, Plus, School, MoreHorizontal, BookOpen, PencilLine, Library, LogOut, CreditCard, Calendar, Menu, X, User, UserPlus, Eye } from "lucide-react";
// import { socket } from "../../services/socket";
import { fetchUnreadCount, updateUnreadCount } from "../../features/notification/notificationSlice";
import ButtonHeader from "./ButtonHeader";
import OutsideClickWrapper from "../common/OutsideClickWrapper";
import { useLocation } from "react-router-dom";
import Tooltip from "../common/WrapperWithTooltip";
import { setOpenStudentCardModal } from "../../features/auth/authSlice";
import { setOpenJoinClassModal } from "../../features/class/classSlice";
import { findPublicExams } from "../../features/exam/examSlice";
import { findQuestions } from "../../features/question/questionSlice";
import useDebouncedEffect from "../../hooks/useDebouncedEffect";
import LoadingText from "../loading/LoadingText";

const ChoiceHeader = ({ title, route, Icon, className }) => {
    const location = useLocation();
    const navigate = useNavigate();
    const { user } = useSelector((state) => state.auth);
    const [isMobile, setIsMobile] = useState(window.innerWidth < 1000);

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 1000);
        };
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    const isActive = location.pathname === route || location.pathname.startsWith(route + "/");
    const isDisabledHome = (location.pathname.includes("/practice") || location.pathname.includes("/class")) && route === "/";
    const isChoice = isActive && !isDisabledHome;

    const handleClick = () => {
        if (user) {
            navigate(route);
        }
    };

    // 👉 Nếu không phải mobile (desktop)
    return (
        <div className={`relative flex items-center justify-start ${className}`}>
            <div
                onClick={handleClick}
                className={`flex flex-row gap-3 p-1 hover:bg-gray-100 text-slate-700 rounded text-xs font-['Be_Vietnam_Pro'] cursor-pointer
            ${isChoice ? 'font-extrabold ' : 'font-medium'}
            transition-colors duration-200`}
            >
                {Icon && <Icon size={18} />}
                {title}
            </div>
            {/* Nếu được chọn thì render div nằm dưới chữ */}
            {isChoice && (
                <div className="absolute -bottom-0 left-1/2 -translate-x-1/2 w-[100%] h-[2px] bg-sky-500 rounded-t-md"></div>
            )}

        </div>

    );
};

const LogoHeader = () => {

    return (
        <BeeMathLogo className="w-8 h-8" />
    )
}

const AvatarUser = ({ active = true, onClick }) => {
    const { user } = useSelector(state => state.auth);

    return (
        <div
            onClick={onClick}
            className={`relative cursor-pointer flex-shrink-0 w-8 h-8 flex justify-center items-center`}
        >
            {/* Vòng sóng */}
            {active && <div className={`absolute flex items-center justify-center w-full h-full rounded-full  border-2 ${user ? 'border-yellow-400 animate-wave' : ''}`}></div>}

            {/* Avatar */}
            <div className={`relative border border-gray-300 flex items-center justify-center w-full h-full rounded-full overflow-hidden`}>
                {user?.avatarUrl ? (
                    <div className="w-full h-full flex rounded-full overflow-hidden">
                        <img
                            src={user.avatarUrl}
                            alt="avatar"
                            className="w-full h-full object-cover"
                        />
                    </div>

                ) : (
                    <svg width="30" height="30" viewBox="0 0 40 40" fill="none">
                        <path
                            d="M20 2.5C10.335 2.5 2.5 10.335 2.5 20C2.5 29.665 10.335 37.5 20 37.5C29.665 37.5 37.5 29.665 37.5 20C37.5 10.335 29.665 2.5 20 2.5ZM20 22.5C16.6983 22.5 14.1667 19.88 14.1667 16.6667C14.1667 13.4533 16.6983 10.8333 20 10.8333C23.3017 10.8333 25.8333 13.4533 25.8333 16.6667C25.8333 19.88 23.3017 22.5 20 22.5ZM10 32.3C10.1 30.38 10.8 29.03 11.73 28.07C12.72 27.05 14 26.41 15.2 26.03C15.41 25.96 15.73 26.01 16.09 26.26C16.88 26.8 18.25 27.5 20 27.5C21.75 27.5 23.12 26.8 23.91 26.26C24.27 26.01 24.59 25.96 24.8 26.03C26 26.41 27.28 27.05 28.27 28.07C29.2 29.03 29.9 30.39 30 32.29C27.18 34.59 23.65 35.84 20 35.83C16.35 35.84 12.82 34.58 10 32.3Z"
                            fill="#94A3B8"
                        />
                    </svg>
                )}
            </div>
        </div>
    )
}

const UserName = ({ onClick }) => {
    const { user } = useSelector(state => state.auth);
    return (
        <div className="sm:block hidden">
            <HoverWrapper onClick={onClick}>
                <p className="text-sm font-semibold text-slate-900 whitespace-nowrap">
                    {user ? (
                        user?.lastName + " " + user?.firstName
                    ) : (
                        "Đăng nhập"
                    )}
                </p>
            </HoverWrapper>
        </div>
    )
}

const HoverWrapper = ({ children, onClick }) => {
    return (
        <button
            onClick={onClick}
            className="p-1 transition duration-200 hover:bg-gray-100 cursor-pointer rounded">
            {children}
        </button>
    );
};

const NotificationHeader = () => {
    const { user } = useSelector(state => state.auth);
    const [notificationOpen, setNotificationOpen] = useState(false);
    const notificationRef = useRef();
    return (
        <>
            {user && (
                <div className="relative" ref={notificationRef}>
                    <Tooltip title="Thông báo">
                        <button
                            onClick={() => setNotificationOpen(!notificationOpen)}
                            className="notificationRef relative p-[7px] text-gray-600 hover:text-sky-600 hover:bg-gray-100 rounded-md border border-gray-300 transition-colors"
                            title="Notifications"
                        >
                            <Bell size={16} />
                        </button>
                    </Tooltip>
                    <OutsideClickWrapper
                        ignoreOutsideClick="notificationRef"
                        onClickOutside={() => setNotificationOpen(false)}
                    >
                        <NotificationPanel
                            isOpen={notificationOpen}
                            onClose={() => setNotificationOpen(false)}
                        />
                    </OutsideClickWrapper>

                </div>
            )}
        </>
    )
}

const ClassHeader = ({ onClick }) => {
    return (
        <Tooltip title="Lớp học" >
            <button
                className="flex flex-row justify-center items-center relative  text-gray-600 hover:text-sky-600 hover:bg-gray-100 rounded-md border border-gray-300 transition-colors"
                title="Thêm lớp học"
                onClick={onClick}
            >
                <div className="p-[7px] border-r border-gray-300">
                    <School size={16} />
                </div>
                <div className="py-[7px]  px-[4px] border-gray-300">
                    <Plus size={16} />
                </div>
            </button>
        </Tooltip>

    )
}

const QuestionIdInput = ({ onClick }) => {
    return (
        <div
            onClick={onClick}
            className="flex items-center cursor-pointer border border-gray-300 rounded-md px-2 py-[7px] text-gray-600 focus-within:ring-2 focus-within:ring-sky-500">
            <Search size={16} />
            <input
                id="questionId"
                type="text"
                placeholder="Tìm kiếm đề thi và câu hỏi..."
                className="flex-1 pl-2 text-xs outline-none bg-transparent sm:block hidden"
            />
        </div>
    );
};

const RowPanel = ({ text, Icon, onClick }) => {
    return (
        <div
            onClick={onClick}
            className="flex flex-row items-center rounded-md font-inter gap-2 px-[8px] py-[6px] hover:bg-gray-100 cursor-pointer">
            <Icon size={16} className="text-gray-600 " />
            <p className="text-sm">{text}</p>
        </div>
    )
}

const RightPanel = ({ openRightPanel, setOpenRightPanel }) => {
    const { user } = useSelector(state => state.auth);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    if (!openRightPanel) return null;
    return (
        <div className="fixed top-0 inset-0 bg-gray-300 bg-opacity-40 flex justify-end z-50 ">
            <OutsideClickWrapper
                tabIndex={0}
                ignoreOutsideClick="sideBarRef"
                onClickOutside={() => setOpenRightPanel(false)}
                className={`h-full flex flex-col p-4 border-l shadow w-60 sm:w-80 max-h-[calc(100vh - 64px)] overflow-y-auto rounded-l-xl
                            bg-white border-gray-200 text-gray-900`}
            >
                <div className="flex flex-col">
                    <div className="flex justify-between items-center flex-row mb-4">
                        <div className="flex flex-row items-center gap-2">
                            <AvatarUser active={false} />
                            <p className="text-sm font-semibold text-slate-900 whitespace-nowrap">
                                {user ? (
                                    user?.lastName + " " + user?.firstName
                                ) : (
                                    "Đăng nhập"
                                )}
                            </p>
                        </div>
                        <ButtonHeader
                            onClick={() => setOpenRightPanel(false)}
                            Icon={X}
                        />
                    </div>
                    <hr className="my-4" />
                    <RowPanel text="Thông tin cá nhân" Icon={User} onClick={() => dispatch(setOpenStudentCardModal(true))} />
                    <RowPanel text="Tham gia bằng mã lớp" Icon={UserPlus} onClick={() => dispatch(setOpenJoinClassModal(true))} />
                    <RowPanel text="Quản lý học tập" Icon={Eye} onClick={() => navigate('/overview')} />
                    <hr className="my-4" />

                    <RowPanel text="Học phí" Icon={CreditCard} onClick={() => navigate('/tuition-payments')} />
                    <RowPanel text="Lịch sử điểm danh" Icon={Calendar} onClick={() => navigate('/attendance')} />

                    <hr className="my-4" />
                    <RowPanel text="Đăng xuất" Icon={LogOut} onClick={() => dispatch(logout())} />
                </div>
            </OutsideClickWrapper>
        </div>
    );
};

const LeftPanel = ({ openLeftPanel, setOpenLeftPanel }) => {
    const navigate = useNavigate();
    if (!openLeftPanel) return null;

    return (
        <div className="fixed top-0 inset-0 bg-gray-300 bg-opacity-40 flex justify-start z-50 ">
            <OutsideClickWrapper
                tabIndex={0}
                ignoreOutsideClick="sideBarRef"
                onClickOutside={() => setOpenLeftPanel(false)}
                className={`h-full flex flex-col p-4 border-r shadow w-60 sm:w-80 max-h-[calc(100vh - 64px)] overflow-y-auto rounded-r-xl
                            bg-white border-gray-200 text-gray-900`}
            >
                <div className="flex flex-col">
                    <div className="flex justify-between items-center flex-row mb-4">
                        <BeeMathLogo className="w-8 h-8" />
                        <ButtonHeader
                            onClick={() => setOpenLeftPanel(false)}
                            Icon={X}
                        />
                    </div>
                    <hr className="my-4" />
                    <p className="text-sm font-semibold text-gray-900 mb-2">Mục lục</p>
                    <RowPanel text="Tổng quan" Icon={BookOpen} onClick={() => navigate('/overview')} />
                    <RowPanel text="Lớp học" Icon={School} onClick={() => navigate('/class')} />
                    <RowPanel text="Lịch học" Icon={Calendar} onClick={() => navigate('/calendar')} />
                    <RowPanel text="Lý thuyết" Icon={Library} onClick={() => navigate('/articles')} />
                    <RowPanel text="Luyện đề" Icon={PencilLine} onClick={() => navigate('/practice')} />
                    <hr className="my-4" />
                    <p className="text-xs text-gray-500">© 2025 ToanThayBee, Inc.</p>
                </div>
            </OutsideClickWrapper>
        </div>
    );
};

const QuestionItem = ({ question }) => {
    const navigate = useNavigate();

    return (
        <div
            onClick={() => navigate(`/question/${question.id}`)}
            key={question.id}
            className="border-none md:border-b border-gray-300 flex items-center cursor-pointer justify-between gap-2 p-2 hover:bg-gray-50"
        >
            <div className="flex flex-1 flex-row gap-2">
                <div className="text-sky-500 flex justify-center items-center">
                    <FileSearch size={16} />
                </div>

                <div className="flex gap-2 h-full flex-col md:flex-row justify-center flex-wrap">
                    <p className="text-sm text-gray-500">ID: {question.id}</p>
                    <p className="text-sm text-gray-800">
                        {question.content.length > 50
                            ? question.content.substring(0, 50) + "..."
                            : question.content}
                    </p>
                    <p className="text-sm text-gray-500">
                        Lớp: {question.class} | Loại: {question.typeOfQuestion} | Độ khó: {question.difficulty}
                    </p>
                </div>
            </div>
            <div className=" flex-col gap-2 md:flex hidden ">
                <button
                    onClick={(e) => {
                        e.stopPropagation(); // tránh navigate khi bấm "Xem"
                        navigate(`/question/${question.id}`);
                    }}
                    className="text-sm text-sky-600 hover:underline"
                >
                    Xem
                </button>
            </div>
        </div>
    );
};

const ExamItem = ({ exam }) => {
    const navigate = useNavigate();

    return (
        <div
            onClick={() => navigate(`/practice/exam/${exam.id}`)}
            key={exam.id}
            className="border-none md:border-b border-b-gray-300 flex items-center cursor-pointer justify-between gap-2 p-2 hover:bg-gray-50"
        >
            <div className="flex flex-1 flex-row gap-2">
                <div className="text-sky-500 flex justify-center items-center">
                    <BookOpen size={16} />
                </div>
                <div className="flex gap-2 h-full md:flex-row flex-col flex-wrap">
                    <p className="text-sm text-gray-500">ID: {exam.id}</p>
                    <p className="text-sm text-gray-800">
                        {exam.name.length > 50 ? exam.name.substring(0, 50) + "..." : exam.name}
                    </p>
                    <p className="text-sm text-gray-500">
                        Lớp: {exam.class} | Loại: {exam.typeOfExam} | Năm: {exam.year}
                    </p>

                </div>
            </div>
            <div className="flex flex-col gap-2">
                <button
                    onClick={(e) => {
                        e.stopPropagation(); // tránh navigate khi bấm "Xem"
                        navigate(`/practice/exam/${exam.id}`);
                    }}
                    className="text-sm text-sky-600 hover:underline"
                >
                    Xem
                </button>
            </div>
        </div>
    );
};


const LoadingSearchData = ({ Icon }) => {
    return (
        <div className=" flex items-center cursor-pointer justify-between gap-2 p-2 hover:bg-gray-50">
            <div className="flex flex-1 flex-row gap-2">
                <div className="text-sky-500 flex justify-center items-center">
                    {Icon && <Icon size={16} />}
                </div>
                <div className="flex gap-2 h-full flex-row justify-center items-center flex-wrap">
                    <LoadingText loading={true} w="w-80" />
                </div>
            </div>
            <div className="flex flex-col gap-2">
                <button
                    className="text-sm text-sky-600 hover:underline"
                >
                    Xem
                </button>
            </div>
        </div>
    )
}

const ModalSearch = ({ isOpen, setIsOpen }) => {
    const [searchTerm, setSearchTerm] = useState("");
    const { questionsSearch, loadingSearch } = useSelector(state => state.questions);
    const { examsSearch, loadingSearch: loadingSearchExam } = useSelector(state => state.exams);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const dispatch = useDispatch();

    useDebouncedEffect(() => {
        setSearch(searchTerm);
        setLoading(false);
    }, [searchTerm], 1000);

    useEffect(() => {
        dispatch(findQuestions(search));
        dispatch(findPublicExams(search));
    }, [dispatch, search]);

    if (!isOpen) return null;
    return (
        <div className="fixed top-0 inset-0 bg-gray-300 bg-opacity-40 flex justify-start pt-2 md:pl-[221px] md:pr-[340px] items-start z-50 ">
            <OutsideClickWrapper
                tabIndex={0}
                ignoreOutsideClick="searchRef"
                onClickOutside={() => setIsOpen(false)}
                className=" gap-2 flex flex-col border shadow w-full overflow-y-auto rounded-xl bg-white border-gray-200 text-gray-900"
            >
                <div
                    className="flex mx-4 mt-4 items-center cursor-pointer border border-gray-300 rounded-md px-2 py-[7px] text-gray-600 focus-within:ring-2 focus-within:ring-sky-500">
                    <Search size={16} />
                    <input
                        id="questionId"
                        type="text"
                        value={searchTerm}
                        onChange={(e) => {
                            setLoading(true)
                            setSearchTerm(e.target.value)
                        }}
                        placeholder="Tìm kiếm đề thi và câu hỏi..."
                        className="flex-1 pl-2 text-xs outline-none bg-transparent"
                    />
                    <button
                        onClick={() => setSearchTerm("")}
                        className="ml-2 text-gray-400 hover:text-gray-600"
                    >
                        <X size={16} />
                    </button>
                </div>
                <div className="max-h-[400px] flex flex-col overflow-y-auto">
                    <div className="flex flex-col px-4">
                        <p className="text-xs font-semibold text-gray-700 mb-2">
                            Câu hỏi
                        </p>

                        {(loadingSearch || loading) ? (
                            <>
                                <LoadingSearchData Icon={FileSearch} />
                                <LoadingSearchData Icon={FileSearch} />
                            </>
                        ) : questionsSearch.length > 0 ?
                            (questionsSearch.map((question) => (
                                <QuestionItem key={question.id} question={question} />
                            ))) : (
                                <p className="text-xs text-gray-500">
                                    Không tìm thấy câu hỏi
                                </p>
                            )}
                    </div>
                    <hr className="my-4 border-gray-300" />
                    <div className="flex flex-col px-4">
                        <p className="text-xs font-semibold text-gray-700 mb-2">
                            Đề thi
                        </p>

                        {(loadingSearchExam || loading) ? (
                            <>
                                <LoadingSearchData Icon={BookOpen} />
                                <LoadingSearchData Icon={BookOpen} />
                            </>
                        ) : examsSearch.length > 0 ?
                            (examsSearch.map((exam) => (
                                <ExamItem key={exam.id} exam={exam} />
                            ))) : (
                                <p className="text-xs text-gray-500">
                                    Không tìm thấy đề thi
                                </p>
                            )
                        }
                    </div>
                </div>
                <div className="p-4 border-t border-gray-300">
                    <p className="text-xs text-gray-500">
                        Nhập ID hoặc tên hoặc nội dung để tìm đề thi, câu hỏi.
                    </p>
                </div>
            </OutsideClickWrapper>
        </div>
    );
};

const MenuAction = ({ actions, openMenuAction, setOpenMenuAction }) => {
    const navigate = useNavigate();
    if (!openMenuAction) return null;
    return (
        <OutsideClickWrapper
            ignoreOutsideClick="menuActionRef"
            onClickOutside={() => setOpenMenuAction(false)}
            className={`lg:hidden absolute top-full right-0 p-2 mt-2 w-60 sm:w-80 rounded-md shadow-lg z-50 transition-opacity duration-200 
                ${openMenuAction ? "opacity-100 visible" : "opacity-0 invisible"}
                bg-white border border-gray-200
            `}
        >
            {actions.map((action, index) => (
                <div className={`${action.hiddenAt}:hidden block`}>
                    <RowPanel key={index} text={action.title} Icon={action.Icon} onClick={() => navigate(action.route)} />

                </div>
            ))}
        </OutsideClickWrapper>
    );
};

const Header = () => {
    const [openRightPanel, setOpenRightPanel] = useState(false);
    const [openLeftPanel, setOpenLeftPanel] = useState(false);
    const [openMenuAction, setOpenMenuAction] = useState(false);
    const { openStudentCardModal } = useSelector(state => state.auth);
    const { openJoinClassModal } = useSelector(state => state.classes);
    const [openModalSearch, setModalSearch] = useState(false);
    const dispatch = useDispatch();

    const { firstSearch } = useSelector(state => state.questions);
    const { firstSearch: firstSearchExam } = useSelector(state => state.exams);




    useEffect(() => {
        if (firstSearch)
            dispatch(findQuestions(""));
    }, [dispatch, firstSearch]);

    useEffect(() => {
        if (firstSearchExam)
            dispatch(findPublicExams(""));
    }, [dispatch, firstSearchExam]);

    const choices = [
        { title: "Lịch học", route: "/calendar", Icon: Calendar, hiddenAt: "sm" },
        { title: "Lý thuyết", route: "/articles", Icon: Library, hiddenAt: "md" },
        { title: "Luyện đề", route: "/practice", Icon: PencilLine, hiddenAt: "lg" }
    ];



    return (
        <header className="fixed border-b border-gray-300 flex flex-col top-0 left-0 right-0 z-[60] bg-[#f6f8fa]">
            <RightPanel
                openRightPanel={openRightPanel}
                setOpenRightPanel={setOpenRightPanel}
            />
            <LeftPanel openLeftPanel={openLeftPanel} setOpenLeftPanel={setOpenLeftPanel} />
            <JoinClassModal isOpen={openJoinClassModal} setIsModalOpen={() => dispatch(setOpenJoinClassModal(false))} />
            <StudentCardModal isOpen={openStudentCardModal} onClose={() => dispatch(setOpenStudentCardModal(false))} />
            <ModalSearch isOpen={openModalSearch} setIsOpen={setModalSearch} />
            <div className="flex flex-row h-[56px] pb-2 px-4 pt-4">
                <div className="flex-1 flex items-center gap-2 sm:gap-3">
                    <ButtonHeader
                        Icon={Menu}
                        onClick={() => setOpenLeftPanel(!openLeftPanel)}
                    />
                    <LogoHeader />
                    <HoverWrapper>
                        <p className="font-bold text-sm font-bevietnam tracking-wide">
                            <span className="text-yellow-500">Toán</span>{' '}
                            <span className="text-sky-600">Thầy Bee</span>
                        </p>
                    </HoverWrapper>
                </div>
                <div className="sm:flex-1 flex items-center justify-end gap-2 sm:gap-3">
                    <QuestionIdInput onClick={() => setModalSearch(true)} />
                    <div className=" items-center gap-3 flex-row sm:flex hidden">
                        <div className="w-px h-4 bg-gray-400 " />
                        <ClassHeader onClick={() => dispatch(setOpenJoinClassModal(true))} />
                    </div>
                    <NotificationHeader />
                    <div className="w-px h-4 bg-gray-400 sm:block hidden" />
                    <UserName onClick={() => setOpenRightPanel(!openRightPanel)} />
                    <AvatarUser
                        onClick={() => setOpenRightPanel(!openRightPanel)}
                    />
                </div>
            </div>
            <div className="h-12 px-4 flex flex-row justify-between flex-wrap">
                <div className="flex flex-row gap-3 flex-wrap">
                    <ChoiceHeader title="Tổng quan" route="/overview" Icon={BookOpen} />
                    <ChoiceHeader title="Lớp học" route="/class" Icon={School} />
                    <ChoiceHeader title="Lịch học" route="/calendar" Icon={Calendar} className="hidden sm:flex" />
                    <ChoiceHeader title="Lý thuyết" route="/articles" Icon={Library} className="hidden md:flex" />
                    <ChoiceHeader title="Luyện đề" route="/practice" Icon={PencilLine} className="hidden lg:flex" />
                </div>
                <div className="lg:hidden flex py-2 menuActionRef">
                    <ButtonHeader
                        Icon={MoreHorizontal}
                        onClick={() => setOpenMenuAction(!openMenuAction)}
                    />
                </div>
            </div>
            <MenuAction actions={choices} openMenuAction={openMenuAction} setOpenMenuAction={setOpenMenuAction} />

        </header>
    );
};

export default Header;