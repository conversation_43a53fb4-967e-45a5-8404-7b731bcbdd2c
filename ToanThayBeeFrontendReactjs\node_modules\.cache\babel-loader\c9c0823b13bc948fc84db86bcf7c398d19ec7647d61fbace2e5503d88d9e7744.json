{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\RightContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setSelectedQuestion, setEditedExam } from \"src/features/examAI/examAISlice\";\nimport { useState, useEffect } from \"react\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuestionEditView = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    selectedQuestion\n  } = useSelector(state => state.examAI);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const [optionChapter, setOptionChapter] = useState([]);\n\n  // Filter chapter options based on class\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      if (selectedQuestion !== null && selectedQuestion !== void 0 && selectedQuestion.class && selectedQuestion.class.trim() !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.class]);\n  const handleQuestionChange = (field, value) => {\n    const updatedQuestion = {\n      ...selectedQuestion,\n      [field]: value\n    };\n    dispatch(setSelectedQuestion(updatedQuestion));\n  };\n  const handleStatementChange = (index, field, value) => {\n    const updatedStatements = [...((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.statement1s) || [])];\n    updatedStatements[index] = {\n      ...updatedStatements[index],\n      [field]: value\n    };\n    const updatedQuestion = {\n      ...selectedQuestion,\n      statement1s: updatedStatements\n    };\n    dispatch(setSelectedQuestion(updatedQuestion));\n  };\n  if (!selectedQuestion) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed right-0 w-1/3 p-4 overflow-y-auto min-h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-gray-500 mt-8\",\n        children: \"Ch\\u1ECDn m\\u1ED9t c\\xE2u h\\u1ECFi \\u0111\\u1EC3 ch\\u1EC9nh s\\u1EEDa\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold text-gray-800 mb-6\",\n      children: \"Ch\\u1EC9nh s\\u1EEDa c\\xE2u h\\u1ECFi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"N\\u1ED9i dung c\\xE2u h\\u1ECFi:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        className: \"w-full min-h-[150px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n        value: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.content) || \"\",\n        onChange: e => handleQuestionChange('content', e.target.value),\n        placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"L\\u1EDBp:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.class,\n        onChange: value => handleQuestionChange('class', value),\n        options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n        placeholder: \"Ch\\u1ECDn l\\u1EDBp\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"\\u0110\\u1ED9 kh\\xF3:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n        selectedOption: selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.difficulty,\n        onChange: value => handleQuestionChange('difficulty', value),\n        options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n        placeholder: \"Ch\\u1ECDn \\u0111\\u1ED9 kh\\xF3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"Ch\\u01B0\\u01A1ng:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n        selectedOption: selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.chapter,\n        onChange: value => handleQuestionChange('chapter', value),\n        options: optionChapter,\n        placeholder: \"Ch\\u1ECDn ch\\u01B0\\u01A1ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"L\\u1EDDi gi\\u1EA3i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        className: \"w-full min-h-[120px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n        value: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.solution) || \"\",\n        onChange: e => handleQuestionChange('solution', e.target.value),\n        placeholder: \"Nh\\u1EADp l\\u1EDDi gi\\u1EA3i...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 13\n    }, this), (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.statement1s) && selectedQuestion.statement1s.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-3\",\n        children: \"C\\xE1c m\\u1EC7nh \\u0111\\u1EC1:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: selectedQuestion.statement1s.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: [\"M\\u1EC7nh \\u0111\\u1EC1 \", String.fromCharCode(65 + index)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center gap-2 cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"statement-\".concat(index),\n                  checked: statement.isCorrect === true,\n                  onChange: () => handleStatementChange(index, 'isCorrect', true),\n                  className: \"w-4 h-4 text-green-600 focus:ring-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-green-600 font-medium\",\n                  children: \"\\u0110\\xFAng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center gap-2 cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"statement-\".concat(index),\n                  checked: statement.isCorrect === false,\n                  onChange: () => handleStatementChange(index, 'isCorrect', false),\n                  className: \"w-4 h-4 text-red-600 focus:ring-red-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-red-600 font-medium\",\n                  children: \"Sai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"w-full min-h-[80px] border border-gray-300 rounded-lg p-2 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n            value: statement.content || \"\",\n            onChange: e => handleStatementChange(index, 'content', e.target.value),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1 \".concat(String.fromCharCode(65 + index), \"...\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 33\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 29\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true);\n};\n_s(QuestionEditView, \"i+3zP1FOFhpowio7Q+snY+J29CA=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = QuestionEditView;\nexport const RightContent = () => {\n  _s2();\n  const {\n    viewEdit\n  } = useSelector(state => state.examAI);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed pb-10 right-0 w-1/3 p-4 overflow-y-auto min-h-screen max-h-screen bg-white border-l border-gray-200\",\n    children: viewEdit === 'question' && /*#__PURE__*/_jsxDEV(QuestionEditView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 41\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 9\n  }, this);\n};\n_s2(RightContent, \"plqPiIWI8H/U9YD7djE1+JqN1rI=\", false, function () {\n  return [useSelector];\n});\n_c2 = RightContent;\nexport default RightContent;\nvar _c, _c2;\n$RefreshReg$(_c, \"QuestionEditView\");\n$RefreshReg$(_c2, \"RightContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "setSelectedQuestion", "setEditedExam", "useState", "useEffect", "DropMenuBarAdmin", "SuggestInputBarAdmin", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuestionEdit<PERSON>iew", "_s", "dispatch", "selectedQuestion", "state", "examAI", "codes", "optionChapter", "setOptionChapter", "Array", "isArray", "class", "trim", "filter", "code", "length", "handleQuestionChange", "field", "value", "updatedQuestion", "handleStatementChange", "index", "updatedStatements", "statement1s", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "onChange", "e", "target", "placeholder", "selectedOption", "options", "difficulty", "chapter", "solution", "map", "statement", "String", "fromCharCode", "type", "name", "concat", "checked", "isCorrect", "_c", "RightContent", "_s2", "viewEdit", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/RightContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { setSelectedQuestion, setEditedExam } from \"src/features/examAI/examAISlice\";\r\nimport { useState, useEffect } from \"react\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\n\r\nconst QuestionEditView = () => {\r\n    const dispatch = useDispatch();\r\n    const { selectedQuestion } = useSelector((state) => state.examAI);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n\r\n    // Filter chapter options based on class\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (selectedQuestion?.class && selectedQuestion.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, selectedQuestion?.class]);\r\n\r\n    const handleQuestionChange = (field, value) => {\r\n        const updatedQuestion = { ...selectedQuestion, [field]: value };\r\n        dispatch(setSelectedQuestion(updatedQuestion));\r\n    };\r\n\r\n    const handleStatementChange = (index, field, value) => {\r\n        const updatedStatements = [...(selectedQuestion?.statement1s || [])];\r\n        updatedStatements[index] = { ...updatedStatements[index], [field]: value };\r\n        const updatedQuestion = { ...selectedQuestion, statement1s: updatedStatements };\r\n        dispatch(setSelectedQuestion(updatedQuestion));\r\n    };\r\n\r\n    if (!selectedQuestion) {\r\n        return (\r\n            <div className=\"fixed right-0 w-1/3 p-4 overflow-y-auto min-h-screen bg-gray-50\">\r\n                <div className=\"text-center text-gray-500 mt-8\">\r\n                    Chọn một câu hỏi để chỉnh sửa\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <h2 className=\"text-xl font-semibold text-gray-800 mb-6\">Chỉnh sửa câu hỏi</h2>\r\n\r\n            {/* Nội dung câu hỏi */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Nội dung câu hỏi:</label>\r\n                <textarea\r\n                    className=\"w-full min-h-[150px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                    value={selectedQuestion?.content || \"\"}\r\n                    onChange={(e) => handleQuestionChange('content', e.target.value)}\r\n                    placeholder=\"Nhập nội dung câu hỏi...\"\r\n                />\r\n            </div>\r\n\r\n            {/* Lớp */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Lớp:</label>\r\n                <DropMenuBarAdmin\r\n                    selectedOption={selectedQuestion?.class}\r\n                    onChange={(value) => handleQuestionChange('class', value)}\r\n                    options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                    placeholder=\"Chọn lớp\"\r\n                />\r\n            </div>\r\n\r\n            {/* Độ khó */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Độ khó:</label>\r\n                <DropMenuBarAdmin\r\n                    selectedOption={selectedQuestion?.difficulty}\r\n                    onChange={(value) => handleQuestionChange('difficulty', value)}\r\n                    options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                    placeholder=\"Chọn độ khó\"\r\n                />\r\n            </div>\r\n\r\n            {/* Chương */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Chương:</label>\r\n                <SuggestInputBarAdmin\r\n                    selectedOption={selectedQuestion?.chapter}\r\n                    onChange={(value) => handleQuestionChange('chapter', value)}\r\n                    options={optionChapter}\r\n                    placeholder=\"Chọn chương\"\r\n                />\r\n            </div>\r\n\r\n            {/* Lời giải */}\r\n            <div className=\"mb-6\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Lời giải:</label>\r\n                <textarea\r\n                    className=\"w-full min-h-[120px] border border-gray-300 rounded-lg p-3 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                    value={selectedQuestion?.solution || \"\"}\r\n                    onChange={(e) => handleQuestionChange('solution', e.target.value)}\r\n                    placeholder=\"Nhập lời giải...\"\r\n                />\r\n            </div>\r\n\r\n            {/* Các mệnh đề/đáp án */}\r\n            {selectedQuestion?.statement1s && selectedQuestion.statement1s.length > 0 && (\r\n                <div className=\"mb-6\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-3\">Các mệnh đề:</label>\r\n                    <div className=\"space-y-4\">\r\n                        {selectedQuestion.statement1s.map((statement, index) => (\r\n                            <div key={index} className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\r\n                                <div className=\"flex items-center justify-between mb-3\">\r\n                                    <span className=\"text-sm font-medium text-gray-600\">\r\n                                        Mệnh đề {String.fromCharCode(65 + index)}\r\n                                    </span>\r\n                                    <div className=\"flex items-center gap-4\">\r\n                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name={`statement-${index}`}\r\n                                                checked={statement.isCorrect === true}\r\n                                                onChange={() => handleStatementChange(index, 'isCorrect', true)}\r\n                                                className=\"w-4 h-4 text-green-600 focus:ring-green-500\"\r\n                                            />\r\n                                            <span className=\"text-sm text-green-600 font-medium\">Đúng</span>\r\n                                        </label>\r\n                                        <label className=\"flex items-center gap-2 cursor-pointer\">\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name={`statement-${index}`}\r\n                                                checked={statement.isCorrect === false}\r\n                                                onChange={() => handleStatementChange(index, 'isCorrect', false)}\r\n                                                className=\"w-4 h-4 text-red-600 focus:ring-red-500\"\r\n                                            />\r\n                                            <span className=\"text-sm text-red-600 font-medium\">Sai</span>\r\n                                        </label>\r\n                                    </div>\r\n                                </div>\r\n                                <textarea\r\n                                    className=\"w-full min-h-[80px] border border-gray-300 rounded-lg p-2 text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                                    value={statement.content || \"\"}\r\n                                    onChange={(e) => handleStatementChange(index, 'content', e.target.value)}\r\n                                    placeholder={`Nhập nội dung mệnh đề ${String.fromCharCode(65 + index)}...`}\r\n                                />\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n}\r\n\r\n\r\nexport const RightContent = () => {\r\n    const { viewEdit } = useSelector((state) => state.examAI);\r\n\r\n    return (\r\n        <div className=\"fixed pb-10 right-0 w-1/3 p-4 overflow-y-auto min-h-screen max-h-screen bg-white border-l border-gray-200\">\r\n            {viewEdit === 'question' && <QuestionEditView />}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RightContent;"], "mappings": ";;;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,mBAAmB,EAAEC,aAAa,QAAQ,iCAAiC;AACpF,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7E,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc;EAAiB,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACjE,MAAM;IAAEC;EAAM,CAAC,GAAGlB,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EACrD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACZ,IAAIgB,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MACjC,IAAIH,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEQ,KAAK,IAAIR,gBAAgB,CAACQ,KAAK,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjEJ,gBAAgB,CACZF,KAAK,CAAC,SAAS,CAAC,CAACO,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,MAAM,KAAK,CAAC,CAC5D,CAAC;MACL,CAAC,MAAM;QACHP,gBAAgB,CAACF,KAAK,CAAC,SAAS,CAAC,CAACO,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAACC,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACHP,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACF,KAAK,EAAEH,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEQ,KAAK,CAAC,CAAC;EAEpC,MAAMK,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC3C,MAAMC,eAAe,GAAG;MAAE,GAAGhB,gBAAgB;MAAE,CAACc,KAAK,GAAGC;IAAM,CAAC;IAC/DhB,QAAQ,CAACZ,mBAAmB,CAAC6B,eAAe,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAACC,KAAK,EAAEJ,KAAK,EAAEC,KAAK,KAAK;IACnD,MAAMI,iBAAiB,GAAG,CAAC,IAAI,CAAAnB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoB,WAAW,KAAI,EAAE,CAAC,CAAC;IACpED,iBAAiB,CAACD,KAAK,CAAC,GAAG;MAAE,GAAGC,iBAAiB,CAACD,KAAK,CAAC;MAAE,CAACJ,KAAK,GAAGC;IAAM,CAAC;IAC1E,MAAMC,eAAe,GAAG;MAAE,GAAGhB,gBAAgB;MAAEoB,WAAW,EAAED;IAAkB,CAAC;IAC/EpB,QAAQ,CAACZ,mBAAmB,CAAC6B,eAAe,CAAC,CAAC;EAClD,CAAC;EAED,IAAI,CAAChB,gBAAgB,EAAE;IACnB,oBACIN,OAAA;MAAK2B,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC5E5B,OAAA;QAAK2B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAAC;MAEhD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIhC,OAAA,CAAAE,SAAA;IAAA0B,QAAA,gBACI5B,OAAA;MAAI2B,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAG/EhC,OAAA;MAAK2B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB5B,OAAA;QAAO2B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACzFhC,OAAA;QACI2B,SAAS,EAAC,iIAAiI;QAC3IN,KAAK,EAAE,CAAAf,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2B,OAAO,KAAI,EAAG;QACvCC,QAAQ,EAAGC,CAAC,IAAKhB,oBAAoB,CAAC,SAAS,EAAEgB,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;QACjEgB,WAAW,EAAC;MAA0B;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNhC,OAAA;MAAK2B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB5B,OAAA;QAAO2B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5EhC,OAAA,CAACH,gBAAgB;QACbyC,cAAc,EAAEhC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEQ,KAAM;QACxCoB,QAAQ,EAAGb,KAAK,IAAKF,oBAAoB,CAAC,OAAO,EAAEE,KAAK,CAAE;QAC1DkB,OAAO,EAAE3B,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;QAC7D4B,WAAW,EAAC;MAAU;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNhC,OAAA;MAAK2B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB5B,OAAA;QAAO2B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/EhC,OAAA,CAACH,gBAAgB;QACbyC,cAAc,EAAEhC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkC,UAAW;QAC7CN,QAAQ,EAAGb,KAAK,IAAKF,oBAAoB,CAAC,YAAY,EAAEE,KAAK,CAAE;QAC/DkB,OAAO,EAAE3B,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;QACvE4B,WAAW,EAAC;MAAa;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNhC,OAAA;MAAK2B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB5B,OAAA;QAAO2B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/EhC,OAAA,CAACF,oBAAoB;QACjBwC,cAAc,EAAEhC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEmC,OAAQ;QAC1CP,QAAQ,EAAGb,KAAK,IAAKF,oBAAoB,CAAC,SAAS,EAAEE,KAAK,CAAE;QAC5DkB,OAAO,EAAE7B,aAAc;QACvB2B,WAAW,EAAC;MAAa;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNhC,OAAA;MAAK2B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB5B,OAAA;QAAO2B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACjFhC,OAAA;QACI2B,SAAS,EAAC,iIAAiI;QAC3IN,KAAK,EAAE,CAAAf,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoC,QAAQ,KAAI,EAAG;QACxCR,QAAQ,EAAGC,CAAC,IAAKhB,oBAAoB,CAAC,UAAU,EAAEgB,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;QAClEgB,WAAW,EAAC;MAAkB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAGL,CAAA1B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoB,WAAW,KAAIpB,gBAAgB,CAACoB,WAAW,CAACR,MAAM,GAAG,CAAC,iBACrElB,OAAA;MAAK2B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB5B,OAAA;QAAO2B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpFhC,OAAA;QAAK2B,SAAS,EAAC,WAAW;QAAAC,QAAA,EACrBtB,gBAAgB,CAACoB,WAAW,CAACiB,GAAG,CAAC,CAACC,SAAS,EAAEpB,KAAK,kBAC/CxB,OAAA;UAAiB2B,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBACzE5B,OAAA;YAAK2B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACnD5B,OAAA;cAAM2B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,yBACxC,EAACiB,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGtB,KAAK,CAAC;YAAA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACPhC,OAAA;cAAK2B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACpC5B,OAAA;gBAAO2B,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD5B,OAAA;kBACI+C,IAAI,EAAC,OAAO;kBACZC,IAAI,eAAAC,MAAA,CAAezB,KAAK,CAAG;kBAC3B0B,OAAO,EAAEN,SAAS,CAACO,SAAS,KAAK,IAAK;kBACtCjB,QAAQ,EAAEA,CAAA,KAAMX,qBAAqB,CAACC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAE;kBAChEG,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACFhC,OAAA;kBAAM2B,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACRhC,OAAA;gBAAO2B,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD5B,OAAA;kBACI+C,IAAI,EAAC,OAAO;kBACZC,IAAI,eAAAC,MAAA,CAAezB,KAAK,CAAG;kBAC3B0B,OAAO,EAAEN,SAAS,CAACO,SAAS,KAAK,KAAM;kBACvCjB,QAAQ,EAAEA,CAAA,KAAMX,qBAAqB,CAACC,KAAK,EAAE,WAAW,EAAE,KAAK,CAAE;kBACjEG,SAAS,EAAC;gBAAyC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACFhC,OAAA;kBAAM2B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNhC,OAAA;YACI2B,SAAS,EAAC,gIAAgI;YAC1IN,KAAK,EAAEuB,SAAS,CAACX,OAAO,IAAI,EAAG;YAC/BC,QAAQ,EAAGC,CAAC,IAAKZ,qBAAqB,CAACC,KAAK,EAAE,SAAS,EAAEW,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;YACzEgB,WAAW,oDAAAY,MAAA,CAA2BJ,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGtB,KAAK,CAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA,GAjCIR,KAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkCV,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA,eACH,CAAC;AAEX,CAAC;AAAA5B,EAAA,CArJKD,gBAAgB;EAAA,QACDX,WAAW,EACCD,WAAW,EACtBA,WAAW;AAAA;AAAA6D,EAAA,GAH3BjD,gBAAgB;AAwJtB,OAAO,MAAMkD,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC9B,MAAM;IAAEC;EAAS,CAAC,GAAGhE,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEzD,oBACIR,OAAA;IAAK2B,SAAS,EAAC,2GAA2G;IAAAC,QAAA,EACrH2B,QAAQ,KAAK,UAAU,iBAAIvD,OAAA,CAACG,gBAAgB;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC;AAEd,CAAC;AAACsB,GAAA,CARWD,YAAY;EAAA,QACA9D,WAAW;AAAA;AAAAiE,GAAA,GADvBH,YAAY;AAUzB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}