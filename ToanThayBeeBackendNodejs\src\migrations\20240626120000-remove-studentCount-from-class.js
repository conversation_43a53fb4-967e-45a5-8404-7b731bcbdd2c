'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    // Remove the studentCount column from the class table
    await queryInterface.removeColumn('class', 'studentCount');
  },

  async down(queryInterface, Sequelize) {
    // Add the studentCount column back if the migration is reverted
    await queryInterface.addColumn('class', 'studentCount', {
      type: Sequelize.INTEGER,
      defaultValue: 0
    });
  }
};
