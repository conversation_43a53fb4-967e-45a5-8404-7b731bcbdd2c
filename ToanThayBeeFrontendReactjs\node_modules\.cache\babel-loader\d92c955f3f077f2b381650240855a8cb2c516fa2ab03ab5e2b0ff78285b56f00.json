{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\SolutionEditor.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { ImagePlus, Upload } from \"lucide-react\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SolutionEditor = _ref => {\n  _s();\n  let {\n    solution,\n    onSolutionChange,\n    isAddImage\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const [dragPosition, setDragPosition] = useState(null);\n  const [cursorPosition, setCursorPosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const textareaRef = useRef(null);\n  const previewRef = useRef(null);\n  const containerRef = useRef(null);\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\");\n    if (!draggedImage || !onSolutionChange) return;\n\n    // Tính toán vị trí chèn ảnh\n    let insertPosition = 0;\n    if (dragPosition !== null) {\n      insertPosition = dragPosition;\n    } else {\n      // Nếu không có vị trí cụ thể, chèn vào cuối\n      insertPosition = solution ? solution.length : 0;\n    }\n\n    // Tạo markdown image syntax\n    const imageMarkdown = \"\\n![\\u1EA2nh](\".concat(draggedImage, \")\\n\");\n\n    // Chèn vào vị trí\n    const newSolution = solution ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition) : imageMarkdown;\n    onSolutionChange(newSolution);\n    setDragPosition(null);\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (isAddImage && textareaRef.current) {\n      const textarea = textareaRef.current;\n      const rect = textarea.getBoundingClientRect();\n      const x = e.clientX - rect.left;\n      const y = e.clientY - rect.top;\n\n      // Tính toán vị trí character dựa trên mouse position\n      const position = getCharacterPositionFromCoordinates(textarea, x, y);\n      setDragPosition(position);\n      setCursorPosition({\n        x,\n        y\n      });\n    }\n  };\n\n  // Hàm tính toán vị trí character từ tọa độ mouse\n  const getCharacterPositionFromCoordinates = (textarea, x, y) => {\n    const style = window.getComputedStyle(textarea);\n    const lineHeight = parseInt(style.lineHeight) || 24;\n    const fontSize = parseInt(style.fontSize) || 14;\n    const charWidth = fontSize * 0.6; // Ước tính độ rộng character\n\n    const lineIndex = Math.floor(y / lineHeight);\n    const charIndex = Math.floor(x / charWidth);\n    if (!solution) return 0;\n    const lines = solution.split('\\n');\n    let position = 0;\n\n    // Tính position đến dòng hiện tại\n    for (let i = 0; i < Math.min(lineIndex, lines.length); i++) {\n      position += lines[i].length + 1; // +1 cho \\n\n    }\n\n    // Thêm vị trí trong dòng hiện tại\n    if (lineIndex < lines.length) {\n      position += Math.min(charIndex, lines[lineIndex].length);\n    }\n    return Math.max(0, Math.min(position, solution.length));\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (isAddImage && e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!e.currentTarget.contains(e.relatedTarget)) {\n      setIsDraggingOver(false);\n      setDragPosition(null);\n    }\n  };\n  const handleTextareaChange = e => {\n    onSolutionChange(e.target.value);\n  };\n\n  // Hiển thị cursor dọc nhấp nháy\n  const getCursorStyle = () => {\n    if (!isDraggingOver || !cursorPosition) return {\n      display: 'none'\n    };\n    return {\n      position: 'absolute',\n      left: \"\".concat(cursorPosition.x, \"px\"),\n      top: \"\".concat(cursorPosition.y, \"px\"),\n      width: '2px',\n      height: '20px',\n      backgroundColor: '#3b82f6',\n      zIndex: 10,\n      animation: 'blink 1s infinite',\n      pointerEvents: 'none'\n    };\n  };\n  if (!isAddImage) {\n    // Chế độ xem bình thường\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"L\\u1EDDi gi\\u1EA3i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this), \" \", /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-sm font-semibold text-green-700\",\n      children: \"L\\u1EDDi gi\\u1EA3i:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative border rounded-lg transition-all duration-200 \".concat(isDraggingOver ? \"border-2 border-dashed border-blue-400 bg-blue-50\" : \"border-gray-300 hover:border-blue-300\"),\n      onDragOver: handleDragOver,\n      onDragEnter: handleDragEnter,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      ref: containerRef,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: getCursorStyle()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        ref: textareaRef,\n        value: solution || '',\n        onChange: handleTextareaChange,\n        placeholder: \"Nh\\u1EADp l\\u1EDDi gi\\u1EA3i (h\\u1ED7 tr\\u1EE3 Markdown v\\xE0 LaTeX)...\",\n        className: \"w-full p-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px] \".concat(isDraggingOver ? 'bg-transparent' : ''),\n        style: {\n          lineHeight: '24px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 17\n      }, this), !solution && !isDraggingOver && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none\",\n        children: [/*#__PURE__*/_jsxDEV(ImagePlus, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"K\\xE9o \\u1EA3nh v\\xE0o \\u0111\\u1EC3 ch\\xE8n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: \"\\n                @keyframes blink {\\n                    0%, 50% { opacity: 1; }\\n                    51%, 100% { opacity: 0; }\\n                }\\n            \"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 13\n    }, this), solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-3 bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 mb-2\",\n        children: \"Xem tr\\u01B0\\u1EDBc:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 9\n  }, this);\n};\n_s(SolutionEditor, \"CyxXdEWON6rdJXoSLwdbuq/0jwQ=\");\n_c = SolutionEditor;\nexport default SolutionEditor;\nvar _c;\n$RefreshReg$(_c, \"SolutionEditor\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "ImagePlus", "Upload", "MarkdownPreviewWithMath", "jsxDEV", "_jsxDEV", "SolutionEditor", "_ref", "_s", "solution", "onSolutionChange", "isAddImage", "isDraggingOver", "setIsDraggingOver", "dragPosition", "setDragPosition", "cursorPosition", "setCursorPosition", "x", "y", "textareaRef", "previewRef", "containerRef", "handleDrop", "e", "preventDefault", "stopPropagation", "draggedImage", "dataTransfer", "getData", "insertPosition", "length", "imageMarkdown", "concat", "newSolution", "slice", "handleDragOver", "current", "textarea", "rect", "getBoundingClientRect", "clientX", "left", "clientY", "top", "position", "getCharacterPositionFromCoordinates", "style", "window", "getComputedStyle", "lineHeight", "parseInt", "fontSize", "char<PERSON><PERSON><PERSON>", "lineIndex", "Math", "floor", "charIndex", "lines", "split", "i", "min", "max", "handleDragEnter", "types", "includes", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "handleTextareaChange", "target", "value", "getCursorStyle", "display", "width", "height", "backgroundColor", "zIndex", "animation", "pointerEvents", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "ref", "onChange", "placeholder", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/SolutionEditor.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\nimport { ImagePlus, Upload } from \"lucide-react\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\n\nconst SolutionEditor = ({ solution, onSolutionChange, isAddImage }) => {\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\n    const [dragPosition, setDragPosition] = useState(null);\n    const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });\n    const textareaRef = useRef(null);\n    const previewRef = useRef(null);\n    const containerRef = useRef(null);\n\n    const handleDrop = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDraggingOver(false);\n\n        const draggedImage = e.dataTransfer.getData(\"text/plain\");\n        if (!draggedImage || !onSolutionChange) return;\n\n        // Tính toán vị trí chèn <PERSON>nh\n        let insertPosition = 0;\n        \n        if (dragPosition !== null) {\n            insertPosition = dragPosition;\n        } else {\n            // Nếu không có vị trí cụ thể, chèn vào cuối\n            insertPosition = solution ? solution.length : 0;\n        }\n\n        // Tạo markdown image syntax\n        const imageMarkdown = `\\n![Ảnh](${draggedImage})\\n`;\n        \n        // Chèn vào vị trí\n        const newSolution = solution \n            ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition)\n            : imageMarkdown;\n\n        onSolutionChange(newSolution);\n        setDragPosition(null);\n    };\n\n    const handleDragOver = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n\n        if (isAddImage && textareaRef.current) {\n            const textarea = textareaRef.current;\n            const rect = textarea.getBoundingClientRect();\n            const x = e.clientX - rect.left;\n            const y = e.clientY - rect.top;\n\n            // Tính toán vị trí character dựa trên mouse position\n            const position = getCharacterPositionFromCoordinates(textarea, x, y);\n            setDragPosition(position);\n            setCursorPosition({ x, y });\n        }\n    };\n\n    // Hàm tính toán vị trí character từ tọa độ mouse\n    const getCharacterPositionFromCoordinates = (textarea, x, y) => {\n        const style = window.getComputedStyle(textarea);\n        const lineHeight = parseInt(style.lineHeight) || 24;\n        const fontSize = parseInt(style.fontSize) || 14;\n        const charWidth = fontSize * 0.6; // Ước tính độ rộng character\n\n        const lineIndex = Math.floor(y / lineHeight);\n        const charIndex = Math.floor(x / charWidth);\n\n        if (!solution) return 0;\n\n        const lines = solution.split('\\n');\n        let position = 0;\n\n        // Tính position đến dòng hiện tại\n        for (let i = 0; i < Math.min(lineIndex, lines.length); i++) {\n            position += lines[i].length + 1; // +1 cho \\n\n        }\n\n        // Thêm vị trí trong dòng hiện tại\n        if (lineIndex < lines.length) {\n            position += Math.min(charIndex, lines[lineIndex].length);\n        }\n\n        return Math.max(0, Math.min(position, solution.length));\n    };\n\n    const handleDragEnter = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (isAddImage && e.dataTransfer.types.includes('text/plain')) {\n            setIsDraggingOver(true);\n        }\n    };\n\n    const handleDragLeave = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (!e.currentTarget.contains(e.relatedTarget)) {\n            setIsDraggingOver(false);\n            setDragPosition(null);\n        }\n    };\n\n    const handleTextareaChange = (e) => {\n        onSolutionChange(e.target.value);\n    };\n\n    // Hiển thị cursor dọc nhấp nháy\n    const getCursorStyle = () => {\n        if (!isDraggingOver || !cursorPosition) return { display: 'none' };\n\n        return {\n            position: 'absolute',\n            left: `${cursorPosition.x}px`,\n            top: `${cursorPosition.y}px`,\n            width: '2px',\n            height: '20px',\n            backgroundColor: '#3b82f6',\n            zIndex: 10,\n            animation: 'blink 1s infinite',\n            pointerEvents: 'none'\n        };\n    };\n\n    if (!isAddImage) {\n        // Chế độ xem bình thường\n        return (\n            <div className=\"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\">\n                <span className=\"font-semibold\">Lời giải:</span>{\" \"}\n                <MarkdownPreviewWithMath content={solution} />\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"mt-2 space-y-3\">\n            <div className=\"text-sm font-semibold text-green-700\">Lời giải:</div>\n            \n            {/* Editor với drop zone */}\n            <div\n                className={`relative border rounded-lg transition-all duration-200 ${\n                    isDraggingOver\n                        ? \"border-2 border-dashed border-blue-400 bg-blue-50\"\n                        : \"border-gray-300 hover:border-blue-300\"\n                }`}\n                onDragOver={handleDragOver}\n                onDragEnter={handleDragEnter}\n                onDragLeave={handleDragLeave}\n                onDrop={handleDrop}\n                ref={containerRef}\n            >\n                {/* Cursor dọc nhấp nháy */}\n                <div style={getCursorStyle()} />\n\n                {/* Textarea */}\n                <textarea\n                    ref={textareaRef}\n                    value={solution || ''}\n                    onChange={handleTextareaChange}\n                    placeholder=\"Nhập lời giải (hỗ trợ Markdown và LaTeX)...\"\n                    className={`w-full p-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px] ${\n                        isDraggingOver ? 'bg-transparent' : ''\n                    }`}\n                    style={{ lineHeight: '24px' }}\n                />\n\n                {/* Hint */}\n                {!solution && !isDraggingOver && (\n                    <div className=\"absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none\">\n                        <ImagePlus className=\"w-4 h-4\" />\n                        <span>Kéo ảnh vào để chèn</span>\n                    </div>\n                )}\n            </div>\n\n            {/* CSS cho animation nhấp nháy */}\n            <style jsx>{`\n                @keyframes blink {\n                    0%, 50% { opacity: 1; }\n                    51%, 100% { opacity: 0; }\n                }\n            `}</style>\n            \n            {/* Preview */}\n            {solution && (\n                <div className=\"border border-gray-200 rounded-lg p-3 bg-gray-50\">\n                    <div className=\"text-xs text-gray-500 mb-2\">Xem trước:</div>\n                    <MarkdownPreviewWithMath content={solution} />\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default SolutionEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,MAAM,QAAQ,cAAc;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,cAAc,GAAGC,IAAA,IAAgD;EAAAC,EAAA;EAAA,IAA/C;IAAEC,QAAQ;IAAEC,gBAAgB;IAAEC;EAAW,CAAC,GAAAJ,IAAA;EAC9D,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC;IAAEoB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EACpE,MAAMC,WAAW,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMsB,UAAU,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMuB,YAAY,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMwB,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBb,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMc,YAAY,GAAGH,CAAC,CAACI,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,IAAI,CAACF,YAAY,IAAI,CAACjB,gBAAgB,EAAE;;IAExC;IACA,IAAIoB,cAAc,GAAG,CAAC;IAEtB,IAAIhB,YAAY,KAAK,IAAI,EAAE;MACvBgB,cAAc,GAAGhB,YAAY;IACjC,CAAC,MAAM;MACH;MACAgB,cAAc,GAAGrB,QAAQ,GAAGA,QAAQ,CAACsB,MAAM,GAAG,CAAC;IACnD;;IAEA;IACA,MAAMC,aAAa,oBAAAC,MAAA,CAAeN,YAAY,QAAK;;IAEnD;IACA,MAAMO,WAAW,GAAGzB,QAAQ,GACtBA,QAAQ,CAAC0B,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC,GAAGE,aAAa,GAAGvB,QAAQ,CAAC0B,KAAK,CAACL,cAAc,CAAC,GAClFE,aAAa;IAEnBtB,gBAAgB,CAACwB,WAAW,CAAC;IAC7BnB,eAAe,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMqB,cAAc,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IAEnB,IAAIf,UAAU,IAAIS,WAAW,CAACiB,OAAO,EAAE;MACnC,MAAMC,QAAQ,GAAGlB,WAAW,CAACiB,OAAO;MACpC,MAAME,IAAI,GAAGD,QAAQ,CAACE,qBAAqB,CAAC,CAAC;MAC7C,MAAMtB,CAAC,GAAGM,CAAC,CAACiB,OAAO,GAAGF,IAAI,CAACG,IAAI;MAC/B,MAAMvB,CAAC,GAAGK,CAAC,CAACmB,OAAO,GAAGJ,IAAI,CAACK,GAAG;;MAE9B;MACA,MAAMC,QAAQ,GAAGC,mCAAmC,CAACR,QAAQ,EAAEpB,CAAC,EAAEC,CAAC,CAAC;MACpEJ,eAAe,CAAC8B,QAAQ,CAAC;MACzB5B,iBAAiB,CAAC;QAAEC,CAAC;QAAEC;MAAE,CAAC,CAAC;IAC/B;EACJ,CAAC;;EAED;EACA,MAAM2B,mCAAmC,GAAGA,CAACR,QAAQ,EAAEpB,CAAC,EAAEC,CAAC,KAAK;IAC5D,MAAM4B,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACX,QAAQ,CAAC;IAC/C,MAAMY,UAAU,GAAGC,QAAQ,CAACJ,KAAK,CAACG,UAAU,CAAC,IAAI,EAAE;IACnD,MAAME,QAAQ,GAAGD,QAAQ,CAACJ,KAAK,CAACK,QAAQ,CAAC,IAAI,EAAE;IAC/C,MAAMC,SAAS,GAAGD,QAAQ,GAAG,GAAG,CAAC,CAAC;;IAElC,MAAME,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACrC,CAAC,GAAG+B,UAAU,CAAC;IAC5C,MAAMO,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACtC,CAAC,GAAGmC,SAAS,CAAC;IAE3C,IAAI,CAAC5C,QAAQ,EAAE,OAAO,CAAC;IAEvB,MAAMiD,KAAK,GAAGjD,QAAQ,CAACkD,KAAK,CAAC,IAAI,CAAC;IAClC,IAAId,QAAQ,GAAG,CAAC;;IAEhB;IACA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACM,GAAG,CAACP,SAAS,EAAEI,KAAK,CAAC3B,MAAM,CAAC,EAAE6B,CAAC,EAAE,EAAE;MACxDf,QAAQ,IAAIa,KAAK,CAACE,CAAC,CAAC,CAAC7B,MAAM,GAAG,CAAC,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIuB,SAAS,GAAGI,KAAK,CAAC3B,MAAM,EAAE;MAC1Bc,QAAQ,IAAIU,IAAI,CAACM,GAAG,CAACJ,SAAS,EAAEC,KAAK,CAACJ,SAAS,CAAC,CAACvB,MAAM,CAAC;IAC5D;IAEA,OAAOwB,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEP,IAAI,CAACM,GAAG,CAAChB,QAAQ,EAAEpC,QAAQ,CAACsB,MAAM,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMgC,eAAe,GAAIvC,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIf,UAAU,IAAIa,CAAC,CAACI,YAAY,CAACoC,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC3DpD,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMqD,eAAe,GAAI1C,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAI,CAACF,CAAC,CAAC2C,aAAa,CAACC,QAAQ,CAAC5C,CAAC,CAAC6C,aAAa,CAAC,EAAE;MAC5CxD,iBAAiB,CAAC,KAAK,CAAC;MACxBE,eAAe,CAAC,IAAI,CAAC;IACzB;EACJ,CAAC;EAED,MAAMuD,oBAAoB,GAAI9C,CAAC,IAAK;IAChCd,gBAAgB,CAACc,CAAC,CAAC+C,MAAM,CAACC,KAAK,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC7D,cAAc,IAAI,CAACI,cAAc,EAAE,OAAO;MAAE0D,OAAO,EAAE;IAAO,CAAC;IAElE,OAAO;MACH7B,QAAQ,EAAE,UAAU;MACpBH,IAAI,KAAAT,MAAA,CAAKjB,cAAc,CAACE,CAAC,OAAI;MAC7B0B,GAAG,KAAAX,MAAA,CAAKjB,cAAc,CAACG,CAAC,OAAI;MAC5BwD,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,MAAM;MACdC,eAAe,EAAE,SAAS;MAC1BC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,mBAAmB;MAC9BC,aAAa,EAAE;IACnB,CAAC;EACL,CAAC;EAED,IAAI,CAACrE,UAAU,EAAE;IACb;IACA,oBACIN,OAAA;MAAK4E,SAAS,EAAC,uFAAuF;MAAAC,QAAA,gBAClG7E,OAAA;QAAM4E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAG,eACpDjF,OAAA,CAACF,uBAAuB;QAACoF,OAAO,EAAE9E;MAAS;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEd;EAEA,oBACIjF,OAAA;IAAK4E,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3B7E,OAAA;MAAK4E,SAAS,EAAC,sCAAsC;MAAAC,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAGrEjF,OAAA;MACI4E,SAAS,4DAAAhD,MAAA,CACLrB,cAAc,GACR,mDAAmD,GACnD,uCAAuC,CAC9C;MACH4E,UAAU,EAAEpD,cAAe;MAC3BqD,WAAW,EAAE1B,eAAgB;MAC7B2B,WAAW,EAAExB,eAAgB;MAC7ByB,MAAM,EAAEpE,UAAW;MACnBqE,GAAG,EAAEtE,YAAa;MAAA4D,QAAA,gBAGlB7E,OAAA;QAAK0C,KAAK,EAAE0B,cAAc,CAAC;MAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGhCjF,OAAA;QACIuF,GAAG,EAAExE,WAAY;QACjBoD,KAAK,EAAE/D,QAAQ,IAAI,EAAG;QACtBoF,QAAQ,EAAEvB,oBAAqB;QAC/BwB,WAAW,EAAC,yEAA6C;QACzDb,SAAS,kHAAAhD,MAAA,CACLrB,cAAc,GAAG,gBAAgB,GAAG,EAAE,CACvC;QACHmC,KAAK,EAAE;UAAEG,UAAU,EAAE;QAAO;MAAE;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAGD,CAAC7E,QAAQ,IAAI,CAACG,cAAc,iBACzBP,OAAA;QAAK4E,SAAS,EAAC,6FAA6F;QAAAC,QAAA,gBACxG7E,OAAA,CAACJ,SAAS;UAACgF,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjCjF,OAAA;UAAA6E,QAAA,EAAM;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNjF,OAAA;MAAO0F,GAAG;MAAAb,QAAA;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKD,CAAC,EAGT7E,QAAQ,iBACLJ,OAAA;MAAK4E,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC7D7E,OAAA;QAAK4E,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DjF,OAAA,CAACF,uBAAuB;QAACoF,OAAO,EAAE9E;MAAS;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC9E,EAAA,CA7LIF,cAAc;AAAA0F,EAAA,GAAd1F,cAAc;AA+LpB,eAAeA,cAAc;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}