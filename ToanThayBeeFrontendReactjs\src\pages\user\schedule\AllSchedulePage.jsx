import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { ArrowLeft, CalendarClock, Clock, Users, BookOpen, MapPin, Filter, Search, Calendar, ChevronDown, ChevronUp } from 'lucide-react';
import UserLayoutHome from '../../../layouts/UserLayoutHome';
import { fetchClassesPublic } from "../../../features/class/classSlice";
import LoadingSpinner from '../../../components/loading/LoadingSpinner';
import Schedule from '../../../components/Schedule';
import { fetchCodesByType } from "../../../features/code/codeSlice";

const AllSchedulePage = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { classes, loading } = useSelector((state) => state.classes);
    const { codes } = useSelector((state) => state.codes);

    const [searchTerm, setSearchTerm] = useState('');
    const [selectedGrade, setSelectedGrade] = useState('');
    const [selectedDay, setSelectedDay] = useState('');
    const [expandedClass, setExpandedClass] = useState(null);

    useEffect(() => {
        dispatch(fetchClassesPublic());
    }, [dispatch]);
    useEffect(() => {
        dispatch(fetchCodesByType(['dow', 'grade']));
    }, [dispatch]);

    // Format time to show only hours and minutes (HH:MM)
    const formatTime = (timeString) => {
        if (!timeString) return '';
        if (timeString.length === 5 && timeString.includes(':')) {
            return timeString;
        }
        return timeString.substring(0, 5);
    };

    // Get day of week description
    const getDayDescription = (dayCode) => {
        if (!dayCode) return '';
        if (dayCode === 'CN') return 'CN';
        return codes['dow']?.find((code) => code.code === dayCode)?.description || dayCode;
    };

    // Convert day code to short format for Schedule component
    const getDayShort = (dayCode) => {
        if (!dayCode) return '';
        const dayMap = {
            2: 'T2',
            3: 'T3',
            4: 'T4',
            5: 'T5',
            6: 'T6',
            7: 'T7',
            'CN': 'CN'
        };
        return dayMap[dayCode] || dayCode;
    };

    // Get grade description
    const getGradeDescription = (gradeCode) => {
        if (!gradeCode) return '';
        return codes['grade']?.find((code) => code.code === gradeCode)?.description || `Lớp ${gradeCode}`;
    };

    // Filter classes based on search and filters
    const filteredClasses = classes.filter(classItem => {
        const matchesSearch = classItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            classItem.academicYear.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesGrade = !selectedGrade || classItem.grade === selectedGrade;

        const matchesDay = !selectedDay ||
                          classItem.dayOfWeek1 === selectedDay ||
                          classItem.dayOfWeek2 === selectedDay;

        // Filter out classes that don't have any valid session times
        const hasValidSession = (classItem.startTime1 && classItem.endTime1) ||
                               (classItem.startTime2 && classItem.endTime2);

        return matchesSearch && matchesGrade && matchesDay && hasValidSession;
    });

    // Prepare classes for Schedule component (convert day codes to short format)
    const classesForSchedule = filteredClasses.map(classItem => ({
        ...classItem,
        dayOfWeek1: getDayShort(classItem.dayOfWeek1),
        dayOfWeek2: getDayShort(classItem.dayOfWeek2)
    }));

    // Group classes by day of week
    const groupedClasses = {};
    filteredClasses.forEach(classItem => {
        // Add to dayOfWeek1
        if (classItem.dayOfWeek1) {
            const day1 = getDayDescription(classItem.dayOfWeek1);
            if (!groupedClasses[day1]) groupedClasses[day1] = [];
            groupedClasses[day1].push({
                ...classItem,
                sessionType: 'session1',
                dayOfWeek: classItem.dayOfWeek1,
                startTime: classItem.startTime1,
                endTime: classItem.endTime1
            });
        }

        // Add to dayOfWeek2 if exists
        if (classItem.dayOfWeek2) {
            const day2 = getDayDescription(classItem.dayOfWeek2);
            if (!groupedClasses[day2]) groupedClasses[day2] = [];
            groupedClasses[day2].push({
                ...classItem,
                sessionType: 'session2',
                dayOfWeek: classItem.dayOfWeek2,
                startTime: classItem.startTime2,
                endTime: classItem.endTime2
            });
        }
    });

    // Sort days of week
    const dayOrder = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'CN'];
    const sortedDays = Object.keys(groupedClasses).sort((a, b) => {
        return dayOrder.indexOf(a) - dayOrder.indexOf(b);
    });

    if (loading) {
        return (
            <UserLayoutHome>
                <div className="min-h-screen flex items-center justify-center">
                    <LoadingSpinner size="4rem" showText={true} text="Đang tải lịch học..." />
                </div>
            </UserLayoutHome>
        );
    }

    return (
        <UserLayoutHome>
            <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-green-50">
                {/* Header */}
                <section className="w-full px-4 py-16">
                    <div className="max-w-screen-xl mx-auto">
                        <motion.div
                            className="text-center mb-12"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            {/* Back Button */}
                            <motion.button
                                onClick={() => navigate('/')}
                                className="inline-flex items-center gap-2 px-4 py-2 text-emerald-700 hover:text-emerald-800 transition-colors duration-300 mb-6"
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.5 }}
                                whileHover={{ x: -5 }}
                            >
                                <ArrowLeft size={20} />
                                Quay về trang chủ
                            </motion.button>

                            <motion.div
                                className="inline-block px-4 py-2 bg-emerald-100 text-emerald-700 rounded-full text-sm font-medium mb-4"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 0.2 }}
                            >
                                Thời khóa biểu
                            </motion.div>

                            <motion.h1
                                className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 font-cubano mb-6 flex items-center justify-center gap-4"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                            >
                                <CalendarClock className="w-12 h-12 text-emerald-600" />
                                Lịch học chi tiết
                            </motion.h1>

                            <motion.p
                                className="text-gray-600 max-w-3xl mx-auto text-lg lg:text-xl"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.5 }}
                            >
                                Xem chi tiết lịch học tất cả các lớp trong năm học 2024-2025. Tìm kiếm và lọc theo khối lớp,
                                thứ trong tuần để dễ dàng theo dõi thời khóa biểu.
                            </motion.p>
                        </motion.div>

                        {/* Filters */}
                        <motion.div
                            className="bg-white rounded-2xl shadow-lg p-6 mb-8"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.7 }}
                        >
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {/* Search */}
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                    <input
                                        type="text"
                                        placeholder="Tìm kiếm lớp học..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none transition-colors"
                                    />
                                </div>

                                {/* Grade Filter */}
                                <div className="relative">
                                    <select
                                        value={selectedGrade}
                                        onChange={(e) => setSelectedGrade(e.target.value)}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none transition-colors appearance-none bg-white"
                                    >
                                        <option value="">Tất cả khối lớp</option>
                                        {codes['grade']?.map((grade) => (
                                            <option key={grade.code} value={grade.code}>
                                                {grade.description}
                                            </option>
                                        ))}
                                    </select>
                                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                                </div>

                                {/* Day Filter */}
                                <div className="relative">
                                    <select
                                        value={selectedDay}
                                        onChange={(e) => setSelectedDay(e.target.value)}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none transition-colors appearance-none bg-white"
                                    >
                                        <option value="">Tất cả các ngày</option>
                                        {codes['dow']?.map((day) => (
                                            <option key={day.code} value={day.code}>
                                                {day.description}
                                            </option>
                                        ))}
                                    </select>
                                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                                </div>
                            </div>

                            {/* Results count */}
                            <div className="mt-4 text-sm text-gray-600">
                                Tìm thấy <span className="font-semibold text-emerald-600">{filteredClasses.length}</span> lớp học
                            </div>
                        </motion.div>

                        {/* Calendar View */}
                        <motion.div
                            className="bg-white rounded-2xl shadow-lg p-6 mb-8"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.8 }}
                        >
                            <div className="flex items-center gap-3 mb-6">
                                <CalendarClock className="w-6 h-6 text-emerald-600" />
                                <h3 className="text-xl font-bold text-gray-900">Lịch học theo tuần</h3>
                            </div>

                            <div className="bg-gray-50 rounded-xl p-4">
                                <Schedule classes={classesForSchedule} />
                            </div>
                        </motion.div>

                        {/* Schedule by Days */}
                        <section className="w-full">
                            <div className="max-w-screen-xl mx-auto">
                                {sortedDays.length > 0 ? (
                                    <div className="space-y-8">
                                        {sortedDays.map((day, dayIndex) => (
                                            <motion.div
                                                key={day}
                                                className="bg-white rounded-2xl shadow-lg overflow-hidden"
                                                initial={{ opacity: 0, y: 30 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.6, delay: 0.9 + dayIndex * 0.1 }}
                                            >
                                                {/* Day Header */}
                                                <div className="bg-gradient-to-r from-emerald-600 to-green-600 text-white py-4 px-6">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-3">
                                                            <Calendar className="w-6 h-6" />
                                                            <h3 className="text-xl font-bold">{day}</h3>
                                                        </div>
                                                        <span className="bg-white/20 px-3 py-1 rounded-full text-sm font-medium">
                                                            {groupedClasses[day].length} lớp học
                                                        </span>
                                                    </div>
                                                </div>

                                                {/* Classes List */}
                                                <div className="divide-y divide-gray-200">
                                                    {groupedClasses[day]
                                                        .sort((a, b) => {
                                                            // Handle null/undefined startTime values
                                                            if (!a.startTime && !b.startTime) return 0;
                                                            if (!a.startTime) return 1;
                                                            if (!b.startTime) return -1;
                                                            return a.startTime.localeCompare(b.startTime);
                                                        })
                                                        .map((classItem, index) => (
                                                        <motion.div
                                                            key={`${classItem.id}-${classItem.sessionType}`}
                                                            className="p-6 hover:bg-emerald-50 transition-colors duration-200 cursor-pointer"
                                                            initial={{ opacity: 0, x: -20 }}
                                                            animate={{ opacity: 1, x: 0 }}
                                                            transition={{ duration: 0.4, delay: 1.0 + dayIndex * 0.1 + index * 0.05 }}
                                                            onClick={() => setExpandedClass(
                                                                expandedClass === `${classItem.id}-${classItem.sessionType}`
                                                                    ? null
                                                                    : `${classItem.id}-${classItem.sessionType}`
                                                            )}
                                                        >
                                                            <div className="flex items-center justify-between">
                                                                <div className="flex items-center gap-4">
                                                                    {/* Time Badge */}
                                                                    <div className="bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg font-semibold text-sm min-w-[120px] text-center">
                                                                        <Clock className="w-4 h-4 inline mr-1" />
                                                                        {formatTime(classItem.startTime)} - {formatTime(classItem.endTime)}
                                                                    </div>

                                                                    {/* Class Info */}
                                                                    <div className="flex-1">
                                                                        <div className="flex items-center gap-2 mb-1">
                                                                            <h4 className="text-lg font-semibold text-gray-900">{classItem.name}</h4>
                                                                            {classItem.sessionType === 'session2' && (
                                                                                <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
                                                                                    Buổi 2
                                                                                </span>
                                                                            )}
                                                                        </div>
                                                                        <div className="flex items-center gap-4 text-sm text-gray-600">
                                                                            <span className="flex items-center gap-1">
                                                                                <BookOpen className="w-4 h-4" />
                                                                                {getGradeDescription(classItem.grade)}
                                                                            </span>
                                                                            <span className="flex items-center gap-1">
                                                                                <Calendar className="w-4 h-4" />
                                                                                {classItem.academicYear}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                {/* Expand Icon */}
                                                                <div className="text-gray-400">
                                                                    {expandedClass === `${classItem.id}-${classItem.sessionType}` ? (
                                                                        <ChevronUp className="w-5 h-5" />
                                                                    ) : (
                                                                        <ChevronDown className="w-5 h-5" />
                                                                    )}
                                                                </div>
                                                            </div>

                                                            {/* Expanded Details */}
                                                            {expandedClass === `${classItem.id}-${classItem.sessionType}` && (
                                                                <motion.div
                                                                    className="mt-4 pt-4 border-t border-gray-200"
                                                                    initial={{ opacity: 0, height: 0 }}
                                                                    animate={{ opacity: 1, height: 'auto' }}
                                                                    exit={{ opacity: 0, height: 0 }}
                                                                    transition={{ duration: 0.3 }}
                                                                >
                                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                                        {/* Left - Class Details */}
                                                                        <div className="space-y-3">
                                                                            <h5 className="font-semibold text-gray-900 mb-3">Thông tin lớp học</h5>

                                                                            <div className="flex items-center gap-2 text-sm">
                                                                                <span className="font-medium text-gray-700 min-w-[100px]">Tên lớp:</span>
                                                                                <span className="text-gray-600">{classItem.name}</span>
                                                                            </div>

                                                                            <div className="flex items-center gap-2 text-sm">
                                                                                <span className="font-medium text-gray-700 min-w-[100px]">Khối lớp:</span>
                                                                                <span className="text-gray-600">{getGradeDescription(classItem.grade)}</span>
                                                                            </div>

                                                                            <div className="flex items-center gap-2 text-sm">
                                                                                <span className="font-medium text-gray-700 min-w-[100px]">Năm học:</span>
                                                                                <span className="text-gray-600">{classItem.academicYear}</span>
                                                                            </div>

                                                                            <div className="flex items-center gap-2 text-sm">
                                                                                <span className="font-medium text-gray-700 min-w-[100px]">Trạng thái:</span>
                                                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                                                    classItem.status === 'LHD'
                                                                                        ? 'bg-green-100 text-green-700'
                                                                                        : 'bg-gray-100 text-gray-700'
                                                                                }`}>
                                                                                    {classItem.status === 'LHD' ? 'Đang hoạt động' : classItem.status}
                                                                                </span>
                                                                            </div>

                                                                            {classItem.description && (
                                                                                <div className="text-sm">
                                                                                    <span className="font-medium text-gray-700 block mb-1">Mô tả:</span>
                                                                                    <p className="text-gray-600 leading-relaxed">{classItem.description}</p>
                                                                                </div>
                                                                            )}
                                                                        </div>

                                                                        {/* Right - Schedule Details */}
                                                                        <div className="space-y-3">
                                                                            <h5 className="font-semibold text-gray-900 mb-3">Lịch học chi tiết</h5>

                                                                            {/* Session 1 */}
                                                                            <div className="bg-emerald-50 p-3 rounded-lg">
                                                                                <div className="flex items-center gap-2 mb-2">
                                                                                    <span className="font-medium text-emerald-700">Buổi 1:</span>
                                                                                    <span className="text-emerald-600">{getDayDescription(classItem.dayOfWeek1)}</span>
                                                                                </div>
                                                                                <div className="text-sm text-emerald-600">
                                                                                    {formatTime(classItem.startTime1)} - {formatTime(classItem.endTime1)}
                                                                                </div>
                                                                            </div>

                                                                            {/* Session 2 if exists */}
                                                                            {classItem.dayOfWeek2 && (
                                                                                <div className="bg-blue-50 p-3 rounded-lg">
                                                                                    <div className="flex items-center gap-2 mb-2">
                                                                                        <span className="font-medium text-blue-700">Buổi 2:</span>
                                                                                        <span className="text-blue-600">{getDayDescription(classItem.dayOfWeek2)}</span>
                                                                                    </div>
                                                                                    <div className="text-sm text-blue-600">
                                                                                        {formatTime(classItem.startTime2)} - {formatTime(classItem.endTime2)}
                                                                                    </div>
                                                                                </div>
                                                                            )}

                                                                            {/* Contact Info */}
                                                                            <div className="bg-gray-50 p-3 rounded-lg">
                                                                                <h6 className="font-medium text-gray-700 mb-2">Liên hệ đăng ký</h6>
                                                                                <div className="space-y-1 text-sm text-gray-600">
                                                                                    <p>📞 Hotline: 0333.726.202</p>
                                                                                    <p>💬 Zalo: 0333.726.202</p>
                                                                                    <p>📧 Email: <EMAIL></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </motion.div>
                                                            )}
                                                        </motion.div>
                                                    ))}
                                                </div>
                                            </motion.div>
                                        ))}
                                    </div>
                                ) : (
                                    <motion.div
                                        className="bg-white rounded-2xl shadow-lg p-12 text-center"
                                        initial={{ opacity: 0, y: 30 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: 0.9 }}
                                    >
                                        <CalendarClock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                                        <h3 className="text-xl font-semibold text-gray-700 mb-2">Không tìm thấy lớp học</h3>
                                        <p className="text-gray-500">
                                            Không có lớp học nào phù hợp với bộ lọc hiện tại. Hãy thử thay đổi từ khóa tìm kiếm hoặc bộ lọc.
                                        </p>
                                    </motion.div>
                                )}
                            </div>
                        </section>
                    </div>
                </section>

                {/* Summary Statistics */}
                <section className="w-full px-4 py-16 bg-gradient-to-r from-emerald-600 to-green-600">
                    <div className="max-w-screen-xl mx-auto">
                        <motion.div
                            className="grid grid-cols-2 md:grid-cols-4 gap-6 text-white"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 1.2 }}
                        >
                            <motion.div
                                className="text-center"
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.4, delay: 1.3 }}
                            >
                                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <BookOpen className="w-8 h-8 text-white" />
                                </div>
                                <div className="text-3xl font-bold mb-1">{classes.length}</div>
                                <div className="text-emerald-100 text-sm">Tổng số lớp</div>
                            </motion.div>

                            <motion.div
                                className="text-center"
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.4, delay: 1.4 }}
                            >
                                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <Calendar className="w-8 h-8 text-white" />
                                </div>
                                <div className="text-3xl font-bold mb-1">{sortedDays.length}</div>
                                <div className="text-emerald-100 text-sm">Ngày trong tuần</div>
                            </motion.div>

                            <motion.div
                                className="text-center"
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.4, delay: 1.5 }}
                            >
                                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <Users className="w-8 h-8 text-white" />
                                </div>
                                <div className="text-3xl font-bold mb-1">15</div>
                                <div className="text-emerald-100 text-sm">Học sinh/lớp</div>
                            </motion.div>

                            <motion.div
                                className="text-center"
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.4, delay: 1.6 }}
                            >
                                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <Clock className="w-8 h-8 text-white" />
                                </div>
                                <div className="text-3xl font-bold mb-1">90</div>
                                <div className="text-emerald-100 text-sm">Phút/buổi học</div>
                            </motion.div>
                        </motion.div>
                    </div>
                </section>
            </div>
        </UserLayoutHome>
    );
};

export default AllSchedulePage;
