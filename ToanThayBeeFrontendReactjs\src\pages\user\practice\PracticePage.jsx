import UserLayout from "../../../layouts/UserLayout";
import ShowTotalResult from "../../../components/bar/ShowTotalResult";
import ExamCard from "../../../components/card/ExamCard";
import { useDispatch, useSelector } from "react-redux";
import { useState } from "react";
import { setCurrentPage } from "../../../features/exam/examSlice";
import Pagination from "../../../components/Pagination";
import NoDataFound from "../../../assets/images/error-file.png";
import FilterExamTopbar from "../../../components/filter/FilterExamTopbar";
import FilterExamSidebar from "../../../components/filter/FilterExamSidebar";
import {
    Filter,
    BookOpen,
    Loader
} from "lucide-react";

const PracticePage = () => {
    const { exams, pagination } = useSelector((state) => state.exams);
    const dispatch = useDispatch();
    const { loading } = useSelector((state) => state.states);
    const { page: currentPage, total: totalItems } = pagination;
    const { codes } = useSelector((state) => state.codes);
    const [showMobileSidebar, setShowMobileSidebar] = useState(false);

    const handlePageChange = (page) => {
        dispatch(setCurrentPage(page));
    };

    return (
        <UserLayout>
            <div className="flex flex-col lg:flex-row w-full bg-gray-50 min-h-screen">
                {/* Left Sidebar - Filters - Hidden on mobile, shown on desktop */}
                <div className="hidden lg:block">
                    <FilterExamSidebar />
                </div>

                {/* Main Content */}
                <div className="flex-1 transition-all duration-300 w-full lg:w-auto">
                    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 max-w-full lg:max-w-6xl">
                        {/* Page Header */}
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 gap-4">
                            <div className="text-center sm:text-left">
                                <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 flex items-center justify-start gap-2 sm:gap-3">
                                    <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl flex items-center justify-center">
                                        <BookOpen className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                                    </div>
                                    Danh sách đề thi
                                </h1>
                                <p className="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">Khám phá và luyện tập với các đề thi chất lượng</p>
                            </div>
                            <div className="flex items-center justify-center sm:justify-start gap-2 px-3 sm:px-4 py-2 bg-sky-50 border border-sky-200 rounded-lg">
                                <div className="w-2 h-2 bg-sky-500 rounded-full animate-pulse"></div>
                                <span className="text-sky-700 text-sm font-medium">
                                    {totalItems || exams.length} đề thi
                                </span>
                            </div>
                        </div>

                        {/* Mobile Filters */}
                        <div className="lg:hidden mb-6">
                            <button
                                className="w-full bg-sky-600 text-white p-3 rounded-lg flex items-center justify-center gap-2"
                                onClick={() => setShowMobileSidebar(!showMobileSidebar)}
                            >
                                <Filter className="w-4 h-4" />
                                Bộ lọc
                            </button>

                            {showMobileSidebar && (
                                <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
                                    <FilterExamTopbar />
                                </div>
                            )}
                        </div>

                        {/* Show Total Result */}
                        <div className="mb-4">
                            <ShowTotalResult />
                        </div>

                        {/* Exams Section */}
                        <div className="mb-6 sm:mb-8">
                            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                                {loading ? (
                                    <div className="p-6 sm:p-8 text-center text-gray-500">
                                        <Loader size={32} className="sm:w-10 sm:h-10 mx-auto mb-4 text-gray-300 animate-spin" />
                                        <p className="text-sm sm:text-base">Đang tải danh sách đề thi...</p>
                                    </div>
                                ) : exams && exams.length > 0 && exams[0] ? (
                                    <div className="p-3 sm:p-6">
                                        <div className="space-y-3 sm:space-y-4">
                                            {exams.map((exam, index) => (
                                                <ExamCard
                                                    key={index}
                                                    exam={exam}
                                                    codes={codes}
                                                    horizontal={true}
                                                />
                                            ))}
                                        </div>

                                        {/* Pagination */}
                                        <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-100">
                                            <Pagination
                                                currentPage={currentPage}
                                                totalItems={totalItems}
                                                limit={10}
                                                onPageChange={handlePageChange}
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <div className="p-6 sm:p-8 text-center text-gray-500">
                                        <img src={NoDataFound} alt="No Data Found" className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 opacity-50" />
                                        <p className="text-sm sm:text-base">Không có đề thi nào phù hợp.</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Panel - Quick Actions - Hidden on mobile and tablet */}
                <div className="sticky top-20 py-4 w-[280px] xl:w-[300px] h-[90vh] overflow-y-auto hide-scrollbar hidden xl:block">
                    {/* You can add quick actions or additional content here */}
                </div>
            </div>
        </UserLayout>
    );
};

export default PracticePage;
