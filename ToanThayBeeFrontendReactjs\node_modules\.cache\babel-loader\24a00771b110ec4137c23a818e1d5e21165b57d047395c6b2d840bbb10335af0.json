{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as sheetApi from \"../../services/sheetApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nexport const readSheetAndUpdateTuitionSheet = createAsyncThunk(\"sheet/readSheetAndUpdateTuitionSheet\", async (_ref, _ref2) => {\n  let {\n    sheetUrl,\n    month,\n    colNames,\n    sheetIndex\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, sheetApi.readSheetAndUpdateTuitionSheet, {\n    sheetUrl,\n    month,\n    colNames,\n    sheetIndex\n  }, null, true, false, false, false);\n});\nconst initialState = {\n  loadingUpdate: false,\n  error: null,\n  success: false,\n  data: null,\n  sheetUrl: null,\n  month: null,\n  sheetIndex: 0,\n  colNames: {\n    studentPhone: \"SĐT HS\",\n    parentPhone: \"SĐT PH\",\n    fullName: \"HỌ VÀ TÊN\",\n    targetMonthCol: \"ĐÃ ĐÓNG T ...\"\n  }\n};\nconst sheetSlice = createSlice({\n  name: \"sheet\",\n  initialState,\n  reducers: {\n    setSheetUrl: (state, action) => {\n      state.sheetUrl = action.payload;\n    },\n    setMonth: (state, action) => {\n      state.month = action.payload;\n    },\n    setSheetIndex: (state, action) => {\n      state.sheetIndex = action.payload;\n    },\n    setColNames: (state, action) => {\n      state.colNames = action.payload;\n    },\n    resetSheetState: state => {\n      state.loadingUpdate = false;\n      state.error = null;\n      state.success = false;\n      state.data = null;\n      state.sheetUrl = null;\n      state.month = null;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(readSheetAndUpdateTuitionSheet.pending, state => {\n      state.loadingUpdate = true;\n      state.success = false;\n    }).addCase(readSheetAndUpdateTuitionSheet.fulfilled, (state, action) => {\n      state.loadingUpdate = false;\n      state.success = true;\n      if (action.payload) {\n        state.data = action.payload.data;\n      }\n    }).addCase(readSheetAndUpdateTuitionSheet.rejected, (state, action) => {\n      state.loadingUpdate = false;\n    });\n  }\n});\nexport const {\n  setSheetUrl,\n  setMonth,\n  setColNames,\n  resetSheetState,\n  setSheetIndex\n} = sheetSlice.actions;\nexport default sheetSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "sheetApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "readSheetAndUpdateTuitionSheet", "_ref", "_ref2", "sheetUrl", "month", "colNames", "sheetIndex", "dispatch", "initialState", "loadingUpdate", "error", "success", "data", "studentPhone", "parentPhone", "fullName", "targetMonthCol", "sheetSlice", "name", "reducers", "setSheetUrl", "state", "action", "payload", "setMonth", "setSheetIndex", "setColNames", "resetSheetState", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/sheet/sheetSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as sheetApi from \"../../services/sheetApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\n\r\nexport const readSheetAndUpdateTuitionSheet = createAsyncThunk(\r\n    \"sheet/readSheetAndUpdateTuitionSheet\",\r\n    async ({ sheetUrl, month, colNames, sheetIndex }, { dispatch }) => {\r\n        return await apiHandler(dispatch, sheetApi.readSheetAndUpdateTuitionSheet, { sheetUrl, month, colNames, sheetIndex }, null, true, false, false, false);\r\n\r\n    }\r\n);\r\n\r\nconst initialState = {\r\n    loadingUpdate: false,\r\n    error: null,\r\n    success: false,\r\n    data: null,\r\n    sheetUrl: null,\r\n    month: null,\r\n    sheetIndex: 0,\r\n    colNames: {\r\n        studentPhone: \"SĐT HS\",\r\n        parentPhone: \"SĐT PH\",\r\n        fullName: \"HỌ VÀ TÊN\",\r\n        targetMonthCol: \"ĐÃ ĐÓNG T ...\"\r\n    }\r\n};\r\n\r\nconst sheetSlice = createSlice({\r\n    name: \"sheet\",\r\n    initialState,\r\n    reducers: {\r\n        setSheetUrl: (state, action) => {\r\n            state.sheetUrl = action.payload;\r\n        },\r\n        setMonth: (state, action) => {\r\n            state.month = action.payload;\r\n        },\r\n        setSheetIndex: (state, action) => {\r\n            state.sheetIndex = action.payload;\r\n        },\r\n        setColNames: (state, action) => {\r\n            state.colNames = action.payload;\r\n        },\r\n        resetSheetState: (state) => {\r\n            state.loadingUpdate = false;\r\n            state.error = null;\r\n            state.success = false;\r\n            state.data = null;\r\n            state.sheetUrl = null;\r\n            state.month = null;\r\n        },\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(readSheetAndUpdateTuitionSheet.pending, (state) => {\r\n                state.loadingUpdate = true;\r\n                state.success = false;\r\n            })\r\n            .addCase(readSheetAndUpdateTuitionSheet.fulfilled, (state, action) => {\r\n                state.loadingUpdate = false;\r\n                state.success = true;\r\n                if (action.payload) {\r\n                    state.data = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(readSheetAndUpdateTuitionSheet.rejected, (state, action) => {\r\n                state.loadingUpdate = false;\r\n            });\r\n    },\r\n});\r\n\r\nexport const {\r\n    setSheetUrl,\r\n    setMonth,\r\n    setColNames,\r\n    resetSheetState,\r\n    setSheetIndex\r\n} = sheetSlice.actions;\r\nexport default sheetSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,QAAQ,MAAM,yBAAyB;AACnD,SAASC,UAAU,QAAQ,wBAAwB;AAEnD,OAAO,MAAMC,8BAA8B,GAAGH,gBAAgB,CAC1D,sCAAsC,EACtC,OAAAI,IAAA,EAAAC,KAAA,KAAmE;EAAA,IAA5D;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAW,CAAC,GAAAL,IAAA;EAAA,IAAE;IAAEM;EAAS,CAAC,GAAAL,KAAA;EAC1D,OAAO,MAAMH,UAAU,CAACQ,QAAQ,EAAET,QAAQ,CAACE,8BAA8B,EAAE;IAAEG,QAAQ;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAE1J,CACJ,CAAC;AAED,MAAME,YAAY,GAAG;EACjBC,aAAa,EAAE,KAAK;EACpBC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,KAAK;EACdC,IAAI,EAAE,IAAI;EACVT,QAAQ,EAAE,IAAI;EACdC,KAAK,EAAE,IAAI;EACXE,UAAU,EAAE,CAAC;EACbD,QAAQ,EAAE;IACNQ,YAAY,EAAE,QAAQ;IACtBC,WAAW,EAAE,QAAQ;IACrBC,QAAQ,EAAE,WAAW;IACrBC,cAAc,EAAE;EACpB;AACJ,CAAC;AAED,MAAMC,UAAU,GAAGrB,WAAW,CAAC;EAC3BsB,IAAI,EAAE,OAAO;EACbV,YAAY;EACZW,QAAQ,EAAE;IACNC,WAAW,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC5BD,KAAK,CAAClB,QAAQ,GAAGmB,MAAM,CAACC,OAAO;IACnC,CAAC;IACDC,QAAQ,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MACzBD,KAAK,CAACjB,KAAK,GAAGkB,MAAM,CAACC,OAAO;IAChC,CAAC;IACDE,aAAa,EAAEA,CAACJ,KAAK,EAAEC,MAAM,KAAK;MAC9BD,KAAK,CAACf,UAAU,GAAGgB,MAAM,CAACC,OAAO;IACrC,CAAC;IACDG,WAAW,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MAC5BD,KAAK,CAAChB,QAAQ,GAAGiB,MAAM,CAACC,OAAO;IACnC,CAAC;IACDI,eAAe,EAAGN,KAAK,IAAK;MACxBA,KAAK,CAACZ,aAAa,GAAG,KAAK;MAC3BY,KAAK,CAACX,KAAK,GAAG,IAAI;MAClBW,KAAK,CAACV,OAAO,GAAG,KAAK;MACrBU,KAAK,CAACT,IAAI,GAAG,IAAI;MACjBS,KAAK,CAAClB,QAAQ,GAAG,IAAI;MACrBkB,KAAK,CAACjB,KAAK,GAAG,IAAI;IACtB;EACJ,CAAC;EACDwB,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC9B,8BAA8B,CAAC+B,OAAO,EAAGV,KAAK,IAAK;MACxDA,KAAK,CAACZ,aAAa,GAAG,IAAI;MAC1BY,KAAK,CAACV,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDmB,OAAO,CAAC9B,8BAA8B,CAACgC,SAAS,EAAE,CAACX,KAAK,EAAEC,MAAM,KAAK;MAClED,KAAK,CAACZ,aAAa,GAAG,KAAK;MAC3BY,KAAK,CAACV,OAAO,GAAG,IAAI;MACpB,IAAIW,MAAM,CAACC,OAAO,EAAE;QAChBF,KAAK,CAACT,IAAI,GAAGU,MAAM,CAACC,OAAO,CAACX,IAAI;MACpC;IACJ,CAAC,CAAC,CACDkB,OAAO,CAAC9B,8BAA8B,CAACiC,QAAQ,EAAE,CAACZ,KAAK,EAAEC,MAAM,KAAK;MACjED,KAAK,CAACZ,aAAa,GAAG,KAAK;IAC/B,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACTW,WAAW;EACXI,QAAQ;EACRE,WAAW;EACXC,eAAe;EACfF;AACJ,CAAC,GAAGR,UAAU,CAACiB,OAAO;AACtB,eAAejB,UAAU,CAACkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}