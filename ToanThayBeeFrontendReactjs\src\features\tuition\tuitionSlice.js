import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as tuitionApi from "../../services/tuitionApi";
// import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";
// import { setErrorMessage, setSuccessMessage } from "../state/stateApiSlice";
import { initialPaginationState, paginationReducers } from "../pagination/paginationReducer";
import { initialFilterState, filterReducers } from "../filter/filterReducer";

// Tuition Payment Thunks
export const fetchTuitionPayments = createAsyncThunk(
  "tuition/fetchTuitionPayments",
  async (params, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getAllTuitionPaymentsAPI,
      params,
      null,
      true,
      false
    );
  }
);

export const fetchUserTuitionPaymentsAdmin = createAsyncThunk(
  "tuition/fetchUserTuitionPaymentsAdmin",
  async (params, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getUserTuitionPaymentsAdminAPI,
      params,
      null,
      true,
      false
    );
  }
);

export const fetchUserTuitionPayments = createAsyncThunk(
  "tuition/fetchUserTuitionPayments",
  async (params, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getUserTuitionPaymentsAPI,
      params,
      null,
      false,
      false
    );
  }
);

export const fetchTuitionPaymentByIdAdmin = createAsyncThunk(
  "tuition/fetchTuitionPaymentByIdAdmin",
  async (id, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getTuitionPaymentByIdAdminAPI,
      id,
      null,
      true,
      false,
      false,
      false
    );
  }
);

export const fetchTuitionPaymentByIdUser = createAsyncThunk(
  "tuition/fetchTuitionPaymentByIdUser",
  async (id, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getTuitionPaymentByIdUserAPI,
      id,
      null,
      true,
      false
    );
  }
);

// Alias cho fetchTuitionPaymentByIdUser để sử dụng trong trang chi tiết học phí
export const fetchUserTuitionPaymentById = fetchTuitionPaymentByIdUser;

export const createTuitionPayment = createAsyncThunk(
  "tuition/createTuitionPayment",
  async (paymentData, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.createTuitionPaymentAPI,
      paymentData,
      null,
      true,
      false,
      false,
      false
    );
  }
);

export const createBatchTuitionPayments = createAsyncThunk(
  "tuition/createBatchTuitionPayments",
  async (batchData, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.createBatchTuitionPaymentsAPI,
      batchData,
      null,
      true,
      false,
      false,
      false
    );
  }
);

export const updateTuitionPayment = createAsyncThunk(
  "tuition/updateTuitionPayment",
  async ({ id, paymentData }, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.updateTuitionPaymentAPI,
      { id, paymentData },
      null,
      true,
      false,
      false,
      false
    );
  }
);

export const deleteTuitionPayment = createAsyncThunk(
  "tuition/deleteTuitionPayment",
  async (id, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.deleteTuitionPaymentAPI,
      id,
      null,
      true,
      false,
      false,
      false
    );
  }
);

// Thống kê doanh thu học phí
export const fetchTuitionStatistics = createAsyncThunk(
  "tuition/fetchTuitionStatistics",
  async (params, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getTuitionStatisticsAPI,
      params,
      null,
      true,
      false,
      false,
      false
    );
  }
);

// Lấy thống kê tổng quan về các khoản học phí của một học sinh (admin view)
export const fetchUserTuitionSummaryAdmin = createAsyncThunk(
  "tuition/fetchUserTuitionSummaryAdmin",
  async (userId, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getUserTuitionSummaryAdminAPI,
      userId,
      null,
      true,
      false
    );
  }
);

// Lấy thống kê tổng quan về các khoản học phí của học sinh hiện tại
export const fetchUserTuitionSummary = createAsyncThunk(
  "tuition/fetchUserTuitionSummary",
  async (_, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getUserTuitionSummaryAPI,
      null,
      null,
      false,
      false
    );
  }
);

export const checkTuitionPaymentNotPaid = createAsyncThunk(
  "tuition/checkTuitionPaymentNotPaid",
  async (_, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.checkTuitionPaymentNotPaidApi,
      null,
      null,
      false,
      false,
      false,
      false
    );
  }
);



// Tuition Slice
const tuitionSlice = createSlice({
  name: "tuition",
  initialState: {
    tuitionPayments: [],
    tuitionPayment: null,
    tuitionStatistics: null,
    userTuitionSummary: null,
    filterMonth: "",
    filterIsPaid: "",
    filterOverdue: "",
    filterClass: "",
    filterClassId: "",
    filterGraduationYear: "",
    loading: false,
    error: null,
    pagination: { ...initialPaginationState },
    ...initialFilterState,

    isFirstTimeCheckTuition: true,
    tuitionPaymentNotPaid: [],
    showUnpaidModal: false,

  },
  reducers: {
    clearTuitionPayment: (state) => {
      state.tuitionPayment = null;
    },
    clearTuitionStatistics: (state) => {
      state.tuitionStatistics = null;
    },
    clearUserTuitionSummary: (state) => {
      state.userTuitionSummary = null;
    },
    setFilterMonthSlice: (state, action) => {
      state.filterMonth = action.payload;
    },
    setFilterIsPaidSlice: (state, action) => {
      state.filterIsPaid = action.payload;
    },
    setFilterOverdueSlice: (state, action) => {
      state.filterOverdue = action.payload;
    },
    setFilterClassSlice: (state, action) => {
      state.filterClass = action.payload;
    },
    setFilterClassIdSlice: (state, action) => {
      state.filterClassId = action.payload;
    },
    setFilterGraduationYear: (state, action) => {
      state.filterGraduationYear = action.payload;
    },
    setShowUnpaidModal: (state, action) => {
      state.showUnpaidModal = action.payload;
    },
    ...paginationReducers,
    ...filterReducers,
  },
  extraReducers: (builder) => {
    builder
      // Tuition Payment reducers
      .addCase(fetchTuitionPayments.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTuitionPayments.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayments = action.payload?.data || [];
        state.pagination = action.payload?.pagination || { ...initialPaginationState };
      })
      .addCase(fetchTuitionPayments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchUserTuitionPaymentsAdmin.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUserTuitionPaymentsAdmin.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayments = action.payload?.data || [];
        state.pagination = action.payload?.pagination || { ...initialPaginationState };
      })
      .addCase(fetchUserTuitionPaymentsAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchUserTuitionPayments.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUserTuitionPayments.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayments = action.payload?.data || [];
        state.pagination = action.payload?.pagination || { ...initialPaginationState };
      })
      .addCase(fetchUserTuitionPayments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchTuitionPaymentByIdAdmin.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTuitionPaymentByIdAdmin.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayment = action.payload?.data || null;
      })
      .addCase(fetchTuitionPaymentByIdAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchTuitionPaymentByIdUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTuitionPaymentByIdUser.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayment = action.payload?.data || null;
      })
      .addCase(fetchTuitionPaymentByIdUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(deleteTuitionPayment.fulfilled, (state, action) => {
        // console.log("deleteTuitionPayment", action.payload.data);
        state.tuitionPayments = state.tuitionPayments.filter(
          (payment) => payment.id != action.payload.data
        );
      })
      // Tuition Statistics reducers
      .addCase(fetchTuitionStatistics.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTuitionStatistics.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionStatistics = action.payload?.data || null;
      })
      .addCase(fetchTuitionStatistics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      // User Tuition Summary reducers (admin view)
      .addCase(fetchUserTuitionSummaryAdmin.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUserTuitionSummaryAdmin.fulfilled, (state, action) => {
        state.loading = false;
        state.userTuitionSummary = action.payload?.data || null;
      })
      .addCase(fetchUserTuitionSummaryAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      // User Tuition Summary reducers (user view)
      .addCase(fetchUserTuitionSummary.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUserTuitionSummary.fulfilled, (state, action) => {
        state.loading = false;
        state.userTuitionSummary = action.payload?.data || null;
      })
      .addCase(fetchUserTuitionSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(updateTuitionPayment.fulfilled, (state, action) => {
        const updatedPayment = action.payload.data;
        state.tuitionPayments = state.tuitionPayments.map((payment) =>
          payment.id === updatedPayment.id
            ? { ...payment, ...updatedPayment }
            : payment
        );
      })
      .addCase(checkTuitionPaymentNotPaid.fulfilled, (state, action) => {
        state.tuitionPaymentNotPaid = action.payload?.data || [];
        state.isFirstTimeCheckTuition = false;
        state.showUnpaidModal = state.tuitionPaymentNotPaid.length > 0;
      })
      .addCase(checkTuitionPaymentNotPaid.rejected, (state, action) => {
        state.error = action.error.message;
        state.isFirstTimeCheckTuition = false;
      });
  },
});

export const {
  clearTuitionPayment,
  clearTuitionStatistics,
  clearUserTuitionSummary,
  setCurrentPage,
  setLimit,
  setSortOrder,
  setLoading,
  setSearch,
  setFilterMonthSlice,
  setFilterIsPaidSlice,
  setFilterOverdueSlice,
  setFilterClassSlice,
  setFilterClassIdSlice,
  setShowUnpaidModal,
  setFilterGraduationYear,
} = tuitionSlice.actions;
export default tuitionSlice.reducer;
