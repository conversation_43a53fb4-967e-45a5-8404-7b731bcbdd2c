{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\ExamManagement.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect } from \"react\";\nimport AdminLayout from \"../../../layouts/AdminLayout\";\nimport FunctionBarAdmin from \"../../../components/bar/FunctionBarAdmin\";\nimport ExamTable from \"../../../components/table/ExamTable\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setIsAddView, setIsFilterView } from \"../../../features/filter/filterSlice\";\nimport AddExamModal from \"../../../components/modal/AddExamModal\";\nimport AdminModal from \"../../../components/modal/AdminModal\";\nimport { fetchExams } from \"../../../features/exam/examSlice\";\nimport { setCurrentPage, setLimit, setSearch } from \"src/features/exam/examSlice\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExamManagement = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    isAddView,\n    isFilterVIew\n  } = useSelector(state => state.filter);\n  const {\n    exams,\n    pagination,\n    search\n  } = useSelector(state => state.exams);\n  const navigate = useNavigate();\n  const {\n    page: currentPage,\n    pageSize: limit,\n    sortOrder\n  } = pagination;\n  useEffect(() => {\n    dispatch(fetchExams({\n      search,\n      currentPage,\n      limit,\n      sortOrder\n    }));\n  }, [dispatch, search, currentPage, limit, sortOrder]);\n  const handleAddExam = () => {\n    navigate(\"/admin/exam-management/add\");\n  };\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(AdminModal, {\n      isOpen: isAddView,\n      headerText: 'Tạo câu hỏi mới',\n      onClose: () => dispatch(setIsAddView(false)),\n      children: /*#__PURE__*/_jsxDEV(AddExamModal, {\n        onClose: () => dispatch(setIsAddView(false)),\n        fetchExams: fetchExams\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9\",\n      children: \"Danh s\\xE1ch \\u0111\\u1EC1 thi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(FunctionBarAdmin, {\n      currentPage: pagination.page,\n      totalItems: pagination.total,\n      totalPages: pagination.totalPages,\n      limit: pagination.pageSize,\n      setLimit: newLimit => dispatch(setLimit(newLimit)),\n      setCurrentPage: newPage => dispatch(setCurrentPage(newPage)),\n      setSearch: value => dispatch(setSearch(value)),\n      handleAddExam: handleAddExam\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ExamTable, {\n      exams: exams,\n      fetchExams: fetchExams\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n};\n_s(ExamManagement, \"asYCN/piell4n2O4tEyrNbGQgVs=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useNavigate];\n});\n_c = ExamManagement;\nexport default ExamManagement;\nvar _c;\n$RefreshReg$(_c, \"ExamManagement\");", "map": {"version": 3, "names": ["useEffect", "AdminLayout", "FunctionBarAdmin", "ExamTable", "useSelector", "useDispatch", "setIsAddView", "setIsFilterView", "AddExamModal", "AdminModal", "fetchExams", "setCurrentPage", "setLimit", "setSearch", "useNavigate", "jsxDEV", "_jsxDEV", "ExamManagement", "_s", "dispatch", "isAddView", "isFilterVIew", "state", "filter", "exams", "pagination", "search", "navigate", "page", "currentPage", "pageSize", "limit", "sortOrder", "handleAddExam", "children", "isOpen", "headerText", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "totalItems", "total", "totalPages", "newLimit", "newPage", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/ExamManagement.jsx"], "sourcesContent": ["import { useEffect } from \"react\";\r\nimport AdminLayout from \"../../../layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"../../../components/bar/FunctionBarAdmin\";\r\nimport ExamTable from \"../../../components/table/ExamTable\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setIsAddView, setIsFilterView } from \"../../../features/filter/filterSlice\";\r\nimport AddExamModal from \"../../../components/modal/AddExamModal\";\r\nimport AdminModal from \"../../../components/modal/AdminModal\";\r\nimport { fetchExams } from \"../../../features/exam/examSlice\";\r\nimport { setCurrentPage, setLimit, setSearch } from \"src/features/exam/examSlice\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nconst ExamManagement = () => {\r\n    const dispatch = useDispatch();\r\n    const { isAddView, isFilterVIew } = useSelector(state => state.filter);\r\n    const { exams, pagination, search } = useSelector(state => state.exams);\r\n    const navigate = useNavigate();\r\n    const { page: currentPage, pageSize: limit, sortOrder } = pagination;\r\n\r\n    useEffect(() => {\r\n        dispatch(fetchExams({ search, currentPage, limit, sortOrder }));\r\n    }, [dispatch, search, currentPage, limit, sortOrder]);\r\n\r\n    const handleAddExam = () => {\r\n        navigate(`/admin/exam-management/add`);\r\n    }\r\n\r\n    return (\r\n        <AdminLayout>\r\n            <AdminModal isOpen={isAddView} headerText={'Tạo câu hỏi mới'} onClose={() => dispatch(setIsAddView(false))} >\r\n                <AddExamModal onClose={() => dispatch(setIsAddView(false))} fetchExams={fetchExams} />\r\n            </AdminModal>\r\n            <div className=\"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9\">\r\n                Danh sách đề thi\r\n            </div>\r\n            <FunctionBarAdmin\r\n                currentPage={pagination.page}\r\n                totalItems={pagination.total}\r\n                totalPages={pagination.totalPages}\r\n                limit={pagination.pageSize}\r\n                setLimit={(newLimit) => dispatch(setLimit(newLimit))}\r\n                setCurrentPage={(newPage) => dispatch(setCurrentPage(newPage))}\r\n                setSearch={(value) => dispatch(setSearch(value))}\r\n                handleAddExam={handleAddExam}\r\n            />\r\n            <ExamTable exams={exams} fetchExams={fetchExams} />\r\n        </AdminLayout >\r\n    );\r\n}\r\n\r\nexport default ExamManagement;"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,YAAY,EAAEC,eAAe,QAAQ,sCAAsC;AACpF,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,cAAc,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,6BAA6B;AACjF,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe,SAAS;IAAEC;EAAa,CAAC,GAAGjB,WAAW,CAACkB,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EACtE,MAAM;IAAEC,KAAK;IAAEC,UAAU;IAAEC;EAAO,CAAC,GAAGtB,WAAW,CAACkB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAAC;EACvE,MAAMG,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,IAAI,EAAEC,WAAW;IAAEC,QAAQ,EAAEC,KAAK;IAAEC;EAAU,CAAC,GAAGP,UAAU;EAEpEzB,SAAS,CAAC,MAAM;IACZmB,QAAQ,CAACT,UAAU,CAAC;MAAEgB,MAAM;MAAEG,WAAW;MAAEE,KAAK;MAAEC;IAAU,CAAC,CAAC,CAAC;EACnE,CAAC,EAAE,CAACb,QAAQ,EAAEO,MAAM,EAAEG,WAAW,EAAEE,KAAK,EAAEC,SAAS,CAAC,CAAC;EAErD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxBN,QAAQ,6BAA6B,CAAC;EAC1C,CAAC;EAED,oBACIX,OAAA,CAACf,WAAW;IAAAiC,QAAA,gBACRlB,OAAA,CAACP,UAAU;MAAC0B,MAAM,EAAEf,SAAU;MAACgB,UAAU,EAAE,iBAAkB;MAACC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAACb,YAAY,CAAC,KAAK,CAAC,CAAE;MAAA4B,QAAA,eACvGlB,OAAA,CAACR,YAAY;QAAC6B,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAACb,YAAY,CAAC,KAAK,CAAC,CAAE;QAACI,UAAU,EAAEA;MAAW;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9E,CAAC,eACbzB,OAAA;MAAK0B,SAAS,EAAC,+DAA+D;MAAAR,QAAA,EAAC;IAE/E;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNzB,OAAA,CAACd,gBAAgB;MACb2B,WAAW,EAAEJ,UAAU,CAACG,IAAK;MAC7Be,UAAU,EAAElB,UAAU,CAACmB,KAAM;MAC7BC,UAAU,EAAEpB,UAAU,CAACoB,UAAW;MAClCd,KAAK,EAAEN,UAAU,CAACK,QAAS;MAC3BlB,QAAQ,EAAGkC,QAAQ,IAAK3B,QAAQ,CAACP,QAAQ,CAACkC,QAAQ,CAAC,CAAE;MACrDnC,cAAc,EAAGoC,OAAO,IAAK5B,QAAQ,CAACR,cAAc,CAACoC,OAAO,CAAC,CAAE;MAC/DlC,SAAS,EAAGmC,KAAK,IAAK7B,QAAQ,CAACN,SAAS,CAACmC,KAAK,CAAC,CAAE;MACjDf,aAAa,EAAEA;IAAc;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eACFzB,OAAA,CAACb,SAAS;MAACqB,KAAK,EAAEA,KAAM;MAACd,UAAU,EAAEA;IAAW;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzC,CAAC;AAEvB,CAAC;AAAAvB,EAAA,CApCKD,cAAc;EAAA,QACCZ,WAAW,EACQD,WAAW,EACTA,WAAW,EAChCU,WAAW;AAAA;AAAAmC,EAAA,GAJ1BhC,cAAc;AAsCpB,eAAeA,cAAc;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}