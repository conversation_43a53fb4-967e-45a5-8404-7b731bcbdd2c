import { sendNotificationToUser, sendNotificationToUsers } from '../socket/handlers/notificationHandler.js';

/**
 * Send a notification to a specific user
 * @param {Object} io - Socket.IO server instance
 * @param {number} userId - User ID
 * @param {string} title - Notification title
 * @param {string} content - Notification content
 * @param {string} type - Notification type (SYSTEM, CLASS, EXAM, LESSON, etc.)
 * @param {number} relatedId - ID of related object (optional)
 * @param {string} relatedType - Type of related object (optional)
 * @param {string} actionUrl - URL to redirect to when clicked (optional)
 * @returns {Promise<Object>} Created notification
 */
export const sendUserNotification = async (io, userId, title, content, type = 'SYSTEM', relatedId = null, relatedType = null, actionUrl = null) => {
  try {
    const notification = {
      title,
      content,
      type,
      relatedId,
      relatedType,
      actionUrl
    };
    
    return await sendNotificationToUser(io, userId, notification);
  } catch (error) {
    console.error('Error sending user notification:', error);
    throw error;
  }
};

/**
 * Send a notification to multiple users
 * @param {Object} io - Socket.IO server instance
 * @param {Array<number>} userIds - Array of user IDs
 * @param {string} title - Notification title
 * @param {string} content - Notification content
 * @param {string} type - Notification type (SYSTEM, CLASS, EXAM, LESSON, etc.)
 * @param {number} relatedId - ID of related object (optional)
 * @param {string} relatedType - Type of related object (optional)
 * @param {string} actionUrl - URL to redirect to when clicked (optional)
 * @returns {Promise<Array<Object>>} Created notifications
 */
export const sendMultiUserNotification = async (io, userIds, title, content, type = 'SYSTEM', relatedId = null, relatedType = null, actionUrl = null) => {
  try {
    const notification = {
      title,
      content,
      type,
      relatedId,
      relatedType,
      actionUrl
    };
    
    return await sendNotificationToUsers(io, userIds, notification);
  } catch (error) {
    console.error('Error sending multi-user notification:', error);
    throw error;
  }
};

/**
 * Send a notification to all users in a class
 * @param {Object} io - Socket.IO server instance
 * @param {number} classId - Class ID
 * @param {string} title - Notification title
 * @param {string} content - Notification content
 * @param {string} type - Notification type (default: CLASS)
 * @param {string} actionUrl - URL to redirect to when clicked (optional)
 * @returns {Promise<Array<Object>>} Created notifications
 */
export const sendClassNotification = async (io, classId, title, content, type = 'CLASS', actionUrl = null) => {
  try {
    const db = (await import('../models/index.js')).default;
    
    // Get all students in the class
    const classStatuses = await db.StudentClassStatus.findAll({
      where: { 
        classId,
        status: 'JS' // Only send to joined students
      },
      attributes: ['studentId']
    });
    
    const userIds = classStatuses.map(status => status.studentId);
    
    if (userIds.length === 0) {
      console.log(`No students found in class ${classId}`);
      return [];
    }
    
    return await sendMultiUserNotification(
      io,
      userIds,
      title,
      content,
      type,
      classId,
      'CLASS',
      actionUrl
    );
  } catch (error) {
    console.error('Error sending class notification:', error);
    throw error;
  }
};

/**
 * Send a notification to all users taking an exam
 * @param {Object} io - Socket.IO server instance
 * @param {number} examId - Exam ID
 * @param {string} title - Notification title
 * @param {string} content - Notification content
 * @param {string} actionUrl - URL to redirect to when clicked (optional)
 * @returns {Promise<Array<Object>>} Created notifications
 */
export const sendExamNotification = async (io, examId, title, content, actionUrl = null) => {
  try {
    const db = (await import('../models/index.js')).default;
    
    // Get all students with active attempts for this exam
    const attempts = await db.StudentExamAttempt.findAll({
      where: { 
        examId,
        endTime: null // Only active attempts
      },
      attributes: ['studentId'],
      group: ['studentId']
    });
    
    const userIds = attempts.map(attempt => attempt.studentId);
    
    if (userIds.length === 0) {
      console.log(`No active students found for exam ${examId}`);
      return [];
    }
    
    return await sendMultiUserNotification(
      io,
      userIds,
      title,
      content,
      'EXAM',
      examId,
      'EXAM',
      actionUrl
    );
  } catch (error) {
    console.error('Error sending exam notification:', error);
    throw error;
  }
};
