import { BrowserRouter, Routes, Route } from "react-router-dom";
import LoginPage from "./pages/LoginPage";
import QuestionManagement from "./pages/admin/question/questionManagement";
import ClassManagement from "./pages/admin/class/ClassManagement";
import ExamManagement from "./pages/admin/exam/ExamManagement";
import StudentManagement from "./pages/admin/user/StudentManagement";
import ProtectedRoute from "./components/ProtectedRoute";
import NotificationDisplay from "./components/error/NotificationDisplay"; // Đảm bảo file này export SuccessDisplay
import MaintenanceWrapper from "./components/MaintenanceWrapper";
import MaintenanceCleaner from "./components/MaintenanceCleaner";
import { setupKatexWarningFilter } from "./utils/setupKatexWarningFilter";


import QuestionDetailAdmin from "./pages/admin/question/QuestionDetailAdmin";
import ExamDetailAdmin from "./pages/admin/exam/ExamDetailAdmin";
import QuestionOfExamAdmin from "./pages/admin/exam/QuestionOfExamAdmin";
import CodeManagement from "./pages/admin/CodeManagement";
import PreviewExamAdmin from "./pages/admin/exam/PreviewExamAdmin";
import StudentDetailAdmin from "./pages/admin/user/StudentDetailAdmin";
import ClassDetailAdmin from "./pages/admin/class/ClassDetailAdmin";
import Home from "./pages/user/home/<USER>"
import PracticePage from "./pages/user/practice/PracticePage";
import ExamDetailPage from "./pages/user/practice/ExamDetail";
import DoExamPage from "./pages/user/practice/DoExamPage";
import PreviewExamPage from "./pages/user/practice/PreviewExam";
import RankingPage from "./pages/user/practice/RankingPage";
import HistoryDoExamPage from "./pages/user/practice/HistoryDoExamPage";
import ScorePage from "./pages/user/practice/ScorePage";
import ClassUserPage from "./pages/user/class/ClassUserPage";
import ClassDetailPage from "./pages/user/class/ClassDetailPage";
import LearningPage from "./pages/user/class/LearningPage";
import ClassUserManagement from "./pages/admin/class/ClassUserManagement";
import LessonManagement from "./pages/admin/class/LessonManagement";
import UserClassManagement from "./pages/admin/user/UserClassManagement";
import TrackingPage from "./pages/admin/exam/TrackingExamAdmin";
import ArticlePostPage from "./pages/admin/ArticlePostPage";
import ArticleManagement from "./pages/admin/ArticleManagement";
import ArticlePage from "./pages/user/article/ArticlePage";
import ArticleListPage from "./pages/user/article/ArticleListPage";
import HomePageManagement from "./pages/admin/HomePageManagement";
import AchievementManagement from "./pages/admin/achievement/AchievementManagement";
import OverViewPage from "./pages/user/home/<USER>";
import SpinnerDemo from "./components/loading/SpinnerDemo";
import QuestionReportManagement from "./pages/admin/QuestionReportManagement";
import NotificationsPage from "./pages/user/notifications/NotificationsPage";
import TuitionPaymentList from "./pages/admin/tuition/TuitionPaymentList";
import UserTuitionPayments from "./pages/user/tuition/UserTuitionPayments";
import UserTuitionPaymentDetail from "./pages/user/tuition/UserTuitionPaymentDetail";
import AttendancePage from "./pages/admin/class/AttendancePage";
import UserAttendancePage from "./pages/user/attendance/UserAttendancePage";
import AdminUserSearchPage from "./pages/admin/attendance/AdminUserSearchPage";
import AdminMobileAttendancePage from "./pages/admin/attendance/AdminMobileAttendancePage";
import AllAchievementsPage from "./pages/user/achievements/AllAchievementsPage";
import AllFeaturesPage from "./pages/user/features/AllFeaturesPage";
import AllSchedulePage from "./pages/user/schedule/AllSchedulePage";
import ScrollToTop from "./components/ScrollToTop";
import FullSchedulePage from "./pages/user/schedule/FullSchedulePage";
import UserType from "src/constants/UserType"
import AdminDashboard from "./pages/admin/AdminDashboard";
import AiExamManagement from "./pages/admin/exam/AIExamManagement";
import AIExamDetailAdmin from "./pages/admin/exam/AiExamDetailAdmin";
import StudentTuitionAdmin from "./pages/admin/user/StudentTuitionAdmin";
import StudentAttendanceAdmin from "./pages/admin/user/StudentAttendanceAdmin";
import StudentHistoryAdmin from "./pages/admin/user/StudentHistoryAdmin";
// Setup global KaTeX warning filter
setupKatexWarningFilter();
// import TestPage from "./pages/TestPage";

function App() {
    return (
        <BrowserRouter>
            <MaintenanceWrapper>
                <ScrollToTop />
                {/* Auto-clear maintenance mode if FORCE_DISABLE is true */}
                <MaintenanceCleaner />

                {/* Hiển thị lỗi toàn cục */}
                <NotificationDisplay />

                <Routes>
                    {/* Trang công khai */}
                    <Route path="/" element={<Home />} />
                    <Route path="/achievements" element={<AllAchievementsPage />} />
                    <Route path="/features" element={<AllFeaturesPage />} />
                    <Route path="/schedule" element={<AllSchedulePage />} />
                    <Route path="/login" element={<LoginPage />} />

                    {/* <Route path="/admin/test" element={<TestPage />} /> */}

                    {/* Trang cần đăng nhập */}
                    <Route element={<ProtectedRoute />}>

                        <Route path="/practice" element={<PracticePage />} />
                        <Route path="/practice/exam/:examId" element={<ExamDetailPage />} />
                        <Route path="/practice/exam/:examId/do" element={<DoExamPage />} />
                        <Route path="/practice/exam/:examId/preview" element={<PreviewExamPage />} />
                        <Route path="/practice/exam/:examId/ranking" element={<RankingPage />} />
                        <Route path="/practice/exam/:examId/history" element={<HistoryDoExamPage />} />
                        <Route path="/practice/exam/attempt/:attemptId/score" element={<ScorePage />} />
                        <Route path="/class" element={<ClassUserPage />} />
                        <Route path="/class/:classCode" element={<ClassDetailPage />} />
                        <Route path="/class/:classCode/learning" element={<LearningPage />} />
                        <Route path="/articles" element={<ArticleListPage />} />
                        <Route path="/articles/:id" element={<ArticlePage />} />
                        <Route path="/overview" element={<OverViewPage />} />
                        <Route path="/notifications" element={<NotificationsPage />} />
                        <Route path="/tuition-payments" element={<UserTuitionPayments />} />
                        <Route path="/tuition-payment/:id" element={<UserTuitionPaymentDetail />} />
                        <Route path="/attendance" element={<UserAttendancePage />} />
                        <Route path="/overview/schedule" element={<FullSchedulePage />} />
                    </Route>

                    {/* Trang Admin chỉ dành cho người có quyền */}
                    <Route element={<ProtectedRoute allowedRoles={[
                        UserType.ADMIN,
                        UserType.ASSISTANT,
                        UserType.TEACHER,
                        UserType.MARKETING,
                        UserType.CLASSMANAGEMENT,
                        UserType.HUMANRESOURCEMANAGEMENT
                    ]} />}>
                        <Route path="/admin" element={<AdminDashboard />} />

                        <Route path="/admin/class-management" element={<ClassManagement />} />
                        <Route path="/admin/class-management/:classId" element={<ClassDetailAdmin />} />
                        <Route path="/admin/class-management/:classId/users" element={<ClassUserManagement />} />
                        <Route path="/admin/class-management/:classId/lessons" element={<LessonManagement />} />
                        <Route path="/admin/class-management/:classId/attendance" element={<AttendancePage />} />

                        {/* Chỉ dành cho Admin */}
                        <Route path="/admin/student-management" element={<StudentManagement />} />
                        <Route path="/admin/student-management/:userId" element={<StudentDetailAdmin />} />
                        <Route path="/admin/student-management/:userId/classes" element={<UserClassManagement />} />
                        <Route path="/admin/student-management/:userId/history" element={<StudentHistoryAdmin />} />
                        <Route path="/admin/student-management/:userId/attendance" element={<StudentAttendanceAdmin />} />
                        {/* Attendance Management Routes */}
                        {/* <Route path="/admin/attendance" element={<AdminUserSearchPage />} /> */}
                        <Route path="/admin/attendance/user/:userId" element={<AdminMobileAttendancePage />} />
                    </Route>

                    <Route element={<ProtectedRoute allowedRoles={[
                        UserType.ADMIN,
                        UserType.TEACHER,
                        UserType.CLASSMANAGEMENT,
                    ]} />}>
                        <Route path="/admin/question-management" element={<QuestionManagement />} />
                        <Route path="/admin/question-management/:questionId" element={<QuestionDetailAdmin />} />

                        <Route path="/admin/question-report-management" element={<QuestionReportManagement />} />
                        <Route path="/admin/AI/exam-management" element={<AiExamManagement />} />
                        <Route path="/admin/AI/exam-management/:examId" element={<AIExamDetailAdmin />} />


                        <Route path="/admin/exam-management" element={<ExamManagement />} />
                        <Route path="/admin/exam-management/:examId" element={<ExamDetailAdmin />} />
                        <Route path="/admin/exam-management/:examId/questions" element={<QuestionOfExamAdmin />} />
                        <Route path="/admin/exam-management/:examId/preview" element={<PreviewExamAdmin />} />
                        <Route path="/admin/exam-management/:examId/tracking" element={<TrackingPage />} />
                    </Route>

                    <Route element={<ProtectedRoute allowedRoles={[
                        UserType.ADMIN,
                        UserType.HUMANRESOURCEMANAGEMENT
                    ]} />}>
                        <Route path="/admin/tuition-payment" element={<TuitionPaymentList />} />
                    </Route>
                    <Route element={<ProtectedRoute allowedRoles={[
                        UserType.ADMIN,
                        UserType.MARKETING
                    ]} />}>
                        <Route path="/admin/homepage-management" element={<HomePageManagement />} />
                        <Route path="/admin/achievement-management" element={<AchievementManagement />} />
                        <Route path="/admin/student-management/:userId/tuition" element={<StudentTuitionAdmin />} />

                    </Route>


                    <Route element={<ProtectedRoute allowedRoles={["AD"]} />}>
                        <Route path="/admin/code-management" element={<CodeManagement />} />
                        <Route path="/admin/article-management" element={<ArticleManagement />} />
                        <Route path="/admin/article-post" element={<ArticlePostPage />} />
                        <Route path="/admin/article-management/edit/:id" element={<ArticlePostPage />} />
                        <Route path="/admin/spinner-demo" element={<SpinnerDemo />} />
                    </Route>

                </Routes>
            </MaintenanceWrapper>
        </BrowserRouter>
    );
}

export default App;
