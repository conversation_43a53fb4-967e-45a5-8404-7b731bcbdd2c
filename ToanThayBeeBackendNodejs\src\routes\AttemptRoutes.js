import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as AttemptController from '../controllers/AttemptController.js'

const router = express.Router()

// Route lấy tất cả các bài làm của học sinh
router.get('/v1/user/attempt',
    requireRoles(Roles.JustStudent),
    asyncHandler(AttemptController.getAttemptsByUser)
)

router.get('/v1/admin/attempt/:userId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttemptController.getAttemptsByUserId)
)

// Route lấy tất cả các bài làm của học sinh theo id của đề thi
router.get('/v1/user/attempt/exam/:examId/history',
    requireRoles(Roles.JustStudent),
    asyncHandler(AttemptController.getAttemptByStudentId)
)

// Route lấy tất cả các bài làm của học sinh theo id của đề thi
router.get('/v1/admin/attempt/exam/:examId',
    requireRoles(Roles.AllExceptMarketingStudent),
    asyncHandler(AttemptController.getAttemptsForAdminByExamId)
)

router.get('/v1/user/attempt/exam/:examId',
    requireRoles(Roles.JustStudent),
    asyncHandler(AttemptController.getAttemptByExamId)
)

export default router