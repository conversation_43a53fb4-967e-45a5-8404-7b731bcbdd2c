import FullScreen from "../button/ScreenButton";
const HeaderDoExamPage = ({ nameExam, isDarkMode }) => {
    return (
        <div className="sticky w-full top-0 z-20 bg-sky-800 shadow-md p-4">
            <div className="flex justify-between items-center">
                <div className="text-white text-xl">{nameExam}</div>

                <FullScreen isDarkMode={isDarkMode}/>
            </div>
        </div>
    );
};

export default HeaderDoExamPage;
