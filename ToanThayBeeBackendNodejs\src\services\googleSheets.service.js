import { google } from 'googleapis';
import db from '../models/index.js';
import dotenv from 'dotenv';
dotenv.config();

// Cấu hình Google Sheets API
const SCOPES = ['https://www.googleapis.com/auth/spreadsheets'];



// Hàm để lấy Google Sheets client
const getGoogleSheetsClient = () => {
    try {
        // Cách 1: Sử dụng environment variables
        if (process.env.GOOGLE_PRIVATE_KEY) {
            // Kiểm tra các biến môi trường cần thiết
            const requiredEnvVars = [
                'GOOGLE_TYPE',
                'GOOGLE_PROJECT_ID',
                'GOOGLE_PRIVATE_KEY_ID',
                'GOOGLE_PRIVATE_KEY',
                'GOOGLE_CLIENT_EMAIL',
                'GOOGLE_CLIENT_ID'
            ];

            for (const envVar of requiredEnvVars) {
                if (!process.env[envVar]) {
                    throw new Error(`Missing required environment variable: ${envVar}`);
                }
            }

            // Xử lý private key đúng cách
            let privateKey = process.env.GOOGLE_PRIVATE_KEY;

            // Debug log để kiểm tra private key format
            console.log('Private key length:', privateKey?.length);
            console.log('Private key starts with:', privateKey?.substring(0, 50));

            // Nếu private key được wrap trong quotes, loại bỏ chúng
            if (privateKey.startsWith('"') && privateKey.endsWith('"')) {
                privateKey = privateKey.slice(1, -1);
            }

            // Thay thế \\n bằng \n thực sự
            privateKey = privateKey.replace(/\\n/g, '\n');

            // Kiểm tra format của private key
            if (!privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
                throw new Error('Private key format không đúng. Phải bắt đầu với -----BEGIN PRIVATE KEY-----');
            }

            // Sử dụng service account credentials từ environment variables
            const credentials = {
                type: process.env.GOOGLE_TYPE,
                project_id: process.env.GOOGLE_PROJECT_ID,
                private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,
                private_key: privateKey,
                client_email: process.env.GOOGLE_CLIENT_EMAIL,
                client_id: process.env.GOOGLE_CLIENT_ID,
                auth_uri: process.env.GOOGLE_AUTH_URI || 'https://accounts.google.com/o/oauth2/auth',
                token_uri: process.env.GOOGLE_TOKEN_URI || 'https://oauth2.googleapis.com/token',
                auth_provider_x509_cert_url: process.env.GOOGLE_AUTH_PROVIDER_X509_CERT_URL || 'https://www.googleapis.com/oauth2/v1/certs',
                client_x509_cert_url: process.env.GOOGLE_CLIENT_X509_CERT_URL
            };

            console.log('Initializing Google Sheets with service account:', credentials.client_email);

            const auth = new google.auth.GoogleAuth({
                credentials,
                scopes: SCOPES,
            });

            return google.sheets({ version: 'v4', auth });
        }

        // Cách 2: Fallback - sử dụng file credentials.json nếu có
        const auth = new google.auth.GoogleAuth({
            keyFile: './credentials.json', // Tạo file này nếu cần
            scopes: SCOPES,
        });

        return google.sheets({ version: 'v4', auth });

    } catch (error) {
        console.error('Lỗi khi khởi tạo Google Sheets client:', error);
        throw new Error(`Không thể kết nối với Google Sheets API: ${error.message}`);
    }
};

// Hàm để lấy spreadsheet ID từ URL
const getSpreadsheetIdFromUrl = (url) => {
    const match = url.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    return match ? match[1] : null;
};

export const readSheetAndCheckUpdateTuition = async (sheetUrl, month) => {
    const spreadsheetId = getSpreadsheetIdFromUrl(sheetUrl);
    if (!spreadsheetId) throw new Error('URL Google Sheets không hợp lệ');

    // Kiểm tra định dạng tháng
    if (!/^\d{4}-\d{2}$/.test(month)) {
        throw new Error('Tháng phải có định dạng YYYY-MM, ví dụ: 2023-06');
    }

    const sheets = getGoogleSheetsClient();

    // Lấy thông tin sheet đầu tiên
    const spreadsheetInfo = await sheets.spreadsheets.get({ spreadsheetId });
    const firstSheet = spreadsheetInfo.data.sheets[0];
    const sheetName = firstSheet.properties.title;

    const res = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range: `${sheetName}!A1:Z1000` // tuỳ vào dữ liệu
    });

    const rows = res.data.values;
    if (!rows || rows.length === 0) throw new Error('Sheet không có dữ liệu');

    const headers = rows[0];
    const rawMonth = String(Number(month.split('-')[1])); // từ "06" hoặc "6" → "6"
    const targetMonthCol = `ĐÃ ĐÓNG T${rawMonth}`;

    // Xác định vị trí các cột
    const colIndexes = {
        studentPhone: headers.findIndex(h => h.trim().toUpperCase() === 'SĐT HS'),
        parentPhone: headers.findIndex(h => h.trim().toUpperCase() === 'SĐT PH'),
        fullName: headers.findIndex(h => h.trim().toUpperCase() === 'HỌ VÀ TÊN'),
        paidStatus: headers.findIndex(h => h.trim().toUpperCase() === targetMonthCol.toUpperCase())
    };

    const missingCols = Object.entries(colIndexes).filter(([_, index]) => index === -1);
    if (missingCols.length > 0) {
        throw new Error(`Không tìm thấy các cột: ${missingCols.map(([key]) => key).join(', ')}`);
    }

    const notFoundUsers = [];
    const foundUsers = [];
    const updatedTuitions = [];

    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const studentPhone = row[colIndexes.studentPhone]?.trim();
        const parentPhone = row[colIndexes.parentPhone]?.trim();
        const name = row[colIndexes.fullName]?.trim();
        const paidRaw = row[colIndexes.paidStatus]?.trim().toUpperCase();
        const paidInSheet = paidRaw === 'TRUE';

        if (!studentPhone && !parentPhone) continue;

        const user = await db.User.findOne({
            where: {
                phone: parentPhone,
                password: studentPhone,
                userType: 'HS1',
                isActive: true
            },
            attributes: ['id', 'firstName', 'lastName', 'phone', 'password', 'highSchool', 'class', 'graduationYear']
        });

        if (!user) {
            notFoundUsers.push({ row: i + 1, name, studentPhone, parentPhone });
            continue;
        }

        const tuition = await db.TuitionPayment.findOne({
            where: {
                userId: user.id,
                month
            },
            attributes: ['id', 'isPaid']
        });

        const paidInDb = tuition ? tuition.isPaid : null;

        let updatedTuition = null;

        if (tuition) {
            if (tuition.isPaid !== paidInSheet) {
                const paymentDate = paidInSheet ? new Date() : null;
                updatedTuition = await tuition.update({ isPaid: paidInSheet, paymentDate });
                console.log(`✅ Cập nhật học phí cho user ${user.lastName} + ${user.firstName} : ${paidInSheet ? 'Đã đóng' : 'Chưa đóng'}`);
                updatedTuitions.push(
                    {
                        userId: user.id,
                        name: `${user.lastName} ${user.firstName}`,
                        month,
                        paidInSheet,
                        paidInDb: paidInDb,
                        match: paidInDb === paidInSheet,
                        isUpdate: true
                    }
                );
            }
        } else {
            // Nếu không có học phí, tạo mới
            const paymentDate = paidInSheet ? new Date() : null;
            updatedTuition = await db.TuitionPayment.create({
                userId: user.id,
                month,
                isPaid: paidInSheet,
                paymentDate,
            });
            // console.log(`✅ Tạo mới học phí cho user ${user.lastName} + ${user.firstName} : ${paidInSheet ? 'Đã đóng' : 'Chưa đóng'}`);
            updatedTuitions.push(
                {
                    userId: user.id,
                    name: `${user.lastName} ${user.firstName}`,
                    month,
                    paidInSheet,
                    paidInDb: paidInDb,
                    match: paidInDb === paidInSheet,
                    isUpdate: false
                }
            );
        }

        foundUsers.push({
            row: i + 1,
            userId: user.id,
            name: `${user.lastName} ${user.firstName}`,
            highSchool: user.highSchool,
            phoneHS: user.phone,
            phonePH: user.password,
            paidInSheet,
            paidInDb: paidInDb,
            match: paidInDb === paidInSheet,
            isUpdate: updatedTuition ? true : false
        });
    }

    return {
        message: `Đã kiểm tra và cập nhật học phí cho ${foundUsers.length} user(s) trong sheet`,
        updatedTuitions,
        notFoundUsers
    };
};

// Hàm để kiểm tra và lấy thông tin sheet
const getSheetInfo = async (spreadsheetId, sheetName = 'Sheet1') => {
    try {
        const sheets = getGoogleSheetsClient();
        const response = await sheets.spreadsheets.get({
            spreadsheetId,
            includeGridData: false
        });

        const sheet = response.data.sheets.find(s => s.properties.title === sheetName);
        return sheet ? sheet.properties : null;
    } catch (error) {
        console.error('Error getting sheet info:', error);
        throw error;
    }
};

// Hàm để kiểm tra user đã tồn tại trong sheet chưa
const checkUserExistsInSheet = async (spreadsheetId, userId, sheetName = 'Trang tính1') => {
    try {
        const sheets = getGoogleSheetsClient();

        // Lấy tất cả dữ liệu trong cột A (ID column)
        const response = await sheets.spreadsheets.values.get({
            spreadsheetId,
            range: `${sheetName}!A:A`
        });

        const values = response.data.values || [];

        // Kiểm tra xem userId có tồn tại trong cột A không
        for (let i = 0; i < values.length; i++) {
            if (values[i][0] && values[i][0].toString() === userId.toString()) {
                return {
                    exists: true,
                    rowIndex: i + 1 // Google Sheets sử dụng 1-based index
                };
            }
        }

        return { exists: false, rowIndex: null };
    } catch (error) {
        console.error('Error checking user existence in sheet:', error);
        // Nếu có lỗi (ví dụ: sheet chưa có dữ liệu), coi như user chưa tồn tại
        return { exists: false, rowIndex: null };
    }
};

// Hàm để thêm user mới vào Google Sheets
export const addUserToSheet = async (userData, id, addedToClasses) => {
    try {
        // Lấy thông tin sheet từ database
        const sheetLink = await db.SheetLinks.findByPk(id);
        // console.log('Sheet ID:', id);
        // console.log('Sheet link:', sheetLink);
        if (!sheetLink || !sheetLink.isActive) {
            throw new Error('Sheet không tồn tại hoặc đã bị vô hiệu hóa');
        }

        const spreadsheetId = getSpreadsheetIdFromUrl(sheetLink.sheetUrl);
        // console.log('Spreadsheet ID:', spreadsheetId);
        if (!spreadsheetId) {
            throw new Error('URL Google Sheets không hợp lệ');
        }

        // Lấy thông tin về tất cả sheets để tìm sheet đầu tiên
        const sheets = getGoogleSheetsClient();
        const spreadsheetInfo = await sheets.spreadsheets.get({
            spreadsheetId,
            includeGridData: false
        });

        // Lấy tên sheet đầu tiên
        const firstSheet = spreadsheetInfo.data.sheets[0];
        const sheetName = firstSheet.properties.title;
        // console.log('Sheet name:', sheetName);

        // Kiểm tra user đã tồn tại trong sheet chưa
        const userExists = await checkUserExistsInSheet(spreadsheetId, userData.id, sheetName);
        // console.log('User exists check:', userExists);

        if (userExists.exists) {
            // console.log(`User ${userData.id} đã tồn tại trong sheet tại dòng ${userExists.rowIndex}`);
            return {
                message: `User ${userData.id} đã tồn tại trong sheet`,
                rowIndex: userExists.rowIndex,
                action: 'skipped'
            };
        }

        // Chuẩn bị dữ liệu để thêm vào sheet
        const userDataFullName = `${userData.lastName} ${userData.firstName}`;
        const values = [
            [
                userData.id,
                userDataFullName, // Thêm họ và tên đầy đủ của người dùng
                userData.phone,
                userData.highSchool || '',
                userData.class || '',
                userData.graduationYear || '',
                new Date().toISOString(),
                userData.isActive ? 'Hoạt động' : 'Không hoạt động',
                addedToClasses.join(', ') || '', // Thêm các lớp mà người dùng được thêm vào
                userData.userName,
                userData.password
            ]
        ];
        // console.log('Values to append:', values);

        // Thêm dữ liệu vào sheet (append vào cuối)
        const response = await sheets.spreadsheets.values.append({
            spreadsheetId,
            range: `${sheetName}!A1`, // Sử dụng tên sheet động
            valueInputOption: 'RAW',
            insertDataOption: 'INSERT_ROWS', // Thêm dòng mới
            resource: {
                values
            }
        });

        // // Cập nhật lastUpdated cho sheet link
        // await db.SheetLinks.update(
        //     { lastUpdated: new Date() },
        //     { where: { id } }
        // );

        console.log(`Đã thêm user ${userData.id} vào Google Sheets thành công`);
        return response.data;

    } catch (error) {
        console.error('Lỗi khi thêm user vào Google Sheets:', error);
        throw error;
    }
};

// Hàm để tạo header cho sheet (chỉ chạy một lần khi setup)
export const createSheetHeader = async (sheetId) => {
    try {
        const sheetLink = await db.SheetLinks.findByPk(sheetId);
        if (!sheetLink || !sheetLink.isActive) {
            throw new Error('Sheet không tồn tại hoặc đã bị vô hiệu hóa');
        }

        const spreadsheetId = getSpreadsheetIdFromUrl(sheetLink.sheetUrl);
        if (!spreadsheetId) {
            throw new Error('URL Google Sheets không hợp lệ');
        }

        const sheets = getGoogleSheetsClient();

        // Header cho bảng user
        const headerValues = [
            [
                'ID',
                'Họ và tên', // Thêm cột mới để lưu họ và tên đầy đủ của người dùng
                'Số điện thoại',
                'Trường THPT',
                'Lớp',
                'Năm tốt nghiệp',
                'Ngày tạo',
                'Trạng thái',
                'Thêm vào lớp', // Thêm cột mới để lưu các lớp mà người dùng được thêm vào
                'Tên đăng nhập',
                'Mật khẩu'
            ]
        ];

        const response = await sheets.spreadsheets.values.update({
            spreadsheetId,
            range: 'Trang tính1!A1:K1', // Sử dụng tên sheet động
            valueInputOption: 'RAW',
            resource: {
                values: headerValues
            }
        });

        console.log('Đã tạo header cho Google Sheets thành công');
        return response.data;

    } catch (error) {
        console.error('Lỗi khi tạo header cho Google Sheets:', error);
        throw error;
    }
};

// Hàm để xóa user khỏi Google Sheets
export const deleteUserFromSheet = async (userId, sheetId) => {
    try {
        // Lấy thông tin sheet từ database
        const sheetLink = await db.SheetLinks.findByPk(sheetId);
        if (!sheetLink || !sheetLink.isActive) {
            throw new Error('Sheet không tồn tại hoặc đã bị vô hiệu hóa');
        }

        const spreadsheetId = getSpreadsheetIdFromUrl(sheetLink.sheetUrl);
        if (!spreadsheetId) {
            throw new Error('URL Google Sheets không hợp lệ');
        }

        // Lấy thông tin về tất cả sheets để tìm sheet đầu tiên
        const sheets = getGoogleSheetsClient();
        const spreadsheetInfo = await sheets.spreadsheets.get({
            spreadsheetId,
            includeGridData: false
        });

        // Lấy tên sheet đầu tiên
        const firstSheet = spreadsheetInfo.data.sheets[0];
        const sheetName = firstSheet.properties.title;

        // Kiểm tra user có tồn tại trong sheet không
        const userExists = await checkUserExistsInSheet(spreadsheetId, userId, sheetName);

        if (!userExists.exists) {
            console.log(`User ${userId} không tồn tại trong sheet ${sheetId}`);
            return {
                message: `User ${userId} không tồn tại trong sheet`,
                action: 'not_found'
            };
        }

        // Xóa dòng chứa user (sử dụng batchUpdate để xóa dòng)
        const requests = [{
            deleteDimension: {
                range: {
                    sheetId: firstSheet.properties.sheetId,
                    dimension: 'ROWS',
                    startIndex: userExists.rowIndex - 1, // 0-based index
                    endIndex: userExists.rowIndex // exclusive end
                }
            }
        }];

        await sheets.spreadsheets.batchUpdate({
            spreadsheetId,
            resource: {
                requests
            }
        });

        console.log(`Đã xóa user ${userId} khỏi Google Sheets thành công tại dòng ${userExists.rowIndex}`);
        return {
            message: `User ${userId} đã được xóa khỏi sheet`,
            rowIndex: userExists.rowIndex,
            action: 'deleted'
        };

    } catch (error) {
        console.error('Lỗi khi xóa user khỏi Google Sheets:', error);
        throw error;
    }
};

// Hàm để xóa user khỏi tất cả Google Sheets có type USER_LIST
export const deleteUserFromAllSheets = async (userId) => {
    try {
        // Lấy tất cả sheets có type USER_LIST và đang active
        const userListSheets = await getUserListSheets();

        if (!userListSheets || userListSheets.length === 0) {
            console.log('Không có Google Sheets nào để xóa user');
            return {
                message: 'Không có Google Sheets nào để xóa user',
                results: []
            };
        }

        const results = [];

        // Xóa user khỏi từng sheet
        for (const sheet of userListSheets) {
            try {
                const result = await deleteUserFromSheet(userId, sheet.id);
                results.push({
                    sheetId: sheet.id,
                    sheetName: sheet.name,
                    ...result
                });
            } catch (error) {
                console.error(`Lỗi khi xóa user ${userId} khỏi sheet ${sheet.id}:`, error);
                results.push({
                    sheetId: sheet.id,
                    sheetName: sheet.name,
                    message: `Lỗi: ${error.message}`,
                    action: 'error'
                });
            }
        }

        return {
            message: `Đã xử lý xóa user ${userId} khỏi ${userListSheets.length} sheets`,
            results
        };

    } catch (error) {
        console.error('Lỗi khi xóa user khỏi tất cả Google Sheets:', error);
        throw error;
    }
};

// Hàm để cập nhật thông tin lớp học cho user trong Google Sheets
export const updateUserClassInSheet = async (userId, className, sheetId) => {
    try {
        // Lấy thông tin sheet từ database
        const sheetLink = await db.SheetLinks.findByPk(sheetId);
        if (!sheetLink || !sheetLink.isActive) {
            throw new Error('Sheet không tồn tại hoặc đã bị vô hiệu hóa');
        }

        const spreadsheetId = getSpreadsheetIdFromUrl(sheetLink.sheetUrl);
        if (!spreadsheetId) {
            throw new Error('URL Google Sheets không hợp lệ');
        }

        // Lấy thông tin về tất cả sheets để tìm sheet đầu tiên
        const sheets = getGoogleSheetsClient();
        const spreadsheetInfo = await sheets.spreadsheets.get({
            spreadsheetId,
            includeGridData: false
        });

        // Lấy tên sheet đầu tiên
        const firstSheet = spreadsheetInfo.data.sheets[0];
        const sheetName = firstSheet.properties.title;

        // Kiểm tra user có tồn tại trong sheet không
        const userExists = await checkUserExistsInSheet(spreadsheetId, userId, sheetName);

        if (!userExists.exists) {
            console.log(`User ${userId} không tồn tại trong sheet ${sheetId}`);
            return {
                message: `User ${userId} không tồn tại trong sheet`,
                action: 'not_found'
            };
        }

        // Lấy dữ liệu hiện tại của dòng user (cột I - "Thêm vào lớp")
        const currentRowRange = `${sheetName}!I${userExists.rowIndex}`;
        const currentDataResponse = await sheets.spreadsheets.values.get({
            spreadsheetId,
            range: currentRowRange
        });

        let currentClasses = '';
        if (currentDataResponse.data.values && currentDataResponse.data.values[0] && currentDataResponse.data.values[0][0]) {
            currentClasses = currentDataResponse.data.values[0][0];
        }

        // Thêm tên lớp mới vào cuối (nếu chưa có)
        let updatedClasses = currentClasses;
        if (currentClasses) {
            // Kiểm tra xem lớp đã tồn tại chưa
            const existingClasses = currentClasses.split(', ').map(c => c.trim());
            if (!existingClasses.includes(className)) {
                updatedClasses = `${currentClasses}, ${className}`;
            }
        } else {
            updatedClasses = className;
        }

        // Cập nhật dữ liệu trong sheet
        await sheets.spreadsheets.values.update({
            spreadsheetId,
            range: currentRowRange,
            valueInputOption: 'RAW',
            resource: {
                values: [[updatedClasses]]
            }
        });

        console.log(`Đã cập nhật lớp "${className}" cho user ${userId} trong Google Sheets`);
        return {
            message: `Đã cập nhật lớp "${className}" cho user ${userId}`,
            rowIndex: userExists.rowIndex,
            updatedClasses,
            action: 'updated'
        };

    } catch (error) {
        console.error('Lỗi khi cập nhật lớp học trong Google Sheets:', error);
        throw error;
    }
};

// Hàm để cập nhật thông tin lớp học cho user trong tất cả Google Sheets
export const updateUserClassInAllSheets = async (userId, className) => {
    try {
        // Lấy tất cả sheets có type USER_LIST và đang active
        const userListSheets = await getUserListSheets();

        if (!userListSheets || userListSheets.length === 0) {
            console.log('Không có Google Sheets nào để cập nhật user');
            return {
                message: 'Không có Google Sheets nào để cập nhật user',
                results: []
            };
        }

        const results = [];

        // Cập nhật user trong từng sheet
        for (const sheet of userListSheets) {
            try {
                const result = await updateUserClassInSheet(userId, className, sheet.id);
                results.push({
                    sheetId: sheet.id,
                    sheetName: sheet.name,
                    ...result
                });
            } catch (error) {
                console.error(`Lỗi khi cập nhật user ${userId} trong sheet ${sheet.id}:`, error);
                results.push({
                    sheetId: sheet.id,
                    sheetName: sheet.name,
                    message: `Lỗi: ${error.message}`,
                    action: 'error'
                });
            }
        }

        return {
            message: `Đã xử lý cập nhật user ${userId} trong ${userListSheets.length} sheets`,
            results
        };

    } catch (error) {
        console.error('Lỗi khi cập nhật user trong tất cả Google Sheets:', error);
        throw error;
    }
};

// Hàm để xóa tên lớp khỏi danh sách lớp của user trong Google Sheets
export const removeUserClassFromSheet = async (userId, className, sheetId) => {
    try {
        // Lấy thông tin sheet từ database
        const sheetLink = await db.SheetLinks.findByPk(sheetId);
        if (!sheetLink || !sheetLink.isActive) {
            throw new Error('Sheet không tồn tại hoặc đã bị vô hiệu hóa');
        }

        const spreadsheetId = getSpreadsheetIdFromUrl(sheetLink.sheetUrl);
        if (!spreadsheetId) {
            throw new Error('URL Google Sheets không hợp lệ');
        }

        // Lấy thông tin về tất cả sheets để tìm sheet đầu tiên
        const sheets = getGoogleSheetsClient();
        const spreadsheetInfo = await sheets.spreadsheets.get({
            spreadsheetId,
            includeGridData: false
        });

        // Lấy tên sheet đầu tiên
        const firstSheet = spreadsheetInfo.data.sheets[0];
        const sheetName = firstSheet.properties.title;

        // Kiểm tra user có tồn tại trong sheet không
        const userExists = await checkUserExistsInSheet(spreadsheetId, userId, sheetName);

        if (!userExists.exists) {
            console.log(`User ${userId} không tồn tại trong sheet ${sheetId}`);
            return {
                message: `User ${userId} không tồn tại trong sheet`,
                action: 'not_found'
            };
        }

        // Lấy dữ liệu hiện tại của dòng user (cột I - "Thêm vào lớp")
        const currentRowRange = `${sheetName}!I${userExists.rowIndex}`;
        const currentDataResponse = await sheets.spreadsheets.values.get({
            spreadsheetId,
            range: currentRowRange
        });

        let currentClasses = '';
        if (currentDataResponse.data.values && currentDataResponse.data.values[0] && currentDataResponse.data.values[0][0]) {
            currentClasses = currentDataResponse.data.values[0][0];
        }

        // Xóa tên lớp khỏi danh sách
        let updatedClasses = currentClasses;
        if (currentClasses) {
            const existingClasses = currentClasses.split(', ').map(c => c.trim()).filter(c => c !== '');
            const filteredClasses = existingClasses.filter(c => c !== className);
            updatedClasses = filteredClasses.join(', ');
        }

        // Cập nhật dữ liệu trong sheet
        await sheets.spreadsheets.values.update({
            spreadsheetId,
            range: currentRowRange,
            valueInputOption: 'RAW',
            resource: {
                values: [[updatedClasses]]
            }
        });

        console.log(`Đã xóa lớp "${className}" cho user ${userId} trong Google Sheets`);
        return {
            message: `Đã xóa lớp "${className}" cho user ${userId}`,
            rowIndex: userExists.rowIndex,
            updatedClasses,
            action: 'removed'
        };

    } catch (error) {
        console.error('Lỗi khi xóa lớp học trong Google Sheets:', error);
        throw error;
    }
};

// Hàm để xóa tên lớp khỏi danh sách lớp của user trong tất cả Google Sheets
export const removeUserClassFromAllSheets = async (userId, className) => {
    try {
        // Lấy tất cả sheets có type USER_LIST và đang active
        const userListSheets = await getUserListSheets();

        if (!userListSheets || userListSheets.length === 0) {
            console.log('Không có Google Sheets nào để cập nhật user');
            return {
                message: 'Không có Google Sheets nào để cập nhật user',
                results: []
            };
        }

        const results = [];

        // Xóa lớp khỏi user trong từng sheet
        for (const sheet of userListSheets) {
            try {
                const result = await removeUserClassFromSheet(userId, className, sheet.id);
                results.push({
                    sheetId: sheet.id,
                    sheetName: sheet.name,
                    ...result
                });
            } catch (error) {
                console.error(`Lỗi khi xóa lớp cho user ${userId} trong sheet ${sheet.id}:`, error);
                results.push({
                    sheetId: sheet.id,
                    sheetName: sheet.name,
                    message: `Lỗi: ${error.message}`,
                    action: 'error'
                });
            }
        }

        return {
            message: `Đã xử lý xóa lớp "${className}" cho user ${userId} trong ${userListSheets.length} sheets`,
            results
        };

    } catch (error) {
        console.error('Lỗi khi xóa lớp cho user trong tất cả Google Sheets:', error);
        throw error;
    }
};

// Hàm để lấy tất cả sheet links có type USER_LIST
export const getUserListSheets = async () => {
    try {
        const sheets = await db.SheetLinks.findAll({
            where: {
                type: 'USER_LIST',
                isActive: true
            }
        });
        return sheets;
    } catch (error) {
        console.error('Lỗi khi lấy danh sách user list sheets:', error);
        throw error;
    }
};

// Hàm duyệt qua các dòng tìm kiếm học sinh rồi thêm username/password vào cột J và K
// CHẠY MỘT LẦN RỒI XÓA ĐI
export const addUsernamePasswordToSheet = async (sheetId) => {
    try {
        console.log('🚀 Bắt đầu thêm username/password vào Google Sheets...');

        // Lấy thông tin sheet từ database
        const sheetLink = await db.SheetLinks.findByPk(sheetId);
        if (!sheetLink || !sheetLink.isActive) {
            throw new Error('Sheet không tồn tại hoặc đã bị vô hiệu hóa');
        }

        const spreadsheetId = getSpreadsheetIdFromUrl(sheetLink.sheetUrl);
        if (!spreadsheetId) {
            throw new Error('URL Google Sheets không hợp lệ');
        }

        // Lấy thông tin về sheet
        const sheets = getGoogleSheetsClient();
        const spreadsheetInfo = await sheets.spreadsheets.get({
            spreadsheetId,
            includeGridData: false
        });

        const firstSheet = spreadsheetInfo.data.sheets[0];
        const sheetName = firstSheet.properties.title;

        // Lấy tất cả dữ liệu từ sheet (từ dòng 2 để bỏ qua header)
        const response = await sheets.spreadsheets.values.get({
            spreadsheetId,
            range: `${sheetName}!A2:K` // Lấy từ cột A đến K, từ dòng 2 trở đi
        });

        const rows = response.data.values || [];
        console.log(`📊 Tìm thấy ${rows.length} dòng dữ liệu trong sheet`);

        if (rows.length === 0) {
            console.log('❌ Không có dữ liệu để xử lý');
            return {
                message: 'Không có dữ liệu để xử lý',
                processed: 0,
                updated: 0,
                errors: []
            };
        }

        const updates = [];
        const errors = [];
        let processed = 0;
        let updated = 0;

        // Duyệt qua từng dòng
        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const rowIndex = i + 2; // +2 vì bắt đầu từ dòng 2 và Google Sheets dùng 1-based index

            try {
                processed++;

                // Kiểm tra xem dòng có dữ liệu không (cột A - ID)
                if (!row[0]) {
                    console.log(`⏭️  Bỏ qua dòng ${rowIndex}: Không có ID`);
                    continue;
                }

                const userId = row[0].toString().trim();

                // Kiểm tra xem cột J và K đã có dữ liệu chưa
                const hasUsername = row[9] && row[9].toString().trim(); // Cột J (index 9)
                const hasPassword = row[10] && row[10].toString().trim(); // Cột K (index 10)

                if (hasUsername && hasPassword) {
                    console.log(`✅ Dòng ${rowIndex} (ID: ${userId}): Đã có username và password`);
                    continue;
                }

                // Tìm user trong database
                const user = await db.User.findByPk(userId, {
                    attributes: ['id', 'username', 'password', 'firstName', 'lastName']
                });

                if (!user) {
                    console.log(`❌ Dòng ${rowIndex}: Không tìm thấy user với ID ${userId} trong database`);
                    errors.push({
                        rowIndex,
                        userId,
                        error: 'User không tồn tại trong database'
                    });
                    continue;
                }

                // Chuẩn bị dữ liệu để update
                const username = user.username || '';
                const password = user.password || '';

                if (!username || !password) {
                    console.log(`⚠️  Dòng ${rowIndex} (ID: ${userId}): User thiếu username hoặc password`);
                    errors.push({
                        rowIndex,
                        userId,
                        userName: `${user.lastName} ${user.firstName}`,
                        error: 'User thiếu username hoặc password'
                    });
                    continue;
                }

                // Thêm vào danh sách update
                updates.push({
                    range: `${sheetName}!J${rowIndex}:K${rowIndex}`,
                    values: [[username, password]]
                });

                updated++;
                console.log(`✅ Chuẩn bị update dòng ${rowIndex} (ID: ${userId}): ${user.lastName} ${user.firstName}`);

            } catch (error) {
                console.error(`❌ Lỗi xử lý dòng ${rowIndex}:`, error);
                errors.push({
                    rowIndex,
                    userId: row[0] || 'Unknown',
                    error: error.message
                });
            }
        }

        // Thực hiện batch update nếu có dữ liệu để update
        if (updates.length > 0) {
            console.log(`🔄 Bắt đầu update ${updates.length} dòng...`);

            const batchUpdateRequest = {
                spreadsheetId,
                resource: {
                    valueInputOption: 'RAW',
                    data: updates
                }
            };

            await sheets.spreadsheets.values.batchUpdate(batchUpdateRequest);
            console.log(`✅ Đã update thành công ${updates.length} dòng!`);
        }

        const result = {
            message: `Hoàn thành! Đã xử lý ${processed} dòng, update thành công ${updated} dòng`,
            processed,
            updated,
            errors: errors.length,
            errorDetails: errors
        };

        console.log('🎉 Kết quả cuối cùng:', result);
        return result;

    } catch (error) {
        console.error('❌ Lỗi khi thêm username/password vào Google Sheets:', error);
        throw error;
    }
};

// Hàm chạy một lần cho tất cả sheets USER_LIST
export const addUsernamePasswordToAllSheets = async () => {
    try {
        console.log('🚀 Bắt đầu thêm username/password cho tất cả sheets...');

        // Lấy tất cả sheets có type USER_LIST
        const userListSheets = await getUserListSheets();

        if (!userListSheets || userListSheets.length === 0) {
            console.log('❌ Không có Google Sheets nào để xử lý');
            return {
                message: 'Không có Google Sheets nào để xử lý',
                results: []
            };
        }

        console.log(`📊 Tìm thấy ${userListSheets.length} sheets để xử lý`);
        const results = [];

        // Xử lý từng sheet
        for (const sheet of userListSheets) {
            try {
                console.log(`\n🔄 Đang xử lý sheet: ${sheet.name} (ID: ${sheet.id})`);
                const result = await addUsernamePasswordToSheet(sheet.id);

                results.push({
                    sheetId: sheet.id,
                    sheetName: sheet.name,
                    ...result
                });

                console.log(`✅ Hoàn thành sheet: ${sheet.name}`);

            } catch (error) {
                console.error(`❌ Lỗi khi xử lý sheet ${sheet.name}:`, error);
                results.push({
                    sheetId: sheet.id,
                    sheetName: sheet.name,
                    message: `Lỗi: ${error.message}`,
                    processed: 0,
                    updated: 0,
                    errors: 1,
                    errorDetails: [{ error: error.message }]
                });
            }
        }

        const totalProcessed = results.reduce((sum, r) => sum + (r.processed || 0), 0);
        const totalUpdated = results.reduce((sum, r) => sum + (r.updated || 0), 0);
        const totalErrors = results.reduce((sum, r) => sum + (r.errors || 0), 0);

        const finalResult = {
            message: `Hoàn thành tất cả! Đã xử lý ${totalProcessed} dòng, update thành công ${totalUpdated} dòng, ${totalErrors} lỗi`,
            totalSheets: userListSheets.length,
            totalProcessed,
            totalUpdated,
            totalErrors,
            results
        };

        console.log('\n🎉 KẾT QUẢ CUỐI CÙNG:', finalResult);
        return finalResult;

    } catch (error) {
        console.error('❌ Lỗi khi xử lý tất cả sheets:', error);
        throw error;
    }
};
