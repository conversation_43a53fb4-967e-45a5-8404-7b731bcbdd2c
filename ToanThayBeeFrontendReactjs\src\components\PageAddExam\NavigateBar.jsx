const NavigateBar = ({ list, active, setActive }) => {
    return (
        <div className="flex flex-row text-xs w-full">
            {list.map((item, index) => (
                <button
                    key={item.id}
                    onClick={() => setActive(item.value)}
                    className={`flex-1 p-2 text-center ${active === item.value ? 'font-bold' : ' border-b '} first:border-r last:border-l border-gray-200 cursor-pointer `}>
                    {item.name}
                </button>
            ))}
        </div>
    )
}

export default NavigateBar;