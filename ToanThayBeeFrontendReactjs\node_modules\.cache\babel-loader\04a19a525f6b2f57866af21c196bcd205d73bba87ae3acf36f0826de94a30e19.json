{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\RightContent.jsx\";\nimport NavigateBar from \"../PageAddExam/NavigateBar\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RightContent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 overflow-y-auto p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold text-gray-800 mb-4\",\n      children: \"Xem tr\\u01B0\\u1EDBc\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 9\n  }, this);\n};\n_c = RightContent;\nexport default RightContent;\nvar _c;\n$RefreshReg$(_c, \"RightContent\");", "map": {"version": 3, "names": ["NavigateBar", "jsxDEV", "_jsxDEV", "RightContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/RightContent.jsx"], "sourcesContent": ["import NavigateBar from \"../PageAddExam/NavigateBar\";\r\n\r\nconst RightContent = () => {\r\n    return (\r\n        <div className=\"flex-1 overflow-y-auto p-4\">\r\n            <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Xem trước</h2>\r\n            <div className=\"space-y-3\">\r\n                {/* Hiển thị trước câu hỏi ở đây */}\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default RightContent;"], "mappings": ";AAAA,OAAOA,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,oBACID,OAAA;IAAKE,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACvCH,OAAA;MAAIE,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvEP,OAAA;MAAKE,SAAS,EAAC;IAAW;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAErB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAC,EAAA,GATKP,YAAY;AAWlB,eAAeA,YAAY;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}