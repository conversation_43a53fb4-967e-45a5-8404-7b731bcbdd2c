{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\QuestionView.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { FileText } from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { reorderStatements, reorderQuestions } from \"src/features/questionsExam/questionsExamSlice\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport SortableQuestionItem from \"../PageAIexam/SortableQuestionItem\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuestionContent = _ref => {\n  _s();\n  var _question$ExamQuestio, _codes$chapter, _codes$chapter$find;\n  let {\n    question,\n    index\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\\n            \".concat(selectedIndex === ((_question$ExamQuestio = question.ExamQuestions) === null || _question$ExamQuestio === void 0 ? void 0 : _question$ExamQuestio.order) ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\"),\n    onClick: () => {\n      var _question$ExamQuestio2;\n      return dispatch(setSelectedIndex((_question$ExamQuestio2 = question.ExamQuestions) === null || _question$ExamQuestio2 === void 0 ? void 0 : _question$ExamQuestio2.order));\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      div: true,\n      className: \"flex flex-wrap items-center text-xs text-gray-500 gap-x-4 gap-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-gray-700\",\n        children: [\"C\\xE2u \", index + 1]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\u0110\\u1ED9 kh\\xF3: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: question.difficulty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Ch\\u01B0\\u01A1ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: ((_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === question.chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || \"Chưa xác định\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-base text-gray-800 leading-relaxed\",\n        children: /*#__PURE__*/_jsxDEV(LatexRenderer, {\n          className: \"text-gray-800 text-xs\",\n          text: question.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 17\n      }, this), question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center w-full h-[10rem] mt-1\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: question.imageUrl,\n          alt: \"question\",\n          className: \"object-contain w-full h-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"text-xs font-bold\",\n        children: \"L\\u1EDDi gi\\u1EA3i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: question.solution,\n        className: \" w-full\",\n        style: {\n          fontSize: '12px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 17\n    }, this)]\n  }, question.id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionContent, \"XZZ2Q+l9zMAy4Vp/xsF8U7mo49M=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = QuestionContent;\nconst QuestionViewHeader = _ref2 => {\n  let {\n    title,\n    count,\n    noQuestionText\n  } = _ref2;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" p-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-1 mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-3 h-3 text-gray-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-xs font-semibold text-gray-900\",\n        children: [title, \" (\", count, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this), count === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-4 text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-6 h-6 mx-auto mb-1 opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs\",\n        children: noQuestionText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 9\n  }, this);\n};\n_c2 = QuestionViewHeader;\nconst QuestionTNView = () => {\n  _s2();\n  const {\n    questionsExam,\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const [questionsTN, setQuestionsTN] = useState([]);\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const dispatch = useDispatch();\n  useEffect(() => {\n    setQuestionsTN(questionsExam.filter(q => q.typeOfQuestion === 'TN'));\n  }, [questionsExam]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\",\n      count: questionsTN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col gap-4\",\n      children: questionsTN.map((question, index) => /*#__PURE__*/_jsxDEV(QuestionContent, {\n        question: question,\n        index: index\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s2(QuestionTNView, \"1+RuVH3kC0NCu1cnn0vlYg/Ibwk=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c3 = QuestionTNView;\nconst QuestionDSView = () => {\n  _s3();\n  const {\n    questionsExam,\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const [questionsDS, setQuestionsDS] = useState([]);\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const dispatch = useDispatch();\n  useEffect(() => {\n    setQuestionsDS(questionsExam.filter(q => q.typeOfQuestion === 'DS'));\n  }, [questionsExam]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi \\u0111\\xFAng sai\",\n      count: questionsDS.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi \\u0111\\xFAng sai\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col gap-4\",\n      children: questionsDS.map((question, index) => /*#__PURE__*/_jsxDEV(QuestionContent, {\n        question: question,\n        index: index\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s3(QuestionDSView, \"ZDc0MkYy/C9SINXWupXUjKn7BIA=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c4 = QuestionDSView;\nconst QuestionTLNView = () => {\n  _s4();\n  const {\n    questionsExam,\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  const [questionsTLN, setQuestionsTLN] = useState([]);\n  useEffect(() => {\n    setQuestionsTLN(questionsExam.filter(q => q.typeOfQuestion === 'TLN'));\n  }, [questionsExam]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\",\n      count: questionsTLN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col gap-4\",\n      children: questionsTLN.map((question, index) => /*#__PURE__*/_jsxDEV(QuestionContent, {\n        question: question,\n        index: index\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s4(QuestionTLNView, \"lMktx9ypr+KT1Lv7d9GhrS8Xzgo=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c5 = QuestionTLNView;\nconst QuestionView = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionTNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"border-gray-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(QuestionDSView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"border-gray-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(QuestionTLNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_c6 = QuestionView;\nexport default QuestionView;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"QuestionContent\");\n$RefreshReg$(_c2, \"QuestionViewHeader\");\n$RefreshReg$(_c3, \"QuestionTNView\");\n$RefreshReg$(_c4, \"QuestionDSView\");\n$RefreshReg$(_c5, \"QuestionTLNView\");\n$RefreshReg$(_c6, \"QuestionView\");", "map": {"version": 3, "names": ["FileText", "useEffect", "useState", "useSelector", "useDispatch", "setSelectedIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "reorderStatements", "reorderQuestions", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "SortableQuestionItem", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuestionContent", "_ref", "_s", "_question$ExamQuestio", "_codes$chapter", "_codes$chapter$find", "question", "index", "dispatch", "selectedIndex", "state", "questionsExam", "codes", "className", "concat", "ExamQuestions", "order", "onClick", "_question$ExamQuestio2", "children", "div", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "difficulty", "find", "c", "code", "chapter", "description", "text", "content", "imageUrl", "src", "alt", "solution", "style", "fontSize", "id", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "title", "count", "noQuestionText", "_c2", "QuestionTNView", "_s2", "questionsTN", "setQuestionsTN", "prefixTN", "filter", "q", "typeOfQuestion", "length", "map", "_c3", "QuestionDSView", "_s3", "questionsDS", "setQuestionsDS", "prefixDS", "_c4", "QuestionTLNView", "_s4", "questionsTLN", "setQuestionsTLN", "_c5", "Question<PERSON>iew", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/QuestionView.jsx"], "sourcesContent": ["import { FileText } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\nimport { reorderStatements, reorderQuestions } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport {\r\n    DndContext,\r\n    closestCenter,\r\n    KeyboardSensor,\r\n    PointerSensor,\r\n    useSensor,\r\n    useSensors,\r\n} from '@dnd-kit/core';\r\nimport {\r\n    SortableContext,\r\n    sortableKeyboardCoordinates,\r\n    verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport SortableQuestionItem from \"../PageAIexam/SortableQuestionItem\";\r\n\r\nconst QuestionContent = ({ question, index }) => {\r\n    const dispatch = useDispatch();\r\n    const { selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    return (\r\n        <div\r\n            key={question.id}\r\n            className={`\r\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\r\n            ${selectedIndex === question.ExamQuestions?.order ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\"}`}\r\n            onClick={() => dispatch(setSelectedIndex(question.ExamQuestions?.order))}\r\n        >\r\n            {/* Thông tin câu hỏi */}\r\n            <div div className=\"flex flex-wrap items-center text-xs text-gray-500 gap-x-4 gap-y-1\" >\r\n                <span className=\"font-medium text-gray-700\">Câu {index + 1}</span>\r\n                <span>Độ khó: <span className=\"text-gray-700\">{question.difficulty}</span></span>\r\n                <span>\r\n                    Chương:{\" \"}\r\n                    <span className=\"text-gray-700\">\r\n                        {codes[\"chapter\"]?.find(c => c.code === question.chapter)?.description || \"Chưa xác định\"}\r\n                    </span>\r\n                </span>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col gap-2\">\r\n                <div className=\"text-base text-gray-800 leading-relaxed\">\r\n                    <LatexRenderer className=\"text-gray-800 text-xs\" text={question.content} />\r\n                </div>\r\n                {question.imageUrl && (\r\n                    <div className=\"flex flex-col items-center justify-center w-full h-[10rem] mt-1\">\r\n                        <img\r\n                            src={question.imageUrl}\r\n                            alt=\"question\"\r\n                            className=\"object-contain w-full h-full\"\r\n                        />\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {question.solution && (\r\n                <div className=\"mt-2\">\r\n                    <h6 className=\"text-xs font-bold\">Lời giải</h6>\r\n                    <MarkdownPreviewWithMath content={question.solution} className=\" w-full\" style={{ fontSize: '12px' }} />\r\n                </div>\r\n            )}\r\n\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nconst QuestionViewHeader = ({ title, count, noQuestionText }) => {\r\n    return (\r\n        <div className=\" p-3 \">\r\n            <div className=\"flex items-center gap-1 mb-2\">\r\n                <FileText className=\"w-3 h-3 text-gray-600\" />\r\n                <h4 className=\"text-xs font-semibold text-gray-900\">{title} ({count})</h4>\r\n            </div>\r\n            {count === 0 && (\r\n                <div className=\"text-center py-4 text-gray-500\">\r\n                    <FileText className=\"w-6 h-6 mx-auto mb-1 opacity-50\" />\r\n                    <p className=\"text-xs\">{noQuestionText}</p>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst QuestionTNView = () => {\r\n    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const [questionsTN, setQuestionsTN] = useState([]);\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        setQuestionsTN(questionsExam.filter(q => q.typeOfQuestion === 'TN'));\r\n    }, [questionsExam]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trắc nghiệm\" count={questionsTN.length} noQuestionText=\"Chưa có câu hỏi trắc nghiệm\" />\r\n            <div className=\"p-3 flex flex-col gap-4\">\r\n                {questionsTN.map((question, index) => (\r\n                    <QuestionContent key={index} question={question} index={index} />\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionDSView = () => {\r\n    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const [questionsDS, setQuestionsDS] = useState([]);\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const dispatch = useDispatch();\r\n\r\n    useEffect(() => {\r\n        setQuestionsDS(questionsExam.filter(q => q.typeOfQuestion === 'DS'));\r\n    }, [questionsExam]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi đúng sai\" count={questionsDS.length} noQuestionText=\"Chưa có câu hỏi đúng sai\" />\r\n            <div className=\"p-3 flex flex-col gap-4\">\r\n                {questionsDS.map((question, index) => (\r\n                    <QuestionContent key={index} question={question} index={index} />\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionTLNView = () => {\r\n    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n    const [questionsTLN, setQuestionsTLN] = useState([]);\r\n\r\n    useEffect(() => {\r\n        setQuestionsTLN(questionsExam.filter(q => q.typeOfQuestion === 'TLN'));\r\n    }, [questionsExam]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trả lời ngắn\" count={questionsTLN.length} noQuestionText=\"Chưa có câu hỏi trả lời ngắn\" />\r\n            <div className=\"p-3 flex flex-col gap-4\">\r\n                {questionsTLN.map((question, index) => (\r\n                    <QuestionContent key={index} question={question} index={index} />\r\n                ))}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionView = () => {\r\n\r\n    return (\r\n        <>\r\n            <QuestionTNView />\r\n            <hr className=\"border-gray-200\" />\r\n            <QuestionDSView />\r\n            <hr className=\"border-gray-200\" />\r\n            <QuestionTLNView />\r\n        </>\r\n    )\r\n}\r\n\r\nexport default QuestionView;"], "mappings": ";;;;;AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAC9D,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,+CAA+C;AACnG,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAC1B,OAAOC,oBAAoB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtE,MAAMC,eAAe,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,cAAA,EAAAC,mBAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAN,IAAA;EACxC,MAAMO,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B;EAAc,CAAC,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACrE,MAAM;IAAEC;EAAM,CAAC,GAAGjC,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,oBACIf,OAAA;IAEIgB,SAAS,oGAAAC,MAAA,CAEPL,aAAa,OAAAN,qBAAA,GAAKG,QAAQ,CAACS,aAAa,cAAAZ,qBAAA,uBAAtBA,qBAAA,CAAwBa,KAAK,IAAG,4BAA4B,GAAG,2CAA2C,CAAG;IACjIC,OAAO,EAAEA,CAAA;MAAA,IAAAC,sBAAA;MAAA,OAAMV,QAAQ,CAAC3B,gBAAgB,EAAAqC,sBAAA,GAACZ,QAAQ,CAACS,aAAa,cAAAG,sBAAA,uBAAtBA,sBAAA,CAAwBF,KAAK,CAAC,CAAC;IAAA,CAAC;IAAAG,QAAA,gBAGzEtB,OAAA;MAAKuB,GAAG;MAACP,SAAS,EAAC,mEAAmE;MAAAM,QAAA,gBAClFtB,OAAA;QAAMgB,SAAS,EAAC,2BAA2B;QAAAM,QAAA,GAAC,SAAI,EAACZ,KAAK,GAAG,CAAC;MAAA;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClE3B,OAAA;QAAAsB,QAAA,GAAM,uBAAQ,eAAAtB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAM,QAAA,EAAEb,QAAQ,CAACmB;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjF3B,OAAA;QAAAsB,QAAA,GAAM,mBACK,EAAC,GAAG,eACXtB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAM,QAAA,EAC1B,EAAAf,cAAA,GAAAQ,KAAK,CAAC,SAAS,CAAC,cAAAR,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKtB,QAAQ,CAACuB,OAAO,CAAC,cAAAxB,mBAAA,uBAAxDA,mBAAA,CAA0DyB,WAAW,KAAI;QAAe;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN3B,OAAA;MAAKgB,SAAS,EAAC,qBAAqB;MAAAM,QAAA,gBAChCtB,OAAA;QAAKgB,SAAS,EAAC,yCAAyC;QAAAM,QAAA,eACpDtB,OAAA,CAACf,aAAa;UAAC+B,SAAS,EAAC,uBAAuB;UAACkB,IAAI,EAAEzB,QAAQ,CAAC0B;QAAQ;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,EACLlB,QAAQ,CAAC2B,QAAQ,iBACdpC,OAAA;QAAKgB,SAAS,EAAC,iEAAiE;QAAAM,QAAA,eAC5EtB,OAAA;UACIqC,GAAG,EAAE5B,QAAQ,CAAC2B,QAAS;UACvBE,GAAG,EAAC,UAAU;UACdtB,SAAS,EAAC;QAA8B;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAELlB,QAAQ,CAAC8B,QAAQ,iBACdvC,OAAA;MAAKgB,SAAS,EAAC,MAAM;MAAAM,QAAA,gBACjBtB,OAAA;QAAIgB,SAAS,EAAC,mBAAmB;QAAAM,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/C3B,OAAA,CAACd,uBAAuB;QAACiD,OAAO,EAAE1B,QAAQ,CAAC8B,QAAS;QAACvB,SAAS,EAAC,SAAS;QAACwB,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CACR;EAAA,GAtCIlB,QAAQ,CAACiC,EAAE;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAyCf,CAAC;AAEd,CAAC;AAAAtB,EAAA,CAlDKF,eAAe;EAAA,QACApB,WAAW,EACFD,WAAW,EACnBA,WAAW;AAAA;AAAA6D,EAAA,GAH3BxC,eAAe;AAoDrB,MAAMyC,kBAAkB,GAAGC,KAAA,IAAsC;EAAA,IAArC;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAe,CAAC,GAAAH,KAAA;EACxD,oBACI7C,OAAA;IAAKgB,SAAS,EAAC,OAAO;IAAAM,QAAA,gBAClBtB,OAAA;MAAKgB,SAAS,EAAC,8BAA8B;MAAAM,QAAA,gBACzCtB,OAAA,CAACrB,QAAQ;QAACqC,SAAS,EAAC;MAAuB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9C3B,OAAA;QAAIgB,SAAS,EAAC,qCAAqC;QAAAM,QAAA,GAAEwB,KAAK,EAAC,IAAE,EAACC,KAAK,EAAC,GAAC;MAAA;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,EACLoB,KAAK,KAAK,CAAC,iBACR/C,OAAA;MAAKgB,SAAS,EAAC,gCAAgC;MAAAM,QAAA,gBAC3CtB,OAAA,CAACrB,QAAQ;QAACqC,SAAS,EAAC;MAAiC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxD3B,OAAA;QAAGgB,SAAS,EAAC,SAAS;QAAAM,QAAA,EAAE0B;MAAc;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAsB,GAAA,GAfKL,kBAAkB;AAiBxB,MAAMM,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAErC,aAAa;IAAEF;EAAc,CAAC,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACpF,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMyE,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM3C,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAE9BH,SAAS,CAAC,MAAM;IACZyE,cAAc,CAACvC,aAAa,CAACyC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,IAAI,CAAC,CAAC;EACxE,CAAC,EAAE,CAAC3C,aAAa,CAAC,CAAC;EAEnB,oBACId,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACItB,OAAA,CAAC4C,kBAAkB;MAACE,KAAK,EAAC,uCAAqB;MAACC,KAAK,EAAEK,WAAW,CAACM,MAAO;MAACV,cAAc,EAAC;IAA6B;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1H3B,OAAA;MAAKgB,SAAS,EAAC,yBAAyB;MAAAM,QAAA,EACnC8B,WAAW,CAACO,GAAG,CAAC,CAAClD,QAAQ,EAAEC,KAAK,kBAC7BV,OAAA,CAACG,eAAe;QAAaM,QAAQ,EAAEA,QAAS;QAACC,KAAK,EAAEA;MAAM,GAAxCA,KAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqC,CACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAwB,GAAA,CApBKD,cAAc;EAAA,QACyBpE,WAAW,EAGnCC,WAAW;AAAA;AAAA6E,GAAA,GAJ1BV,cAAc;AAsBpB,MAAMW,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAEhD,aAAa;IAAEF;EAAc,CAAC,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACpF,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMoF,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMtD,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAE9BH,SAAS,CAAC,MAAM;IACZoF,cAAc,CAAClD,aAAa,CAACyC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,IAAI,CAAC,CAAC;EACxE,CAAC,EAAE,CAAC3C,aAAa,CAAC,CAAC;EAEnB,oBACId,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACItB,OAAA,CAAC4C,kBAAkB;MAACE,KAAK,EAAC,kCAAkB;MAACC,KAAK,EAAEgB,WAAW,CAACL,MAAO;MAACV,cAAc,EAAC;IAA0B;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpH3B,OAAA;MAAKgB,SAAS,EAAC,yBAAyB;MAAAM,QAAA,EACnCyC,WAAW,CAACJ,GAAG,CAAC,CAAClD,QAAQ,EAAEC,KAAK,kBAC7BV,OAAA,CAACG,eAAe;QAAaM,QAAQ,EAAEA,QAAS;QAACC,KAAK,EAAEA;MAAM,GAAxCA,KAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqC,CACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAmC,GAAA,CApBKD,cAAc;EAAA,QACyB/E,WAAW,EAGnCC,WAAW;AAAA;AAAAmF,GAAA,GAJ1BL,cAAc;AAsBpB,MAAMM,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAM;IAAEtD,aAAa;IAAEF;EAAc,CAAC,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACC,aAAa,CAAC;EACpF,MAAMH,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAEpDD,SAAS,CAAC,MAAM;IACZ0F,eAAe,CAACxD,aAAa,CAACyC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,KAAK,CAAC,CAAC;EAC1E,CAAC,EAAE,CAAC3C,aAAa,CAAC,CAAC;EAEnB,oBACId,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACItB,OAAA,CAAC4C,kBAAkB;MAACE,KAAK,EAAC,6CAAsB;MAACC,KAAK,EAAEsB,YAAY,CAACX,MAAO;MAACV,cAAc,EAAC;IAA8B;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7H3B,OAAA;MAAKgB,SAAS,EAAC,yBAAyB;MAAAM,QAAA,EACnC+C,YAAY,CAACV,GAAG,CAAC,CAAClD,QAAQ,EAAEC,KAAK,kBAC9BV,OAAA,CAACG,eAAe;QAAaM,QAAQ,EAAEA,QAAS;QAACC,KAAK,EAAEA;MAAM,GAAxCA,KAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqC,CACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAyC,GAAA,CAnBKD,eAAe;EAAA,QACwBrF,WAAW,EACnCC,WAAW;AAAA;AAAAwF,GAAA,GAF1BJ,eAAe;AAqBrB,MAAMK,YAAY,GAAGA,CAAA,KAAM;EAEvB,oBACIxE,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACItB,OAAA,CAACkD,cAAc;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClB3B,OAAA;MAAIgB,SAAS,EAAC;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClC3B,OAAA,CAAC6D,cAAc;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClB3B,OAAA;MAAIgB,SAAS,EAAC;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClC3B,OAAA,CAACmE,eAAe;MAAA3C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACrB,CAAC;AAEX,CAAC;AAAA8C,GAAA,GAXKD,YAAY;AAalB,eAAeA,YAAY;AAAC,IAAA7B,EAAA,EAAAM,GAAA,EAAAW,GAAA,EAAAM,GAAA,EAAAK,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAA/B,EAAA;AAAA+B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}