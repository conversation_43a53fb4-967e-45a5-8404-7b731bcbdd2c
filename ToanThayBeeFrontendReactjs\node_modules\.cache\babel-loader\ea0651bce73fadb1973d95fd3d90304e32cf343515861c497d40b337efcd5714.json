{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\practice\\\\ExamDetail.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$();\nimport UserLayout from \"../../../layouts/UserLayout\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport { fetchPublicExamById, saveExamForUser, fetchRelatedExamsIfNeeded } from \"../../../features/exam/examDetailSlice\";\nimport { fetchCodesByType } from \"../../../features/code/codeSlice\";\nimport { useEffect, useState, useRef } from \"react\";\nimport { StarIcon, Info, RefreshCcw, BookOpen, Calendar, Award, FileText, BarChart2, History, QrCode as QrCodeIcon, Pin, Eye, Play, Clock, GraduationCap, ListOrdered, StickyNote, ExternalLink, Send, Smile } from \"lucide-react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { setView } from \"../../../features/exam/examDetailSlice\";\nimport { formatDate } from \"src/utils/formatters\";\nimport QRCodeComponent from \"src/components/QrCode\";\nimport YouTubePlayer from \"src/components/YouTubePlayer\";\nimport { fetchCommentsByExamId, postComment, putComment, deleteComment, setCurrentPage } from \"src/features/comments/ExamCommentsSlice\";\nimport CommentSection from \"src/components/comment/CommentSection\";\nimport LoadingText from \"src/components/loading/LoadingText\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ActionButton = _ref => {\n  let {\n    icon: Icon,\n    title,\n    shortTitle,\n    isActive = false,\n    onClick\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onClick,\n    className: \"w-fit px-2 py-[7px] rounded-md text-xs font-medium transition-colors whitespace-nowrap\\n                \".concat(isActive ? 'bg-sky-100 text-sky-700 border border-sky-300' : 'bg-gray-100 text-gray-700 hover:bg-gray-200', \"\\n            \"),\n    children: [/*#__PURE__*/_jsxDEV(Icon, {\n      className: \"w-4 h-4 inline mr-1 lg:mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"hidden sm:inline\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"sm:hidden\",\n      children: shortTitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 9\n  }, this);\n};\n_c = ActionButton;\nconst ActionButtons = () => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    view,\n    exam\n  } = useSelector(state => state.examDetail);\n  const handleClickRanking = () => {\n    // Cho phép xem BXH nếu:\n    // 1. Đã làm bài và được phép xem đáp án, HOẶC\n    // 2. Không cho phép làm bài nữa (hết thời gian)\n    if (exam !== null && exam !== void 0 && exam.isDone && exam !== null && exam !== void 0 && exam.seeCorrectAnswer || !(exam !== null && exam !== void 0 && exam.acceptDoExam) && exam !== null && exam !== void 0 && exam.seeCorrectAnswer) {\n      navigate(\"/practice/exam/\".concat(exam.id, \"/ranking\"));\n    }\n  };\n  const handleClickPreviewExam = () => {\n    // Cho phép xem đề thi nếu:\n    // 1. Đã làm bài và được phép xem đáp án, HOẶC\n    // 2. Không cho phép làm bài nữa (hết thời gian)\n    if (exam !== null && exam !== void 0 && exam.isDone && exam !== null && exam !== void 0 && exam.seeCorrectAnswer || !(exam !== null && exam !== void 0 && exam.acceptDoExam) && exam !== null && exam !== void 0 && exam.seeCorrectAnswer) {\n      navigate(\"/practice/exam/\".concat(exam.id, \"/preview\"));\n    }\n  };\n  const handleClickHistory = () => {\n    if (!(exam !== null && exam !== void 0 && exam.isDone) || !(exam !== null && exam !== void 0 && exam.seeCorrectAnswer)) return;\n    navigate(\"/practice/exam/\".concat(exam.id, \"/history\"));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-wrap gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n      icon: Info,\n      title: \"Th\\xF4ng tin chi ti\\u1EBFt\",\n      shortTitle: \"Chi ti\\u1EBFt\",\n      isActive: view === \"detail\",\n      onClick: () => dispatch(setView(\"detail\"))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n      icon: Award,\n      title: \"B\\u1EA3ng x\\u1EBFp h\\u1EA1ng\",\n      shortTitle: \"X\\u1EBFp h\\u1EA1ng\",\n      isActive: view === \"ranking\"\n      // onClick={() => dispatch(setView(\"ranking\"))}\n      ,\n      onClick: handleClickRanking\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n      icon: Eye,\n      title: \"Xem \\u0111\\u1EC1 thi\",\n      shortTitle: \"Xem \\u0111\\u1EC1\",\n      isActive: view === \"preview\"\n      // onClick={() => dispatch(setView(\"preview\"))}\n      ,\n      onClick: handleClickPreviewExam\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n      icon: History,\n      title: \"L\\u1ECBch s\\u1EED l\\xE0m b\\xE0i\",\n      shortTitle: \"L\\u1ECBch s\\u1EED\",\n      isActive: view === \"history\"\n      // onClick={() => dispatch(setView(\"history\"))}\n      ,\n      onClick: handleClickHistory\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 9\n  }, this);\n};\n_s(ActionButtons, \"gkx3cAT2mJEKKt4tNHb11mboLmE=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c2 = ActionButtons;\nconst DoExamButton = _ref2 => {\n  _s2();\n  let {\n    onClick,\n    disabled = false\n  } = _ref2;\n  const {\n    loading\n  } = useSelector(state => state.states);\n  return /*#__PURE__*/_jsxDEV(LoadingText, {\n    loading: true,\n    w: \"w-20\",\n    h: \"h-[30px]\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClick,\n      disabled: disabled,\n      className: \"flex-shrink-0 flex items-center gap-2 px-3 py-[7px] rounded-md text-xs font-medium transition-all\\n                \".concat(disabled ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-green-600 text-white hover:bg-green-700', \"\\n            \"),\n      children: [/*#__PURE__*/_jsxDEV(Play, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"L\\xE0m b\\xE0i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 9\n  }, this);\n};\n_s2(DoExamButton, \"qPH7E8ZBRBZG+ChS5bGqdjAK4Pc=\", false, function () {\n  return [useSelector];\n});\n_c3 = DoExamButton;\nconst InfoRow = _ref3 => {\n  _s3();\n  let {\n    icon: Icon,\n    label,\n    value,\n    w = \"w-48\"\n  } = _ref3;\n  const {\n    loading\n  } = useSelector(state => state.states);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-between gap-2 text-sm text-gray-800\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        size: 16,\n        className: \"text-sky-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-gray-800\",\n        children: [label, \":\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(LoadingText, {\n      loading: true,\n      w: w,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 9\n  }, this);\n};\n_s3(InfoRow, \"qPH7E8ZBRBZG+ChS5bGqdjAK4Pc=\", false, function () {\n  return [useSelector];\n});\n_c4 = InfoRow;\nconst InfoExam = () => {\n  _s4();\n  var _codes$examType, _codes$examType$find, _codes$chapter, _codes$chapter$find;\n  const {\n    exam,\n    loading\n  } = useSelector(state => state.examDetail);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col border border-gray-300 rounded-md\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]\",\n      children: [/*#__PURE__*/_jsxDEV(LoadingText, {\n        loading: true,\n        w: \"w-48\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-Inter text-sm font-semibold\",\n          children: exam === null || exam === void 0 ? void 0 : exam.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(RefreshCcw, {\n          size: 16,\n          className: \"flex-shrink-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(LoadingText, {\n          loading: true,\n          w: \"w-40\",\n          color: \"bg-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"C\\u1EADp nh\\u1EADt:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 48\n            }, this), \" \", formatDate(exam === null || exam === void 0 ? void 0 : exam.updatedAt)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-row gap-3 p-4 bg-white \",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full flex flex-col sm:flex-row gap-4 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full sm:w-1/3 flex justify-center items-center\",\n          children: exam !== null && exam !== void 0 && exam.imageUrl ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: exam.imageUrl,\n            alt: \"Exam\",\n            className: \"w-full max-w-[200px] rounded-md shadow border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-2 rounded-md border\",\n            children: /*#__PURE__*/_jsxDEV(QRCodeComponent, {\n              url: \"https://toanthaybee.edu.vn/practice/exam/\".concat(exam === null || exam === void 0 ? void 0 : exam.id) // hoặc `https://toanthaybee.edu.vn/practice/exam/${exam.id}` hoặc `https://toanthaybee.edu.vn/practice/exam/${exam.id}`\n              ,\n              size: 128\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full flex flex-col sm:gap-4 gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(InfoRow, {\n            icon: BookOpen,\n            label: \"Lo\\u1EA1i \\u0111\\u1EC1\",\n            value: (codes === null || codes === void 0 ? void 0 : (_codes$examType = codes[\"exam type\"]) === null || _codes$examType === void 0 ? void 0 : (_codes$examType$find = _codes$examType.find(c => c.code === (exam === null || exam === void 0 ? void 0 : exam.typeOfExam))) === null || _codes$examType$find === void 0 ? void 0 : _codes$examType$find.description) || (exam === null || exam === void 0 ? void 0 : exam.typeOfExam) || \"Không rõ\",\n            w: \"w-56\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InfoRow, {\n            icon: GraduationCap,\n            label: \"Kh\\u1ED1i\",\n            value: (exam === null || exam === void 0 ? void 0 : exam.class) || \"Không rõ\",\n            w: \"w-48\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InfoRow, {\n            icon: Calendar,\n            label: \"N\\u0103m\",\n            value: (exam === null || exam === void 0 ? void 0 : exam.year) || \"Không rõ\",\n            w: \"w-40\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InfoRow, {\n            icon: Clock,\n            label: \"Th\\u1EDDi gian\",\n            value: exam !== null && exam !== void 0 && exam.testDuration ? \"\".concat(exam.testDuration, \" ph\\xFAt\") : \"Vô thời hạn\",\n            w: \"w-56\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InfoRow, {\n            icon: ListOrdered,\n            label: \"Ch\\u01B0\\u01A1ng\",\n            value: (codes === null || codes === void 0 ? void 0 : (_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === (exam === null || exam === void 0 ? void 0 : exam.chapter))) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || (exam === null || exam === void 0 ? void 0 : exam.chapter) || \"Không rõ\",\n            w: \"w-56\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InfoRow, {\n            icon: BarChart2,\n            label: \"T\\u1EC9 l\\u1EC7 \\u0111\\u1EA1t\",\n            value: exam !== null && exam !== void 0 && exam.passRate ? \"\".concat(exam.passRate, \"%\") : \"Không có\",\n            w: \"w-48\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 17\n      }, this), (exam === null || exam === void 0 ? void 0 : exam.description) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start gap-2 text-sm text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(StickyNote, {\n          size: 16,\n          className: \"text-sky-600 mt-0.5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-800\",\n          children: \"M\\xF4 t\\u1EA3:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"leading-relaxed\",\n          children: exam.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 9\n  }, this);\n};\n_s4(InfoExam, \"xoA6OQnLBcMxejdkSw1+YmUNjNc=\", false, function () {\n  return [useSelector, useSelector];\n});\n_c5 = InfoExam;\nconst SolutionVideo = () => {\n  _s5();\n  const {\n    exam,\n    loading\n  } = useSelector(state => state.examDetail);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col border border-gray-300 rounded-md\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm font-Inter font-semibold\",\n        children: \"Video Ch\\u1EEFa b\\xE0i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(LoadingText, {\n        loading: true,\n        w: \"w-40\",\n        children: exam !== null && exam !== void 0 && exam.isDone ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.open(exam === null || exam === void 0 ? void 0 : exam.solutionUrl, \"_blank\"),\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md transition-all text-gray-700 hover:bg-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"M\\u1EDF trong tab m\\u1EDBi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 cursor-not-allowed\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Ch\\u01B0a l\\xE0m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(LoadingText, {\n      loading: false,\n      w: \"w-full\",\n      h: \"h-40\",\n      children: [(exam === null || exam === void 0 ? void 0 : exam.isDone) && (exam === null || exam === void 0 ? void 0 : exam.solutionUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/_jsxDEV(YouTubePlayer, {\n          url: exam === null || exam === void 0 ? void 0 : exam.solutionUrl\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 21\n      }, this), (exam === null || exam === void 0 ? void 0 : exam.isDone) && !(exam !== null && exam !== void 0 && exam.solutionUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Ch\\u01B0a c\\xF3 video h\\u01B0\\u1EDBng d\\u1EABn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 21\n      }, this), !(exam !== null && exam !== void 0 && exam.isDone) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Ch\\u1EC9 c\\xF3 th\\u1EC3 xem sau khi l\\xE0m b\\xE0i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 9\n  }, this);\n};\n_s5(SolutionVideo, \"lRa3hYLyJpoOevefzZ/o/VIXxqc=\", false, function () {\n  return [useSelector];\n});\n_c6 = SolutionVideo;\nconst RelatedExamCard = _ref4 => {\n  let {\n    exam,\n    onClick,\n    compact = false\n  } = _ref4;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    onClick: onClick,\n    className: \"bg-white rounded-md border border-gray-200 cursor-pointer p-3 sm:p-4 flex flex-col gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm font-semibold text-gray-800 line-clamp-2\",\n        children: exam.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClick,\n        className: \"whitespace-nowrap text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Xem \\u0111\\u1EC1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-2 text-xs text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(Clock, {\n        size: 14\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: exam.testDuration ? \"\".concat(exam.testDuration, \" ph\\xFAt\") : \"Vô thời hạn\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-2 text-xs text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(Calendar, {\n        size: 14\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"N\\u0103m: \", exam.year || \"Không rõ\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 299,\n    columnNumber: 9\n  }, this);\n};\n_c7 = RelatedExamCard;\nconst RightPanel = () => {\n  _s6();\n  const {\n    relatedExams\n  } = useSelector(state => state.examDetail);\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sm:w-1/4 flex flex-col gap-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-lg font-semibold\",\n      children: \"B\\xE0i thi kh\\xE1c\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: relatedExams.map(exam => /*#__PURE__*/_jsxDEV(RelatedExamCard, {\n        exam: exam,\n        onClick: () => navigate(\"/practice/exam/\".concat(exam.id)),\n        compact: true\n      }, exam.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 330,\n    columnNumber: 9\n  }, this);\n};\n_s6(RightPanel, \"l6JjVJSMiHgZ1OUH38L+dPHBXEg=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c8 = RightPanel;\nconst ExamDetail = () => {\n  _s7();\n  const {\n    examId\n  } = useParams();\n  const {\n    exam,\n    loading\n  } = useSelector(state => state.examDetail);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const shareLink = \"\".concat(window.location.origin, \"/practice/exam/\").concat(examId);\n  const {\n    comments,\n    pagination\n  } = useSelector(state => state.comments);\n  const {\n    page\n  } = pagination;\n  const handleCopyLink = () => {\n    navigator.clipboard.writeText(shareLink);\n  };\n  const handleSaveExam = () => {\n    dispatch(saveExamForUser({\n      examId\n    }));\n  };\n  const handleDoExam = () => {\n    navigate(\"/practice/exam/\".concat(examId, \"/do\"));\n  };\n  const handleSendComment = content => {\n    dispatch(postComment({\n      examId,\n      content\n    }));\n  };\n  const handleUpdateComment = (commentId, content) => {\n    dispatch(putComment({\n      commentId,\n      content\n    }));\n  };\n  const handleDeleteComment = commentId => {\n    dispatch(deleteComment(commentId));\n  };\n  const handleReplyComment = (content, parentCommentId) => {\n    dispatch(postComment({\n      examId,\n      content,\n      parentCommentId\n    }));\n  };\n  useEffect(() => {\n    dispatch(fetchPublicExamById(examId));\n    dispatch(fetchRelatedExamsIfNeeded(examId));\n    dispatch(fetchCodesByType([\"chapter\", \"exam type\", \"user type\"]));\n  }, [dispatch, examId]);\n  useEffect(() => {\n    dispatch(fetchCommentsByExamId({\n      examId,\n      page\n    }));\n  }, [dispatch, examId, page]);\n  return /*#__PURE__*/_jsxDEV(UserLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container flex flex-col mb-9\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full flex flex-col md:flex-row gap-2 justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"items-start justify-start flex flex-wrap gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"text-sky-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(LoadingText, {\n            loading: true,\n            w: \"w-48\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold\",\n              children: exam === null || exam === void 0 ? void 0 : exam.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"py-0.5 px-1.5 border rounded-full text-xs text-gray-500 flex-shrink-0 \".concat(exam !== null && exam !== void 0 && exam.acceptDoExam ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'),\n              children: exam !== null && exam !== void 0 && exam.acceptDoExam ? 'Hoạt động' : 'Kết thúc'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"py-0.5 px-1.5 border rounded-full text-xs text-gray-500 flex-shrink-0 \".concat(exam !== null && exam !== void 0 && exam.isDone ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'),\n              children: exam !== null && exam !== void 0 && exam.isDone ? 'Đã làm' : 'Chưa làm'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(LoadingText, {\n          loading: true,\n          w: \"w-80\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col md:w-fit w-full gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              // onClick={onClick}\n              className: \"flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 py-[3px] px-3 rounded-md border transition-all\\n                                \".concat(exam !== null && exam !== void 0 && exam.isSave ? \"border-blue-500 bg-blue-50 text-blue-600 hover:bg-blue-100\" : \"border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200\"),\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: exam !== null && exam !== void 0 && exam.isSave ? \"Đã lưu\" : \"Lưu đề\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              // onClick={onClick}\n              className: \"flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"S\\u1ED1 l\\u01B0\\u1EE3t l\\xE0m b\\xE0i: \", exam === null || exam === void 0 ? void 0 : exam.studentCount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              // onClick={onClick}\n              className: \"flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(StarIcon, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Star: \", exam === null || exam === void 0 ? void 0 : exam.studentCount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \"my-6 border-gray-300\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex flex-col gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row justify-between items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(ActionButtons, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(DoExamButton, {\n              onClick: handleDoExam,\n              disabled: !(exam !== null && exam !== void 0 && exam.acceptDoExam)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(InfoExam, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(SolutionVideo, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(CommentSection, {\n            comments: comments,\n            onSubmit: handleSendComment,\n            onUpdate: handleUpdateComment,\n            onDelete: handleDeleteComment,\n            onReply: handleReplyComment\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(RightPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 397,\n    columnNumber: 9\n  }, this);\n};\n\n// Component hiển thị đề thi liên quan\n_s7(ExamDetail, \"VQ/4LgocQKZvo1YnYRyVsHJfgkE=\", false, function () {\n  return [useParams, useSelector, useDispatch, useNavigate, useSelector];\n});\n_c9 = ExamDetail;\nexport default ExamDetail;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ActionButton\");\n$RefreshReg$(_c2, \"ActionButtons\");\n$RefreshReg$(_c3, \"DoExamButton\");\n$RefreshReg$(_c4, \"InfoRow\");\n$RefreshReg$(_c5, \"InfoExam\");\n$RefreshReg$(_c6, \"SolutionVideo\");\n$RefreshReg$(_c7, \"RelatedExamCard\");\n$RefreshReg$(_c8, \"RightPanel\");\n$RefreshReg$(_c9, \"ExamDetail\");", "map": {"version": 3, "names": ["UserLayout", "useSelector", "useDispatch", "useParams", "fetchPublicExamById", "saveExamForUser", "fetchRelatedExamsIfNeeded", "fetchCodesByType", "useEffect", "useState", "useRef", "StarIcon", "Info", "RefreshCcw", "BookOpen", "Calendar", "Award", "FileText", "BarChart2", "History", "QrCode", "QrCodeIcon", "<PERSON>n", "Eye", "Play", "Clock", "GraduationCap", "ListOrdered", "StickyNote", "ExternalLink", "Send", "Smile", "useNavigate", "<PERSON><PERSON><PERSON><PERSON>", "formatDate", "QRCodeComponent", "YouTubePlayer", "fetchCommentsByExamId", "postComment", "putComment", "deleteComment", "setCurrentPage", "CommentSection", "LoadingText", "jsxDEV", "_jsxDEV", "ActionButton", "_ref", "icon", "Icon", "title", "shortTitle", "isActive", "onClick", "className", "concat", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ActionButtons", "_s", "dispatch", "navigate", "view", "exam", "state", "examDetail", "handleClickRanking", "isDone", "seeCorrectAnswer", "acceptDoExam", "id", "handleClickPreviewExam", "handleClickHistory", "_c2", "DoExamButton", "_ref2", "_s2", "disabled", "loading", "states", "w", "h", "size", "_c3", "InfoRow", "_ref3", "_s3", "label", "value", "_c4", "InfoExam", "_s4", "_codes$examType", "_codes$examType$find", "_codes$chapter", "_codes$chapter$find", "codes", "name", "color", "updatedAt", "imageUrl", "src", "alt", "url", "find", "c", "code", "typeOfExam", "description", "class", "year", "testDuration", "chapter", "passRate", "_c5", "SolutionVideo", "_s5", "window", "open", "solutionUrl", "_c6", "RelatedExamCard", "_ref4", "compact", "_c7", "RightPanel", "_s6", "relatedExams", "map", "_c8", "ExamDetail", "_s7", "examId", "shareLink", "location", "origin", "comments", "pagination", "page", "handleCopyLink", "navigator", "clipboard", "writeText", "handleSaveExam", "handleDoExam", "handleSendComment", "content", "handleUpdateComment", "commentId", "handleDeleteComment", "handleReplyComment", "parentCommentId", "isSave", "studentCount", "onSubmit", "onUpdate", "onDelete", "onReply", "_c9", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/practice/ExamDetail.jsx"], "sourcesContent": ["import UserLayout from \"../../../layouts/UserLayout\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { fetchPublicExamById, saveExamForUser, fetchRelatedExamsIfNeeded } from \"../../../features/exam/examDetailSlice\";\r\nimport { fetchCodesByType } from \"../../../features/code/codeSlice\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport {\r\n    StarIcon,\r\n    Info,\r\n    RefreshCcw,\r\n    BookOpen,\r\n    Calendar,\r\n    Award,\r\n    FileText,\r\n    BarChart2,\r\n    History,\r\n    QrCode as QrCodeIcon,\r\n    Pin,\r\n    Eye,\r\n    Play,\r\n    Clock,\r\n    GraduationCap,\r\n    ListOrdered,\r\n    StickyNote,\r\n    ExternalLink,\r\n    Send,\r\n    Smile\r\n} from \"lucide-react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { setView } from \"../../../features/exam/examDetailSlice\";\r\nimport { formatDate } from \"src/utils/formatters\";\r\nimport QRCodeComponent from \"src/components/QrCode\";\r\nimport YouTubePlayer from \"src/components/YouTubePlayer\";\r\nimport { fetchCommentsByExamId, postComment, putComment, deleteComment, setCurrentPage } from \"src/features/comments/ExamCommentsSlice\";\r\nimport CommentSection from \"src/components/comment/CommentSection\";\r\nimport LoadingText from \"src/components/loading/LoadingText\";\r\n\r\nconst ActionButton = ({ icon: Icon, title, shortTitle, isActive = false, onClick }) => {\r\n    return (\r\n        <button\r\n            onClick={onClick}\r\n            className={`w-fit px-2 py-[7px] rounded-md text-xs font-medium transition-colors whitespace-nowrap\r\n                ${isActive\r\n                    ? 'bg-sky-100 text-sky-700 border border-sky-300'\r\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}\r\n            `}\r\n        >\r\n            <Icon className=\"w-4 h-4 inline mr-1 lg:mr-2\" />\r\n            <span className=\"hidden sm:inline\">{title}</span>\r\n            <span className=\"sm:hidden\">{shortTitle}</span>\r\n        </button>\r\n    );\r\n};\r\n\r\nconst ActionButtons = () => {\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const { view, exam } = useSelector((state) => state.examDetail);\r\n\r\n    const handleClickRanking = () => {\r\n        // Cho phép xem BXH nếu:\r\n        // 1. Đã làm bài và được phép xem đáp án, HOẶC\r\n        // 2. Không cho phép làm bài nữa (hết thời gian)\r\n        if ((exam?.isDone && exam?.seeCorrectAnswer) || (!exam?.acceptDoExam && exam?.seeCorrectAnswer)) {\r\n            navigate(`/practice/exam/${exam.id}/ranking`);\r\n        }\r\n\r\n    }\r\n\r\n    const handleClickPreviewExam = () => {\r\n        // Cho phép xem đề thi nếu:\r\n        // 1. Đã làm bài và được phép xem đáp án, HOẶC\r\n        // 2. Không cho phép làm bài nữa (hết thời gian)\r\n        if ((exam?.isDone && exam?.seeCorrectAnswer) || (!exam?.acceptDoExam && exam?.seeCorrectAnswer)) {\r\n            navigate(`/practice/exam/${exam.id}/preview`);\r\n        }\r\n    };\r\n    const handleClickHistory = () => {\r\n        if (!exam?.isDone || !exam?.seeCorrectAnswer) return\r\n        navigate(`/practice/exam/${exam.id}/history`)\r\n    };\r\n    return (\r\n        <div className=\"flex flex-wrap gap-2\">\r\n            <ActionButton\r\n                icon={Info}\r\n                title=\"Thông tin chi tiết\"\r\n                shortTitle=\"Chi tiết\"\r\n                isActive={view === \"detail\"}\r\n                onClick={() => dispatch(setView(\"detail\"))}\r\n            />\r\n            <ActionButton\r\n                icon={Award}\r\n                title=\"Bảng xếp hạng\"\r\n                shortTitle=\"Xếp hạng\"\r\n                isActive={view === \"ranking\"}\r\n                // onClick={() => dispatch(setView(\"ranking\"))}\r\n                onClick={handleClickRanking}\r\n            />\r\n            <ActionButton\r\n                icon={Eye}\r\n                title=\"Xem đề thi\"\r\n                shortTitle=\"Xem đề\"\r\n                isActive={view === \"preview\"}\r\n                // onClick={() => dispatch(setView(\"preview\"))}\r\n                onClick={handleClickPreviewExam}\r\n            />\r\n            <ActionButton\r\n                icon={History}\r\n                title=\"Lịch sử làm bài\"\r\n                shortTitle=\"Lịch sử\"\r\n                isActive={view === \"history\"}\r\n                // onClick={() => dispatch(setView(\"history\"))}\r\n                onClick={handleClickHistory}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nconst DoExamButton = ({ onClick, disabled = false }) => {\r\n    const { loading } = useSelector((state) => state.states);\r\n    return (\r\n        <LoadingText loading={true} w=\"w-20\" h=\"h-[30px]\">\r\n            <button\r\n                onClick={onClick}\r\n                disabled={disabled}\r\n                className={`flex-shrink-0 flex items-center gap-2 px-3 py-[7px] rounded-md text-xs font-medium transition-all\r\n                ${disabled\r\n                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'\r\n                        : 'bg-green-600 text-white hover:bg-green-700'}\r\n            `}\r\n            >\r\n                <Play size={16} />\r\n                <span>Làm bài</span>\r\n            </button>\r\n        </LoadingText>\r\n\r\n    );\r\n};\r\n\r\nconst InfoRow = ({ icon: Icon, label, value, w = \"w-48\" }) => {\r\n    const { loading } = useSelector((state) => state.states);\r\n    return (\r\n        <div className=\"flex items-center justify-between gap-2 text-sm text-gray-800\">\r\n            <div className=\"flex items-center gap-2\">\r\n                <Icon size={16} className=\"text-sky-600\" />\r\n                <span className=\"font-medium text-gray-800\">{label}:</span>\r\n            </div>\r\n            <LoadingText loading={true} w={w}>\r\n                <span>{value}</span>\r\n            </LoadingText>\r\n        </div>\r\n    );\r\n};\r\n\r\nconst InfoExam = () => {\r\n    const { exam, loading } = useSelector((state) => state.examDetail);\r\n    const { codes } = useSelector((state) => state.codes);\r\n    return (\r\n        <div className=\"flex flex-col border border-gray-300 rounded-md\">\r\n            {/** Header **/}\r\n            <div className=\"flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]\">\r\n                <LoadingText loading={true} w=\"w-48\">\r\n                    <p className=\"font-Inter text-sm font-semibold\">{exam?.name}</p>\r\n                </LoadingText>\r\n                <div className=\"flex items-center gap-2 text-gray-500\">\r\n                    <RefreshCcw size={16} className=\"flex-shrink-0\" />\r\n                    <LoadingText loading={true} w=\"w-40\" color=\"bg-gray-200\">\r\n                        <p className=\"text-xs\"><span className=\"hidden sm:inline\">Cập nhật:</span> {formatDate(exam?.updatedAt)}</p>\r\n                    </LoadingText>\r\n                </div>\r\n            </div>\r\n            <div className=\"flex flex-row gap-3 p-4 bg-white \">\r\n                <div className=\"w-full flex flex-col sm:flex-row gap-4 items-center\">\r\n                    {/* Bên trái: ảnh hoặc QR */}\r\n                    <div className=\"w-full sm:w-1/3 flex justify-center items-center\">\r\n                        {exam?.imageUrl ? (\r\n                            <img\r\n                                src={exam.imageUrl}\r\n                                alt=\"Exam\"\r\n                                className=\"w-full max-w-[200px] rounded-md shadow border\"\r\n                            />\r\n                        ) : (\r\n                            <div className=\"bg-white p-2 rounded-md border\">\r\n                                <QRCodeComponent\r\n                                    url={`https://toanthaybee.edu.vn/practice/exam/${exam?.id}`} // hoặc `https://toanthaybee.edu.vn/practice/exam/${exam.id}` hoặc `https://toanthaybee.edu.vn/practice/exam/${exam.id}`\r\n                                    size={128}\r\n                                />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Bên phải: thông tin */}\r\n                    <div className=\"w-full flex flex-col sm:gap-4 gap-2\">\r\n                        <InfoRow\r\n                            icon={BookOpen}\r\n                            label=\"Loại đề\"\r\n                            value={codes?.[\"exam type\"]?.find(c => c.code === exam?.typeOfExam)?.description || exam?.typeOfExam || \"Không rõ\"}\r\n                            w=\"w-56\"\r\n                        />\r\n                        <InfoRow\r\n                            icon={GraduationCap}\r\n                            label=\"Khối\"\r\n                            value={exam?.class || \"Không rõ\"}\r\n                            w=\"w-48\"\r\n                        />\r\n                        <InfoRow\r\n                            icon={Calendar}\r\n                            label=\"Năm\"\r\n                            value={exam?.year || \"Không rõ\"}\r\n                            w=\"w-40\"\r\n                        />\r\n                        <InfoRow\r\n                            icon={Clock}\r\n                            label=\"Thời gian\"\r\n                            value={exam?.testDuration ? `${exam.testDuration} phút` : \"Vô thời hạn\"}\r\n                            w=\"w-56\"\r\n                        />\r\n                        <InfoRow\r\n                            icon={ListOrdered}\r\n                            label=\"Chương\"\r\n                            value={codes?.[\"chapter\"]?.find(c => c.code === exam?.chapter)?.description || exam?.chapter || \"Không rõ\"}\r\n                            w=\"w-56\"\r\n                        />\r\n                        <InfoRow\r\n                            icon={BarChart2}\r\n                            label=\"Tỉ lệ đạt\"\r\n                            value={exam?.passRate ? `${exam.passRate}%` : \"Không có\"}\r\n                            w=\"w-48\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Mô tả */}\r\n                {exam?.description && (\r\n                    <div className=\"flex items-start gap-2 text-sm text-gray-600\">\r\n                        <StickyNote size={16} className=\"text-sky-600 mt-0.5\" />\r\n                        <span className=\"font-medium text-gray-800\">Mô tả:</span>\r\n                        <span className=\"leading-relaxed\">{exam.description}</span>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nconst SolutionVideo = () => {\r\n    const { exam, loading } = useSelector((state) => state.examDetail);\r\n\r\n    return (\r\n        <div className=\"flex flex-col border border-gray-300 rounded-md\">\r\n            <div className=\"flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]\">\r\n                <p className=\"text-sm font-Inter font-semibold\">Video Chữa bài</p>\r\n                <LoadingText loading={true} w=\"w-40\">\r\n                    {exam?.isDone ? (\r\n                        <button\r\n                            onClick={() => window.open(exam?.solutionUrl, \"_blank\")}\r\n                            className=\"text-xs flex items-center gap-2 px-2 py-1 rounded-md transition-all text-gray-700 hover:bg-gray-200\"\r\n                        >\r\n                            <ExternalLink size={16} />\r\n                            <span>Mở trong tab mới</span>\r\n                        </button>\r\n                    ) : (\r\n                        <button\r\n                            className=\"text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 cursor-not-allowed\"\r\n                        >\r\n                            <span>Chưa làm</span>\r\n                        </button>\r\n                    )}\r\n                </LoadingText>\r\n\r\n            </div>\r\n            <LoadingText loading={false} w=\"w-full\" h=\"h-40\">\r\n                {exam?.isDone && exam?.solutionUrl && (\r\n                    <div className=\"p-4\">\r\n                        <YouTubePlayer url={exam?.solutionUrl} />\r\n                    </div>\r\n                )}\r\n                {exam?.isDone && !exam?.solutionUrl && (\r\n                    <div className=\"p-4\">\r\n                        <p className=\"text-sm text-gray-600\">Chưa có video hướng dẫn</p>\r\n                    </div>\r\n                )}\r\n\r\n                {!exam?.isDone && (\r\n                    <div className=\"p-4\">\r\n                        <p className=\"text-sm text-gray-600\">Chỉ có thể xem sau khi làm bài</p>\r\n                    </div>\r\n                )}\r\n            </LoadingText>\r\n\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nconst RelatedExamCard = ({ exam, onClick, compact = false }) => {\r\n    return (\r\n        <div\r\n            onClick={onClick}\r\n            className=\"bg-white rounded-md border border-gray-200 cursor-pointer p-3 sm:p-4 flex flex-col gap-2\"\r\n        >\r\n            <div className=\"flex justify-between items-center\">\r\n                <p className=\"text-sm font-semibold text-gray-800 line-clamp-2\">\r\n                    {exam.name}\r\n                </p>\r\n                <button\r\n                    onClick={onClick}\r\n                    className=\"whitespace-nowrap text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200\"\r\n                >\r\n                    <span>Xem đề</span>\r\n                </button>\r\n            </div>\r\n            <div className=\"flex items-center gap-2 text-xs text-gray-500\">\r\n                <Clock size={14} />\r\n                <span>{exam.testDuration ? `${exam.testDuration} phút` : \"Vô thời hạn\"}</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2 text-xs text-gray-500\">\r\n                <Calendar size={14} />\r\n                <span>Năm: {exam.year || \"Không rõ\"}</span>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nconst RightPanel = () => {\r\n    const { relatedExams } = useSelector((state) => state.examDetail);\r\n    const navigate = useNavigate();\r\n    return (\r\n        <div className=\"sm:w-1/4 flex flex-col gap-4\">\r\n            <p className=\"text-lg font-semibold\">Bài thi khác</p>\r\n            <div className=\"flex flex-col gap-2\">\r\n                {relatedExams.map((exam) => (\r\n                    <RelatedExamCard\r\n                        key={exam.id}\r\n                        exam={exam}\r\n                        onClick={() => navigate(`/practice/exam/${exam.id}`)}\r\n                        compact={true}\r\n                    />\r\n                ))}\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nconst ExamDetail = () => {\r\n    const { examId } = useParams();\r\n    const { exam, loading } = useSelector((state) => state.examDetail);\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const shareLink = `${window.location.origin}/practice/exam/${examId}`;\r\n\r\n    const { comments, pagination } = useSelector((state) => state.comments);\r\n    const { page } = pagination;\r\n\r\n    const handleCopyLink = () => {\r\n        navigator.clipboard.writeText(shareLink);\r\n    };\r\n\r\n    const handleSaveExam = () => {\r\n        dispatch(saveExamForUser({ examId }));\r\n    };\r\n\r\n    const handleDoExam = () => {\r\n        navigate(`/practice/exam/${examId}/do`);\r\n    };\r\n\r\n\r\n\r\n    const handleSendComment = (content) => {\r\n        dispatch(postComment({ examId, content }));\r\n    };\r\n\r\n    const handleUpdateComment = (commentId, content) => {\r\n        dispatch(putComment({ commentId, content }));\r\n    };\r\n\r\n    const handleDeleteComment = (commentId) => {\r\n        dispatch(deleteComment(commentId));\r\n    };\r\n\r\n    const handleReplyComment = (content, parentCommentId) => {\r\n        dispatch(postComment({ examId, content, parentCommentId }));\r\n    };\r\n\r\n    useEffect(() => {\r\n        dispatch(fetchPublicExamById(examId));\r\n        dispatch(fetchRelatedExamsIfNeeded(examId));\r\n        dispatch(fetchCodesByType([\"chapter\", \"exam type\", \"user type\"]));\r\n    }, [dispatch, examId]);\r\n\r\n    useEffect(() => {\r\n        dispatch(fetchCommentsByExamId({ examId, page }));\r\n    }, [dispatch, examId, page]);\r\n\r\n    return (\r\n        <UserLayout>\r\n            <div className=\"container flex flex-col mb-9\">\r\n                <div className=\"w-full flex flex-col md:flex-row gap-2 justify-between items-center\">\r\n                    <div className=\"items-start justify-start flex flex-wrap gap-2\">\r\n                        <FileText className=\"text-sky-600\" />\r\n                        <LoadingText loading={true} w=\"w-48\">\r\n                            <p className=\"text-lg font-semibold\">{exam?.name}</p>\r\n                            <p className={`py-0.5 px-1.5 border rounded-full text-xs text-gray-500 flex-shrink-0 ${exam?.acceptDoExam ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'}`}>\r\n                                {exam?.acceptDoExam ? 'Hoạt động' : 'Kết thúc'}\r\n                            </p>\r\n                            <p className={`py-0.5 px-1.5 border rounded-full text-xs text-gray-500 flex-shrink-0 ${exam?.isDone ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'}`}>\r\n                                {exam?.isDone ? 'Đã làm' : 'Chưa làm'}\r\n                            </p>\r\n                        </LoadingText>\r\n                    </div>\r\n                    <LoadingText loading={true} w=\"w-80\">\r\n                        <div className=\"flex md:flex-row flex-col md:w-fit w-full gap-2\">\r\n                            <button\r\n                                // onClick={onClick}\r\n                                className={`flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 py-[3px] px-3 rounded-md border transition-all\r\n                                ${exam?.isSave\r\n                                        ? \"border-blue-500 bg-blue-50 text-blue-600 hover:bg-blue-100\"\r\n                                        : \"border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200\"\r\n                                    }`}\r\n                            >\r\n                                <Pin size={16} />\r\n                                <span>{exam?.isSave ? \"Đã lưu\" : \"Lưu đề\"}</span>\r\n                            </button>\r\n                            <button\r\n                                // onClick={onClick}\r\n                                className={`flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200`}\r\n                            >\r\n                                <Eye size={16} />\r\n                                <span>Số lượt làm bài: {exam?.studentCount}</span>\r\n                            </button>\r\n                            <button\r\n                                // onClick={onClick}\r\n                                className={`flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200`}\r\n                            >\r\n                                <StarIcon size={16} />\r\n                                <span>Star: {exam?.studentCount}</span>\r\n                            </button>\r\n                        </div>\r\n                    </LoadingText>\r\n\r\n                </div>\r\n                <hr className=\"my-6 border-gray-300\" />\r\n                <div className=\"flex flex-col md:flex-row gap-4\">\r\n                    <div className=\"flex-1 flex flex-col gap-4\">\r\n                        <div className=\"flex flex-row justify-between items-center gap-2\">\r\n                            <ActionButtons />\r\n                            <DoExamButton\r\n                                onClick={handleDoExam}\r\n                                disabled={!exam?.acceptDoExam}\r\n                            />\r\n                        </div>\r\n                        <InfoExam />\r\n                        <SolutionVideo />\r\n                        <CommentSection\r\n                            comments={comments}\r\n                            onSubmit={handleSendComment}\r\n                            onUpdate={handleUpdateComment}\r\n                            onDelete={handleDeleteComment}\r\n                            onReply={handleReplyComment}\r\n                        />\r\n                    </div>\r\n                    <RightPanel />\r\n                </div>\r\n            </div>\r\n        </UserLayout>\r\n    )\r\n}\r\n\r\n// Component hiển thị đề thi liên quan\r\n\r\n\r\nexport default ExamDetail;\r\n"], "mappings": ";;;;;;;;AAAA,OAAOA,UAAU,MAAM,6BAA6B;AACpD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,mBAAmB,EAAEC,eAAe,EAAEC,yBAAyB,QAAQ,wCAAwC;AACxH,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACnD,SACIC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,MAAM,IAAIC,UAAU,EACpBC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,YAAY,EACZC,IAAI,EACJC,KAAK,QACF,cAAc;AACrB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wCAAwC;AAChE,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,SAASC,qBAAqB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,aAAa,EAAEC,cAAc,QAAQ,yCAAyC;AACvI,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,WAAW,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,YAAY,GAAGC,IAAA,IAAkE;EAAA,IAAjE;IAAEC,IAAI,EAAEC,IAAI;IAAEC,KAAK;IAAEC,UAAU;IAAEC,QAAQ,GAAG,KAAK;IAAEC;EAAQ,CAAC,GAAAN,IAAA;EAC9E,oBACIF,OAAA;IACIQ,OAAO,EAAEA,OAAQ;IACjBC,SAAS,6GAAAC,MAAA,CACHH,QAAQ,GACJ,+CAA+C,GAC/C,6CAA6C,mBACrD;IAAAI,QAAA,gBAEFX,OAAA,CAACI,IAAI;MAACK,SAAS,EAAC;IAA6B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChDf,OAAA;MAAMS,SAAS,EAAC,kBAAkB;MAAAE,QAAA,EAAEN;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACjDf,OAAA;MAAMS,SAAS,EAAC,WAAW;MAAAE,QAAA,EAAEL;IAAU;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3C,CAAC;AAEjB,CAAC;AAACC,EAAA,GAfIf,YAAY;AAiBlB,MAAMgB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAG9D,WAAW,CAAC,CAAC;EAC9B,MAAM+D,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkC,IAAI;IAAEC;EAAK,CAAC,GAAGlE,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAACC,UAAU,CAAC;EAE/D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC7B;IACA;IACA;IACA,IAAKH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,MAAM,IAAIJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,gBAAgB,IAAM,EAACL,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,YAAY,KAAIN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,gBAAiB,EAAE;MAC7FP,QAAQ,mBAAAV,MAAA,CAAmBY,IAAI,CAACO,EAAE,aAAU,CAAC;IACjD;EAEJ,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACjC;IACA;IACA;IACA,IAAKR,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,MAAM,IAAIJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,gBAAgB,IAAM,EAACL,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,YAAY,KAAIN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,gBAAiB,EAAE;MAC7FP,QAAQ,mBAAAV,MAAA,CAAmBY,IAAI,CAACO,EAAE,aAAU,CAAC;IACjD;EACJ,CAAC;EACD,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACT,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,MAAM,KAAI,EAACJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,gBAAgB,GAAE;IAC9CP,QAAQ,mBAAAV,MAAA,CAAmBY,IAAI,CAACO,EAAE,aAAU,CAAC;EACjD,CAAC;EACD,oBACI7B,OAAA;IAAKS,SAAS,EAAC,sBAAsB;IAAAE,QAAA,gBACjCX,OAAA,CAACC,YAAY;MACTE,IAAI,EAAEpC,IAAK;MACXsC,KAAK,EAAC,4BAAoB;MAC1BC,UAAU,EAAC,eAAU;MACrBC,QAAQ,EAAEc,IAAI,KAAK,QAAS;MAC5Bb,OAAO,EAAEA,CAAA,KAAMW,QAAQ,CAAC/B,OAAO,CAAC,QAAQ,CAAC;IAAE;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eACFf,OAAA,CAACC,YAAY;MACTE,IAAI,EAAEhC,KAAM;MACZkC,KAAK,EAAC,8BAAe;MACrBC,UAAU,EAAC,oBAAU;MACrBC,QAAQ,EAAEc,IAAI,KAAK;MACnB;MAAA;MACAb,OAAO,EAAEiB;IAAmB;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eACFf,OAAA,CAACC,YAAY;MACTE,IAAI,EAAEzB,GAAI;MACV2B,KAAK,EAAC,sBAAY;MAClBC,UAAU,EAAC,kBAAQ;MACnBC,QAAQ,EAAEc,IAAI,KAAK;MACnB;MAAA;MACAb,OAAO,EAAEsB;IAAuB;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eACFf,OAAA,CAACC,YAAY;MACTE,IAAI,EAAE7B,OAAQ;MACd+B,KAAK,EAAC,iCAAiB;MACvBC,UAAU,EAAC,mBAAS;MACpBC,QAAQ,EAAEc,IAAI,KAAK;MACnB;MAAA;MACAb,OAAO,EAAEuB;IAAmB;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACG,EAAA,CA9DID,aAAa;EAAA,QACE5D,WAAW,EACX8B,WAAW,EACL/B,WAAW;AAAA;AAAA4E,GAAA,GAHhCf,aAAa;AAgEnB,MAAMgB,YAAY,GAAGC,KAAA,IAAmC;EAAAC,GAAA;EAAA,IAAlC;IAAE3B,OAAO;IAAE4B,QAAQ,GAAG;EAAM,CAAC,GAAAF,KAAA;EAC/C,MAAM;IAAEG;EAAQ,CAAC,GAAGjF,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAACe,MAAM,CAAC;EACxD,oBACItC,OAAA,CAACF,WAAW;IAACuC,OAAO,EAAE,IAAK;IAACE,CAAC,EAAC,MAAM;IAACC,CAAC,EAAC,UAAU;IAAA7B,QAAA,eAC7CX,OAAA;MACIQ,OAAO,EAAEA,OAAQ;MACjB4B,QAAQ,EAAEA,QAAS;MACnB3B,SAAS,wHAAAC,MAAA,CACP0B,QAAQ,GACA,8CAA8C,GAC9C,4CAA4C,mBACxD;MAAAzB,QAAA,gBAEEX,OAAA,CAACrB,IAAI;QAAC8D,IAAI,EAAE;MAAG;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClBf,OAAA;QAAAW,QAAA,EAAM;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAGtB,CAAC;AAACoB,GAAA,CAnBIF,YAAY;EAAA,QACM7E,WAAW;AAAA;AAAAsF,GAAA,GAD7BT,YAAY;AAqBlB,MAAMU,OAAO,GAAGC,KAAA,IAA8C;EAAAC,GAAA;EAAA,IAA7C;IAAE1C,IAAI,EAAEC,IAAI;IAAE0C,KAAK;IAAEC,KAAK;IAAER,CAAC,GAAG;EAAO,CAAC,GAAAK,KAAA;EACrD,MAAM;IAAEP;EAAQ,CAAC,GAAGjF,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAACe,MAAM,CAAC;EACxD,oBACItC,OAAA;IAAKS,SAAS,EAAC,+DAA+D;IAAAE,QAAA,gBAC1EX,OAAA;MAAKS,SAAS,EAAC,yBAAyB;MAAAE,QAAA,gBACpCX,OAAA,CAACI,IAAI;QAACqC,IAAI,EAAE,EAAG;QAAChC,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cf,OAAA;QAAMS,SAAS,EAAC,2BAA2B;QAAAE,QAAA,GAAEmC,KAAK,EAAC,GAAC;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eACNf,OAAA,CAACF,WAAW;MAACuC,OAAO,EAAE,IAAK;MAACE,CAAC,EAAEA,CAAE;MAAA5B,QAAA,eAC7BX,OAAA;QAAAW,QAAA,EAAOoC;MAAK;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEd,CAAC;AAAC8B,GAAA,CAbIF,OAAO;EAAA,QACWvF,WAAW;AAAA;AAAA4F,GAAA,GAD7BL,OAAO;AAeb,MAAMM,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,cAAA,EAAAC,mBAAA;EACnB,MAAM;IAAEhC,IAAI;IAAEe;EAAQ,CAAC,GAAGjF,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAACC,UAAU,CAAC;EAClE,MAAM;IAAE+B;EAAM,CAAC,GAAGnG,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAACgC,KAAK,CAAC;EACrD,oBACIvD,OAAA;IAAKS,SAAS,EAAC,iDAAiD;IAAAE,QAAA,gBAE5DX,OAAA;MAAKS,SAAS,EAAC,gEAAgE;MAAAE,QAAA,gBAC3EX,OAAA,CAACF,WAAW;QAACuC,OAAO,EAAE,IAAK;QAACE,CAAC,EAAC,MAAM;QAAA5B,QAAA,eAChCX,OAAA;UAAGS,SAAS,EAAC,kCAAkC;UAAAE,QAAA,EAAEW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC;QAAI;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACdf,OAAA;QAAKS,SAAS,EAAC,uCAAuC;QAAAE,QAAA,gBAClDX,OAAA,CAAChC,UAAU;UAACyE,IAAI,EAAE,EAAG;UAAChC,SAAS,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDf,OAAA,CAACF,WAAW;UAACuC,OAAO,EAAE,IAAK;UAACE,CAAC,EAAC,MAAM;UAACkB,KAAK,EAAC,aAAa;UAAA9C,QAAA,eACpDX,OAAA;YAAGS,SAAS,EAAC,SAAS;YAAAE,QAAA,gBAACX,OAAA;cAAMS,SAAS,EAAC,kBAAkB;cAAAE,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC,EAAC1B,UAAU,CAACiC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,SAAS,CAAC;UAAA;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNf,OAAA;MAAKS,SAAS,EAAC,mCAAmC;MAAAE,QAAA,gBAC9CX,OAAA;QAAKS,SAAS,EAAC,qDAAqD;QAAAE,QAAA,gBAEhEX,OAAA;UAAKS,SAAS,EAAC,kDAAkD;UAAAE,QAAA,EAC5DW,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqC,QAAQ,gBACX3D,OAAA;YACI4D,GAAG,EAAEtC,IAAI,CAACqC,QAAS;YACnBE,GAAG,EAAC,MAAM;YACVpD,SAAS,EAAC;UAA+C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEFf,OAAA;YAAKS,SAAS,EAAC,gCAAgC;YAAAE,QAAA,eAC3CX,OAAA,CAACV,eAAe;cACZwE,GAAG,8CAAApD,MAAA,CAA8CY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,EAAE,CAAG,CAAC;cAAA;cAC7DY,IAAI,EAAE;YAAI;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGNf,OAAA;UAAKS,SAAS,EAAC,qCAAqC;UAAAE,QAAA,gBAChDX,OAAA,CAAC2C,OAAO;YACJxC,IAAI,EAAElC,QAAS;YACf6E,KAAK,EAAC,wBAAS;YACfC,KAAK,EAAE,CAAAQ,KAAK,aAALA,KAAK,wBAAAJ,eAAA,GAALI,KAAK,CAAG,WAAW,CAAC,cAAAJ,eAAA,wBAAAC,oBAAA,GAApBD,eAAA,CAAsBY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,MAAK3C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,UAAU,EAAC,cAAAd,oBAAA,uBAA5DA,oBAAA,CAA8De,WAAW,MAAI7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,UAAU,KAAI,UAAW;YACnH3B,CAAC,EAAC;UAAM;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFf,OAAA,CAAC2C,OAAO;YACJxC,IAAI,EAAEtB,aAAc;YACpBiE,KAAK,EAAC,WAAM;YACZC,KAAK,EAAE,CAAAzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,KAAK,KAAI,UAAW;YACjC7B,CAAC,EAAC;UAAM;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFf,OAAA,CAAC2C,OAAO;YACJxC,IAAI,EAAEjC,QAAS;YACf4E,KAAK,EAAC,UAAK;YACXC,KAAK,EAAE,CAAAzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,IAAI,KAAI,UAAW;YAChC9B,CAAC,EAAC;UAAM;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFf,OAAA,CAAC2C,OAAO;YACJxC,IAAI,EAAEvB,KAAM;YACZkE,KAAK,EAAC,gBAAW;YACjBC,KAAK,EAAEzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgD,YAAY,MAAA5D,MAAA,CAAMY,IAAI,CAACgD,YAAY,gBAAU,aAAc;YACxE/B,CAAC,EAAC;UAAM;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFf,OAAA,CAAC2C,OAAO;YACJxC,IAAI,EAAErB,WAAY;YAClBgE,KAAK,EAAC,kBAAQ;YACdC,KAAK,EAAE,CAAAQ,KAAK,aAALA,KAAK,wBAAAF,cAAA,GAALE,KAAK,CAAG,SAAS,CAAC,cAAAF,cAAA,wBAAAC,mBAAA,GAAlBD,cAAA,CAAoBU,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,MAAK3C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,OAAO,EAAC,cAAAjB,mBAAA,uBAAvDA,mBAAA,CAAyDa,WAAW,MAAI7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,OAAO,KAAI,UAAW;YAC3GhC,CAAC,EAAC;UAAM;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFf,OAAA,CAAC2C,OAAO;YACJxC,IAAI,EAAE9B,SAAU;YAChByE,KAAK,EAAC,+BAAW;YACjBC,KAAK,EAAEzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkD,QAAQ,MAAA9D,MAAA,CAAMY,IAAI,CAACkD,QAAQ,SAAM,UAAW;YACzDjC,CAAC,EAAC;UAAM;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL,CAAAO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,WAAW,kBACdnE,OAAA;QAAKS,SAAS,EAAC,8CAA8C;QAAAE,QAAA,gBACzDX,OAAA,CAACjB,UAAU;UAAC0D,IAAI,EAAE,EAAG;UAAChC,SAAS,EAAC;QAAqB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDf,OAAA;UAAMS,SAAS,EAAC,2BAA2B;UAAAE,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDf,OAAA;UAAMS,SAAS,EAAC,iBAAiB;UAAAE,QAAA,EAAEW,IAAI,CAAC6C;QAAW;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEL,CAAC;AAEd,CAAC;AAAAmC,GAAA,CA1FKD,QAAQ;EAAA,QACgB7F,WAAW,EACnBA,WAAW;AAAA;AAAAqH,GAAA,GAF3BxB,QAAQ;AA4Fd,MAAMyB,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAM;IAAErD,IAAI;IAAEe;EAAQ,CAAC,GAAGjF,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAACC,UAAU,CAAC;EAElE,oBACIxB,OAAA;IAAKS,SAAS,EAAC,iDAAiD;IAAAE,QAAA,gBAC5DX,OAAA;MAAKS,SAAS,EAAC,gEAAgE;MAAAE,QAAA,gBAC3EX,OAAA;QAAGS,SAAS,EAAC,kCAAkC;QAAAE,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClEf,OAAA,CAACF,WAAW;QAACuC,OAAO,EAAE,IAAK;QAACE,CAAC,EAAC,MAAM;QAAA5B,QAAA,EAC/BW,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,MAAM,gBACT1B,OAAA;UACIQ,OAAO,EAAEA,CAAA,KAAMoE,MAAM,CAACC,IAAI,CAACvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,WAAW,EAAE,QAAQ,CAAE;UACxDrE,SAAS,EAAC,qGAAqG;UAAAE,QAAA,gBAE/GX,OAAA,CAAChB,YAAY;YAACyD,IAAI,EAAE;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1Bf,OAAA;YAAAW,QAAA,EAAM;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,gBAETf,OAAA;UACIS,SAAS,EAAC,0IAA0I;UAAAE,QAAA,eAEpJX,OAAA;YAAAW,QAAA,EAAM;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEb,CAAC,eACNf,OAAA,CAACF,WAAW;MAACuC,OAAO,EAAE,KAAM;MAACE,CAAC,EAAC,QAAQ;MAACC,CAAC,EAAC,MAAM;MAAA7B,QAAA,GAC3C,CAAAW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,MAAM,MAAIJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,WAAW,kBAC9B9E,OAAA;QAAKS,SAAS,EAAC,KAAK;QAAAE,QAAA,eAChBX,OAAA,CAACT,aAAa;UAACuE,GAAG,EAAExC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD;QAAY;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACR,EACA,CAAAO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,MAAM,KAAI,EAACJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwD,WAAW,kBAC/B9E,OAAA;QAAKS,SAAS,EAAC,KAAK;QAAAE,QAAA,eAChBX,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAE,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CACR,EAEA,EAACO,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,MAAM,kBACV1B,OAAA;QAAKS,SAAS,EAAC,KAAK;QAAAE,QAAA,eAChBX,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAE,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGb,CAAC;AAEd,CAAC;AAAA4D,GAAA,CAhDKD,aAAa;EAAA,QACWtH,WAAW;AAAA;AAAA2H,GAAA,GADnCL,aAAa;AAkDnB,MAAMM,eAAe,GAAGC,KAAA,IAAwC;EAAA,IAAvC;IAAE3D,IAAI;IAAEd,OAAO;IAAE0E,OAAO,GAAG;EAAM,CAAC,GAAAD,KAAA;EACvD,oBACIjF,OAAA;IACIQ,OAAO,EAAEA,OAAQ;IACjBC,SAAS,EAAC,0FAA0F;IAAAE,QAAA,gBAEpGX,OAAA;MAAKS,SAAS,EAAC,mCAAmC;MAAAE,QAAA,gBAC9CX,OAAA;QAAGS,SAAS,EAAC,kDAAkD;QAAAE,QAAA,EAC1DW,IAAI,CAACkC;MAAI;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACJf,OAAA;QACIQ,OAAO,EAAEA,OAAQ;QACjBC,SAAS,EAAC,2JAA2J;QAAAE,QAAA,eAErKX,OAAA;UAAAW,QAAA,EAAM;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACNf,OAAA;MAAKS,SAAS,EAAC,+CAA+C;MAAAE,QAAA,gBAC1DX,OAAA,CAACpB,KAAK;QAAC6D,IAAI,EAAE;MAAG;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBf,OAAA;QAAAW,QAAA,EAAOW,IAAI,CAACgD,YAAY,MAAA5D,MAAA,CAAMY,IAAI,CAACgD,YAAY,gBAAU;MAAa;QAAA1D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CAAC,eACNf,OAAA;MAAKS,SAAS,EAAC,+CAA+C;MAAAE,QAAA,gBAC1DX,OAAA,CAAC9B,QAAQ;QAACuE,IAAI,EAAE;MAAG;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtBf,OAAA;QAAAW,QAAA,GAAM,YAAK,EAACW,IAAI,CAAC+C,IAAI,IAAI,UAAU;MAAA;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACoE,GAAA,GA3BIH,eAAe;AA6BrB,MAAMI,UAAU,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACrB,MAAM;IAAEC;EAAa,CAAC,GAAGlI,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAACC,UAAU,CAAC;EACjE,MAAMJ,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,oBACIa,OAAA;IAAKS,SAAS,EAAC,8BAA8B;IAAAE,QAAA,gBACzCX,OAAA;MAAGS,SAAS,EAAC,uBAAuB;MAAAE,QAAA,EAAC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACrDf,OAAA;MAAKS,SAAS,EAAC,qBAAqB;MAAAE,QAAA,EAC/B2E,YAAY,CAACC,GAAG,CAAEjE,IAAI,iBACnBtB,OAAA,CAACgF,eAAe;QAEZ1D,IAAI,EAAEA,IAAK;QACXd,OAAO,EAAEA,CAAA,KAAMY,QAAQ,mBAAAV,MAAA,CAAmBY,IAAI,CAACO,EAAE,CAAE,CAAE;QACrDqD,OAAO,EAAE;MAAK,GAHT5D,IAAI,CAACO,EAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIf,CACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAsE,GAAA,CAlBKD,UAAU;EAAA,QACahI,WAAW,EACnB+B,WAAW;AAAA;AAAAqG,GAAA,GAF1BJ,UAAU;AAoBhB,MAAMK,UAAU,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACrB,MAAM;IAAEC;EAAO,CAAC,GAAGrI,SAAS,CAAC,CAAC;EAC9B,MAAM;IAAEgE,IAAI;IAAEe;EAAQ,CAAC,GAAGjF,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAACC,UAAU,CAAC;EAClE,MAAML,QAAQ,GAAG9D,WAAW,CAAC,CAAC;EAC9B,MAAM+D,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAMyG,SAAS,MAAAlF,MAAA,CAAMkE,MAAM,CAACiB,QAAQ,CAACC,MAAM,qBAAApF,MAAA,CAAkBiF,MAAM,CAAE;EAErE,MAAM;IAAEI,QAAQ;IAAEC;EAAW,CAAC,GAAG5I,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAACwE,QAAQ,CAAC;EACvE,MAAM;IAAEE;EAAK,CAAC,GAAGD,UAAU;EAE3B,MAAME,cAAc,GAAGA,CAAA,KAAM;IACzBC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACT,SAAS,CAAC;EAC5C,CAAC;EAED,MAAMU,cAAc,GAAGA,CAAA,KAAM;IACzBnF,QAAQ,CAAC3D,eAAe,CAAC;MAAEmI;IAAO,CAAC,CAAC,CAAC;EACzC,CAAC;EAED,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACvBnF,QAAQ,mBAAAV,MAAA,CAAmBiF,MAAM,QAAK,CAAC;EAC3C,CAAC;EAID,MAAMa,iBAAiB,GAAIC,OAAO,IAAK;IACnCtF,QAAQ,CAAC1B,WAAW,CAAC;MAAEkG,MAAM;MAAEc;IAAQ,CAAC,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAACC,SAAS,EAAEF,OAAO,KAAK;IAChDtF,QAAQ,CAACzB,UAAU,CAAC;MAAEiH,SAAS;MAAEF;IAAQ,CAAC,CAAC,CAAC;EAChD,CAAC;EAED,MAAMG,mBAAmB,GAAID,SAAS,IAAK;IACvCxF,QAAQ,CAACxB,aAAa,CAACgH,SAAS,CAAC,CAAC;EACtC,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAACJ,OAAO,EAAEK,eAAe,KAAK;IACrD3F,QAAQ,CAAC1B,WAAW,CAAC;MAAEkG,MAAM;MAAEc,OAAO;MAAEK;IAAgB,CAAC,CAAC,CAAC;EAC/D,CAAC;EAEDnJ,SAAS,CAAC,MAAM;IACZwD,QAAQ,CAAC5D,mBAAmB,CAACoI,MAAM,CAAC,CAAC;IACrCxE,QAAQ,CAAC1D,yBAAyB,CAACkI,MAAM,CAAC,CAAC;IAC3CxE,QAAQ,CAACzD,gBAAgB,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;EACrE,CAAC,EAAE,CAACyD,QAAQ,EAAEwE,MAAM,CAAC,CAAC;EAEtBhI,SAAS,CAAC,MAAM;IACZwD,QAAQ,CAAC3B,qBAAqB,CAAC;MAAEmG,MAAM;MAAEM;IAAK,CAAC,CAAC,CAAC;EACrD,CAAC,EAAE,CAAC9E,QAAQ,EAAEwE,MAAM,EAAEM,IAAI,CAAC,CAAC;EAE5B,oBACIjG,OAAA,CAAC7C,UAAU;IAAAwD,QAAA,eACPX,OAAA;MAAKS,SAAS,EAAC,8BAA8B;MAAAE,QAAA,gBACzCX,OAAA;QAAKS,SAAS,EAAC,qEAAqE;QAAAE,QAAA,gBAChFX,OAAA;UAAKS,SAAS,EAAC,gDAAgD;UAAAE,QAAA,gBAC3DX,OAAA,CAAC5B,QAAQ;YAACqC,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCf,OAAA,CAACF,WAAW;YAACuC,OAAO,EAAE,IAAK;YAACE,CAAC,EAAC,MAAM;YAAA5B,QAAA,gBAChCX,OAAA;cAAGS,SAAS,EAAC,uBAAuB;cAAAE,QAAA,EAAEW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC;YAAI;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDf,OAAA;cAAGS,SAAS,2EAAAC,MAAA,CAA2EY,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,YAAY,GAAG,iCAAiC,GAAG,mCAAmC,CAAG;cAAAjB,QAAA,EACjLW,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,YAAY,GAAG,WAAW,GAAG;YAAU;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACJf,OAAA;cAAGS,SAAS,2EAAAC,MAAA,CAA2EY,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,MAAM,GAAG,iCAAiC,GAAG,mCAAmC,CAAG;cAAAf,QAAA,EAC3KW,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,MAAM,GAAG,QAAQ,GAAG;YAAU;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNf,OAAA,CAACF,WAAW;UAACuC,OAAO,EAAE,IAAK;UAACE,CAAC,EAAC,MAAM;UAAA5B,QAAA,eAChCX,OAAA;YAAKS,SAAS,EAAC,iDAAiD;YAAAE,QAAA,gBAC5DX,OAAA;cACI;cACAS,SAAS,mJAAAC,MAAA,CACPY,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyF,MAAM,GACJ,4DAA4D,GAC5D,8DAA8D,CACjE;cAAApG,QAAA,gBAEPX,OAAA,CAACvB,GAAG;gBAACgE,IAAI,EAAE;cAAG;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjBf,OAAA;gBAAAW,QAAA,EAAOW,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyF,MAAM,GAAG,QAAQ,GAAG;cAAQ;gBAAAnG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACTf,OAAA;cACI;cACAS,SAAS,yKAA0K;cAAAE,QAAA,gBAEnLX,OAAA,CAACtB,GAAG;gBAAC+D,IAAI,EAAE;cAAG;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjBf,OAAA;gBAAAW,QAAA,GAAM,wCAAiB,EAACW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,YAAY;cAAA;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACTf,OAAA;cACI;cACAS,SAAS,yKAA0K;cAAAE,QAAA,gBAEnLX,OAAA,CAAClC,QAAQ;gBAAC2E,IAAI,EAAE;cAAG;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtBf,OAAA;gBAAAW,QAAA,GAAM,QAAM,EAACW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,YAAY;cAAA;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEb,CAAC,eACNf,OAAA;QAAIS,SAAS,EAAC;MAAsB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCf,OAAA;QAAKS,SAAS,EAAC,iCAAiC;QAAAE,QAAA,gBAC5CX,OAAA;UAAKS,SAAS,EAAC,4BAA4B;UAAAE,QAAA,gBACvCX,OAAA;YAAKS,SAAS,EAAC,kDAAkD;YAAAE,QAAA,gBAC7DX,OAAA,CAACiB,aAAa;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjBf,OAAA,CAACiC,YAAY;cACTzB,OAAO,EAAE+F,YAAa;cACtBnE,QAAQ,EAAE,EAACd,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,YAAY;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNf,OAAA,CAACiD,QAAQ;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACZf,OAAA,CAAC0E,aAAa;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjBf,OAAA,CAACH,cAAc;YACXkG,QAAQ,EAAEA,QAAS;YACnBkB,QAAQ,EAAET,iBAAkB;YAC5BU,QAAQ,EAAER,mBAAoB;YAC9BS,QAAQ,EAAEP,mBAAoB;YAC9BQ,OAAO,EAAEP;UAAmB;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNf,OAAA,CAACoF,UAAU;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;;AAED;AAAA2E,GAAA,CA5HMD,UAAU;EAAA,QACOnI,SAAS,EACFF,WAAW,EACpBC,WAAW,EACX8B,WAAW,EAGK/B,WAAW;AAAA;AAAAiK,GAAA,GAP1C5B,UAAU;AA+HhB,eAAeA,UAAU;AAAC,IAAAzE,EAAA,EAAAgB,GAAA,EAAAU,GAAA,EAAAM,GAAA,EAAAyB,GAAA,EAAAM,GAAA,EAAAI,GAAA,EAAAK,GAAA,EAAA6B,GAAA;AAAAC,YAAA,CAAAtG,EAAA;AAAAsG,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAAtE,GAAA;AAAAsE,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}