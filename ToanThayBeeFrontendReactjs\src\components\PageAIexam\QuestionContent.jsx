
import { useSelector, useDispatch } from "react-redux";
import { selectQuestion } from "src/features/examAI/examAISlice";
import LatexRenderer from "src/components/latex/RenderLatex";
import MarkdownPreviewWithMath from "src/components/latex/MarkDownPreview";
import SortableStatementsContainer from "./SortableStatementsContainer";
import { setQuestionsEdited } from "src/features/examAI/examAISlice";
import { Trash2 } from "lucide-react";

export const QuestionContent = ({ question, index }) => {
    const dispatch = useDispatch();
    const { selectedQuestion, isAddImage } = useSelector((state) => state.examAI);

    const { codes } = useSelector((state) => state.codes);

    return (
        <div
            key={question.id}
            className={`
            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer
            ${selectedQuestion?.id === question.id
                    ? "bg-blue-50 border-blue-500"
                    : "bg-white hover:bg-gray-50 border-gray-300"
                }
        `}
            onClick={() => dispatch(selectQuestion(question))}
        >
            {/* Thông tin câu hỏi */}
            <div className="flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1">
                <span className="font-medium text-gray-700">Câu {index + 1}</span>
                <span>Độ khó: <span className="text-gray-700">{question.difficulty}</span></span>
                <span>
                    Chương:{" "}
                    <span className="text-gray-700">
                        {codes["chapter"]?.find(c => c.code === question.chapter)?.description || "Chưa xác định"}
                    </span>
                </span>
            </div>

            {/* Nội dung câu hỏi */}
            {isAddImage ? (
                <div className="text-base text-gray-800 leading-relaxed"
                    onDragOver={(e) => e.preventDefault()} // bắt buộc để cho phép drop
                    onDrop={(e) => {
                        e.preventDefault();
                        const draggedImage = e.dataTransfer.getData("text/plain");
                        dispatch(setQuestionsEdited({ ...question, imageUrl: draggedImage }))
                        // Bạn có thể xử lý thêm logic ở đây: set state, push vào mảng, v.v.
                    }}
                >
                    <LatexRenderer className="text-gray-800" text={question.content} />
                </div>
            ) : (
                <div className="text-base text-gray-800 leading-relaxed">
                    <LatexRenderer className="text-gray-800" text={question.content} />
                </div>
            )}

            {question.imageUrl && (
                <div className="mt-2 relative group w-fit">
                    {/* Hình ảnh */}
                    <img
                        src={question.imageUrl}
                        alt="statement"
                        className="max-h-32 object-contain rounded-md border border-gray-200 transition-all group-hover:brightness-50"
                    />

                    {/* Nút xoá */}
                    <button
                        onClick={() => {
                            dispatch(
                                setQuestionsEdited({
                                    ...question,
                                    imageUrl: null
                                })
                            );
                        }}
                        className="absolute inset-0 flex items-center justify-center bg-transparent text-gray-100 hover:text-white transition-opacity opacity-0 group-hover:opacity-100"
                        title="Xóa ảnh"
                    >
                        <Trash2 className="w-8 h-8" />
                    </button>
                </div>
            )}


            <SortableStatementsContainer question={question} />
            {/* Statement: A, B, C,... */}

            {/* Lời giải (nếu có) */}
            {isAddImage ? (
                <div className="mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded"
                    onDragOver={(e) => e.preventDefault()} // bắt buộc để cho phép drop
                    onDrop={(e) => {
                        e.preventDefault();
                        const draggedImage = e.dataTransfer.getData("text/plain");
                        dispatch(setQuestionsEdited({ ...question, solutionImageUrl: draggedImage }))
                        // Bạn có thể xử lý thêm logic ở đây: set state, push vào mảng, v.v.
                    }}
                >
                    <span className="font-semibold">Lời giải:</span>{" "}
                    <MarkdownPreviewWithMath content={question.solution} />
                </div>
            ) : (
                <div className="mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded">
                    <span className="font-semibold">Lời giải:</span>{" "}
                    <MarkdownPreviewWithMath content={question.solution} />
                </div>
            )}

            {question.solutionImageUrl && (
                <div className="mt-2 relative group w-fit">
                    {/* Hình ảnh */}
                    <img
                        src={question.solutionImageUrl}
                        alt="statement"
                        className="max-h-32 object-contain rounded-md border border-gray-200 transition-all group-hover:brightness-50"
                    />

                    {/* Nút xoá */}
                    <button
                        onClick={() => {
                            dispatch(
                                setQuestionsEdited({
                                    ...question,
                                    solutionImageUrl: null
                                })
                            );
                        }}
                        className="absolute inset-0 flex items-center justify-center bg-transparent text-gray-100 hover:text-white transition-opacity opacity-0 group-hover:opacity-100"
                        title="Xóa ảnh"
                    >
                        <Trash2 className="w-8 h-8" />
                    </button>
                </div>
            )}
        </div>
    );
}

export default QuestionContent;