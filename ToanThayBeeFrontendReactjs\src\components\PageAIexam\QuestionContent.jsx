
import { useSelector, useDispatch } from "react-redux";
import { selectQuestion } from "src/features/examAI/examAISlice";
import LatexRenderer from "src/components/latex/RenderLatex";
import MarkdownPreviewWithMath from "src/components/latex/MarkDownPreview";

export const QuestionContent = ({ question, index }) => {
    const dispatch = useDispatch();
    const { selectedQuestion } = useSelector((state) => state.examAI);

    const { codes } = useSelector((state) => state.codes);

    const prefixTN = ["A.", "B.", "C.", "D."];
    const prefixDS = ["a)", "b)", "c)", "d)"];

    const renderStatement = (question) => {
        if (question.typeOfQuestion === "TN") {
            return (
                question.statement1s.map((item, idx) => (
                    <div key={idx} className={`text-gray-800 flex flex-row gap-1 ${item.isCorrect ? "text-green-600" : "text-gray-800"}`}>
                        <span className="font-semibold">{prefixTN[idx]} </span>
                        <LatexRenderer text={item.content} />
                    </div>
                ))
            );
        } else if (question.typeOfQuestion === "DS") {
            return (
                question.statement1s.map((item, idx) => (
                    <div key={idx} className={`text-gray-800 flex flex-row gap-1 ${item.isCorrect ? "text-green-600" : "text-red-600"}`}>
                        <span className="font-semibold">{prefixDS[idx]} </span>
                        <LatexRenderer text={item.content} />
                    </div>
                ))
            );
        } else if (question.typeOfQuestion === "TLN") {
            return (
                <div className="text-gray-800">
                    <span className="font-semibold">Đáp án: </span>
                    <span>{question.correctAnswer}</span>
                </div>
            );
        }

    }

    return (
        <div
            key={question.id}
            className={`
            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer
            ${selectedQuestion?.id === question.id
                    ? "bg-blue-50 border-blue-500"
                    : "bg-white hover:bg-gray-50 border-gray-300"
                }
        `}
            onClick={() => dispatch(selectQuestion(question))}
        >
            {/* Thông tin câu hỏi */}
            <div className="flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1">
                <span className="font-medium text-gray-700">Câu {index + 1}</span>
                <span>Độ khó: <span className="text-gray-700">{question.difficulty}</span></span>
                <span>
                    Chương:{" "}
                    <span className="text-gray-700">
                        {codes["chapter"]?.find(c => c.code === question.chapter)?.description || "Chưa xác định"}
                    </span>
                </span>
            </div>

            {/* Nội dung câu hỏi */}
            <div className="text-base text-gray-800 leading-relaxed">
                <LatexRenderer className="text-gray-800" text={question.content} />
            </div>

            {/* Statement: A, B, C,... */}
            {renderStatement(question)}

            {/* Lời giải (nếu có) */}
            {question.solution && (
                <div className="mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded">
                    <span className="font-semibold">Lời giải:</span>{" "}
                    <MarkdownPreviewWithMath content={question.solution} />
                </div>
            )}
        </div>
    );
}

    export default QuestionContent;