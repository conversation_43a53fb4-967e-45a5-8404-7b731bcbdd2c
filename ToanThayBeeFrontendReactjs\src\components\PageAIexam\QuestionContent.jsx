
import { useSelector, useDispatch } from "react-redux";
import { selectQuestion, setSelectedQuestion } from "src/features/examAI/examAISlice";
import LatexRenderer from "src/components/latex/RenderLatex";
import MarkdownPreviewWithMath from "src/components/latex/MarkDownPreview";
import SortableStatementsContainer from "./SortableStatementsContainer";
import DropZone from "./DropZone";

export const QuestionContent = ({ question, index }) => {
    const dispatch = useDispatch();
    const { selectedQuestion, isAddImage } = useSelector((state) => state.examAI);

    const { codes } = useSelector((state) => state.codes);

    const handleImageDrop = (dropZoneId, imageUrl) => {
        const updatedQuestion = { ...question };

        if (dropZoneId === `question-content-${question.id}`) {
            updatedQuestion.imageUrl = imageUrl;
        } else if (dropZoneId === `question-solution-${question.id}`) {
            updatedQuestion.solutionImageUrl = imageUrl;
        } else if (dropZoneId.startsWith(`statement-${question.id}-`)) {
            const statementIndex = parseInt(dropZoneId.split('-')[3]);
            if (updatedQuestion.statement1s && updatedQuestion.statement1s[statementIndex]) {
                updatedQuestion.statement1s[statementIndex].imageUrl = imageUrl;
            }
        }

        dispatch(setSelectedQuestion(updatedQuestion));
    };

    return (
        <div
            key={question.id}
            className={`
            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer
            ${selectedQuestion?.id === question.id
                    ? "bg-blue-50 border-blue-500"
                    : "bg-white hover:bg-gray-50 border-gray-300"
                }
        `}
            onClick={() => dispatch(selectQuestion(question))}
        >
            {/* Thông tin câu hỏi */}
            <div className="flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1">
                <span className="font-medium text-gray-700">Câu {index + 1}</span>
                <span>Độ khó: <span className="text-gray-700">{question.difficulty}</span></span>
                <span>
                    Chương:{" "}
                    <span className="text-gray-700">
                        {codes["chapter"]?.find(c => c.code === question.chapter)?.description || "Chưa xác định"}
                    </span>
                </span>
            </div>

            {/* Nội dung câu hỏi */}
            {!isAddImage ? (
                <DropZone
                    id={`question-content-${question.id}`}
                    className="text-base text-gray-800 leading-relaxed"
                    placeholder="Thả hình ảnh vào nội dung câu hỏi"
                >
                    <LatexRenderer className="text-gray-800" text={question.content} />
                    {question.imageUrl && (
                        <img src={question.imageUrl} alt="Question" className="mt-2 max-w-full h-auto rounded" />
                    )}
                </DropZone>
            ) : (
                <div className="text-base text-gray-800 leading-relaxed">
                    <LatexRenderer className="text-gray-800" text={question.content} />
                    {question.imageUrl && (
                        <img src={question.imageUrl} alt="Question" className="mt-2 max-w-full h-auto rounded" />
                    )}
                </div>
            )}

            {/* Statement: A, B, C,... */}
            <SortableStatementsContainer question={question} />

            {/* Lời giải (nếu có) */}
            {question.solution && (
                !isAddImage ? (
                    <DropZone
                        id={`question-solution-${question.id}`}
                        className="mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded"
                        placeholder="Thả hình ảnh vào lời giải"
                    >
                        <span className="font-semibold">Lời giải:</span>{" "}
                        <MarkdownPreviewWithMath content={question.solution} />
                        {question.solutionImageUrl && (
                            <img src={question.solutionImageUrl} alt="Solution" className="mt-2 max-w-full h-auto rounded" />
                        )}
                    </DropZone>
                ) : (
                    <div className="mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded">
                        <span className="font-semibold">Lời giải:</span>{" "}
                        <MarkdownPreviewWithMath content={question.solution} />
                        {question.solutionImageUrl && (
                            <img src={question.solutionImageUrl} alt="Solution" className="mt-2 max-w-full h-auto rounded" />
                        )}
                    </div>
                )
            )}
        </div>
    );
}

export default QuestionContent;