
import { useSelector, useDispatch } from "react-redux";
import { selectQuestion } from "src/features/examAI/examAISlice";
import LatexRenderer from "src/components/latex/RenderLatex";
import MarkdownPreviewWithMath from "src/components/latex/MarkDownPreview";
import SortableStatementsContainer from "./SortableStatementsContainer";
import { setQuestionsEdited } from "src/features/examAI/examAISlice";
import { Trash2 } from "lucide-react";
import ImageDropZone from "./ImageDropZone";
import SolutionEditor from "./SolutionEditor";

export const QuestionContent = ({ question, index }) => {
    const dispatch = useDispatch();
    const { selectedQuestion, isAddImage } = useSelector((state) => state.examAI);

    const { codes } = useSelector((state) => state.codes);

    return (
        <div
            key={question.id}
            className={`
            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer
            ${selectedQuestion?.id === question.id && !isAddImage
                    ? "bg-blue-50 border-blue-500"
                    : "bg-white hover:bg-gray-50 border-gray-300"
                }
        `}
            onClick={() => {
                if (isAddImage) return
                dispatch(selectQuestion(question))
            }}
        >
            {/* Thông tin câu hỏi */}
            <div div className="flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1" >
                <span className="font-medium text-gray-700">Câu {index + 1}</span>
                <span>Độ khó: <span className="text-gray-700">{question.difficulty}</span></span>
                <span>
                    Chương:{" "}
                    <span className="text-gray-700">
                        {codes["chapter"]?.find(c => c.code === question.chapter)?.description || "Chưa xác định"}
                    </span>
                </span>
            </div>

            {/* Nội dung câu hỏi */}

            {
                isAddImage ? (
                    <ImageDropZone
                        imageUrl={question.imageUrl}
                        onImageDrop={(newUrl) => {
                            dispatch(
                                setQuestionsEdited({
                                    ...question,
                                    imageUrl: newUrl
                                })
                            );
                        }}
                        onImageRemove={() => {
                            dispatch(
                                setQuestionsEdited({
                                    ...question,
                                    imageUrl: null
                                })
                            );
                        }}
                        content={question.content}
                    />
                ) : (
                    <div className="flex flex-col gap-2">
                        <div className="text-base text-gray-800 leading-relaxed">
                            <LatexRenderer className="text-gray-800" text={question.content} />
                        </div>
                        {question.imageUrl && (
                            <div className="flex flex-col items-center justify-center w-full h-[10rem] mt-1">
                                <img
                                    src={question.imageUrl}
                                    alt="question"
                                    className="object-contain w-full h-full"
                                />
                            </div>
                        )}
                    </div>
                )
            }


            <SortableStatementsContainer question={question} />
            {/* Statement: A, B, C,... */}

            {/* Lời giải (nếu có) */}
            {question.solution && (
                <SolutionEditor
                    solution={question.solution}
                    onSolutionChange={(newSolution) => {
                        dispatch(setQuestionsEdited({ ...question, solution: newSolution }));
                    }}
                    isAddImage={isAddImage}
                />
            )}


        </div >
    );
}

export default QuestionContent;