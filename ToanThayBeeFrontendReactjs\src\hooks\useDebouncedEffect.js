import { useEffect } from "react";

/**
 * @param {Function} effect - Hàm effect bạn muốn thực thi sau delay.
 * @param {Array<any>} deps - Dependencies giống như trong useEffect.
 * @param {number} delay - Th<PERSON><PERSON> gian chờ (ms) sau khi deps dừng thay đổi.
 */
const useDebouncedEffect = (effect, deps, delay) => {
    useEffect(() => {
        const handler = setTimeout(() => {
            effect();
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [...deps, delay]);
};

export default useDebouncedEffect;
