'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('tuitionPayment', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'user',
          key: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
        comment: 'ID của học sinh đóng học phí'
      },
      month: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Tháng đóng học phí (định dạng YYYY-MM)'
      },
      expectedAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
        comment: 'Số tiền học phí cần đóng'
      },
      paidAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
        comment: 'Số tiền học phí đã đóng'
      },
      paymentDate: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Ngày đóng học phí'
      },
      dueDate: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Ngày đến hạn đóng học phí'
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'UNPAID',
        comment: 'Trạng thái đóng học phí: UNPAID, PARTIAL, PAID'
      },
      note: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Ghi chú về việc đóng học phí'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Thêm index cho các cột thường xuyên truy vấn
    await queryInterface.addIndex('tuitionPayment', ['userId', 'month'], {
      name: 'idx_tuition_payment_user_month',
      unique: true
    });

    await queryInterface.addIndex('tuitionPayment', ['status'], {
      name: 'idx_tuition_payment_status'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('tuitionPayment');
  }
};
