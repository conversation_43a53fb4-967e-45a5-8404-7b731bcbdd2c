{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\ImageDropZone.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageDropZone = _ref => {\n  _s();\n  let {\n    imageUrl,\n    onImageDrop,\n    onImageRemove,\n    content\n  } = _ref;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\n    if (draggedImage && onImageDrop) {\n      onImageDrop(draggedImage);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-2 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative flex items-center justify-start rounded-md p-4 transition-all duration-300 cursor-pointer hover:border-sky-500 hover:bg-sky-50 hover:border-2 hover:border-dashed \".concat(isDraggingOver ? \"border-sky-500 bg-sky-50 border-2 border-dashed\" : \"\"),\n      onHover: true,\n      onDragOver: e => e.preventDefault(),\n      onDragEnter: () => setIsDraggingOver(true),\n      onDragLeave: () => setIsDraggingOver(false),\n      onDrop: handleDrop,\n      children: isDraggingOver ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500 text-sm\",\n        children: \"Th\\xEAm \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(LatexRenderer, {\n        text: content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this), imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative group w-fit\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: \"dropped\",\n        className: \"rounded-md max-h-40 object-contain w-full border transition-all group-hover:brightness-75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition\",\n        onClick: e => {\n          e.stopPropagation();\n          onImageRemove === null || onImageRemove === void 0 ? void 0 : onImageRemove();\n        },\n        title: \"X\\xF3a \\u1EA3nh\",\n        children: /*#__PURE__*/_jsxDEV(Trash2, {\n          className: \"w-6 h-6 text-white bg-red-500 p-1 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_s(ImageDropZone, \"VKW1ZLDpPe+zo2/qAoOWVX5YkVs=\");\n_c = ImageDropZone;\nexport default ImageDropZone;\nvar _c;\n$RefreshReg$(_c, \"ImageDropZone\");", "map": {"version": 3, "names": ["React", "useState", "Trash2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ImageDropZone", "_ref", "_s", "imageUrl", "onImageDrop", "onImageRemove", "content", "isDraggingOver", "setIsDraggingOver", "handleDrop", "e", "preventDefault", "draggedImage", "dataTransfer", "getData", "className", "children", "concat", "onHover", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "src", "alt", "onClick", "stopPropagation", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/ImageDropZone.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Trash2 } from \"lucide-react\"; // Lucide icon\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nconst ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove, content }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\"); // Expects base64 or URL\r\n        if (draggedImage && onImageDrop) {\r\n            onImageDrop(draggedImage);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-2 w-full\">\r\n            <div\r\n                className={\r\n                    `relative flex items-center justify-start rounded-md p-4 transition-all duration-300 cursor-pointer hover:border-sky-500 hover:bg-sky-50 hover:border-2 hover:border-dashed ${isDraggingOver ? \"border-sky-500 bg-sky-50 border-2 border-dashed\" : \"\"\r\n                    }`\r\n                }\r\n                onHover\r\n                onDragOver={(e) => e.preventDefault()}\r\n                onDragEnter={() => setIsDraggingOver(true)}\r\n                onDragLeave={() => setIsDraggingOver(false)}\r\n                onDrop={handleDrop}\r\n            >\r\n                {isDraggingOver ? (\r\n                    <p className=\"text-gray-500 text-sm\">Thêm ảnh</p>\r\n                ) : (\r\n                    <LatexRenderer text={content} />\r\n                )}\r\n            </div>\r\n            {imageUrl && (\r\n                <div className=\"relative group w-fit\">\r\n                    <img\r\n                        src={imageUrl}\r\n                        alt=\"dropped\"\r\n                        className=\"rounded-md max-h-40 object-contain w-full border transition-all group-hover:brightness-75\"\r\n                    />\r\n                    <button\r\n                        className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition\"\r\n                        onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            onImageRemove?.();\r\n                        }}\r\n                        title=\"Xóa ảnh\"\r\n                    >\r\n                        <Trash2 className=\"w-6 h-6 text-white bg-red-500 p-1 rounded-full\" />\r\n                    </button>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ImageDropZone;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,cAAc,CAAC,CAAC;AACvC,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACjD,MAAMC,aAAa,GAAGC,IAAA,IAAuD;EAAAC,EAAA;EAAA,IAAtD;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,aAAa;IAAEC;EAAQ,CAAC,GAAAL,IAAA;EACpE,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMc,UAAU,GAAIC,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMI,YAAY,GAAGF,CAAC,CAACG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3D,IAAIF,YAAY,IAAIR,WAAW,EAAE;MAC7BA,WAAW,CAACQ,YAAY,CAAC;IAC7B;EACJ,CAAC;EAED,oBACIb,OAAA;IAAKgB,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACvCjB,OAAA;MACIgB,SAAS,gLAAAE,MAAA,CACyKV,cAAc,GAAG,iDAAiD,GAAG,EAAE,CAExP;MACDW,OAAO;MACPC,UAAU,EAAGT,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;MACtCS,WAAW,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC,IAAI,CAAE;MAC3Ca,WAAW,EAAEA,CAAA,KAAMb,iBAAiB,CAAC,KAAK,CAAE;MAC5Cc,MAAM,EAAEb,UAAW;MAAAO,QAAA,EAElBT,cAAc,gBACXR,OAAA;QAAGgB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAEjD3B,OAAA,CAACF,aAAa;QAAC8B,IAAI,EAAErB;MAAQ;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAClC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EACLvB,QAAQ,iBACLJ,OAAA;MAAKgB,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACjCjB,OAAA;QACI6B,GAAG,EAAEzB,QAAS;QACd0B,GAAG,EAAC,SAAS;QACbd,SAAS,EAAC;MAA2F;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG,CAAC,eACF3B,OAAA;QACIgB,SAAS,EAAC,gGAAgG;QAC1Ge,OAAO,EAAGpB,CAAC,IAAK;UACZA,CAAC,CAACqB,eAAe,CAAC,CAAC;UACnB1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAG,CAAC;QACrB,CAAE;QACF2B,KAAK,EAAC,iBAAS;QAAAhB,QAAA,eAEfjB,OAAA,CAACH,MAAM;UAACmB,SAAS,EAAC;QAAgD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACxB,EAAA,CArDIF,aAAa;AAAAiC,EAAA,GAAbjC,aAAa;AAuDnB,eAAeA,aAAa;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}