import React, { useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useSelector, useDispatch } from 'react-redux';
import {
    setSelectedDay,
    prevMonth,
    nextMonth,
    setView
} from 'src/features/calendar/calendarSlice';

import { getLearningItemMonth } from 'src/features/learningItem/learningItemSlice';

const getDaysInMonth = (year, month) => new Date(year, month + 1, 0).getDate();
const getFirstDayOfMonth = (year, month) => new Date(year, month, 1).getDay(); // 0 (CN) - 6 (T7)

const MonthView = ({ isMobile }) => {
    const dispatch = useDispatch();
    const { currentMonth, currentYear, selectedDay, today, view } = useSelector((state) => state.calendar);
    const { learningItemsMonth } = useSelector((state) => state.learningItems);
    const { classesOverview: classes } = useSelector((state) => state.classes);

    const selectedDate = new Date(selectedDay);
    const todayDate = new Date(today);

    const daysInMonth = getDaysInMonth(currentYear, currentMonth);
    const firstDay = getFirstDayOfMonth(currentYear, currentMonth); // 0 (CN)

    const prevDaysCount = firstDay === 0 ? 6 : firstDay - 1; // Số ô tháng trước
    const nextDaysCount = 42 - (prevDaysCount + daysInMonth); // Số ô tháng sau

    const prevMonth1 = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const daysInPrevMonth = getDaysInMonth(prevYear, prevMonth1);

    const nextMonth1 = currentMonth === 11 ? 0 : currentMonth + 1;
    const nextYear = currentMonth === 11 ? currentYear + 1 : currentYear;

    // Ngày đầu tiên của lưới (ô đầu tiên trong tháng trước)
    const firstGridDate = new Date(prevYear, prevMonth1, daysInPrevMonth - prevDaysCount + 1);
    firstGridDate.setHours(0, 0, 0, 0);

    // Ngày cuối cùng của lưới (ô cuối cùng trong tháng sau)
    const lastGridDate = new Date(nextYear, nextMonth1, nextDaysCount);
    lastGridDate.setHours(23, 59, 59, 999);

    // console.log('First grid date:', firstGridDate);
    // console.log('Last grid date:', lastGridDate);

    useEffect(() => {
        if (view !== 'month') return;
        if (firstGridDate && lastGridDate) {
            dispatch(getLearningItemMonth({
                firstDay: firstGridDate.toISOString(),
                lastDay: lastGridDate.toISOString(),
            }))
        }
    }, [dispatch, currentMonth, view]);

    const handleDayClick = (date) => {
        dispatch(setSelectedDay(date.toISOString()));
        dispatch(setView('week'));
    };

    const isSameDate = (date1, date2) =>
        date1.getDate() === date2.getDate() &&
        date1.getMonth() === date2.getMonth() &&
        date1.getFullYear() === date2.getFullYear();
    const vietnameseMonths = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12',
    ];
    const getVietnameseShortDay = (date) => {
        const day = date.getDay(); // 0 = CN, 1 = T2, ..., 6 = T7
        if (day === 0) return 'CN';
        return `T${day + 1}`;
    };

    return (
        <div className="overflow-auto w-full flex-grow p-4 pb-0 bg-gray-50" >
            {/* Header */}
            <div className="p-2 flex border-b border-gray-200 flex-row justify-between bg-white" >
                <button onClick={() => dispatch(prevMonth())} className='p-1 rounded-full hover:bg-gray-100 transition'>
                    <ChevronLeft className="w-5 h-5" />
                </button>
                <div className="text-center flex flex-col gap-1 items-center z-10">
                    <div className="font-semibold text-emerald-800">{vietnameseMonths[currentMonth]}</div>
                    <div className="text-md text-gray-500">{currentYear}</div>
                </div>
                <button onClick={() => dispatch(nextMonth())} className='p-1 rounded-full hover:bg-gray-100 transition'>
                    <ChevronRight className="w-5 h-5" />
                </button>
            </div>

            {/* Dòng T2 đến CN */}
            <div className="grid grid-cols-7 text-[12px] font-medium text-center text-gray-500 bg-white border-b border-gray-200">
                {['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'].map((day, idx) => (
                    <div key={idx} className="py-2 border-l border-gray-200 last:border-r">{day}</div>
                ))}
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 border-l border-gray-200 text-[12px] bg-white text-center h-[75vh]" >
                {/* Days from previous month */}
                {
                    [...Array(prevDaysCount)].map((_, i) => {
                        const day = daysInPrevMonth - prevDaysCount + 1 + i;
                        const date = new Date(prevYear, prevMonth1, day);

                        return (
                            <div key={`prev-${i}`}
                                onClick={() => handleDayClick(date)}
                                className="border-b border-r border-gray-200 text-gray-400">
                                {day}
                            </div>
                        );
                    })
                }

                {/* Days in current month */}
                {
                    [...Array(daysInMonth)].map((_, i) => {
                        const day = i + 1;
                        const date = new Date(currentYear, currentMonth, day);
                        const dayOfWeek = getVietnameseShortDay(date); // e.g. "T2"

                        // Định nghĩa class UI
                        const baseClass = 'w-6 h-6 flex items-center justify-center rounded-full cursor-pointer';
                        let bgClass = 'hover:bg-gray-200';
                        let textClass = '';

                        if (isSameDate(date, todayDate)) {
                            bgClass = 'bg-sky-500';
                            textClass = 'text-white font-bold';
                        } else if (isSameDate(date, selectedDate)) {
                            bgClass = 'bg-sky-200';
                            textClass = 'text-black font-semibold';
                        }

                        const matchingClasses = classes.filter(cls =>
                            cls.class.status === 'LHD' &&
                            cls.class.public === true &&
                            (
                                cls.class.dayOfWeek1 === dayOfWeek ||
                                cls.class.dayOfWeek2 === dayOfWeek
                            )
                        );
                        const processedClasses = [];
                        matchingClasses.forEach((cls) => {
                            // Handle new format - only show session that matches current day
                            if (cls.class.dayOfWeek1 === dayOfWeek && cls.class.startTime1 && cls.class.endTime1) {
                                // Session 1 for this day
                                const startTime1 = cls.class.startTime1 || "08:00:00";
                                const endTime1 = cls.class.endTime1 || "09:00:00";
                                const formattedStartTime1 = startTime1.substring(0, 5);
                                const formattedEndTime1 = endTime1.substring(0, 5);

                                processedClasses.push({
                                    ...cls.class,
                                    formattedStartTime: formattedStartTime1,
                                    formattedEndTime: formattedEndTime1,
                                    sessionNumber: 1,
                                    sessionId: `${cls.id}-session1`
                                });
                            }

                            if (cls.class.dayOfWeek2 === dayOfWeek && cls.class.startTime2 && cls.class.endTime2) {
                                // Session 2 for this day
                                const startTime2 = cls.class.startTime2 || "10:00:00";
                                const endTime2 = cls.class.endTime2 || "11:00:00";
                                const formattedStartTime2 = startTime2.substring(0, 5);
                                const formattedEndTime2 = endTime2.substring(0, 5);

                                processedClasses.push({
                                    ...cls.class,
                                    formattedStartTime: formattedStartTime2,
                                    formattedEndTime: formattedEndTime2,
                                    sessionNumber: 2,
                                    sessionId: `${cls.class.id}-session2`
                                });
                            }
                        });

                        const itemsToday = learningItemsMonth.filter(item => {
                            const itemDate = new Date(item.learningItem.deadline);
                            return isSameDate(itemDate, date);
                        });



                        return (
                            <div key={day}
                                onClick={() => handleDayClick(date)}
                                className="border-b border-r border-gray-200 flex flex-col items-center p-1 gap-1">
                                <div
                                    className={`${baseClass} ${bgClass} ${textClass}`}
                                    onClick={() => {
                                        dispatch(setSelectedDay(date.toISOString()));
                                        dispatch(setView('day'));
                                    }}
                                >
                                    {day}
                                </div>

                                {itemsToday?.map((item, i) => {
                                    const now = new Date();
                                    const deadline = new Date(item.learningItem.deadline);
                                    deadline.setHours(23, 59, 59, 999);
                                    const isOverdue = !item.isDone && deadline < now;

                                    const bgColor = item.isDone
                                        ? "bg-green-100"
                                        : isOverdue
                                            ? "bg-red-100"
                                            : "bg-yellow-100";

                                    const borderColor = item.isDone
                                        ? "border-green-500"
                                        : isOverdue
                                            ? "border-red-500"
                                            : "border-yellow-400";

                                    const textColor = item.isDone
                                        ? "text-green-700"
                                        : isOverdue
                                            ? "text-red-700"
                                            : "text-yellow-800";

                                    return (
                                        <div
                                            key={i}
                                            title={item.title}
                                            className={`w-full ${bgColor} border-l-4 ${borderColor} rounded-r-md px-2 py-[0.3rem] overflow-hidden hover:brightness-95 transition-colors duration-150 cursor-pointer text-[11px] text-left flex items-center gap-1 ${textColor} mt-[4px] first:mt-0`}
                                        >
                                            <span className="truncate">
                                                {item.learningItem.name.length > 15 ? item.learningItem.name.slice(0, 15) + '…' : item.learningItem.name}
                                            </span>
                                        </div>
                                    );
                                })}


                                {/* Render các lớp học khớp với thứ */}
                                {processedClasses.map((cls, idx) => {
                                    // Determine color based on class name
                                    const lowerName = cls?.name?.toLowerCase();

                                    // Xác định khối
                                    let grade = null;
                                    if (lowerName?.includes('10')) grade = '10';
                                    else if (lowerName?.includes('11')) grade = '11';
                                    else if (lowerName?.includes('12')) grade = '12';

                                    // Bảng màu theo từng khối và loại lớp
                                    const colorMap = {
                                        '10': {
                                            đề: 'bg-blue-100 border-blue-500 text-blue-800',
                                            đại: 'bg-indigo-100 border-indigo-500 text-indigo-800',
                                            hình: 'bg-emerald-100 border-emerald-500 text-emerald-800',
                                        },
                                        '11': {
                                            đề: 'bg-orange-100 border-orange-500 text-orange-800',
                                            đại: 'bg-cyan-100 border-cyan-500 text-cyan-800', // đổi từ pink sang cyan
                                            hình: 'bg-teal-100 border-teal-500 text-teal-800',
                                        },
                                        '12': {
                                            đề: 'bg-purple-100 border-purple-500 text-purple-800',
                                            đại: 'bg-lime-100 border-lime-500 text-lime-800', // đổi từ rose sang lime
                                            hình: 'bg-amber-100 border-amber-500 text-amber-800',
                                        }
                                    };


                                    let colorClass;

                                    if (grade) {
                                        if (lowerName.includes('đề')) {
                                            colorClass = colorMap[grade]['đề'];
                                        } else if (lowerName.includes('đại')) {
                                            colorClass = colorMap[grade]['đại'];
                                        } else if (lowerName.includes('hình')) {
                                            colorClass = colorMap[grade]['hình'];
                                        }
                                    }

                                    // Nếu không thuộc khối nào hoặc không có từ khóa phù hợp → dùng màu mặc định
                                    const defaultColors = [
                                        'bg-cyan-100 border-cyan-500 text-cyan-800',
                                        'bg-lime-100 border-lime-500 text-lime-800',
                                        'bg-sky-100 border-sky-500 text-sky-800',
                                        'bg-fuchsia-100 border-fuchsia-500 text-fuchsia-800',
                                        'bg-yellow-100 border-yellow-500 text-yellow-800',
                                    ];

                                    if (!colorClass) {
                                        const colorIndex = idx % defaultColors.length;
                                        colorClass = defaultColors[colorIndex];
                                    }

                                    const [bgColor, borderColor, textColor] = colorClass.split(' ');

                                    return (
                                        <div
                                            key={idx}
                                            className={`flex w-full ${bgColor} border-l-4 ${borderColor} rounded-r-md p-1 overflow-hidden hover:brightness-95 transition-colors duration-150 cursor-pointer`}
                                            title={cls.name}
                                        >
                                            <div className={`font-semibold text-xs ${textColor} truncate`}>
                                                {!isMobile && cls.formattedStartTime} {cls.name}
                                            </div>
                                        </div>
                                    )
                                })}
                            </div>
                        );
                    })

                }

                {/* Days from next month */}
                {
                    [...Array(nextDaysCount)].map((_, i) => {
                        const day = i + 1;
                        const date = new Date(nextYear, nextMonth1, day);

                        return (
                            <div
                                onClick={() => handleDayClick(date)}
                                key={`next-${i}`}
                                className="border-b border-r border-gray-200 text-gray-400">
                                {day}
                            </div>
                        );
                    })
                }
            </div>
        </div>
    );
};

export default MonthView;