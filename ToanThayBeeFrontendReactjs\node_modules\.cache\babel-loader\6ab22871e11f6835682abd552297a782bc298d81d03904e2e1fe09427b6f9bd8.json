{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\NavigateBar.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavigateBar = _ref => {\n  let {\n    list,\n    active,\n    setActive\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-row text-xs w-full\",\n    children: list.map((item, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setActive(item.value),\n      className: \"flex-1 p-2 text-center \".concat(active === item.value ? 'font-bold' : ' border-b ', \" first:border-r last:border-l border-gray-200 cursor-pointer \"),\n      children: item.name\n    }, item.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = NavigateBar;\nexport default NavigateBar;\nvar _c;\n$RefreshReg$(_c, \"NavigateBar\");", "map": {"version": 3, "names": ["NavigateBar", "_ref", "list", "active", "setActive", "_jsxDEV", "className", "children", "map", "item", "index", "onClick", "value", "concat", "name", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/NavigateBar.jsx"], "sourcesContent": ["const NavigateBar = ({ list, active, setActive }) => {\r\n    return (\r\n        <div className=\"flex flex-row text-xs w-full\">\r\n            {list.map((item, index) => (\r\n                <button\r\n                    key={item.id}\r\n                    onClick={() => setActive(item.value)}\r\n                    className={`flex-1 p-2 text-center ${active === item.value ? 'font-bold' : ' border-b '} first:border-r last:border-l border-gray-200 cursor-pointer `}>\r\n                    {item.name}\r\n                </button>\r\n            ))}\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default NavigateBar;"], "mappings": ";;AAAA,MAAMA,WAAW,GAAGC,IAAA,IAAiC;EAAA,IAAhC;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAU,CAAC,GAAAH,IAAA;EAC5C,oBACII,OAAA;IAAKC,SAAS,EAAC,8BAA8B;IAAAC,QAAA,EACxCL,IAAI,CAACM,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAClBL,OAAA;MAEIM,OAAO,EAAEA,CAAA,KAAMP,SAAS,CAACK,IAAI,CAACG,KAAK,CAAE;MACrCN,SAAS,4BAAAO,MAAA,CAA4BV,MAAM,KAAKM,IAAI,CAACG,KAAK,GAAG,WAAW,GAAG,YAAY,kEAAgE;MAAAL,QAAA,EACtJE,IAAI,CAACK;IAAI,GAHLL,IAAI,CAACM,EAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIR,CACX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAAC,EAAA,GAbKpB,WAAW;AAejB,eAAeA,WAAW;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}