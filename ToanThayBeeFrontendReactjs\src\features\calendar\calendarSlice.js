import { createSlice } from '@reduxjs/toolkit';

// Hàm tính đầu tuần & cuối tuần dựa trên ngày truyền vào
const calculateWeekRange = (date) => {
    const day = date.getDay(); // 0 - <PERSON><PERSON>t
    const start = new Date(date);
    start.setDate(date.getDate() - (day === 0 ? 6 : day - 1));
    start.setHours(0, 0, 0, 0);

    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    end.setHours(23, 59, 59, 999);

    return {
        startOfWeek: start.toISOString(),
        endOfWeek: end.toISOString(),
    };
};

const getTimeSlots = () => {
    const timeSlots = [];
    for (let hour = 8; hour < 24; hour++) {
        const formattedHour = hour.toString().padStart(2, '0');
        timeSlots.push(`${formattedHour}:00`);
    }
    return timeSlots;
};

const today = new Date();
const { startOfWeek, endOfWeek } = calculateWeekRange(today);

const initialState = {
    currentMonth: today.getMonth(),
    currentYear: today.getFullYear(),
    selectedDay: today.toISOString(),
    today: today.toISOString(),
    view: 'week',
    timeSlots: getTimeSlots(),
    startOfWeek,
    endOfWeek,
};

const calendarSlice = createSlice({
    name: 'calendar',
    initialState,
    reducers: {
        prevMonth: (state) => {
            if (state.currentMonth === 0) {
                state.currentMonth = 11;
                state.currentYear -= 1;
            } else {
                state.currentMonth -= 1;
            }

            const newDate = new Date(state.currentYear, state.currentMonth, 1);
            state.selectedDay = newDate.toISOString();

            const { startOfWeek, endOfWeek } = calculateWeekRange(newDate);
            state.startOfWeek = startOfWeek;
            state.endOfWeek = endOfWeek;
        },
        nextMonth: (state) => {
            if (state.currentMonth === 11) {
                state.currentMonth = 0;
                state.currentYear += 1;
            } else {
                state.currentMonth += 1;
            }

            const newDate = new Date(state.currentYear, state.currentMonth, 1);
            state.selectedDay = newDate.toISOString();

            const { startOfWeek, endOfWeek } = calculateWeekRange(newDate);
            state.startOfWeek = startOfWeek;
            state.endOfWeek = endOfWeek;
        },
        setCurrentMonth: (state, action) => {
            state.currentMonth = action.payload;
        },
        setCurrentYear: (state, action) => {
            state.currentYear = action.payload;
        },
        setSelectedDay: (state, action) => {
            const selectedDate = new Date(action.payload);
            state.selectedDay = selectedDate.toISOString();
            state.currentMonth = selectedDate.getMonth();
            state.currentYear = selectedDate.getFullYear();

            const { startOfWeek, endOfWeek } = calculateWeekRange(selectedDate);
            state.startOfWeek = startOfWeek;
            state.endOfWeek = endOfWeek;
        },
        resetCalendar: (state) => {
            const now = new Date();
            state.today = now.toISOString();
            state.currentMonth = now.getMonth();
            state.currentYear = now.getFullYear();
            state.selectedDay = now.toISOString();

            const { startOfWeek, endOfWeek } = calculateWeekRange(now);
            state.startOfWeek = startOfWeek;
            state.endOfWeek = endOfWeek;
        },
        setCalendarView: (state, action) => {
            state.view = action.payload;
        },
        updateWeekRange: (state) => {
            const date = new Date(state.selectedDay);
            const { startOfWeek, endOfWeek } = calculateWeekRange(date);
            state.startOfWeek = startOfWeek;
            state.endOfWeek = endOfWeek;
        },
        setView: (state, action) => {
            state.view = action.payload;
        }
    },
});

export const {
    setCurrentMonth,
    setCurrentYear,
    setSelectedDay,
    resetCalendar,
    prevMonth,
    nextMonth,
    setCalendarView,
    updateWeekRange,
    setView
} = calendarSlice.actions;

export default calendarSlice.reducer;
