import LoadingSpinner from "../loading/LoadingSpinner";
const ConfirmModal = ({ isOpen, onClose, onConfirm, title, message, color = "red", loading = false }) => {
    if (!isOpen) return null;

    const colorClasses = {
        red: "bg-red-500 hover:bg-red-600",
        blue: "bg-blue-500 hover:bg-blue-600",
        green: "bg-green-500 hover:bg-green-600",
        yellow: "bg-yellow-500 hover:bg-yellow-600",
        gray: "bg-gray-500 hover:bg-gray-600",
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
                <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
                    <p className="text-sm text-gray-500 mb-6">{message}</p>
                    <div className="flex justify-end gap-3">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
                        >
                            Hủy
                        </button>
                        <button
                            onClick={onConfirm}
                            className={"px-4 py-2 text-white rounded-md" + ` ${colorClasses[color]}`}
                        >
                            {loading ? <LoadingSpinner
                                minHeight="min-h-0"
                            /> : "Xác nhận"}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ConfirmModal;
