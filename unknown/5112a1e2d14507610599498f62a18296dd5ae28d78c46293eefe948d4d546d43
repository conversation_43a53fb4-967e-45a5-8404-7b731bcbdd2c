{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport * as questionApi from \"../../services/questionApi\";\nexport const fetchExamQuestionsWithoutPagination = createAsyncThunk(\"questions/fetchExamQuestionsWithExam\", async (_ref, _ref2) => {\n  let {\n    id\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, {\n    id,\n    pageSize: 1000,\n    sortOrder: 'DESC'\n  }, data => {}, false, false);\n});\nconst questionsExamSlice = createSlice({\n  name: \"questionsExam\",\n  initialState: {\n    questionsExam: [],\n    loading: false,\n    view: 'question',\n    showAddImagesModal: false,\n    folder: \"questionImage\",\n    selectedIndex: 0\n  },\n  reducers: {\n    setQuestionsExam: (state, action) => {\n      state.questionsExam = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setViewRightContent: (state, action) => {\n      state.view = action.payload;\n    },\n    setSelectedIndex: (state, action) => {\n      state.selectedIndex = action.payload;\n    },\n    reorderQuestions: (state, action) => {\n      const {\n        oldIndex,\n        newIndex\n      } = action.payload;\n      console.log('Reorder Questions:', {\n        oldIndex,\n        newIndex\n      });\n      if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0 && oldIndex < state.questionsExam.length && newIndex < state.questionsExam.length) {\n        // Tạo mảng mới với thứ tự đã thay đổi\n        const newQuestions = [...state.questionsExam];\n        const [movedQuestion] = newQuestions.splice(oldIndex, 1);\n        newQuestions.splice(newIndex, 0, movedQuestion);\n\n        // Cập nhật thuộc tính order cho tất cả câu hỏi\n        const updatedQuestions = newQuestions.map((question, index) => ({\n          ...question,\n          ExamQuestions: {\n            ...question.ExamQuestions,\n            order: index\n          }\n        }));\n        state.questionsExam = updatedQuestions;\n      }\n    },\n    reorderStatements: (state, action) => {\n      const {\n        questionId,\n        oldIndex,\n        newIndex\n      } = action.payload;\n      const questionIndex = state.questionsExam.findIndex(q => q.id === questionId);\n      if (questionIndex === -1) return;\n      const question = state.questionsExam[questionIndex];\n      if (!question.statements || oldIndex === newIndex) return;\n      if (oldIndex >= 0 && newIndex >= 0 && oldIndex < question.statements.length && newIndex < question.statements.length) {\n        // Tạo mảng mới với thứ tự đã thay đổi\n        const newStatements = [...question.statements];\n        const [movedStatement] = newStatements.splice(oldIndex, 1);\n        newStatements.splice(newIndex, 0, movedStatement);\n\n        // Cập nhật thuộc tính order cho tất cả statements\n        const updatedStatements = newStatements.map((statement, index) => ({\n          ...statement,\n          order: index\n        }));\n\n        // Cập nhật question với statements mới\n        const updatedQuestion = {\n          ...question,\n          statements: updatedStatements\n        };\n        state.questionsExam[questionIndex] = updatedQuestion;\n      }\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchExamQuestionsWithoutPagination.pending, state => {\n      state.loading = true;\n    }).addCase(fetchExamQuestionsWithoutPagination.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.questionsExam = action.payload.data;\n      }\n      state.loading = false;\n    }).addCase(fetchExamQuestionsWithoutPagination.rejected, state => {\n      state.questionsExam = [];\n      state.loading = false;\n    });\n  }\n});\nexport const {\n  setQuestionsExam,\n  setLoading,\n  setViewRightContent,\n  setSelectedIndex,\n  reorderQuestions,\n  reorderStatements\n} = questionsExamSlice.actions;\nexport default questionsExamSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questionA<PERSON>", "fetchExamQuestionsWithoutPagination", "_ref", "_ref2", "id", "dispatch", "getExamQuestionsAPI", "pageSize", "sortOrder", "data", "questionsExamSlice", "name", "initialState", "questionsExam", "loading", "view", "showAddImagesModal", "folder", "selectedIndex", "reducers", "setQuestionsExam", "state", "action", "payload", "setLoading", "setViewRightContent", "setSelectedIndex", "reorderQuestions", "oldIndex", "newIndex", "console", "log", "length", "newQuestions", "movedQuestion", "splice", "updatedQuestions", "map", "question", "index", "ExamQuestions", "order", "reorderStatements", "questionId", "questionIndex", "findIndex", "q", "statements", "newStatements", "movedStatement", "updatedStatements", "statement", "updatedQuestion", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/questionsExam/questionsExamSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport * as questionApi from \"../../services/questionApi\";\r\n\r\nexport const fetchExamQuestionsWithoutPagination = createAsyncThunk(\r\n    \"questions/fetchExamQuestionsWithExam\",\r\n    async ({ id }, { dispatch }) => {\r\n        return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, { id, pageSize: 1000, sortOrder: 'DESC' }, (data) => {\r\n        }, false, false);\r\n    }\r\n);\r\n\r\n\r\nconst questionsExamSlice = createSlice({\r\n    name: \"questionsExam\",\r\n    initialState: {\r\n        questionsExam: [],\r\n        loading: false,\r\n        view: 'question',\r\n        showAddImagesModal: false,\r\n        folder: \"questionImage\",\r\n        selectedIndex: 0,\r\n    },\r\n    reducers: {\r\n        setQuestionsExam: (state, action) => {\r\n            state.questionsExam = action.payload;\r\n        },\r\n        setLoading: (state, action) => {\r\n            state.loading = action.payload;\r\n        },\r\n        setViewRightContent: (state, action) => {\r\n            state.view = action.payload;\r\n        },\r\n        setSelectedIndex: (state, action) => {\r\n            state.selectedIndex = action.payload;\r\n        },\r\n        reorderQuestions: (state, action) => {\r\n            const { oldIndex, newIndex } = action.payload;\r\n            console.log('Reorder Questions:', { oldIndex, newIndex });\r\n\r\n            if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0 &&\r\n                oldIndex < state.questionsExam.length && newIndex < state.questionsExam.length) {\r\n\r\n                // Tạo mảng mới với thứ tự đã thay đổi\r\n                const newQuestions = [...state.questionsExam];\r\n                const [movedQuestion] = newQuestions.splice(oldIndex, 1);\r\n                newQuestions.splice(newIndex, 0, movedQuestion);\r\n\r\n                // Cập nhật thuộc tính order cho tất cả câu hỏi\r\n                const updatedQuestions = newQuestions.map((question, index) => ({\r\n                    ...question,\r\n                    ExamQuestions: {\r\n                        ...question.ExamQuestions,\r\n                        order: index\r\n                    }\r\n                }));\r\n\r\n                state.questionsExam = updatedQuestions;\r\n            }\r\n        },\r\n        reorderStatements: (state, action) => {\r\n            const { questionId, oldIndex, newIndex } = action.payload;\r\n\r\n            const questionIndex = state.questionsExam.findIndex(q => q.id === questionId);\r\n            if (questionIndex === -1) return;\r\n\r\n            const question = state.questionsExam[questionIndex];\r\n            if (!question.statements || oldIndex === newIndex) return;\r\n\r\n            if (oldIndex >= 0 && newIndex >= 0 &&\r\n                oldIndex < question.statements.length && newIndex < question.statements.length) {\r\n\r\n                // Tạo mảng mới với thứ tự đã thay đổi\r\n                const newStatements = [...question.statements];\r\n                const [movedStatement] = newStatements.splice(oldIndex, 1);\r\n                newStatements.splice(newIndex, 0, movedStatement);\r\n\r\n                // Cập nhật thuộc tính order cho tất cả statements\r\n                const updatedStatements = newStatements.map((statement, index) => ({\r\n                    ...statement,\r\n                    order: index\r\n                }));\r\n\r\n                // Cập nhật question với statements mới\r\n                const updatedQuestion = {\r\n                    ...question,\r\n                    statements: updatedStatements\r\n                };\r\n\r\n                state.questionsExam[questionIndex] = updatedQuestion;\r\n            }\r\n        }\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchExamQuestionsWithoutPagination.pending, (state) => {\r\n                state.loading = true;\r\n            })\r\n            .addCase(fetchExamQuestionsWithoutPagination.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.questionsExam = action.payload.data;\r\n                }\r\n                state.loading = false;\r\n            })\r\n            .addCase(fetchExamQuestionsWithoutPagination.rejected, (state) => {\r\n                state.questionsExam = [];\r\n                state.loading = false;\r\n            })\r\n    },\r\n});\r\n\r\nexport const {\r\n    setQuestionsExam,\r\n    setLoading,\r\n    setViewRightContent,\r\n    setSelectedIndex,\r\n    reorderQuestions,\r\n    reorderStatements,\r\n} = questionsExamSlice.actions;\r\nexport default questionsExamSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAO,KAAKC,WAAW,MAAM,4BAA4B;AAEzD,OAAO,MAAMC,mCAAmC,GAAGJ,gBAAgB,CAC/D,sCAAsC,EACtC,OAAAK,IAAA,EAAAC,KAAA,KAAgC;EAAA,IAAzB;IAAEC;EAAG,CAAC,GAAAF,IAAA;EAAA,IAAE;IAAEG;EAAS,CAAC,GAAAF,KAAA;EACvB,OAAO,MAAMJ,UAAU,CAACM,QAAQ,EAAEL,WAAW,CAACM,mBAAmB,EAAE;IAAEF,EAAE;IAAEG,QAAQ,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAO,CAAC,EAAGC,IAAI,IAAK,CACxH,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACpB,CACJ,CAAC;AAGD,MAAMC,kBAAkB,GAAGd,WAAW,CAAC;EACnCe,IAAI,EAAE,eAAe;EACrBC,YAAY,EAAE;IACVC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,UAAU;IAChBC,kBAAkB,EAAE,KAAK;IACzBC,MAAM,EAAE,eAAe;IACvBC,aAAa,EAAE;EACnB,CAAC;EACDC,QAAQ,EAAE;IACNC,gBAAgB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MACjCD,KAAK,CAACR,aAAa,GAAGS,MAAM,CAACC,OAAO;IACxC,CAAC;IACDC,UAAU,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MAC3BD,KAAK,CAACP,OAAO,GAAGQ,MAAM,CAACC,OAAO;IAClC,CAAC;IACDE,mBAAmB,EAAEA,CAACJ,KAAK,EAAEC,MAAM,KAAK;MACpCD,KAAK,CAACN,IAAI,GAAGO,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDG,gBAAgB,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MACjCD,KAAK,CAACH,aAAa,GAAGI,MAAM,CAACC,OAAO;IACxC,CAAC;IACDI,gBAAgB,EAAEA,CAACN,KAAK,EAAEC,MAAM,KAAK;MACjC,MAAM;QAAEM,QAAQ;QAAEC;MAAS,CAAC,GAAGP,MAAM,CAACC,OAAO;MAC7CO,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;QAAEH,QAAQ;QAAEC;MAAS,CAAC,CAAC;MAEzD,IAAID,QAAQ,KAAKC,QAAQ,IAAID,QAAQ,IAAI,CAAC,IAAIC,QAAQ,IAAI,CAAC,IACvDD,QAAQ,GAAGP,KAAK,CAACR,aAAa,CAACmB,MAAM,IAAIH,QAAQ,GAAGR,KAAK,CAACR,aAAa,CAACmB,MAAM,EAAE;QAEhF;QACA,MAAMC,YAAY,GAAG,CAAC,GAAGZ,KAAK,CAACR,aAAa,CAAC;QAC7C,MAAM,CAACqB,aAAa,CAAC,GAAGD,YAAY,CAACE,MAAM,CAACP,QAAQ,EAAE,CAAC,CAAC;QACxDK,YAAY,CAACE,MAAM,CAACN,QAAQ,EAAE,CAAC,EAAEK,aAAa,CAAC;;QAE/C;QACA,MAAME,gBAAgB,GAAGH,YAAY,CAACI,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,MAAM;UAC5D,GAAGD,QAAQ;UACXE,aAAa,EAAE;YACX,GAAGF,QAAQ,CAACE,aAAa;YACzBC,KAAK,EAAEF;UACX;QACJ,CAAC,CAAC,CAAC;QAEHlB,KAAK,CAACR,aAAa,GAAGuB,gBAAgB;MAC1C;IACJ,CAAC;IACDM,iBAAiB,EAAEA,CAACrB,KAAK,EAAEC,MAAM,KAAK;MAClC,MAAM;QAAEqB,UAAU;QAAEf,QAAQ;QAAEC;MAAS,CAAC,GAAGP,MAAM,CAACC,OAAO;MAEzD,MAAMqB,aAAa,GAAGvB,KAAK,CAACR,aAAa,CAACgC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAKuC,UAAU,CAAC;MAC7E,IAAIC,aAAa,KAAK,CAAC,CAAC,EAAE;MAE1B,MAAMN,QAAQ,GAAGjB,KAAK,CAACR,aAAa,CAAC+B,aAAa,CAAC;MACnD,IAAI,CAACN,QAAQ,CAACS,UAAU,IAAInB,QAAQ,KAAKC,QAAQ,EAAE;MAEnD,IAAID,QAAQ,IAAI,CAAC,IAAIC,QAAQ,IAAI,CAAC,IAC9BD,QAAQ,GAAGU,QAAQ,CAACS,UAAU,CAACf,MAAM,IAAIH,QAAQ,GAAGS,QAAQ,CAACS,UAAU,CAACf,MAAM,EAAE;QAEhF;QACA,MAAMgB,aAAa,GAAG,CAAC,GAAGV,QAAQ,CAACS,UAAU,CAAC;QAC9C,MAAM,CAACE,cAAc,CAAC,GAAGD,aAAa,CAACb,MAAM,CAACP,QAAQ,EAAE,CAAC,CAAC;QAC1DoB,aAAa,CAACb,MAAM,CAACN,QAAQ,EAAE,CAAC,EAAEoB,cAAc,CAAC;;QAEjD;QACA,MAAMC,iBAAiB,GAAGF,aAAa,CAACX,GAAG,CAAC,CAACc,SAAS,EAAEZ,KAAK,MAAM;UAC/D,GAAGY,SAAS;UACZV,KAAK,EAAEF;QACX,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMa,eAAe,GAAG;UACpB,GAAGd,QAAQ;UACXS,UAAU,EAAEG;QAChB,CAAC;QAED7B,KAAK,CAACR,aAAa,CAAC+B,aAAa,CAAC,GAAGQ,eAAe;MACxD;IACJ;EACJ,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAACtD,mCAAmC,CAACuD,OAAO,EAAGnC,KAAK,IAAK;MAC7DA,KAAK,CAACP,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDyC,OAAO,CAACtD,mCAAmC,CAACwD,SAAS,EAAE,CAACpC,KAAK,EAAEC,MAAM,KAAK;MACvE,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBF,KAAK,CAACR,aAAa,GAAGS,MAAM,CAACC,OAAO,CAACd,IAAI;MAC7C;MACAY,KAAK,CAACP,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDyC,OAAO,CAACtD,mCAAmC,CAACyD,QAAQ,EAAGrC,KAAK,IAAK;MAC9DA,KAAK,CAACR,aAAa,GAAG,EAAE;MACxBQ,KAAK,CAACP,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACTM,gBAAgB;EAChBI,UAAU;EACVC,mBAAmB;EACnBC,gBAAgB;EAChBC,gBAAgB;EAChBe;AACJ,CAAC,GAAGhC,kBAAkB,CAACiD,OAAO;AAC9B,eAAejD,kBAAkB,CAACkD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}