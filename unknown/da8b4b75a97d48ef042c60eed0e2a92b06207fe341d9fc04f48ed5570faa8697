import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as examApi from "../../services/examApi";
import { apiHandler } from "../../utils/apiHandler";
import * as questionApi from "../../services/questionApi";

export const fetchExamQuestionsWithoutPagination = createAsyncThunk(
    "questions/fetchExamQuestionsWithExam",
    async ({ id }, { dispatch }) => {
        return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, { id, pageSize: 1000, sortOrder: 'DESC' }, (data) => {
        }, false, false);
    }
);


const questionsExamSlice = createSlice({
    name: "questionsExam",
    initialState: {
        questionsExam: [],
        loading: false,
        view: 'question',
        showAddImagesModal: false,
        folder: "questionImage",
        selectedIndex: 0,
    },
    reducers: {
        setQuestionsExam: (state, action) => {
            state.questionsExam = action.payload;
        },
        setLoading: (state, action) => {
            state.loading = action.payload;
        },
        setViewRightContent: (state, action) => {
            state.view = action.payload;
        },
        setSelectedIndex: (state, action) => {
            state.selectedIndex = action.payload;
        },
        reorderQuestions: (state, action) => {
            const { oldIndex, newIndex } = action.payload;
            console.log('Reorder Questions:', { oldIndex, newIndex });

            if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0 &&
                oldIndex < state.questionsExam.length && newIndex < state.questionsExam.length) {

                // Tạo mảng mới với thứ tự đã thay đổi
                const newQuestions = [...state.questionsExam];
                const [movedQuestion] = newQuestions.splice(oldIndex, 1);
                newQuestions.splice(newIndex, 0, movedQuestion);

                // Cập nhật thuộc tính order cho tất cả câu hỏi
                const updatedQuestions = newQuestions.map((question, index) => ({
                    ...question,
                    ExamQuestions: {
                        ...question.ExamQuestions,
                        order: index
                    }
                }));

                state.questionsExam = updatedQuestions;
            }
        },
        reorderStatements: (state, action) => {
            const { questionId, oldIndex, newIndex } = action.payload;

            const questionIndex = state.questionsExam.findIndex(q => q.id === questionId);
            if (questionIndex === -1) return;

            const question = state.questionsExam[questionIndex];
            if (!question.statements || oldIndex === newIndex) return;

            if (oldIndex >= 0 && newIndex >= 0 &&
                oldIndex < question.statements.length && newIndex < question.statements.length) {

                // Tạo mảng mới với thứ tự đã thay đổi
                const newStatements = [...question.statements];
                const [movedStatement] = newStatements.splice(oldIndex, 1);
                newStatements.splice(newIndex, 0, movedStatement);

                // Cập nhật thuộc tính order cho tất cả statements
                const updatedStatements = newStatements.map((statement, index) => ({
                    ...statement,
                    order: index
                }));

                // Cập nhật question với statements mới
                const updatedQuestion = {
                    ...question,
                    statements: updatedStatements
                };

                state.questionsExam[questionIndex] = updatedQuestion;
            }
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchExamQuestionsWithoutPagination.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchExamQuestionsWithoutPagination.fulfilled, (state, action) => {
                if (action.payload) {
                    state.questionsExam = action.payload.data;
                }
                state.loading = false;
            })
            .addCase(fetchExamQuestionsWithoutPagination.rejected, (state) => {
                state.questionsExam = [];
                state.loading = false;
            })
    },
});

export const {
    setQuestionsExam,
    setLoading,
    setViewRightContent,
    setSelectedIndex,
    reorderQuestions,
    reorderStatements,
} = questionsExamSlice.actions;
export default questionsExamSlice.reducer;
