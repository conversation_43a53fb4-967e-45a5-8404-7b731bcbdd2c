import AdminLayout from "../../../layouts/AdminLayout";
import FunctionBarAdmin from "../../../components/bar/FunctionBarAdmin";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import QuestionTable from "../../../components/table/QuestionTable";
import { fetchExamQuestions } from "../../../features/question/questionSlice";
import { useNavigate } from "react-router-dom";
import AdminModal from "../../../components/modal/AdminModal";
import AddQuestionModal from "../../../components/modal/AddQuestionModal";
import { setIsAddView } from "../../../features/filter/filterSlice";
import { setCurrentPage, setLimit, setSearch } from "src/features/question/questionSlice";
import ExamAdminLayout from "src/layouts/ExamAdminLayout";

const QuestionOfExamAdmin = () => {
    const { examId } = useParams();
    const navigate = useNavigate();
    const handleClickedDetail = () => {
        navigate(`/admin/exam-management/${examId}`);
    }
    const handleClickedPreviewExam = () => {
        navigate(`/admin/exam-management/${examId}/preview`);
    }
    const handleClickedTracking = () => {
        navigate(`/admin/exam-management/${examId}/tracking`);
    }
    const dispatch = useDispatch();
    const { isAddView } = useSelector(state => state.filter);
    const { pagination } = useSelector(state => state.questions);

    return (
        <ExamAdminLayout>
            <div className="flex-1 overflow-hidden p-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">

                    <AdminModal isOpen={isAddView} headerText={'Tạo câu hỏi mới'} onClose={() => dispatch(setIsAddView(false))} >
                        <AddQuestionModal onClose={() => dispatch(setIsAddView(false))} examId={examId} fetchQuestions={fetchExamQuestions} />
                    </AdminModal>
                    <div className="flex-shrink-0 p-6 border-b border-gray-200">
                        <div className="flex justify-between items-center mb-4">

                            <FunctionBarAdmin
                                currentPage={pagination.page}
                                totalItems={pagination.total}
                                totalPages={pagination.totalPages}
                                limit={pagination.pageSize}
            setLimit={(newLimit) => {
                        dispatch(setLimit(newLimit))
                        dispatch(setCurrentPage(1))
                    }}                                setCurrentPage={(newPage) => dispatch(setCurrentPage(newPage))}
                                setSearch={(value) => dispatch(setSearch(value))}
                            />
                        </div>
                        <QuestionTable fetchQuestions={fetchExamQuestions} examId={examId} />
                    </div>
                </div>

            </div>
        </ExamAdminLayout>

    )
}

export default QuestionOfExamAdmin;
