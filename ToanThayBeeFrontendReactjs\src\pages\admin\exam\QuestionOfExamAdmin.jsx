import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchExamQuestionsWithoutPagination } from "src/features/questionsExam/questionsExamSlice";
import { useNavigate } from "react-router-dom";
import ExamAdminLayout from "src/layouts/ExamAdminLayout";
import { useEffect } from "react";
import LeftContent from "src/components/PageQuestionsExam/LeftContent";
import RightContent from "src/components/PageQuestionsExam/RightContent";

const QuestionOfExamAdmin = () => {
    const { examId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(fetchExamQuestionsWithoutPagination({ id: examId }));
    }, [dispatch, examId]);

    return (
        <ExamAdminLayout>
            <div className="flex flex-1 overflow-hidden">
                {/* Left Panel - Form */}
                <div className="w-1/2 border-r border-gray-200 bg-white">
                    <LeftContent />
                </div>

                {/* Right Panel - Preview */}
                <div className="w-1/2">
                    <RightContent />
                </div>
            </div>
        </ExamAdminLayout>

    )
}

export default QuestionOfExamAdmin;
