import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchExamQuestionsWithoutPagination } from "src/features/questionsExam/questionsExamSlice";
import { useNavigate } from "react-router-dom";
import ExamAdminLayout from "src/layouts/ExamAdminLayout";
import { useEffect } from "react";
import LeftContent from "src/components/PageQuestionsExam/LeftContent";
import RightContent from "src/components/PageQuestionsExam/RightContent";
import LoadingData from "src/components/loading/LoadingData";
import { fetchImages } from "src/features/image/imageSlice";
import { fetchCodesByType } from "src/features/code/codeSlice";

const QuestionOfExamAdmin = () => {
    const { examId } = useParams();
    const { questionsExam, loading, folder } = useSelector((state) => state.questionsExam);
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(fetchExamQuestionsWithoutPagination({ id: examId }));
    }, [dispatch, examId]);

    useEffect(() => {
        dispatch(fetchImages(folder));
        dispatch(fetchCodesByType(["chapter", "difficulty", "grade", "exam type", "year"]));
    }, [dispatch]);

    return (
        <ExamAdminLayout>
            <LoadingData
                loading={loading}
                loadText="Đang tải danh sách câu hỏi"
                noDataText="Không có câu hỏi nào."
                isNoData={questionsExam.length > 0 ? false : true}
            >
                <div className="flex flex-1 overflow-hidden">
                    {/* Left Panel - Form */}
                    <div className="w-1/2 border-r border-gray-200 bg-white">
                        <LeftContent />
                    </div>

                    {/* Right Panel - Preview */}
                    <div className="w-1/2">
                        <RightContent />
                    </div>
                </div>
            </LoadingData>

        </ExamAdminLayout>

    )
}

export default QuestionOfExamAdmin;
