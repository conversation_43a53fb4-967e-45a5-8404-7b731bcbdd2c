{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\QuestionContent.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { selectQuestion, setSelectedQuestion } from \"src/features/examAI/examAISlice\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\nimport DropZone from \"./DropZone\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const QuestionContent = _ref => {\n  _s();\n  var _codes$chapter, _codes$chapter$find;\n  let {\n    question,\n    index\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    selectedQuestion,\n    isAddImage\n  } = useSelector(state => state.examAI);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const handleImageDrop = (dropZoneId, imageUrl) => {\n    const updatedQuestion = {\n      ...question\n    };\n    if (dropZoneId === \"question-content-\".concat(question.id)) {\n      updatedQuestion.imageUrl = imageUrl;\n    } else if (dropZoneId === \"question-solution-\".concat(question.id)) {\n      updatedQuestion.solutionImageUrl = imageUrl;\n    } else if (dropZoneId.startsWith(\"statement-\".concat(question.id, \"-\"))) {\n      const statementIndex = parseInt(dropZoneId.split('-')[3]);\n      if (updatedQuestion.statement1s && updatedQuestion.statement1s[statementIndex]) {\n        updatedQuestion.statement1s[statementIndex].imageUrl = imageUrl;\n      }\n    }\n    dispatch(setSelectedQuestion(updatedQuestion));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\\n            \".concat((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.id) === question.id ? \"bg-blue-50 border-blue-500\" : \"bg-white hover:bg-gray-50 border-gray-300\", \"\\n        \"),\n    onClick: () => dispatch(selectQuestion(question)),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-gray-700\",\n        children: [\"C\\xE2u \", index + 1]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\u0110\\u1ED9 kh\\xF3: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: question.difficulty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Ch\\u01B0\\u01A1ng:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: ((_codes$chapter = codes[\"chapter\"]) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === question.chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || \"Chưa xác định\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this), isAddImage ? /*#__PURE__*/_jsxDEV(DropZone, {\n      id: \"question-content-\".concat(question.id),\n      className: \"text-base text-gray-800 leading-relaxed\",\n      placeholder: \"Th\\u1EA3 h\\xECnh \\u1EA3nh v\\xE0o n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n      children: [/*#__PURE__*/_jsxDEV(LatexRenderer, {\n        className: \"text-gray-800\",\n        text: question.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 21\n      }, this), question.imageUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: question.imageUrl,\n        alt: \"Question\",\n        className: \"mt-2 max-w-full h-auto rounded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-base text-gray-800 leading-relaxed\",\n      children: [/*#__PURE__*/_jsxDEV(LatexRenderer, {\n        className: \"text-gray-800\",\n        text: question.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 21\n      }, this), question.imageUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: question.imageUrl,\n        alt: \"Question\",\n        className: \"mt-2 max-w-full h-auto rounded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(SortableStatementsContainer, {\n      question: question\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this), question.solution && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"L\\u1EDDi gi\\u1EA3i:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 21\n      }, this), \" \", /*#__PURE__*/_jsxDEV(MarkdownPreviewWithMath, {\n        content: question.solution\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 17\n    }, this)]\n  }, question.id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 9\n  }, this);\n};\n_s(QuestionContent, \"MTz42/6xoRQRjp3Dnj9r7syqXC4=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = QuestionContent;\nexport default QuestionContent;\nvar _c;\n$RefreshReg$(_c, \"QuestionContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "selectQuestion", "setSelectedQuestion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "SortableStatementsContainer", "DropZone", "jsxDEV", "_jsxDEV", "QuestionContent", "_ref", "_s", "_codes$chapter", "_codes$chapter$find", "question", "index", "dispatch", "selectedQuestion", "isAddImage", "state", "examAI", "codes", "handleImageDrop", "dropZoneId", "imageUrl", "updatedQuestion", "concat", "id", "solutionImageUrl", "startsWith", "statementIndex", "parseInt", "split", "statement1s", "className", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "difficulty", "find", "c", "code", "chapter", "description", "placeholder", "text", "content", "src", "alt", "solution", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/QuestionContent.jsx"], "sourcesContent": ["\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { selectQuestion, setSelectedQuestion } from \"src/features/examAI/examAISlice\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"src/components/latex/MarkDownPreview\";\r\nimport SortableStatementsContainer from \"./SortableStatementsContainer\";\r\nimport DropZone from \"./DropZone\";\r\n\r\nexport const QuestionContent = ({ question, index }) => {\r\n    const dispatch = useDispatch();\r\n    const { selectedQuestion, isAddImage } = useSelector((state) => state.examAI);\r\n\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    const handleImageDrop = (dropZoneId, imageUrl) => {\r\n        const updatedQuestion = { ...question };\r\n\r\n        if (dropZoneId === `question-content-${question.id}`) {\r\n            updatedQuestion.imageUrl = imageUrl;\r\n        } else if (dropZoneId === `question-solution-${question.id}`) {\r\n            updatedQuestion.solutionImageUrl = imageUrl;\r\n        } else if (dropZoneId.startsWith(`statement-${question.id}-`)) {\r\n            const statementIndex = parseInt(dropZoneId.split('-')[3]);\r\n            if (updatedQuestion.statement1s && updatedQuestion.statement1s[statementIndex]) {\r\n                updatedQuestion.statement1s[statementIndex].imageUrl = imageUrl;\r\n            }\r\n        }\r\n\r\n        dispatch(setSelectedQuestion(updatedQuestion));\r\n    };\r\n\r\n    return (\r\n        <div\r\n            key={question.id}\r\n            className={`\r\n            flex flex-col gap-3 p-4 rounded-lg border transition cursor-pointer\r\n            ${selectedQuestion?.id === question.id\r\n                    ? \"bg-blue-50 border-blue-500\"\r\n                    : \"bg-white hover:bg-gray-50 border-gray-300\"\r\n                }\r\n        `}\r\n            onClick={() => dispatch(selectQuestion(question))}\r\n        >\r\n            {/* Thông tin câu hỏi */}\r\n            <div className=\"flex flex-wrap items-center text-sm text-gray-500 gap-x-4 gap-y-1\">\r\n                <span className=\"font-medium text-gray-700\">Câu {index + 1}</span>\r\n                <span>Độ khó: <span className=\"text-gray-700\">{question.difficulty}</span></span>\r\n                <span>\r\n                    Chương:{\" \"}\r\n                    <span className=\"text-gray-700\">\r\n                        {codes[\"chapter\"]?.find(c => c.code === question.chapter)?.description || \"Chưa xác định\"}\r\n                    </span>\r\n                </span>\r\n            </div>\r\n\r\n            {/* Nội dung câu hỏi */}\r\n            {isAddImage ? (\r\n                <DropZone\r\n                    id={`question-content-${question.id}`}\r\n                    className=\"text-base text-gray-800 leading-relaxed\"\r\n                    placeholder=\"Thả hình ảnh vào nội dung câu hỏi\"\r\n                >\r\n                    <LatexRenderer className=\"text-gray-800\" text={question.content} />\r\n                    {question.imageUrl && (\r\n                        <img src={question.imageUrl} alt=\"Question\" className=\"mt-2 max-w-full h-auto rounded\" />\r\n                    )}\r\n                </DropZone>\r\n            ) : (\r\n                <div className=\"text-base text-gray-800 leading-relaxed\">\r\n                    <LatexRenderer className=\"text-gray-800\" text={question.content} />\r\n                    {question.imageUrl && (\r\n                        <img src={question.imageUrl} alt=\"Question\" className=\"mt-2 max-w-full h-auto rounded\" />\r\n                    )}\r\n                </div>\r\n            )}\r\n\r\n            {/* Statement: A, B, C,... */}\r\n            <SortableStatementsContainer question={question} />\r\n\r\n            {/* Lời giải (nếu có) */}\r\n            {question.solution && (\r\n                <div className=\"mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded\">\r\n                    <span className=\"font-semibold\">Lời giải:</span>{\" \"}\r\n                    <MarkdownPreviewWithMath content={question.solution} />\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default QuestionContent;"], "mappings": ";;AACA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,iCAAiC;AACrF,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,OAAO,MAAMC,eAAe,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,mBAAA;EAAA,IAAxB;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAAL,IAAA;EAC/C,MAAMM,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,gBAAgB;IAAEC;EAAW,CAAC,GAAGnB,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAE7E,MAAM;IAAEC;EAAM,CAAC,GAAGtB,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAErD,MAAMC,eAAe,GAAGA,CAACC,UAAU,EAAEC,QAAQ,KAAK;IAC9C,MAAMC,eAAe,GAAG;MAAE,GAAGX;IAAS,CAAC;IAEvC,IAAIS,UAAU,yBAAAG,MAAA,CAAyBZ,QAAQ,CAACa,EAAE,CAAE,EAAE;MAClDF,eAAe,CAACD,QAAQ,GAAGA,QAAQ;IACvC,CAAC,MAAM,IAAID,UAAU,0BAAAG,MAAA,CAA0BZ,QAAQ,CAACa,EAAE,CAAE,EAAE;MAC1DF,eAAe,CAACG,gBAAgB,GAAGJ,QAAQ;IAC/C,CAAC,MAAM,IAAID,UAAU,CAACM,UAAU,cAAAH,MAAA,CAAcZ,QAAQ,CAACa,EAAE,MAAG,CAAC,EAAE;MAC3D,MAAMG,cAAc,GAAGC,QAAQ,CAACR,UAAU,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,IAAIP,eAAe,CAACQ,WAAW,IAAIR,eAAe,CAACQ,WAAW,CAACH,cAAc,CAAC,EAAE;QAC5EL,eAAe,CAACQ,WAAW,CAACH,cAAc,CAAC,CAACN,QAAQ,GAAGA,QAAQ;MACnE;IACJ;IAEAR,QAAQ,CAACd,mBAAmB,CAACuB,eAAe,CAAC,CAAC;EAClD,CAAC;EAED,oBACIjB,OAAA;IAEI0B,SAAS,oGAAAR,MAAA,CAEP,CAAAT,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEU,EAAE,MAAKb,QAAQ,CAACa,EAAE,GAC5B,4BAA4B,GAC5B,2CAA2C,eAEvD;IACEQ,OAAO,EAAEA,CAAA,KAAMnB,QAAQ,CAACf,cAAc,CAACa,QAAQ,CAAC,CAAE;IAAAsB,QAAA,gBAGlD5B,OAAA;MAAK0B,SAAS,EAAC,mEAAmE;MAAAE,QAAA,gBAC9E5B,OAAA;QAAM0B,SAAS,EAAC,2BAA2B;QAAAE,QAAA,GAAC,SAAI,EAACrB,KAAK,GAAG,CAAC;MAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEhC,OAAA;QAAA4B,QAAA,GAAM,uBAAQ,eAAA5B,OAAA;UAAM0B,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAEtB,QAAQ,CAAC2B;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjFhC,OAAA;QAAA4B,QAAA,GAAM,mBACK,EAAC,GAAG,eACX5B,OAAA;UAAM0B,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC1B,EAAAxB,cAAA,GAAAS,KAAK,CAAC,SAAS,CAAC,cAAAT,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkB8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK9B,QAAQ,CAAC+B,OAAO,CAAC,cAAAhC,mBAAA,uBAAxDA,mBAAA,CAA0DiC,WAAW,KAAI;QAAe;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLtB,UAAU,gBACPV,OAAA,CAACF,QAAQ;MACLqB,EAAE,sBAAAD,MAAA,CAAsBZ,QAAQ,CAACa,EAAE,CAAG;MACtCO,SAAS,EAAC,yCAAyC;MACnDa,WAAW,EAAC,gEAAmC;MAAAX,QAAA,gBAE/C5B,OAAA,CAACL,aAAa;QAAC+B,SAAS,EAAC,eAAe;QAACc,IAAI,EAAElC,QAAQ,CAACmC;MAAQ;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAClE1B,QAAQ,CAACU,QAAQ,iBACdhB,OAAA;QAAK0C,GAAG,EAAEpC,QAAQ,CAACU,QAAS;QAAC2B,GAAG,EAAC,UAAU;QAACjB,SAAS,EAAC;MAAgC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC3F;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,gBAEXhC,OAAA;MAAK0B,SAAS,EAAC,yCAAyC;MAAAE,QAAA,gBACpD5B,OAAA,CAACL,aAAa;QAAC+B,SAAS,EAAC,eAAe;QAACc,IAAI,EAAElC,QAAQ,CAACmC;MAAQ;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAClE1B,QAAQ,CAACU,QAAQ,iBACdhB,OAAA;QAAK0C,GAAG,EAAEpC,QAAQ,CAACU,QAAS;QAAC2B,GAAG,EAAC,UAAU;QAACjB,SAAS,EAAC;MAAgC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC3F;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR,eAGDhC,OAAA,CAACH,2BAA2B;MAACS,QAAQ,EAAEA;IAAS;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGlD1B,QAAQ,CAACsC,QAAQ,iBACd5C,OAAA;MAAK0B,SAAS,EAAC,uFAAuF;MAAAE,QAAA,gBAClG5B,OAAA;QAAM0B,SAAS,EAAC,eAAe;QAAAE,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAG,eACpDhC,OAAA,CAACJ,uBAAuB;QAAC6C,OAAO,EAAEnC,QAAQ,CAACsC;MAAS;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CACR;EAAA,GApDI1B,QAAQ,CAACa,EAAE;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAqDf,CAAC;AAEd,CAAC;AAAA7B,EAAA,CAhFYF,eAAe;EAAA,QACPT,WAAW,EACaD,WAAW,EAElCA,WAAW;AAAA;AAAAsD,EAAA,GAJpB5C,eAAe;AAkF5B,eAAeA,eAAe;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}