import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { ImagePlus } from 'lucide-react';

const DropZone = ({ id, children, onDrop, className = "", placeholder = "Thả hình ảnh vào đây" }) => {
    const { isOver, setNodeRef } = useDroppable({
        id: id,
    });

    const style = {
        backgroundColor: isOver ? 'rgba(59, 130, 246, 0.1)' : 'transparent',
        border: isOver ? '2px dashed #3b82f6' : '2px dashed transparent',
        transition: 'all 0.2s ease',
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            className={`relative min-h-[60px] rounded-lg p-2 ${className}`}
        >
            {children}
            {isOver && (
                <div className="absolute inset-0 flex items-center justify-center bg-blue-50 bg-opacity-90 rounded-lg border-2 border-dashed border-blue-400">
                    <div className="flex items-center gap-2 text-blue-600">
                        <ImagePlus size={20} />
                        <span className="text-sm font-medium">{placeholder}</span>
                    </div>
                </div>
            )}
        </div>
    );
};

export default DropZone;
