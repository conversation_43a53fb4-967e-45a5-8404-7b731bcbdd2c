import React, { useState, useRef, useEffect } from "react";
import { ImagePlus, Upload } from "lucide-react";
import MarkdownPreviewWithMath from "../latex/MarkDownPreview";

const SolutionEditor = ({ solution, onSolutionChange, isAddImage }) => {
    const [isDraggingOver, setIsDraggingOver] = useState(false);
    const [dragPosition, setDragPosition] = useState(null);
    const textareaRef = useRef(null);
    const previewRef = useRef(null);

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDraggingOver(false);

        const draggedImage = e.dataTransfer.getData("text/plain");
        if (!draggedImage || !onSolutionChange) return;

        // Tính toán vị trí chèn ảnh
        let insertPosition = 0;
        
        if (dragPosition !== null) {
            insertPosition = dragPosition;
        } else {
            // Nếu không có vị trí cụ thể, chèn vào cuối
            insertPosition = solution ? solution.length : 0;
        }

        // Tạo markdown image syntax
        const imageMarkdown = `\n![Ảnh](${draggedImage})\n`;
        
        // Chèn vào vị trí
        const newSolution = solution 
            ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition)
            : imageMarkdown;

        onSolutionChange(newSolution);
        setDragPosition(null);
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        if (isAddImage) {
            // Tính toán vị trí dựa trên mouse position
            const rect = e.currentTarget.getBoundingClientRect();
            const y = e.clientY - rect.top;
            
            // Ước tính vị trí trong text dựa trên tọa độ Y
            const lineHeight = 24; // Ước tính chiều cao dòng
            const lineIndex = Math.floor(y / lineHeight);
            
            // Tính toán position trong text
            if (solution) {
                const lines = solution.split('\n');
                let position = 0;
                for (let i = 0; i < Math.min(lineIndex, lines.length); i++) {
                    position += lines[i].length + 1; // +1 cho \n
                }
                setDragPosition(position);
            }
        }
    };

    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (isAddImage && e.dataTransfer.types.includes('text/plain')) {
            setIsDraggingOver(true);
        }
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!e.currentTarget.contains(e.relatedTarget)) {
            setIsDraggingOver(false);
            setDragPosition(null);
        }
    };

    const handleTextareaChange = (e) => {
        onSolutionChange(e.target.value);
    };

    // Hiển thị drop indicator
    const getDropIndicatorStyle = () => {
        if (!isDraggingOver || dragPosition === null) return {};
        
        const lineHeight = 24;
        const lines = solution ? solution.split('\n') : [''];
        let currentPos = 0;
        let lineIndex = 0;
        
        for (let i = 0; i < lines.length; i++) {
            if (currentPos + lines[i].length >= dragPosition) {
                lineIndex = i;
                break;
            }
            currentPos += lines[i].length + 1;
        }
        
        return {
            position: 'absolute',
            top: `${lineIndex * lineHeight + 8}px`,
            left: '8px',
            right: '8px',
            height: '2px',
            backgroundColor: '#3b82f6',
            zIndex: 10,
            borderRadius: '1px'
        };
    };

    if (!isAddImage) {
        // Chế độ xem bình thường
        return (
            <div className="mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded">
                <span className="font-semibold">Lời giải:</span>{" "}
                <MarkdownPreviewWithMath content={solution} />
            </div>
        );
    }

    return (
        <div className="mt-2 space-y-3">
            <div className="text-sm font-semibold text-green-700">Lời giải:</div>
            
            {/* Editor với drop zone */}
            <div
                className={`relative border rounded-lg transition-all duration-200 ${
                    isDraggingOver 
                        ? "border-2 border-dashed border-blue-400 bg-blue-50" 
                        : "border-gray-300 hover:border-blue-300"
                }`}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
            >
                {/* Drop indicator */}
                {isDraggingOver && <div style={getDropIndicatorStyle()} />}
                
                {/* Drag overlay */}
                {isDraggingOver && (
                    <div className="absolute inset-0 bg-blue-50 bg-opacity-90 flex items-center justify-center z-20 rounded-lg">
                        <div className="text-center">
                            <Upload className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                            <p className="text-blue-600 font-medium">Thả ảnh vào đây</p>
                            <p className="text-blue-400 text-sm">Ảnh sẽ được chèn vào vị trí này</p>
                        </div>
                    </div>
                )}
                
                {/* Textarea */}
                <textarea
                    ref={textareaRef}
                    value={solution || ''}
                    onChange={handleTextareaChange}
                    placeholder="Nhập lời giải (hỗ trợ Markdown và LaTeX)..."
                    className="w-full p-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px]"
                    style={{ lineHeight: '24px' }}
                />
                
                {/* Hint */}
                {!solution && (
                    <div className="absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none">
                        <ImagePlus className="w-4 h-4" />
                        <span>Kéo ảnh vào để chèn</span>
                    </div>
                )}
            </div>
            
            {/* Preview */}
            {solution && (
                <div className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                    <div className="text-xs text-gray-500 mb-2">Xem trước:</div>
                    <MarkdownPreviewWithMath content={solution} />
                </div>
            )}
        </div>
    );
};

export default SolutionEditor;
