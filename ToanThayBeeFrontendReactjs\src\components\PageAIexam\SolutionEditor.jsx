import React, { useState, useRef, useEffect } from "react";
import { ImagePlus, Upload } from "lucide-react";
import MarkdownPreviewWithMath from "../latex/MarkDownPreview";

const SolutionEditor = ({ solution, onSolutionChange, isAddImage }) => {
    const [isDraggingOver, setIsDraggingOver] = useState(false);
    const [dragPosition, setDragPosition] = useState(null);
    const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
    const textareaRef = useRef(null);
    const previewRef = useRef(null);
    const containerRef = useRef(null);

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDraggingOver(false);

        const draggedImage = e.dataTransfer.getData("text/plain");
        if (!draggedImage || !onSolutionChange) return;

        // Tính toán vị trí chèn <PERSON>nh
        let insertPosition = 0;
        
        if (dragPosition !== null) {
            insertPosition = dragPosition;
        } else {
            // Nếu không có vị trí cụ thể, chèn vào cuối
            insertPosition = solution ? solution.length : 0;
        }

        // Tạo markdown image syntax
        const imageMarkdown = `\n![Ảnh](${draggedImage})\n`;
        
        // Chèn vào vị trí
        const newSolution = solution 
            ? solution.slice(0, insertPosition) + imageMarkdown + solution.slice(insertPosition)
            : imageMarkdown;

        onSolutionChange(newSolution);
        setDragPosition(null);
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();

        if (isAddImage && textareaRef.current) {
            const textarea = textareaRef.current;
            const rect = textarea.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Tính toán vị trí character dựa trên mouse position
            const position = getCharacterPositionFromCoordinates(textarea, x, y);
            setDragPosition(position);
            setCursorPosition({ x, y });
        }
    };

    // Hàm tính toán vị trí character từ tọa độ mouse
    const getCharacterPositionFromCoordinates = (textarea, x, y) => {
        const style = window.getComputedStyle(textarea);
        const lineHeight = parseInt(style.lineHeight) || 24;
        const fontSize = parseInt(style.fontSize) || 14;
        const charWidth = fontSize * 0.6; // Ước tính độ rộng character

        const lineIndex = Math.floor(y / lineHeight);
        const charIndex = Math.floor(x / charWidth);

        if (!solution) return 0;

        const lines = solution.split('\n');
        let position = 0;

        // Tính position đến dòng hiện tại
        for (let i = 0; i < Math.min(lineIndex, lines.length); i++) {
            position += lines[i].length + 1; // +1 cho \n
        }

        // Thêm vị trí trong dòng hiện tại
        if (lineIndex < lines.length) {
            position += Math.min(charIndex, lines[lineIndex].length);
        }

        return Math.max(0, Math.min(position, solution.length));
    };

    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (isAddImage && e.dataTransfer.types.includes('text/plain')) {
            setIsDraggingOver(true);
        }
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!e.currentTarget.contains(e.relatedTarget)) {
            setIsDraggingOver(false);
            setDragPosition(null);
        }
    };

    const handleTextareaChange = (e) => {
        onSolutionChange(e.target.value);
    };

    // Hiển thị cursor dọc nhấp nháy
    const getCursorStyle = () => {
        if (!isDraggingOver || !cursorPosition) return { display: 'none' };

        return {
            position: 'absolute',
            left: `${cursorPosition.x}px`,
            top: `${cursorPosition.y}px`,
            width: '2px',
            height: '20px',
            backgroundColor: '#3b82f6',
            zIndex: 10,
            animation: 'blink 1s infinite',
            pointerEvents: 'none'
        };
    };

    if (!isAddImage) {
        // Chế độ xem bình thường
        return (
            <div className="mt-2 text-sm text-green-700 border-l-4 border-green-400 pl-3 bg-green-50 py-2 rounded">
                <span className="font-semibold">Lời giải:</span>{" "}
                <MarkdownPreviewWithMath content={solution} />
            </div>
        );
    }

    return (
        <div className="mt-2 space-y-3">
            <div className="text-sm font-semibold text-green-700">Lời giải:</div>
            
            {/* Editor với drop zone */}
            <div
                className={`relative border rounded-lg transition-all duration-200 ${
                    isDraggingOver
                        ? "border-2 border-dashed border-blue-400 bg-blue-50"
                        : "border-gray-300 hover:border-blue-300"
                }`}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                ref={containerRef}
            >
                {/* Cursor dọc nhấp nháy */}
                <div style={getCursorStyle()} />

                {/* Textarea */}
                <textarea
                    ref={textareaRef}
                    value={solution || ''}
                    onChange={handleTextareaChange}
                    placeholder="Nhập lời giải (hỗ trợ Markdown và LaTeX)..."
                    className={`w-full p-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px] ${
                        isDraggingOver ? 'bg-transparent' : ''
                    }`}
                    style={{ lineHeight: '24px' }}
                />

                {/* Hint */}
                {!solution && !isDraggingOver && (
                    <div className="absolute bottom-3 right-3 flex items-center gap-2 text-gray-400 text-sm pointer-events-none">
                        <ImagePlus className="w-4 h-4" />
                        <span>Kéo ảnh vào để chèn</span>
                    </div>
                )}
            </div>

            {/* CSS cho animation nhấp nháy */}
            <style jsx>{`
                @keyframes blink {
                    0%, 50% { opacity: 1; }
                    51%, 100% { opacity: 0; }
                }
            `}</style>
            
            {/* Preview */}
            {solution && (
                <div className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                    <div className="text-xs text-gray-500 mb-2">Xem trước:</div>
                    <MarkdownPreviewWithMath content={solution} />
                </div>
            )}
        </div>
    );
};

export default SolutionEditor;
