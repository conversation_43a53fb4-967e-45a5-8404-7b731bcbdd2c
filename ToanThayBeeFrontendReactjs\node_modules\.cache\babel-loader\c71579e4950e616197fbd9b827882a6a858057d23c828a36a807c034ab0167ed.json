{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\AddExamAdmin.jsx\";\nimport AdminLayout from \"src/layouts/AdminLayout\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AddExamAdmin = () => {\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9\",\n      children: \"T\\u1EA1o \\u0111\\u1EC1 thi m\\u1EDBi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n};\n_c = AddExamAdmin;\nexport default AddExamAdmin;\nvar _c;\n$RefreshReg$(_c, \"AddExamAdmin\");", "map": {"version": 3, "names": ["AdminLayout", "jsxDEV", "_jsxDEV", "AddExamAdmin", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/AddExamAdmin.jsx"], "sourcesContent": ["import AdminLayout from \"src/layouts/AdminLayout\";\r\n\r\n\r\nexport const AddExamAdmin = () => {\r\n    \r\n\r\n    return (\r\n        <AdminLayout>\r\n            <div className=\"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9\">\r\n                Tạo đề thi mới\r\n            </div>\r\n        </AdminLayout>\r\n    )\r\n\r\n}\r\n\r\nexport default AddExamAdmin;"], "mappings": ";AAAA,OAAOA,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlD,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAG9B,oBACID,OAAA,CAACF,WAAW;IAAAI,QAAA,eACRF,OAAA;MAAKG,SAAS,EAAC,+DAA+D;MAAAD,QAAA,EAAC;IAE/E;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAGtB,CAAC;AAAAC,EAAA,GAXYP,YAAY;AAazB,eAAeA,YAAY;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}