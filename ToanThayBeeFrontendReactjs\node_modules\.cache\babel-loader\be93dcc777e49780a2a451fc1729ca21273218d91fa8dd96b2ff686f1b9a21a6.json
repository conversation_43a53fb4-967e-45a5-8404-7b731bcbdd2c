{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\AddExamAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport { postExam } from \"src/features/exam/examSlice\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport ImageUpload from \"src/components/image/UploadImage\";\nimport UploadPdf from \"src/components/UploadPdf\";\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport { ArrowLeft, Save, Eye, FileText, Plus, Trash2 } from \"lucide-react\";\n\n// Left Panel Component - Form Controls\n\n// Right Panel Component - Live Preview\n\n// Main Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AddExamAdmin = () => {\n  _s();\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" bg-gray-50 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 flex flex-col \".concat(closeSidebar ? \"ml-[104px]\" : \"ml-64\"),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/admin/exam-management'),\n            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(ArrowLeft, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"T\\u1EA1o \\u0111\\u1EC1 thi m\\u1EDBi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"T\\u1EA1o \\u0111\\u1EC1 thi v\\u1EDBi c\\xE2u h\\u1ECFi v\\xE0 c\\u1EA5u h\\xECnh chi ti\\u1EBFt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\xE1dfasdf\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\xE1dfasdf\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 9\n  }, this);\n};\n_s(AddExamAdmin, \"Naix3msqY0pflSvPtEy536j5qZI=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AddExamAdmin;\nexport default AddExamAdmin;\nvar _c;\n$RefreshReg$(_c, \"AddExamAdmin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "useNavigate", "AdminSidebar", "fetchCodesByType", "postExam", "DropMenuBarAdmin", "SuggestInputBarAdmin", "ImageUpload", "UploadPdf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "ArrowLeft", "Save", "Eye", "FileText", "Plus", "Trash2", "jsxDEV", "_jsxDEV", "AddExamAdmin", "_s", "closeSidebar", "state", "sidebar", "navigate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "concat", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/AddExamAdmin.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AdminSidebar from \"src/components/sidebar/AdminSidebar\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport { postExam } from \"src/features/exam/examSlice\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport ImageUpload from \"src/components/image/UploadImage\";\r\nimport UploadPdf from \"src/components/UploadPdf\";\r\nimport LatexRenderer from \"src/components/latex/RenderLatex\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport { ArrowLeft, Save, Eye, FileText, Plus, Trash2 } from \"lucide-react\";\r\n\r\n// Left Panel Component - Form Controls\r\n\r\n// Right Panel Component - Live Preview\r\n\r\n\r\n// Main Component\r\nexport const AddExamAdmin = () => {\r\n\r\n    const { closeSidebar } = useSelector((state) => state.sidebar);\r\n    const navigate = useNavigate();\r\n    return (\r\n        <div className=\" bg-gray-50 flex flex-col\">\r\n            <AdminSidebar />\r\n            <div className={`bg-gray-50 flex flex-col ${closeSidebar ? \"ml-[104px]\" : \"ml-64\"}`}>\r\n                {/* Header */}\r\n                <div className=\"bg-white border-b border-gray-200 px-6 py-4\">\r\n                    <div className=\"flex items-center gap-4\">\r\n                        <button\r\n                            onClick={() => navigate('/admin/exam-management')}\r\n                            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\r\n                        >\r\n                            <ArrowLeft className=\"w-5 h-5 text-gray-600\" />\r\n                        </button>\r\n                        <div>\r\n                            <h1 className=\"text-2xl font-bold text-gray-900\">Tạo đề thi mới</h1>\r\n                            <p className=\"text-gray-600\">Tạo đề thi với câu hỏi và cấu hình chi tiết</p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Main Content */}\r\n                <div className=\"flex-1 flex overflow-hidden\">\r\n                    <div>\r\n                        ádfasdf\r\n                    </div>\r\n                    <div>\r\n                        ádfasdf\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AddExamAdmin;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,cAAc,MAAM,uCAAuC;AAClE,SAASC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;;AAE3E;;AAEA;;AAGA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAE9B,MAAM;IAAEC;EAAa,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC9D,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,oBACIiB,OAAA;IAAKO,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACtCR,OAAA,CAAChB,YAAY;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBZ,OAAA;MAAKO,SAAS,8BAAAM,MAAA,CAA8BV,YAAY,GAAG,YAAY,GAAG,OAAO,CAAG;MAAAK,QAAA,gBAEhFR,OAAA;QAAKO,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eACxDR,OAAA;UAAKO,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpCR,OAAA;YACIc,OAAO,EAAEA,CAAA,KAAMR,QAAQ,CAAC,wBAAwB,CAAE;YAClDC,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eAE9DR,OAAA,CAACP,SAAS;cAACc,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACTZ,OAAA;YAAAQ,QAAA,gBACIR,OAAA;cAAIO,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpEZ,OAAA;cAAGO,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA2C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNZ,OAAA;QAAKO,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBACxCR,OAAA;UAAAQ,QAAA,EAAK;QAEL;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNZ,OAAA;UAAAQ,QAAA,EAAK;QAEL;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACV,EAAA,CApCWD,YAAY;EAAA,QAEIpB,WAAW,EACnBE,WAAW;AAAA;AAAAgC,EAAA,GAHnBd,YAAY;AAsCzB,eAAeA,YAAY;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}