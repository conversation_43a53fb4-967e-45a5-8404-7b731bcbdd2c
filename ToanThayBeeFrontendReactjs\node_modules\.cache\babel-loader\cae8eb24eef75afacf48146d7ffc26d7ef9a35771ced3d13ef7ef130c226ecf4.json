{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\Header.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { ChevronLeft } from 'lucide-react';\nimport Button from \"src/components/PageAIexam/Button\";\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useState, useEffect } from 'react';\nimport { setViewEdit, setIsChange, saveExam, commitExam } from 'src/features/examAI/examAISlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst arrayOfObjectEqualUnordered = (arr1, arr2) => {\n  if (arr1.length !== arr2.length) return false;\n  const sortedArr1 = arr1.map(obj => JSON.stringify(obj)).sort();\n  const sortedArr2 = arr2.map(obj => JSON.stringify(obj)).sort();\n  return sortedArr1.every((value, index) => value === sortedArr2[index]);\n};\nconst objectEqual = (obj1, obj2) => {\n  return JSON.stringify(obj1) === JSON.stringify(obj2);\n};\nconst Header = _ref => {\n  _s();\n  let {\n    title = \"Đề thi AI\",\n    tabNavigate = true\n  } = _ref;\n  const navigate = useNavigate();\n  const {\n    questions,\n    questionsEdited,\n    viewEdit,\n    exam,\n    editedExam,\n    isChange,\n    loadingSave,\n    loadingCommit\n  } = useSelector(state => state.examAI);\n  const dispatch = useDispatch();\n  const {\n    closeSidebar\n  } = useSelector(state => state.sidebar);\n  useEffect(() => {\n    if (isChange === true) return;\n    dispatch(setIsChange(!arrayOfObjectEqualUnordered(questions, questionsEdited)));\n  }, [questions, questionsEdited]);\n  useEffect(() => {\n    if (isChange === true) return;\n    dispatch(setIsChange(!objectEqual(exam, editedExam)));\n  }, [exam, editedExam]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed z-50 top-0  bg-white h-16 px-4 flex items-center justify-between border-b border-gray-300 \".concat(closeSidebar ? \"w-[calc(100vw_-_104px)]\" : \"w-[calc(100vw_-16rem)]\"),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => navigate(-1),\n        type: \"none\",\n        children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n          className: \"w-5 h-5 text-black\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"font-bold\",\n        children: \"Qu\\u1EA3n l\\xFD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this), tabNavigate && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => dispatch(commitExam(exam.id)),\n        type: \"commit\",\n        disabled: loadingCommit,\n        loading: loadingCommit,\n        children: \"C\\u1EADp nh\\u1EADt\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => dispatch(saveExam({\n          examData: editedExam,\n          questions: questionsEdited,\n          id: exam.id\n        })),\n        type: !isChange ? \"disabled\" : \"save\",\n        disabled: !isChange || loadingSave,\n        loading: loadingSave,\n        children: \"L\\u01B0u thay \\u0111\\u1ED5i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 17\n    }, this), tabNavigate && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute left-1/2 -translate-x-1/2 bottom-[-1.25rem] z-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => dispatch(setViewEdit('exam')),\n        className: \"px-4 py-1 \".concat(viewEdit === 'exam' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200', \" text-sm border border-gray-300\"),\n        children: \"\\u0110\\u1EC1 thi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => dispatch(setViewEdit('question')),\n        className: \"px-4 py-1 \".concat(viewEdit === 'question' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200', \" text-sm  border border-gray-300\"),\n        children: \"C\\xE2u h\\u1ECFi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 9\n  }, this);\n};\n_s(Header, \"H9IQBo4G/V4MbGTLQunEN7v8Uh4=\", false, function () {\n  return [useNavigate, useSelector, useDispatch, useSelector];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useNavigate", "ChevronLeft", "<PERSON><PERSON>", "useSelector", "useDispatch", "useState", "useEffect", "setViewEdit", "setIsChange", "saveExam", "commitExam", "jsxDEV", "_jsxDEV", "arrayOfObjectEqualUnordered", "arr1", "arr2", "length", "sortedArr1", "map", "obj", "JSON", "stringify", "sort", "sortedArr2", "every", "value", "index", "objectEqual", "obj1", "obj2", "Header", "_ref", "_s", "title", "tabNavigate", "navigate", "questions", "questionsEdited", "viewEdit", "exam", "editedExam", "isChange", "loadingSave", "loadingCommit", "state", "examAI", "dispatch", "closeSidebar", "sidebar", "className", "concat", "children", "onClick", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "disabled", "loading", "examData", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/Header.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { ChevronLeft } from 'lucide-react';\r\nimport Button from \"src/components/PageAIexam/Button\";\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { useState, useEffect } from 'react';\r\nimport { setViewEdit, setIsChange, saveExam, commitExam } from 'src/features/examAI/examAISlice';\r\n\r\nconst arrayOfObjectEqualUnordered = (arr1, arr2) => {\r\n    if (arr1.length !== arr2.length) return false;\r\n    const sortedArr1 = arr1.map(obj => JSON.stringify(obj)).sort();\r\n    const sortedArr2 = arr2.map(obj => JSON.stringify(obj)).sort();\r\n    return sortedArr1.every((value, index) => value === sortedArr2[index]);\r\n};\r\n\r\nconst objectEqual = (obj1, obj2) => {\r\n    return JSON.stringify(obj1) === JSON.stringify(obj2);\r\n};\r\n\r\nconst Header = ({ title = \"Đề thi AI\", tabNavigate = true }) => {\r\n    const navigate = useNavigate();\r\n    const { questions, questionsEdited, viewEdit, exam, editedExam, isChange, loadingSave, loadingCommit } = useSelector((state) => state.examAI);\r\n    const dispatch = useDispatch();\r\n    const { closeSidebar } = useSelector((state) => state.sidebar);\r\n\r\n    useEffect(() => {\r\n        if (isChange === true) return\r\n        dispatch(setIsChange(!arrayOfObjectEqualUnordered(questions, questionsEdited)));\r\n    }, [questions, questionsEdited]);\r\n\r\n    useEffect(() => {\r\n        if (isChange === true) return\r\n        dispatch(setIsChange(!objectEqual(exam, editedExam)));\r\n    }, [exam, editedExam]);\r\n\r\n    return (\r\n        <div className={`fixed z-50 top-0  bg-white h-16 px-4 flex items-center justify-between border-b border-gray-300 ${closeSidebar ? \"w-[calc(100vw_-_104px)]\" : \"w-[calc(100vw_-16rem)]\"}`}>\r\n            <div className=\"flex items-center gap-4\">\r\n                <Button onClick={() => navigate(-1)} type=\"none\">\r\n                    <ChevronLeft className=\"w-5 h-5 text-black\" />\r\n                </Button>\r\n                <p className=\"font-bold\">Quản lý</p>\r\n                <p className=\"text-gray-500\">{title}</p>\r\n            </div>\r\n            {tabNavigate && (\r\n                <div className=\"flex items-center gap-4\">\r\n                    {/* <button\r\n                        onClick={() => dispatch(saveExam({ examData: editedExam, questions: questionsEdited, id: exam.id }))}\r\n                        className={`px-2 py-1 text-sm rounded-md transition-colors duration-300\r\n                ${isChange ? 'bg-emerald-500 hover:bg-emerald-700' : 'bg-gray-300 cursor-not-allowed'}`}\r\n                        disabled={!isChange}\r\n                    >\r\n                        <span className=\"text-white\">Lưu thay đổi</span>\r\n                    </button> */}\r\n\r\n                    <Button\r\n                        onClick={() => dispatch(commitExam(exam.id))}\r\n                        type=\"commit\"\r\n                        disabled={loadingCommit}\r\n                        loading={loadingCommit}\r\n\r\n                    >\r\n                        Cập nhật\r\n                    </Button>\r\n                    <Button\r\n                        onClick={() => dispatch(saveExam({ examData: editedExam, questions: questionsEdited, id: exam.id }))}\r\n                        type={!isChange ? \"disabled\" : \"save\"}\r\n                        disabled={!isChange || loadingSave}\r\n                        loading={loadingSave}\r\n                    >\r\n                        Lưu thay đổi\r\n                    </Button>\r\n                </div>\r\n            )}\r\n\r\n            {/* 🟦 Nút giữa cạnh dưới header */}\r\n            {tabNavigate && (\r\n                <div className=\"absolute left-1/2 -translate-x-1/2 bottom-[-1.25rem] z-50\">\r\n                    <button\r\n                        onClick={() => dispatch(setViewEdit('exam'))}\r\n                        className={`px-4 py-1 ${viewEdit === 'exam' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200'} text-sm border border-gray-300`}\r\n                    >\r\n                        Đề thi\r\n                    </button>\r\n                    <button\r\n                        onClick={() => dispatch(setViewEdit('question'))}\r\n                        className={`px-4 py-1 ${viewEdit === 'question' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-white text-black hover:bg-gray-200'} text-sm  border border-gray-300`}\r\n                    >\r\n                        Câu hỏi\r\n                    </button>\r\n                </div>\r\n            )}\r\n        </div>\r\n\r\n    );\r\n};\r\nexport default Header;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,MAAM,MAAM,kCAAkC;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,MAAMC,2BAA2B,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAChD,IAAID,IAAI,CAACE,MAAM,KAAKD,IAAI,CAACC,MAAM,EAAE,OAAO,KAAK;EAC7C,MAAMC,UAAU,GAAGH,IAAI,CAACI,GAAG,CAACC,GAAG,IAAIC,IAAI,CAACC,SAAS,CAACF,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;EAC9D,MAAMC,UAAU,GAAGR,IAAI,CAACG,GAAG,CAACC,GAAG,IAAIC,IAAI,CAACC,SAAS,CAACF,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;EAC9D,OAAOL,UAAU,CAACO,KAAK,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKD,KAAK,KAAKF,UAAU,CAACG,KAAK,CAAC,CAAC;AAC1E,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAChC,OAAOT,IAAI,CAACC,SAAS,CAACO,IAAI,CAAC,KAAKR,IAAI,CAACC,SAAS,CAACQ,IAAI,CAAC;AACxD,CAAC;AAED,MAAMC,MAAM,GAAGC,IAAA,IAAiD;EAAAC,EAAA;EAAA,IAAhD;IAAEC,KAAK,GAAG,WAAW;IAAEC,WAAW,GAAG;EAAK,CAAC,GAAAH,IAAA;EACvD,MAAMI,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoC,SAAS;IAAEC,eAAe;IAAEC,QAAQ;IAAEC,IAAI;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAGxC,WAAW,CAAEyC,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAC7I,MAAMC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2C;EAAa,CAAC,GAAG5C,WAAW,CAAEyC,KAAK,IAAKA,KAAK,CAACI,OAAO,CAAC;EAE9D1C,SAAS,CAAC,MAAM;IACZ,IAAImC,QAAQ,KAAK,IAAI,EAAE;IACvBK,QAAQ,CAACtC,WAAW,CAAC,CAACK,2BAA2B,CAACuB,SAAS,EAAEC,eAAe,CAAC,CAAC,CAAC;EACnF,CAAC,EAAE,CAACD,SAAS,EAAEC,eAAe,CAAC,CAAC;EAEhC/B,SAAS,CAAC,MAAM;IACZ,IAAImC,QAAQ,KAAK,IAAI,EAAE;IACvBK,QAAQ,CAACtC,WAAW,CAAC,CAACmB,WAAW,CAACY,IAAI,EAAEC,UAAU,CAAC,CAAC,CAAC;EACzD,CAAC,EAAE,CAACD,IAAI,EAAEC,UAAU,CAAC,CAAC;EAEtB,oBACI5B,OAAA;IAAKqC,SAAS,qGAAAC,MAAA,CAAqGH,YAAY,GAAG,yBAAyB,GAAG,wBAAwB,CAAG;IAAAI,QAAA,gBACrLvC,OAAA;MAAKqC,SAAS,EAAC,yBAAyB;MAAAE,QAAA,gBACpCvC,OAAA,CAACV,MAAM;QAACkD,OAAO,EAAEA,CAAA,KAAMjB,QAAQ,CAAC,CAAC,CAAC,CAAE;QAACkB,IAAI,EAAC,MAAM;QAAAF,QAAA,eAC5CvC,OAAA,CAACX,WAAW;UAACgD,SAAS,EAAC;QAAoB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACT7C,OAAA;QAAGqC,SAAS,EAAC,WAAW;QAAAE,QAAA,EAAC;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpC7C,OAAA;QAAGqC,SAAS,EAAC,eAAe;QAAAE,QAAA,EAAElB;MAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,EACLvB,WAAW,iBACRtB,OAAA;MAAKqC,SAAS,EAAC,yBAAyB;MAAAE,QAAA,gBAUpCvC,OAAA,CAACV,MAAM;QACHkD,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAACpC,UAAU,CAAC6B,IAAI,CAACmB,EAAE,CAAC,CAAE;QAC7CL,IAAI,EAAC,QAAQ;QACbM,QAAQ,EAAEhB,aAAc;QACxBiB,OAAO,EAAEjB,aAAc;QAAAQ,QAAA,EAE1B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7C,OAAA,CAACV,MAAM;QACHkD,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAACrC,QAAQ,CAAC;UAAEoD,QAAQ,EAAErB,UAAU;UAAEJ,SAAS,EAAEC,eAAe;UAAEqB,EAAE,EAAEnB,IAAI,CAACmB;QAAG,CAAC,CAAC,CAAE;QACrGL,IAAI,EAAE,CAACZ,QAAQ,GAAG,UAAU,GAAG,MAAO;QACtCkB,QAAQ,EAAE,CAAClB,QAAQ,IAAIC,WAAY;QACnCkB,OAAO,EAAElB,WAAY;QAAAS,QAAA,EACxB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,EAGAvB,WAAW,iBACRtB,OAAA;MAAKqC,SAAS,EAAC,2DAA2D;MAAAE,QAAA,gBACtEvC,OAAA;QACIwC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAACvC,WAAW,CAAC,MAAM,CAAC,CAAE;QAC7C0C,SAAS,eAAAC,MAAA,CAAeZ,QAAQ,KAAK,MAAM,GAAG,0CAA0C,GAAG,uCAAuC,oCAAkC;QAAAa,QAAA,EACvK;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7C,OAAA;QACIwC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAACvC,WAAW,CAAC,UAAU,CAAC,CAAE;QACjD0C,SAAS,eAAAC,MAAA,CAAeZ,QAAQ,KAAK,UAAU,GAAG,0CAA0C,GAAG,uCAAuC,qCAAmC;QAAAa,QAAA,EAC5K;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAGd,CAAC;AAACzB,EAAA,CA5EIF,MAAM;EAAA,QACS9B,WAAW,EAC6EG,WAAW,EACnGC,WAAW,EACHD,WAAW;AAAA;AAAA2D,EAAA,GAJlChC,MAAM;AA6EZ,eAAeA,MAAM;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}