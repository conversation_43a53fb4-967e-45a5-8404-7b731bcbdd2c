'use strict'
import { Model } from 'sequelize'
export default (sequelize, DataTypes) => {
  class QuestionReport extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      QuestionReport.belongsTo(models.Question, { foreignKey: 'questionId', as: 'question' })
      QuestionReport.belongsTo(models.User, { foreignKey: 'userId', as: 'user' })
    }
  }
  QuestionReport.init({
    userId: DataTypes.INTEGER,
    questionId: DataTypes.INTEGER,
    content: DataTypes.TEXT,
    createdAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: 'QuestionReport',
    tableName: 'questionReport',
    timestamps: false,
  })
  return QuestionReport
}