import React, { useMemo } from 'react';
import MarkdownPreview from "@uiw/react-markdown-preview";
import rehypeKatex from "rehype-katex";
import remarkMath from "remark-math";
import "@uiw/react-markdown-preview/markdown.css";
import "katex/dist/katex.min.css";
import "../../styles/markdown-preview.css";
import NoTranslate from '../utils/NoTranslate';

const MarkdownPreviewWithMath = ({ content, className='', style }) => {

    const processedContent = useMemo(() => {
        if (!content) return "";

        // Replace \( \) with $ $ for inline math
        // Replace \[ \] with $$ $$ for block math
        let processed = content
            .replace(/\\\(/g, "$")
            .replace(/\\\)/g, "$")
            .replace(/\\\[/g, "$$")
            .replace(/\\\]/g, "$$");

        return processed;
    }, [content]);

    return (
        <NoTranslate as="div" className={className} style={style}>
            <MarkdownPreview
                source={processedContent}
                remarkPlugins={[remarkMath]}
                rehypePlugins={[rehypeKatex]}
            />
        </NoTranslate>
    );
};

export default MarkdownPreviewWithMath;
