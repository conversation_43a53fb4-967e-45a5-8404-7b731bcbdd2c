import React, { useMemo } from 'react';
import MarkdownPreview from "@uiw/react-markdown-preview";
import rehypeKatex from "rehype-katex";
import remarkMath from "remark-math";
import "@uiw/react-markdown-preview/markdown.css";
import "katex/dist/katex.min.css";
import "../../styles/markdown-preview.css";
import NoTranslate from '../utils/NoTranslate';

const MarkdownPreviewWithMath = ({ content, className='', style }) => {

    const processedContent = useMemo(() => {
        if (!content) return "";

        // Replace \( \) with $ $ for inline math
        // Replace \[ \] with $$ $$ for block math
        let processed = content
            .replace(/\\\(/g, "$")
            .replace(/\\\)/g, "$")
            .replace(/\\\[/g, "$$")
            .replace(/\\\]/g, "$$");

        return processed;
    }, [content]);

    return (
        <NoTranslate as="div" className={className} style={style}>
            <MarkdownPreview
                source={processedContent}
                remarkPlugins={[remarkMath]}
                rehypePlugins={[
                    [rehypeKatex, {
                        strict: false, // Tắt strict mode để không warning với Unicode
                        throwOnError: false, // Không throw error
                        errorColor: '#cc0000',
                        macros: {
                            "\\RR": "\\mathbb{R}",
                            "\\NN": "\\mathbb{N}",
                            "\\ZZ": "\\mathbb{Z}",
                            "\\QQ": "\\mathbb{Q}",
                            "\\CC": "\\mathbb{C}"
                        }
                    }]
                ]}
            />
        </NoTranslate>
    );
};

export default MarkdownPreviewWithMath;
