import { uploadImage, cleanupUploadedFiles, getAllImagesFromFolder } from "../utils/imageUpload.js"
import { getAllPdfsFromFolder } from "../utils/pdfUpload.js"

export async function getAllImages(req, res) {
    const folder = req.params.folder
    if (!folder || typeof folder !== 'string') {
        return res.status(400).json({ message: 'Folder name is required.' })
    }

    const images = await getAllImagesFromFolder(folder)
    return res.status(200).json({
        message: "Get all images successfully",
        images,
        folder,
    })
}

export async function getAllImagesFolders(req, res) {
    const folders = req.body.folders
    if (!folders || !Array.isArray(folders)) {
        return res.status(400).json({ message: 'Folders name is required.' })
    }

    const images = await Promise.all(folders.map(async (folder) => {
        return await getAllImagesFromFolder(folder)
    }))

    return res.status(200).json({
        message: "Get all images successfully",
        images,
    })
}

export async function getAllPdfs(req, res) {
    const folder = req.params.folder
    if (!folder || typeof folder !== 'string') {
        return res.status(400).json({ message: 'Folder name is required.' })
    }

    const pdfs = await getAllPdfsFromFolder(folder)
    return res.status(200).json({
        message: "Get all pdfs successfully",
        pdfs,
        folder,
    })
}


export async function uploadImageToFirebase(req, res) {
    const newImageFile = req.file
    const folder = req.body.folder
    if (!newImageFile) {
        throw new Error('Please select an image to upload')
    }

    let newImageUrl = await uploadImage(newImageFile, folder)

    if (!newImageUrl) {
        throw new Error('Upload image failed')
    }

    return res.status(201).json({
        message: "Upload image successfully",
        file: newImageUrl,
        folder,
    })
}

export async function uploadMultipleImagesToFirebase(req, res) {
    const newImageFiles = req.files
    const folder = req.body.folder
    // console.log("newImageFiles", newImageFiles)

    if (!newImageFiles) {
        throw new Error('Please select an image to upload')
    }

    let newImageUrls = await Promise.all(newImageFiles.map(async (file) => {
        return await uploadImage(file, folder)
    }))

    if (!newImageUrls) {
        throw new Error('Upload image failed')
    }

    return res.status(201).json({
        message: "Upload images successfully",
        files: newImageUrls,
        folder,
    })
}

export async function uploadBase64ImagesToFirebase(req, res) {
    const { images, folder } = req.body;
    // console.log(req.body)
    console.log("images", images)
    console.log("folder", folder)

    if (!Array.isArray(images) || images.length === 0) {
        return res.status(400).json({
            message: "Missing images array in request body",
        });
    }


    if (!folder) {
        return res.status(400).json({
            message: "Missing folder in request body",
        });
    }

    const uploadedUrls = [];

    for (let i = 0; i < images.length; i++) {
        const base64 = images[i];

        const matches = base64.match(/^data:(image\/\w+);base64,(.+)$/);
        const mimeType = matches ? matches[1] : 'image/png';
        const base64Data = matches ? matches[2] : base64;

        const buffer = Buffer.from(base64Data, 'base64');

        // Gọi lại hàm uploadImage có sẵn của bạn
        const imageUrl = await uploadImage(buffer, folder);

        if (!imageUrl) {
            throw new Error(`Upload failed for image ${i + 1}`);
        }

        uploadedUrls.push(imageUrl);
    }

    return res.status(201).json({
        message: "Upload images successfully",
        files: uploadedUrls,
        folder,
    });
}


export async function deleteImage(req, res) {
    const { imageUrl } = req.body

    if (!imageUrl) {
        throw new Error('Please select an image to delete')
    }

    await cleanupUploadedFiles([imageUrl])

    return res.status(200).json({
        message: "Delete image successfully",
        file: imageUrl,
    })
}