'use strict'

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    // Xoá bảng classTuition
    await queryInterface.dropTable('classTuition');
  },

  async down(queryInterface, Sequelize) {
    // Tạo lại bảng classTuition nếu cần rollback
    await queryInterface.createTable('classTuition', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      classId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'class',
          key: 'id'
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      },
      month: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Tháng áp dụng học phí (định dạng YYYY-MM)'
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: '<PERSON><PERSON> tiền học phí'
      },
      note: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '<PERSON><PERSON> chú về học phí'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });
  }
};
