{"ast": null, "code": "import { useMemo, useLayoutEffect, useEffect, useRef, useCallback } from 'react';\nfunction useCombinedRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  return useMemo(() => node => {\n    refs.forEach(ref => ref(node));\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  refs);\n}\n\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === '[object Window]' ||\n  // In Electron context the Window object serializes to [object global]\n  elementString === '[object global]';\n}\nfunction isNode(node) {\n  return 'nodeType' in node;\n}\nfunction getWindow(target) {\n  var _target$ownerDocument, _target$ownerDocument2;\n  if (!target) {\n    return window;\n  }\n  if (isWindow(target)) {\n    return target;\n  }\n  if (!isNode(target)) {\n    return window;\n  }\n  return (_target$ownerDocument = (_target$ownerDocument2 = target.ownerDocument) == null ? void 0 : _target$ownerDocument2.defaultView) != null ? _target$ownerDocument : window;\n}\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document;\n}\nfunction isHTMLElement(node) {\n  if (isWindow(node)) {\n    return false;\n  }\n  return node instanceof getWindow(node).HTMLElement;\n}\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement;\n}\nfunction getOwnerDocument(target) {\n  if (!target) {\n    return document;\n  }\n  if (isWindow(target)) {\n    return target.document;\n  }\n  if (!isNode(target)) {\n    return document;\n  }\n  if (isDocument(target)) {\n    return target;\n  }\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n  return document;\n}\n\n/**\r\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\r\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\r\n */\n\nconst useIsomorphicLayoutEffect = canUseDOM ? useLayoutEffect : useEffect;\nfunction useEvent(handler) {\n  const handlerRef = useRef(handler);\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n  return useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return handlerRef.current == null ? void 0 : handlerRef.current(...args);\n  }, []);\n}\nfunction useInterval() {\n  const intervalRef = useRef(null);\n  const set = useCallback((listener, duration) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n  return [set, clear];\n}\nfunction useLatestValue(value, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [value];\n  }\n  const valueRef = useRef(value);\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n  return valueRef;\n}\nfunction useLazyMemo(callback, dependencies) {\n  const valueRef = useRef();\n  return useMemo(() => {\n    const newValue = callback(valueRef.current);\n    valueRef.current = newValue;\n    return newValue;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...dependencies]);\n}\nfunction useNodeRef(onChange) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef(null);\n  const setNodeRef = useCallback(element => {\n    if (element !== node.current) {\n      onChangeHandler == null ? void 0 : onChangeHandler(element, node.current);\n    }\n    node.current = element;\n  },\n  //eslint-disable-next-line\n  []);\n  return [node, setNodeRef];\n}\nfunction usePrevious(value) {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\nlet ids = {};\nfunction useUniqueId(prefix, value) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n    return prefix + \"-\" + id;\n  }, [prefix, value]);\n}\nfunction createAdjustmentFn(modifier) {\n  return function (object) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n    return adjustments.reduce((accumulator, adjustment) => {\n      const entries = Object.entries(adjustment);\n      for (const [key, valueAdjustment] of entries) {\n        const value = accumulator[key];\n        if (value != null) {\n          accumulator[key] = value + modifier * valueAdjustment;\n        }\n      }\n      return accumulator;\n    }, {\n      ...object\n    });\n  };\n}\nconst add = /*#__PURE__*/createAdjustmentFn(1);\nconst subtract = /*#__PURE__*/createAdjustmentFn(-1);\nfunction hasViewportRelativeCoordinates(event) {\n  return 'clientX' in event && 'clientY' in event;\n}\nfunction isKeyboardEvent(event) {\n  if (!event) {\n    return false;\n  }\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\nfunction isTouchEvent(event) {\n  if (!event) {\n    return false;\n  }\n  const {\n    TouchEvent\n  } = getWindow(event.target);\n  return TouchEvent && event instanceof TouchEvent;\n}\n\n/**\r\n * Returns the normalized x and y coordinates for mouse and touch events.\r\n */\n\nfunction getEventCoordinates(event) {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.touches[0];\n      return {\n        x,\n        y\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.changedTouches[0];\n      return {\n        x,\n        y\n      };\n    }\n  }\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  return null;\n}\nconst CSS = /*#__PURE__*/Object.freeze({\n  Translate: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n      const {\n        x,\n        y\n      } = transform;\n      return \"translate3d(\" + (x ? Math.round(x) : 0) + \"px, \" + (y ? Math.round(y) : 0) + \"px, 0)\";\n    }\n  },\n  Scale: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n      const {\n        scaleX,\n        scaleY\n      } = transform;\n      return \"scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\";\n    }\n  },\n  Transform: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n      return [CSS.Translate.toString(transform), CSS.Scale.toString(transform)].join(' ');\n    }\n  },\n  Transition: {\n    toString(_ref) {\n      let {\n        property,\n        duration,\n        easing\n      } = _ref;\n      return property + \" \" + duration + \"ms \" + easing;\n    }\n  }\n});\nconst SELECTOR = 'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\nfunction findFirstFocusableNode(element) {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n  return element.querySelector(SELECTOR);\n}\nexport { CSS, add, canUseDOM, findFirstFocusableNode, getEventCoordinates, getOwnerDocument, getWindow, hasViewportRelativeCoordinates, isDocument, isHTMLElement, isKeyboardEvent, isNode, isSVGElement, isTouchEvent, isWindow, subtract, useCombinedRefs, useEvent, useInterval, useIsomorphicLayoutEffect, useLatestValue, useLazyMemo, useNodeRef, usePrevious, useUniqueId };", "map": {"version": 3, "names": ["useCombinedRefs", "refs", "Array", "_len", "_key", "arguments", "useMemo", "node", "for<PERSON>ach", "ref", "canUseDOM", "window", "document", "createElement", "isWindow", "element", "elementString", "Object", "prototype", "toString", "call", "isNode", "getWindow", "target", "_target$ownerDocument", "_target$ownerDocument2", "ownerDocument", "defaultView", "isDocument", "Document", "isHTMLElement", "HTMLElement", "isSVGElement", "SVGElement", "getOwnerDocument", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useEvent", "handler", "handler<PERSON>ef", "useRef", "current", "useCallback", "args", "useInterval", "intervalRef", "set", "listener", "duration", "setInterval", "clear", "clearInterval", "useLatestValue", "value", "dependencies", "valueRef", "useLazyMemo", "callback", "newValue", "useNodeRef", "onChange", "onChangeHandler", "setNodeRef", "usePrevious", "ids", "useUniqueId", "prefix", "id", "createAdjustmentFn", "modifier", "object", "adjustments", "reduce", "accumulator", "adjustment", "entries", "key", "valueAdjustment", "add", "subtract", "hasViewportRelativeCoordinates", "event", "isKeyboardEvent", "KeyboardEvent", "isTouchEvent", "TouchEvent", "getEventCoordinates", "touches", "length", "clientX", "x", "clientY", "y", "changedTouches", "CSS", "freeze", "Translate", "transform", "Math", "round", "Scale", "scaleX", "scaleY", "Transform", "join", "Transition", "_ref", "property", "easing", "SELECTOR", "findFirstFocusableNode", "matches", "querySelector"], "sources": ["C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\hooks\\useCombinedRefs.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\execution-context\\canUseDOM.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\type-guards\\isWindow.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\type-guards\\isNode.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\execution-context\\getWindow.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\type-guards\\isDocument.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\type-guards\\isHTMLElement.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\type-guards\\isSVGElement.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\execution-context\\getOwnerDocument.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\hooks\\useIsomorphicLayoutEffect.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\hooks\\useEvent.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\hooks\\useInterval.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\hooks\\useLatestValue.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\hooks\\useLazyMemo.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\hooks\\useNodeRef.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\hooks\\usePrevious.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\hooks\\useUniqueId.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\adjustment.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\event\\hasViewportRelativeCoordinates.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\event\\isKeyboardEvent.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\event\\isTouchEvent.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\coordinates\\getEventCoordinates.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\css.ts", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\node_modules\\@dnd-kit\\utilities\\src\\focus\\findFirstFocusableNode.ts"], "sourcesContent": ["import {useMemo} from 'react';\n\nexport function useCombinedRefs<T>(\n  ...refs: ((node: T) => void)[]\n): (node: T) => void {\n  return useMemo(\n    () => (node: T) => {\n      refs.forEach((ref) => ref(node));\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    refs\n  );\n}\n", "// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nexport const canUseDOM =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined';\n", "export function isWindow(element: Object): element is typeof window {\n  const elementString = Object.prototype.toString.call(element);\n  return (\n    elementString === '[object Window]' ||\n    // In Electron context the Window object serializes to [object global]\n    elementString === '[object global]'\n  );\n}\n", "export function isNode(node: Object): node is Node {\n  return 'nodeType' in node;\n}\n", "import {isWindow} from '../type-guards/isWindow';\nimport {isNode} from '../type-guards/isNode';\n\nexport function getWindow(target: Event['target']): typeof window {\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return target.ownerDocument?.defaultView ?? window;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isDocument(node: Node): node is Document {\n  const {Document} = getWindow(node);\n\n  return node instanceof Document;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nimport {isWindow} from './isWindow';\n\nexport function isHTMLElement(node: Node | Window): node is HTMLElement {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isSVGElement(node: Node): node is SVGElement {\n  return node instanceof getWindow(node).SVGElement;\n}\n", "import {\n  isWindow,\n  isHTMLElement,\n  isDocument,\n  isNode,\n  isSVGElement,\n} from '../type-guards';\n\nexport function getOwnerDocument(target: Event['target']): Document {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n", "import {useEffect, useLayoutEffect} from 'react';\n\nimport {canUseDOM} from '../execution-context';\n\n/**\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\n */\nexport const useIsomorphicLayoutEffect = canUseDOM\n  ? useLayoutEffect\n  : useEffect;\n", "import {useCallback, useRef} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useEvent<T extends Function>(handler: T | undefined) {\n  const handlerRef = useRef<T | undefined>(handler);\n\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n\n  return useCallback(function (...args: any) {\n    return handlerRef.current?.(...args);\n  }, []);\n}\n", "import {useCallback, useRef} from 'react';\n\nexport function useInterval() {\n  const intervalRef = useRef<number | null>(null);\n\n  const set = useCallback((listener: Function, duration: number) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n\n  return [set, clear] as const;\n}\n", "import {useRef} from 'react';\nimport type {DependencyList} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useLatestValue<T extends any>(\n  value: T,\n  dependencies: DependencyList = [value]\n) {\n  const valueRef = useRef<T>(value);\n\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n\n  return valueRef;\n}\n", "import {useMemo, useRef} from 'react';\n\nexport function useLazyMemo<T>(\n  callback: (prevValue: T | undefined) => T,\n  dependencies: any[]\n) {\n  const valueRef = useRef<T>();\n\n  return useMemo(\n    () => {\n      const newValue = callback(valueRef.current);\n      valueRef.current = newValue;\n\n      return newValue;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...dependencies]\n  );\n}\n", "import {useRef, useCallback} from 'react';\n\nimport {useEvent} from './useEvent';\n\nexport function useNodeRef(\n  onChange?: (\n    newElement: HTMLElement | null,\n    previousElement: HTMLElement | null\n  ) => void\n) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef<HTMLElement | null>(null);\n  const setNodeRef = useCallback(\n    (element: HTMLElement | null) => {\n      if (element !== node.current) {\n        onChangeHandler?.(element, node.current);\n      }\n\n      node.current = element;\n    },\n    //eslint-disable-next-line\n    []\n  );\n\n  return [node, setNodeRef] as const;\n}\n", "import {useRef, useEffect} from 'react';\n\nexport function usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n", "import {useMemo} from 'react';\n\nlet ids: Record<string, number> = {};\n\nexport function useUniqueId(prefix: string, value?: string) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n\n    return `${prefix}-${id}`;\n  }, [prefix, value]);\n}\n", "function createAdjustmentFn(modifier: number) {\n  return <T extends Record<U, number>, U extends string>(\n    object: T,\n    ...adjustments: Partial<T>[]\n  ): T => {\n    return adjustments.reduce<T>(\n      (accumulator, adjustment) => {\n        const entries = Object.entries(adjustment) as [U, number][];\n\n        for (const [key, valueAdjustment] of entries) {\n          const value = accumulator[key];\n\n          if (value != null) {\n            accumulator[key] = (value + modifier * valueAdjustment) as T[U];\n          }\n        }\n\n        return accumulator;\n      },\n      {\n        ...object,\n      }\n    );\n  };\n}\n\nexport const add = createAdjustmentFn(1);\nexport const subtract = createAdjustmentFn(-1);\n", "export function hasViewportRelativeCoordinates(\n  event: Event\n): event is Event & Pick<PointerEvent, 'clientX' | 'clientY'> {\n  return 'clientX' in event && 'clientY' in event;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isKeyboardEvent(\n  event: Event | undefined | null\n): event is KeyboardEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {KeyboardEvent} = getWindow(event.target);\n\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isTouchEvent(\n  event: Event | undefined | null\n): event is TouchEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {TouchEvent} = getWindow(event.target);\n\n  return TouchEvent && event instanceof TouchEvent;\n}\n", "import type {Coordinates} from './types';\nimport {isTouchEvent, hasViewportRelativeCoordinates} from '../event';\n\n/**\n * Returns the normalized x and y coordinates for mouse and touch events.\n */\nexport function getEventCoordinates(event: Event): Coordinates | null {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {clientX: x, clientY: y} = event.touches[0];\n\n      return {\n        x,\n        y,\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {clientX: x, clientY: y} = event.changedTouches[0];\n\n      return {\n        x,\n        y,\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n    };\n  }\n\n  return null;\n}\n", "export type Transform = {\n  x: number;\n  y: number;\n  scaleX: number;\n  scaleY: number;\n};\n\nexport interface Transition {\n  property: string;\n  easing: string;\n  duration: number;\n}\n\nexport const CSS = Object.freeze({\n  Translate: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {x, y} = transform;\n\n      return `translate3d(${x ? Math.round(x) : 0}px, ${\n        y ? Math.round(y) : 0\n      }px, 0)`;\n    },\n  },\n  Scale: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {scaleX, scaleY} = transform;\n\n      return `scaleX(${scaleX}) scaleY(${scaleY})`;\n    },\n  },\n  Transform: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      return [\n        CSS.Translate.toString(transform),\n        CSS.Scale.toString(transform),\n      ].join(' ');\n    },\n  },\n  Transition: {\n    toString({property, duration, easing}: Transition) {\n      return `${property} ${duration}ms ${easing}`;\n    },\n  },\n});\n", "const SELECTOR =\n  'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\n\nexport function findFirstFocusableNode(\n  element: HTMLElement\n): HTMLElement | null {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n"], "mappings": ";SAEgBA,gBAAA;oCACXC,IAAA,OAAAC,KAAA,CAAAC,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;IAAAH,IAAA,CAAAG,IAAA,IAAAC,SAAA,CAAAD,IAAA;;EAEH,OAAOE,OAAO,CACZ,MAAOC,IAAD;IACJN,IAAI,CAACO,OAAL,CAAcC,GAAD,IAASA,GAAG,CAACF,IAAD,CAAzB;GAFU;EAAA;EAKZN,IALY,CAAd;AAOD;;ACZD;AACA,MAAaS,SAAS,GACpB,OAAOC,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACC,QAAd,KAA2B,WAD3B,IAEA,OAAOD,MAAM,CAACC,QAAP,CAAgBC,aAAvB,KAAyC,WAHpC;SCDSC,SAASC,OAAA;EACvB,MAAMC,aAAa,GAAGC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BL,OAA/B,CAAtB;EACA,OACEC,aAAa,KAAK,iBAAlB;EAAA;EAEAA,aAAa,KAAK,iBAHpB;AAKD;SCPeK,OAAOd,IAAA;EACrB,OAAO,cAAcA,IAArB;AACD;SCCee,UAAUC,MAAA;;EACxB,IAAI,CAACA,MAAL,EAAa;IACX,OAAOZ,MAAP;;EAGF,IAAIG,QAAQ,CAACS,MAAD,CAAZ,EAAsB;IACpB,OAAOA,MAAP;;EAGF,IAAI,CAACF,MAAM,CAACE,MAAD,CAAX,EAAqB;IACnB,OAAOZ,MAAP;;EAGF,QAAAa,qBAAA,IAAAC,sBAAA,GAAOF,MAAM,CAACG,aAAd,qBAAOD,sBAAA,CAAsBE,WAA7B,YAAAH,qBAAA,GAA4Cb,MAA5C;AACD;SCfeiB,WAAWrB,IAAA;EACzB,MAAM;IAACsB;MAAYP,SAAS,CAACf,IAAD,CAA5B;EAEA,OAAOA,IAAI,YAAYsB,QAAvB;AACD;SCFeC,cAAcvB,IAAA;EAC5B,IAAIO,QAAQ,CAACP,IAAD,CAAZ,EAAoB;IAClB,OAAO,KAAP;;EAGF,OAAOA,IAAI,YAAYe,SAAS,CAACf,IAAD,CAAT,CAAgBwB,WAAvC;AACD;SCReC,aAAazB,IAAA;EAC3B,OAAOA,IAAI,YAAYe,SAAS,CAACf,IAAD,CAAT,CAAgB0B,UAAvC;AACD;SCIeC,iBAAiBX,MAAA;EAC/B,IAAI,CAACA,MAAL,EAAa;IACX,OAAOX,QAAP;;EAGF,IAAIE,QAAQ,CAACS,MAAD,CAAZ,EAAsB;IACpB,OAAOA,MAAM,CAACX,QAAd;;EAGF,IAAI,CAACS,MAAM,CAACE,MAAD,CAAX,EAAqB;IACnB,OAAOX,QAAP;;EAGF,IAAIgB,UAAU,CAACL,MAAD,CAAd,EAAwB;IACtB,OAAOA,MAAP;;EAGF,IAAIO,aAAa,CAACP,MAAD,CAAb,IAAyBS,YAAY,CAACT,MAAD,CAAzC,EAAmD;IACjD,OAAOA,MAAM,CAACG,aAAd;;EAGF,OAAOd,QAAP;AACD;;AC1BD;;;;;AAIA,MAAauB,yBAAyB,GAAGzB,SAAS,GAC9C0B,eAD8C,GAE9CC,SAFG;SCJSC,SAA6BC,OAAA;EAC3C,MAAMC,UAAU,GAAGC,MAAM,CAAgBF,OAAhB,CAAzB;EAEAJ,yBAAyB,CAAC;IACxBK,UAAU,CAACE,OAAX,GAAqBH,OAArB;GADuB,CAAzB;EAIA,OAAOI,WAAW,CAAC;sCAAaC,IAAA,OAAA1C,KAAA,CAAAC,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAAwC,IAAA,CAAAxC,IAAA,IAAAC,SAAA,CAAAD,IAAA;;IAC9B,OAAOoC,UAAU,CAACE,OAAlB,oBAAOF,UAAU,CAACE,OAAX,CAAqB,GAAGE,IAAxB,CAAP;GADgB,EAEf,EAFe,CAAlB;AAGD;SCZeC,YAAA;EACd,MAAMC,WAAW,GAAGL,MAAM,CAAgB,IAAhB,CAA1B;EAEA,MAAMM,GAAG,GAAGJ,WAAW,CAAC,CAACK,QAAD,EAAqBC,QAArB;IACtBH,WAAW,CAACJ,OAAZ,GAAsBQ,WAAW,CAACF,QAAD,EAAWC,QAAX,CAAjC;GADqB,EAEpB,EAFoB,CAAvB;EAIA,MAAME,KAAK,GAAGR,WAAW,CAAC;IACxB,IAAIG,WAAW,CAACJ,OAAZ,KAAwB,IAA5B,EAAkC;MAChCU,aAAa,CAACN,WAAW,CAACJ,OAAb,CAAb;MACAI,WAAW,CAACJ,OAAZ,GAAsB,IAAtB;;GAHqB,EAKtB,EALsB,CAAzB;EAOA,OAAO,CAACK,GAAD,EAAMI,KAAN,CAAP;AACD;SCZeE,eACdC,KAAA,EACAC,YAAA;MAAAA,YAAA;IAAAA,YAAA,GAA+B,CAACD,KAAD;;EAE/B,MAAME,QAAQ,GAAGf,MAAM,CAAIa,KAAJ,CAAvB;EAEAnB,yBAAyB,CAAC;IACxB,IAAIqB,QAAQ,CAACd,OAAT,KAAqBY,KAAzB,EAAgC;MAC9BE,QAAQ,CAACd,OAAT,GAAmBY,KAAnB;;GAFqB,EAItBC,YAJsB,CAAzB;EAMA,OAAOC,QAAP;AACD;SChBeC,YACdC,QAAA,EACAH,YAAA;EAEA,MAAMC,QAAQ,GAAGf,MAAM,EAAvB;EAEA,OAAOnC,OAAO,CACZ;IACE,MAAMqD,QAAQ,GAAGD,QAAQ,CAACF,QAAQ,CAACd,OAAV,CAAzB;IACAc,QAAQ,CAACd,OAAT,GAAmBiB,QAAnB;IAEA,OAAOA,QAAP;GALU;EAAA;EAQZ,CAAC,GAAGJ,YAAJ,CARY,CAAd;AAUD;SCdeK,WACdC,QAAA;EAKA,MAAMC,eAAe,GAAGxB,QAAQ,CAACuB,QAAD,CAAhC;EACA,MAAMtD,IAAI,GAAGkC,MAAM,CAAqB,IAArB,CAAnB;EACA,MAAMsB,UAAU,GAAGpB,WAAW,CAC3B5B,OAAD;IACE,IAAIA,OAAO,KAAKR,IAAI,CAACmC,OAArB,EAA8B;MAC5BoB,eAAe,QAAf,YAAAA,eAAe,CAAG/C,OAAH,EAAYR,IAAI,CAACmC,OAAjB,CAAf;;IAGFnC,IAAI,CAACmC,OAAL,GAAe3B,OAAf;GAN0B;EAAA;EAS5B,EAT4B,CAA9B;EAYA,OAAO,CAACR,IAAD,EAAOwD,UAAP,CAAP;AACD;SCvBeC,YAAeV,KAAA;EAC7B,MAAM7C,GAAG,GAAGgC,MAAM,EAAlB;EAEAJ,SAAS,CAAC;IACR5B,GAAG,CAACiC,OAAJ,GAAcY,KAAd;GADO,EAEN,CAACA,KAAD,CAFM,CAAT;EAIA,OAAO7C,GAAG,CAACiC,OAAX;AACD;ACRD,IAAIuB,GAAG,GAA2B,EAAlC;AAEA,SAAgBC,YAAYC,MAAA,EAAgBb,KAAA;EAC1C,OAAOhD,OAAO,CAAC;IACb,IAAIgD,KAAJ,EAAW;MACT,OAAOA,KAAP;;IAGF,MAAMc,EAAE,GAAGH,GAAG,CAACE,MAAD,CAAH,IAAe,IAAf,GAAsB,CAAtB,GAA0BF,GAAG,CAACE,MAAD,CAAH,GAAc,CAAnD;IACAF,GAAG,CAACE,MAAD,CAAH,GAAcC,EAAd;IAEA,OAAUD,MAAV,SAAoBC,EAApB;GARY,EASX,CAACD,MAAD,EAASb,KAAT,CATW,CAAd;AAUD;ACfD,SAASe,kBAATA,CAA4BC,QAA5B;EACE,OAAO,UACLC,MADK;sCAEFC,WAAA,OAAAtE,KAAA,CAAAC,IAAA,OAAAA,IAAA,WAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAAoE,WAAA,CAAApE,IAAA,QAAAC,SAAA,CAAAD,IAAA;;IAEH,OAAOoE,WAAW,CAACC,MAAZ,CACL,CAACC,WAAD,EAAcC,UAAd;MACE,MAAMC,OAAO,GAAG3D,MAAM,CAAC2D,OAAP,CAAeD,UAAf,CAAhB;MAEA,KAAK,MAAM,CAACE,GAAD,EAAMC,eAAN,CAAX,IAAqCF,OAArC,EAA8C;QAC5C,MAAMtB,KAAK,GAAGoB,WAAW,CAACG,GAAD,CAAzB;QAEA,IAAIvB,KAAK,IAAI,IAAb,EAAmB;UACjBoB,WAAW,CAACG,GAAD,CAAX,GAAoBvB,KAAK,GAAGgB,QAAQ,GAAGQ,eAAvC;;;MAIJ,OAAOJ,WAAP;KAZG,EAcL;MACE,GAAGH;KAfA,CAAP;GAJF;AAuBD;AAED,MAAaQ,GAAG,gBAAGV,kBAAkB,CAAC,CAAD,CAA9B;AACP,MAAaW,QAAQ,gBAAGX,kBAAkB,CAAC,CAAC,CAAF,CAAnC;SC3BSY,+BACdC,KAAA;EAEA,OAAO,aAAaA,KAAb,IAAsB,aAAaA,KAA1C;AACD;SCFeC,gBACdD,KAAA;EAEA,IAAI,CAACA,KAAL,EAAY;IACV,OAAO,KAAP;;EAGF,MAAM;IAACE;MAAiB9D,SAAS,CAAC4D,KAAK,CAAC3D,MAAP,CAAjC;EAEA,OAAO6D,aAAa,IAAIF,KAAK,YAAYE,aAAzC;AACD;SCVeC,aACdH,KAAA;EAEA,IAAI,CAACA,KAAL,EAAY;IACV,OAAO,KAAP;;EAGF,MAAM;IAACI;MAAchE,SAAS,CAAC4D,KAAK,CAAC3D,MAAP,CAA9B;EAEA,OAAO+D,UAAU,IAAIJ,KAAK,YAAYI,UAAtC;AACD;;ACTD;;;;AAGA,SAAgBC,oBAAoBL,KAAA;EAClC,IAAIG,YAAY,CAACH,KAAD,CAAhB,EAAyB;IACvB,IAAIA,KAAK,CAACM,OAAN,IAAiBN,KAAK,CAACM,OAAN,CAAcC,MAAnC,EAA2C;MACzC,MAAM;QAACC,OAAO,EAAEC,CAAV;QAAaC,OAAO,EAAEC;UAAKX,KAAK,CAACM,OAAN,CAAc,CAAd,CAAjC;MAEA,OAAO;QACLG,CADK;QAELE;OAFF;KAHF,MAOO,IAAIX,KAAK,CAACY,cAAN,IAAwBZ,KAAK,CAACY,cAAN,CAAqBL,MAAjD,EAAyD;MAC9D,MAAM;QAACC,OAAO,EAAEC,CAAV;QAAaC,OAAO,EAAEC;UAAKX,KAAK,CAACY,cAAN,CAAqB,CAArB,CAAjC;MAEA,OAAO;QACLH,CADK;QAELE;OAFF;;;EAOJ,IAAIZ,8BAA8B,CAACC,KAAD,CAAlC,EAA2C;IACzC,OAAO;MACLS,CAAC,EAAET,KAAK,CAACQ,OADJ;MAELG,CAAC,EAAEX,KAAK,CAACU;KAFX;;EAMF,OAAO,IAAP;AACD;MCpBYG,GAAG,gBAAG9E,MAAM,CAAC+E,MAAP,CAAc;EAC/BC,SAAS,EAAE;IACT9E,QAAQA,CAAC+E,SAAD;MACN,IAAI,CAACA,SAAL,EAAgB;QACd;;MAGF,MAAM;QAACP,CAAD;QAAIE;UAAKK,SAAf;MAEA,yBAAsBP,CAAC,GAAGQ,IAAI,CAACC,KAAL,CAAWT,CAAX,CAAH,GAAmB,CAA1C,cACEE,CAAC,GAAGM,IAAI,CAACC,KAAL,CAAWP,CAAX,CAAH,GAAmB,CADtB;;GAT2B;EAc/BQ,KAAK,EAAE;IACLlF,QAAQA,CAAC+E,SAAD;MACN,IAAI,CAACA,SAAL,EAAgB;QACd;;MAGF,MAAM;QAACI,MAAD;QAASC;UAAUL,SAAzB;MAEA,mBAAiBI,MAAjB,iBAAmCC,MAAnC;;GAtB2B;EAyB/BC,SAAS,EAAE;IACTrF,QAAQA,CAAC+E,SAAD;MACN,IAAI,CAACA,SAAL,EAAgB;QACd;;MAGF,OAAO,CACLH,GAAG,CAACE,SAAJ,CAAc9E,QAAd,CAAuB+E,SAAvB,CADK,EAELH,GAAG,CAACM,KAAJ,CAAUlF,QAAV,CAAmB+E,SAAnB,CAFK,EAGLO,IAHK,CAGA,GAHA,CAAP;;GA/B2B;EAqC/BC,UAAU,EAAE;IACVvF,QAAQA,CAAAwF,IAAA;UAAC;QAACC,QAAD;QAAW3D,QAAX;QAAqB4D;;MAC5B,OAAUD,QAAV,SAAsB3D,QAAtB,WAAoC4D,MAApC;;;AAvC2B,CAAd,CAAZ;ACbP,MAAMC,QAAQ,GACZ,wIADF;AAGA,SAAgBC,uBACdhG,OAAA;EAEA,IAAIA,OAAO,CAACiG,OAAR,CAAgBF,QAAhB,CAAJ,EAA+B;IAC7B,OAAO/F,OAAP;;EAGF,OAAOA,OAAO,CAACkG,aAAR,CAAsBH,QAAtB,CAAP;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}