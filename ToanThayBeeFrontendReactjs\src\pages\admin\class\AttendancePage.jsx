import { use, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
    fetchAttendancesByLessonId,
    postAttendance,
    postAllAttendanceInLesson,
    updateAttendance,
    deleteAttendance,
    resetAttendanceState,
    setPage,
    setLimit,
    fetchLessonAttendanceStatistics,
    clearLessonStatistics,
    setMonthTuition,
} from '../../../features/attendance/attendanceSlice';
import { findUsers } from 'src/features/user/userSlice';
import { fetchLessonByClassId, addStudentToClass } from '../../../features/class/classSlice';
// import { setSuccessMessage } from '../features/notification/notificationSlice';
import LoadingSpinner from '../../../components/loading/LoadingSpinner';
import Pagination from '../../../components/Pagination';
import ConfirmModal from '../../../components/modal/ConfirmModal';
import UserSearchInput from '../../../components/UserSearchInput';
import AdminSidebar from '../../../components/sidebar/AdminSidebar';
import { exportAttendancesToExcel } from '../../../utils/excelExport';
import { Calendar, Users, Plus, Save, Trash2, UserPlus, CheckCircle, XCircle, Clock, Filter, FileSpreadsheet, Search, Home, BookOpen, Award, ChevronDown } from 'lucide-react';
import ClassAdminLayout from 'src/layouts/ClassAdminLayout';
import { clearStudentNotJoined } from '../../../features/attendance/attendanceSlice';

const getMonthOptions = () => {
    const start = new Date(2025, 5); // tháng 6 năm 2025 (tháng tính từ 0)
    const now = new Date();

    const months = [];
    let current = new Date(start);

    while (
        current.getFullYear() < now.getFullYear() ||
        (current.getFullYear() === now.getFullYear() && current.getMonth() <= now.getMonth())
    ) {
        const year = current.getFullYear();
        const month = String(current.getMonth() + 1).padStart(2, "0");
        months.push({
            label: `Tháng ${month} - ${year}`,
            value: `${year}-${month}`,
        });

        // tăng 1 tháng
        current.setMonth(current.getMonth() + 1);
    }

    return months.reverse(); // đảo ngược để hiện tháng mới nhất trước
};


export function MonthDropdown({ monthTuition, setMonthTuition }) {
    const [open, setOpen] = useState(false);
    const options = getMonthOptions();

    const selected = options.find((opt) => opt.value === monthTuition);

    return (
        <th className="border border-gray-200 p-3 text-center relative">
            <div className="flex flex-col items-center">
                <span>Học phí</span>
                <button
                    onClick={() => setOpen(!open)}
                    className="text-sm text-gray-500 mt-1 flex items-center gap-1 border border-gray-300 px-2 py-[2px] rounded"
                >
                    {selected ? selected.label : "Chọn tháng"}
                    <ChevronDown size={14} />
                </button>

                {open && (
                    <div className="absolute top-full mt-1 bg-white border border-gray-300 shadow z-10 rounded w-[140px] max-h-[200px] overflow-y-auto">
                        {options.map((option) => (
                            <div
                                key={option.value}
                                onClick={() => {
                                    setMonthTuition(option.value);
                                    setOpen(false);
                                }}
                                className="px-2 py-1 hover:bg-gray-100 cursor-pointer text-sm"
                            >
                                {option.label}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </th>
    );
}


const AttendancePage = () => {
    const { classId } = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { lessons, loadingAdd } = useSelector(state => state.classes);
    const {
        list: attendances,
        loading: attendanceLoading,
        page: currentPage,
        pageSize,
        total: totalItems,
        totalPages,
        lessonStatistics,
        userNotJoined,
        monthTuition,
    } = useSelector(state => state.attendances);
    const { classDetail, loading: classLoading } = useSelector(state => state.classes);

    const [selectedLesson, setSelectedLesson] = useState(null);
    const [attendanceData, setAttendanceData] = useState({});
    const [isCreatingAttendance, setIsCreatingAttendance] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [attendanceToDelete, setAttendanceToDelete] = useState(null);
    const [selectedUser, setSelectedUser] = useState(null);
    const [userSearchTerm, setUserSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [tuitionFilter, setTuitionFilter] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const [defaultStatus, setDefaultStatus] = useState('present');
    const [defaultNote, setDefaultNote] = useState('');
    const [showAddStudentToClass, setShowAddStudentToClass] = useState(false);
    const [addStudent, setAddStudent] = useState(false);
    // Fetch class lessons on component mount
    useEffect(() => {
        if (classId) {
            dispatch(fetchLessonByClassId({ classId }));
        }

        return () => {
            dispatch(resetAttendanceState());
            dispatch(clearLessonStatistics());
        };
    }, [dispatch, classId]);

    useEffect(() => {
        dispatch(findUsers(''));
    }, [dispatch])

    useEffect(() => {
        if (userNotJoined !== null) {
            setShowAddStudentToClass(true);
        } else {
            setShowAddStudentToClass(false);
        }
    }, [userNotJoined]);

    // Debounce search term
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 1000); // 500ms delay

        return () => clearTimeout(timer);
    }, [searchTerm]);

    // Reset page to 1 when search term changes
    useEffect(() => {
        if (debouncedSearchTerm !== '') {
            dispatch(setPage(1));
        }
    }, [debouncedSearchTerm, dispatch]);

    // Fetch attendances when lesson is selected or page changes
    useEffect(() => {
        if (selectedLesson) {
            dispatch(fetchAttendancesByLessonId({
                lessonId: selectedLesson.id,
                status: statusFilter,
                search: debouncedSearchTerm,
                page: currentPage,
                limit: pageSize,
                tuition: tuitionFilter,
                month: monthTuition
            }));

            // Fetch lesson statistics
            dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
        }
    }, [dispatch, selectedLesson, currentPage, pageSize, statusFilter, debouncedSearchTerm, tuitionFilter, monthTuition]);

    // Initialize attendance data when attendances are loaded
    useEffect(() => {
        if (attendances.length > 0) {
            const data = {};
            attendances.forEach(attendance => {
                data[attendance.userId] = {
                    id: attendance.id,
                    status: attendance.status,
                    note: attendance.note || ''
                };
            });
            setAttendanceData(data);
        } else {
            setAttendanceData({});
        }
    }, [attendances]);

    const handleLessonSelect = (lesson) => {
        setSelectedLesson(lesson);
        setAttendanceData({});
        // Reset to page 1 when selecting new lesson
        dispatch(setPage(1));
    };

    useEffect(() => {
        if (selectedLesson && !monthTuition) {
            const lessonDate = new Date(selectedLesson.day);
            const formattedMonth = `${lessonDate.getFullYear()}-${String(lessonDate.getMonth() + 1).padStart(2, '0')}`;
            dispatch(setMonthTuition(formattedMonth));
        }
    }, [selectedLesson, monthTuition]);


    const handlePageChange = (page) => {
        dispatch(setPage(page));
    };

    const handleAttendanceChange = (studentId, field, value) => {
        setAttendanceData(prev => {
            const currentData = prev[studentId] || {};
            return {
                ...prev,
                [studentId]: {
                    id: currentData.id || null,
                    status: currentData.status || 'absent',
                    note: currentData.note || '',
                    ...currentData,
                    [field]: value
                }
            };
        });

        // Auto-save when status changes
        if (field === 'status') {
            handleSaveAttendance(studentId, { [field]: value });
        }
    };

    const handleCreateAllAttendance = async () => {
        if (!selectedLesson) return;

        setIsCreatingAttendance(true);
        try {
            // console.log('Creating attendance for all students in lesson:', selectedLesson.id, classId);
            await dispatch(postAllAttendanceInLesson({
                classId,
                lessonId: selectedLesson.id
            })).unwrap().finally(() => {
                dispatch(fetchAttendancesByLessonId({
                    lessonId: selectedLesson.id,
                    status: statusFilter,
                    search: debouncedSearchTerm,
                    page: currentPage,
                    limit: pageSize,
                    tuition: tuitionFilter,
                    month: monthTuition
                }));

                // Update lesson statistics after creating all attendance
                dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
            })
        } catch (error) {
            console.error('Error creating attendance:', error);
        } finally {
            setIsCreatingAttendance(false);
        }
    };

    const handleSaveAttendance = async (studentId, autoSaveData = null) => {
        const data = autoSaveData || attendanceData[studentId];
        if (!data) return;

        try {
            // Get current attendance data for this student
            const currentAttendance = attendances.find(att => att.userId === studentId);
            const attendanceId = currentAttendance?.id || attendanceData[studentId]?.id;

            if (attendanceId) {
                // Update existing attendance
                const updateData = autoSaveData ? {
                    ...currentAttendance,
                    ...autoSaveData
                } : {
                    status: data.status,
                    note: data.note
                };

                await dispatch(updateAttendance({
                    attendanceId: attendanceId,
                    data: updateData
                })).unwrap();
            } else {
                // Create new attendance
                const createData = autoSaveData ? {
                    userId: studentId,
                    lessonId: selectedLesson.id,
                    status: 'present',
                    note: '',
                    classId: classId,
                    ...autoSaveData
                } : {
                    userId: studentId,
                    lessonId: selectedLesson.id,
                    status: data.status,
                    note: data.note,
                    classId: classId,
                };

                await dispatch(postAttendance(createData)).unwrap();
            }

            // Update lesson statistics after successful save
            if (selectedLesson) {
                dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
            }
        } catch (error) {
            console.error('Error saving attendance:', error);
        }
    };



    const handleDeleteClick = (attendanceId) => {
        setAttendanceToDelete(attendanceId);
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        if (!attendanceToDelete) return;

        try {
            await dispatch(deleteAttendance(attendanceToDelete)).unwrap();

            // Refresh attendance list
            // dispatch(fetchAttendancesByLessonId({
            //     lessonId: selectedLesson.id,
            //     status: statusFilter,
            //     search: debouncedSearchTerm,
            //     page: currentPage,
            //     limit: pageSize
            // }));

            // Update lesson statistics after successful delete
            dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
        } catch (error) {
            console.error('Error deleting attendance:', error);
        } finally {
            setShowDeleteModal(false);
            setAttendanceToDelete(null);
        }
    };

    const handleDeleteCancel = () => {
        setShowDeleteModal(false);
        setAttendanceToDelete(null);
    };

    const handleDeleteAddStudentToClass = () => {
        dispatch(clearStudentNotJoined());
    }

    const handleAddStudentToClass = async () => {
        if (!userNotJoined || !classId) return;

        try {
            await dispatch(addStudentToClass({
                classId: parseInt(classId),
                studentId: userNotJoined
            })).unwrap().finally(() => {
                // Refresh the student list
                dispatch(clearStudentNotJoined());
            });
        } catch (error) {
            console.error('Error adding student to class:', error);
        }
    }

    // Helper function to get status styling
    const getStatusStyle = (status) => {
        switch (status) {
            case 'present':
                return 'bg-green-100 text-green-800 border-green-300';
            case 'absent':
                return 'bg-red-100 text-red-800 border-red-300';
            case 'late':
                return 'bg-yellow-100 text-yellow-800 border-yellow-300';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-300';
        }
    };

    // Helper function to get status icon
    const getStatusIcon = (status) => {
        switch (status) {
            case 'present':
                return <CheckCircle size={14} className="text-green-600" />;
            case 'absent':
                return <XCircle size={14} className="text-red-600" />;
            case 'late':
                return <Clock size={14} className="text-yellow-600" />;
            default:
                return null;
        }
    };

    // Helper function to check if note has changed
    const hasNoteChanged = (studentId) => {
        const currentAttendance = attendances.find(att => att.userId === studentId);
        const localData = attendanceData[studentId];

        if (!localData) return false;

        const originalNote = currentAttendance?.note || '';
        const currentNote = localData.note || '';

        return originalNote !== currentNote;
    };

    // Helper function to render BTVN status
    const renderBTVNStatus = (btvnStatus) => {
        if (!btvnStatus) {
            return (
                <div className="flex items-center justify-center gap-1 text-gray-400">
                    <BookOpen size={14} />
                    <span className="text-xs">Không có BTVN</span>
                </div>
            );
        }

        const { isDone, type, score, studyTime } = btvnStatus;

        if (isDone) {
            return (
                <div className="flex items-center justify-center gap-1">
                    <CheckCircle size={14} className="text-green-600" />
                    <div className="text-center">
                        <div className="text-xs font-medium text-green-700">Đã hoàn thành</div>
                        {type === 'exam' && score !== undefined && (
                            <div className="text-xs text-green-600">Điểm: {score}</div>
                        )}
                        {type === 'learningItem' && studyTime && (
                            <div className="text-xs text-gray-500">
                                {new Date(studyTime).toLocaleDateString('vi-VN')}
                            </div>
                        )}
                    </div>
                </div>
            );
        } else {
            return (
                <div className="flex items-center justify-center gap-1">
                    <XCircle size={14} className="text-red-600" />
                    <div className="text-center">
                        <div className="text-xs font-medium text-red-700">Chưa hoàn thành</div>
                    </div>
                </div>
            );
        }
    };

    // Helper function to render weekly attendances
    const renderWeeklyAttendances = (weeklyAttendances) => {
        if (!weeklyAttendances || weeklyAttendances.length === 0) {
            return (
                <div className="text-center text-gray-400">
                    <span className="text-xs">Không có buổi học khác</span>
                </div>
            );
        }

        return (
            <div className="space-y-1">
                {weeklyAttendances.map((attendance, index) => (
                    <div key={attendance.id || index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                        <div className="flex-1">
                            <div className="font-medium text-gray-700 truncate">
                                {attendance.lesson.name} - {attendance.lesson?.class?.name}
                            </div>
                            <div className="text-gray-500">
                                {new Date(attendance.lesson.day).toLocaleDateString('vi-VN')}
                            </div>
                        </div>
                        <div className="flex items-center gap-1">
                            {getStatusIcon(attendance.status)}
                            <span className={`px-1 py-0.5 rounded text-xs font-medium ${getStatusStyle(attendance.status)}`}>
                                {attendance.status === 'present' ? 'Có mặt' :
                                    attendance.status === 'absent' ? 'Vắng' : 'Muộn'}
                            </span>
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    const renderTuition = (isPaid) => {
        if (isPaid === null) {
            return (
                <div className="text-center text-gray-400">
                    <span className="text-xs">Chưa có</span>
                </div>
            );
        }
        return (
            <div className="flex items-center justify-center gap-1">
                {isPaid ? (
                    <CheckCircle size={14} className="text-green-600" />
                ) : (
                    <XCircle size={14} className="text-red-600" />
                )}
            </div>
        );
    };


    const handleUserSelect = (user) => {
        setSelectedUser(user);
        setUserSearchTerm(`${user.lastName} ${user.firstName}`);
    };

    const handleUserSearchChange = (value) => {
        setUserSearchTerm(value);
        if (!value) {
            setSelectedUser(null);
        }
    };

    const handleUserSearchClear = () => {
        setSelectedUser(null);
        setUserSearchTerm('');
    };

    const handleCreateSingleAttendance = async () => {
        if (!selectedUser || !selectedLesson) {
            return;
        }

        try {
            await dispatch(postAttendance({
                userId: selectedUser.id,
                lessonId: selectedLesson.id,
                status: defaultStatus,
                note: defaultNote,
                classId
            })).unwrap();

            // Clear selection
            setSelectedUser(null);
            setUserSearchTerm('');
            setDefaultNote('');

            // Refresh attendance list
            dispatch(fetchAttendancesByLessonId({
                lessonId: selectedLesson.id,
                status: statusFilter,
                tuition: tuitionFilter,
                search: debouncedSearchTerm,
                page: currentPage,
                limit: pageSize,
                month: monthTuition
            }));

            // Update lesson statistics after creating single attendance
            dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
        } catch (error) {
            console.error('Error creating single attendance:', error);
        }
    };

    const handleExportToExcel = async () => {
        if (!selectedLesson) {
            return;
        }

        try {
            // Fetch all attendances for export (limit 1000, all statuses)
            const result = await dispatch(fetchAttendancesByLessonId({
                lessonId: selectedLesson.id,
                status: 'all', // Get all statuses
                tuition: 'all',
                page: 1,
                limit: 1000,
                month: monthTuition,
            })).unwrap();

            if (result && result.data && result.data.length > 0) {
                // Export to Excel
                exportAttendancesToExcel(
                    result.data,
                    selectedLesson.name,
                    classDetail?.name
                );
            } else {
                alert('Không có dữ liệu điểm danh để xuất');
            }
        } catch (error) {
            console.error('Error exporting to Excel:', error);
            alert('Có lỗi xảy ra khi xuất Excel');
        }
    };

    if (classLoading) {
        return (
            <div className="flex items-center justify-center h-screen">
                <LoadingSpinner
                    size="4rem"
                    showText={true}
                    text="Đang tải thông tin lớp học..."
                />
            </div>
        );
    }

    return (
        <ClassAdminLayout>

            {/* Content */}
            <div className="flex-1 p-6 pb-20">

                {/* Lesson Selection */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6 max-h-[20rem] overflow-y-auto">
                    <div className="flex items-center gap-2 mb-4">
                        <Calendar className="text-sky-600" size={20} />
                        <h3 className="text-lg font-semibold text-gray-800">Chọn buổi học</h3>
                    </div>

                    <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                        {lessons?.map((lesson) => (
                            <div
                                key={lesson.id}
                                onClick={() => handleLessonSelect(lesson)}
                                className={`p-3 rounded-lg border cursor-pointer transition-all ${selectedLesson?.id === lesson.id
                                    ? 'border-sky-500 bg-sky-50'
                                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                    }`}
                            >
                                <span className="font-medium text-gray-800">{lesson.name} </span>
                                <span className="text-sm text-gray-500">
                                    {new Date(lesson.day).toLocaleDateString('vi-VN')}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>

                {/* User Search Section */}
                {selectedLesson && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                        <div className="flex items-center gap-2 mb-4">
                            <UserPlus className="text-sky-600" size={20} />
                            <h3 className="text-lg font-semibold text-gray-800">Tạo điểm danh cho học sinh</h3>
                        </div>

                        {/* Default Status Selection */}
                        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                                <CheckCircle className="text-sky-600" size={16} />
                                <label className="text-sm font-medium text-gray-700">Trạng thái mặc định:</label>
                            </div>
                            <select
                                value={defaultStatus}
                                onChange={(e) => setDefaultStatus(e.target.value)}
                                className={`px-3 py-2 border rounded-md text-sm font-medium ${getStatusStyle(defaultStatus)}`}
                            >
                                <option value="present">Có mặt</option>
                                <option value="absent">Vắng mặt</option>
                                <option value="late">Đi muộn</option>
                            </select>
                            <div className="flex items-center mt-2">
                                <input
                                    type="checkbox"
                                    checked={addStudent}
                                    onChange={() => setAddStudent(!addStudent)}
                                    className="ml-2"
                                />
                                <span className="text-sm text-gray-600 ml-1">Thêm học sinh chưa vào lớp</span>

                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                                Trạng thái này sẽ được áp dụng khi tạo điểm danh mới
                            </p>
                        </div>

                        <div className="space-y-4">
                            {/* User Search Input with higher z-index */}
                            <div className="relative z-50">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Chọn học sinh:
                                </label>
                                <UserSearchInput
                                    value={userSearchTerm}
                                    selectedUserId={selectedUser?.id || ''}
                                    onChange={handleUserSearchChange}
                                    onSelect={handleUserSelect}
                                    onClear={handleUserSearchClear}
                                    placeholder="Tìm kiếm học sinh..."
                                    className="w-full"
                                />
                            </div>

                            {/* Note Input */}
                            <div className="relative z-10">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Ghi chú (tùy chọn):
                                </label>
                                <input
                                    type="text"
                                    value={defaultNote}
                                    onChange={(e) => setDefaultNote(e.target.value)}
                                    placeholder="Nhập ghi chú cho điểm danh..."
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                />
                            </div>

                            <div className="relative z-0">
                                <button
                                    onClick={handleCreateSingleAttendance}
                                    disabled={!selectedUser}
                                    className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <UserPlus size={16} />
                                    Tạo điểm danh
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Attendance Management */}
                {selectedLesson && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-2">
                                <Users className="text-sky-600" size={20} />
                                <h3 className="text-lg font-semibold text-gray-800">
                                    Điểm danh: {selectedLesson.name}
                                </h3>
                            </div>

                            <div className="flex items-center gap-3">
                                {/* Search Input */}
                                <div className="flex items-center gap-2">
                                    <Search className="text-gray-500" size={16} />
                                    <input
                                        type="text"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        placeholder="Tìm kiếm học sinh..."
                                        className="px-3 py-1 border border-gray-300 rounded text-sm w-48"
                                    />
                                </div>

                                {/* Status Filter */}
                                <div className="flex items-center gap-2">
                                    <Filter className="text-gray-500" size={16} />
                                    <select
                                        value={statusFilter}
                                        onChange={(e) => setStatusFilter(e.target.value)}
                                        className="px-3 py-1 border border-gray-300 rounded text-sm"
                                    >
                                        <option value="all">Tất cả trạng thái</option>
                                        <option value="present">Có mặt</option>
                                        <option value="absent">Vắng mặt</option>
                                        <option value="late">Đi muộn</option>
                                    </select>
                                </div>

                                <div className="flex items-center gap-2">
                                    <select
                                        value={tuitionFilter}
                                        onChange={(e) => setTuitionFilter(e.target.value)}
                                        className="px-3 py-1 border border-gray-300 rounded text-sm"
                                    >
                                        <option value="all">Tất cả</option>
                                        <option value="true">Đã thanh toán</option>
                                        <option value="false">Chưa thanh toán</option>
                                    </select>
                                </div>

                                {/* {attendances.length === 0 && ( */}
                                <button
                                    onClick={handleCreateAllAttendance}
                                    disabled={isCreatingAttendance}
                                    className="flex items-center gap-2 px-4 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 disabled:opacity-50"
                                    title="Tạo điểm danh cho tất cả học sinh trong lớp với trạng thái mặc định"
                                >
                                    <Plus size={16} />
                                    {isCreatingAttendance ? 'Đang tạo...' : `Tạo điểm danh`}
                                </button>
                                {/* )} */}

                                {/* Export Excel Button */}
                                <button
                                    onClick={handleExportToExcel}
                                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                                    title="Xuất Excel"
                                >
                                    <FileSpreadsheet size={16} />
                                    Excel
                                </button>
                            </div>
                        </div>

                        {attendanceLoading ? (
                            <div className="flex items-center justify-center py-12">
                                <LoadingSpinner
                                    size="3rem"
                                    showText={true}
                                    text="Đang tải dữ liệu điểm danh..."
                                />
                            </div>
                        ) : attendances.length > 0 ? (
                            <>
                                {/* Status Legend with Statistics */}
                                <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                                    <h4 className="text-sm font-medium text-gray-700 mb-3">Thống kê điểm danh:</h4>

                                    {/* Statistics Summary */}
                                    {lessonStatistics.loading ? (
                                        <div className="flex justify-center py-4">
                                            <LoadingSpinner size="2rem" />
                                        </div>
                                    ) : lessonStatistics.data ? (
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <Users size={16} className="text-gray-600" />
                                                    <span className="text-sm font-medium text-gray-700">Tổng số</span>
                                                </div>
                                                <div className="text-xl font-bold text-gray-900 mt-1">
                                                    {lessonStatistics.data.statistics.total}
                                                </div>
                                            </div>

                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <CheckCircle size={16} className="text-green-600" />
                                                    <span className="text-sm font-medium text-gray-700">Có mặt</span>
                                                </div>
                                                <div className="text-xl font-bold text-green-600 mt-1">
                                                    {lessonStatistics.data.statistics.present}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {lessonStatistics.data.statistics.presentPercentage}%
                                                </div>
                                            </div>

                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <XCircle size={16} className="text-red-600" />
                                                    <span className="text-sm font-medium text-gray-700">Vắng mặt</span>
                                                </div>
                                                <div className="text-xl font-bold text-red-600 mt-1">
                                                    {lessonStatistics.data.statistics.absent}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {lessonStatistics.data.statistics.absentPercentage}%
                                                </div>
                                            </div>

                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <Clock size={16} className="text-yellow-600" />
                                                    <span className="text-sm font-medium text-gray-700">Đi muộn</span>
                                                </div>
                                                <div className="text-xl font-bold text-yellow-600 mt-1">
                                                    {lessonStatistics.data.statistics.late}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {lessonStatistics.data.statistics.latePercentage}%
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <Users size={16} className="text-gray-600" />
                                                    <span className="text-sm font-medium text-gray-700">Tổng số</span>
                                                </div>
                                                <div className="text-xl font-bold text-gray-900 mt-1">0</div>
                                            </div>
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <CheckCircle size={16} className="text-green-600" />
                                                    <span className="text-sm font-medium text-gray-700">Có mặt</span>
                                                </div>
                                                <div className="text-xl font-bold text-green-600 mt-1">0</div>
                                                <div className="text-xs text-gray-500">0%</div>
                                            </div>
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <XCircle size={16} className="text-red-600" />
                                                    <span className="text-sm font-medium text-gray-700">Vắng mặt</span>
                                                </div>
                                                <div className="text-xl font-bold text-red-600 mt-1">0</div>
                                                <div className="text-xs text-gray-500">0%</div>
                                            </div>
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <Clock size={16} className="text-yellow-600" />
                                                    <span className="text-sm font-medium text-gray-700">Đi muộn</span>
                                                </div>
                                                <div className="text-xl font-bold text-yellow-600 mt-1">0</div>
                                                <div className="text-xs text-gray-500">0%</div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Status Legend */}
                                    <div className="border-t border-gray-200 pt-3">
                                        <h5 className="text-xs font-medium text-gray-600 mb-2">Chú thích trạng thái:</h5>
                                        <div className="flex flex-wrap gap-3">
                                            <div className="flex items-center gap-2">
                                                <CheckCircle size={14} className="text-green-600" />
                                                <span className="px-2 py-1 bg-green-100 text-green-800 border border-green-300 rounded text-xs font-medium">
                                                    Có mặt
                                                </span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <XCircle size={14} className="text-red-600" />
                                                <span className="px-2 py-1 bg-red-100 text-red-800 border border-red-300 rounded text-xs font-medium">
                                                    Vắng mặt
                                                </span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Clock size={14} className="text-yellow-600" />
                                                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 border border-yellow-300 rounded text-xs font-medium">
                                                    Đi muộn
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="overflow-x-auto">
                                    <table className="w-full border-collapse border border-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="border border-gray-200 p-3 text-center">STT</th>
                                                <th className="border border-gray-200 p-3 text-left">Học sinh</th>
                                                <th className="border border-gray-200 p-3 text-center">Trạng thái</th>

                                                <MonthDropdown
                                                    monthTuition={monthTuition}
                                                    setMonthTuition={(month) => dispatch(setMonthTuition(month))}
                                                />

                                                <th className="border border-gray-200 p-3 text-left">Ghi chú</th>
                                                <th className="border border-gray-200 p-3 text-center">BTVN</th>
                                                <th className="border border-gray-200 p-3 text-center">Điểm danh tuần</th>

                                                <th className="border border-gray-200 p-3 text-center">Thao tác</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {attendances.map((attendance, idx) => (
                                                <tr key={attendance.id} className="hover:bg-gray-50">
                                                    <td className="border border-gray-200 p-3 text-center">
                                                        {idx + 1 + (currentPage - 1) * pageSize}
                                                    </td>
                                                    <td className="border border-gray-200 p-3">
                                                        <div
                                                            className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer hover:underline"
                                                            onClick={() => {
                                                                window.open(`/admin/student-management/${attendance.userId}/attendance`, '_blank');
                                                            }}
                                                        >
                                                            {attendance.user?.fullName}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {attendance?.userId} | {attendance.user?.highSchool} | {attendance.user?.class} | {attendance.user?.phone}
                                                        </div>
                                                    </td>
                                                    <td className="border border-gray-200 p-3 text-center">
                                                        <div className="flex items-center justify-center gap-2">
                                                            {getStatusIcon(attendanceData[attendance.userId]?.status || attendance.status)}
                                                            <select
                                                                value={attendanceData[attendance.userId]?.status || attendance.status}
                                                                onChange={(e) => handleAttendanceChange(attendance.userId, 'status', e.target.value)}
                                                                className={`px-2 py-1 border rounded text-sm font-medium ${getStatusStyle(attendanceData[attendance.userId]?.status || attendance.status)}`}
                                                            >
                                                                <option value="present">Có mặt</option>
                                                                <option value="absent">Vắng mặt</option>
                                                                <option value="late">Đi muộn</option>
                                                            </select>
                                                        </div>
                                                    </td>
                                                    <td className="border border-gray-200 p-3">
                                                        {renderTuition(attendance.user?.isPaid)}
                                                    </td>
                                                    <td className="border border-gray-200 p-3">
                                                        <input
                                                            type="text"
                                                            value={attendanceData[attendance.userId]?.note !== undefined
                                                                ? attendanceData[attendance.userId].note
                                                                : attendance.note || ''}
                                                            onChange={(e) => handleAttendanceChange(attendance.userId, 'note', e.target.value)}
                                                            placeholder="Ghi chú..."
                                                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                                                        />
                                                    </td>
                                                    <td className="border border-gray-200 p-3">
                                                        {renderBTVNStatus(attendance.btvnStatus)}
                                                    </td>
                                                    <td className="border border-gray-200 p-3">
                                                        {renderWeeklyAttendances(attendance.weeklyAttendances)}
                                                    </td>
                                                    <td className="border border-gray-200 p-3 text-center">
                                                        <div className="flex items-center gap-2 justify-center">
                                                            {hasNoteChanged(attendance.userId) && (
                                                                <button
                                                                    onClick={() => handleSaveAttendance(attendance.userId)}
                                                                    className="flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                                                                >
                                                                    <Save size={14} />
                                                                    Lưu ghi chú
                                                                </button>
                                                            )}
                                                            {attendance.id && (
                                                                <button
                                                                    onClick={() => handleDeleteClick(attendance.id)}
                                                                    className="flex items-center gap-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                                                                >
                                                                    <Trash2 size={14} />
                                                                    Xóa
                                                                </button>
                                                            )}
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>

                                {/* Pagination */}
                                <div className="mt-4 flex justify-center">
                                    <Pagination
                                        currentPage={currentPage}
                                        totalItems={totalItems}
                                        limit={pageSize}
                                        onPageChange={handlePageChange}
                                    />
                                </div>
                            </>
                        ) : (
                            <div className="text-center py-8 text-gray-500">
                                <Users size={48} className="mx-auto mb-4 text-gray-300" />
                                <p>Chưa có dữ liệu điểm danh cho buổi học này</p>
                                <p className="text-sm">Nhấn "Tạo điểm danh" để bắt đầu</p>
                            </div>
                        )}
                    </div>
                )}

                {!selectedLesson && (
                    <div className="flex-1 flex items-center justify-center text-gray-500">
                        <div className="text-center">
                            <Calendar size={48} className="mx-auto mb-4 text-gray-300" />
                            <p>Vui lòng chọn một buổi học để bắt đầu điểm danh</p>
                        </div>
                    </div>
                )}

                {/* Confirm Delete Modal */}
                <ConfirmModal
                    isOpen={showDeleteModal}
                    onClose={handleDeleteCancel}
                    onConfirm={handleDeleteConfirm}
                    title="Xác nhận xóa"
                    message="Bạn có chắc chắn muốn xóa bản ghi điểm danh này? Hành động này không thể hoàn tác."
                />


                <ConfirmModal
                    isOpen={showAddStudentToClass && addStudent}
                    onClose={handleDeleteAddStudentToClass}
                    onConfirm={handleAddStudentToClass}
                    loading={loadingAdd}
                    title="Xác nhận thêm học sinh vào lớp"
                    message="Học sinh này chưa có trong lớp học. Bạn có chắc chắn muốn thêm học sinh này vào lớp không?"
                    color="green"
                />


            </div>
        </ClassAdminLayout>
    );
};

export default AttendancePage;
