import { Server } from 'socket.io';
import { EVENTS } from './constants.js';
import {
  handleJoinExam,
  handleSubmitExam,
  handleRequestTime,
  handleSendNotification,
  handleAdminJoinExamTracking
} from './handlers/examHandler.js';
import {
  handleSelectAnswer,
  handleCalculateScore
} from './handlers/answerHandler.js';
import { handleUserLog } from './handlers/cheatHandler.js';
import {
  handleUserAuthentication,
  handleMarkNotificationAsRead,
  handleMarkAllNotificationsAsRead,
  handleUserDisconnect
} from './handlers/notificationHandler.js';
import { checkAndSubmitTimedOutExams } from '../utils/examAutoSubmit.js';
import { deleteOldNotifications } from '../services/notification.service.js';

/**
 * Initialize Socket.IO server
 * @param {Object} server - HTTP server instance
 * @param {Array} allowedOrigins - Array of allowed origins for CORS
 * @returns {Object} Socket.IO server instance
 */
export const initializeSocketIO = (server, allowedOrigins) => {
  const io = new Server(server, {
    cors: {
      origin: function (origin, callback) {
        if (!origin || allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS: ' + origin));
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Origin', 'Accept']
    },
    transports: ['websocket', 'polling'], // Thêm polling làm fallback
    pingTimeout: 60000, // Tăng timeout để tránh ngắt kết nối
    pingInterval: 25000
  });

  // Set up socket event handlers
  io.on(EVENTS.CONNECTION, (socket) => {
    console.log("📡 Client connected:", socket.id);

    // Exam handlers
    socket.on(EVENTS.JOIN_EXAM, (data) => handleJoinExam(socket, io, data));
    socket.on(EVENTS.SUBMIT_EXAM, (data) => handleSubmitExam(socket, io, data));
    socket.on(EVENTS.REQUEST_TIME, (data) => handleRequestTime(socket, io, data));
    socket.on(EVENTS.SEND_NOTIFICATION, (data) => handleSendNotification(socket, io, data));
    socket.on(EVENTS.ADMIN_JOIN_EXAM_TRACKING, (data) => handleAdminJoinExamTracking(socket, io, data));

    // Answer handlers
    socket.on(EVENTS.SELECT_ANSWER, (data) => handleSelectAnswer(socket, io, data));
    socket.on(EVENTS.CALCULATE_SCORE, (data) => handleCalculateScore(socket, io, data));

    // Cheat detection handlers
    socket.on(EVENTS.USER_LOG, (data) => handleUserLog(socket, io, data));

    // User notification handlers
    socket.on('authenticate', (data) => handleUserAuthentication(socket, io, data));
    socket.on('mark_notification_read', (data) => handleMarkNotificationAsRead(socket, io, data));
    socket.on('mark_all_notifications_read', (data) => handleMarkAllNotificationsAsRead(socket, io, data));

    // Disconnect handler
    socket.on(EVENTS.DISCONNECT, () => {
      console.log("Client disconnected:", socket.id);
      handleUserDisconnect(socket.id);
    });
  });

  // Set up auto-submit check for timed-out exams
  setupAutoSubmitCheck(io);

  // Set up scheduled task to delete old notifications
  setupNotificationCleanup();

  return io;
};

/**
 * Set up auto-submit check for timed-out exams
 * @param {Object} io - Socket.IO server instance
 */
const setupAutoSubmitCheck = (io) => {
  const AUTO_SUBMIT_CHECK_INTERVAL = 60000 * 30; // Check every 30 minutes

  setInterval(async () => {
    try {
      const submittedAttempts = await checkAndSubmitTimedOutExams();

      // Notify clients about auto-submitted exams
      for (const attempt of submittedAttempts) {
        io.emit(`${EVENTS.EXAM_AUTO_SUBMITTED}_${attempt.id}`, {
          message: "Bài thi đã tự động nộp do hết thời gian làm bài",
          timestamp: new Date(),
          attemptId: attempt.id,
          score: attempt.score
        });
      }
    } catch (error) {
      console.error("Error in auto-submit check:", error);
    }
  }, AUTO_SUBMIT_CHECK_INTERVAL);
};

/**
 * Set up scheduled task to delete old notifications
 * Runs once a day at midnight to delete notifications older than 30 days
 */
const setupNotificationCleanup = () => {
  const ONE_DAY_IN_MS = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  // Calculate time until next midnight
  const calculateNextMidnight = () => {
    const now = new Date();
    const midnight = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate() + 1, // Next day
      0, 0, 0 // Midnight (00:00:00)
    );
    return midnight.getTime() - now.getTime();
  };

  // Function to run the cleanup
  const runCleanup = async () => {
    try {
      console.log('Starting scheduled notification cleanup...');
      const deletedCount = await deleteOldNotifications(30); // Delete notifications older than 30 days
      console.log(`Scheduled cleanup complete. Deleted ${deletedCount} old notifications.`);

      // Schedule next run for tomorrow at midnight
      setTimeout(runCleanup, ONE_DAY_IN_MS);
    } catch (error) {
      console.error('Error in scheduled notification cleanup:', error);
      // If there's an error, try again in 1 hour
      console.log('Will retry notification cleanup in 1 hour');
      setTimeout(runCleanup, 60 * 60 * 1000);
    }
  };

  // Schedule first run at the next midnight
  const timeUntilMidnight = calculateNextMidnight();
  console.log(`Scheduled first notification cleanup in ${Math.round(timeUntilMidnight / 1000 / 60)} minutes`);
  setTimeout(runCleanup, timeUntilMidnight);

  // Also run immediately if server starts after a long downtime
  // This ensures we don't accumulate too many old notifications
  setTimeout(async () => {
    try {
      console.log('Running initial notification cleanup...');
      const deletedCount = await deleteOldNotifications(30);
      console.log(`Initial cleanup complete. Deleted ${deletedCount} old notifications.`);
    } catch (error) {
      console.error('Error in initial notification cleanup:', error);
    }
  }, 5 * 60 * 1000); // Run 5 minutes after server start
};
