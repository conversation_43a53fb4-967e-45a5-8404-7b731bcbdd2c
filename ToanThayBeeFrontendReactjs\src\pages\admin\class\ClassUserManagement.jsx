import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useState } from "react";
import UserClassTable from "../../../components/table/UserClassTable";
import FunctionBarAdmin from "../../../components/bar/FunctionBarAdmin";
import AdminSidebar from "../../../components/sidebar/AdminSidebar";
import UserSearchInput from "../../../components/UserSearchInput";
import ConfirmModal from "../../../components/modal/ConfirmModal";
import { Home, UserPlus, X } from 'lucide-react';
import { addStudentToClass, kickStudentFromClass } from "../../../features/class/classSlice";
import { fetchUserClasses } from "../../../features/user/userSlice";
import { setIsAddView } from "../../../features/filter/filterSlice";
import ClassAdminLayout from "src/layouts/ClassAdminLayout";
import{ setLimit, setCurrentPage, setSearch } from "../../../features/user/userSlice";

const ClassUserManagement = () => {
    const { classId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { closeSidebar } = useSelector((state) => state.sidebar);
    const { isAddView } = useSelector(state => state.filter);

    const { search, pagination } = useSelector(state => state.users);
    const { page: currentPage, pageSize: limit, sortOrder } = pagination;
    // State for add student functionality
    const [showAddStudent, setShowAddStudent] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedUser, setSelectedUser] = useState(null);

    // State for kick confirmation modal
    const [showKickModal, setShowKickModal] = useState(false);
    const [studentToKick, setStudentToKick] = useState(null);

    // Handle user selection from search
    const handleSelectUser = (user) => {
        setSelectedUser(user);
        setSearchTerm(`${user.lastName} ${user.firstName}`);
    };

    // Handle clear selection
    const handleClearSelection = () => {
        setSelectedUser(null);
        setSearchTerm('');
    };

    // Handle add student to class
    const handleAddStudent = async () => {
        if (!selectedUser) return;

        try {
            await dispatch(addStudentToClass({
                classId: parseInt(classId),
                studentId: selectedUser.id
            })).unwrap();

            // Refresh the student list
            dispatch(fetchUserClasses({ id: classId, search, currentPage, limit, sortOrder }));

            // Reset form
            setSelectedUser(null);
            setSearchTerm('');
            setShowAddStudent(false);
        } catch (error) {
            console.error('Lỗi khi thêm học sinh:', error);
        }
    };

    // Handle kick student from class - show modal
    const handleKickStudent = (studentId, studentName) => {
        setStudentToKick({ id: studentId, name: studentName });
        setShowKickModal(true);
    };

    // Confirm kick student
    const confirmKickStudent = async () => {
        if (!studentToKick) return;

        try {
            await dispatch(kickStudentFromClass({
                classId: parseInt(classId),
                studentId: studentToKick.id
            })).unwrap();

            // Refresh the student list
            dispatch(fetchUserClasses({ id: classId, search, currentPage, limit, sortOrder }));

            // Close modal and reset state
            setShowKickModal(false);
            setStudentToKick(null);
        } catch (error) {
            console.error('Lỗi khi kick học sinh:', error);
        }
    };

    // Cancel kick student
    const cancelKickStudent = () => {
        setShowKickModal(false);
        setStudentToKick(null);
    };

    return (
        <ClassAdminLayout>

            {/* Content */}
            <div className="flex-1 overflow-hidden p-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
                    {/* Function Bar with Add Student */}
                    <div className="flex-shrink-0 p-6 border-b border-gray-200">
                        <div className="flex justify-between items-center mb-4">
                            <FunctionBarAdmin
                                currentPage={currentPage}
                                limit={limit}
                                totalItems={pagination.total}
                                totalPages={pagination.totalPages}
            setLimit={(newLimit) => {
                        dispatch(setLimit(newLimit))
                        dispatch(setCurrentPage(1))
                    }}                                setCurrentPage={(page) => dispatch(setCurrentPage(page))}
                                setSearch={(search) => dispatch(setSearch(search))}
                            />
                        </div>

                        {/* Add Student Form */}
                        {isAddView && (
                            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                <div className="flex items-center gap-2 mb-3">
                                    <UserPlus size={16} className="text-sky-600" />
                                    <h3 className="font-medium text-gray-900">Thêm học sinh vào lớp</h3>
                                    <button
                                        onClick={() => dispatch(setIsAddView(false))}
                                        className="ml-auto text-gray-400 hover:text-gray-600"
                                    >
                                        <X size={16} />
                                    </button>
                                </div>

                                <div className="flex gap-3 items-end">
                                    <div className="flex-1">
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Tìm kiếm học sinh
                                        </label>
                                        <UserSearchInput
                                            value={searchTerm}
                                            selectedUserId={selectedUser?.id}
                                            onChange={setSearchTerm}
                                            onSelect={handleSelectUser}
                                            onClear={handleClearSelection}
                                            placeholder="Nhập tên hoặc ID học sinh..."
                                            className="w-full"
                                        />
                                    </div>
                                    <button
                                        onClick={handleAddStudent}
                                        disabled={!selectedUser}
                                        className={`px-4 py-2 rounded-lg font-medium transition-colors ${selectedUser
                                            ? 'bg-green-600 text-white hover:bg-green-700'
                                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            }`}
                                    >
                                        Thêm vào lớp
                                    </button>
                                </div>

                                {selectedUser && (
                                    <div className="mt-3 p-3 bg-white rounded border border-gray-200">
                                        <p className="text-sm text-gray-600">
                                            <span className="font-medium">Học sinh được chọn:</span> {selectedUser.lastName} {selectedUser.firstName}
                                            <span className="text-gray-400 ml-2">(ID: {selectedUser.id})</span>
                                        </p>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    {/* Table Container - takes remaining space */}
                    <div className="flex-1 min-h-0 p-6">
                        <UserClassTable
                            classId={classId}
                            onKickStudent={handleKickStudent}
                        />
                    </div>
                </div>
            </div>

            {/* Kick Confirmation Modal */}
            <ConfirmModal
                isOpen={showKickModal}
                onClose={cancelKickStudent}
                onConfirm={confirmKickStudent}
                title="Xác nhận kick học sinh"
                message={`Bạn có chắc chắn muốn kick "${studentToKick?.name}" ra khỏi lớp này không? Hành động này không thể hoàn tác.`}
            />
        </ClassAdminLayout>
    )
}

export default ClassUserManagement;
