{"ast": null, "code": "/**\n * Global filter for KaTeX warnings\n * This should be imported and called once in the main App.js or index.js\n */\n\nexport const setupKatexWarningFilter = () => {\n  // Store the original console.warn\n  const originalWarn = console.warn;\n\n  // Override console.warn with a filtered version\n  console.warn = function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    const message = args.join(' ');\n\n    // Debug: Log tất cả warnings để xem\n    console.log('🔍 Console warn called:', message);\n\n    // Filter out KaTeX-related warnings that we don't want to see\n    const katexWarningPatterns = ['No character metrics for', 'Unrecognized Unicode character', 'LaTeX-incompatible input and strict mode is set to', 'unknownSymbol', 'katex.mjs:',\n    // B<PERSON><PERSON> kỳ warning nào từ katex.mjs\n    'KaTeX',\n    // B<PERSON><PERSON> kỳ warning nào có chứa KaTeX\n    'reportNonstrict' // Function báo warning trong KaTeX\n    ];\n\n    // Check if this warning matches any KaTeX pattern\n    const isKatexWarning = katexWarningPatterns.some(pattern => message.includes(pattern));\n\n    // Debug: Log để kiểm tra filter có hoạt động không\n    if (isKatexWarning) {\n      console.log('🔇 Filtered KaTeX warning:', message);\n      return;\n    }\n\n    // Only show the warning if it's not a KaTeX warning we want to suppress\n    console.log('✅ Allowing warning:', message);\n    originalWarn.apply(console, args);\n  };\n\n  // Return a function to restore original console.warn if needed\n  return () => {\n    console.warn = originalWarn;\n  };\n};", "map": {"version": 3, "names": ["setupKatexWarningFilter", "originalWarn", "console", "warn", "_len", "arguments", "length", "args", "Array", "_key", "message", "join", "log", "katexWarningPatterns", "isKatexWarning", "some", "pattern", "includes", "apply"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/utils/setupKatexWarningFilter.js"], "sourcesContent": ["/**\n * Global filter for KaTeX warnings\n * This should be imported and called once in the main App.js or index.js\n */\n\nexport const setupKatexWarningFilter = () => {\n    // Store the original console.warn\n    const originalWarn = console.warn;\n    \n    // Override console.warn with a filtered version\n    console.warn = (...args) => {\n        const message = args.join(' ');\n\n        // Debug: Log tất cả warnings để xem\n        console.log('🔍 Console warn called:', message);\n\n        // Filter out KaTeX-related warnings that we don't want to see\n        const katexWarningPatterns = [\n            'No character metrics for',\n            'Unrecognized Unicode character',\n            'LaTeX-incompatible input and strict mode is set to',\n            'unknownSymbol',\n            'katex.mjs:', // B<PERSON>t kỳ warning nào từ katex.mjs\n            'KaTeX', // B<PERSON>t kỳ warning nào có chứa KaTeX\n            'reportNonstrict' // Function báo warning trong KaTeX\n        ];\n\n        // Check if this warning matches any KaTeX pattern\n        const isKatexWarning = katexWarningPatterns.some(pattern =>\n            message.includes(pattern)\n        );\n\n        // Debug: Log để kiểm tra filter có hoạt động không\n        if (isKatexWarning) {\n            console.log('🔇 Filtered KaTeX warning:', message);\n            return;\n        }\n\n        // Only show the warning if it's not a KaTeX warning we want to suppress\n        console.log('✅ Allowing warning:', message);\n        originalWarn.apply(console, args);\n    };\n    \n    // Return a function to restore original console.warn if needed\n    return () => {\n        console.warn = originalWarn;\n    };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,uBAAuB,GAAGA,CAAA,KAAM;EACzC;EACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,IAAI;;EAEjC;EACAD,OAAO,CAACC,IAAI,GAAG,YAAa;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IACnB,MAAMC,OAAO,GAAGH,IAAI,CAACI,IAAI,CAAC,GAAG,CAAC;;IAE9B;IACAT,OAAO,CAACU,GAAG,CAAC,yBAAyB,EAAEF,OAAO,CAAC;;IAE/C;IACA,MAAMG,oBAAoB,GAAG,CACzB,0BAA0B,EAC1B,gCAAgC,EAChC,oDAAoD,EACpD,eAAe,EACf,YAAY;IAAE;IACd,OAAO;IAAE;IACT,iBAAiB,CAAC;IAAA,CACrB;;IAED;IACA,MAAMC,cAAc,GAAGD,oBAAoB,CAACE,IAAI,CAACC,OAAO,IACpDN,OAAO,CAACO,QAAQ,CAACD,OAAO,CAC5B,CAAC;;IAED;IACA,IAAIF,cAAc,EAAE;MAChBZ,OAAO,CAACU,GAAG,CAAC,4BAA4B,EAAEF,OAAO,CAAC;MAClD;IACJ;;IAEA;IACAR,OAAO,CAACU,GAAG,CAAC,qBAAqB,EAAEF,OAAO,CAAC;IAC3CT,YAAY,CAACiB,KAAK,CAAChB,OAAO,EAAEK,IAAI,CAAC;EACrC,CAAC;;EAED;EACA,OAAO,MAAM;IACTL,OAAO,CAACC,IAAI,GAAGF,YAAY;EAC/B,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}