{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\nimport { fetchWithCacheIfNeeded } from \"../helpers/fetchWithCacheIfNeeded\";\nexport const fetchExams = createAsyncThunk(\"exams/fetchExams\", async (_ref, _ref2) => {\n  let {\n    search,\n    currentPage,\n    limit,\n    sortOrder\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, examApi.getAllExamAPI, {\n    search,\n    currentPage,\n    limit,\n    sortOrder\n  }, data => {\n    // dispatch(setCurrentPage(data.currentPage));\n    // dispatch(setTotalPages(data.totalPages));\n    // dispatch(setTotalItems(data.totalItems));\n  }, true, false);\n});\nexport const fetchNewestExams = createAsyncThunk(\"exams/fetchNewestExams\", async (_, _ref3) => {\n  let {\n    dispatch\n  } = _ref3;\n  return await apiHandler(dispatch, examApi.getNewestExamAPI, null, () => {}, false, false);\n});\nexport const fetchSavedExam = createAsyncThunk(\"exams/fetchSavedExam\", async (_, _ref4) => {\n  let {\n    dispatch\n  } = _ref4;\n  return await apiHandler(dispatch, examApi.getExamsSavedAPI, null, () => {}, false, false);\n});\nexport const reExamination = createAsyncThunk(\"exams/reExamination\", async (id, _ref5) => {\n  let {\n    dispatch\n  } = _ref5;\n  return await apiHandler(dispatch, examApi.reExamination, id, () => {}, true, false, false, false);\n});\n\n// export const summitExam = createAsyncThunk(\n//     \"exams/summitExam\",\n//     async (attemptId, { dispatch }) => {\n//         return await apiHandler(dispatch, examApi.summitExamAPI, { attemptId }, () => { }, true, false);\n//     }\n// );\n\nexport const fetchRelatedExams = createAsyncThunk(\"exams/fetchRelatedExams\", async (id, _ref6) => {\n  let {\n    dispatch\n  } = _ref6;\n  return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => {}, false, false);\n});\n\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\"exams/fetchRelatedExamsIfNeeded\", async (id, _ref7) => {\n  let {\n    dispatch,\n    getState\n  } = _ref7;\n  const state = getState();\n  const {\n    relatedExams,\n    lastFetchedRelatedExams\n  } = state.exams;\n\n  // Kiểm tra xem đã có dữ liệu trong cache chưa\n  const hasData = relatedExams[id] && relatedExams[id].length > 0;\n\n  // Kiểm tra thời gian cache (5 phút = 300000ms)\n  const now = Date.now();\n  const lastFetched = lastFetchedRelatedExams[id] || 0;\n  const isCacheValid = now - lastFetched < 300000;\n\n  // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache\n  if (hasData && isCacheValid) {\n    // Cập nhật state.exams để hiển thị dữ liệu cache\n    return {\n      data: relatedExams[id]\n    };\n  }\n\n  // Nếu không có dữ liệu hoặc cache hết hạn, gọi API\n  return await dispatch(fetchRelatedExams(id)).unwrap();\n});\nexport const fetchExamById = createAsyncThunk(\"exams/fetchExamById\", async (id, _ref8) => {\n  let {\n    dispatch\n  } = _ref8;\n  return await apiHandler(dispatch, examApi.getExamByIdAPI, id, () => {}, true, false);\n});\nexport const findExams = createAsyncThunk(\"exams/findExams\", async (search, _ref9) => {\n  let {\n    dispatch\n  } = _ref9;\n  return await apiHandler(dispatch, examApi.findExamsAPI, search, () => {}, false, false, false, false);\n});\nexport const fetchPublicExamById = createAsyncThunk(\"exams/fetchPublicExamById\", async (id, _ref10) => {\n  let {\n    dispatch\n  } = _ref10;\n  return await apiHandler(dispatch, examApi.getExamPublic, id, () => {}, false, false);\n});\nexport const putExam = createAsyncThunk(\"exams/putExam\", async (_ref11, _ref12) => {\n  let {\n    examId,\n    examData\n  } = _ref11;\n  let {\n    dispatch\n  } = _ref12;\n  return await apiHandler(dispatch, examApi.putExamAPI, {\n    examId,\n    examData\n  }, () => {}, true, false);\n});\nexport const putImageExam = createAsyncThunk(\"exams/putImageExam\", async (_ref13, _ref14) => {\n  let {\n    examId,\n    examImage\n  } = _ref13;\n  let {\n    dispatch\n  } = _ref14;\n  return await apiHandler(dispatch, examApi.putImageExamAPI, {\n    examId,\n    examImage\n  }, () => {}, true, false);\n});\nexport const uploadSolutionPdf = createAsyncThunk(\"exams/uploadSolutionPdf\", async (_ref15, _ref16) => {\n  let {\n    examId,\n    pdfFile\n  } = _ref15;\n  let {\n    dispatch\n  } = _ref16;\n  return await apiHandler(dispatch, examApi.uploadSolutionPdfAPI, {\n    examId,\n    pdfFile\n  }, () => {}, true, false, true, false);\n});\nexport const uploadExamPdf = createAsyncThunk(\"exams/uploadExamPdf\", async (_ref17, _ref18) => {\n  let {\n    examId,\n    pdfFile\n  } = _ref17;\n  let {\n    dispatch\n  } = _ref18;\n  return await apiHandler(dispatch, examApi.uploadExamPdfAPI, {\n    examId,\n    pdfFile\n  }, () => {}, true, false, true, false);\n});\nexport const saveExamForUser = createAsyncThunk(\"exams/saveExamForUser\", async (_ref19, _ref20) => {\n  let {\n    examId\n  } = _ref19;\n  let {\n    dispatch\n  } = _ref20;\n  return await apiHandler(dispatch, examApi.saveExamForUserAPI, {\n    examId\n  }, () => {}, true, false, false, false);\n});\nexport const deleteExam = createAsyncThunk(\"exams/deleteExam\", async (id, _ref21) => {\n  let {\n    dispatch\n  } = _ref21;\n  return await apiHandler(dispatch, examApi.deleteExamAPI, id, () => {}, true, false);\n});\nexport const findPublicExams = createAsyncThunk(\"exams/findPublicExams\", async (search, _ref22) => {\n  let {\n    dispatch\n  } = _ref22;\n  return await apiHandler(dispatch, examApi.findPublicExamsAPI, search, () => {}, false, false, false, false);\n});\nexport const fetchPublicExamsIfNeeded = createAsyncThunk('exams/fetchPublicExamsIfNeeded', async (search, _ref23) => {\n  let {\n    dispatch,\n    getState\n  } = _ref23;\n  return await fetchWithCacheIfNeeded({\n    id: search.trim().toLowerCase(),\n    // dùng search làm key\n    stateKey: 'exams',\n    cacheKey: 'examsSearch',\n    dispatch,\n    getState,\n    fetchAction: findPublicExams,\n    // thunk gọi API\n    cacheDuration: 300000 // 5 phút\n  });\n});\nconst examSlice = createSlice({\n  name: \"exams\",\n  initialState: {\n    exams: [],\n    exam: null,\n    examsSearch: [],\n    relatedExams: {},\n    // Lưu trữ đề thi liên quan theo examId\n    lastFetchedRelatedExams: {},\n    // Lưu thời gian gọi API cuối cùng theo examId\n    loadingExam: false,\n    loadingSearch: false,\n    loadingSubmit: false,\n    loadingUpload1: false,\n    loadingUpload2: false,\n    isSubmit: false,\n    pagination: {\n      ...initialPaginationState\n    },\n    ...initialFilterState\n  },\n  reducers: {\n    setExam: (state, action) => {\n      state.exam = action.payload;\n    },\n    ...paginationReducers,\n    ...filterReducers\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchExams.pending, state => {\n      state.exams = [];\n      state.loading = true;\n    }).addCase(fetchExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n        state.pagination = action.payload.pagination || initialPaginationState;\n      }\n      state.loading = false;\n    }).addCase(fetchExams.rejected, state => {\n      state.loading = false;\n      state.exams = [];\n    }).addCase(fetchNewestExams.pending, state => {\n      state.exams = [];\n    }).addCase(fetchNewestExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n      }\n    }).addCase(fetchSavedExam.pending, state => {\n      state.exams = [];\n      state.loadingExam = true;\n    }).addCase(fetchSavedExam.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n      }\n      state.loadingExam = false;\n    }).addCase(fetchSavedExam.rejected, state => {\n      state.loadingExam = false;\n    }).addCase(fetchRelatedExams.pending, () => {\n      // Không xóa state.exams nữa để tránh làm mất dữ liệu hiện tại\n    }).addCase(fetchRelatedExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        const examId = action.meta.arg; // Lấy examId từ tham số gọi API\n        state.exams = action.payload.data;\n        state.relatedExams[examId] = action.payload.data;\n        state.lastFetchedRelatedExams[examId] = Date.now();\n      }\n    }).addCase(fetchExamById.pending, state => {\n      state.exam = null;\n    }).addCase(fetchExamById.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exam = action.payload.data;\n      }\n    }).addCase(findExams.pending, state => {\n      state.examsSearch = [];\n    }).addCase(findExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.examsSearch = action.payload.data;\n      }\n    }).addCase(fetchPublicExamById.pending, state => {\n      state.exam = null;\n    }).addCase(fetchPublicExamById.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exam = action.payload.data;\n      }\n    }).addCase(saveExamForUser.fulfilled, (state, action) => {\n      if (action.payload) {\n        var _state$exams$;\n        const {\n          examId,\n          isSave\n        } = action.payload;\n\n        // Trường hợp 1: Khi exams chứa đối tượng có thuộc tính exam (từ API saved exams)\n        if (state.exams.length > 0 && (_state$exams$ = state.exams[0]) !== null && _state$exams$ !== void 0 && _state$exams$.exam) {\n          if (isSave === false) {\n            // Nếu isSave = false, loại bỏ exam khỏi danh sách đã lưu\n            state.exams = state.exams.filter(exam => {\n              var _exam$exam;\n              return ((_exam$exam = exam.exam) === null || _exam$exam === void 0 ? void 0 : _exam$exam.id) !== examId;\n            });\n          } else {\n            // Nếu isSave = true, cập nhật trạng thái\n            state.exams = state.exams.map(exam => {\n              var _exam$exam2;\n              if (((_exam$exam2 = exam.exam) === null || _exam$exam2 === void 0 ? void 0 : _exam$exam2.id) === examId) {\n                return {\n                  ...exam,\n                  isSave: true\n                };\n              }\n              return exam;\n            });\n          }\n          return;\n        }\n\n        // Trường hợp 2: Khi exams chứa danh sách exam thông thường\n        if (state.exams && Array.isArray(state.exams)) {\n          state.exams = state.exams.map(exam => {\n            if (exam.id === examId) {\n              return {\n                ...exam,\n                isSave: isSave\n              };\n            }\n            return exam;\n          });\n        }\n\n        // Cập nhật trạng thái cho exam hiện tại nếu có\n        if (state.exam) {\n          state.exam.isSave = isSave;\n        }\n      }\n    }).addCase(uploadSolutionPdf.pending, state => {\n      state.loadingUpload2 = true;\n    }).addCase(uploadSolutionPdf.fulfilled, (state, action) => {\n      // console.log(action.payload)\n      const pdfUrl = action.payload;\n      if (state.exam) {\n        state.exam.solutionPdfUrl = pdfUrl;\n      }\n      state.loadingUpload2 = false;\n    }).addCase(uploadSolutionPdf.rejected, state => {\n      state.loadingUpload1 = false;\n    }).addCase(uploadExamPdf.pending, state => {\n      state.loadingUpload1 = true;\n    }).addCase(uploadExamPdf.fulfilled, (state, action) => {\n      const pdfUrl = action.payload;\n      if (state.exam) {\n        state.exam.fileUrl = pdfUrl;\n      }\n      state.loadingUpload1 = false;\n    }).addCase(uploadExamPdf.rejected, state => {\n      state.loadingUpload1 = false;\n    }).addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\n      if (action.payload && action.payload.data) {\n        state.exams = action.payload.data;\n      }\n    })\n    // .addCase(summitExam.pending, (state) => {\n    //     state.loadingSubmit = true;\n    // })\n    // .addCase(summitExam.fulfilled, (state, action) => {\n    //     state.loadingSubmit = false;\n    //     state.isSubmit = true;\n    // })\n    // .addCase(summitExam.rejected, (state) => {\n    //     state.loadingSubmit = false;\n    //     state.isSubmit = false;\n    // })\n    .addCase(fetchPublicExamsIfNeeded.pending, state => {\n      state.examsSearch = [];\n      state.loadingSearch = true;\n    }).addCase(fetchPublicExamsIfNeeded.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.examsSearch = action.payload.data;\n      }\n      state.loadingSearch = false;\n    }).addCase(fetchPublicExamsIfNeeded.rejected, state => {\n      state.examsSearch = [];\n      state.loadingSearch = false;\n    }).addCase(findExams.pending, state => {\n      state.examsSearch = [];\n      state.loadingSearch = true;\n    }).addCase(findExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.examsSearch = action.payload.data;\n      }\n      state.loadingSearch = false;\n    }).addCase(findExams.rejected, state => {\n      state.examsSearch = [];\n      state.loadingSearch = false;\n    });\n  }\n});\nexport const {\n  setExam,\n  setCurrentPage,\n  setLimit,\n  setSortOrder,\n  setLoading,\n  setSearch\n} = examSlice.actions;\nexport default examSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialPaginationState", "paginationReducers", "initialFilterState", "filterReducers", "fetchWithCacheIfNeeded", "fetchExams", "_ref", "_ref2", "search", "currentPage", "limit", "sortOrder", "dispatch", "getAllExamAPI", "data", "fetchNewestExams", "_", "_ref3", "getNewestExamAPI", "fetchSavedExam", "_ref4", "getExamsSavedAPI", "reExamination", "id", "_ref5", "fetchRelatedExams", "_ref6", "getRelatedExamAPI", "fetchRelatedExamsIfNeeded", "_ref7", "getState", "state", "relatedExams", "lastFetchedRelatedExams", "exams", "hasData", "length", "now", "Date", "lastFetched", "isCache<PERSON><PERSON>d", "unwrap", "fetchExamById", "_ref8", "getExamByIdAPI", "findExams", "_ref9", "findExamsAPI", "fetchPublicExamById", "_ref10", "getExamPublic", "putExam", "_ref11", "_ref12", "examId", "examData", "putExamAPI", "putImageExam", "_ref13", "_ref14", "examImage", "putImageExamAPI", "uploadSolutionPdf", "_ref15", "_ref16", "pdfFile", "uploadSolutionPdfAPI", "uploadExamPdf", "_ref17", "_ref18", "uploadExamPdfAPI", "saveExamForUser", "_ref19", "_ref20", "saveExamForUserAPI", "deleteExam", "_ref21", "deleteExamAPI", "findPublicExams", "_ref22", "findPublicExamsAPI", "fetchPublicExamsIfNeeded", "_ref23", "trim", "toLowerCase", "stateKey", "cache<PERSON>ey", "fetchAction", "cacheDuration", "examSlice", "name", "initialState", "exam", "examsSearch", "loadingExam", "loadingSearch", "loadingSubmit", "loadingUpload1", "loadingUpload2", "isSubmit", "pagination", "reducers", "setExam", "action", "payload", "extraReducers", "builder", "addCase", "pending", "loading", "fulfilled", "rejected", "meta", "arg", "_state$exams$", "isSave", "filter", "_exam$exam", "map", "_exam$exam2", "Array", "isArray", "pdfUrl", "solutionPdfUrl", "fileUrl", "setCurrentPage", "setLimit", "setSortOrder", "setLoading", "setSearch", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/exam/examSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\r\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\r\nimport { fetchWithCacheIfNeeded } from \"../helpers/fetchWithCacheIfNeeded\";\r\n\r\nexport const fetchExams = createAsyncThunk(\r\n    \"exams/fetchExams\",\r\n    async ({ search, currentPage, limit, sortOrder }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getAllExamAPI, { search, currentPage, limit, sortOrder }, (data) => {\r\n            // dispatch(setCurrentPage(data.currentPage));\r\n            // dispatch(setTotalPages(data.totalPages));\r\n            // dispatch(setTotalItems(data.totalItems));\r\n        }, true, false);\r\n    }\r\n);\r\n\r\nexport const fetchNewestExams = createAsyncThunk(\r\n    \"exams/fetchNewestExams\",\r\n    async (_, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getNewestExamAPI, null, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchSavedExam = createAsyncThunk(\r\n    \"exams/fetchSavedExam\",\r\n    async (_, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamsSavedAPI, null, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const reExamination = createAsyncThunk(\r\n    \"exams/reExamination\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.reExamination, id, () => { }, true, false, false, false);\r\n    }\r\n);\r\n\r\n// export const summitExam = createAsyncThunk(\r\n//     \"exams/summitExam\",\r\n//     async (attemptId, { dispatch }) => {\r\n//         return await apiHandler(dispatch, examApi.summitExamAPI, { attemptId }, () => { }, true, false);\r\n//     }\r\n// );\r\n\r\nexport const fetchRelatedExams = createAsyncThunk(\r\n    \"exams/fetchRelatedExams\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\r\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\r\n    \"exams/fetchRelatedExamsIfNeeded\",\r\n    async (id, { dispatch, getState }) => {\r\n        const state = getState();\r\n        const { relatedExams, lastFetchedRelatedExams } = state.exams;\r\n\r\n        // Kiểm tra xem đã có dữ liệu trong cache chưa\r\n        const hasData = relatedExams[id] && relatedExams[id].length > 0;\r\n\r\n        // Kiểm tra thời gian cache (5 phút = 300000ms)\r\n        const now = Date.now();\r\n        const lastFetched = lastFetchedRelatedExams[id] || 0;\r\n        const isCacheValid = now - lastFetched < 300000;\r\n\r\n        // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache\r\n        if (hasData && isCacheValid) {\r\n            // Cập nhật state.exams để hiển thị dữ liệu cache\r\n            return { data: relatedExams[id] };\r\n        }\r\n\r\n        // Nếu không có dữ liệu hoặc cache hết hạn, gọi API\r\n        return await dispatch(fetchRelatedExams(id)).unwrap();\r\n    }\r\n);\r\n\r\nexport const fetchExamById = createAsyncThunk(\r\n    \"exams/fetchExamById\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamByIdAPI, id, () => { }, true, false);\r\n    }\r\n);\r\n\r\nexport const findExams = createAsyncThunk(\r\n    \"exams/findExams\",\r\n    async (search, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.findExamsAPI, search, () => { }, false, false, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchPublicExamById = createAsyncThunk(\r\n    \"exams/fetchPublicExamById\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamPublic, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const putExam = createAsyncThunk(\r\n    \"exams/putExam\",\r\n    async ({ examId, examData }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.putExamAPI, { examId, examData }, () => { }, true, false);\r\n    }\r\n);\r\n\r\nexport const putImageExam = createAsyncThunk(\r\n    \"exams/putImageExam\",\r\n    async ({ examId, examImage }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.putImageExamAPI, { examId, examImage }, () => { }, true, false);\r\n    }\r\n);\r\n\r\n\r\n\r\nexport const uploadSolutionPdf = createAsyncThunk(\r\n    \"exams/uploadSolutionPdf\",\r\n    async ({ examId, pdfFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.uploadSolutionPdfAPI, { examId, pdfFile }, () => { }, true, false, true, false);\r\n    }\r\n);\r\n\r\nexport const uploadExamPdf = createAsyncThunk(\r\n    \"exams/uploadExamPdf\",\r\n    async ({ examId, pdfFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.uploadExamPdfAPI, { examId, pdfFile }, () => { }, true, false, true, false);\r\n    }\r\n);\r\n\r\nexport const saveExamForUser = createAsyncThunk(\r\n    \"exams/saveExamForUser\",\r\n    async ({ examId }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.saveExamForUserAPI, { examId }, () => { }, true, false, false, false);\r\n    }\r\n);\r\n\r\nexport const deleteExam = createAsyncThunk(\r\n    \"exams/deleteExam\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.deleteExamAPI, id, () => { }, true, false);\r\n    }\r\n);\r\n\r\nexport const findPublicExams = createAsyncThunk(\r\n    \"exams/findPublicExams\",\r\n    async (search, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.findPublicExamsAPI, search, () => { }, false, false, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchPublicExamsIfNeeded = createAsyncThunk(\r\n    'exams/fetchPublicExamsIfNeeded',\r\n    async (search, { dispatch, getState }) => {\r\n        return await fetchWithCacheIfNeeded({\r\n            id: search.trim().toLowerCase(),   // dùng search làm key\r\n            stateKey: 'exams',\r\n            cacheKey: 'examsSearch',\r\n            dispatch,\r\n            getState,\r\n            fetchAction: findPublicExams,      // thunk gọi API\r\n            cacheDuration: 300000,             // 5 phút\r\n        });\r\n    }\r\n);\r\n\r\nconst examSlice = createSlice({\r\n    name: \"exams\",\r\n    initialState: {\r\n        exams: [],\r\n        exam: null,\r\n        examsSearch: [],\r\n        relatedExams: {}, // Lưu trữ đề thi liên quan theo examId\r\n        lastFetchedRelatedExams: {}, // Lưu thời gian gọi API cuối cùng theo examId\r\n        loadingExam: false,\r\n        loadingSearch: false,\r\n        loadingSubmit: false,\r\n        loadingUpload1: false,\r\n        loadingUpload2: false,\r\n        isSubmit: false,\r\n        pagination: { ...initialPaginationState },\r\n        ...initialFilterState,\r\n    },\r\n    reducers: {\r\n        setExam: (state, action) => {\r\n            state.exam = action.payload;\r\n        },\r\n        ...paginationReducers,\r\n        ...filterReducers,\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchExams.pending, (state) => {\r\n                state.exams = [];\r\n                state.loading = true;\r\n            })\r\n            .addCase(fetchExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                    state.pagination = action.payload.pagination || initialPaginationState;\r\n                }\r\n                state.loading = false;\r\n            })\r\n            .addCase(fetchExams.rejected, (state) => {\r\n                state.loading = false;\r\n                state.exams = [];\r\n            })\r\n\r\n            .addCase(fetchNewestExams.pending, (state) => {\r\n                state.exams = [];\r\n            })\r\n            .addCase(fetchNewestExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(fetchSavedExam.pending, (state) => {\r\n                state.exams = [];\r\n                state.loadingExam = true;\r\n            })\r\n            .addCase(fetchSavedExam.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                }\r\n                state.loadingExam = false;\r\n            })\r\n            .addCase(fetchSavedExam.rejected, (state) => {\r\n                state.loadingExam = false;\r\n            })\r\n            .addCase(fetchRelatedExams.pending, () => {\r\n                // Không xóa state.exams nữa để tránh làm mất dữ liệu hiện tại\r\n            })\r\n            .addCase(fetchRelatedExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    const examId = action.meta.arg; // Lấy examId từ tham số gọi API\r\n                    state.exams = action.payload.data;\r\n                    state.relatedExams[examId] = action.payload.data;\r\n                    state.lastFetchedRelatedExams[examId] = Date.now();\r\n                }\r\n            })\r\n            .addCase(fetchExamById.pending, (state) => {\r\n                state.exam = null;\r\n            })\r\n            .addCase(fetchExamById.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exam = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(findExams.pending, (state) => {\r\n                state.examsSearch = [];\r\n            })\r\n            .addCase(findExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.examsSearch = action.payload.data;\r\n                }\r\n            })\r\n\r\n            .addCase(fetchPublicExamById.pending, (state) => {\r\n                state.exam = null;\r\n            })\r\n            .addCase(fetchPublicExamById.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exam = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(saveExamForUser.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    const { examId, isSave } = action.payload;\r\n\r\n                    // Trường hợp 1: Khi exams chứa đối tượng có thuộc tính exam (từ API saved exams)\r\n                    if (state.exams.length > 0 && state.exams[0]?.exam) {\r\n                        if (isSave === false) {\r\n                            // Nếu isSave = false, loại bỏ exam khỏi danh sách đã lưu\r\n                            state.exams = state.exams.filter(exam => exam.exam?.id !== examId);\r\n                        } else {\r\n                            // Nếu isSave = true, cập nhật trạng thái\r\n                            state.exams = state.exams.map(exam => {\r\n                                if (exam.exam?.id === examId) {\r\n                                    return { ...exam, isSave: true };\r\n                                }\r\n                                return exam;\r\n                            });\r\n                        }\r\n                        return;\r\n                    }\r\n\r\n                    // Trường hợp 2: Khi exams chứa danh sách exam thông thường\r\n                    if (state.exams && Array.isArray(state.exams)) {\r\n                        state.exams = state.exams.map(exam => {\r\n                            if (exam.id === examId) {\r\n                                return { ...exam, isSave: isSave };\r\n                            }\r\n                            return exam;\r\n                        });\r\n                    }\r\n\r\n                    // Cập nhật trạng thái cho exam hiện tại nếu có\r\n                    if (state.exam) {\r\n                        state.exam.isSave = isSave;\r\n                    }\r\n                }\r\n            })\r\n            .addCase(uploadSolutionPdf.pending, (state) => {\r\n                state.loadingUpload2 = true;\r\n            })\r\n            .addCase(uploadSolutionPdf.fulfilled, (state, action) => {\r\n                // console.log(action.payload)\r\n                const pdfUrl = action.payload\r\n                if (state.exam) {\r\n                    state.exam.solutionPdfUrl = pdfUrl;\r\n                }\r\n                state.loadingUpload2 = false;\r\n            })\r\n            .addCase(uploadSolutionPdf.rejected, (state) => {\r\n                state.loadingUpload1 = false;\r\n            })\r\n            .addCase(uploadExamPdf.pending, (state) => {\r\n                state.loadingUpload1 = true;\r\n            })\r\n            .addCase(uploadExamPdf.fulfilled, (state, action) => {\r\n                const pdfUrl = action.payload;\r\n                if (state.exam) {\r\n                    state.exam.fileUrl = pdfUrl;\r\n                }\r\n                state.loadingUpload1 = false;\r\n            })\r\n            .addCase(uploadExamPdf.rejected, (state) => {\r\n                state.loadingUpload1 = false;\r\n            })\r\n\r\n            .addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\r\n                if (action.payload && action.payload.data) {\r\n                    state.exams = action.payload.data;\r\n                }\r\n            })\r\n            // .addCase(summitExam.pending, (state) => {\r\n            //     state.loadingSubmit = true;\r\n            // })\r\n            // .addCase(summitExam.fulfilled, (state, action) => {\r\n            //     state.loadingSubmit = false;\r\n            //     state.isSubmit = true;\r\n            // })\r\n            // .addCase(summitExam.rejected, (state) => {\r\n            //     state.loadingSubmit = false;\r\n            //     state.isSubmit = false;\r\n            // })\r\n            .addCase(fetchPublicExamsIfNeeded.pending, (state) => {\r\n                state.examsSearch = [];\r\n                state.loadingSearch = true;\r\n            })\r\n            .addCase(fetchPublicExamsIfNeeded.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.examsSearch = action.payload.data;\r\n                }\r\n                state.loadingSearch = false;\r\n            })\r\n            .addCase(fetchPublicExamsIfNeeded.rejected, (state) => {\r\n                state.examsSearch = [];\r\n                state.loadingSearch = false;\r\n            })\r\n            .addCase(findExams.pending, (state) => {\r\n                state.examsSearch = [];\r\n                state.loadingSearch = true;\r\n            })\r\n            .addCase(findExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.examsSearch = action.payload.data;\r\n                }\r\n                state.loadingSearch = false;\r\n            })\r\n            .addCase(findExams.rejected, (state) => {\r\n                state.examsSearch = [];\r\n                state.loadingSearch = false;\r\n            })\r\n\r\n    }\r\n});\r\n\r\nexport const {\r\n    setExam,\r\n    setCurrentPage,\r\n    setLimit,\r\n    setSortOrder,\r\n    setLoading,\r\n    setSearch\r\n} = examSlice.actions;\r\nexport default examSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,sBAAsB,QAAQ,mCAAmC;AAE1E,OAAO,MAAMC,UAAU,GAAGR,gBAAgB,CACtC,kBAAkB,EAClB,OAAAS,IAAA,EAAAC,KAAA,KAAmE;EAAA,IAA5D;IAAEC,MAAM;IAAEC,WAAW;IAAEC,KAAK;IAAEC;EAAU,CAAC,GAAAL,IAAA;EAAA,IAAE;IAAEM;EAAS,CAAC,GAAAL,KAAA;EAC1D,OAAO,MAAMR,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAACe,aAAa,EAAE;IAAEL,MAAM;IAAEC,WAAW;IAAEC,KAAK;IAAEC;EAAU,CAAC,EAAGG,IAAI,IAAK;IAC1G;IACA;IACA;EAAA,CACH,EAAE,IAAI,EAAE,KAAK,CAAC;AACnB,CACJ,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGlB,gBAAgB,CAC5C,wBAAwB,EACxB,OAAOmB,CAAC,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEL;EAAS,CAAC,GAAAK,KAAA;EAClB,OAAO,MAAMlB,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAACoB,gBAAgB,EAAE,IAAI,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC9F,CACJ,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGtB,gBAAgB,CAC1C,sBAAsB,EACtB,OAAOmB,CAAC,EAAAI,KAAA,KAAmB;EAAA,IAAjB;IAAER;EAAS,CAAC,GAAAQ,KAAA;EAClB,OAAO,MAAMrB,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAACuB,gBAAgB,EAAE,IAAI,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC9F,CACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGzB,gBAAgB,CACzC,qBAAqB,EACrB,OAAO0B,EAAE,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEZ;EAAS,CAAC,GAAAY,KAAA;EACnB,OAAO,MAAMzB,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAACwB,aAAa,EAAEC,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACtG,CACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAME,iBAAiB,GAAG5B,gBAAgB,CAC7C,yBAAyB,EACzB,OAAO0B,EAAE,EAAAG,KAAA,KAAmB;EAAA,IAAjB;IAAEd;EAAS,CAAC,GAAAc,KAAA;EACnB,OAAO,MAAM3B,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAAC6B,iBAAiB,EAAEJ,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC7F,CACJ,CAAC;;AAED;AACA,OAAO,MAAMK,yBAAyB,GAAG/B,gBAAgB,CACrD,iCAAiC,EACjC,OAAO0B,EAAE,EAAAM,KAAA,KAA6B;EAAA,IAA3B;IAAEjB,QAAQ;IAAEkB;EAAS,CAAC,GAAAD,KAAA;EAC7B,MAAME,KAAK,GAAGD,QAAQ,CAAC,CAAC;EACxB,MAAM;IAAEE,YAAY;IAAEC;EAAwB,CAAC,GAAGF,KAAK,CAACG,KAAK;;EAE7D;EACA,MAAMC,OAAO,GAAGH,YAAY,CAACT,EAAE,CAAC,IAAIS,YAAY,CAACT,EAAE,CAAC,CAACa,MAAM,GAAG,CAAC;;EAE/D;EACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;EACtB,MAAME,WAAW,GAAGN,uBAAuB,CAACV,EAAE,CAAC,IAAI,CAAC;EACpD,MAAMiB,YAAY,GAAGH,GAAG,GAAGE,WAAW,GAAG,MAAM;;EAE/C;EACA,IAAIJ,OAAO,IAAIK,YAAY,EAAE;IACzB;IACA,OAAO;MAAE1B,IAAI,EAAEkB,YAAY,CAACT,EAAE;IAAE,CAAC;EACrC;;EAEA;EACA,OAAO,MAAMX,QAAQ,CAACa,iBAAiB,CAACF,EAAE,CAAC,CAAC,CAACkB,MAAM,CAAC,CAAC;AACzD,CACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG7C,gBAAgB,CACzC,qBAAqB,EACrB,OAAO0B,EAAE,EAAAoB,KAAA,KAAmB;EAAA,IAAjB;IAAE/B;EAAS,CAAC,GAAA+B,KAAA;EACnB,OAAO,MAAM5C,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAAC8C,cAAc,EAAErB,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACzF,CACJ,CAAC;AAED,OAAO,MAAMsB,SAAS,GAAGhD,gBAAgB,CACrC,iBAAiB,EACjB,OAAOW,MAAM,EAAAsC,KAAA,KAAmB;EAAA,IAAjB;IAAElC;EAAS,CAAC,GAAAkC,KAAA;EACvB,OAAO,MAAM/C,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAACiD,YAAY,EAAEvC,MAAM,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAC1G,CACJ,CAAC;AAED,OAAO,MAAMwC,mBAAmB,GAAGnD,gBAAgB,CAC/C,2BAA2B,EAC3B,OAAO0B,EAAE,EAAA0B,MAAA,KAAmB;EAAA,IAAjB;IAAErC;EAAS,CAAC,GAAAqC,MAAA;EACnB,OAAO,MAAMlD,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAACoD,aAAa,EAAE3B,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACzF,CACJ,CAAC;AAED,OAAO,MAAM4B,OAAO,GAAGtD,gBAAgB,CACnC,eAAe,EACf,OAAAuD,MAAA,EAAAC,MAAA,KAA8C;EAAA,IAAvC;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAAH,MAAA;EAAA,IAAE;IAAExC;EAAS,CAAC,GAAAyC,MAAA;EACrC,OAAO,MAAMtD,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAAC0D,UAAU,EAAE;IAAEF,MAAM;IAAEC;EAAS,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACvG,CACJ,CAAC;AAED,OAAO,MAAME,YAAY,GAAG5D,gBAAgB,CACxC,oBAAoB,EACpB,OAAA6D,MAAA,EAAAC,MAAA,KAA+C;EAAA,IAAxC;IAAEL,MAAM;IAAEM;EAAU,CAAC,GAAAF,MAAA;EAAA,IAAE;IAAE9C;EAAS,CAAC,GAAA+C,MAAA;EACtC,OAAO,MAAM5D,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAAC+D,eAAe,EAAE;IAAEP,MAAM;IAAEM;EAAU,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AAC7G,CACJ,CAAC;AAID,OAAO,MAAME,iBAAiB,GAAGjE,gBAAgB,CAC7C,yBAAyB,EACzB,OAAAkE,MAAA,EAAAC,MAAA,KAA6C;EAAA,IAAtC;IAAEV,MAAM;IAAEW;EAAQ,CAAC,GAAAF,MAAA;EAAA,IAAE;IAAEnD;EAAS,CAAC,GAAAoD,MAAA;EACpC,OAAO,MAAMjE,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAACoE,oBAAoB,EAAE;IAAEZ,MAAM;IAAEW;EAAQ,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AAC7H,CACJ,CAAC;AAED,OAAO,MAAME,aAAa,GAAGtE,gBAAgB,CACzC,qBAAqB,EACrB,OAAAuE,MAAA,EAAAC,MAAA,KAA6C;EAAA,IAAtC;IAAEf,MAAM;IAAEW;EAAQ,CAAC,GAAAG,MAAA;EAAA,IAAE;IAAExD;EAAS,CAAC,GAAAyD,MAAA;EACpC,OAAO,MAAMtE,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAACwE,gBAAgB,EAAE;IAAEhB,MAAM;IAAEW;EAAQ,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AACzH,CACJ,CAAC;AAED,OAAO,MAAMM,eAAe,GAAG1E,gBAAgB,CAC3C,uBAAuB,EACvB,OAAA2E,MAAA,EAAAC,MAAA,KAAoC;EAAA,IAA7B;IAAEnB;EAAO,CAAC,GAAAkB,MAAA;EAAA,IAAE;IAAE5D;EAAS,CAAC,GAAA6D,MAAA;EAC3B,OAAO,MAAM1E,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAAC4E,kBAAkB,EAAE;IAAEpB;EAAO,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACnH,CACJ,CAAC;AAED,OAAO,MAAMqB,UAAU,GAAG9E,gBAAgB,CACtC,kBAAkB,EAClB,OAAO0B,EAAE,EAAAqD,MAAA,KAAmB;EAAA,IAAjB;IAAEhE;EAAS,CAAC,GAAAgE,MAAA;EACnB,OAAO,MAAM7E,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAAC+E,aAAa,EAAEtD,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACxF,CACJ,CAAC;AAED,OAAO,MAAMuD,eAAe,GAAGjF,gBAAgB,CAC3C,uBAAuB,EACvB,OAAOW,MAAM,EAAAuE,MAAA,KAAmB;EAAA,IAAjB;IAAEnE;EAAS,CAAC,GAAAmE,MAAA;EACvB,OAAO,MAAMhF,UAAU,CAACa,QAAQ,EAAEd,OAAO,CAACkF,kBAAkB,EAAExE,MAAM,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAChH,CACJ,CAAC;AAED,OAAO,MAAMyE,wBAAwB,GAAGpF,gBAAgB,CACpD,gCAAgC,EAChC,OAAOW,MAAM,EAAA0E,MAAA,KAA6B;EAAA,IAA3B;IAAEtE,QAAQ;IAAEkB;EAAS,CAAC,GAAAoD,MAAA;EACjC,OAAO,MAAM9E,sBAAsB,CAAC;IAChCmB,EAAE,EAAEf,MAAM,CAAC2E,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAAI;IACnCC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,aAAa;IACvB1E,QAAQ;IACRkB,QAAQ;IACRyD,WAAW,EAAET,eAAe;IAAO;IACnCU,aAAa,EAAE,MAAM,CAAc;EACvC,CAAC,CAAC;AACN,CACJ,CAAC;AAED,MAAMC,SAAS,GAAG7F,WAAW,CAAC;EAC1B8F,IAAI,EAAE,OAAO;EACbC,YAAY,EAAE;IACVzD,KAAK,EAAE,EAAE;IACT0D,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,EAAE;IACf7D,YAAY,EAAE,CAAC,CAAC;IAAE;IAClBC,uBAAuB,EAAE,CAAC,CAAC;IAAE;IAC7B6D,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,KAAK;IACrBC,cAAc,EAAE,KAAK;IACrBC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;MAAE,GAAGpG;IAAuB,CAAC;IACzC,GAAGE;EACP,CAAC;EACDmG,QAAQ,EAAE;IACNC,OAAO,EAAEA,CAACvE,KAAK,EAAEwE,MAAM,KAAK;MACxBxE,KAAK,CAAC6D,IAAI,GAAGW,MAAM,CAACC,OAAO;IAC/B,CAAC;IACD,GAAGvG,kBAAkB;IACrB,GAAGE;EACP,CAAC;EACDsG,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAACtG,UAAU,CAACuG,OAAO,EAAG7E,KAAK,IAAK;MACpCA,KAAK,CAACG,KAAK,GAAG,EAAE;MAChBH,KAAK,CAAC8E,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDF,OAAO,CAACtG,UAAU,CAACyG,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MAC9C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBzE,KAAK,CAACG,KAAK,GAAGqE,MAAM,CAACC,OAAO,CAAC1F,IAAI;QACjCiB,KAAK,CAACqE,UAAU,GAAGG,MAAM,CAACC,OAAO,CAACJ,UAAU,IAAIpG,sBAAsB;MAC1E;MACA+B,KAAK,CAAC8E,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDF,OAAO,CAACtG,UAAU,CAAC0G,QAAQ,EAAGhF,KAAK,IAAK;MACrCA,KAAK,CAAC8E,OAAO,GAAG,KAAK;MACrB9E,KAAK,CAACG,KAAK,GAAG,EAAE;IACpB,CAAC,CAAC,CAEDyE,OAAO,CAAC5F,gBAAgB,CAAC6F,OAAO,EAAG7E,KAAK,IAAK;MAC1CA,KAAK,CAACG,KAAK,GAAG,EAAE;IACpB,CAAC,CAAC,CACDyE,OAAO,CAAC5F,gBAAgB,CAAC+F,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MACpD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBzE,KAAK,CAACG,KAAK,GAAGqE,MAAM,CAACC,OAAO,CAAC1F,IAAI;MACrC;IACJ,CAAC,CAAC,CACD6F,OAAO,CAACxF,cAAc,CAACyF,OAAO,EAAG7E,KAAK,IAAK;MACxCA,KAAK,CAACG,KAAK,GAAG,EAAE;MAChBH,KAAK,CAAC+D,WAAW,GAAG,IAAI;IAC5B,CAAC,CAAC,CACDa,OAAO,CAACxF,cAAc,CAAC2F,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MAClD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBzE,KAAK,CAACG,KAAK,GAAGqE,MAAM,CAACC,OAAO,CAAC1F,IAAI;MACrC;MACAiB,KAAK,CAAC+D,WAAW,GAAG,KAAK;IAC7B,CAAC,CAAC,CACDa,OAAO,CAACxF,cAAc,CAAC4F,QAAQ,EAAGhF,KAAK,IAAK;MACzCA,KAAK,CAAC+D,WAAW,GAAG,KAAK;IAC7B,CAAC,CAAC,CACDa,OAAO,CAAClF,iBAAiB,CAACmF,OAAO,EAAE,MAAM;MACtC;IAAA,CACH,CAAC,CACDD,OAAO,CAAClF,iBAAiB,CAACqF,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MACrD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB,MAAMlD,MAAM,GAAGiD,MAAM,CAACS,IAAI,CAACC,GAAG,CAAC,CAAC;QAChClF,KAAK,CAACG,KAAK,GAAGqE,MAAM,CAACC,OAAO,CAAC1F,IAAI;QACjCiB,KAAK,CAACC,YAAY,CAACsB,MAAM,CAAC,GAAGiD,MAAM,CAACC,OAAO,CAAC1F,IAAI;QAChDiB,KAAK,CAACE,uBAAuB,CAACqB,MAAM,CAAC,GAAGhB,IAAI,CAACD,GAAG,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC,CACDsE,OAAO,CAACjE,aAAa,CAACkE,OAAO,EAAG7E,KAAK,IAAK;MACvCA,KAAK,CAAC6D,IAAI,GAAG,IAAI;IACrB,CAAC,CAAC,CACDe,OAAO,CAACjE,aAAa,CAACoE,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MACjD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBzE,KAAK,CAAC6D,IAAI,GAAGW,MAAM,CAACC,OAAO,CAAC1F,IAAI;MACpC;IACJ,CAAC,CAAC,CACD6F,OAAO,CAAC9D,SAAS,CAAC+D,OAAO,EAAG7E,KAAK,IAAK;MACnCA,KAAK,CAAC8D,WAAW,GAAG,EAAE;IAC1B,CAAC,CAAC,CACDc,OAAO,CAAC9D,SAAS,CAACiE,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MAC7C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBzE,KAAK,CAAC8D,WAAW,GAAGU,MAAM,CAACC,OAAO,CAAC1F,IAAI;MAC3C;IACJ,CAAC,CAAC,CAED6F,OAAO,CAAC3D,mBAAmB,CAAC4D,OAAO,EAAG7E,KAAK,IAAK;MAC7CA,KAAK,CAAC6D,IAAI,GAAG,IAAI;IACrB,CAAC,CAAC,CACDe,OAAO,CAAC3D,mBAAmB,CAAC8D,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MACvD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBzE,KAAK,CAAC6D,IAAI,GAAGW,MAAM,CAACC,OAAO,CAAC1F,IAAI;MACpC;IACJ,CAAC,CAAC,CACD6F,OAAO,CAACpC,eAAe,CAACuC,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MACnD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAAA,IAAAU,aAAA;QAChB,MAAM;UAAE5D,MAAM;UAAE6D;QAAO,CAAC,GAAGZ,MAAM,CAACC,OAAO;;QAEzC;QACA,IAAIzE,KAAK,CAACG,KAAK,CAACE,MAAM,GAAG,CAAC,KAAA8E,aAAA,GAAInF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,cAAAgF,aAAA,eAAdA,aAAA,CAAgBtB,IAAI,EAAE;UAChD,IAAIuB,MAAM,KAAK,KAAK,EAAE;YAClB;YACApF,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAACkF,MAAM,CAACxB,IAAI;cAAA,IAAAyB,UAAA;cAAA,OAAI,EAAAA,UAAA,GAAAzB,IAAI,CAACA,IAAI,cAAAyB,UAAA,uBAATA,UAAA,CAAW9F,EAAE,MAAK+B,MAAM;YAAA,EAAC;UACtE,CAAC,MAAM;YACH;YACAvB,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAACoF,GAAG,CAAC1B,IAAI,IAAI;cAAA,IAAA2B,WAAA;cAClC,IAAI,EAAAA,WAAA,GAAA3B,IAAI,CAACA,IAAI,cAAA2B,WAAA,uBAATA,WAAA,CAAWhG,EAAE,MAAK+B,MAAM,EAAE;gBAC1B,OAAO;kBAAE,GAAGsC,IAAI;kBAAEuB,MAAM,EAAE;gBAAK,CAAC;cACpC;cACA,OAAOvB,IAAI;YACf,CAAC,CAAC;UACN;UACA;QACJ;;QAEA;QACA,IAAI7D,KAAK,CAACG,KAAK,IAAIsF,KAAK,CAACC,OAAO,CAAC1F,KAAK,CAACG,KAAK,CAAC,EAAE;UAC3CH,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAACoF,GAAG,CAAC1B,IAAI,IAAI;YAClC,IAAIA,IAAI,CAACrE,EAAE,KAAK+B,MAAM,EAAE;cACpB,OAAO;gBAAE,GAAGsC,IAAI;gBAAEuB,MAAM,EAAEA;cAAO,CAAC;YACtC;YACA,OAAOvB,IAAI;UACf,CAAC,CAAC;QACN;;QAEA;QACA,IAAI7D,KAAK,CAAC6D,IAAI,EAAE;UACZ7D,KAAK,CAAC6D,IAAI,CAACuB,MAAM,GAAGA,MAAM;QAC9B;MACJ;IACJ,CAAC,CAAC,CACDR,OAAO,CAAC7C,iBAAiB,CAAC8C,OAAO,EAAG7E,KAAK,IAAK;MAC3CA,KAAK,CAACmE,cAAc,GAAG,IAAI;IAC/B,CAAC,CAAC,CACDS,OAAO,CAAC7C,iBAAiB,CAACgD,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MACrD;MACA,MAAMmB,MAAM,GAAGnB,MAAM,CAACC,OAAO;MAC7B,IAAIzE,KAAK,CAAC6D,IAAI,EAAE;QACZ7D,KAAK,CAAC6D,IAAI,CAAC+B,cAAc,GAAGD,MAAM;MACtC;MACA3F,KAAK,CAACmE,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CACDS,OAAO,CAAC7C,iBAAiB,CAACiD,QAAQ,EAAGhF,KAAK,IAAK;MAC5CA,KAAK,CAACkE,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CACDU,OAAO,CAACxC,aAAa,CAACyC,OAAO,EAAG7E,KAAK,IAAK;MACvCA,KAAK,CAACkE,cAAc,GAAG,IAAI;IAC/B,CAAC,CAAC,CACDU,OAAO,CAACxC,aAAa,CAAC2C,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MACjD,MAAMmB,MAAM,GAAGnB,MAAM,CAACC,OAAO;MAC7B,IAAIzE,KAAK,CAAC6D,IAAI,EAAE;QACZ7D,KAAK,CAAC6D,IAAI,CAACgC,OAAO,GAAGF,MAAM;MAC/B;MACA3F,KAAK,CAACkE,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CACDU,OAAO,CAACxC,aAAa,CAAC4C,QAAQ,EAAGhF,KAAK,IAAK;MACxCA,KAAK,CAACkE,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CAEDU,OAAO,CAAC/E,yBAAyB,CAACkF,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MAC7D,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAAC1F,IAAI,EAAE;QACvCiB,KAAK,CAACG,KAAK,GAAGqE,MAAM,CAACC,OAAO,CAAC1F,IAAI;MACrC;IACJ,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAAA,CACC6F,OAAO,CAAC1B,wBAAwB,CAAC2B,OAAO,EAAG7E,KAAK,IAAK;MAClDA,KAAK,CAAC8D,WAAW,GAAG,EAAE;MACtB9D,KAAK,CAACgE,aAAa,GAAG,IAAI;IAC9B,CAAC,CAAC,CACDY,OAAO,CAAC1B,wBAAwB,CAAC6B,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MAC5D,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBzE,KAAK,CAAC8D,WAAW,GAAGU,MAAM,CAACC,OAAO,CAAC1F,IAAI;MAC3C;MACAiB,KAAK,CAACgE,aAAa,GAAG,KAAK;IAC/B,CAAC,CAAC,CACDY,OAAO,CAAC1B,wBAAwB,CAAC8B,QAAQ,EAAGhF,KAAK,IAAK;MACnDA,KAAK,CAAC8D,WAAW,GAAG,EAAE;MACtB9D,KAAK,CAACgE,aAAa,GAAG,KAAK;IAC/B,CAAC,CAAC,CACDY,OAAO,CAAC9D,SAAS,CAAC+D,OAAO,EAAG7E,KAAK,IAAK;MACnCA,KAAK,CAAC8D,WAAW,GAAG,EAAE;MACtB9D,KAAK,CAACgE,aAAa,GAAG,IAAI;IAC9B,CAAC,CAAC,CACDY,OAAO,CAAC9D,SAAS,CAACiE,SAAS,EAAE,CAAC/E,KAAK,EAAEwE,MAAM,KAAK;MAC7C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBzE,KAAK,CAAC8D,WAAW,GAAGU,MAAM,CAACC,OAAO,CAAC1F,IAAI;MAC3C;MACAiB,KAAK,CAACgE,aAAa,GAAG,KAAK;IAC/B,CAAC,CAAC,CACDY,OAAO,CAAC9D,SAAS,CAACkE,QAAQ,EAAGhF,KAAK,IAAK;MACpCA,KAAK,CAAC8D,WAAW,GAAG,EAAE;MACtB9D,KAAK,CAACgE,aAAa,GAAG,KAAK;IAC/B,CAAC,CAAC;EAEV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACTO,OAAO;EACPuB,cAAc;EACdC,QAAQ;EACRC,YAAY;EACZC,UAAU;EACVC;AACJ,CAAC,GAAGxC,SAAS,CAACyC,OAAO;AACrB,eAAezC,SAAS,CAAC0C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}