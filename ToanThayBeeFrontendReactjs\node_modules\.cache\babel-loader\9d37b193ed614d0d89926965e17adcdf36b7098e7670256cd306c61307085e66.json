{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageQuestionsExam\\\\QuestionView.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { FileText } from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\nimport { reorderStatements, reorderQuestions } from \"src/features/questionsExam/questionsExamSlice\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport SortableQuestionItem from \"./SortableQuestionItem\";\nimport QuestionContent from \"./QuestionContent\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuestionViewHeader = _ref => {\n  let {\n    title,\n    count,\n    noQuestionText\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" p-3 \",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-1 mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-3 h-3 text-gray-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-xs font-semibold text-gray-900\",\n        children: [title, \" (\", count, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this), count === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-4 text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-6 h-6 mx-auto mb-1 opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs\",\n        children: noQuestionText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 9\n  }, this);\n};\n_c = QuestionViewHeader;\nconst QuestionTNView = () => {\n  _s();\n  const {\n    questionsExam,\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const [questionsTN, setQuestionsTN] = useState([]);\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const dispatch = useDispatch();\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\n      const oldIndex = questionsExam.findIndex(q => q.id === active.id);\n      const newIndex = questionsExam.findIndex(q => q.id === over.id);\n\n      // Optimistic update: cập nhật local state ngay lập tức\n      const oldIndexInFiltered = questionsTN.findIndex(q => q.id === active.id);\n      const newIndexInFiltered = questionsTN.findIndex(q => q.id === over.id);\n      if (oldIndexInFiltered !== -1 && newIndexInFiltered !== -1) {\n        const newQuestionsTN = [...questionsTN];\n        const [movedQuestion] = newQuestionsTN.splice(oldIndexInFiltered, 1);\n        newQuestionsTN.splice(newIndexInFiltered, 0, movedQuestion);\n        setQuestionsTN(newQuestionsTN);\n      }\n\n      // Dispatch Redux action\n      dispatch(reorderQuestions({\n        oldIndex,\n        newIndex\n      }));\n    }\n  };\n  useEffect(() => {\n    const filteredTN = questionsExam.filter(q => q.typeOfQuestion === 'TN');\n    // console.log('TN Questions:', filteredTN.map(q => ({ id: q.id, content: q.content?.substring(0, 50) })));\n    setQuestionsTN(filteredTN);\n  }, [questionsExam]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\",\n      count: questionsTN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EAFc nghi\\u1EC7m\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col gap-4\",\n      children: /*#__PURE__*/_jsxDEV(DndContext, {\n        sensors: sensors,\n        collisionDetection: closestCenter,\n        onDragEnd: handleDragEnd,\n        children: /*#__PURE__*/_jsxDEV(SortableContext, {\n          items: questionsTN.map(q => q.id),\n          strategy: verticalListSortingStrategy,\n          children: questionsTN.map((q, index) => /*#__PURE__*/_jsxDEV(SortableQuestionItem, {\n            question: q,\n            index: index\n          }, q.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(QuestionTNView, \"O1EoMXSoFnJNB9YhxaV4wXmLVnQ=\", false, function () {\n  return [useSelector, useDispatch, useSensors, useSensor, useSensor];\n});\n_c2 = QuestionTNView;\nconst QuestionDSView = () => {\n  _s2();\n  const {\n    questionsExam,\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const [questionsDS, setQuestionsDS] = useState([]);\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const dispatch = useDispatch();\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\n      const oldIndex = questionsExam.findIndex(q => q.id === active.id);\n      const newIndex = questionsExam.findIndex(q => q.id === over.id);\n\n      // Optimistic update: cập nhật local state ngay lập tức\n      const oldIndexInFiltered = questionsDS.findIndex(q => q.id === active.id);\n      const newIndexInFiltered = questionsDS.findIndex(q => q.id === over.id);\n      if (oldIndexInFiltered !== -1 && newIndexInFiltered !== -1) {\n        const newQuestionsDS = [...questionsDS];\n        const [movedQuestion] = newQuestionsDS.splice(oldIndexInFiltered, 1);\n        newQuestionsDS.splice(newIndexInFiltered, 0, movedQuestion);\n        setQuestionsDS(newQuestionsDS);\n      }\n\n      // Dispatch Redux action\n      dispatch(reorderQuestions({\n        oldIndex,\n        newIndex\n      }));\n    }\n  };\n  useEffect(() => {\n    setQuestionsDS(questionsExam.filter(q => q.typeOfQuestion === 'DS'));\n  }, [questionsExam]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi \\u0111\\xFAng sai\",\n      count: questionsDS.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi \\u0111\\xFAng sai\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col gap-4\",\n      children: /*#__PURE__*/_jsxDEV(DndContext, {\n        sensors: sensors,\n        collisionDetection: closestCenter,\n        onDragEnd: handleDragEnd,\n        children: /*#__PURE__*/_jsxDEV(SortableContext, {\n          items: questionsDS.map(q => q.id),\n          strategy: verticalListSortingStrategy,\n          children: questionsDS.map((q, index) => /*#__PURE__*/_jsxDEV(SortableQuestionItem, {\n            question: q,\n            index: index\n          }, q.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s2(QuestionDSView, \"ZyfPPE8Yqw9ucPiA9vcDAiX5WLI=\", false, function () {\n  return [useSelector, useDispatch, useSensors, useSensor, useSensor];\n});\n_c3 = QuestionDSView;\nconst QuestionTLNView = () => {\n  _s3();\n  const {\n    questionsExam,\n    selectedIndex\n  } = useSelector(state => state.questionsExam);\n  const dispatch = useDispatch();\n  const [questionsTLN, setQuestionsTLN] = useState([]);\n  const sensors = useSensors(useSensor(PointerSensor, {\n    activationConstraint: {\n      distance: 8\n    }\n  }), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\n      const oldIndex = questionsExam.findIndex(q => q.id === active.id);\n      const newIndex = questionsExam.findIndex(q => q.id === over.id);\n\n      // Optimistic update: cập nhật local state ngay lập tức\n      const oldIndexInFiltered = questionsTLN.findIndex(q => q.id === active.id);\n      const newIndexInFiltered = questionsTLN.findIndex(q => q.id === over.id);\n      if (oldIndexInFiltered !== -1 && newIndexInFiltered !== -1) {\n        const newQuestionsTLN = [...questionsTLN];\n        const [movedQuestion] = newQuestionsTLN.splice(oldIndexInFiltered, 1);\n        newQuestionsTLN.splice(newIndexInFiltered, 0, movedQuestion);\n        setQuestionsTLN(newQuestionsTLN);\n      }\n\n      // Dispatch Redux action\n      dispatch(reorderQuestions({\n        oldIndex,\n        newIndex\n      }));\n    }\n  };\n  useEffect(() => {\n    setQuestionsTLN(questionsExam.filter(q => q.typeOfQuestion === 'TLN'));\n  }, [questionsExam]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionViewHeader, {\n      title: \"C\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\",\n      count: questionsTLN.length,\n      noQuestionText: \"Ch\\u01B0a c\\xF3 c\\xE2u h\\u1ECFi tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 flex flex-col gap-4\",\n      children: /*#__PURE__*/_jsxDEV(DndContext, {\n        sensors: sensors,\n        collisionDetection: closestCenter,\n        onDragEnd: handleDragEnd,\n        children: /*#__PURE__*/_jsxDEV(SortableContext, {\n          items: questionsTLN.map(q => q.id),\n          strategy: verticalListSortingStrategy,\n          children: questionsTLN.map((q, index) => /*#__PURE__*/_jsxDEV(SortableQuestionItem, {\n            question: q,\n            index: index\n          }, q.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s3(QuestionTLNView, \"jJJweLYgyB6Rw4dClMfVbmd8VEc=\", false, function () {\n  return [useSelector, useDispatch, useSensors, useSensor, useSensor];\n});\n_c4 = QuestionTLNView;\nconst QuestionView = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(QuestionTNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"border-gray-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(QuestionDSView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"border-gray-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(QuestionTLNView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_c5 = QuestionView;\nexport default QuestionView;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"QuestionViewHeader\");\n$RefreshReg$(_c2, \"QuestionTNView\");\n$RefreshReg$(_c3, \"QuestionDSView\");\n$RefreshReg$(_c4, \"QuestionTLNView\");\n$RefreshReg$(_c5, \"QuestionView\");", "map": {"version": 3, "names": ["FileText", "useEffect", "useState", "useSelector", "useDispatch", "setSelectedIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkdownPreviewWithMath", "reorderStatements", "reorderQuestions", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "SortableQuestionItem", "QuestionContent", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "title", "count", "noQuestionText", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "QuestionTNView", "_s", "questionsExam", "selectedIndex", "state", "questionsTN", "setQuestionsTN", "prefixTN", "dispatch", "sensors", "activationConstraint", "distance", "coordinateGetter", "handleDragEnd", "event", "active", "over", "id", "oldIndex", "findIndex", "q", "newIndex", "oldIndexInFiltered", "newIndexInFiltered", "newQuestionsTN", "movedQuestion", "splice", "filteredTN", "filter", "typeOfQuestion", "length", "collisionDetection", "onDragEnd", "items", "map", "strategy", "index", "question", "_c2", "QuestionDSView", "_s2", "questionsDS", "setQuestionsDS", "prefixDS", "newQuestionsDS", "_c3", "QuestionTLNView", "_s3", "questionsTLN", "setQuestionsTLN", "newQuestionsTLN", "_c4", "Question<PERSON>iew", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageQuestionsExam/QuestionView.jsx"], "sourcesContent": ["import { FileText } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setSelectedIndex } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport LatexRenderer from \"../latex/RenderLatex\";\r\nimport MarkdownPreviewWithMath from \"../latex/MarkDownPreview\";\r\nimport { reorderStatements, reorderQuestions } from \"src/features/questionsExam/questionsExamSlice\";\r\nimport {\r\n    DndContext,\r\n    closestCenter,\r\n    KeyboardSensor,\r\n    PointerSensor,\r\n    useSensor,\r\n    useSensors,\r\n} from '@dnd-kit/core';\r\nimport {\r\n    SortableContext,\r\n    sortableKeyboardCoordinates,\r\n    verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport SortableQuestionItem from \"./SortableQuestionItem\";\r\nimport QuestionContent from \"./QuestionContent\";\r\n\r\n\r\nconst QuestionViewHeader = ({ title, count, noQuestionText }) => {\r\n    return (\r\n        <div className=\" p-3 \">\r\n            <div className=\"flex items-center gap-1 mb-2\">\r\n                <FileText className=\"w-3 h-3 text-gray-600\" />\r\n                <h4 className=\"text-xs font-semibold text-gray-900\">{title} ({count})</h4>\r\n            </div>\r\n            {count === 0 && (\r\n                <div className=\"text-center py-4 text-gray-500\">\r\n                    <FileText className=\"w-6 h-6 mx-auto mb-1 opacity-50\" />\r\n                    <p className=\"text-xs\">{noQuestionText}</p>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst QuestionTNView = () => {\r\n    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const [questionsTN, setQuestionsTN] = useState([]);\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const dispatch = useDispatch();\r\n    \r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor, {\r\n            activationConstraint: {\r\n                distance: 8,\r\n            },\r\n        }),\r\n        useSensor(KeyboardSensor, {\r\n            coordinateGetter: sortableKeyboardCoordinates,\r\n        })\r\n    );\r\n\r\n    const handleDragEnd = (event) => {\r\n        const { active, over } = event;\r\n\r\n        if (active.id !== over?.id) {\r\n            // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\r\n            const oldIndex = questionsExam.findIndex(q => q.id === active.id);\r\n            const newIndex = questionsExam.findIndex(q => q.id === over.id);\r\n\r\n            // Optimistic update: cập nhật local state ngay lập tức\r\n            const oldIndexInFiltered = questionsTN.findIndex(q => q.id === active.id);\r\n            const newIndexInFiltered = questionsTN.findIndex(q => q.id === over.id);\r\n\r\n            if (oldIndexInFiltered !== -1 && newIndexInFiltered !== -1) {\r\n                const newQuestionsTN = [...questionsTN];\r\n                const [movedQuestion] = newQuestionsTN.splice(oldIndexInFiltered, 1);\r\n                newQuestionsTN.splice(newIndexInFiltered, 0, movedQuestion);\r\n                setQuestionsTN(newQuestionsTN);\r\n            }\r\n\r\n            // Dispatch Redux action\r\n            dispatch(reorderQuestions({\r\n                oldIndex,\r\n                newIndex\r\n            }));\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const filteredTN = questionsExam.filter(q => q.typeOfQuestion === 'TN');\r\n        // console.log('TN Questions:', filteredTN.map(q => ({ id: q.id, content: q.content?.substring(0, 50) })));\r\n        setQuestionsTN(filteredTN);\r\n    }, [questionsExam]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trắc nghiệm\" count={questionsTN.length} noQuestionText=\"Chưa có câu hỏi trắc nghiệm\" />\r\n            <div className=\"p-3 flex flex-col gap-4\">\r\n                <DndContext\r\n                    sensors={sensors}\r\n                    collisionDetection={closestCenter}\r\n                    onDragEnd={handleDragEnd}\r\n                >\r\n                    <SortableContext\r\n                        items={questionsTN.map(q => q.id)}\r\n                        strategy={verticalListSortingStrategy}\r\n                    >\r\n                        {questionsTN.map((q, index) => (\r\n                            <SortableQuestionItem\r\n                                key={q.id}\r\n                                question={q}\r\n                                index={index}\r\n                            />\r\n                        ))}\r\n                    </SortableContext>\r\n                </DndContext>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionDSView = () => {\r\n    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const [questionsDS, setQuestionsDS] = useState([]);\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const dispatch = useDispatch();\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor, {\r\n            activationConstraint: {\r\n                distance: 8,\r\n            },\r\n        }),\r\n        useSensor(KeyboardSensor, {\r\n            coordinateGetter: sortableKeyboardCoordinates,\r\n        })\r\n    );\r\n\r\n    const handleDragEnd = (event) => {\r\n        const { active, over } = event;\r\n\r\n        if (active.id !== over?.id) {\r\n            // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\r\n            const oldIndex = questionsExam.findIndex(q => q.id === active.id);\r\n            const newIndex = questionsExam.findIndex(q => q.id === over.id);\r\n\r\n            // Optimistic update: cập nhật local state ngay lập tức\r\n            const oldIndexInFiltered = questionsDS.findIndex(q => q.id === active.id);\r\n            const newIndexInFiltered = questionsDS.findIndex(q => q.id === over.id);\r\n\r\n            if (oldIndexInFiltered !== -1 && newIndexInFiltered !== -1) {\r\n                const newQuestionsDS = [...questionsDS];\r\n                const [movedQuestion] = newQuestionsDS.splice(oldIndexInFiltered, 1);\r\n                newQuestionsDS.splice(newIndexInFiltered, 0, movedQuestion);\r\n                setQuestionsDS(newQuestionsDS);\r\n            }\r\n\r\n            // Dispatch Redux action\r\n            dispatch(reorderQuestions({\r\n                oldIndex,\r\n                newIndex\r\n            }));\r\n        }\r\n    };\r\n    useEffect(() => {\r\n        setQuestionsDS(questionsExam.filter(q => q.typeOfQuestion === 'DS'));\r\n    }, [questionsExam]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi đúng sai\" count={questionsDS.length} noQuestionText=\"Chưa có câu hỏi đúng sai\" />\r\n            <div className=\"p-3 flex flex-col gap-4\">\r\n                <DndContext\r\n                    sensors={sensors}\r\n                    collisionDetection={closestCenter}\r\n                    onDragEnd={handleDragEnd}\r\n                >\r\n                    <SortableContext\r\n                        items={questionsDS.map(q => q.id)}\r\n                        strategy={verticalListSortingStrategy}\r\n                    >\r\n                        {questionsDS.map((q, index) => (\r\n                            <SortableQuestionItem\r\n                                key={q.id}\r\n                                question={q}\r\n                                index={index}\r\n                            />\r\n                        ))}\r\n                    </SortableContext>\r\n                </DndContext>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionTLNView = () => {\r\n    const { questionsExam, selectedIndex } = useSelector((state) => state.questionsExam);\r\n    const dispatch = useDispatch();\r\n    const [questionsTLN, setQuestionsTLN] = useState([]);\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor, {\r\n            activationConstraint: {\r\n                distance: 8,\r\n            },\r\n        }),\r\n        useSensor(KeyboardSensor, {\r\n            coordinateGetter: sortableKeyboardCoordinates,\r\n        })\r\n    );\r\n\r\n    const handleDragEnd = (event) => {\r\n        const { active, over } = event;\r\n\r\n        if (active.id !== over?.id) {\r\n            // Tìm index trong mảng gốc questionsExam, không phải mảng filtered\r\n            const oldIndex = questionsExam.findIndex(q => q.id === active.id);\r\n            const newIndex = questionsExam.findIndex(q => q.id === over.id);\r\n\r\n            // Optimistic update: cập nhật local state ngay lập tức\r\n            const oldIndexInFiltered = questionsTLN.findIndex(q => q.id === active.id);\r\n            const newIndexInFiltered = questionsTLN.findIndex(q => q.id === over.id);\r\n\r\n            if (oldIndexInFiltered !== -1 && newIndexInFiltered !== -1) {\r\n                const newQuestionsTLN = [...questionsTLN];\r\n                const [movedQuestion] = newQuestionsTLN.splice(oldIndexInFiltered, 1);\r\n                newQuestionsTLN.splice(newIndexInFiltered, 0, movedQuestion);\r\n                setQuestionsTLN(newQuestionsTLN);\r\n            }\r\n\r\n            // Dispatch Redux action\r\n            dispatch(reorderQuestions({\r\n                oldIndex,\r\n                newIndex\r\n            }));\r\n        }\r\n    };\r\n    useEffect(() => {\r\n        setQuestionsTLN(questionsExam.filter(q => q.typeOfQuestion === 'TLN'));\r\n    }, [questionsExam]);\r\n\r\n    return (\r\n        <>\r\n            <QuestionViewHeader title=\"Câu hỏi trả lời ngắn\" count={questionsTLN.length} noQuestionText=\"Chưa có câu hỏi trả lời ngắn\" />\r\n            <div className=\"p-3 flex flex-col gap-4\">\r\n                <DndContext\r\n                    sensors={sensors}\r\n                    collisionDetection={closestCenter}\r\n                    onDragEnd={handleDragEnd}\r\n                >\r\n                    <SortableContext\r\n                        items={questionsTLN.map(q => q.id)}\r\n                        strategy={verticalListSortingStrategy}\r\n                    >\r\n                        {questionsTLN.map((q, index) => (\r\n                            <SortableQuestionItem\r\n                                key={q.id}\r\n                                question={q}\r\n                                index={index}\r\n                            />\r\n                        ))}\r\n                    </SortableContext>\r\n                </DndContext>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n\r\nconst QuestionView = () => {\r\n\r\n    return (\r\n        <>\r\n            <QuestionTNView />\r\n            <hr className=\"border-gray-200\" />\r\n            <QuestionDSView />\r\n            <hr className=\"border-gray-200\" />\r\n            <QuestionTLNView />\r\n        </>\r\n    )\r\n}\r\n\r\nexport default QuestionView;"], "mappings": ";;;;AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,uBAAuB,MAAM,0BAA0B;AAC9D,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,+CAA+C;AACnG,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAC1B,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGhD,MAAMC,kBAAkB,GAAGC,IAAA,IAAsC;EAAA,IAArC;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAe,CAAC,GAAAH,IAAA;EACxD,oBACIJ,OAAA;IAAKQ,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAClBT,OAAA;MAAKQ,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBACzCT,OAAA,CAACtB,QAAQ;QAAC8B,SAAS,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Cb,OAAA;QAAIQ,SAAS,EAAC,qCAAqC;QAAAC,QAAA,GAAEJ,KAAK,EAAC,IAAE,EAACC,KAAK,EAAC,GAAC;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,EACLP,KAAK,KAAK,CAAC,iBACRN,OAAA;MAAKQ,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC3CT,OAAA,CAACtB,QAAQ;QAAC8B,SAAS,EAAC;MAAiC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDb,OAAA;QAAGQ,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAEF;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAC,EAAA,GAfKX,kBAAkB;AAiBxB,MAAMY,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,aAAa;IAAEC;EAAc,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACF,aAAa,CAAC;EACpF,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM0C,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAE9B,MAAM0C,OAAO,GAAG/B,UAAU,CACtBD,SAAS,CAACD,aAAa,EAAE;IACrBkC,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACFlC,SAAS,CAACF,cAAc,EAAE;IACtBqC,gBAAgB,EAAEhC;EACtB,CAAC,CACL,CAAC;EAED,MAAMiC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB;MACA,MAAMC,QAAQ,GAAGhB,aAAa,CAACiB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACjE,MAAMI,QAAQ,GAAGnB,aAAa,CAACiB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;;MAE/D;MACA,MAAMK,kBAAkB,GAAGjB,WAAW,CAACc,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACzE,MAAMM,kBAAkB,GAAGlB,WAAW,CAACc,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;MAEvE,IAAIK,kBAAkB,KAAK,CAAC,CAAC,IAAIC,kBAAkB,KAAK,CAAC,CAAC,EAAE;QACxD,MAAMC,cAAc,GAAG,CAAC,GAAGnB,WAAW,CAAC;QACvC,MAAM,CAACoB,aAAa,CAAC,GAAGD,cAAc,CAACE,MAAM,CAACJ,kBAAkB,EAAE,CAAC,CAAC;QACpEE,cAAc,CAACE,MAAM,CAACH,kBAAkB,EAAE,CAAC,EAAEE,aAAa,CAAC;QAC3DnB,cAAc,CAACkB,cAAc,CAAC;MAClC;;MAEA;MACAhB,QAAQ,CAACpC,gBAAgB,CAAC;QACtB8C,QAAQ;QACRG;MACJ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAEDzD,SAAS,CAAC,MAAM;IACZ,MAAM+D,UAAU,GAAGzB,aAAa,CAAC0B,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACS,cAAc,KAAK,IAAI,CAAC;IACvE;IACAvB,cAAc,CAACqB,UAAU,CAAC;EAC9B,CAAC,EAAE,CAACzB,aAAa,CAAC,CAAC;EAEnB,oBACIjB,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACIT,OAAA,CAACG,kBAAkB;MAACE,KAAK,EAAC,uCAAqB;MAACC,KAAK,EAAEc,WAAW,CAACyB,MAAO;MAACtC,cAAc,EAAC;IAA6B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1Hb,OAAA;MAAKQ,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACpCT,OAAA,CAACZ,UAAU;QACPoC,OAAO,EAAEA,OAAQ;QACjBsB,kBAAkB,EAAEzD,aAAc;QAClC0D,SAAS,EAAEnB,aAAc;QAAAnB,QAAA,eAEzBT,OAAA,CAACN,eAAe;UACZsD,KAAK,EAAE5B,WAAW,CAAC6B,GAAG,CAACd,CAAC,IAAIA,CAAC,CAACH,EAAE,CAAE;UAClCkB,QAAQ,EAAEtD,2BAA4B;UAAAa,QAAA,EAErCW,WAAW,CAAC6B,GAAG,CAAC,CAACd,CAAC,EAAEgB,KAAK,kBACtBnD,OAAA,CAACH,oBAAoB;YAEjBuD,QAAQ,EAAEjB,CAAE;YACZgB,KAAK,EAAEA;UAAM,GAFRhB,CAAC,CAACH,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAG,EAAA,CA3EKD,cAAc;EAAA,QACyBlC,WAAW,EAGnCC,WAAW,EAEZW,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAA6D,GAAA,GAZXtC,cAAc;AA6EpB,MAAMuC,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAEtC,aAAa;IAAEC;EAAc,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACF,aAAa,CAAC;EACpF,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM8E,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMnC,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM0C,OAAO,GAAG/B,UAAU,CACtBD,SAAS,CAACD,aAAa,EAAE;IACrBkC,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACFlC,SAAS,CAACF,cAAc,EAAE;IACtBqC,gBAAgB,EAAEhC;EACtB,CAAC,CACL,CAAC;EAED,MAAMiC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB;MACA,MAAMC,QAAQ,GAAGhB,aAAa,CAACiB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACjE,MAAMI,QAAQ,GAAGnB,aAAa,CAACiB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;;MAE/D;MACA,MAAMK,kBAAkB,GAAGmB,WAAW,CAACtB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACzE,MAAMM,kBAAkB,GAAGkB,WAAW,CAACtB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;MAEvE,IAAIK,kBAAkB,KAAK,CAAC,CAAC,IAAIC,kBAAkB,KAAK,CAAC,CAAC,EAAE;QACxD,MAAMqB,cAAc,GAAG,CAAC,GAAGH,WAAW,CAAC;QACvC,MAAM,CAAChB,aAAa,CAAC,GAAGmB,cAAc,CAAClB,MAAM,CAACJ,kBAAkB,EAAE,CAAC,CAAC;QACpEsB,cAAc,CAAClB,MAAM,CAACH,kBAAkB,EAAE,CAAC,EAAEE,aAAa,CAAC;QAC3DiB,cAAc,CAACE,cAAc,CAAC;MAClC;;MAEA;MACApC,QAAQ,CAACpC,gBAAgB,CAAC;QACtB8C,QAAQ;QACRG;MACJ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EACDzD,SAAS,CAAC,MAAM;IACZ8E,cAAc,CAACxC,aAAa,CAAC0B,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACS,cAAc,KAAK,IAAI,CAAC,CAAC;EACxE,CAAC,EAAE,CAAC3B,aAAa,CAAC,CAAC;EAEnB,oBACIjB,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACIT,OAAA,CAACG,kBAAkB;MAACE,KAAK,EAAC,kCAAkB;MAACC,KAAK,EAAEkD,WAAW,CAACX,MAAO;MAACtC,cAAc,EAAC;IAA0B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpHb,OAAA;MAAKQ,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACpCT,OAAA,CAACZ,UAAU;QACPoC,OAAO,EAAEA,OAAQ;QACjBsB,kBAAkB,EAAEzD,aAAc;QAClC0D,SAAS,EAAEnB,aAAc;QAAAnB,QAAA,eAEzBT,OAAA,CAACN,eAAe;UACZsD,KAAK,EAAEQ,WAAW,CAACP,GAAG,CAACd,CAAC,IAAIA,CAAC,CAACH,EAAE,CAAE;UAClCkB,QAAQ,EAAEtD,2BAA4B;UAAAa,QAAA,EAErC+C,WAAW,CAACP,GAAG,CAAC,CAACd,CAAC,EAAEgB,KAAK,kBACtBnD,OAAA,CAACH,oBAAoB;YAEjBuD,QAAQ,EAAEjB,CAAE;YACZgB,KAAK,EAAEA;UAAM,GAFRhB,CAAC,CAACH,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAA0C,GAAA,CAvEKD,cAAc;EAAA,QACyBzE,WAAW,EAGnCC,WAAW,EACZW,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAAoE,GAAA,GAXXN,cAAc;AAyEpB,MAAMO,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAM;IAAE7C,aAAa;IAAEC;EAAc,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACF,aAAa,CAAC;EACpF,MAAMM,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiF,YAAY,EAAEC,eAAe,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM4C,OAAO,GAAG/B,UAAU,CACtBD,SAAS,CAACD,aAAa,EAAE;IACrBkC,oBAAoB,EAAE;MAClBC,QAAQ,EAAE;IACd;EACJ,CAAC,CAAC,EACFlC,SAAS,CAACF,cAAc,EAAE;IACtBqC,gBAAgB,EAAEhC;EACtB,CAAC,CACL,CAAC;EAED,MAAMiC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB;MACA,MAAMC,QAAQ,GAAGhB,aAAa,CAACiB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MACjE,MAAMI,QAAQ,GAAGnB,aAAa,CAACiB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;;MAE/D;MACA,MAAMK,kBAAkB,GAAG0B,YAAY,CAAC7B,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKF,MAAM,CAACE,EAAE,CAAC;MAC1E,MAAMM,kBAAkB,GAAGyB,YAAY,CAAC7B,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKD,IAAI,CAACC,EAAE,CAAC;MAExE,IAAIK,kBAAkB,KAAK,CAAC,CAAC,IAAIC,kBAAkB,KAAK,CAAC,CAAC,EAAE;QACxD,MAAM2B,eAAe,GAAG,CAAC,GAAGF,YAAY,CAAC;QACzC,MAAM,CAACvB,aAAa,CAAC,GAAGyB,eAAe,CAACxB,MAAM,CAACJ,kBAAkB,EAAE,CAAC,CAAC;QACrE4B,eAAe,CAACxB,MAAM,CAACH,kBAAkB,EAAE,CAAC,EAAEE,aAAa,CAAC;QAC5DwB,eAAe,CAACC,eAAe,CAAC;MACpC;;MAEA;MACA1C,QAAQ,CAACpC,gBAAgB,CAAC;QACtB8C,QAAQ;QACRG;MACJ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EACDzD,SAAS,CAAC,MAAM;IACZqF,eAAe,CAAC/C,aAAa,CAAC0B,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACS,cAAc,KAAK,KAAK,CAAC,CAAC;EAC1E,CAAC,EAAE,CAAC3B,aAAa,CAAC,CAAC;EAEnB,oBACIjB,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACIT,OAAA,CAACG,kBAAkB;MAACE,KAAK,EAAC,6CAAsB;MAACC,KAAK,EAAEyD,YAAY,CAAClB,MAAO;MAACtC,cAAc,EAAC;IAA8B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7Hb,OAAA;MAAKQ,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACpCT,OAAA,CAACZ,UAAU;QACPoC,OAAO,EAAEA,OAAQ;QACjBsB,kBAAkB,EAAEzD,aAAc;QAClC0D,SAAS,EAAEnB,aAAc;QAAAnB,QAAA,eAEzBT,OAAA,CAACN,eAAe;UACZsD,KAAK,EAAEe,YAAY,CAACd,GAAG,CAACd,CAAC,IAAIA,CAAC,CAACH,EAAE,CAAE;UACnCkB,QAAQ,EAAEtD,2BAA4B;UAAAa,QAAA,EAErCsD,YAAY,CAACd,GAAG,CAAC,CAACd,CAAC,EAAEgB,KAAK,kBACvBnD,OAAA,CAACH,oBAAoB;YAEjBuD,QAAQ,EAAEjB,CAAE;YACZgB,KAAK,EAAEA;UAAM,GAFRhB,CAAC,CAACH,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA,eACR,CAAC;AAEX,CAAC;AAAAiD,GAAA,CAtEKD,eAAe;EAAA,QACwBhF,WAAW,EACnCC,WAAW,EAEZW,UAAU,EACtBD,SAAS,EAKTA,SAAS;AAAA;AAAA0E,GAAA,GAVXL,eAAe;AAwErB,MAAMM,YAAY,GAAGA,CAAA,KAAM;EAEvB,oBACInE,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACIT,OAAA,CAACe,cAAc;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBb,OAAA;MAAIQ,SAAS,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClCb,OAAA,CAACsD,cAAc;MAAA5C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBb,OAAA;MAAIQ,SAAS,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClCb,OAAA,CAAC6D,eAAe;MAAAnD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACrB,CAAC;AAEX,CAAC;AAAAuD,GAAA,GAXKD,YAAY;AAalB,eAAeA,YAAY;AAAC,IAAArD,EAAA,EAAAuC,GAAA,EAAAO,GAAA,EAAAM,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAvD,EAAA;AAAAuD,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}