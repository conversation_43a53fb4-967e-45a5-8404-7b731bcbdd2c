class ResponseDataPagination {
    constructor(data, pagination) {
        this.data = data;
        this.pagination = {
            page: pagination.page || 1,
            pageSize: pagination.pageSize || data.length,
            total: pagination.total || data.length,
            totalPages: pagination.totalPages || 1,
            sortOrder: pagination.sortOrder || 'DESC',
        };
    }
}

export default ResponseDataPagination;
