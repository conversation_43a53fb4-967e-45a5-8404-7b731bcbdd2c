'use strict'
import { Model } from 'sequelize'
export default (sequelize, DataTypes) => {
  class Lesson extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      Lesson.belongsTo(models.Class, {
        foreignKey: 'classId',
        as: 'class',
      })
      Lesson.hasMany(models.LearningItem, {
        foreignKey: 'lessonId',
        as: 'learningItems',
      })
      Lesson.hasMany(models.Attendance, {
        foreignKey: 'lessonId',
        as: 'attendances'
      })
    }
  }
  Lesson.init({
    name: DataTypes.STRING,
    description: DataTypes.TEXT,
    learningItemCount: DataTypes.INTEGER,
    day: DataTypes.DATE,
    classId: DataTypes.INTEGER,
    chapter: DataTypes.STRING,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE
  }, {
    sequelize,
    modelName: 'Lesson',
    tableName: 'lesson',
    indexes: [
      {
        name: 'idx_lesson_classId',
        fields: ['classId'],
      },
    ]
  })
  return Lesson
}