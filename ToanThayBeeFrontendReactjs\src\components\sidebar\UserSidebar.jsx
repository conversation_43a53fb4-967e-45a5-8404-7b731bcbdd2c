import { useSelector } from "react-redux";

const UserSidebar = () => {
    const user = useSelector(state => state.auth.user);
    const closeSidebar = useSelector(state => state.sidebar?.closeSidebar); // Fix lỗi undefined
    const avatar = user?.avatarUrl;
    return (
        <div className="w-full p-3 justify-center items-center rounded-lg gap-2 inline-flex hover:bg-[#f0f4fa] hover:text-[#253f61]'}">
            <div className="justify-center items-center flex overflow-hidden">
                <div className=" bg-white rounded-full border border-[#d9d9d9] justify-center items-center inline-flex overflow-hidden">
                    {avatar ? (
                        <img className={`${closeSidebar ? 'w-5 h-5' : 'w-8 h-8'} rounded-[282.34px]`} src={avatar ? avatar : "https://scontent.fhan18-1.fna.fbcdn.net/v/t39.30808-6/465613742_1509724689699736_1584381155012909401_n.jpg?_nc_cat=105&ccb=1-7&_nc_sid=6ee11a&_nc_eui2=AeHwbZJYGWvTLoyU4edGibdRgRUsjMtL1sWBFSyMy0vWxcpysFeS8CjKdSc_kHTpUY3XViRFJV6OjBh0u8brfziu&_nc_ohc=CVxQT7vwfIUQ7kNvgF-yo9Z&_nc_oc=AdiXd147HIQIir34CCoWXKwDdw8ROOrQSYuHMmYwKNhRdrMHhfnO3FyfmRGHsJq5n5w&_nc_zt=23&_nc_ht=scontent.fhan18-1.fna&_nc_gid=A7gOES3tLcInSvR6LP8YyUn&oh=00_AYD4KQijD_yjEcvSVDPPqsm3jSKjXXYfDG7b9ga45jdcTg&oe=67CF3E96"} />
                    ) : (
                        <svg className={`${closeSidebar ? 'w-5 h-5' : 'w-8 h-8'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" fill="none">
                            <path d="M7.34002 7.40502C6.67131 7.40502 6.01762 7.20666 5.46167 6.83504C4.90573 6.46342 4.47251 5.93524 4.21683 5.31733C3.96115 4.69943 3.89451 4.01957 4.02534 3.36377C4.15616 2.70798 4.47857 2.10573 4.95177 1.63323C5.42497 1.16073 6.0277 0.839211 6.68368 0.709358C7.33967 0.579505 8.01943 0.647155 8.63696 0.903746C9.25448 1.16034 9.78202 1.59434 10.1528 2.15083C10.5236 2.70733 10.721 3.36131 10.72 4.03002C10.7174 4.92518 10.3603 5.78285 9.72682 6.41535C9.09337 7.04786 8.23518 7.4037 7.34002 7.40502ZM7.34002 1.65002C6.8693 1.65002 6.40915 1.78961 6.01776 2.05112C5.62637 2.31264 5.32132 2.68435 5.14119 3.11923C4.96105 3.55412 4.91392 4.03266 5.00575 4.49434C5.09758 4.95601 5.32426 5.38009 5.65711 5.71293C5.98995 6.04578 6.41403 6.27246 6.87571 6.36429C7.33738 6.45612 7.81592 6.40899 8.25081 6.22885C8.68569 6.04872 9.0574 5.74367 9.31892 5.35228C9.58043 4.96089 9.72002 4.50074 9.72002 4.03002C9.72002 3.39881 9.46927 2.79344 9.02293 2.34711C8.5766 1.90077 7.97124 1.65002 7.34002 1.65002Z" fill="black" />
                            <path d="M8.21 15.8399C8.10072 15.7306 8.01643 15.5989 7.96291 15.4539C7.9094 15.3089 7.88793 15.154 7.9 14.9999H2V12.1099C2.70876 11.3532 3.56863 10.7538 4.52386 10.3507C5.4791 9.94759 6.50839 9.74971 7.545 9.76989H7.905C7.88205 9.60225 7.89808 9.43158 7.95185 9.27114C8.00561 9.11071 8.09567 8.96484 8.215 8.84489L8.275 8.78989C8.04 8.78989 7.775 8.75989 7.545 8.75989C6.3252 8.73093 5.11421 8.97339 3.99959 9.46974C2.88496 9.96609 1.89456 10.7039 1.1 11.6299C1.03509 11.7164 1 11.8217 1 11.9299V14.9999C1 15.2651 1.10536 15.5195 1.29289 15.707C1.48043 15.8945 1.73478 15.9999 2 15.9999H8.35L8.21 15.8399Z" fill="black" />
                            <path d="M16.84 11.66L15.84 11.355C15.7686 11.1104 15.6714 10.8741 15.55 10.65L16.05 9.71999C16.0663 9.68279 16.0702 9.64135 16.0612 9.60176C16.0522 9.56217 16.0308 9.5265 16 9.49999L15.275 8.77499C15.2476 8.7453 15.211 8.72571 15.1711 8.71936C15.1312 8.71302 15.0903 8.72028 15.055 8.73999L14.135 9.23999C13.9087 9.11209 13.6689 9.00979 13.42 8.93499L13.115 7.93499C13.1021 7.89818 13.0775 7.86657 13.045 7.84493C13.0126 7.82328 12.974 7.81278 12.935 7.81499H11.91C11.8706 7.81453 11.8322 7.827 11.8006 7.85048C11.769 7.87396 11.7459 7.90716 11.735 7.94499L11.43 8.94499C11.1796 9.01785 10.9381 9.11847 10.71 9.24499L9.80001 8.74499C9.76549 8.72566 9.72544 8.7186 9.6864 8.72496C9.64735 8.73131 9.61161 8.75071 9.58501 8.77999L8.84501 9.49999C8.81757 9.52888 8.79991 9.56567 8.79453 9.60515C8.78914 9.64463 8.79631 9.6848 8.81501 9.71999L9.31501 10.63C9.18288 10.8554 9.07718 11.0953 9.00001 11.345L8.00001 11.645C7.96218 11.6559 7.92898 11.679 7.9055 11.7106C7.88202 11.7422 7.86955 11.7806 7.87001 11.82V12.845C7.87296 12.8811 7.88705 12.9154 7.91034 12.9432C7.93363 12.9709 7.96496 12.9908 8.00001 13L9.00001 13.305C9.07391 13.5503 9.17451 13.7867 9.30001 14.01L8.80001 14.965C8.78093 14.9992 8.77354 15.0387 8.77895 15.0775C8.78436 15.1163 8.80229 15.1523 8.83001 15.18L9.55501 15.905C9.58333 15.9332 9.61984 15.9518 9.65934 15.9581C9.69884 15.9644 9.73932 15.9581 9.77501 15.94L10.71 15.44C10.9324 15.56 11.1671 15.6555 11.41 15.725L11.71 16.725C11.7223 16.762 11.7457 16.7943 11.777 16.8176C11.8083 16.8408 11.846 16.8539 11.885 16.855H12.91C12.9492 16.8546 12.9872 16.8419 13.0187 16.8185C13.0501 16.7951 13.0734 16.7624 13.085 16.725L13.39 15.7C13.6295 15.6299 13.8608 15.5343 14.08 15.415L15.025 15.915C15.0597 15.9334 15.0996 15.94 15.1384 15.9337C15.1772 15.9274 15.2129 15.9085 15.24 15.88L16 15.2C16.0204 15.1706 16.0314 15.1357 16.0314 15.1C16.0314 15.0642 16.0204 15.0293 16 15L15.5 14.06C15.6214 13.8394 15.7186 13.6064 15.79 13.365L16.79 13.06C16.8278 13.0491 16.861 13.026 16.8845 12.9944C16.908 12.9628 16.9205 12.9244 16.92 12.885V11.835C16.9246 11.8012 16.9195 11.7669 16.9054 11.7359C16.8912 11.7049 16.8685 11.6786 16.84 11.66ZM12.425 14C12.0943 14.001 11.7707 13.9037 11.4953 13.7206C11.2199 13.5375 11.0051 13.2767 10.8781 12.9714C10.7511 12.666 10.7176 12.3298 10.7819 12.0054C10.8461 11.681 11.0053 11.383 11.2391 11.1491C11.473 10.9153 11.771 10.7561 12.0954 10.6918C12.4199 10.6276 12.756 10.6611 13.0614 10.7881C13.3668 10.9151 13.6275 11.1299 13.8106 11.4053C13.9938 11.6807 14.091 12.0043 14.09 12.335C14.0887 12.7762 13.9128 13.1989 13.6009 13.5109C13.2889 13.8228 12.8662 13.9987 12.425 14Z" fill="black" />
                        </svg>
                    )}

                </div>
            </div>
            {!closeSidebar && (
                <div className="grow shrink basis-0 flex-col justify-start items-start gap-1 inline-flex">
                    <div className="self-stretch text-[#313f53] text-xs font-normal font-['Inter'] leading-none">{user.lastName + " " + user.firstName + " - " + user.userType}</div>
                </div>
            )
            }
        </div >
    )

}



export default UserSidebar;