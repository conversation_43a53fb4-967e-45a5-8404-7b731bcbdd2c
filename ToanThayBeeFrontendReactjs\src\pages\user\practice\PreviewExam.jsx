import UserLayout from "../../../layouts/UserLayout";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchPublicQuestionsByExamId } from "../../../features/question/questionSlice";
import { fetchRelatedExamsIfNeeded, fetchPublicExamById } from "../../../features/exam/examSlice";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import PreviewExam from "../../../components/detail/PreviewExam";
import RelatedExamCard from "../../../components/card/RelatedExamCard";
import { ChevronRight } from "lucide-react";
import Breadcrumb from "../../../components/breadcrumb/Breadcrumb";

const PreviewExamPage = () => {
    const { examId } = useParams();
    const { exam, exams } = useSelector((state) => state.exams);
    const { questions } = useSelector((state) => state.questions);
    const { loading } = useSelector((state) => state.states);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    useEffect(() => {
        dispatch(fetchPublicQuestionsByExamId(examId));
    }, [dispatch, examId]);

    useEffect(() => {
        dispatch(fetchPublicExamById(examId));
    }, [dispatch, examId]);

    useEffect(() => {
        dispatch(fetchRelatedExamsIfNeeded(examId));
    }, [dispatch, examId]);

    useEffect(() => {
        if (exam?.seeCorrectAnswer === false) {
            navigate(`/practice/exam/${examId}`);
        }
    }, [exam])

    return (
        <UserLayout>
            <div className="flex flex-col min-h-[100vh] overflow-y-auto hide-scrollbar items-center bg-gray-50 px-2 py-3 sm:px-3 sm:py-4 lg:px-4 lg:py-6">
                <div className="w-full lg:max-w-7xl lg:mx-auto overflow-hidden">
                    {/* Breadcrumb */}
                    <Breadcrumb items={[
                        { label: "Trang chủ", path: "/", icon: "home" },
                        { label: "Luyện đề", path: "/practice", icon: "practice" },
                        { label: exam?.name, path: `/practice/exam/${examId}`, icon: "exam" },
                        { label: "Xem trước", path: `/practice/exam/${examId}/preview`, icon: "preview", active: true }
                    ]}
                    />

                    {/* Main content */}
                    <div className="flex flex-col w-full lg:flex-row gap-4 sm:gap-6">
                        {/* Card */}
                        <div className="flex flex-col bg-white rounded-lg shadow-lg p-3 sm:p-4 lg:p-6 gap-2 sm:gap-3 lg:gap-4 w-full lg:w-3/4 overflow-hidden">
                            <div className="flex justify-center w-full flex-col gap-3 sm:gap-4">
                                <div className="text-xl sm:text-2xl font-semibold text-zinc-900 font-inter">
                                    {exam?.name}
                                </div>
                                <PreviewExam exam={exam} questions={questions} />
                            </div>
                        </div>

                        {/* Đề thi liên quan */}
                        {!loading && exams && exams.length > 0 && (
                            <div className="bg-white rounded shadow-lg p-3 sm:p-4 lg:p-6 w-full lg:w-1/4 h-fit sticky top-4">
                                <div className="flex items-center justify-between mb-3 sm:mb-4">
                                    <h2 className="text-base sm:text-lg font-semibold text-gray-800">Đề thi liên quan</h2>
                                    <button
                                        onClick={() => navigate('/practice')}
                                        className="flex items-center text-xs text-sky-600 hover:text-sky-800 transition-colors"
                                    >
                                        Xem tất cả
                                        <ChevronRight className="w-3 h-3 ml-1" />
                                    </button>
                                </div>

                                <div className="flex flex-col gap-2 sm:gap-3">
                                    {exams.map((relatedExam) => (
                                        <RelatedExamCard
                                            key={relatedExam.id}
                                            exam={relatedExam}
                                            onClick={() => navigate(`/practice/exam/${relatedExam.id}`)}
                                            compact={false}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </UserLayout>
    )
}

export default PreviewExamPage;
