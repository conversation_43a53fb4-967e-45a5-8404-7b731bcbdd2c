{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\latex\\\\MarkDownPreview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useMemo } from 'react';\nimport MarkdownPreview from \"@uiw/react-markdown-preview\";\nimport rehypeKatex from \"rehype-katex\";\nimport remarkMath from \"remark-math\";\nimport \"@uiw/react-markdown-preview/markdown.css\";\nimport \"katex/dist/katex.min.css\";\nimport \"../../styles/markdown-preview.css\";\nimport NoTranslate from '../utils/NoTranslate';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MarkdownPreviewWithMath = _ref => {\n  _s();\n  let {\n    content,\n    className = '',\n    style\n  } = _ref;\n  const processedContent = useMemo(() => {\n    if (!content) return \"\";\n\n    // Replace \\( \\) with $ $ for inline math\n    // Replace \\[ \\] with $$ $$ for block math\n    let processed = content.replace(/\\\\\\(/g, \"$\").replace(/\\\\\\)/g, \"$\").replace(/\\\\\\[/g, \"$$\").replace(/\\\\\\]/g, \"$$\");\n    return processed;\n  }, [content]);\n  return /*#__PURE__*/_jsxDEV(NoTranslate, {\n    as: \"div\",\n    className: className,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(MarkdownPreview, {\n      source: processedContent,\n      remarkPlugins: [remarkMath],\n      rehypePlugins: [[rehypeKatex, {\n        strict: false,\n        // Tắt strict mode để không warning với Unicode\n        throwOnError: false,\n        // Không throw error\n        errorColor: '#cc0000',\n        macros: {\n          \"\\\\RR\": \"\\\\mathbb{R}\",\n          \"\\\\NN\": \"\\\\mathbb{N}\",\n          \"\\\\ZZ\": \"\\\\mathbb{Z}\",\n          \"\\\\QQ\": \"\\\\mathbb{Q}\",\n          \"\\\\CC\": \"\\\\mathbb{C}\"\n        }\n      }]]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 9\n  }, this);\n};\n_s(MarkdownPreviewWithMath, \"pj7ZUFO/TIPT+0Iv71RTcfjVF4c=\");\n_c = MarkdownPreviewWithMath;\nexport default MarkdownPreviewWithMath;\nvar _c;\n$RefreshReg$(_c, \"MarkdownPreviewWithMath\");", "map": {"version": 3, "names": ["React", "useMemo", "MarkdownPreview", "rehypeKatex", "remarkMath", "NoTranslate", "jsxDEV", "_jsxDEV", "MarkdownPreviewWithMath", "_ref", "_s", "content", "className", "style", "processedContent", "processed", "replace", "as", "children", "source", "remarkPlugins", "rehypePlugins", "strict", "throwOnError", "errorColor", "macros", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/latex/MarkDownPreview.jsx"], "sourcesContent": ["import React, { useMemo } from 'react';\r\nimport MarkdownPreview from \"@uiw/react-markdown-preview\";\r\nimport rehypeKatex from \"rehype-katex\";\r\nimport remarkMath from \"remark-math\";\r\nimport \"@uiw/react-markdown-preview/markdown.css\";\r\nimport \"katex/dist/katex.min.css\";\r\nimport \"../../styles/markdown-preview.css\";\r\nimport NoTranslate from '../utils/NoTranslate';\r\n\r\nconst MarkdownPreviewWithMath = ({ content, className='', style }) => {\r\n\r\n    const processedContent = useMemo(() => {\r\n        if (!content) return \"\";\r\n\r\n        // Replace \\( \\) with $ $ for inline math\r\n        // Replace \\[ \\] with $$ $$ for block math\r\n        let processed = content\r\n            .replace(/\\\\\\(/g, \"$\")\r\n            .replace(/\\\\\\)/g, \"$\")\r\n            .replace(/\\\\\\[/g, \"$$\")\r\n            .replace(/\\\\\\]/g, \"$$\");\r\n\r\n        return processed;\r\n    }, [content]);\r\n\r\n    return (\r\n        <NoTranslate as=\"div\" className={className} style={style}>\r\n            <MarkdownPreview\r\n                source={processedContent}\r\n                remarkPlugins={[remarkMath]}\r\n                rehypePlugins={[\r\n                    [rehypeKatex, {\r\n                        strict: false, // Tắt strict mode để không warning với Unicode\r\n                        throwOnError: false, // Không throw error\r\n                        errorColor: '#cc0000',\r\n                        macros: {\r\n                            \"\\\\RR\": \"\\\\mathbb{R}\",\r\n                            \"\\\\NN\": \"\\\\mathbb{N}\",\r\n                            \"\\\\ZZ\": \"\\\\mathbb{Z}\",\r\n                            \"\\\\QQ\": \"\\\\mathbb{Q}\",\r\n                            \"\\\\CC\": \"\\\\mathbb{C}\"\r\n                        }\r\n                    }]\r\n                ]}\r\n            />\r\n        </NoTranslate>\r\n    );\r\n};\r\n\r\nexport default MarkdownPreviewWithMath;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAO,0CAA0C;AACjD,OAAO,0BAA0B;AACjC,OAAO,mCAAmC;AAC1C,OAAOC,WAAW,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,uBAAuB,GAAGC,IAAA,IAAsC;EAAAC,EAAA;EAAA,IAArC;IAAEC,OAAO;IAAEC,SAAS,GAAC,EAAE;IAAEC;EAAM,CAAC,GAAAJ,IAAA;EAE7D,MAAMK,gBAAgB,GAAGb,OAAO,CAAC,MAAM;IACnC,IAAI,CAACU,OAAO,EAAE,OAAO,EAAE;;IAEvB;IACA;IACA,IAAII,SAAS,GAAGJ,OAAO,CAClBK,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;IAE3B,OAAOD,SAAS;EACpB,CAAC,EAAE,CAACJ,OAAO,CAAC,CAAC;EAEb,oBACIJ,OAAA,CAACF,WAAW;IAACY,EAAE,EAAC,KAAK;IAACL,SAAS,EAAEA,SAAU;IAACC,KAAK,EAAEA,KAAM;IAAAK,QAAA,eACrDX,OAAA,CAACL,eAAe;MACZiB,MAAM,EAAEL,gBAAiB;MACzBM,aAAa,EAAE,CAAChB,UAAU,CAAE;MAC5BiB,aAAa,EAAE,CACX,CAAClB,WAAW,EAAE;QACVmB,MAAM,EAAE,KAAK;QAAE;QACfC,YAAY,EAAE,KAAK;QAAE;QACrBC,UAAU,EAAE,SAAS;QACrBC,MAAM,EAAE;UACJ,MAAM,EAAE,aAAa;UACrB,MAAM,EAAE,aAAa;UACrB,MAAM,EAAE,aAAa;UACrB,MAAM,EAAE,aAAa;UACrB,MAAM,EAAE;QACZ;MACJ,CAAC,CAAC;IACJ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEtB,CAAC;AAACnB,EAAA,CAtCIF,uBAAuB;AAAAsB,EAAA,GAAvBtB,uBAAuB;AAwC7B,eAAeA,uBAAuB;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}