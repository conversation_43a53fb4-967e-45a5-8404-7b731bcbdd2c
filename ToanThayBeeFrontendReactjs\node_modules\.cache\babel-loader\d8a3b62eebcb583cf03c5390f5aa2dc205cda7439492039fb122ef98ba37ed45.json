{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$(),\n  _s8 = $RefreshSig$(),\n  _s9 = $RefreshSig$();\n// Optimized Form Panel Component\nimport { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport { setExamData, postExam, nextStep, prevStep, setExamImage, setExamFile, setQuestionTNContent, setQuestionDSContent, setQuestionTLNContent, setCorrectAnswerTN, setCorrectAnswerDS, setCorrectAnswerTLN, setQuestions, setSelectedIndex } from \"src/features/addExam/addExamSlice\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport CompactStepHeader from \"./CompactStepHeader\";\nimport { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, CheckCircle, ChevronRight, ChevronLeft, Plus, Save, Trash2 } from \"lucide-react\";\nimport ImageUpload from \"src/components/image/UploadImage\";\nimport UploadPdf from \"src/components/UploadPdf\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport NavigateBar from \"./NavigateBar\";\nimport SolutionEditor from \"./SolutionEditor\";\nimport { normalizeText } from \"src/utils/question/questionUtils\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Step1Form = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    examData,\n    examImage,\n    examFile\n  } = useSelector(state => state.addExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const updateExamData = (field, value) => {\n    dispatch(setExamData({\n      field,\n      value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"T\\xEAn \\u0111\\u1EC1 thi \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: examData.name || '',\n          onChange: e => updateExamData('name', e.target.value),\n          className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"Nh\\u1EADp t\\xEAn \\u0111\\u1EC1 thi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"Ki\\u1EC3u \\u0111\\u1EC1 \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: examData.typeOfExam,\n          onChange: option => updateExamData('typeOfExam', option),\n          options: Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: examData.class,\n          onChange: option => updateExamData('class', option),\n          options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [\"N\\u0103m \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n          selectedOption: examData.year,\n          onChange: option => updateExamData('year', option),\n          options: Array.isArray(codes[\"year\"]) ? codes[\"year\"] : [],\n          className: \"text-xs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(Clock, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 25\n          }, this), \"Th\\u1EDDi gian (ph\\xFAt)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: examData.testDuration || '',\n          onChange: e => updateExamData('testDuration', e.target.value),\n          className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"90\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(Users, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 25\n          }, this), \"\\u0110i\\u1EC3m \\u0111\\u1EA1t (%)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: examData.passRate || '',\n          onChange: e => updateExamData('passRate', e.target.value),\n          className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this), examData.typeOfExam === \"OT\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700 mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(BookOpen, {\n          className: \"w-3 h-3 inline mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 25\n        }, this), \"Ch\\u01B0\\u01A1ng\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n        selectedOption: examData.chapter,\n        onChange: option => updateExamData('chapter', option),\n        options: Array.isArray(codes[\"chapter\"]) ? codes[\"chapter\"] : [],\n        className: \"text-xs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700 mb-1\",\n        children: \"Link l\\u1EDDi gi\\u1EA3i\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: examData.solutionUrl,\n        onChange: e => updateExamData('solutionUrl', e.target.value),\n        className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n        placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i vd: youtube, ...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700 mb-1\",\n        children: \"M\\xF4 t\\u1EA3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: examData.description || '',\n        onChange: e => updateExamData('description', e.target.value),\n        rows: 2,\n        className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n        placeholder: \"M\\xF4 t\\u1EA3 ng\\u1EAFn v\\u1EC1 \\u0111\\u1EC1 thi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: examData.public || false,\n          onChange: e => updateExamData('public', e.target.checked),\n          className: \"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-1 text-gray-700\",\n          children: \"C\\xF4ng khai\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"flex items-center text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: examData.isClassroomExam || false,\n          onChange: e => updateExamData('isClassroomExam', e.target.checked),\n          className: \"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-1 text-gray-700\",\n          children: \"\\u0110\\u1EC1 thi l\\u1EDBp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(ImageIcon, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 25\n          }, this), \"\\u1EA2nh \\u0111\\u1EC1 thi\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ImageUpload, {\n          image: examImage,\n          setImage: img => dispatch(setExamImage(img)),\n          inputId: \"exam-image-compact\",\n          compact: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-xs font-medium text-gray-700 mb-1\",\n          children: [/*#__PURE__*/_jsxDEV(Upload, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 25\n          }, this), \"File PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(UploadPdf, {\n          setPdf: pdf => dispatch(setExamFile(pdf)),\n          deleteButton: false,\n          compact: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 9\n  }, this);\n};\n_s(Step1Form, \"P4fhebAumt8V+6XTy3zQSZoxQZU=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = Step1Form;\nconst ButtonAddQuestion = _ref => {\n  let {\n    text,\n    onClick\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onClick,\n    className: \"w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs\",\n    children: [/*#__PURE__*/_jsxDEV(Plus, {\n      className: \"w-3 h-3 inline mr-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 13\n    }, this), text]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 9\n  }, this);\n};\n_c2 = ButtonAddQuestion;\nconst TextArea = _ref2 => {\n  _s2();\n  let {\n    value,\n    onChange,\n    placeholder,\n    label,\n    Icon = null,\n    buttonFilterText = false\n  } = _ref2;\n  const textAreaRef = useRef(null);\n  useEffect(() => {\n    if (textAreaRef.current) {\n      textAreaRef.current.style.height = \"auto\"; // Reset để tính lại\n      textAreaRef.current.style.height = textAreaRef.current.scrollHeight + \"px\";\n    }\n  }, [value]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-row justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-xs font-medium text-gray-700\",\n        children: [Icon && /*#__PURE__*/_jsxDEV(Icon, {\n          className: \"w-3 h-3 inline mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 30\n        }, this), label]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 17\n      }, this), buttonFilterText && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: buttonFilterText.onClick,\n        className: \"text-xs font-medium text-white hover:underline px-2 py-1 bg-blue-500 rounded\",\n        children: buttonFilterText.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n      ref: textAreaRef,\n      value: value,\n      onChange: onChange,\n      className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none overflow-hidden\",\n      placeholder: placeholder,\n      rows: 1 // để bắt đầu nhỏ nhất có thể\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 9\n  }, this);\n};\n_s2(TextArea, \"sgKjUq37zymv5NZ2Nt9pN3iZA3Y=\");\n_c3 = TextArea;\nconst AddQuestionForm = _ref3 => {\n  let {\n    questionContent,\n    correctAnswerContent,\n    handleContentChange,\n    handleCorrectAnswerChange\n  } = _ref3;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-3 flex flex-col gap-4\",\n    children: [/*#__PURE__*/_jsxDEV(TextArea, {\n      value: correctAnswerContent,\n      onChange: handleCorrectAnswerChange,\n      placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n      label: \"\\u0110\\xE1p \\xE1n\",\n      Icon: CheckCircle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n      value: questionContent,\n      onChange: handleContentChange,\n      placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n      label: \"C\\xE2u h\\u1ECFi\",\n      Icon: Plus,\n      buttonFilterText: {\n        text: \"Lọc\",\n        onClick: () => {\n          handleContentChange(normalizeText(questionContent));\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 266,\n    columnNumber: 9\n  }, this);\n};\n_c4 = AddQuestionForm;\nconst AddTNQuestion = () => {\n  _s3();\n  const dispatch = useDispatch();\n  const {\n    questionTNContent,\n    correctAnswerTN\n  } = useSelector(state => state.addExam);\n  const handleContentChange = e => {\n    dispatch(setQuestionTNContent(e.target.value));\n  };\n  const handleCorrectAnswerChange = e => {\n    dispatch(setCorrectAnswerTN(e.target.value));\n  };\n  return /*#__PURE__*/_jsxDEV(AddQuestionForm, {\n    questionContent: questionTNContent,\n    correctAnswerContent: correctAnswerTN,\n    handleContentChange: handleContentChange,\n    handleCorrectAnswerChange: handleCorrectAnswerChange\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 305,\n    columnNumber: 9\n  }, this);\n};\n_s3(AddTNQuestion, \"TUrm8UjG+jst7xNoz/PmxzyVdbI=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c5 = AddTNQuestion;\nconst AddDSQuestion = () => {\n  _s4();\n  const dispatch = useDispatch();\n  const {\n    questionDSContent,\n    correctAnswerDS\n  } = useSelector(state => state.addExam);\n  const handleContentChange = e => {\n    dispatch(setQuestionDSContent(e.target.value));\n  };\n  const handleCorrectAnswerChange = e => {\n    dispatch(setCorrectAnswerDS(e.target.value));\n  };\n  return /*#__PURE__*/_jsxDEV(AddQuestionForm, {\n    questionContent: questionDSContent,\n    correctAnswerContent: correctAnswerDS,\n    handleContentChange: handleContentChange,\n    handleCorrectAnswerChange: handleCorrectAnswerChange\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 327,\n    columnNumber: 9\n  }, this);\n};\n_s4(AddDSQuestion, \"OhFBIlLHYM7nXLOS6xnOobx4j7k=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c6 = AddDSQuestion;\nconst AddTLNQuestion = () => {\n  _s5();\n  const dispatch = useDispatch();\n  const {\n    questionTLNContent,\n    correctAnswerTLN\n  } = useSelector(state => state.addExam);\n  const handleContentChange = e => {\n    dispatch(setQuestionTLNContent(e.target.value));\n  };\n  const handleCorrectAnswerChange = e => {\n    dispatch(setCorrectAnswerTLN(e.target.value));\n  };\n  return /*#__PURE__*/_jsxDEV(AddQuestionForm, {\n    questionContent: questionTLNContent,\n    correctAnswerContent: correctAnswerTLN,\n    handleContentChange: handleContentChange,\n    handleCorrectAnswerChange: handleCorrectAnswerChange\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 349,\n    columnNumber: 9\n  }, this);\n};\n_s5(AddTLNQuestion, \"2wyCIdvmC1fR2wIxMXSCGwa2CEM=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c7 = AddTLNQuestion;\nconst Step2Form = () => {\n  _s6();\n  const [isViewAdd, setIsViewAdd] = useState(true);\n  const [view, setView] = useState('TN');\n  const {\n    questionTNContent,\n    questionDSContent,\n    questionTLNContent,\n    correctAnswerTN,\n    correctAnswerDS,\n    correctAnswerTLN\n  } = useSelector(state => state.addExam);\n  useEffect(() => {\n    if (!isViewAdd) return;\n    if (questionTNContent.trim() !== \"\" || correctAnswerTN.trim() !== \"\") {\n      setIsViewAdd(false);\n      setView('TN');\n    } else if (questionDSContent.trim() !== \"\" || correctAnswerDS.trim() !== \"\") {\n      setIsViewAdd(false);\n      setView('DS');\n    } else if (questionTLNContent.trim() !== \"\" || correctAnswerTLN.trim() !== \"\") {\n      setIsViewAdd(false);\n      setView('TLN');\n    }\n  }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3\",\n    children: [isViewAdd ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-4 px-3\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-8 h-8 mx-auto text-gray-400 mb-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-900 mb-1\",\n        children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-600 mb-3\",\n        children: \"T\\u1EA1o c\\xE2u h\\u1ECFi cho \\u0111\\u1EC1 thi c\\u1EE7a b\\u1EA1n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(ButtonAddQuestion, {\n          text: \"Th\\xEAm c\\xE2u tr\\u1EAFc nghi\\u1EC7m\",\n          onClick: () => {\n            setIsViewAdd(false);\n            setView('TN');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(ButtonAddQuestion, {\n          text: \"Th\\xEAm c\\xE2u \\u0111\\xFAng sai\",\n          onClick: () => {\n            setIsViewAdd(false);\n            setView('DS');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(ButtonAddQuestion, {\n          text: \"Th\\xEAm c\\xE2u tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\",\n          onClick: () => {\n            setIsViewAdd(false);\n            setView('TLN');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(NavigateBar, {\n      list: [{\n        id: 1,\n        name: 'Trắc nghiệm',\n        value: 'TN'\n      }, {\n        id: 2,\n        name: 'Đúng sai',\n        value: 'DS'\n      }, {\n        id: 3,\n        name: 'Trả lời ngắn',\n        value: 'TLN'\n      }],\n      active: view,\n      setActive: setView\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 17\n    }, this), view === 'TN' && !isViewAdd && /*#__PURE__*/_jsxDEV(AddTNQuestion, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 17\n    }, this), view === 'DS' && !isViewAdd && /*#__PURE__*/_jsxDEV(AddDSQuestion, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 17\n    }, this), view === 'TLN' && !isViewAdd && /*#__PURE__*/_jsxDEV(AddTLNQuestion, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 378,\n    columnNumber: 9\n  }, this);\n};\n_s6(Step2Form, \"dcub7DBrYmyBBirG/id8qBEAGuQ=\", false, function () {\n  return [useSelector];\n});\n_c8 = Step2Form;\nconst ListQuestions = _ref4 => {\n  let {\n    count,\n    title,\n    onClick,\n    i\n  } = _ref4;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex  flex-row w-full justify-start items-center border-b border-[#e3e4e5] pb-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-xs font-bold font-bevietnam leading-loose whitespace-nowrap mr-4 flex-shrink-0 min-w-[6.2rem]\",\n      children: [title, \":\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-row gap-5 w-full overflow-x-auto min-h-max \",\n      children: Array.from({\n        length: count\n      }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => onClick(index),\n        className: \"cursor-pointer border text-xs border-[#e3e4e5] rounded flex justify-center whitespace-nowrap items-center gap-2.5 \".concat(i === index ? 'bg-[#253f61] text-white' : 'bg-white text-[#253f61]', \" px-2 py-1\"),\n        children: [\"C\\xE2u h\\u1ECFi \", index + 1]\n      }, index + title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 448,\n    columnNumber: 9\n  }, this);\n};\n_c9 = ListQuestions;\nconst ImageDropZone = _ref5 => {\n  _s7();\n  let {\n    imageUrl,\n    onImageDrop,\n    onImageRemove\n  } = _ref5;\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDraggingOver(false);\n    const draggedImage = e.dataTransfer.getData(\"text/plain\");\n    if (draggedImage && onImageDrop) {\n      onImageDrop(draggedImage);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragEnter = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.dataTransfer.types.includes('text/plain')) {\n      setIsDraggingOver(true);\n    }\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!e.currentTarget.contains(e.relatedTarget)) {\n      setIsDraggingOver(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative rounded-lg w-full p-4 transition-all duration-200 min-h-[60px] flex items-center justify-center\\n                    \".concat(isDraggingOver ? \"border-2 border-dashed border-blue-400 bg-blue-50\" : \"border border-gray-200 hover:border-blue-300 hover:bg-blue-25\"),\n    onDragOver: handleDragOver,\n    onDragEnter: handleDragEnter,\n    onDragLeave: handleDragLeave,\n    onDrop: handleDrop,\n    children: imageUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative group w-fit bg-gray-50 rounded-lg p-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: \"Attached image\",\n        className: \"rounded-md max-h-48 max-w-full object-contain\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n        onClick: onImageRemove,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 text-xs text-gray-500 text-center\",\n        children: \"\\u1EA2nh \\u0111\\xE3 th\\xEAm \\u2022 Click v\\xE0o icon \\u0111\\u1EC3 x\\xF3a\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center justify-center gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Plus, {\n        className: \"w-5 h-5 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: \"Th\\xEAm \\u1EA3nh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 505,\n    columnNumber: 9\n  }, this);\n};\n_s7(ImageDropZone, \"VKW1ZLDpPe+zo2/qAoOWVX5YkVs=\");\n_c10 = ImageDropZone;\nconst Step3Form = () => {\n  _s8();\n  const {\n    questions,\n    selectedIndex\n  } = useSelector(state => state.addExam);\n  const dispatch = useDispatch();\n  const [questionCount, setQuestionCount] = useState({\n    TN: 0,\n    DS: 0,\n    TLN: 0\n  });\n  const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const [optionChapter, setOptionChapter] = useState([]);\n  useEffect(() => {\n    if (questions) {\n      const counts = questions.reduce((acc, q) => {\n        const type = q.questionData.typeOfQuestion;\n        if (acc[type] !== undefined) acc[type]++;\n        return acc;\n      }, {\n        TN: 0,\n        DS: 0,\n        TLN: 0\n      });\n      setQuestionCount(counts);\n    }\n  }, [questions]);\n  useEffect(() => {\n    if (Array.isArray(codes[\"chapter\"])) {\n      var _questions$selectedIn, _questions$selectedIn2, _questions$selectedIn3, _questions$selectedIn4;\n      if ((_questions$selectedIn = questions[selectedIndex]) !== null && _questions$selectedIn !== void 0 && (_questions$selectedIn2 = _questions$selectedIn.questionData) !== null && _questions$selectedIn2 !== void 0 && _questions$selectedIn2.class && ((_questions$selectedIn3 = questions[selectedIndex]) === null || _questions$selectedIn3 === void 0 ? void 0 : (_questions$selectedIn4 = _questions$selectedIn3.questionData) === null || _questions$selectedIn4 === void 0 ? void 0 : _questions$selectedIn4.class.trim()) !== \"\") {\n        setOptionChapter(codes[\"chapter\"].filter(code => {\n          var _questions$selectedIn5, _questions$selectedIn6;\n          return code.code.startsWith((_questions$selectedIn5 = questions[selectedIndex]) === null || _questions$selectedIn5 === void 0 ? void 0 : (_questions$selectedIn6 = _questions$selectedIn5.questionData) === null || _questions$selectedIn6 === void 0 ? void 0 : _questions$selectedIn6.class) && code.code.length === 5;\n        }));\n      } else {\n        setOptionChapter(codes[\"chapter\"].filter(code => code.code.length === 5));\n      }\n    } else {\n      setOptionChapter([]);\n    }\n  }, [codes, questions, selectedIndex]);\n  const handleQuestionChange = (e, field) => {\n    const newQuestions = questions.map((question, qIndex) => {\n      if (qIndex === selectedIndex) {\n        return {\n          ...question,\n          questionData: {\n            ...question.questionData,\n            [field]: e.target.value\n          }\n        };\n      }\n      return question;\n    });\n    dispatch(setQuestions(newQuestions));\n  };\n  const handleSolutionQuestionChange = newSolution => {\n    const newQuestions = questions.map((question, qIndex) => {\n      if (qIndex === selectedIndex) {\n        return {\n          ...question,\n          questionData: {\n            ...question.questionData,\n            solution: newSolution\n          }\n        };\n      }\n      return question;\n    });\n    dispatch(setQuestions(newQuestions));\n  };\n  const handleStatementChange = (index, value, field) => {\n    const newQuestions = questions.map((question, qIndex) => {\n      if (qIndex === selectedIndex) {\n        return {\n          ...question,\n          statements: question.statements.map((stmt, sIndex) => {\n            if (sIndex === index) {\n              return {\n                ...stmt,\n                [field]: value\n              };\n            }\n            return stmt;\n          })\n        };\n      }\n      return question;\n    });\n    dispatch(setQuestions(newQuestions));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3 p-3 w-full\",\n    children: [/*#__PURE__*/_jsxDEV(ListQuestions, {\n      count: questionCount.TN,\n      title: 'Trắc nghiệm',\n      onClick: index => dispatch(setSelectedIndex(index)),\n      i: selectedIndex\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ListQuestions, {\n      count: questionCount.DS,\n      title: 'Đúng sai',\n      onClick: index => dispatch(setSelectedIndex(index + questionCount.TN)),\n      i: selectedIndex - questionCount.TN\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ListQuestions, {\n      count: questionCount.TLN,\n      title: 'Trả lời ngắn',\n      onClick: index => dispatch(setSelectedIndex(index + questionCount.DS + questionCount.TN)),\n      i: selectedIndex - (questionCount.DS + questionCount.TN)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 13\n    }, this), questions && questions[selectedIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Ph\\xE2n lo\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: questions[selectedIndex].questionData.class,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'class'),\n            options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n            selectedOption: questions[selectedIndex].questionData.chapter,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'chapter'),\n            options: optionChapter,\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n            selectedOption: questions[selectedIndex].questionData.difficulty,\n            onChange: option => handleQuestionChange({\n              target: {\n                value: option\n              }\n            }, 'difficulty'),\n            options: Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : [],\n            className: \"text-xs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \" bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"Th\\xF4ng tin c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(TextArea, {\n            value: questions[selectedIndex].questionData.content,\n            onChange: e => handleQuestionChange(e, 'content'),\n            placeholder: \"Nh\\u1EADp n\\u1ED9i dung c\\xE2u h\\u1ECFi\",\n            label: \"C\\xE2u h\\u1ECFi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(ImageDropZone, {\n            imageUrl: questions[selectedIndex].questionData.imageUrl,\n            onImageDrop: image => handleQuestionChange({\n              target: {\n                value: image\n              }\n            }, 'imageUrl'),\n            onImageRemove: () => handleQuestionChange({\n              target: {\n                value: ''\n              }\n            }, 'imageUrl')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 29\n          }, this), questions[selectedIndex].questionData.typeOfQuestion !== 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: questions[selectedIndex].statements.map((statement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 items-center w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row gap-2 items-center w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs font-bold whitespace-nowrap\",\n                  children: questions[selectedIndex].questionData.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n                  value: statement.content,\n                  onChange: e => handleStatementChange(index, e.target.value, 'content'),\n                  placeholder: \"Nh\\u1EADp n\\u1ED9i dung m\\u1EC7nh \\u0111\\u1EC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(ImageDropZone, {\n                imageUrl: statement.imageUrl,\n                onImageDrop: image => handleStatementChange(index, image, 'imageUrl'),\n                onImageRemove: () => handleStatementChange(index, '', 'imageUrl')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 45\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 41\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 33\n          }, this), questions[selectedIndex].questionData.typeOfQuestion === 'TLN' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              value: questions[selectedIndex].questionData.correctAnswer,\n              onChange: e => handleQuestionChange(e, 'correctAnswer'),\n              placeholder: \"Nh\\u1EADp \\u0111\\xE1p \\xE1n\",\n              label: \"\\u0110\\xE1p \\xE1n\",\n              Icon: CheckCircle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(SolutionEditor, {\n            solution: questions[selectedIndex].questionData.solution,\n            onSolutionChange: handleSolutionQuestionChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 639,\n    columnNumber: 9\n  }, this);\n};\n_s8(Step3Form, \"MpXuyZvR/T3VAxqhcJOkbJf1Zuo=\", false, function () {\n  return [useSelector, useDispatch, useSelector];\n});\n_c11 = Step3Form;\nconst LeftContent = () => {\n  _s9();\n  const dispatch = useDispatch();\n  const {\n    step,\n    examData,\n    loading\n  } = useSelector(state => state.addExam);\n  const handleNext = () => {\n    if (step < 3) dispatch(nextStep());\n  };\n  const handlePrev = () => {\n    if (step > 1) dispatch(prevStep());\n  };\n  const handleSubmit = async () => {\n    try {\n      await dispatch(postExam({\n        examData,\n        examImage: null,\n        questions: [],\n        questionImages: [],\n        statementImages: [],\n        solutionImages: [],\n        examFile: null\n      })).unwrap();\n      // Handle success\n    } catch (error) {\n      console.error('Error creating exam:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_42px)]\",\n    children: [/*#__PURE__*/_jsxDEV(CompactStepHeader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 771,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: [step === 1 && /*#__PURE__*/_jsxDEV(Step1Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 21\n      }, this), step === 2 && /*#__PURE__*/_jsxDEV(Step2Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 782,\n        columnNumber: 21\n      }, this), step === 3 && /*#__PURE__*/_jsxDEV(Step3Form, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 787,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 774,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-t border-gray-200 p-2 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePrev,\n          disabled: step === 1,\n          className: \"flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: [/*#__PURE__*/_jsxDEV(ChevronLeft, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 25\n          }, this), \"Quay l\\u1EA1i\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 21\n        }, this), step < 3 ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNext,\n          className: \"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs\",\n          children: [\"Ti\\u1EBFp theo\", /*#__PURE__*/_jsxDEV(ChevronRight, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSubmit,\n          disabled: loading || !examData.name || !examData.typeOfExam || !examData.class || !examData.year,\n          className: \"flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs\",\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 37\n            }, this), \"\\u0110ang t\\u1EA1o...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 37\n            }, this), \"T\\u1EA1o \\u0111\\u1EC1 thi\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 793,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 792,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 769,\n    columnNumber: 9\n  }, this);\n};\n_s9(LeftContent, \"o3GHNNcKn4ehgxm52lXl3JlWlCk=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c12 = LeftContent;\nexport default LeftContent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12;\n$RefreshReg$(_c, \"Step1Form\");\n$RefreshReg$(_c2, \"ButtonAddQuestion\");\n$RefreshReg$(_c3, \"TextArea\");\n$RefreshReg$(_c4, \"AddQuestionForm\");\n$RefreshReg$(_c5, \"AddTNQuestion\");\n$RefreshReg$(_c6, \"AddDSQuestion\");\n$RefreshReg$(_c7, \"AddTLNQuestion\");\n$RefreshReg$(_c8, \"Step2Form\");\n$RefreshReg$(_c9, \"ListQuestions\");\n$RefreshReg$(_c10, \"ImageDropZone\");\n$RefreshReg$(_c11, \"Step3Form\");\n$RefreshReg$(_c12, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useState", "useRef", "useDispatch", "useSelector", "fetchCodesByType", "setExamData", "postExam", "nextStep", "prevStep", "setExamImage", "setExamFile", "setQuestionTNContent", "setQuestionDSContent", "setQuestionTLNContent", "setCorrectAnswerTN", "setCorrectAnswerDS", "setCorrectAnswerTLN", "setQuestions", "setSelectedIndex", "DropMenuBarAdmin", "SuggestInputBarAdmin", "CompactStepHeader", "Clock", "Users", "BookOpen", "Image", "ImageIcon", "Upload", "FileText", "CheckCircle", "ChevronRight", "ChevronLeft", "Plus", "Save", "Trash2", "ImageUpload", "UploadPdf", "LoadingSpinner", "NavigateBar", "SolutionEditor", "normalizeText", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Step1Form", "_s", "dispatch", "examData", "examImage", "examFile", "state", "addExam", "codes", "updateExamData", "field", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "name", "onChange", "e", "target", "placeholder", "selectedOption", "typeOfExam", "option", "options", "Array", "isArray", "class", "year", "testDuration", "passRate", "chapter", "solutionUrl", "description", "rows", "checked", "public", "isClassroomExam", "image", "setImage", "img", "inputId", "compact", "setPdf", "pdf", "deleteButton", "_c", "ButtonAddQuestion", "_ref", "text", "onClick", "_c2", "TextArea", "_ref2", "_s2", "label", "Icon", "buttonFilterText", "textAreaRef", "current", "style", "height", "scrollHeight", "ref", "_c3", "AddQuestionForm", "_ref3", "questionContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleContentChange", "handleCorrectAnswerChange", "_c4", "AddTNQuestion", "_s3", "questionT<PERSON>ontent", "correctAnswerTN", "_c5", "AddDSQuestion", "_s4", "question<PERSON><PERSON><PERSON><PERSON>", "correctAnswerDS", "_c6", "AddTLNQuestion", "_s5", "questionTLNContent", "correctAnswerTLN", "_c7", "Step2Form", "_s6", "isViewAdd", "setIsViewAdd", "view", "<PERSON><PERSON><PERSON><PERSON>", "trim", "list", "id", "active", "setActive", "_c8", "ListQuestions", "_ref4", "count", "title", "i", "from", "length", "map", "_", "index", "concat", "_c9", "ImageDropZone", "_ref5", "_s7", "imageUrl", "onImageDrop", "onImageRemove", "isDraggingOver", "setIsDraggingOver", "handleDrop", "preventDefault", "stopPropagation", "draggedImage", "dataTransfer", "getData", "handleDragOver", "types", "includes", "handleDragEnter", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "onDragOver", "onDragEnter", "onDragLeave", "onDrop", "src", "alt", "_c10", "Step3Form", "_s8", "questions", "selectedIndex", "questionCount", "setQuestionCount", "TN", "DS", "TLN", "prefixTN", "prefixDS", "optionChapter", "setOptionChapter", "counts", "reduce", "acc", "q", "questionData", "typeOfQuestion", "undefined", "_questions$selectedIn", "_questions$selectedIn2", "_questions$selectedIn3", "_questions$selectedIn4", "filter", "code", "_questions$selectedIn5", "_questions$selectedIn6", "startsWith", "handleQuestionChange", "newQuestions", "question", "qIndex", "handleSolutionQuestionChange", "newSolution", "solution", "handleStatementChange", "statements", "stmt", "sIndex", "difficulty", "content", "statement", "<PERSON><PERSON><PERSON><PERSON>", "onSolutionChange", "_c11", "LeftContent", "_s9", "step", "loading", "handleNext", "handlePrev", "handleSubmit", "questionImages", "statementImages", "solutionImages", "unwrap", "error", "console", "disabled", "size", "_c12", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["// Optimized Form Panel Component\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport {\r\n    setExamData,\r\n    postExam,\r\n    nextStep,\r\n    prevStep,\r\n    setExamImage,\r\n    setExamFile,\r\n    setQuestionTNContent,\r\n    setQuestionDSContent,\r\n    setQuestionTLNContent,\r\n    setCorrectAnswerTN,\r\n    setCorrectAnswerDS,\r\n    setCorrectAnswerTLN,\r\n    setQuestions,\r\n    setSelectedIndex,\r\n} from \"src/features/addExam/addExamSlice\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport CompactStepHeader from \"./CompactStepHeader\";\r\nimport { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, CheckCircle, ChevronRight, ChevronLeft, Plus, Save, Trash2 } from \"lucide-react\";\r\nimport ImageUpload from \"src/components/image/UploadImage\";\r\nimport UploadPdf from \"src/components/UploadPdf\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport NavigateBar from \"./NavigateBar\";\r\nimport SolutionEditor from \"./SolutionEditor\";\r\nimport { normalizeText } from \"src/utils/question/questionUtils\";\r\n\r\nconst Step1Form = () => {\r\n    const dispatch = useDispatch();\r\n    const { examData, examImage, examFile } = useSelector((state) => state.addExam);\r\n    const { codes } = useSelector(state => state.codes);\r\n\r\n    const updateExamData = (field, value) => {\r\n        dispatch(setExamData({ field, value }));\r\n    };\r\n\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3\">\r\n            {/* Compact Name & Type Row */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Tên đề thi <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input\r\n                        type=\"text\"\r\n                        value={examData.name || ''}\r\n                        onChange={(e) => updateExamData('name', e.target.value)}\r\n                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                        placeholder=\"Nhập tên đề thi\"\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Kiểu đề <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={examData.typeOfExam}\r\n                        onChange={(option) => updateExamData('typeOfExam', option)}\r\n                        options={Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Compact Class & Year Row */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Lớp <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={examData.class}\r\n                        onChange={(option) => updateExamData('class', option)}\r\n                        options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        Năm <span className=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <DropMenuBarAdmin\r\n                        selectedOption={examData.year}\r\n                        onChange={(option) => updateExamData('year', option)}\r\n                        options={Array.isArray(codes[\"year\"]) ? codes[\"year\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Compact Duration & Pass Rate Row */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <Clock className=\"w-3 h-3 inline mr-1\" />\r\n                        Thời gian (phút)\r\n                    </label>\r\n                    <input\r\n                        type=\"number\"\r\n                        value={examData.testDuration || ''}\r\n                        onChange={(e) => updateExamData('testDuration', e.target.value)}\r\n                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                        placeholder=\"90\"\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <Users className=\"w-3 h-3 inline mr-1\" />\r\n                        Điểm đạt (%)\r\n                    </label>\r\n                    <input\r\n                        type=\"number\"\r\n                        value={examData.passRate || ''}\r\n                        onChange={(e) => updateExamData('passRate', e.target.value)}\r\n                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                        placeholder=\"50\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Chapter (conditional) */}\r\n            {examData.typeOfExam === \"OT\" && (\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <BookOpen className=\"w-3 h-3 inline mr-1\" />\r\n                        Chương\r\n                    </label>\r\n                    <SuggestInputBarAdmin\r\n                        selectedOption={examData.chapter}\r\n                        onChange={(option) => updateExamData('chapter', option)}\r\n                        options={Array.isArray(codes[\"chapter\"]) ? codes[\"chapter\"] : []}\r\n                        className=\"text-xs\"\r\n                    />\r\n                </div>\r\n            )}\r\n            <div>\r\n                <label className=\"block text-xs font-medium text-gray-700 mb-1\">Link lời giải</label>\r\n                <textarea\r\n                    value={examData.solutionUrl}\r\n                    onChange={(e) => updateExamData('solutionUrl', e.target.value)}\r\n                    className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                    placeholder=\"Nhập link lời giải vd: youtube, ...\"\r\n                />\r\n            </div>\r\n            {/* Compact Description */}\r\n            <div>\r\n                <label className=\"block text-xs font-medium text-gray-700 mb-1\">Mô tả</label>\r\n                <textarea\r\n                    value={examData.description || ''}\r\n                    onChange={(e) => updateExamData('description', e.target.value)}\r\n                    rows={2}\r\n                    className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                    placeholder=\"Mô tả ngắn về đề thi...\"\r\n                />\r\n            </div>\r\n\r\n            {/* Compact Checkboxes */}\r\n            <div className=\"flex items-center gap-3\">\r\n                <label className=\"flex items-center text-xs\">\r\n                    <input\r\n                        type=\"checkbox\"\r\n                        checked={examData.public || false}\r\n                        onChange={(e) => updateExamData('public', e.target.checked)}\r\n                        className=\"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\r\n                    />\r\n                    <span className=\"ml-1 text-gray-700\">Công khai</span>\r\n                </label>\r\n                <label className=\"flex items-center text-xs\">\r\n                    <input\r\n                        type=\"checkbox\"\r\n                        checked={examData.isClassroomExam || false}\r\n                        onChange={(e) => updateExamData('isClassroomExam', e.target.checked)}\r\n                        className=\"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\r\n                    />\r\n                    <span className=\"ml-1 text-gray-700\">Đề thi lớp</span>\r\n                </label>\r\n            </div>\r\n\r\n            {/* Compact File Uploads */}\r\n            <div className=\"flex flex-col gap-2\">\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <ImageIcon className=\"w-3 h-3 inline mr-1\" />\r\n                        Ảnh đề thi\r\n                    </label>\r\n                    <ImageUpload\r\n                        image={examImage}\r\n                        setImage={(img) => dispatch(setExamImage(img))}\r\n                        inputId=\"exam-image-compact\"\r\n                        compact={true}\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                        <Upload className=\"w-3 h-3 inline mr-1\" />\r\n                        File PDF\r\n                    </label>\r\n                    <UploadPdf\r\n                        setPdf={(pdf) => dispatch(setExamFile(pdf))}\r\n                        deleteButton={false}\r\n                        compact={true}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nconst ButtonAddQuestion = ({ text, onClick }) => {\r\n    return (\r\n        <button\r\n            onClick={onClick}\r\n            className=\"w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs\">\r\n            <Plus className=\"w-3 h-3 inline mr-1\" />\r\n            {text}\r\n        </button>\r\n    )\r\n}\r\n\r\nconst TextArea = ({ value, onChange, placeholder, label, Icon = null, buttonFilterText = false }) => {\r\n    const textAreaRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        if (textAreaRef.current) {\r\n            textAreaRef.current.style.height = \"auto\"; // Reset để tính lại\r\n            textAreaRef.current.style.height = textAreaRef.current.scrollHeight + \"px\";\r\n        }\r\n    }, [value]);\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-2\">\r\n            <div className=\"flex flex-row justify-between items-center\">\r\n                <label className=\"block text-xs font-medium text-gray-700\">\r\n                    {Icon && <Icon className=\"w-3 h-3 inline mr-1\" />}\r\n                    {label}\r\n                </label>\r\n                {buttonFilterText && (\r\n                    <button\r\n                        onClick={buttonFilterText.onClick}\r\n                        className=\"text-xs font-medium text-white hover:underline px-2 py-1 bg-blue-500 rounded\"\r\n                    >\r\n                        {buttonFilterText.text}\r\n                    </button>\r\n                )}\r\n            </div>\r\n            <textarea\r\n                ref={textAreaRef}\r\n                value={value}\r\n                onChange={onChange}\r\n                className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none overflow-hidden\"\r\n                placeholder={placeholder}\r\n                rows={1} // để bắt đầu nhỏ nhất có thể\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nconst AddQuestionForm = ({ questionContent, correctAnswerContent, handleContentChange, handleCorrectAnswerChange }) => {\r\n    return (\r\n        <div className=\"p-3 flex flex-col gap-4\">\r\n            <TextArea\r\n                value={correctAnswerContent}\r\n                onChange={handleCorrectAnswerChange}\r\n                placeholder=\"Nhập đáp án\"\r\n                label=\"Đáp án\"\r\n                Icon={CheckCircle}\r\n            />\r\n            <TextArea\r\n                value={questionContent}\r\n                onChange={handleContentChange}\r\n                placeholder=\"Nhập nội dung câu hỏi\"\r\n                label=\"Câu hỏi\"\r\n                Icon={Plus}\r\n                buttonFilterText={{\r\n                    text: \"Lọc\",\r\n                    onClick: () => {\r\n                        handleContentChange(normalizeText(questionContent));\r\n                    }\r\n                }}\r\n            />\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst AddTNQuestion = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionTNContent, correctAnswerTN } = useSelector((state) => state.addExam);\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setQuestionTNContent(e.target.value));\r\n    };\r\n\r\n    const handleCorrectAnswerChange = (e) => {\r\n        dispatch(setCorrectAnswerTN(e.target.value));\r\n    };\r\n\r\n    return (\r\n        <AddQuestionForm\r\n            questionContent={questionTNContent}\r\n            correctAnswerContent={correctAnswerTN}\r\n            handleContentChange={handleContentChange}\r\n            handleCorrectAnswerChange={handleCorrectAnswerChange}\r\n        />\r\n    )\r\n}\r\n\r\nconst AddDSQuestion = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionDSContent, correctAnswerDS } = useSelector((state) => state.addExam);\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setQuestionDSContent(e.target.value));\r\n    };\r\n\r\n    const handleCorrectAnswerChange = (e) => {\r\n        dispatch(setCorrectAnswerDS(e.target.value));\r\n    };\r\n\r\n    return (\r\n        <AddQuestionForm\r\n            questionContent={questionDSContent}\r\n            correctAnswerContent={correctAnswerDS}\r\n            handleContentChange={handleContentChange}\r\n            handleCorrectAnswerChange={handleCorrectAnswerChange}\r\n        />\r\n    )\r\n}\r\n\r\nconst AddTLNQuestion = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionTLNContent, correctAnswerTLN } = useSelector((state) => state.addExam);\r\n\r\n    const handleContentChange = (e) => {\r\n        dispatch(setQuestionTLNContent(e.target.value));\r\n    };\r\n\r\n    const handleCorrectAnswerChange = (e) => {\r\n        dispatch(setCorrectAnswerTLN(e.target.value));\r\n    };\r\n\r\n    return (\r\n        <AddQuestionForm\r\n            questionContent={questionTLNContent}\r\n            correctAnswerContent={correctAnswerTLN}\r\n            handleContentChange={handleContentChange}\r\n            handleCorrectAnswerChange={handleCorrectAnswerChange}\r\n        />\r\n    )\r\n}\r\n\r\nconst Step2Form = () => {\r\n    const [isViewAdd, setIsViewAdd] = useState(true);\r\n    const [view, setView] = useState('TN');\r\n    const { questionTNContent, questionDSContent, questionTLNContent, correctAnswerTN, correctAnswerDS, correctAnswerTLN } = useSelector((state) => state.addExam);\r\n\r\n    useEffect(() => {\r\n        if (!isViewAdd) return\r\n        if (questionTNContent.trim() !== \"\" || correctAnswerTN.trim() !== \"\") {\r\n            setIsViewAdd(false);\r\n            setView('TN')\r\n        } else if (questionDSContent.trim() !== \"\" || correctAnswerDS.trim() !== \"\") {\r\n            setIsViewAdd(false);\r\n            setView('DS')\r\n        } else if (questionTLNContent.trim() !== \"\" || correctAnswerTLN.trim() !== \"\") {\r\n            setIsViewAdd(false);\r\n            setView('TLN')\r\n        }\r\n    }, [questionTNContent, correctAnswerTN, questionDSContent, correctAnswerDS, questionTLNContent, correctAnswerTLN]);\r\n\r\n    return (\r\n        <div className=\"space-y-3\">\r\n            {isViewAdd ? (\r\n                <div className=\"text-center py-4 px-3\">\r\n                    <FileText className=\"w-8 h-8 mx-auto text-gray-400 mb-2\" />\r\n                    <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thêm câu hỏi</h3>\r\n                    <p className=\"text-xs text-gray-600 mb-3\">\r\n                        Tạo câu hỏi cho đề thi của bạn\r\n                    </p>\r\n                    <div className=\"space-y-2\">\r\n                        <ButtonAddQuestion\r\n                            text=\"Thêm câu trắc nghiệm\"\r\n                            onClick={() => {\r\n                                setIsViewAdd(false);\r\n                                setView('TN')\r\n                            }}\r\n                        />\r\n                        <ButtonAddQuestion\r\n                            text=\"Thêm câu đúng sai\"\r\n                            onClick={() => {\r\n                                setIsViewAdd(false);\r\n                                setView('DS')\r\n                            }}\r\n                        />\r\n                        <ButtonAddQuestion\r\n                            text=\"Thêm câu trả lời ngắn\"\r\n                            onClick={() => {\r\n                                setIsViewAdd(false);\r\n                                setView('TLN')\r\n                            }}\r\n                        />\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <NavigateBar\r\n                    list={[{\r\n                        id: 1,\r\n                        name: 'Trắc nghiệm',\r\n                        value: 'TN'\r\n                    },\r\n                    {\r\n                        id: 2,\r\n                        name: 'Đúng sai',\r\n                        value: 'DS'\r\n                    },\r\n                    {\r\n                        id: 3,\r\n                        name: 'Trả lời ngắn',\r\n                        value: 'TLN'\r\n                    }\r\n                    ]}\r\n                    active={view}\r\n                    setActive={setView}\r\n                />\r\n            )}\r\n\r\n            {view === 'TN' && !isViewAdd && (\r\n                <AddTNQuestion />\r\n            )}\r\n            {view === 'DS' && !isViewAdd && (\r\n                <AddDSQuestion />\r\n            )}\r\n            {view === 'TLN' && !isViewAdd && (\r\n                <AddTLNQuestion />\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst ListQuestions = ({ count, title, onClick, i }) => {\r\n    return (\r\n        <div className=\"flex  flex-row w-full justify-start items-center border-b border-[#e3e4e5] pb-2\">\r\n            <div className=\"text-[#090a0a] text-xs font-bold font-bevietnam leading-loose whitespace-nowrap mr-4 flex-shrink-0 min-w-[6.2rem]\">\r\n                {title}:\r\n            </div>\r\n            <div className=\"flex flex-row gap-5 w-full overflow-x-auto min-h-max \">\r\n                {Array.from({ length: count }).map((_, index) => (\r\n                    <div\r\n                        key={index + title}\r\n                        onClick={() => onClick(index)}\r\n                        className={`cursor-pointer border text-xs border-[#e3e4e5] rounded flex justify-center whitespace-nowrap items-center gap-2.5 ${i === index ? 'bg-[#253f61] text-white' : 'bg-white text-[#253f61]'} px-2 py-1`}>\r\n                        Câu hỏi {index + 1}\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nconst ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove }) => {\r\n    const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n    const handleDrop = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        setIsDraggingOver(false);\r\n\r\n        const draggedImage = e.dataTransfer.getData(\"text/plain\");\r\n        if (draggedImage && onImageDrop) {\r\n            onImageDrop(draggedImage);\r\n        }\r\n    };\r\n\r\n    const handleDragOver = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (e.dataTransfer.types.includes('text/plain')) {\r\n            setIsDraggingOver(true);\r\n        }\r\n    };\r\n\r\n    const handleDragEnter = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (e.dataTransfer.types.includes('text/plain')) {\r\n            setIsDraggingOver(true);\r\n        }\r\n    };\r\n\r\n    const handleDragLeave = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        if (!e.currentTarget.contains(e.relatedTarget)) {\r\n            setIsDraggingOver(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div\r\n            className={`relative rounded-lg w-full p-4 transition-all duration-200 min-h-[60px] flex items-center justify-center\r\n                    ${isDraggingOver\r\n                    ? \"border-2 border-dashed border-blue-400 bg-blue-50\"\r\n                    : \"border border-gray-200 hover:border-blue-300 hover:bg-blue-25\"\r\n                }`}\r\n            onDragOver={handleDragOver}\r\n            onDragEnter={handleDragEnter}\r\n            onDragLeave={handleDragLeave}\r\n            onDrop={handleDrop}\r\n        >\r\n            {imageUrl ? (\r\n                <div className=\"relative group w-fit bg-gray-50 rounded-lg p-2\">\r\n                    <img\r\n                        src={imageUrl}\r\n                        alt=\"Attached image\"\r\n                        className=\"rounded-md max-h-48 max-w-full object-contain\"\r\n                    />\r\n                    <button\r\n                        className=\"absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\r\n                        onClick={onImageRemove}\r\n                    >\r\n                        <div className=\"bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg\">\r\n                            <Trash2 className=\"w-4 h-4\" />\r\n                        </div>\r\n                    </button>\r\n                    <div className=\"mt-2 text-xs text-gray-500 text-center\">\r\n                        Ảnh đã thêm • Click vào icon để xóa\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <div className=\"flex flex-col items-center justify-center gap-2\">\r\n                    <Plus className=\"w-5 h-5 text-gray-400\" />\r\n                    <p className=\"text-xs text-gray-500\">Thêm ảnh</p>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\nconst Step3Form = () => {\r\n    const { questions, selectedIndex } = useSelector((state) => state.addExam);\r\n    const dispatch = useDispatch();\r\n    const [questionCount, setQuestionCount] = useState({\r\n        TN: 0,\r\n        DS: 0,\r\n        TLN: 0\r\n    });\r\n    const prefixTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n    const { codes } = useSelector((state) => state.codes);\r\n    const [optionChapter, setOptionChapter] = useState([]);\r\n\r\n    useEffect(() => {\r\n        if (questions) {\r\n            const counts = questions.reduce(\r\n                (acc, q) => {\r\n                    const type = q.questionData.typeOfQuestion;\r\n                    if (acc[type] !== undefined) acc[type]++;\r\n                    return acc;\r\n                },\r\n                { TN: 0, DS: 0, TLN: 0 }\r\n            );\r\n\r\n            setQuestionCount(counts);\r\n        }\r\n    }, [questions]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (Array.isArray(codes[\"chapter\"])) {\r\n            if (questions[selectedIndex]?.questionData?.class && questions[selectedIndex]?.questionData?.class.trim() !== \"\") {\r\n                setOptionChapter(\r\n                    codes[\"chapter\"].filter((code) => code.code.startsWith(questions[selectedIndex]?.questionData?.class) && code.code.length === 5)\r\n                );\r\n            } else {\r\n                setOptionChapter(codes[\"chapter\"].filter((code) => code.code.length === 5));\r\n            }\r\n        } else {\r\n            setOptionChapter([]);\r\n        }\r\n    }, [codes, questions, selectedIndex]);\r\n\r\n    const handleQuestionChange = (e, field) => {\r\n        const newQuestions = questions.map((question, qIndex) => {\r\n            if (qIndex === selectedIndex) {\r\n                return {\r\n                    ...question,\r\n                    questionData: {\r\n                        ...question.questionData,\r\n                        [field]: e.target.value,\r\n                    }\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setQuestions(newQuestions));\r\n    };\r\n    const handleSolutionQuestionChange = (newSolution) => {\r\n        const newQuestions = questions.map((question, qIndex) => {\r\n            if (qIndex === selectedIndex) {\r\n                return {\r\n                    ...question,\r\n                    questionData: {\r\n                        ...question.questionData,\r\n                        solution: newSolution,\r\n                    }\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setQuestions(newQuestions));\r\n    };\r\n\r\n    const handleStatementChange = (index, value, field) => {\r\n        const newQuestions = questions.map((question, qIndex) => {\r\n            if (qIndex === selectedIndex) {\r\n                return {\r\n                    ...question,\r\n                    statements: question.statements.map((stmt, sIndex) => {\r\n                        if (sIndex === index) {\r\n                            return { ...stmt, [field]: value };\r\n                        }\r\n                        return stmt;\r\n                    })\r\n                };\r\n            }\r\n            return question;\r\n        });\r\n        dispatch(setQuestions(newQuestions));\r\n    };\r\n\r\n\r\n    return (\r\n        <div className=\"space-y-3 p-3 w-full\">\r\n            <ListQuestions count={questionCount.TN} title={'Trắc nghiệm'} onClick={(index) => dispatch(setSelectedIndex(index))} i={selectedIndex} />\r\n            <ListQuestions count={questionCount.DS} title={'Đúng sai'} onClick={(index) => dispatch(setSelectedIndex(index + questionCount.TN))} i={selectedIndex - questionCount.TN} />\r\n            <ListQuestions count={questionCount.TLN} title={'Trả lời ngắn'} onClick={(index) => dispatch(setSelectedIndex(index + questionCount.DS + questionCount.TN))} i={selectedIndex - (questionCount.DS + questionCount.TN)} />\r\n\r\n            {questions && questions[selectedIndex] && (\r\n                <div className=\"space-y-3 w-full\">\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Phân loại</h3>\r\n                        <div className=\"flex flex-row gap-2\">\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={questions[selectedIndex].questionData.class}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'class')}\r\n                                options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <SuggestInputBarAdmin\r\n                                selectedOption={questions[selectedIndex].questionData.chapter}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'chapter')}\r\n                                options={optionChapter}\r\n                                className=\"text-xs\"\r\n                            />\r\n                            <DropMenuBarAdmin\r\n                                selectedOption={questions[selectedIndex].questionData.difficulty}\r\n                                onChange={(option) => handleQuestionChange({ target: { value: option } }, 'difficulty')}\r\n                                options={Array.isArray(codes[\"difficulty\"]) ? codes[\"difficulty\"] : []}\r\n                                className=\"text-xs\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\" bg-gray-200\"></hr>\r\n                    <div className=\"flex flex-col gap-2\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thông tin câu hỏi</h3>\r\n                        <div className=\"space-y-2\">\r\n                            <TextArea\r\n                                value={questions[selectedIndex].questionData.content}\r\n                                onChange={(e) => handleQuestionChange(e, 'content')}\r\n                                placeholder=\"Nhập nội dung câu hỏi\"\r\n                                label=\"Câu hỏi\"\r\n                            />\r\n                            <ImageDropZone\r\n                                imageUrl={questions[selectedIndex].questionData.imageUrl}\r\n                                onImageDrop={(image) => handleQuestionChange({ target: { value: image } }, 'imageUrl')}\r\n                                onImageRemove={() => handleQuestionChange({ target: { value: '' } }, 'imageUrl')}\r\n                            />\r\n                            {questions[selectedIndex].questionData.typeOfQuestion !== 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    {questions[selectedIndex].statements.map((statement, index) => (\r\n                                        <div key={index} className=\"flex flex-col gap-2 items-center w-full\">\r\n                                            <div className=\"flex flex-row gap-2 items-center w-full\">\r\n                                                <p className=\"text-xs font-bold whitespace-nowrap\">\r\n                                                    {questions[selectedIndex].questionData.typeOfQuestion === 'TN' ? prefixTN[index] : prefixDS[index]}\r\n                                                </p>\r\n                                                <TextArea\r\n                                                    value={statement.content}\r\n                                                    onChange={(e) => handleStatementChange(index, e.target.value, 'content')}\r\n                                                    placeholder=\"Nhập nội dung mệnh đề\"\r\n                                                />\r\n                                            </div>\r\n                                            <ImageDropZone\r\n                                                imageUrl={statement.imageUrl}\r\n                                                onImageDrop={(image) => handleStatementChange(index, image, 'imageUrl')}\r\n                                                onImageRemove={() => handleStatementChange(index, '', 'imageUrl')}\r\n                                            />\r\n\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            )}\r\n                            {questions[selectedIndex].questionData.typeOfQuestion === 'TLN' && (\r\n                                <div className=\"space-y-2\">\r\n                                    <TextArea\r\n                                        value={questions[selectedIndex].questionData.correctAnswer}\r\n                                        onChange={(e) => handleQuestionChange(e, 'correctAnswer')}\r\n                                        placeholder=\"Nhập đáp án\"\r\n                                        label=\"Đáp án\"\r\n                                        Icon={CheckCircle}\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            {/* <TextArea\r\n                                value={questions[selectedIndex].questionData.solution}\r\n                                onChange={(e) => handleQuestionChange(e, 'solution')}\r\n                                placeholder=\"Nhập lời giải\"\r\n                                label=\"Lời giải\"\r\n                                Icon={CheckCircle}\r\n                            /> */}\r\n                            <SolutionEditor\r\n                                solution={questions[selectedIndex].questionData.solution}\r\n                                onSolutionChange={handleSolutionQuestionChange}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nconst LeftContent = () => {\r\n    const dispatch = useDispatch();\r\n    const { step, examData, loading } = useSelector((state) => state.addExam);\r\n    const handleNext = () => {\r\n        if (step < 3) dispatch(nextStep());\r\n    };\r\n\r\n    const handlePrev = () => {\r\n        if (step > 1) dispatch(prevStep());\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        try {\r\n            await dispatch(postExam({\r\n                examData,\r\n                examImage: null,\r\n                questions: [],\r\n                questionImages: [],\r\n                statementImages: [],\r\n                solutionImages: [],\r\n                examFile: null\r\n            })).unwrap();\r\n            // Handle success\r\n        } catch (error) {\r\n            console.error('Error creating exam:', error);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_42px)]\">\r\n            {/* Compact Step Header */}\r\n            <CompactStepHeader />\r\n\r\n            {/* Scrollable Form Content */}\r\n            <div className=\"flex-1 overflow-y-auto\">\r\n                {/* Step 1: Basic Information */}\r\n                {step === 1 && (\r\n                    <Step1Form />\r\n                )}\r\n\r\n                {/* Step 2: Questions */}\r\n                {step === 2 && (\r\n                    <Step2Form />\r\n                )}\r\n\r\n                {/* Step 3: Confirmation */}\r\n                {step === 3 && (\r\n                    <Step3Form />\r\n                )}\r\n            </div>\r\n\r\n            {/* Compact Navigation Footer */}\r\n            <div className=\"border-t border-gray-200 p-2 bg-white\">\r\n                <div className=\"flex justify-between items-center\">\r\n                    <button\r\n                        onClick={handlePrev}\r\n                        disabled={step === 1}\r\n                        className=\"flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                        <ChevronLeft className=\"w-3 h-3\" />\r\n                        Quay lại\r\n                    </button>\r\n\r\n                    {step < 3 ? (\r\n                        <button\r\n                            onClick={handleNext}\r\n                            className=\"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs\"\r\n                        >\r\n                            Tiếp theo\r\n                            <ChevronRight className=\"w-3 h-3\" />\r\n                        </button>\r\n                    ) : (\r\n                        <button\r\n                            onClick={handleSubmit}\r\n                            disabled={loading || !examData.name || !examData.typeOfExam || !examData.class || !examData.year}\r\n                            className=\"flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs\"\r\n                        >\r\n                            {loading ? (\r\n                                <>\r\n                                    <LoadingSpinner size=\"sm\" />\r\n                                    Đang tạo...\r\n                                </>\r\n                            ) : (\r\n                                <>\r\n                                    <Save className=\"w-3 h-3\" />\r\n                                    Tạo đề thi\r\n                                </>\r\n                            )}\r\n                        </button>\r\n                    )}\r\n                </div>\r\n            </div>\r\n        </div >\r\n    );\r\n};\r\n\r\nexport default LeftContent;"], "mappings": ";;;;;;;;;;AAAA;AACA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SACIC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,YAAY,EACZC,WAAW,EACXC,oBAAoB,EACpBC,oBAAoB,EACpBC,qBAAqB,EACrBC,kBAAkB,EAClBC,kBAAkB,EAClBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,QACb,mCAAmC;AAC1C,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AACvJ,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,aAAa,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8C,QAAQ;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAG/C,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC/E,MAAM;IAAEC;EAAM,CAAC,GAAGlD,WAAW,CAACgD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAAC;EAEnD,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCT,QAAQ,CAAC1C,WAAW,CAAC;MAAEkD,KAAK;MAAEC;IAAM,CAAC,CAAC,CAAC;EAC3C,CAAC;EAGD,oBACId,OAAA;IAAKe,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE1BhB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnChB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,0BACjD,eAAAhB,OAAA;YAAMe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACRpB,OAAA;UACIqB,IAAI,EAAC,MAAM;UACXP,KAAK,EAAER,QAAQ,CAACgB,IAAI,IAAI,EAAG;UAC3BC,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,MAAM,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;UACxDC,SAAS,EAAC,kHAAkH;UAC5HW,WAAW,EAAC;QAAiB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,yBACpD,eAAAhB,OAAA;YAAMe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACRpB,OAAA,CAACvB,gBAAgB;UACbkD,cAAc,EAAErB,QAAQ,CAACsB,UAAW;UACpCL,QAAQ,EAAGM,MAAM,IAAKjB,cAAc,CAAC,YAAY,EAAEiB,MAAM,CAAE;UAC3DC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,WAAW,CAAC,CAAC,GAAGA,KAAK,CAAC,WAAW,CAAC,GAAG,EAAG;UACrEI,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnChB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,WACxD,eAAAhB,OAAA;YAAMe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACRpB,OAAA,CAACvB,gBAAgB;UACbkD,cAAc,EAAErB,QAAQ,CAAC2B,KAAM;UAC/BV,QAAQ,EAAGM,MAAM,IAAKjB,cAAc,CAAC,OAAO,EAAEiB,MAAM,CAAE;UACtDC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;UAC7DI,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,WACxD,eAAAhB,OAAA;YAAMe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACRpB,OAAA,CAACvB,gBAAgB;UACbkD,cAAc,EAAErB,QAAQ,CAAC4B,IAAK;UAC9BX,QAAQ,EAAGM,MAAM,IAAKjB,cAAc,CAAC,MAAM,EAAEiB,MAAM,CAAE;UACrDC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,MAAM,CAAC,CAAC,GAAGA,KAAK,CAAC,MAAM,CAAC,GAAG,EAAG;UAC3DI,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnChB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DhB,OAAA,CAACpB,KAAK;YAACmC,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpB,OAAA;UACIqB,IAAI,EAAC,QAAQ;UACbP,KAAK,EAAER,QAAQ,CAAC6B,YAAY,IAAI,EAAG;UACnCZ,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,cAAc,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;UAChEC,SAAS,EAAC,kHAAkH;UAC5HW,WAAW,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DhB,OAAA,CAACnB,KAAK;YAACkC,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oCAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpB,OAAA;UACIqB,IAAI,EAAC,QAAQ;UACbP,KAAK,EAAER,QAAQ,CAAC8B,QAAQ,IAAI,EAAG;UAC/Bb,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,UAAU,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;UAC5DC,SAAS,EAAC,kHAAkH;UAC5HW,WAAW,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLd,QAAQ,CAACsB,UAAU,KAAK,IAAI,iBACzB5B,OAAA;MAAAgB,QAAA,gBACIhB,OAAA;QAAOe,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DhB,OAAA,CAAClB,QAAQ;UAACiC,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRpB,OAAA,CAACtB,oBAAoB;QACjBiD,cAAc,EAAErB,QAAQ,CAAC+B,OAAQ;QACjCd,QAAQ,EAAGM,MAAM,IAAKjB,cAAc,CAAC,SAAS,EAAEiB,MAAM,CAAE;QACxDC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,SAAS,CAAC,CAAC,GAAGA,KAAK,CAAC,SAAS,CAAC,GAAG,EAAG;QACjEI,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eACDpB,OAAA;MAAAgB,QAAA,gBACIhB,OAAA;QAAOe,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrFpB,OAAA;QACIc,KAAK,EAAER,QAAQ,CAACgC,WAAY;QAC5Bf,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,aAAa,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;QAC/DC,SAAS,EAAC,kHAAkH;QAC5HW,WAAW,EAAC;MAAqC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENpB,OAAA;MAAAgB,QAAA,gBACIhB,OAAA;QAAOe,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC7EpB,OAAA;QACIc,KAAK,EAAER,QAAQ,CAACiC,WAAW,IAAI,EAAG;QAClChB,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,aAAa,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;QAC/D0B,IAAI,EAAE,CAAE;QACRzB,SAAS,EAAC,kHAAkH;QAC5HW,WAAW,EAAC;MAAyB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACpChB,OAAA;QAAOe,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxChB,OAAA;UACIqB,IAAI,EAAC,UAAU;UACfoB,OAAO,EAAEnC,QAAQ,CAACoC,MAAM,IAAI,KAAM;UAClCnB,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,QAAQ,EAAEY,CAAC,CAACC,MAAM,CAACgB,OAAO,CAAE;UAC5D1B,SAAS,EAAC;QAAiE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACFpB,OAAA;UAAMe,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACRpB,OAAA;QAAOe,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxChB,OAAA;UACIqB,IAAI,EAAC,UAAU;UACfoB,OAAO,EAAEnC,QAAQ,CAACqC,eAAe,IAAI,KAAM;UAC3CpB,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC,iBAAiB,EAAEY,CAAC,CAACC,MAAM,CAACgB,OAAO,CAAE;UACrE1B,SAAS,EAAC;QAAiE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACFpB,OAAA;UAAMe,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChChB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DhB,OAAA,CAAChB,SAAS;YAAC+B,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpB,OAAA,CAACP,WAAW;UACRmD,KAAK,EAAErC,SAAU;UACjBsC,QAAQ,EAAGC,GAAG,IAAKzC,QAAQ,CAACtC,YAAY,CAAC+E,GAAG,CAAC,CAAE;UAC/CC,OAAO,EAAC,oBAAoB;UAC5BC,OAAO,EAAE;QAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpB,OAAA;QAAAgB,QAAA,gBACIhB,OAAA;UAAOe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DhB,OAAA,CAACf,MAAM;YAAC8B,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpB,OAAA,CAACN,SAAS;UACNuD,MAAM,EAAGC,GAAG,IAAK7C,QAAQ,CAACrC,WAAW,CAACkF,GAAG,CAAC,CAAE;UAC5CC,YAAY,EAAE,KAAM;UACpBH,OAAO,EAAE;QAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAhB,EAAA,CArLKD,SAAS;EAAA,QACM3C,WAAW,EACcC,WAAW,EACnCA,WAAW;AAAA;AAAA2F,EAAA,GAH3BjD,SAAS;AAuLf,MAAMkD,iBAAiB,GAAGC,IAAA,IAAuB;EAAA,IAAtB;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAAF,IAAA;EACxC,oBACItD,OAAA;IACIwD,OAAO,EAAEA,OAAQ;IACjBzC,SAAS,EAAC,wGAAwG;IAAAC,QAAA,gBAClHhB,OAAA,CAACV,IAAI;MAACyB,SAAS,EAAC;IAAqB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACvCmC,IAAI;EAAA;IAAAtC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEjB,CAAC;AAAAqC,GAAA,GATKJ,iBAAiB;AAWvB,MAAMK,QAAQ,GAAGC,KAAA,IAAoF;EAAAC,GAAA;EAAA,IAAnF;IAAE9C,KAAK;IAAES,QAAQ;IAAEG,WAAW;IAAEmC,KAAK;IAAEC,IAAI,GAAG,IAAI;IAAEC,gBAAgB,GAAG;EAAM,CAAC,GAAAJ,KAAA;EAC5F,MAAMK,WAAW,GAAGzG,MAAM,CAAC,IAAI,CAAC;EAEhCF,SAAS,CAAC,MAAM;IACZ,IAAI2G,WAAW,CAACC,OAAO,EAAE;MACrBD,WAAW,CAACC,OAAO,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM,CAAC,CAAC;MAC3CH,WAAW,CAACC,OAAO,CAACC,KAAK,CAACC,MAAM,GAAGH,WAAW,CAACC,OAAO,CAACG,YAAY,GAAG,IAAI;IAC9E;EACJ,CAAC,EAAE,CAACtD,KAAK,CAAC,CAAC;EAEX,oBACId,OAAA;IAAKe,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChChB,OAAA;MAAKe,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACvDhB,OAAA;QAAOe,SAAS,EAAC,yCAAyC;QAAAC,QAAA,GACrD8C,IAAI,iBAAI9D,OAAA,CAAC8D,IAAI;UAAC/C,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAChDyC,KAAK;MAAA;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACP2C,gBAAgB,iBACb/D,OAAA;QACIwD,OAAO,EAAEO,gBAAgB,CAACP,OAAQ;QAClCzC,SAAS,EAAC,8EAA8E;QAAAC,QAAA,EAEvF+C,gBAAgB,CAACR;MAAI;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACNpB,OAAA;MACIqE,GAAG,EAAEL,WAAY;MACjBlD,KAAK,EAAEA,KAAM;MACbS,QAAQ,EAAEA,QAAS;MACnBR,SAAS,EAAC,8IAA8I;MACxJW,WAAW,EAAEA,WAAY;MACzBc,IAAI,EAAE,CAAE,CAAC;IAAA;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACwC,GAAA,CApCIF,QAAQ;AAAAY,GAAA,GAARZ,QAAQ;AAsCd,MAAMa,eAAe,GAAGC,KAAA,IAA+F;EAAA,IAA9F;IAAEC,eAAe;IAAEC,oBAAoB;IAAEC,mBAAmB;IAAEC;EAA0B,CAAC,GAAAJ,KAAA;EAC9G,oBACIxE,OAAA;IAAKe,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACpChB,OAAA,CAAC0D,QAAQ;MACL5C,KAAK,EAAE4D,oBAAqB;MAC5BnD,QAAQ,EAAEqD,yBAA0B;MACpClD,WAAW,EAAC,6BAAa;MACzBmC,KAAK,EAAC,mBAAQ;MACdC,IAAI,EAAE3E;IAAY;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eACFpB,OAAA,CAAC0D,QAAQ;MACL5C,KAAK,EAAE2D,eAAgB;MACvBlD,QAAQ,EAAEoD,mBAAoB;MAC9BjD,WAAW,EAAC,yCAAuB;MACnCmC,KAAK,EAAC,iBAAS;MACfC,IAAI,EAAExE,IAAK;MACXyE,gBAAgB,EAAE;QACdR,IAAI,EAAE,KAAK;QACXC,OAAO,EAAEA,CAAA,KAAM;UACXmB,mBAAmB,CAAC7E,aAAa,CAAC2E,eAAe,CAAC,CAAC;QACvD;MACJ;IAAE;MAAAxD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAAyD,GAAA,GAzBKN,eAAe;AA4BrB,MAAMO,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAM1E,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwH,iBAAiB;IAAEC;EAAgB,CAAC,GAAGxH,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEpF,MAAMiE,mBAAmB,GAAInD,CAAC,IAAK;IAC/BnB,QAAQ,CAACpC,oBAAoB,CAACuD,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EAClD,CAAC;EAED,MAAM8D,yBAAyB,GAAIpD,CAAC,IAAK;IACrCnB,QAAQ,CAACjC,kBAAkB,CAACoD,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EAChD,CAAC;EAED,oBACId,OAAA,CAACuE,eAAe;IACZE,eAAe,EAAEO,iBAAkB;IACnCN,oBAAoB,EAAEO,eAAgB;IACtCN,mBAAmB,EAAEA,mBAAoB;IACzCC,yBAAyB,EAAEA;EAA0B;IAAA3D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CAAC;AAEV,CAAC;AAAA2D,GAAA,CApBKD,aAAa;EAAA,QACEtH,WAAW,EACmBC,WAAW;AAAA;AAAAyH,GAAA,GAFxDJ,aAAa;AAsBnB,MAAMK,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAM/E,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6H,iBAAiB;IAAEC;EAAgB,CAAC,GAAG7H,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEpF,MAAMiE,mBAAmB,GAAInD,CAAC,IAAK;IAC/BnB,QAAQ,CAACnC,oBAAoB,CAACsD,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EAClD,CAAC;EAED,MAAM8D,yBAAyB,GAAIpD,CAAC,IAAK;IACrCnB,QAAQ,CAAChC,kBAAkB,CAACmD,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EAChD,CAAC;EAED,oBACId,OAAA,CAACuE,eAAe;IACZE,eAAe,EAAEY,iBAAkB;IACnCX,oBAAoB,EAAEY,eAAgB;IACtCX,mBAAmB,EAAEA,mBAAoB;IACzCC,yBAAyB,EAAEA;EAA0B;IAAA3D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CAAC;AAEV,CAAC;AAAAgE,GAAA,CApBKD,aAAa;EAAA,QACE3H,WAAW,EACmBC,WAAW;AAAA;AAAA8H,GAAA,GAFxDJ,aAAa;AAsBnB,MAAMK,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAMpF,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkI,kBAAkB;IAAEC;EAAiB,CAAC,GAAGlI,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEtF,MAAMiE,mBAAmB,GAAInD,CAAC,IAAK;IAC/BnB,QAAQ,CAAClC,qBAAqB,CAACqD,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EACnD,CAAC;EAED,MAAM8D,yBAAyB,GAAIpD,CAAC,IAAK;IACrCnB,QAAQ,CAAC/B,mBAAmB,CAACkD,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC;EACjD,CAAC;EAED,oBACId,OAAA,CAACuE,eAAe;IACZE,eAAe,EAAEiB,kBAAmB;IACpChB,oBAAoB,EAAEiB,gBAAiB;IACvChB,mBAAmB,EAAEA,mBAAoB;IACzCC,yBAAyB,EAAEA;EAA0B;IAAA3D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CAAC;AAEV,CAAC;AAAAqE,GAAA,CApBKD,cAAc;EAAA,QACChI,WAAW,EACqBC,WAAW;AAAA;AAAAmI,GAAA,GAF1DJ,cAAc;AAsBpB,MAAMK,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1I,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2I,IAAI,EAAEC,OAAO,CAAC,GAAG5I,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM;IAAE0H,iBAAiB;IAAEK,iBAAiB;IAAEK,kBAAkB;IAAET,eAAe;IAAEK,eAAe;IAAEK;EAAiB,CAAC,GAAGlI,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE9JrD,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC0I,SAAS,EAAE;IAChB,IAAIf,iBAAiB,CAACmB,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIlB,eAAe,CAACkB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAClEH,YAAY,CAAC,KAAK,CAAC;MACnBE,OAAO,CAAC,IAAI,CAAC;IACjB,CAAC,MAAM,IAAIb,iBAAiB,CAACc,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIb,eAAe,CAACa,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACzEH,YAAY,CAAC,KAAK,CAAC;MACnBE,OAAO,CAAC,IAAI,CAAC;IACjB,CAAC,MAAM,IAAIR,kBAAkB,CAACS,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIR,gBAAgB,CAACQ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3EH,YAAY,CAAC,KAAK,CAAC;MACnBE,OAAO,CAAC,KAAK,CAAC;IAClB;EACJ,CAAC,EAAE,CAAClB,iBAAiB,EAAEC,eAAe,EAAEI,iBAAiB,EAAEC,eAAe,EAAEI,kBAAkB,EAAEC,gBAAgB,CAAC,CAAC;EAElH,oBACI3F,OAAA;IAAKe,SAAS,EAAC,WAAW;IAAAC,QAAA,GACrB+E,SAAS,gBACN/F,OAAA;MAAKe,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAClChB,OAAA,CAACd,QAAQ;QAAC6B,SAAS,EAAC;MAAoC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DpB,OAAA;QAAIe,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxEpB,OAAA;QAAGe,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJpB,OAAA;QAAKe,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtBhB,OAAA,CAACqD,iBAAiB;UACdE,IAAI,EAAC,sCAAsB;UAC3BC,OAAO,EAAEA,CAAA,KAAM;YACXwC,YAAY,CAAC,KAAK,CAAC;YACnBE,OAAO,CAAC,IAAI,CAAC;UACjB;QAAE;UAAAjF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACFpB,OAAA,CAACqD,iBAAiB;UACdE,IAAI,EAAC,iCAAmB;UACxBC,OAAO,EAAEA,CAAA,KAAM;YACXwC,YAAY,CAAC,KAAK,CAAC;YACnBE,OAAO,CAAC,IAAI,CAAC;UACjB;QAAE;UAAAjF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACFpB,OAAA,CAACqD,iBAAiB;UACdE,IAAI,EAAC,4CAAuB;UAC5BC,OAAO,EAAEA,CAAA,KAAM;YACXwC,YAAY,CAAC,KAAK,CAAC;YACnBE,OAAO,CAAC,KAAK,CAAC;UAClB;QAAE;UAAAjF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAENpB,OAAA,CAACJ,WAAW;MACRwG,IAAI,EAAE,CAAC;QACHC,EAAE,EAAE,CAAC;QACL/E,IAAI,EAAE,aAAa;QACnBR,KAAK,EAAE;MACX,CAAC,EACD;QACIuF,EAAE,EAAE,CAAC;QACL/E,IAAI,EAAE,UAAU;QAChBR,KAAK,EAAE;MACX,CAAC,EACD;QACIuF,EAAE,EAAE,CAAC;QACL/E,IAAI,EAAE,cAAc;QACpBR,KAAK,EAAE;MACX,CAAC,CACC;MACFwF,MAAM,EAAEL,IAAK;MACbM,SAAS,EAAEL;IAAQ;MAAAjF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACJ,EAEA6E,IAAI,KAAK,IAAI,IAAI,CAACF,SAAS,iBACxB/F,OAAA,CAAC8E,aAAa;MAAA7D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACnB,EACA6E,IAAI,KAAK,IAAI,IAAI,CAACF,SAAS,iBACxB/F,OAAA,CAACmF,aAAa;MAAAlE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACnB,EACA6E,IAAI,KAAK,KAAK,IAAI,CAACF,SAAS,iBACzB/F,OAAA,CAACwF,cAAc;MAAAvE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACpB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAA0E,GAAA,CAtFKD,SAAS;EAAA,QAG8GpI,WAAW;AAAA;AAAA+I,GAAA,GAHlIX,SAAS;AAwFf,MAAMY,aAAa,GAAGC,KAAA,IAAkC;EAAA,IAAjC;IAAEC,KAAK;IAAEC,KAAK;IAAEpD,OAAO;IAAEqD;EAAE,CAAC,GAAAH,KAAA;EAC/C,oBACI1G,OAAA;IAAKe,SAAS,EAAC,iFAAiF;IAAAC,QAAA,gBAC5FhB,OAAA;MAAKe,SAAS,EAAC,mHAAmH;MAAAC,QAAA,GAC7H4F,KAAK,EAAC,GACX;IAAA;MAAA3F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNpB,OAAA;MAAKe,SAAS,EAAC,uDAAuD;MAAAC,QAAA,EACjEe,KAAK,CAAC+E,IAAI,CAAC;QAAEC,MAAM,EAAEJ;MAAM,CAAC,CAAC,CAACK,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACxClH,OAAA;QAEIwD,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAAC0D,KAAK,CAAE;QAC9BnG,SAAS,uHAAAoG,MAAA,CAAuHN,CAAC,KAAKK,KAAK,GAAG,yBAAyB,GAAG,yBAAyB,eAAa;QAAAlG,QAAA,GAAC,kBACzM,EAACkG,KAAK,GAAG,CAAC;MAAA,GAHbA,KAAK,GAAGN,KAAK;QAAA3F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIjB,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAgG,GAAA,GAlBKX,aAAa;AAoBnB,MAAMY,aAAa,GAAGC,KAAA,IAA8C;EAAAC,GAAA;EAAA,IAA7C;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAAJ,KAAA;EAC3D,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGtK,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMuK,UAAU,GAAIrG,CAAC,IAAK;IACtBA,CAAC,CAACsG,cAAc,CAAC,CAAC;IAClBtG,CAAC,CAACuG,eAAe,CAAC,CAAC;IACnBH,iBAAiB,CAAC,KAAK,CAAC;IAExB,MAAMI,YAAY,GAAGxG,CAAC,CAACyG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,IAAIF,YAAY,IAAIP,WAAW,EAAE;MAC7BA,WAAW,CAACO,YAAY,CAAC;IAC7B;EACJ,CAAC;EAED,MAAMG,cAAc,GAAI3G,CAAC,IAAK;IAC1BA,CAAC,CAACsG,cAAc,CAAC,CAAC;IAClBtG,CAAC,CAACuG,eAAe,CAAC,CAAC;IACnB,IAAIvG,CAAC,CAACyG,YAAY,CAACG,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC7CT,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMU,eAAe,GAAI9G,CAAC,IAAK;IAC3BA,CAAC,CAACsG,cAAc,CAAC,CAAC;IAClBtG,CAAC,CAACuG,eAAe,CAAC,CAAC;IACnB,IAAIvG,CAAC,CAACyG,YAAY,CAACG,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC7CT,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMW,eAAe,GAAI/G,CAAC,IAAK;IAC3BA,CAAC,CAACsG,cAAc,CAAC,CAAC;IAClBtG,CAAC,CAACuG,eAAe,CAAC,CAAC;IACnB,IAAI,CAACvG,CAAC,CAACgH,aAAa,CAACC,QAAQ,CAACjH,CAAC,CAACkH,aAAa,CAAC,EAAE;MAC5Cd,iBAAiB,CAAC,KAAK,CAAC;IAC5B;EACJ,CAAC;EAED,oBACI5H,OAAA;IACIe,SAAS,mIAAAoG,MAAA,CACCQ,cAAc,GACd,mDAAmD,GACnD,+DAA+D,CAClE;IACPgB,UAAU,EAAER,cAAe;IAC3BS,WAAW,EAAEN,eAAgB;IAC7BO,WAAW,EAAEN,eAAgB;IAC7BO,MAAM,EAAEjB,UAAW;IAAA7G,QAAA,EAElBwG,QAAQ,gBACLxH,OAAA;MAAKe,SAAS,EAAC,gDAAgD;MAAAC,QAAA,gBAC3DhB,OAAA;QACI+I,GAAG,EAAEvB,QAAS;QACdwB,GAAG,EAAC,gBAAgB;QACpBjI,SAAS,EAAC;MAA+C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACFpB,OAAA;QACIe,SAAS,EAAC,4FAA4F;QACtGyC,OAAO,EAAEkE,aAAc;QAAA1G,QAAA,eAEvBhB,OAAA;UAAKe,SAAS,EAAC,qEAAqE;UAAAC,QAAA,eAChFhB,OAAA,CAACR,MAAM;YAACuB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACTpB,OAAA;QAAKe,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAExD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAENpB,OAAA;MAAKe,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC5DhB,OAAA,CAACV,IAAI;QAACyB,SAAS,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CpB,OAAA;QAAGe,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAmG,GAAA,CA7EKF,aAAa;AAAA4B,IAAA,GAAb5B,aAAa;AA+EnB,MAAM6B,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAM;IAAEC,SAAS;IAAEC;EAAc,CAAC,GAAG5L,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAC1E,MAAML,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8L,aAAa,EAAEC,gBAAgB,CAAC,GAAGjM,QAAQ,CAAC;IAC/CkM,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,GAAG,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7E,MAAM;IAAEjJ;EAAM,CAAC,GAAGlD,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EACrD,MAAM,CAACkJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGxM,QAAQ,CAAC,EAAE,CAAC;EAEtDD,SAAS,CAAC,MAAM;IACZ,IAAI+L,SAAS,EAAE;MACX,MAAMW,MAAM,GAAGX,SAAS,CAACY,MAAM,CAC3B,CAACC,GAAG,EAAEC,CAAC,KAAK;QACR,MAAM7I,IAAI,GAAG6I,CAAC,CAACC,YAAY,CAACC,cAAc;QAC1C,IAAIH,GAAG,CAAC5I,IAAI,CAAC,KAAKgJ,SAAS,EAAEJ,GAAG,CAAC5I,IAAI,CAAC,EAAE;QACxC,OAAO4I,GAAG;MACd,CAAC,EACD;QAAET,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAC3B,CAAC;MAEDH,gBAAgB,CAACQ,MAAM,CAAC;IAC5B;EACJ,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;EAGf/L,SAAS,CAAC,MAAM;IACZ,IAAI0E,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;MAAA,IAAA2J,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACjC,IAAI,CAAAH,qBAAA,GAAAlB,SAAS,CAACC,aAAa,CAAC,cAAAiB,qBAAA,gBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BH,YAAY,cAAAI,sBAAA,eAAtCA,sBAAA,CAAwCtI,KAAK,IAAI,EAAAuI,sBAAA,GAAApB,SAAS,CAACC,aAAa,CAAC,cAAAmB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BL,YAAY,cAAAM,sBAAA,uBAAtCA,sBAAA,CAAwCxI,KAAK,CAACkE,IAAI,CAAC,CAAC,MAAK,EAAE,EAAE;QAC9G2D,gBAAgB,CACZnJ,KAAK,CAAC,SAAS,CAAC,CAAC+J,MAAM,CAAEC,IAAI;UAAA,IAAAC,sBAAA,EAAAC,sBAAA;UAAA,OAAKF,IAAI,CAACA,IAAI,CAACG,UAAU,EAAAF,sBAAA,GAACxB,SAAS,CAACC,aAAa,CAAC,cAAAuB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BT,YAAY,cAAAU,sBAAA,uBAAtCA,sBAAA,CAAwC5I,KAAK,CAAC,IAAI0I,IAAI,CAACA,IAAI,CAAC5D,MAAM,KAAK,CAAC;QAAA,EACnI,CAAC;MACL,CAAC,MAAM;QACH+C,gBAAgB,CAACnJ,KAAK,CAAC,SAAS,CAAC,CAAC+J,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACA,IAAI,CAAC5D,MAAM,KAAK,CAAC,CAAC,CAAC;MAC/E;IACJ,CAAC,MAAM;MACH+C,gBAAgB,CAAC,EAAE,CAAC;IACxB;EACJ,CAAC,EAAE,CAACnJ,KAAK,EAAEyI,SAAS,EAAEC,aAAa,CAAC,CAAC;EAErC,MAAM0B,oBAAoB,GAAGA,CAACvJ,CAAC,EAAEX,KAAK,KAAK;IACvC,MAAMmK,YAAY,GAAG5B,SAAS,CAACpC,GAAG,CAAC,CAACiE,QAAQ,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,KAAK7B,aAAa,EAAE;QAC1B,OAAO;UACH,GAAG4B,QAAQ;UACXd,YAAY,EAAE;YACV,GAAGc,QAAQ,CAACd,YAAY;YACxB,CAACtJ,KAAK,GAAGW,CAAC,CAACC,MAAM,CAACX;UACtB;QACJ,CAAC;MACL;MACA,OAAOmK,QAAQ;IACnB,CAAC,CAAC;IACF5K,QAAQ,CAAC9B,YAAY,CAACyM,YAAY,CAAC,CAAC;EACxC,CAAC;EACD,MAAMG,4BAA4B,GAAIC,WAAW,IAAK;IAClD,MAAMJ,YAAY,GAAG5B,SAAS,CAACpC,GAAG,CAAC,CAACiE,QAAQ,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,KAAK7B,aAAa,EAAE;QAC1B,OAAO;UACH,GAAG4B,QAAQ;UACXd,YAAY,EAAE;YACV,GAAGc,QAAQ,CAACd,YAAY;YACxBkB,QAAQ,EAAED;UACd;QACJ,CAAC;MACL;MACA,OAAOH,QAAQ;IACnB,CAAC,CAAC;IACF5K,QAAQ,CAAC9B,YAAY,CAACyM,YAAY,CAAC,CAAC;EACxC,CAAC;EAED,MAAMM,qBAAqB,GAAGA,CAACpE,KAAK,EAAEpG,KAAK,EAAED,KAAK,KAAK;IACnD,MAAMmK,YAAY,GAAG5B,SAAS,CAACpC,GAAG,CAAC,CAACiE,QAAQ,EAAEC,MAAM,KAAK;MACrD,IAAIA,MAAM,KAAK7B,aAAa,EAAE;QAC1B,OAAO;UACH,GAAG4B,QAAQ;UACXM,UAAU,EAAEN,QAAQ,CAACM,UAAU,CAACvE,GAAG,CAAC,CAACwE,IAAI,EAAEC,MAAM,KAAK;YAClD,IAAIA,MAAM,KAAKvE,KAAK,EAAE;cAClB,OAAO;gBAAE,GAAGsE,IAAI;gBAAE,CAAC3K,KAAK,GAAGC;cAAM,CAAC;YACtC;YACA,OAAO0K,IAAI;UACf,CAAC;QACL,CAAC;MACL;MACA,OAAOP,QAAQ;IACnB,CAAC,CAAC;IACF5K,QAAQ,CAAC9B,YAAY,CAACyM,YAAY,CAAC,CAAC;EACxC,CAAC;EAGD,oBACIhL,OAAA;IAAKe,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACjChB,OAAA,CAACyG,aAAa;MAACE,KAAK,EAAE2C,aAAa,CAACE,EAAG;MAAC5C,KAAK,EAAE,aAAc;MAACpD,OAAO,EAAG0D,KAAK,IAAK7G,QAAQ,CAAC7B,gBAAgB,CAAC0I,KAAK,CAAC,CAAE;MAACL,CAAC,EAAEwC;IAAc;MAAApI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzIpB,OAAA,CAACyG,aAAa;MAACE,KAAK,EAAE2C,aAAa,CAACG,EAAG;MAAC7C,KAAK,EAAE,UAAW;MAACpD,OAAO,EAAG0D,KAAK,IAAK7G,QAAQ,CAAC7B,gBAAgB,CAAC0I,KAAK,GAAGoC,aAAa,CAACE,EAAE,CAAC,CAAE;MAAC3C,CAAC,EAAEwC,aAAa,GAAGC,aAAa,CAACE;IAAG;MAAAvI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5KpB,OAAA,CAACyG,aAAa;MAACE,KAAK,EAAE2C,aAAa,CAACI,GAAI;MAAC9C,KAAK,EAAE,cAAe;MAACpD,OAAO,EAAG0D,KAAK,IAAK7G,QAAQ,CAAC7B,gBAAgB,CAAC0I,KAAK,GAAGoC,aAAa,CAACG,EAAE,GAAGH,aAAa,CAACE,EAAE,CAAC,CAAE;MAAC3C,CAAC,EAAEwC,aAAa,IAAIC,aAAa,CAACG,EAAE,GAAGH,aAAa,CAACE,EAAE;IAAE;MAAAvI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAExNgI,SAAS,IAAIA,SAAS,CAACC,aAAa,CAAC,iBAClCrJ,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BhB,OAAA;QAAKe,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChChB,OAAA;UAAIe,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEpB,OAAA;UAAKe,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChChB,OAAA,CAACvB,gBAAgB;YACbkD,cAAc,EAAEyH,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAAClI,KAAM;YAC5DV,QAAQ,EAAGM,MAAM,IAAKkJ,oBAAoB,CAAC;cAAEtJ,MAAM,EAAE;gBAAEX,KAAK,EAAEe;cAAO;YAAE,CAAC,EAAE,OAAO,CAAE;YACnFC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;YAC7DI,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACFpB,OAAA,CAACtB,oBAAoB;YACjBiD,cAAc,EAAEyH,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAAC9H,OAAQ;YAC9Dd,QAAQ,EAAGM,MAAM,IAAKkJ,oBAAoB,CAAC;cAAEtJ,MAAM,EAAE;gBAAEX,KAAK,EAAEe;cAAO;YAAE,CAAC,EAAE,SAAS,CAAE;YACrFC,OAAO,EAAE+H,aAAc;YACvB9I,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACFpB,OAAA,CAACvB,gBAAgB;YACbkD,cAAc,EAAEyH,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACuB,UAAW;YACjEnK,QAAQ,EAAGM,MAAM,IAAKkJ,oBAAoB,CAAC;cAAEtJ,MAAM,EAAE;gBAAEX,KAAK,EAAEe;cAAO;YAAE,CAAC,EAAE,YAAY,CAAE;YACxFC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,YAAY,CAAC,CAAC,GAAGA,KAAK,CAAC,YAAY,CAAC,GAAG,EAAG;YACvEI,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNpB,OAAA;QAAIe,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClCpB,OAAA;QAAKe,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChChB,OAAA;UAAIe,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EpB,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBhB,OAAA,CAAC0D,QAAQ;YACL5C,KAAK,EAAEsI,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACwB,OAAQ;YACrDpK,QAAQ,EAAGC,CAAC,IAAKuJ,oBAAoB,CAACvJ,CAAC,EAAE,SAAS,CAAE;YACpDE,WAAW,EAAC,yCAAuB;YACnCmC,KAAK,EAAC;UAAS;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACFpB,OAAA,CAACqH,aAAa;YACVG,QAAQ,EAAE4B,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAAC3C,QAAS;YACzDC,WAAW,EAAG7E,KAAK,IAAKmI,oBAAoB,CAAC;cAAEtJ,MAAM,EAAE;gBAAEX,KAAK,EAAE8B;cAAM;YAAE,CAAC,EAAE,UAAU,CAAE;YACvF8E,aAAa,EAAEA,CAAA,KAAMqD,oBAAoB,CAAC;cAAEtJ,MAAM,EAAE;gBAAEX,KAAK,EAAE;cAAG;YAAE,CAAC,EAAE,UAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,EACDgI,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACC,cAAc,KAAK,KAAK,iBAC3DpK,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrBoI,SAAS,CAACC,aAAa,CAAC,CAACkC,UAAU,CAACvE,GAAG,CAAC,CAAC4E,SAAS,EAAE1E,KAAK,kBACtDlH,OAAA;cAAiBe,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBAChEhB,OAAA;gBAAKe,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACpDhB,OAAA;kBAAGe,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC7CoI,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACC,cAAc,KAAK,IAAI,GAAGT,QAAQ,CAACzC,KAAK,CAAC,GAAG0C,QAAQ,CAAC1C,KAAK;gBAAC;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACJpB,OAAA,CAAC0D,QAAQ;kBACL5C,KAAK,EAAE8K,SAAS,CAACD,OAAQ;kBACzBpK,QAAQ,EAAGC,CAAC,IAAK8J,qBAAqB,CAACpE,KAAK,EAAE1F,CAAC,CAACC,MAAM,CAACX,KAAK,EAAE,SAAS,CAAE;kBACzEY,WAAW,EAAC;gBAAuB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNpB,OAAA,CAACqH,aAAa;gBACVG,QAAQ,EAAEoE,SAAS,CAACpE,QAAS;gBAC7BC,WAAW,EAAG7E,KAAK,IAAK0I,qBAAqB,CAACpE,KAAK,EAAEtE,KAAK,EAAE,UAAU,CAAE;gBACxE8E,aAAa,EAAEA,CAAA,KAAM4D,qBAAqB,CAACpE,KAAK,EAAE,EAAE,EAAE,UAAU;cAAE;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA,GAfI8F,KAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EACAgI,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACC,cAAc,KAAK,KAAK,iBAC3DpK,OAAA;YAAKe,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtBhB,OAAA,CAAC0D,QAAQ;cACL5C,KAAK,EAAEsI,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAAC0B,aAAc;cAC3DtK,QAAQ,EAAGC,CAAC,IAAKuJ,oBAAoB,CAACvJ,CAAC,EAAE,eAAe,CAAE;cAC1DE,WAAW,EAAC,6BAAa;cACzBmC,KAAK,EAAC,mBAAQ;cACdC,IAAI,EAAE3E;YAAY;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAQDpB,OAAA,CAACH,cAAc;YACXwL,QAAQ,EAAEjC,SAAS,CAACC,aAAa,CAAC,CAACc,YAAY,CAACkB,QAAS;YACzDS,gBAAgB,EAAEX;UAA6B;YAAAlK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAA+H,GAAA,CAhMKD,SAAS;EAAA,QAC0BzL,WAAW,EAC/BD,WAAW,EAQVC,WAAW;AAAA;AAAAsO,IAAA,GAV3B7C,SAAS;AAmMf,MAAM8C,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM5L,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE0O,IAAI;IAAE5L,QAAQ;IAAE6L;EAAQ,CAAC,GAAG1O,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EACzE,MAAM0L,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIF,IAAI,GAAG,CAAC,EAAE7L,QAAQ,CAACxC,QAAQ,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAMwO,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIH,IAAI,GAAG,CAAC,EAAE7L,QAAQ,CAACvC,QAAQ,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAMwO,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMjM,QAAQ,CAACzC,QAAQ,CAAC;QACpB0C,QAAQ;QACRC,SAAS,EAAE,IAAI;QACf6I,SAAS,EAAE,EAAE;QACbmD,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE,EAAE;QACnBC,cAAc,EAAE,EAAE;QAClBjM,QAAQ,EAAE;MACd,CAAC,CAAC,CAAC,CAACkM,MAAM,CAAC,CAAC;MACZ;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAChD;EACJ,CAAC;EAED,oBACI3M,OAAA;IAAKe,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEjDhB,OAAA,CAACrB,iBAAiB;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrBpB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,GAElCkL,IAAI,KAAK,CAAC,iBACPlM,OAAA,CAACG,SAAS;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf,EAGA8K,IAAI,KAAK,CAAC,iBACPlM,OAAA,CAAC6F,SAAS;QAAA5E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf,EAGA8K,IAAI,KAAK,CAAC,iBACPlM,OAAA,CAACkJ,SAAS;QAAAjI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNpB,OAAA;MAAKe,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eAClDhB,OAAA;QAAKe,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9ChB,OAAA;UACIwD,OAAO,EAAE6I,UAAW;UACpBQ,QAAQ,EAAEX,IAAI,KAAK,CAAE;UACrBnL,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAEvIhB,OAAA,CAACX,WAAW;YAAC0B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER8K,IAAI,GAAG,CAAC,gBACLlM,OAAA;UACIwD,OAAO,EAAE4I,UAAW;UACpBrL,SAAS,EAAC,8GAA8G;UAAAC,QAAA,GAC3H,gBAEG,eAAAhB,OAAA,CAACZ,YAAY;YAAC2B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,gBAETpB,OAAA;UACIwD,OAAO,EAAE8I,YAAa;UACtBO,QAAQ,EAAEV,OAAO,IAAI,CAAC7L,QAAQ,CAACgB,IAAI,IAAI,CAAChB,QAAQ,CAACsB,UAAU,IAAI,CAACtB,QAAQ,CAAC2B,KAAK,IAAI,CAAC3B,QAAQ,CAAC4B,IAAK;UACjGnB,SAAS,EAAC,oIAAoI;UAAAC,QAAA,EAE7ImL,OAAO,gBACJnM,OAAA,CAAAE,SAAA;YAAAc,QAAA,gBACIhB,OAAA,CAACL,cAAc;cAACmN,IAAI,EAAC;YAAI;cAAA7L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEhC;UAAA,eAAE,CAAC,gBAEHpB,OAAA,CAAAE,SAAA;YAAAc,QAAA,gBACIhB,OAAA,CAACT,IAAI;cAACwB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAEhC;UAAA,eAAE;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEf,CAAC;AAAC6K,GAAA,CA9FID,WAAW;EAAA,QACIxO,WAAW,EACQC,WAAW;AAAA;AAAAsP,IAAA,GAF7Cf,WAAW;AAgGjB,eAAeA,WAAW;AAAC,IAAA5I,EAAA,EAAAK,GAAA,EAAAa,GAAA,EAAAO,GAAA,EAAAK,GAAA,EAAAK,GAAA,EAAAK,GAAA,EAAAY,GAAA,EAAAY,GAAA,EAAA6B,IAAA,EAAA8C,IAAA,EAAAgB,IAAA;AAAAC,YAAA,CAAA5J,EAAA;AAAA4J,YAAA,CAAAvJ,GAAA;AAAAuJ,YAAA,CAAA1I,GAAA;AAAA0I,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAAzH,GAAA;AAAAyH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAAjB,IAAA;AAAAiB,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}