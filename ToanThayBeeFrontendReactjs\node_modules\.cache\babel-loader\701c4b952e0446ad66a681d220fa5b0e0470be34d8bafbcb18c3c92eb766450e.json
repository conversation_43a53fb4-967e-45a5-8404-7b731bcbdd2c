{"ast": null, "code": "import api from \"./api\";\nexport const getAllUsersAPI = _ref => {\n  let {\n    search = \"\",\n    currentPage = 1,\n    limit = 10,\n    sortOrder = 'desc',\n    graduationYear = null,\n    classFilter = null\n  } = _ref;\n  const params = {\n    search,\n    page: currentPage,\n    limit,\n    sortOrder\n  };\n\n  // Chỉ thêm filter parameters nếu có giá trị\n  if (graduationYear !== null && graduationYear !== undefined) {\n    params.graduationYear = graduationYear;\n  }\n  if (classFilter !== null && classFilter !== undefined && classFilter !== '') {\n    params.class = classFilter;\n  }\n  return api.get(\"/v1/admin/user\", {\n    params\n  });\n};\nexport const getAllStaffAPI = _ref2 => {\n  let {\n    search = \"\",\n    currentPage = 1,\n    limit = 10,\n    sortOrder = 'desc'\n  } = _ref2;\n  return api.get(\"/v1/admin/staff\", {\n    params: {\n      search,\n      page: currentPage,\n      limit,\n      sortOrder\n    }\n  });\n};\nexport const getUserByIdAPI = id => {\n  return api.get(\"/v1/admin/user/\".concat(id));\n};\nexport const findUsersAPI = search => {\n  return api.get(\"/v1/admin/user/search\", {\n    params: {\n      search\n    }\n  });\n};\nexport const getUserClassesAPI = _ref3 => {\n  let {\n    id,\n    search = \"\",\n    currentPage = 1,\n    limit = 10,\n    sortOrder = 'desc'\n  } = _ref3;\n  return api.get(\"/v1/admin/user/class/\".concat(id), {\n    params: {\n      search,\n      page: currentPage,\n      limit,\n      sortOrder\n    }\n  });\n};\nexport const putUserAPI = _ref4 => {\n  let {\n    id,\n    user\n  } = _ref4;\n  return api.put(\"/v1/admin/user/\".concat(id), user);\n};\nexport const putUserTypeAPI = _ref5 => {\n  let {\n    id,\n    type\n  } = _ref5;\n  return api.put(\"/v1/admin/user/\".concat(id, \"/user-type\"), {\n    userType: type\n  });\n};\nexport const putUserStatusAPI = _ref6 => {\n  let {\n    id,\n    status\n  } = _ref6;\n  return api.put(\"/v1/admin/user/\".concat(id, \"/status\"), {\n    status\n  });\n};\nexport const deleteUserAPI = id => {\n  return api.delete(\"/v1/admin/user/\".concat(id));\n};", "map": {"version": 3, "names": ["api", "getAllUsersAPI", "_ref", "search", "currentPage", "limit", "sortOrder", "graduationYear", "classFilter", "params", "page", "undefined", "class", "get", "getAllStaffAPI", "_ref2", "getUserByIdAPI", "id", "concat", "findUsersAPI", "getUserClassesAPI", "_ref3", "putUserAPI", "_ref4", "user", "put", "putUserTypeAPI", "_ref5", "type", "userType", "putUserStatusAPI", "_ref6", "status", "deleteUserAPI", "delete"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/services/userApi.js"], "sourcesContent": ["import api from \"./api\";\r\n\r\nexport const getAllUsersAPI = ({ search = \"\", currentPage = 1, limit = 10, sortOrder = 'desc', graduationYear = null, classFilter = null }) => {\r\n    const params = {\r\n        search,\r\n        page: currentPage,\r\n        limit,\r\n        sortOrder,\r\n    };\r\n\r\n    // Chỉ thêm filter parameters nếu có giá trị\r\n    if (graduationYear !== null && graduationYear !== undefined) {\r\n        params.graduationYear = graduationYear;\r\n    }\r\n\r\n    if (classFilter !== null && classFilter !== undefined && classFilter !== '') {\r\n        params.class = classFilter;\r\n    }\r\n\r\n    return api.get(\"/v1/admin/user\", { params });\r\n};\r\n\r\nexport const getAllStaffAPI = ({ search = \"\", currentPage = 1, limit = 10, sortOrder = 'desc' }) => {\r\n    return api.get(\"/v1/admin/staff\", {\r\n        params: {\r\n            search,\r\n            page: currentPage,\r\n            limit,\r\n            sortOrder,\r\n        }\r\n    });\r\n};\r\n\r\nexport const getUserByIdAPI = (id) => {\r\n    return api.get(`/v1/admin/user/${id}`);\r\n};\r\n\r\nexport const findUsersAPI = (search) => {\r\n    return api.get(\"/v1/admin/user/search\", {\r\n        params: {\r\n            search,\r\n        }\r\n    });\r\n}\r\n\r\nexport const getUserClassesAPI = ({ id, search = \"\", currentPage = 1, limit = 10, sortOrder = 'desc' }) => {\r\n    return api.get(`/v1/admin/user/class/${id}`, {\r\n        params: {\r\n            search,\r\n            page: currentPage,\r\n            limit,\r\n            sortOrder,\r\n        }\r\n    });\r\n}\r\n\r\nexport const putUserAPI = ({ id, user }) => {\r\n    return api.put(`/v1/admin/user/${id}`, user);\r\n}\r\n\r\nexport const putUserTypeAPI = ({ id, type }) => {\r\n    return api.put(`/v1/admin/user/${id}/user-type`, { userType: type });\r\n}\r\n\r\nexport const putUserStatusAPI = ({ id, status }) => {\r\n    return api.put(`/v1/admin/user/${id}/status`, { status });\r\n}\r\n\r\nexport const deleteUserAPI = (id) => {\r\n    return api.delete(`/v1/admin/user/${id}`);\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,OAAO,MAAMC,cAAc,GAAGC,IAAA,IAAiH;EAAA,IAAhH;IAAEC,MAAM,GAAG,EAAE;IAAEC,WAAW,GAAG,CAAC;IAAEC,KAAK,GAAG,EAAE;IAAEC,SAAS,GAAG,MAAM;IAAEC,cAAc,GAAG,IAAI;IAAEC,WAAW,GAAG;EAAK,CAAC,GAAAN,IAAA;EACtI,MAAMO,MAAM,GAAG;IACXN,MAAM;IACNO,IAAI,EAAEN,WAAW;IACjBC,KAAK;IACLC;EACJ,CAAC;;EAED;EACA,IAAIC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKI,SAAS,EAAE;IACzDF,MAAM,CAACF,cAAc,GAAGA,cAAc;EAC1C;EAEA,IAAIC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKG,SAAS,IAAIH,WAAW,KAAK,EAAE,EAAE;IACzEC,MAAM,CAACG,KAAK,GAAGJ,WAAW;EAC9B;EAEA,OAAOR,GAAG,CAACa,GAAG,CAAC,gBAAgB,EAAE;IAAEJ;EAAO,CAAC,CAAC;AAChD,CAAC;AAED,OAAO,MAAMK,cAAc,GAAGC,KAAA,IAAsE;EAAA,IAArE;IAAEZ,MAAM,GAAG,EAAE;IAAEC,WAAW,GAAG,CAAC;IAAEC,KAAK,GAAG,EAAE;IAAEC,SAAS,GAAG;EAAO,CAAC,GAAAS,KAAA;EAC3F,OAAOf,GAAG,CAACa,GAAG,CAAC,iBAAiB,EAAE;IAC9BJ,MAAM,EAAE;MACJN,MAAM;MACNO,IAAI,EAAEN,WAAW;MACjBC,KAAK;MACLC;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMU,cAAc,GAAIC,EAAE,IAAK;EAClC,OAAOjB,GAAG,CAACa,GAAG,mBAAAK,MAAA,CAAmBD,EAAE,CAAE,CAAC;AAC1C,CAAC;AAED,OAAO,MAAME,YAAY,GAAIhB,MAAM,IAAK;EACpC,OAAOH,GAAG,CAACa,GAAG,CAAC,uBAAuB,EAAE;IACpCJ,MAAM,EAAE;MACJN;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMiB,iBAAiB,GAAGC,KAAA,IAA0E;EAAA,IAAzE;IAAEJ,EAAE;IAAEd,MAAM,GAAG,EAAE;IAAEC,WAAW,GAAG,CAAC;IAAEC,KAAK,GAAG,EAAE;IAAEC,SAAS,GAAG;EAAO,CAAC,GAAAe,KAAA;EAClG,OAAOrB,GAAG,CAACa,GAAG,yBAAAK,MAAA,CAAyBD,EAAE,GAAI;IACzCR,MAAM,EAAE;MACJN,MAAM;MACNO,IAAI,EAAEN,WAAW;MACjBC,KAAK;MACLC;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMgB,UAAU,GAAGC,KAAA,IAAkB;EAAA,IAAjB;IAAEN,EAAE;IAAEO;EAAK,CAAC,GAAAD,KAAA;EACnC,OAAOvB,GAAG,CAACyB,GAAG,mBAAAP,MAAA,CAAmBD,EAAE,GAAIO,IAAI,CAAC;AAChD,CAAC;AAED,OAAO,MAAME,cAAc,GAAGC,KAAA,IAAkB;EAAA,IAAjB;IAAEV,EAAE;IAAEW;EAAK,CAAC,GAAAD,KAAA;EACvC,OAAO3B,GAAG,CAACyB,GAAG,mBAAAP,MAAA,CAAmBD,EAAE,iBAAc;IAAEY,QAAQ,EAAED;EAAK,CAAC,CAAC;AACxE,CAAC;AAED,OAAO,MAAME,gBAAgB,GAAGC,KAAA,IAAoB;EAAA,IAAnB;IAAEd,EAAE;IAAEe;EAAO,CAAC,GAAAD,KAAA;EAC3C,OAAO/B,GAAG,CAACyB,GAAG,mBAAAP,MAAA,CAAmBD,EAAE,cAAW;IAAEe;EAAO,CAAC,CAAC;AAC7D,CAAC;AAED,OAAO,MAAMC,aAAa,GAAIhB,EAAE,IAAK;EACjC,OAAOjB,GAAG,CAACkC,MAAM,mBAAAhB,MAAA,CAAmBD,EAAE,CAAE,CAAC;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}