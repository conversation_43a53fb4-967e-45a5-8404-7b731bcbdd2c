import express from 'express';
import asyncHandler from '../middlewares/asyncHandler.js';
import validate from '../middlewares/validate.js';
import Roles from '../constants/Roles.js';
import { requireRoles } from '../middlewares/jwtMiddleware.js';
import * as SheetLinksController from '../controllers/SheetLinksController.js';
import PostSheetLinkRequest from '../dtos/requests/sheetlinks/PostSheetLinkRequest.js';

const router = express.Router();

// Tạo sheet link mới (chỉ admin/teacher/assistant)
router.post('/v1/admin/sheet-links',
    // validate(PostSheetLinkRequest),
    requireRoles(Roles.JustAdmin),
    asyncHandler(SheetLinksController.createSheetLink)
);

// Lấy danh sách sheet links
router.get('/v1/sheet-links',
    requireRoles(Roles.JustAdmin),
    asyncHandler(SheetLinksController.getSheetLinks)
);

// L<PERSON>y chi tiết sheet link theo ID
router.get('/v1/sheet-links/:id',
    requireRoles(Roles.JustAdmin),
    asyncHandler(SheetLinksController.getSheetLinkById)
);

router.post('/v1/admin/sheet-links/tuition/update',
    requireRoles(Roles.JustAdmin),
    asyncHandler(SheetLinksController.CheckUpdateTuitionForSheetLinks)
);

// Test Google Sheets connection
router.post('/v1/admin/sheet-links/:sheetId/test-connection',
    requireRoles(Roles.JustAdmin),
    asyncHandler(SheetLinksController.testGoogleSheetsConnection)
);

// Test thêm user vào sheet
router.post('/v1/admin/sheet-links/:sheetId/test-add-user',
    requireRoles(Roles.JustAdmin),
    asyncHandler(SheetLinksController.testAddUserToSheet)
);

// CHẠY MỘT LẦN RỒI XÓA ĐI - Thêm username/password vào tất cả Google Sheets
router.post('/v1/admin/sheet-links/add-username-password-all',
    requireRoles(Roles.JustAdmin),
    asyncHandler(SheetLinksController.addUsernamePasswordToSheets)
);

// CHẠY MỘT LẦN RỒI XÓA ĐI - Thêm username/password vào một sheet cụ thể
router.post('/v1/admin/sheet-links/:sheetId/add-username-password',
    requireRoles(Roles.JustAdmin),
    asyncHandler(SheetLinksController.addUsernamePasswordToSingleSheet)
);

export default router;
