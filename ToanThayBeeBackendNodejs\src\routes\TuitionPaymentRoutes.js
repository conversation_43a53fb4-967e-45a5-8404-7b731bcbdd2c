import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as TuitionPaymentController from '../controllers/TuitionPaymentController.js'

const router = express.Router()

// Lấy danh sách tất cả các khoản đóng học phí (chỉ admin, giáo viên, trợ giảng)
router.get('/v1/admin/tuition-payment',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.getAllTuitionPayments)
)

// Lấy danh sách đóng học phí của một lớp cụ thể (chỉ admin, giáo viên, trợ giảng)
router.get('/v1/admin/class/:classId/tuition-payment',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.getTuitionPaymentsByClassId)
)

// Lấy danh sách đóng học phí của một học sinh cụ thể (chỉ admin, giáo viên, trợ giảng và chính học sinh đó)
router.get('/v1/admin/user/:userId/tuition-payment',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.getTuitionPaymentsByUserId)
)


// Thống kê doanh thu học phí (chỉ admin, giáo viên)
router.get('/v1/admin/tuition-payment/statistics',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.getTuitionStatistics)
)

// Lấy thống kê tổng quan về các khoản học phí của một học sinh (chỉ admin, giáo viên, trợ giảng)
router.get('/v1/admin/user/:userId/tuition-summary',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.getUserTuitionSummary)
)

// Học sinh xem danh sách đóng học phí của mình
router.get('/v1/user/tuition-payment',
    requireRoles(Roles.JustStudent),
    asyncHandler((req, res) => {
        req.params.userId = req.user.id;
        return TuitionPaymentController.getTuitionPaymentsByUserId(req, res);
    })
)

router.get('/v1/user/tuition-payment/not-paid',
    requireRoles(Roles.JustStudent),
    asyncHandler(TuitionPaymentController.checkTuitionPaymentNotPaid)
)

// Học sinh xem thống kê tổng quan về các khoản học phí của mình
router.get('/v1/user/tuition-summary',
    requireRoles(Roles.JustStudent),
    asyncHandler((req, res) => {
        req.params.userId = req.user.id;
        return TuitionPaymentController.getUserTuitionSummary(req, res);
    })
)

// Lấy thông tin chi tiết một khoản đóng học phí (chỉ admin, giáo viên, trợ giảng và chính học sinh đó)
router.get('/v1/admin/tuition-payment/:id',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.getTuitionPaymentById)
)

// Học sinh xem chi tiết khoản đóng học phí của mình
router.get('/v1/user/tuition-payment/:id',
    requireRoles(Roles.JustStudent),
    asyncHandler(TuitionPaymentController.getUserTuitionPaymentById)
)

// Tạo mới khoản đóng học phí cho học sinh (chỉ admin, giáo viên)
router.post('/v1/admin/tuition-payment',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.createTuitionPayment)
)

// Tạo khoản đóng học phí cho tất cả học sinh trong hệ thống (chỉ admin, giáo viên)
router.post('/v1/admin/tuition-payment/batch',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.createTuitionPaymentsForAllStudents)
)

// Cập nhật thông tin khoản đóng học phí (chỉ admin, giáo viên)
router.put('/v1/admin/tuition-payment/:id',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.updateTuitionPayment)
)

// Xóa khoản đóng học phí (chỉ admin, giáo viên)
router.delete('/v1/admin/tuition-payment/:id',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(TuitionPaymentController.deleteTuitionPayment)
)


export default router
