'use strict';

import { Model } from 'sequelize';

export default (sequelize, DataTypes) => {
  class SheetLinks extends Model {
    static associate(models) {

      // M<PERSON>i quan hệ với bảng User (người tạo)
      SheetLinks.belongsTo(models.User, {
        foreignKey: 'createdBy',
        as: 'creator'
      });
    }
  }

  SheetLinks.init({
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Tiêu đề của sheet'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Mô tả về sheet'
    },
    sheetUrl: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Link đến Google Sheets hoặc Excel file'
    },
    category: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Danh mục của sheet (ví dụ: attendance, grades, reports)'
    },
    type: {
      type: DataTypes.ENUM('USER_LIST', 'ATTENDANCE', 'GRADES', 'REPORTS', 'OTHER'),
      allowNull: false,
      defaultValue: 'OTHER',
      comment: 'Loại sheet: USER_LIST (danh sách user), ATTENDANCE (điểm danh), GRADES (điểm số), REPORTS (báo cáo), OTHER (khác)'
    },
    relatedId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'ID liên quan (ví dụ: examId, lessonId tùy theo type)'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'user',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
      comment: 'ID người tạo sheet'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Trạng thái hoạt động của sheet'
    },
    accessLevel: {
      type: DataTypes.ENUM('PUBLIC', 'CLASS_ONLY', 'ADMIN_ONLY'),
      allowNull: false,
      defaultValue: 'CLASS_ONLY',
      comment: 'Mức độ truy cập: PUBLIC (tất cả), CLASS_ONLY (chỉ lớp), ADMIN_ONLY (chỉ admin)'
    },
    lastUpdated: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Thời gian cập nhật cuối cùng của sheet'
    },
    createdAt: {
      allowNull: false,
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      allowNull: false,
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'SheetLinks',
    tableName: 'sheetLinks',
    timestamps: true
  });

  return SheetLinks;
};
