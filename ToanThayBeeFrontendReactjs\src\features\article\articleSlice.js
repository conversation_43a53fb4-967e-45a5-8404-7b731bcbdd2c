import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as articleApi from "../../services/articleApi";
import { apiHandler } from "../../utils/apiHandler";

export const fetchArticles = createAsyncThunk(
    "articles/fetchArticles",
    async (params = {}, { dispatch }) => {
        return await apiHandler(dispatch, articleApi.getArticleListAPI, params, () => {
        }, false, false);
    }
);

export const fetchNewestArticle = createAsyncThunk(
    "articles/fetchNewestArticle",
    async (_, { dispatch }) => {
        return await apiHandler(dispatch, articleApi.getNewestArticleAPI, null, () => {
        }, false, false);
    }
);

export const getArticleTypeCount = createAsyncThunk(
    "articles/getArticleTypeCount",
    async (_, { dispatch }) => {
        return await apiHandler(dispatch, articleApi.getArticleTypeCountAPI, null, () => {
        }, false, false, false, false);
    }
)

export const fetchArticleById = createAsyncThunk(
    "articles/fetchArticleById",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, articleApi.getArticleAPI, id, () => {
        }, false, false);
    }
);

export const postArticle = createAsyncThunk(
    "articles/postArticle",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, articleApi.postArticleAPI, data, () => {
        }, true, false);
    }
);

export const putArticle = createAsyncThunk(
    "articles/putArticle",
    async ({ articleId, data }, { dispatch }) => {
        return await apiHandler(dispatch, articleApi.putArticleAPI, { articleId, data }, () => {
        }, true, false);
    }
);

export const deleteArticle = createAsyncThunk(
    "articles/deleteArticle",
    async (id, { dispatch }) => {
        return await apiHandler(dispatch, articleApi.deleteArticleAPI, id, () => {
        }, true, false);
    }
);

const articleSlice = createSlice({
    name: "articles",
    initialState: {
        articles: [],
        article: null,
        pagination: {
            total: 0,
            totalPages: 0,
            currentPage: 1,
            limit: 10,
            hasNextPage: false,
            hasPrevPage: false
        },
        articleCount: [
            { "type": "SE", "count": 0 },
            { "type": "LT", "count": 0 },
            { "type": "KT", "count": 0 }
        ],
        loading: false,
    },
    reducers: {
        setArticles: (state, action) => {
            state.articles = action.payload;
        },
        setArticle: (state, action) => {
            state.article = action.payload;
        },
        setPagination: (state, action) => {
            state.pagination = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchArticles.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchArticles.fulfilled, (state, action) => {
                state.loading = false;
                state.articles = action.payload.data;
                if (action.payload.pagination) {
                    state.pagination = action.payload.pagination;
                }
            })
            .addCase(fetchArticles.rejected, (state) => {
                state.loading = false;
                state.articles = [];
            })
            .addCase(fetchNewestArticle.pending, (state) => {
                state.loading = true;
                state.articles = [];
            })
            .addCase(fetchNewestArticle.fulfilled, (state, action) => {
                state.loading = false;
                state.articles = action.payload.data;
            })
            .addCase(fetchNewestArticle.rejected, (state) => {
                state.loading = false;
                state.articles = [];
            })
            .addCase(getArticleTypeCount.fulfilled, (state, action) => {
                state.articleCount = action.payload.data
            })
            .addCase(fetchArticleById.pending, (state) => {
                state.loading = true;
                state.article = null;
            })
            .addCase(fetchArticleById.fulfilled, (state, action) => {
                state.loading = false;
                state.article = action.payload.data;
            })
            .addCase(fetchArticleById.rejected, (state) => {
                state.loading = false;
                state.article = null;
            })
            .addCase(deleteArticle.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteArticle.fulfilled, (state, action) => {
                state.loading = false;
                state.articles = state.articles.filter((article) => article.id != action.payload.data);
            })
            .addCase(deleteArticle.rejected, (state) => {
                state.loading = false;
            })
    },
});

export const { setArticles, setArticle, setPagination } = articleSlice.actions;
export default articleSlice.reducer;