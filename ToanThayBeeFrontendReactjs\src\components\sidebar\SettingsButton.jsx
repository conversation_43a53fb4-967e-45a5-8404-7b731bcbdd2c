import React from 'react';

/**
 * Button component for toggling settings panel
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.showSettings - Whether settings panel is shown
 * @param {Function} props.setShowSettings - Function to toggle settings panel
 * @param {boolean} props.isDarkMode - Whether dark mode is enabled
 */
const SettingsButton = ({ showSettings, setShowSettings, isDarkMode }) => {
  return (
    <button 
      onClick={() => setShowSettings(prev => !prev)} 
      className="transition-transform duration-500"
      aria-label="Toggle settings"
    >
      <svg
        className={`transform transition-transform duration-500 ${
          showSettings ? "rotate-180" : "rotate-0"
        } ${isDarkMode ? 'fill-white' : 'fill-black'}`}
        xmlns="http://www.w3.org/2000/svg"
        width="24" 
        height="24" 
        viewBox="0 0 24 24" 
        fill="none"
      >
        <path 
          fillRule="evenodd" 
          clipRule="evenodd" 
          d="M14.208 4.83C14.6613 4.97 15.0947 5.15 15.508 5.37L17.341 4.27C17.5321 4.15536 17.7561 4.10786 17.9773 4.13504C18.1985 4.16221 18.4043 4.26251 18.562 4.42L19.58 5.438C19.7375 5.5957 19.8378 5.80149 19.865 6.02271C19.8921 6.24392 19.8446 6.46787 19.73 6.659L18.63 8.492C18.85 8.90533 19.03 9.33867 19.17 9.792L21.243 10.311C21.4592 10.3652 21.6512 10.49 21.7883 10.6658C21.9255 10.8415 22 11.0581 22 11.281V12.719C22 12.9419 21.9255 13.1585 21.7883 13.3342C21.6512 13.51 21.4592 13.6348 21.243 13.689L19.17 14.208C19.03 14.6613 18.85 15.0947 18.63 15.508L19.73 17.341C19.8446 17.5321 19.8921 17.7561 19.865 17.9773C19.8378 18.1985 19.7375 18.4043 19.58 18.562L18.562 19.58C18.4043 19.7375 18.1985 19.8378 17.9773 19.865C17.7561 19.8921 17.5321 19.8446 17.341 19.73L15.508 18.63C15.0947 18.85 14.6613 19.03 14.208 19.17L13.689 21.243C13.6348 21.4592 13.51 21.6512 13.3342 21.7883C13.1585 21.9255 12.9419 22 12.719 22H11.281C11.0581 22 10.8415 21.9255 10.6658 21.7883C10.49 21.6512 10.3652 21.4592 10.311 21.243L9.792 19.17C9.3427 19.0312 8.90744 18.8504 8.492 18.63L6.659 19.73C6.46787 19.8446 6.24392 19.8921 6.02271 19.865C5.80149 19.8378 5.5957 19.7375 5.438 19.58L4.42 18.562C4.26251 18.4043 4.16221 18.1985 4.13504 17.9773C4.10786 17.7561 4.15536 17.5321 4.27 17.341L5.37 15.508C5.14964 15.0926 4.96885 14.6573 4.83 14.208L2.757 13.689C2.54092 13.6349 2.3491 13.5101 2.21196 13.3346C2.07483 13.1591 2.00023 12.9428 2 12.72V11.282C2.00001 11.0591 2.0745 10.8425 2.21166 10.6668C2.34881 10.491 2.54075 10.3662 2.757 10.312L4.83 9.793C4.97 9.33967 5.15 8.90633 5.37 8.493L4.27 6.66C4.15536 6.46887 4.10786 6.24492 4.13504 6.02371C4.16221 5.80249 4.26251 5.5967 4.42 5.439L5.438 4.42C5.5957 4.26251 5.80149 4.16221 6.02271 4.13504C6.24392 4.10786 6.46787 4.15536 6.659 4.27L8.492 5.37C8.90533 5.15 9.33867 4.97 9.792 4.83L10.311 2.757C10.3651 2.54092 10.4899 2.3491 10.6654 2.21196C10.8409 2.07483 11.0572 2.00023 11.28 2H12.718C12.9409 2.00001 13.1575 2.0745 13.3332 2.21166C13.509 2.34881 13.6338 2.54075 13.688 2.757L14.208 4.83ZM12 16C13.0609 16 14.0783 15.5786 14.8284 14.8284C15.5786 14.0783 16 13.0609 16 12C16 10.9391 15.5786 9.92172 14.8284 9.17157C14.0783 8.42143 13.0609 8 12 8C10.9391 8 9.92172 8.42143 9.17157 9.17157C8.42143 9.92172 8 10.9391 8 12C8 13.0609 8.42143 14.0783 9.17157 14.8284C9.92172 15.5786 10.9391 16 12 16Z" 
        />
      </svg>
    </button>
  );
};

export default SettingsButton;
