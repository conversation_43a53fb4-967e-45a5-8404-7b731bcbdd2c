import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import Roles from '../constants/Roles.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as SlideController from '../controllers/SlideController.js'

const router = express.Router()

router.get('/v1/slide',
    requireR<PERSON>s(Roles.JustAdmin),
    async<PERSON>and<PERSON>(SlideController.getSlides)
)
router.get('/v1/slide/:id',
    requireRoles(Roles.JustAdmin),
    async<PERSON>andler(SlideController.getSlideById)
)
router.post('/v1/slide',
    requireRoles(Roles.JustAdmin),
    async<PERSON>and<PERSON>(SlideController.postSlide)
)
router.put('/v1/slide/:id',
    requireRoles(Roles.JustAdmin),
    as<PERSON><PERSON><PERSON><PERSON>(SlideController.putSlide)
)
router.delete('/v1/slide/:id',
    requireRoles(Roles.JustAdmin),
    async<PERSON>and<PERSON>(SlideController.deleteSlide)
)

export default router