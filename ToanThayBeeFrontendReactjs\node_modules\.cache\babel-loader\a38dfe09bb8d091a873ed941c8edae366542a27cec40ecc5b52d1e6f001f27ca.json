{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$();\n// Optimized Form Panel Component\nimport { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\nimport { setExamData, postExam, nextStep, prevStep } from \"src/features/addExam/addExamSlice\";\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\nimport CompactStepHeader from \"./CompactStepHeader\";\nimport { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, CheckCircle, ChevronRight, ChevronLeft, Plus, Save } from \"lucide-react\";\nimport ImageUpload from \"src/components/image/UploadImage\";\nimport UploadPdf from \"src/components/UploadPdf\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LeftContent = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    step,\n    examData,\n    loading\n  } = useSelector(state => state.addExam);\n  const {\n    codes\n  } = useSelector(state => state.codes);\n\n  // Load codes on mount\n  useEffect(() => {\n    dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\n  }, [dispatch]);\n  const updateExamData = (field, value) => {\n    console.log('Updating exam data:', field, value);\n    dispatch(setExamData({\n      field,\n      value\n    }));\n  };\n  const handleNext = () => {\n    if (step < 3) dispatch(nextStep());\n  };\n  const handlePrev = () => {\n    if (step > 1) dispatch(prevStep());\n  };\n  const handleSubmit = async () => {\n    try {\n      await dispatch(postExam({\n        examData,\n        examImage: null,\n        questions: [],\n        questionImages: [],\n        statementImages: [],\n        solutionImages: [],\n        examFile: null\n      })).unwrap();\n      // Handle success\n    } catch (error) {\n      console.error('Error creating exam:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-[calc(100vh_-_42px)]\",\n    children: [/*#__PURE__*/_jsxDEV(CompactStepHeader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 space-y-3\",\n        children: [step === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: [\"T\\xEAn \\u0111\\u1EC1 thi \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 52\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: examData.name || '',\n                onChange: e => updateExamData('name', e.target.value),\n                className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Nh\\u1EADp t\\xEAn \\u0111\\u1EC1 thi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: [\"Ki\\u1EC3u \\u0111\\u1EC1 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n                selectedOption: examData.typeOfExam,\n                onChange: option => updateExamData('typeOfExam', option),\n                options: Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : [],\n                className: \"text-xs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n                selectedOption: examData.class,\n                onChange: option => updateExamData('class', option),\n                options: Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : [],\n                className: \"text-xs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: [\"N\\u0103m \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(DropMenuBarAdmin, {\n                selectedOption: examData.year,\n                onChange: option => updateExamData('year', option),\n                options: Array.isArray(codes[\"year\"]) ? codes[\"year\"] : [],\n                className: \"text-xs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(Clock, {\n                  className: \"w-3 h-3 inline mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 41\n                }, this), \"Th\\u1EDDi gian (ph\\xFAt)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: examData.testDuration || '',\n                onChange: e => updateExamData('testDuration', e.target.value),\n                className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"90\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(Users, {\n                  className: \"w-3 h-3 inline mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 41\n                }, this), \"\\u0110i\\u1EC3m \\u0111\\u1EA1t (%)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: examData.passRate || '',\n                onChange: e => updateExamData('passRate', e.target.value),\n                className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 29\n          }, this), examData.typeOfExam === \"OT\" && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-xs font-medium text-gray-700 mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(BookOpen, {\n                className: \"w-3 h-3 inline mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 41\n              }, this), \"Ch\\u01B0\\u01A1ng\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(SuggestInputBarAdmin, {\n              selectedOption: examData.chapter,\n              onChange: option => updateExamData('chapter', option),\n              options: Array.isArray(codes[\"chapter\"]) ? codes[\"chapter\"] : [],\n              className: \"text-xs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-xs font-medium text-gray-700 mb-1\",\n              children: \"Link l\\u1EDDi gi\\u1EA3i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: examData.solutionUrl,\n              onChange: e => updateExamData('solutionUrl', e.target.value),\n              className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n              placeholder: \"Nh\\u1EADp link l\\u1EDDi gi\\u1EA3i vd: youtube, ....\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-xs font-medium text-gray-700 mb-1\",\n              children: \"M\\xF4 t\\u1EA3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: examData.description || '',\n              onChange: e => updateExamData('description', e.target.value),\n              rows: 2,\n              className: \"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n              placeholder: \"M\\xF4 t\\u1EA3 ng\\u1EAFn v\\u1EC1 \\u0111\\u1EC1 thi...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center text-xs\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: examData.public || false,\n                onChange: e => updateExamData('public', e.target.checked),\n                className: \"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1 text-gray-700\",\n                children: \"C\\xF4ng khai\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center text-xs\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: examData.isClassroomExam || false,\n                onChange: e => updateExamData('isClassroomExam', e.target.checked),\n                className: \"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1 text-gray-700\",\n                children: \"\\u0110\\u1EC1 thi l\\u1EDBp\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(ImageIcon, {\n                  className: \"w-3 h-3 inline mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 41\n                }, this), \"\\u1EA2nh \\u0111\\u1EC1 thi\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(ImageUpload, {\n                image: examData.imageUrl,\n                setImage: img => updateExamData('imageUrl', img),\n                inputId: \"exam-image-compact\",\n                compact: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(Upload, {\n                  className: \"w-3 h-3 inline mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 41\n                }, this), \"File PDF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(UploadPdf, {\n                setPdf: pdf => updateExamData('pdfFile', pdf),\n                deleteButton: false,\n                compact: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 25\n        }, this), step === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-8 h-8 mx-auto text-gray-400 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-900 mb-1\",\n              children: \"Th\\xEAm c\\xE2u h\\u1ECFi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-600 mb-3\",\n              children: \"T\\u1EA1o c\\xE2u h\\u1ECFi cho \\u0111\\u1EC1 thi c\\u1EE7a b\\u1EA1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs\",\n                children: [/*#__PURE__*/_jsxDEV(Plus, {\n                  className: \"w-3 h-3 inline mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 41\n                }, this), \"Th\\xEAm c\\xE2u tr\\u1EAFc nghi\\u1EC7m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full px-3 py-2 bg-green-50 border border-green-200 rounded hover:bg-green-100 transition-colors text-xs\",\n                children: [/*#__PURE__*/_jsxDEV(Plus, {\n                  className: \"w-3 h-3 inline mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 41\n                }, this), \"Th\\xEAm c\\xE2u \\u0111\\xFAng sai\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full px-3 py-2 bg-purple-50 border border-purple-200 rounded hover:bg-purple-100 transition-colors text-xs\",\n                children: [/*#__PURE__*/_jsxDEV(Plus, {\n                  className: \"w-3 h-3 inline mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 41\n                }, this), \"Th\\xEAm c\\xE2u tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 25\n        }, this), step === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2\",\n              children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"w-4 h-4 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-900 mb-1\",\n              children: \"X\\xE1c nh\\u1EADn t\\u1EA1o \\u0111\\u1EC1 thi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-600\",\n              children: \"Ki\\u1EC3m tra th\\xF4ng tin v\\xE0 ho\\xE0n t\\u1EA5t\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded p-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900 mb-2 text-xs\",\n              children: \"Th\\xF4ng tin t\\xF3m t\\u1EAFt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-1 text-xs\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"T\\xEAn:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1 font-medium\",\n                  children: examData.name || \"Chưa nhập\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Ki\\u1EC3u:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1 font-medium\",\n                  children: examData.typeOfExam || \"Chưa chọn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"L\\u1EDBp:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1 font-medium\",\n                  children: examData.class || \"Chưa chọn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"N\\u0103m:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1 font-medium\",\n                  children: examData.year || \"Chưa chọn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 29\n          }, this), (!examData.name || !examData.typeOfExam || !examData.class || !examData.year) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded p-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"font-medium text-red-800 mb-1 text-xs\",\n              children: \"Thi\\u1EBFu th\\xF4ng tin:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-xs text-red-700 space-y-0.5\",\n              children: [!examData.name && /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 T\\xEAn \\u0111\\u1EC1 thi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 60\n              }, this), !examData.typeOfExam && /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Ki\\u1EC3u \\u0111\\u1EC1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 66\n              }, this), !examData.class && /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 L\\u1EDBp\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 61\n              }, this), !examData.year && /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 N\\u0103m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 60\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-t border-gray-200 p-2 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePrev,\n          disabled: step === 1,\n          className: \"flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: [/*#__PURE__*/_jsxDEV(ChevronLeft, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 25\n          }, this), \"Quay l\\u1EA1i\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 21\n        }, this), step < 3 ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNext,\n          className: \"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs\",\n          children: [\"Ti\\u1EBFp theo\", /*#__PURE__*/_jsxDEV(ChevronRight, {\n            className: \"w-3 h-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSubmit,\n          disabled: loading || !examData.name || !examData.typeOfExam || !examData.class || !examData.year,\n          className: \"flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs\",\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 37\n            }, this), \"\\u0110ang t\\u1EA1o...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 37\n            }, this), \"T\\u1EA1o \\u0111\\u1EC1 thi\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 9\n  }, this);\n};\n_s(LeftContent, \"Zx+opaNzcPqd6V0aVCs56mhhr6E=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = LeftContent;\nexport default LeftContent;\nvar _c;\n$RefreshReg$(_c, \"LeftContent\");", "map": {"version": 3, "names": ["useEffect", "useDispatch", "useSelector", "fetchCodesByType", "setExamData", "postExam", "nextStep", "prevStep", "DropMenuBarAdmin", "SuggestInputBarAdmin", "CompactStepHeader", "Clock", "Users", "BookOpen", "Image", "ImageIcon", "Upload", "FileText", "CheckCircle", "ChevronRight", "ChevronLeft", "Plus", "Save", "ImageUpload", "UploadPdf", "LoadingSpinner", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LeftContent", "_s", "dispatch", "step", "examData", "loading", "state", "addExam", "codes", "updateExamData", "field", "value", "console", "log", "handleNext", "handlePrev", "handleSubmit", "examImage", "questions", "questionImages", "statementImages", "solutionImages", "examFile", "unwrap", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "name", "onChange", "e", "target", "placeholder", "selectedOption", "typeOfExam", "option", "options", "Array", "isArray", "class", "year", "testDuration", "passRate", "chapter", "solutionUrl", "description", "rows", "checked", "public", "isClassroomExam", "image", "imageUrl", "setImage", "img", "inputId", "compact", "setPdf", "pdf", "deleteButton", "onClick", "disabled", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["// Optimized Form Panel Component\r\nimport { useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { fetchCodesByType } from \"src/features/code/codeSlice\";\r\nimport { setExamData, postExam, nextStep, prevStep } from \"src/features/addExam/addExamSlice\";\r\nimport DropMenuBarAdmin from \"src/components/dropMenu/OptionBarAdmin\";\r\nimport SuggestInputBarAdmin from \"src/components/input/suggestInputBarAdmin\";\r\nimport CompactStepHeader from \"./CompactStepHeader\";\r\nimport { Clock, Users, BookOpen, Image as ImageIcon, Upload, FileText, CheckCircle, ChevronRight, ChevronLeft, Plus, Save } from \"lucide-react\";\r\nimport ImageUpload from \"src/components/image/UploadImage\";\r\nimport UploadPdf from \"src/components/UploadPdf\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\n\r\n\r\nconst LeftContent = () => {\r\n    const dispatch = useDispatch();\r\n    const { step, examData, loading } = useSelector((state) => state.addExam);\r\n    const { codes } = useSelector(state => state.codes);\r\n\r\n    // Load codes on mount\r\n    useEffect(() => {\r\n        dispatch(fetchCodesByType([\"chapter\", \"difficulty\", \"grade\", \"exam type\", \"year\"]));\r\n    }, [dispatch]);\r\n\r\n    const updateExamData = (field, value) => {\r\n        console.log('Updating exam data:', field, value);\r\n        dispatch(setExamData({ field, value }));\r\n    };\r\n\r\n    const handleNext = () => {\r\n        if (step < 3) dispatch(nextStep());\r\n    };\r\n\r\n    const handlePrev = () => {\r\n        if (step > 1) dispatch(prevStep());\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        try {\r\n            await dispatch(postExam({\r\n                examData,\r\n                examImage: null,\r\n                questions: [],\r\n                questionImages: [],\r\n                statementImages: [],\r\n                solutionImages: [],\r\n                examFile: null\r\n            })).unwrap();\r\n            // Handle success\r\n        } catch (error) {\r\n            console.error('Error creating exam:', error);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-[calc(100vh_-_42px)]\">\r\n            {/* Compact Step Header */}\r\n            <CompactStepHeader />\r\n\r\n            {/* Scrollable Form Content */}\r\n            <div className=\"flex-1 overflow-y-auto\">\r\n                <div className=\"p-3 space-y-3\">\r\n                    {/* Step 1: Basic Information */}\r\n                    {step === 1 && (\r\n                        <div className=\"space-y-3\">\r\n                            {/* Compact Name & Type Row */}\r\n                            <div className=\"grid grid-cols-2 gap-2\">\r\n                                <div>\r\n                                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                                        Tên đề thi <span className=\"text-red-500\">*</span>\r\n                                    </label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        value={examData.name || ''}\r\n                                        onChange={(e) => updateExamData('name', e.target.value)}\r\n                                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                                        placeholder=\"Nhập tên đề thi\"\r\n                                    />\r\n                                </div>\r\n                                <div>\r\n                                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                                        Kiểu đề <span className=\"text-red-500\">*</span>\r\n                                    </label>\r\n                                    <DropMenuBarAdmin\r\n                                        selectedOption={examData.typeOfExam}\r\n                                        onChange={(option) => updateExamData('typeOfExam', option)}\r\n                                        options={Array.isArray(codes[\"exam type\"]) ? codes[\"exam type\"] : []}\r\n                                        className=\"text-xs\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Compact Class & Year Row */}\r\n                            <div className=\"grid grid-cols-2 gap-2\">\r\n                                <div>\r\n                                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                                        Lớp <span className=\"text-red-500\">*</span>\r\n                                    </label>\r\n                                    <DropMenuBarAdmin\r\n                                        selectedOption={examData.class}\r\n                                        onChange={(option) => updateExamData('class', option)}\r\n                                        options={Array.isArray(codes[\"grade\"]) ? codes[\"grade\"] : []}\r\n                                        className=\"text-xs\"\r\n                                    />\r\n                                </div>\r\n                                <div>\r\n                                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                                        Năm <span className=\"text-red-500\">*</span>\r\n                                    </label>\r\n                                    <DropMenuBarAdmin\r\n                                        selectedOption={examData.year}\r\n                                        onChange={(option) => updateExamData('year', option)}\r\n                                        options={Array.isArray(codes[\"year\"]) ? codes[\"year\"] : []}\r\n                                        className=\"text-xs\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Compact Duration & Pass Rate Row */}\r\n                            <div className=\"grid grid-cols-2 gap-2\">\r\n                                <div>\r\n                                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                                        <Clock className=\"w-3 h-3 inline mr-1\" />\r\n                                        Thời gian (phút)\r\n                                    </label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        value={examData.testDuration || ''}\r\n                                        onChange={(e) => updateExamData('testDuration', e.target.value)}\r\n                                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                                        placeholder=\"90\"\r\n                                    />\r\n                                </div>\r\n                                <div>\r\n                                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                                        <Users className=\"w-3 h-3 inline mr-1\" />\r\n                                        Điểm đạt (%)\r\n                                    </label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        value={examData.passRate || ''}\r\n                                        onChange={(e) => updateExamData('passRate', e.target.value)}\r\n                                        className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                                        placeholder=\"50\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Chapter (conditional) */}\r\n                            {examData.typeOfExam === \"OT\" && (\r\n                                <div>\r\n                                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                                        <BookOpen className=\"w-3 h-3 inline mr-1\" />\r\n                                        Chương\r\n                                    </label>\r\n                                    <SuggestInputBarAdmin\r\n                                        selectedOption={examData.chapter}\r\n                                        onChange={(option) => updateExamData('chapter', option)}\r\n                                        options={Array.isArray(codes[\"chapter\"]) ? codes[\"chapter\"] : []}\r\n                                        className=\"text-xs\"\r\n                                    />\r\n                                </div>\r\n                            )}\r\n                            <div>\r\n                                <label className=\"block text-xs font-medium text-gray-700 mb-1\">Link lời giải</label>\r\n                                <textarea\r\n                                    value={examData.solutionUrl}\r\n                                    onChange={(e) => updateExamData('solutionUrl', e.target.value)}\r\n                                    className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                                    placeholder=\"Nhập link lời giải vd: youtube, ....\"\r\n                                />\r\n                            </div>\r\n                            {/* Compact Description */}\r\n                            <div>\r\n                                <label className=\"block text-xs font-medium text-gray-700 mb-1\">Mô tả</label>\r\n                                <textarea\r\n                                    value={examData.description || ''}\r\n                                    onChange={(e) => updateExamData('description', e.target.value)}\r\n                                    rows={2}\r\n                                    className=\"w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\r\n                                    placeholder=\"Mô tả ngắn về đề thi...\"\r\n                                />\r\n                            </div>\r\n\r\n                            {/* Compact Checkboxes */}\r\n                            <div className=\"flex items-center gap-3\">\r\n                                <label className=\"flex items-center text-xs\">\r\n                                    <input\r\n                                        type=\"checkbox\"\r\n                                        checked={examData.public || false}\r\n                                        onChange={(e) => updateExamData('public', e.target.checked)}\r\n                                        className=\"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\r\n                                    />\r\n                                    <span className=\"ml-1 text-gray-700\">Công khai</span>\r\n                                </label>\r\n                                <label className=\"flex items-center text-xs\">\r\n                                    <input\r\n                                        type=\"checkbox\"\r\n                                        checked={examData.isClassroomExam || false}\r\n                                        onChange={(e) => updateExamData('isClassroomExam', e.target.checked)}\r\n                                        className=\"form-checkbox h-3 w-3 text-blue-600 rounded focus:ring-blue-500\"\r\n                                    />\r\n                                    <span className=\"ml-1 text-gray-700\">Đề thi lớp</span>\r\n                                </label>\r\n                            </div>\r\n\r\n                            {/* Compact File Uploads */}\r\n                            <div className=\"flex flex-col gap-2\">\r\n                                <div>\r\n                                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                                        <ImageIcon className=\"w-3 h-3 inline mr-1\" />\r\n                                        Ảnh đề thi\r\n                                    </label>\r\n                                    <ImageUpload\r\n                                        image={examData.imageUrl}\r\n                                        setImage={(img) => updateExamData('imageUrl', img)}\r\n                                        inputId=\"exam-image-compact\"\r\n                                        compact={true}\r\n                                    />\r\n                                </div>\r\n                                <div>\r\n                                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n                                        <Upload className=\"w-3 h-3 inline mr-1\" />\r\n                                        File PDF\r\n                                    </label>\r\n                                    <UploadPdf\r\n                                        setPdf={(pdf) => updateExamData('pdfFile', pdf)}\r\n                                        deleteButton={false}\r\n                                        compact={true}\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* Step 2: Questions */}\r\n                    {step === 2 && (\r\n                        <div className=\"space-y-3\">\r\n                            <div className=\"text-center py-4\">\r\n                                <FileText className=\"w-8 h-8 mx-auto text-gray-400 mb-2\" />\r\n                                <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Thêm câu hỏi</h3>\r\n                                <p className=\"text-xs text-gray-600 mb-3\">\r\n                                    Tạo câu hỏi cho đề thi của bạn\r\n                                </p>\r\n                                <div className=\"space-y-2\">\r\n                                    <button className=\"w-full px-3 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors text-xs\">\r\n                                        <Plus className=\"w-3 h-3 inline mr-1\" />\r\n                                        Thêm câu trắc nghiệm\r\n                                    </button>\r\n                                    <button className=\"w-full px-3 py-2 bg-green-50 border border-green-200 rounded hover:bg-green-100 transition-colors text-xs\">\r\n                                        <Plus className=\"w-3 h-3 inline mr-1\" />\r\n                                        Thêm câu đúng sai\r\n                                    </button>\r\n                                    <button className=\"w-full px-3 py-2 bg-purple-50 border border-purple-200 rounded hover:bg-purple-100 transition-colors text-xs\">\r\n                                        <Plus className=\"w-3 h-3 inline mr-1\" />\r\n                                        Thêm câu trả lời ngắn\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* Step 3: Confirmation */}\r\n                    {step === 3 && (\r\n                        <div className=\"space-y-3\">\r\n                            <div className=\"text-center py-3\">\r\n                                <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2\">\r\n                                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\r\n                                </div>\r\n                                <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Xác nhận tạo đề thi</h3>\r\n                                <p className=\"text-xs text-gray-600\">\r\n                                    Kiểm tra thông tin và hoàn tất\r\n                                </p>\r\n                            </div>\r\n\r\n                            {/* Compact Summary */}\r\n                            <div className=\"bg-gray-50 rounded p-2\">\r\n                                <h4 className=\"font-medium text-gray-900 mb-2 text-xs\">Thông tin tóm tắt</h4>\r\n                                <div className=\"grid grid-cols-2 gap-1 text-xs\">\r\n                                    <div>\r\n                                        <span className=\"text-gray-600\">Tên:</span>\r\n                                        <span className=\"ml-1 font-medium\">{examData.name || \"Chưa nhập\"}</span>\r\n                                    </div>\r\n                                    <div>\r\n                                        <span className=\"text-gray-600\">Kiểu:</span>\r\n                                        <span className=\"ml-1 font-medium\">{examData.typeOfExam || \"Chưa chọn\"}</span>\r\n                                    </div>\r\n                                    <div>\r\n                                        <span className=\"text-gray-600\">Lớp:</span>\r\n                                        <span className=\"ml-1 font-medium\">{examData.class || \"Chưa chọn\"}</span>\r\n                                    </div>\r\n                                    <div>\r\n                                        <span className=\"text-gray-600\">Năm:</span>\r\n                                        <span className=\"ml-1 font-medium\">{examData.year || \"Chưa chọn\"}</span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Validation Errors */}\r\n                            {(!examData.name || !examData.typeOfExam || !examData.class || !examData.year) && (\r\n                                <div className=\"bg-red-50 border border-red-200 rounded p-2\">\r\n                                    <h5 className=\"font-medium text-red-800 mb-1 text-xs\">Thiếu thông tin:</h5>\r\n                                    <ul className=\"text-xs text-red-700 space-y-0.5\">\r\n                                        {!examData.name && <li>• Tên đề thi</li>}\r\n                                        {!examData.typeOfExam && <li>• Kiểu đề</li>}\r\n                                        {!examData.class && <li>• Lớp</li>}\r\n                                        {!examData.year && <li>• Năm</li>}\r\n                                    </ul>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            </div>\r\n\r\n            {/* Compact Navigation Footer */}\r\n            <div className=\"border-t border-gray-200 p-2 bg-white\">\r\n                <div className=\"flex justify-between items-center\">\r\n                    <button\r\n                        onClick={handlePrev}\r\n                        disabled={step === 1}\r\n                        className=\"flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                        <ChevronLeft className=\"w-3 h-3\" />\r\n                        Quay lại\r\n                    </button>\r\n\r\n                    {step < 3 ? (\r\n                        <button\r\n                            onClick={handleNext}\r\n                            className=\"flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs\"\r\n                        >\r\n                            Tiếp theo\r\n                            <ChevronRight className=\"w-3 h-3\" />\r\n                        </button>\r\n                    ) : (\r\n                        <button\r\n                            onClick={handleSubmit}\r\n                            disabled={loading || !examData.name || !examData.typeOfExam || !examData.class || !examData.year}\r\n                            className=\"flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 text-xs\"\r\n                        >\r\n                            {loading ? (\r\n                                <>\r\n                                    <LoadingSpinner size=\"sm\" />\r\n                                    Đang tạo...\r\n                                </>\r\n                            ) : (\r\n                                <>\r\n                                    <Save className=\"w-3 h-3\" />\r\n                                    Tạo đề thi\r\n                                </>\r\n                            )}\r\n                        </button>\r\n                    )}\r\n                </div>\r\n            </div>\r\n        </div >\r\n    );\r\n};\r\n\r\nexport default LeftContent;"], "mappings": ";;AAAA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,mCAAmC;AAC7F,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,IAAI,EAAEC,IAAI,QAAQ,cAAc;AAC/I,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,cAAc,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGnE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgC,IAAI;IAAEC,QAAQ;IAAEC;EAAQ,CAAC,GAAGjC,WAAW,CAAEkC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EACzE,MAAM;IAAEC;EAAM,CAAC,GAAGpC,WAAW,CAACkC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAAC;;EAEnD;EACAtC,SAAS,CAAC,MAAM;IACZgC,QAAQ,CAAC7B,gBAAgB,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;EACvF,CAAC,EAAE,CAAC6B,QAAQ,CAAC,CAAC;EAEd,MAAMO,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,KAAK,EAAEC,KAAK,CAAC;IAChDT,QAAQ,CAAC5B,WAAW,CAAC;MAAEoC,KAAK;MAAEC;IAAM,CAAC,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIX,IAAI,GAAG,CAAC,EAAED,QAAQ,CAAC1B,QAAQ,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAMuC,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIZ,IAAI,GAAG,CAAC,EAAED,QAAQ,CAACzB,QAAQ,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAMuC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMd,QAAQ,CAAC3B,QAAQ,CAAC;QACpB6B,QAAQ;QACRa,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,EAAE;QACbC,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE,EAAE;QACnBC,cAAc,EAAE,EAAE;QAClBC,QAAQ,EAAE;MACd,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MACZ;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZZ,OAAO,CAACY,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAChD;EACJ,CAAC;EAED,oBACI3B,OAAA;IAAK4B,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEjD7B,OAAA,CAACjB,iBAAiB;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrBjC,OAAA;MAAK4B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACnC7B,OAAA;QAAK4B,SAAS,EAAC,eAAe;QAAAC,QAAA,GAEzBvB,IAAI,KAAK,CAAC,iBACPN,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAEtB7B,OAAA;YAAK4B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnC7B,OAAA;cAAA6B,QAAA,gBACI7B,OAAA;gBAAO4B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,0BACjD,eAAA7B,OAAA;kBAAM4B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACRjC,OAAA;gBACIkC,IAAI,EAAC,MAAM;gBACXpB,KAAK,EAAEP,QAAQ,CAAC4B,IAAI,IAAI,EAAG;gBAC3BC,QAAQ,EAAGC,CAAC,IAAKzB,cAAc,CAAC,MAAM,EAAEyB,CAAC,CAACC,MAAM,CAACxB,KAAK,CAAE;gBACxDc,SAAS,EAAC,kHAAkH;gBAC5HW,WAAW,EAAC;cAAiB;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjC,OAAA;cAAA6B,QAAA,gBACI7B,OAAA;gBAAO4B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBACpD,eAAA7B,OAAA;kBAAM4B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACRjC,OAAA,CAACnB,gBAAgB;gBACb2D,cAAc,EAAEjC,QAAQ,CAACkC,UAAW;gBACpCL,QAAQ,EAAGM,MAAM,IAAK9B,cAAc,CAAC,YAAY,EAAE8B,MAAM,CAAE;gBAC3DC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAAClC,KAAK,CAAC,WAAW,CAAC,CAAC,GAAGA,KAAK,CAAC,WAAW,CAAC,GAAG,EAAG;gBACrEiB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnC7B,OAAA;cAAA6B,QAAA,gBACI7B,OAAA;gBAAO4B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WACxD,eAAA7B,OAAA;kBAAM4B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACRjC,OAAA,CAACnB,gBAAgB;gBACb2D,cAAc,EAAEjC,QAAQ,CAACuC,KAAM;gBAC/BV,QAAQ,EAAGM,MAAM,IAAK9B,cAAc,CAAC,OAAO,EAAE8B,MAAM,CAAE;gBACtDC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAAClC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAGA,KAAK,CAAC,OAAO,CAAC,GAAG,EAAG;gBAC7DiB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjC,OAAA;cAAA6B,QAAA,gBACI7B,OAAA;gBAAO4B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WACxD,eAAA7B,OAAA;kBAAM4B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACRjC,OAAA,CAACnB,gBAAgB;gBACb2D,cAAc,EAAEjC,QAAQ,CAACwC,IAAK;gBAC9BX,QAAQ,EAAGM,MAAM,IAAK9B,cAAc,CAAC,MAAM,EAAE8B,MAAM,CAAE;gBACrDC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAAClC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAGA,KAAK,CAAC,MAAM,CAAC,GAAG,EAAG;gBAC3DiB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnC7B,OAAA;cAAA6B,QAAA,gBACI7B,OAAA;gBAAO4B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3D7B,OAAA,CAAChB,KAAK;kBAAC4C,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAE7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjC,OAAA;gBACIkC,IAAI,EAAC,QAAQ;gBACbpB,KAAK,EAAEP,QAAQ,CAACyC,YAAY,IAAI,EAAG;gBACnCZ,QAAQ,EAAGC,CAAC,IAAKzB,cAAc,CAAC,cAAc,EAAEyB,CAAC,CAACC,MAAM,CAACxB,KAAK,CAAE;gBAChEc,SAAS,EAAC,kHAAkH;gBAC5HW,WAAW,EAAC;cAAI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjC,OAAA;cAAA6B,QAAA,gBACI7B,OAAA;gBAAO4B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3D7B,OAAA,CAACf,KAAK;kBAAC2C,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oCAE7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjC,OAAA;gBACIkC,IAAI,EAAC,QAAQ;gBACbpB,KAAK,EAAEP,QAAQ,CAAC0C,QAAQ,IAAI,EAAG;gBAC/Bb,QAAQ,EAAGC,CAAC,IAAKzB,cAAc,CAAC,UAAU,EAAEyB,CAAC,CAACC,MAAM,CAACxB,KAAK,CAAE;gBAC5Dc,SAAS,EAAC,kHAAkH;gBAC5HW,WAAW,EAAC;cAAI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAGL1B,QAAQ,CAACkC,UAAU,KAAK,IAAI,iBACzBzC,OAAA;YAAA6B,QAAA,gBACI7B,OAAA;cAAO4B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,gBAC3D7B,OAAA,CAACd,QAAQ;gBAAC0C,SAAS,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjC,OAAA,CAAClB,oBAAoB;cACjB0D,cAAc,EAAEjC,QAAQ,CAAC2C,OAAQ;cACjCd,QAAQ,EAAGM,MAAM,IAAK9B,cAAc,CAAC,SAAS,EAAE8B,MAAM,CAAE;cACxDC,OAAO,EAAEC,KAAK,CAACC,OAAO,CAAClC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAGA,KAAK,CAAC,SAAS,CAAC,GAAG,EAAG;cACjEiB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eACDjC,OAAA;YAAA6B,QAAA,gBACI7B,OAAA;cAAO4B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrFjC,OAAA;cACIc,KAAK,EAAEP,QAAQ,CAAC4C,WAAY;cAC5Bf,QAAQ,EAAGC,CAAC,IAAKzB,cAAc,CAAC,aAAa,EAAEyB,CAAC,CAACC,MAAM,CAACxB,KAAK,CAAE;cAC/Dc,SAAS,EAAC,kHAAkH;cAC5HW,WAAW,EAAC;YAAsC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENjC,OAAA;YAAA6B,QAAA,gBACI7B,OAAA;cAAO4B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7EjC,OAAA;cACIc,KAAK,EAAEP,QAAQ,CAAC6C,WAAW,IAAI,EAAG;cAClChB,QAAQ,EAAGC,CAAC,IAAKzB,cAAc,CAAC,aAAa,EAAEyB,CAAC,CAACC,MAAM,CAACxB,KAAK,CAAE;cAC/DuC,IAAI,EAAE,CAAE;cACRzB,SAAS,EAAC,kHAAkH;cAC5HW,WAAW,EAAC;YAAyB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACpC7B,OAAA;cAAO4B,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC7B,OAAA;gBACIkC,IAAI,EAAC,UAAU;gBACfoB,OAAO,EAAE/C,QAAQ,CAACgD,MAAM,IAAI,KAAM;gBAClCnB,QAAQ,EAAGC,CAAC,IAAKzB,cAAc,CAAC,QAAQ,EAAEyB,CAAC,CAACC,MAAM,CAACgB,OAAO,CAAE;gBAC5D1B,SAAS,EAAC;cAAiE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACFjC,OAAA;gBAAM4B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACRjC,OAAA;cAAO4B,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC7B,OAAA;gBACIkC,IAAI,EAAC,UAAU;gBACfoB,OAAO,EAAE/C,QAAQ,CAACiD,eAAe,IAAI,KAAM;gBAC3CpB,QAAQ,EAAGC,CAAC,IAAKzB,cAAc,CAAC,iBAAiB,EAAEyB,CAAC,CAACC,MAAM,CAACgB,OAAO,CAAE;gBACrE1B,SAAS,EAAC;cAAiE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACFjC,OAAA;gBAAM4B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAChC7B,OAAA;cAAA6B,QAAA,gBACI7B,OAAA;gBAAO4B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3D7B,OAAA,CAACZ,SAAS;kBAACwC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,6BAEjD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjC,OAAA,CAACJ,WAAW;gBACR6D,KAAK,EAAElD,QAAQ,CAACmD,QAAS;gBACzBC,QAAQ,EAAGC,GAAG,IAAKhD,cAAc,CAAC,UAAU,EAAEgD,GAAG,CAAE;gBACnDC,OAAO,EAAC,oBAAoB;gBAC5BC,OAAO,EAAE;cAAK;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjC,OAAA;cAAA6B,QAAA,gBACI7B,OAAA;gBAAO4B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3D7B,OAAA,CAACX,MAAM;kBAACuC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAE9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjC,OAAA,CAACH,SAAS;gBACNkE,MAAM,EAAGC,GAAG,IAAKpD,cAAc,CAAC,SAAS,EAAEoD,GAAG,CAAE;gBAChDC,YAAY,EAAE,KAAM;gBACpBH,OAAO,EAAE;cAAK;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAGA3B,IAAI,KAAK,CAAC,iBACPN,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtB7B,OAAA;YAAK4B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC7B7B,OAAA,CAACV,QAAQ;cAACsC,SAAS,EAAC;YAAoC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DjC,OAAA;cAAI4B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEjC,OAAA;cAAG4B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJjC,OAAA;cAAK4B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB7B,OAAA;gBAAQ4B,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,gBACtH7B,OAAA,CAACN,IAAI;kBAACkC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wCAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjC,OAAA;gBAAQ4B,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,gBACzH7B,OAAA,CAACN,IAAI;kBAACkC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mCAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjC,OAAA;gBAAQ4B,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,gBAC5H7B,OAAA,CAACN,IAAI;kBAACkC,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,8CAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAGA3B,IAAI,KAAK,CAAC,iBACPN,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB7B,OAAA;YAAK4B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC7B7B,OAAA;cAAK4B,SAAS,EAAC,iFAAiF;cAAAC,QAAA,eAC5F7B,OAAA,CAACT,WAAW;gBAACqC,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACNjC,OAAA;cAAI4B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EjC,OAAA;cAAG4B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnC7B,OAAA;cAAI4B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EjC,OAAA;cAAK4B,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC3C7B,OAAA;gBAAA6B,QAAA,gBACI7B,OAAA;kBAAM4B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CjC,OAAA;kBAAM4B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEtB,QAAQ,CAAC4B,IAAI,IAAI;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,eACNjC,OAAA;gBAAA6B,QAAA,gBACI7B,OAAA;kBAAM4B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5CjC,OAAA;kBAAM4B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEtB,QAAQ,CAACkC,UAAU,IAAI;gBAAW;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACNjC,OAAA;gBAAA6B,QAAA,gBACI7B,OAAA;kBAAM4B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CjC,OAAA;kBAAM4B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEtB,QAAQ,CAACuC,KAAK,IAAI;gBAAW;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACNjC,OAAA;gBAAA6B,QAAA,gBACI7B,OAAA;kBAAM4B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CjC,OAAA;kBAAM4B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEtB,QAAQ,CAACwC,IAAI,IAAI;gBAAW;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAGL,CAAC,CAAC1B,QAAQ,CAAC4B,IAAI,IAAI,CAAC5B,QAAQ,CAACkC,UAAU,IAAI,CAAClC,QAAQ,CAACuC,KAAK,IAAI,CAACvC,QAAQ,CAACwC,IAAI,kBACzE/C,OAAA;YAAK4B,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBACxD7B,OAAA;cAAI4B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EjC,OAAA;cAAI4B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAC3C,CAACtB,QAAQ,CAAC4B,IAAI,iBAAInC,OAAA;gBAAA6B,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACvC,CAAC1B,QAAQ,CAACkC,UAAU,iBAAIzC,OAAA;gBAAA6B,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC1C,CAAC1B,QAAQ,CAACuC,KAAK,iBAAI9C,OAAA;gBAAA6B,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACjC,CAAC1B,QAAQ,CAACwC,IAAI,iBAAI/C,OAAA;gBAAA6B,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eAClD7B,OAAA;QAAK4B,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9C7B,OAAA;UACIkE,OAAO,EAAEhD,UAAW;UACpBiD,QAAQ,EAAE7D,IAAI,KAAK,CAAE;UACrBsB,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAEvI7B,OAAA,CAACP,WAAW;YAACmC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER3B,IAAI,GAAG,CAAC,gBACLN,OAAA;UACIkE,OAAO,EAAEjD,UAAW;UACpBW,SAAS,EAAC,8GAA8G;UAAAC,QAAA,GAC3H,gBAEG,eAAA7B,OAAA,CAACR,YAAY;YAACoC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,gBAETjC,OAAA;UACIkE,OAAO,EAAE/C,YAAa;UACtBgD,QAAQ,EAAE3D,OAAO,IAAI,CAACD,QAAQ,CAAC4B,IAAI,IAAI,CAAC5B,QAAQ,CAACkC,UAAU,IAAI,CAAClC,QAAQ,CAACuC,KAAK,IAAI,CAACvC,QAAQ,CAACwC,IAAK;UACjGnB,SAAS,EAAC,oIAAoI;UAAAC,QAAA,EAE7IrB,OAAO,gBACJR,OAAA,CAAAE,SAAA;YAAA2B,QAAA,gBACI7B,OAAA,CAACF,cAAc;cAACsE,IAAI,EAAC;YAAI;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEhC;UAAA,eAAE,CAAC,gBAEHjC,OAAA,CAAAE,SAAA;YAAA2B,QAAA,gBACI7B,OAAA,CAACL,IAAI;cAACiC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAEhC;UAAA,eAAE;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEf,CAAC;AAAC7B,EAAA,CAxVID,WAAW;EAAA,QACI7B,WAAW,EACQC,WAAW,EAC7BA,WAAW;AAAA;AAAA8F,EAAA,GAH3BlE,WAAW;AA0VjB,eAAeA,WAAW;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}