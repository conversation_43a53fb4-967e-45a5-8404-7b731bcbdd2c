import { ChevronLeft, Plus, More<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Trash2, Refresh<PERSON>w } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { fetchExams, uploadExam, resetFilters, resetPagination } from "../../../features/examAI/examAISlice";
import LoadingSpinner from "src/components/loading/LoadingSpinner";
import Header from "src/components/PageAIexam/Header";
import Button from "src/components/PageAIexam/Button";




const AddExamPanel = ({ onClose, search, page, pageSize, sortOrder }) => {
    const dispatch = useDispatch();
    const [file, setFile] = useState(null);
    const { loadingAdd } = useSelector((state) => state.examAI);

    const handleFileChange = (e) => {
        const selected = e.target.files[0];
        if (selected && selected.type === "application/pdf") {
            setFile(selected);
        } else {
            alert("Vui lòng chọn file PDF hợp lệ.");
        }
    };

    const handleUpload = () => {
        if (!file) return alert("Vui lòng chọn file trước khi tạo.");

        dispatch(uploadExam(file))
            .then(() => {
                dispatch(fetchExams({ search, page, pageSize, sortOrder }));
                onClose();
            })
            .catch((err) => {
                console.error(err);
                alert("Tải lên thất bại.");
            });
    };
    const menuRef = useRef();

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                onClose(); // Đóng panel khi click ra ngoài
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    return (
        <div
            ref={menuRef}
            className="fixed top-0 right-0 w-full sm:w-[400px] h-full bg-white border-l shadow-lg z-50 animate-slide-in">
            <div className="flex items-center justify-between p-4 border-b">
                <h3 className="text-lg font-semibold text-gray-800">Thêm đề thi mới</h3>
                <button
                    onClick={onClose}
                    className="text-gray-500 hover:text-gray-700"
                >
                    ✖
                </button>
            </div>

            <div className="p-4 flex flex-col gap-4">
                <div>
                    <p className="text-sm text-gray-600 mb-2">Chọn file đề thi (PDF):</p>
                    <input
                        type="file"
                        accept="application/pdf"
                        onChange={handleFileChange}
                    />
                    {file && (
                        <p className="mt-2 text-green-600 text-sm">Đã chọn: {file.name}</p>
                    )}
                </div>
                <p
                    className="text-sm text-red-500">
                    Đừng spam quá trình tạo có thể mất tới 4-5p sau 5p không thấy đề được tạo thì hãy thử lại</p>
                <button
                    onClick={handleUpload}
                    disabled={!file || loadingAdd}
                    className={`mt-4 px-4 py-2 text-white rounded ${file ? "bg-blue-600 hover:bg-blue-700" : "bg-gray-300 cursor-not-allowed"
                        }`}
                >
                    {loadingAdd ? (
                        <LoadingSpinner
                            minHeight="min-h-0"
                        />
                    ) : (
                        "Tạo đề thi"
                    )}
                </button>
            </div>
        </div>
    );
};

const ExamCard = ({
    exam,
}) => {
    const [menuOpen, setMenuOpen] = useState(false);
    const navigate = useNavigate();
    const handleDelete = () => {
        alert(`Xóa: ${exam.title}`);
        setMenuOpen(false);
    };

    const handleDuplicate = () => {
        alert(`Nhân đôi: ${exam.title}`);
        setMenuOpen(false);
    };

    const handleEdit = () => {
        alert(`Chỉnh sửa: ${exam.title}`);
        setMenuOpen(false);
    };
    const menuRef = useRef();

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setMenuOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    return (
        <div
            className="relative cursor-pointer flex items-center justify-between bg-white shadow-sm rounded-lg border border-gray-200 p-4 hover:shadow-md transition"
            ref={menuRef}
            onClick={() => { navigate(`/admin/AI/exam-management/${exam.id}`) }}
        >
            <div className="flex flex-col">
                <p className="text-base font-medium text-gray-800">{exam.name}</p>
                <p className="text-sm text-gray-500">
                    Ngày tạo: {new Date(exam.createdAt).toLocaleString("vi-VN", { timeZone: "Asia/Ho_Chi_Minh" })}
                </p>
                <p className="text-sm text-gray-500">
                    Ngày cập nhật: {new Date(exam.updatedAt).toLocaleString("vi-VN", { timeZone: "Asia/Ho_Chi_Minh" })}
                </p>
            </div>

            <div className="relative">
                <button
                    onClick={(e) => {
                        e.stopPropagation(); // 👈 chặn sự kiện lan lên thẻ cha
                        setMenuOpen(!menuOpen);
                    }}
                    className="p-2 text-gray-500 hover:text-gray-700"
                >
                    <MoreVertical className="w-5 h-5" />
                </button>

                {menuOpen && (
                    <div className="absolute right-0 top-8 w-40 bg-white border rounded shadow z-10">
                        <button
                            onClick={handleEdit}
                            className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                        >
                            <Pencil className="w-4 h-4 text-gray-600" />
                            Chỉnh sửa
                        </button>
                        <button
                            onClick={handleDuplicate}
                            className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                        >
                            <Copy className="w-4 h-4 text-gray-600" />
                            Nhân đôi
                        </button>
                        <button
                            onClick={handleDelete}
                            className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50"
                        >
                            <Trash2 className="w-4 h-4" />
                            Xóa
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};


const ExamManagement = () => {
    const { exams, pagination, search, loading } = useSelector((state) => state.examAI);
    const { page, pageSize, sortOrder } = pagination;
    // const [file, setFile] = useState(null);
    const dispatch = useDispatch();
    const [showAddPanel, setShowAddPanel] = useState(false);

    const handleRefresh = () => {
        dispatch(resetPagination()); // Reset pagination
        dispatch(resetFilters()); // Reset filters
        dispatch(fetchExams({ search: "", page: 1, pageSize: 10, sortOrder: "DESC" }));
    };

    useEffect(() => {
        // Fetch exams when component mounts
        dispatch(fetchExams({ search, page, pageSize, sortOrder }));
    }, [dispatch, search, page, pageSize, sortOrder]);

    const handleAddExam = () => {
        setShowAddPanel(true); // Mở panel bên phải
    };


    return (
        <div className="w-full h-screen flex flex-col">
            <Header />
            <div className="p-4 bg-gray-50 mt-16 flex-1 overflow-auto">
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold text-gray-700">Danh sách đề thi</h2>

                    <div className="flex items-center gap-2">
                        <Button
                            onClick={handleRefresh}
                            type="primary"
                            p="px-2 py-1"
                        >
                            <RefreshCw className="w-4 h-4" />
                            Làm mới
                        </Button>

                        <Button
                            onClick={handleAddExam}
                            type="primary"
                            p="px-2 py-1"
                        >
                            <Plus className="w-4 h-4" />
                            Thêm đề
                        </Button>
                    </div>
                </div>
                {loading ? (
                    <div className="flex items-center justify-center h-64">
                        <LoadingSpinner
                            size="40"
                            showText={true}
                            text="Đang tải danh sách đề thi..."
                            color="text-gray-500"
                        />
                    </div>
                ) : (
                    <div className="space-y-4">
                        {exams.map((exam) => (
                            <ExamCard
                                key={exam.id}
                                exam={exam}
                            />
                        ))}

                        {exams.length === 0 && (
                            <p className="text-gray-500 text-center">Không có đề thi nào.</p>
                        )}
                    </div>
                )}

            </div>
            {
                showAddPanel && (
                    <AddExamPanel
                        onClose={() => setShowAddPanel(false)}
                        search={search}
                        page={page}
                        pageSize={pageSize}
                        sortOrder={sortOrder}
                    />
                )
            }
        </div >
    );
};


export default ExamManagement;
