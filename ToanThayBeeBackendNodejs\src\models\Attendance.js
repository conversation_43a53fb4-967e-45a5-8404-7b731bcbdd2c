'use strict'
import { Model } from 'sequelize'

export default (sequelize, DataTypes) => {
    class Attendance extends Model {
        /**
         * Define associations
         */
        static associate(models) {
            Attendance.belongsTo(models.User, {
                foreignKey: 'userId',
                as: 'user'
            });
            Attendance.belongsTo(models.Lesson, {
                foreignKey: 'lessonId',
                as: 'lesson'
            });
        }
    }

    Attendance.init({
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        lessonId: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        status: {
            type: DataTypes.ENUM('present', 'absent', 'late'),
            allowNull: false,
            defaultValue: 'absent'
        },
        attendanceTime: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW
        },
        note: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        createdAt: DataTypes.DATE,
        updatedAt: DataTypes.DATE
    }, {
        sequelize,
        modelName: 'Attendance',
        tableName: 'attendance',
        indexes: [
            {
                unique: true,
                fields: ['userId', 'lessonId'],
                name: 'unique_attendance_per_user_per_lesson'
            }
        ]
    });

    return Attendance
}
