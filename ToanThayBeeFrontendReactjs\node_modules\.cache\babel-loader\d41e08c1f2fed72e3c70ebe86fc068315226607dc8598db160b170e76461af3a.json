{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAddExam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { postExam, setExamData } from \"src/features/addExam/addExamSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LeftContent = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    examData\n  } = useSelector(state => state.addExam);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 border-r bg-white h-[calc(100vh-68px)] border-gray-300 overflow-y-auto p-4\",\n    children: \"Left Content\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 9\n  }, this);\n};\n_s(LeftContent, \"rLRAN3FRqlrzGY/YdC5L2ZsGJDw=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = LeftContent;\nexport default LeftContent;\nvar _c;\n$RefreshReg$(_c, \"LeftContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "postExam", "setExamData", "jsxDEV", "_jsxDEV", "LeftContent", "_s", "dispatch", "examData", "state", "addExam", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAddExam/LeftContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { postExam, setExamData } from \"src/features/addExam/addExamSlice\";\r\n\r\n\r\nexport const LeftContent = () => {\r\n    const dispatch = useDispatch();\r\n    const { examData } = useSelector((state) => state.addExam);\r\n\r\n    return (\r\n        <div className=\"flex-1 border-r bg-white h-[calc(100vh-68px)] border-gray-300 overflow-y-auto p-4\">\r\n            Left Content\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default LeftContent;"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,WAAW,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1E,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAS,CAAC,GAAGT,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE1D,oBACIN,OAAA;IAAKO,SAAS,EAAC,mFAAmF;IAAAC,QAAA,EAAC;EAEnG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAEd,CAAC;AAAAV,EAAA,CATYD,WAAW;EAAA,QACHL,WAAW,EACPD,WAAW;AAAA;AAAAkB,EAAA,GAFvBZ,WAAW;AAWxB,eAAeA,WAAW;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}