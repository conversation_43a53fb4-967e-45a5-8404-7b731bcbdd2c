{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\SortableStatementsContainer.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/examAI/examAISlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport LatexRenderer from \"../latex/RenderLatex\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SortableStatementsContainer = _ref => {\n  _s();\n  let {\n    question\n  } = _ref;\n  const dispatch = useDispatch();\n  const {\n    isAddImage\n  } = useSelector(state => state.examAI);\n  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n  const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      const oldIndex = question.statement1s.findIndex((item, idx) => \"\".concat(item.id || idx) === active.id);\n      const newIndex = question.statement1s.findIndex((item, idx) => \"\".concat(item.id || idx) === over.id);\n      if (oldIndex !== -1 && newIndex !== -1) {\n        dispatch(reorderStatements({\n          questionId: question.id,\n          oldIndex,\n          newIndex\n        }));\n      }\n    }\n  };\n  const getPrefix = index => {\n    if (question.typeOfQuestion === \"TN\") {\n      return prefixTN[index] || \"\".concat(String.fromCharCode(65 + index), \".\");\n    } else if (question.typeOfQuestion === \"DS\") {\n      return prefixDS[index] || \"\".concat(String.fromCharCode(97 + index), \")\");\n    }\n    return \"\".concat(index + 1, \".\");\n  };\n  if (question.typeOfQuestion === \"TLN\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-800\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \"\\u0110\\xE1p \\xE1n: \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: question.correctAnswer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this);\n  }\n  if (!question.statement1s || question.statement1s.length === 0) {\n    return null;\n  }\n  if (isAddImage) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-1\",\n      children: question.statement1s.map((item, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-700\",\n          children: getPrefix(idx)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          onDragOver: e => e.preventDefault() // bắt buộc để cho phép drop\n          ,\n          onDrop: e => {\n            e.preventDefault();\n            const draggedImage = e.dataTransfer.getData(\"text/plain\");\n            dispatch(setQuestionsEdited({\n              ...question,\n              solutionImageUrl: draggedImage\n            }));\n            // Bạn có thể xử lý thêm logic ở đây: set state, push vào mảng, v.v.\n          },\n          children: [/*#__PURE__*/_jsxDEV(LatexRenderer, {\n            text: item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 29\n          }, this), item.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.imageUrl,\n              alt: \"statement\",\n              className: \"max-h-32 object-contain rounded-md border border-gray-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 25\n        }, this)]\n      }, \"\".concat(item.id || idx), true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(DndContext, {\n      sensors: sensors,\n      collisionDetection: closestCenter,\n      onDragEnd: handleDragEnd,\n      children: /*#__PURE__*/_jsxDEV(SortableContext, {\n        items: question.statement1s.map((item, idx) => \"\".concat(item.id || idx)),\n        strategy: verticalListSortingStrategy,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1\",\n          children: question.statement1s.map((item, idx) => /*#__PURE__*/_jsxDEV(SortableStatementItem, {\n            statement: item,\n            index: idx,\n            prefix: getPrefix(idx),\n            isCorrect: item.isCorrect,\n            questionType: question.typeOfQuestion\n          }, \"\".concat(item.id || idx), false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 9\n  }, this);\n};\n_s(SortableStatementsContainer, \"Ba0KReOlD6ojHwLmVxD/12/DPAE=\", false, function () {\n  return [useDispatch, useSelector, useSensors, useSensor, useSensor];\n});\n_c = SortableStatementsContainer;\nexport default SortableStatementsContainer;\nvar _c;\n$RefreshReg$(_c, \"SortableStatementsContainer\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "reorderStatements", "SortableStatementItem", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SortableStatementsContainer", "_ref", "_s", "question", "dispatch", "isAddImage", "state", "examAI", "sensors", "coordinateGetter", "prefixTN", "prefixDS", "handleDragEnd", "event", "active", "over", "id", "oldIndex", "statement1s", "findIndex", "item", "idx", "concat", "newIndex", "questionId", "getPrefix", "index", "typeOfQuestion", "String", "fromCharCode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "length", "map", "onDragOver", "e", "preventDefault", "onDrop", "draggedImage", "dataTransfer", "getData", "setQuestionsEdited", "solutionImageUrl", "text", "content", "imageUrl", "src", "alt", "collisionDetection", "onDragEnd", "items", "strategy", "statement", "prefix", "isCorrect", "questionType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/SortableStatementsContainer.jsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { reorderStatements } from \"src/features/examAI/examAISlice\";\nimport SortableStatementItem from \"./SortableStatementItem\";\nimport {\n    DndContext,\n    closestCenter,\n    KeyboardSensor,\n    PointerSensor,\n    useSensor,\n    useSensors,\n} from '@dnd-kit/core';\nimport {\n    SortableContext,\n    sortableKeyboardCoordinates,\n    verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport LatexRenderer from \"../latex/RenderLatex\";\n\nconst SortableStatementsContainer = ({ question }) => {\n    const dispatch = useDispatch();\n    const { isAddImage } = useSelector((state) => state.examAI);\n    const sensors = useSensors(\n        useSensor(PointerSensor),\n        useSensor(KeyboardSensor, {\n            coordinateGetter: sortableKeyboardCoordinates,\n        })\n    );\n\n    const prefixTN = [\"A.\", \"B.\", \"C.\", \"D.\"];\n    const prefixDS = [\"a)\", \"b)\", \"c)\", \"d)\"];\n\n    const handleDragEnd = (event) => {\n        const { active, over } = event;\n\n        if (active.id !== over?.id) {\n            const oldIndex = question.statement1s.findIndex((item, idx) =>\n                `${item.id || idx}` === active.id\n            );\n            const newIndex = question.statement1s.findIndex((item, idx) =>\n                `${item.id || idx}` === over.id\n            );\n\n            if (oldIndex !== -1 && newIndex !== -1) {\n                dispatch(reorderStatements({\n                    questionId: question.id,\n                    oldIndex,\n                    newIndex\n                }));\n            }\n        }\n    };\n\n    const getPrefix = (index) => {\n        if (question.typeOfQuestion === \"TN\") {\n            return prefixTN[index] || `${String.fromCharCode(65 + index)}.`;\n        } else if (question.typeOfQuestion === \"DS\") {\n            return prefixDS[index] || `${String.fromCharCode(97 + index)})`;\n        }\n        return `${index + 1}.`;\n    };\n\n    if (question.typeOfQuestion === \"TLN\") {\n        return (\n            <div className=\"text-gray-800\">\n                <span className=\"font-semibold\">Đáp án: </span>\n                <span>{question.correctAnswer}</span>\n            </div>\n        );\n    }\n\n    if (!question.statement1s || question.statement1s.length === 0) {\n        return null;\n    }\n\n    if (isAddImage) {\n        return (\n            <div className=\"space-y-1\">\n                {question.statement1s.map((item, idx) => (\n                    <div key={`${item.id || idx}`} className=\"flex items-center gap-2\">\n                        <span className=\"font-medium text-gray-700\">{getPrefix(idx)}</span>\n                        <div className=\"flex-1\"\n                            onDragOver={(e) => e.preventDefault()} // bắt buộc để cho phép drop\n                            onDrop={(e) => {\n                                e.preventDefault();\n                                const draggedImage = e.dataTransfer.getData(\"text/plain\");\n                                dispatch(setQuestionsEdited({ ...question, solutionImageUrl: draggedImage }))\n                                // Bạn có thể xử lý thêm logic ở đây: set state, push vào mảng, v.v.\n                            }}\n                        >\n                            <LatexRenderer text={item.content} />\n                            {item.imageUrl && (\n                                <div className=\"mt-2\">\n                                    <img\n                                        src={item.imageUrl}\n                                        alt=\"statement\"\n                                        className=\"max-h-32 object-contain rounded-md border border-gray-200\"\n                                    />\n                                </div>\n                            )}\n                        </div>\n                    </div>\n                ))}\n            </div>\n        );\n    }\n\n    return (\n        <div>\n            <DndContext\n                sensors={sensors}\n                collisionDetection={closestCenter}\n                onDragEnd={handleDragEnd}\n            >\n                <SortableContext\n                    items={question.statement1s.map((item, idx) => `${item.id || idx}`)}\n                    strategy={verticalListSortingStrategy}\n                >\n                    <div className=\"space-y-1\">\n                        {question.statement1s.map((item, idx) => (\n                            <SortableStatementItem\n                                key={`${item.id || idx}`}\n                                statement={item}\n                                index={idx}\n                                prefix={getPrefix(idx)}\n                                isCorrect={item.isCorrect}\n                                questionType={question.typeOfQuestion}\n                            />\n                        ))}\n                    </div>\n                </SortableContext>\n            </DndContext>\n        </div>\n    );\n};\n\nexport default SortableStatementsContainer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAC1B,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,2BAA2B,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC7C,MAAMG,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqB;EAAW,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAC3D,MAAMC,OAAO,GAAGf,UAAU,CACtBD,SAAS,CAACD,aAAa,CAAC,EACxBC,SAAS,CAACF,cAAc,EAAE;IACtBmB,gBAAgB,EAAEd;EACtB,CAAC,CACL,CAAC;EAED,MAAMe,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxB,MAAMC,QAAQ,GAAGd,QAAQ,CAACe,WAAW,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACtD,GAAAC,MAAA,CAAGF,IAAI,CAACJ,EAAE,IAAIK,GAAG,MAAOP,MAAM,CAACE,EACnC,CAAC;MACD,MAAMO,QAAQ,GAAGpB,QAAQ,CAACe,WAAW,CAACC,SAAS,CAAC,CAACC,IAAI,EAAEC,GAAG,KACtD,GAAAC,MAAA,CAAGF,IAAI,CAACJ,EAAE,IAAIK,GAAG,MAAON,IAAI,CAACC,EACjC,CAAC;MAED,IAAIC,QAAQ,KAAK,CAAC,CAAC,IAAIM,QAAQ,KAAK,CAAC,CAAC,EAAE;QACpCnB,QAAQ,CAAClB,iBAAiB,CAAC;UACvBsC,UAAU,EAAErB,QAAQ,CAACa,EAAE;UACvBC,QAAQ;UACRM;QACJ,CAAC,CAAC,CAAC;MACP;IACJ;EACJ,CAAC;EAED,MAAME,SAAS,GAAIC,KAAK,IAAK;IACzB,IAAIvB,QAAQ,CAACwB,cAAc,KAAK,IAAI,EAAE;MAClC,OAAOjB,QAAQ,CAACgB,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE,CAAC,MAAM,IAAIvB,QAAQ,CAACwB,cAAc,KAAK,IAAI,EAAE;MACzC,OAAOhB,QAAQ,CAACe,KAAK,CAAC,OAAAJ,MAAA,CAAOM,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,KAAK,CAAC,MAAG;IACnE;IACA,UAAAJ,MAAA,CAAUI,KAAK,GAAG,CAAC;EACvB,CAAC;EAED,IAAIvB,QAAQ,CAACwB,cAAc,KAAK,KAAK,EAAE;IACnC,oBACI5B,OAAA;MAAK+B,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BhC,OAAA;QAAM+B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CpC,OAAA;QAAAgC,QAAA,EAAO5B,QAAQ,CAACiC;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEd;EAEA,IAAI,CAAChC,QAAQ,CAACe,WAAW,IAAIf,QAAQ,CAACe,WAAW,CAACmB,MAAM,KAAK,CAAC,EAAE;IAC5D,OAAO,IAAI;EACf;EAEA,IAAIhC,UAAU,EAAE;IACZ,oBACIN,OAAA;MAAK+B,SAAS,EAAC,WAAW;MAAAC,QAAA,EACrB5B,QAAQ,CAACe,WAAW,CAACoB,GAAG,CAAC,CAAClB,IAAI,EAAEC,GAAG,kBAChCtB,OAAA;QAA+B+B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBAC9DhC,OAAA;UAAM+B,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEN,SAAS,CAACJ,GAAG;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnEpC,OAAA;UAAK+B,SAAS,EAAC,QAAQ;UACnBS,UAAU,EAAGC,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE,CAAC;UAAA;UACvCC,MAAM,EAAGF,CAAC,IAAK;YACXA,CAAC,CAACC,cAAc,CAAC,CAAC;YAClB,MAAME,YAAY,GAAGH,CAAC,CAACI,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;YACzDzC,QAAQ,CAAC0C,kBAAkB,CAAC;cAAE,GAAG3C,QAAQ;cAAE4C,gBAAgB,EAAEJ;YAAa,CAAC,CAAC,CAAC;YAC7E;UACJ,CAAE;UAAAZ,QAAA,gBAEFhC,OAAA,CAACF,aAAa;YAACmD,IAAI,EAAE5B,IAAI,CAAC6B;UAAQ;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACpCf,IAAI,CAAC8B,QAAQ,iBACVnD,OAAA;YAAK+B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACjBhC,OAAA;cACIoD,GAAG,EAAE/B,IAAI,CAAC8B,QAAS;cACnBE,GAAG,EAAC,WAAW;cACftB,SAAS,EAAC;YAA2D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA,MAAAb,MAAA,CArBGF,IAAI,CAACJ,EAAE,IAAIK,GAAG;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsBtB,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEd;EAEA,oBACIpC,OAAA;IAAAgC,QAAA,eACIhC,OAAA,CAACX,UAAU;MACPoB,OAAO,EAAEA,OAAQ;MACjB6C,kBAAkB,EAAEhE,aAAc;MAClCiE,SAAS,EAAE1C,aAAc;MAAAmB,QAAA,eAEzBhC,OAAA,CAACL,eAAe;QACZ6D,KAAK,EAAEpD,QAAQ,CAACe,WAAW,CAACoB,GAAG,CAAC,CAAClB,IAAI,EAAEC,GAAG,QAAAC,MAAA,CAAQF,IAAI,CAACJ,EAAE,IAAIK,GAAG,CAAE,CAAE;QACpEmC,QAAQ,EAAE5D,2BAA4B;QAAAmC,QAAA,eAEtChC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,EACrB5B,QAAQ,CAACe,WAAW,CAACoB,GAAG,CAAC,CAAClB,IAAI,EAAEC,GAAG,kBAChCtB,OAAA,CAACZ,qBAAqB;YAElBsE,SAAS,EAAErC,IAAK;YAChBM,KAAK,EAAEL,GAAI;YACXqC,MAAM,EAAEjC,SAAS,CAACJ,GAAG,CAAE;YACvBsC,SAAS,EAAEvC,IAAI,CAACuC,SAAU;YAC1BC,YAAY,EAAEzD,QAAQ,CAACwB;UAAe,MAAAL,MAAA,CAL9BF,IAAI,CAACJ,EAAE,IAAIK,GAAG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMzB,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEd,CAAC;AAACjC,EAAA,CAnHIF,2BAA2B;EAAA,QACZhB,WAAW,EACLC,WAAW,EAClBQ,UAAU,EACtBD,SAAS,EACTA,SAAS;AAAA;AAAAqE,EAAA,GALX7D,2BAA2B;AAqHjC,eAAeA,2BAA2B;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}