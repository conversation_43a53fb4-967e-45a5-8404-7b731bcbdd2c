'use strict';
/** @type {import('sequelize-cli').Migration} */
export default {
    async up(queryInterface, Sequelize) {
        // Add new columns
        await queryInterface.addColumn('class', 'startTime1', {
            type: Sequelize.TIME,
            allowNull: true,
            comment: 'Class start time',
            after: 'dayOfWeek'
        });

        await queryInterface.addColumn('class', 'endTime1', {
            type: Sequelize.TIME,
            allowNull: true,
            comment: 'Class end time',
            after: 'startTime1'
        });

        await queryInterface.addColumn('class', 'startTime2', {
            type: Sequelize.TIME,
            allowNull: true,
            comment: 'Class start time',
            after: 'dayOfWeek'
        });

        await queryInterface.addColumn('class', 'endTime2', {
            type: Sequelize.TIME,
            allowNull: true,
            comment: 'Class end time',
            after: 'startTime2'
        });
        // Remove columns
    },

    async down(queryInterface, Sequelize) {
        // Add back removed columns

        // Remove added columns
        await queryInterface.removeColumn('class', 'startTime1');
        await queryInterface.removeColumn('class', 'endTime1');
        await queryInterface.removeColumn('class', 'startTime2');
        await queryInterface.removeColumn('class', 'endTime2');
    }
};
