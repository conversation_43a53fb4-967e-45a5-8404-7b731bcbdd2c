{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PageAIexam\\\\LeftContent.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { reorderQuestions } from \"src/features/examAI/examAISlice\";\nimport LoadingData from \"../loading/LoadingData\";\nimport SortableQuestionItem from \"./SortableQuestionItem\";\nimport { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';\nimport { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const EditQuestionView = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    questionsEdited,\n    loading\n  } = useSelector(state => state.examAI);\n  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor, {\n    coordinateGetter: sortableKeyboardCoordinates\n  }));\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    if (active.id !== (over === null || over === void 0 ? void 0 : over.id)) {\n      dispatch(reorderQuestions({\n        activeId: active.id,\n        overId: over.id\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(LoadingData, {\n    loading: loading,\n    loadText: \"\\u0110ang t\\u1EA3i danh s\\xE1ch c\\xE2u h\\u1ECFi...\",\n    isNoData: questionsEdited.length > 0 ? false : true,\n    noDataText: \"Kh\\xF4ng c\\xF3 c\\xE2u h\\u1ECFi n\\xE0o.\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold text-gray-800 mb-4\",\n      children: \"Danh s\\xE1ch c\\xE2u h\\u1ECFi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: questionsEdited.length > 0 ? /*#__PURE__*/_jsxDEV(DndContext, {\n        sensors: sensors,\n        collisionDetection: closestCenter,\n        onDragEnd: handleDragEnd,\n        children: /*#__PURE__*/_jsxDEV(SortableContext, {\n          items: questionsEdited.map(q => q.id),\n          strategy: verticalListSortingStrategy,\n          children: questionsEdited.map((q, index) => /*#__PURE__*/_jsxDEV(SortableQuestionItem, {\n            question: q,\n            index: index\n          }, q.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 c\\xE2u h\\u1ECFi n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 9\n  }, this);\n};\n_s(EditQuestionView, \"OnJVrBptPmCkljyQk9X+uGRu22c=\", false, function () {\n  return [useDispatch, useSelector, useSensors, useSensor, useSensor];\n});\n_c = EditQuestionView;\nexport const LeftContent = () => {\n  _s2();\n  const {\n    viewEdit\n  } = useSelector(state => state.examAI);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-2/3 border-r border-gray-300 overflow-y-auto p-4\",\n    children: viewEdit === 'question' && /*#__PURE__*/_jsxDEV(EditQuestionView, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 41\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 9\n  }, this);\n};\n_s2(LeftContent, \"plqPiIWI8H/U9YD7djE1+JqN1rI=\", false, function () {\n  return [useSelector];\n});\n_c2 = LeftContent;\nexport default LeftContent;\nvar _c, _c2;\n$RefreshReg$(_c, \"EditQuestionView\");\n$RefreshReg$(_c2, \"LeftContent\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "reorderQuestions", "LoadingData", "SortableQuestionItem", "DndContext", "closestCenter", "KeyboardSensor", "PointerSensor", "useSensor", "useSensors", "SortableContext", "sortableKeyboardCoordinates", "verticalListSortingStrategy", "jsxDEV", "_jsxDEV", "EditQuestionView", "_s", "dispatch", "questionsEdited", "loading", "state", "examAI", "sensors", "coordinateGetter", "handleDragEnd", "event", "active", "over", "id", "activeId", "overId", "loadText", "isNoData", "length", "noDataText", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "collisionDetection", "onDragEnd", "items", "map", "q", "strategy", "index", "question", "_c", "LeftContent", "_s2", "viewEdit", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PageAIexam/LeftContent.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { reorderQuestions } from \"src/features/examAI/examAISlice\";\r\nimport LoadingData from \"../loading/LoadingData\";\r\nimport SortableQuestionItem from \"./SortableQuestionItem\";\r\nimport {\r\n    DndContext,\r\n    closestCenter,\r\n    KeyboardSensor,\r\n    PointerSensor,\r\n    useSensor,\r\n    useSensors,\r\n} from '@dnd-kit/core';\r\nimport {\r\n    SortableContext,\r\n    sortableKeyboardCoordinates,\r\n    verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\n\r\nexport const EditQuestionView = () => {\r\n    const dispatch = useDispatch();\r\n    const { questionsEdited, loading } = useSelector((state) => state.examAI);\r\n\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor),\r\n        useSensor(KeyboardSensor, {\r\n            coordinateGetter: sortableKeyboardCoordinates,\r\n        })\r\n    );\r\n\r\n    const handleDragEnd = (event) => {\r\n        const { active, over } = event;\r\n\r\n        if (active.id !== over?.id) {\r\n            dispatch(reorderQuestions({\r\n                activeId: active.id,\r\n                overId: over.id\r\n            }));\r\n        }\r\n    };\r\n\r\n    return (\r\n        <LoadingData loading={loading} loadText=\"Đang tải danh sách câu hỏi...\" isNoData={questionsEdited.length > 0 ? false : true} noDataText=\"Không có câu hỏi nào.\">\r\n            <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Danh sách câu hỏi</h2>\r\n            <div className=\"space-y-3\">\r\n                {questionsEdited.length > 0 ? (\r\n                    <DndContext\r\n                        sensors={sensors}\r\n                        collisionDetection={closestCenter}\r\n                        onDragEnd={handleDragEnd}\r\n                    >\r\n                        <SortableContext\r\n                            items={questionsEdited.map(q => q.id)}\r\n                            strategy={verticalListSortingStrategy}\r\n                        >\r\n                            {questionsEdited.map((q, index) => (\r\n                                <SortableQuestionItem\r\n                                    key={q.id}\r\n                                    question={q}\r\n                                    index={index}\r\n                                />\r\n                            ))}\r\n                        </SortableContext>\r\n                    </DndContext>\r\n                ) : (\r\n                    <p className=\"text-gray-500\">Không có câu hỏi nào.</p>\r\n                )}\r\n            </div>\r\n        </LoadingData>\r\n    )\r\n}\r\n\r\nexport const LeftContent = () => {\r\n    const { viewEdit } = useSelector((state) => state.examAI);\r\n\r\n    return (\r\n        <div className=\"w-2/3 border-r border-gray-300 overflow-y-auto p-4\">\r\n            {viewEdit === 'question' && <EditQuestionView />}\r\n        </div>\r\n    )\r\n\r\n}\r\nexport default LeftContent;"], "mappings": ";;;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SACIC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,2BAA2B,QACxB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB,eAAe;IAAEC;EAAQ,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEzE,MAAMC,OAAO,GAAGb,UAAU,CACtBD,SAAS,CAACD,aAAa,CAAC,EACxBC,SAAS,CAACF,cAAc,EAAE;IACtBiB,gBAAgB,EAAEZ;EACtB,CAAC,CACL,CAAC;EAED,MAAMa,aAAa,GAAIC,KAAK,IAAK;IAC7B,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,KAAK;IAE9B,IAAIC,MAAM,CAACE,EAAE,MAAKD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,EAAE,GAAE;MACxBX,QAAQ,CAAChB,gBAAgB,CAAC;QACtB4B,QAAQ,EAAEH,MAAM,CAACE,EAAE;QACnBE,MAAM,EAAEH,IAAI,CAACC;MACjB,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAED,oBACId,OAAA,CAACZ,WAAW;IAACiB,OAAO,EAAEA,OAAQ;IAACY,QAAQ,EAAC,oDAA+B;IAACC,QAAQ,EAAEd,eAAe,CAACe,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,IAAK;IAACC,UAAU,EAAC,wCAAuB;IAAAC,QAAA,gBAC3JrB,OAAA;MAAIsB,SAAS,EAAC,0CAA0C;MAAAD,QAAA,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/E1B,OAAA;MAAKsB,SAAS,EAAC,WAAW;MAAAD,QAAA,EACrBjB,eAAe,CAACe,MAAM,GAAG,CAAC,gBACvBnB,OAAA,CAACV,UAAU;QACPkB,OAAO,EAAEA,OAAQ;QACjBmB,kBAAkB,EAAEpC,aAAc;QAClCqC,SAAS,EAAElB,aAAc;QAAAW,QAAA,eAEzBrB,OAAA,CAACJ,eAAe;UACZiC,KAAK,EAAEzB,eAAe,CAAC0B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,EAAE,CAAE;UACtCkB,QAAQ,EAAElC,2BAA4B;UAAAuB,QAAA,EAErCjB,eAAe,CAAC0B,GAAG,CAAC,CAACC,CAAC,EAAEE,KAAK,kBAC1BjC,OAAA,CAACX,oBAAoB;YAEjB6C,QAAQ,EAAEH,CAAE;YACZE,KAAK,EAAEA;UAAM,GAFRF,CAAC,CAACjB,EAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAEb1B,OAAA;QAAGsB,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IACxD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAAxB,EAAA,CAnDYD,gBAAgB;EAAA,QACRf,WAAW,EACSD,WAAW,EAEhCU,UAAU,EACtBD,SAAS,EACTA,SAAS;AAAA;AAAAyC,EAAA,GANJlC,gBAAgB;AAqD7B,OAAO,MAAMmC,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAM;IAAEC;EAAS,CAAC,GAAGrD,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEzD,oBACIP,OAAA;IAAKsB,SAAS,EAAC,oDAAoD;IAAAD,QAAA,EAC9DiB,QAAQ,KAAK,UAAU,iBAAItC,OAAA,CAACC,gBAAgB;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC;AAGd,CAAC;AAAAW,GAAA,CATYD,WAAW;EAAA,QACCnD,WAAW;AAAA;AAAAsD,GAAA,GADvBH,WAAW;AAUxB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}