import React, { useState } from "react";
import { Trash2 } from "lucide-react"; // Lucide icon
import LatexRenderer from "../latex/RenderLatex";
const ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove, content }) => {
    const [isDraggingOver, setIsDraggingOver] = useState(false);
    const [isHovering, setIsHovering] = useState(false);

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDraggingOver(false);

        const draggedImage = e.dataTransfer.getData("text/plain"); // Expects base64 or URL
        if (draggedImage && onImageDrop) {
            onImageDrop(draggedImage);
        }
    };

    return (
        <div className="flex flex-col gap-2 w-full">
            <div
                className={`relative flex items-center  rounded-md p-4 cursor-pointer
        ${isDraggingOver || isHovering ? "border border-dashed border-sky-500 bg-sky-50" : ""}`}
                onDragOver={(e) => e.preventDefault()}
                onDragEnter={() => setIsDraggingOver(true)}
                onDragLeave={() => setIsDraggingOver(false)}
                onDrop={(e) => {
                    setIsDraggingOver(false); // reset lại trạng thái sau khi drop
                    handleDrop(e);
                }}
                onMouseEnter={() => setIsHovering(true)}
                onMouseLeave={() => setIsHovering(false)}
            >
                {isDraggingOver || isHovering ? (
                    <p className="text-gray-500 text-sm text-center">Thêm ảnh</p>
                ) : (
                    <LatexRenderer text={content} />
                )}
            </div>
            {imageUrl && (
                <div className="relative w-fit group/image">
                    <img
                        src={imageUrl}
                        alt="dropped"
                        className="rounded-md max-h-40 object-contain w-full border transition-all group-hover/image:brightness-75"
                    />
                    <button
                        className="absolute inset-0 flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20 rounded-md"
                        onClick={(e) => {
                            e.stopPropagation();
                            onImageRemove?.();
                        }}
                        title="Xóa ảnh"
                    >
                        <Trash2 className="w-6 h-6 text-white bg-red-500 p-1 rounded-full group-hover/image:opacity-100" />
                    </button>
                </div>
            )}
        </div>
    );
};

export default ImageDropZone;
