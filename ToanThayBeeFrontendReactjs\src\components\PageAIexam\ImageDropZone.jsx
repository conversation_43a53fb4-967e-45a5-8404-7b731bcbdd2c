import React, { useState } from "react";
import { Trash2, ImagePlus, Upload } from "lucide-react";
import LatexRenderer from "../latex/RenderLatex";

const ImageDropZone = ({ imageUrl, onImageDrop, onImageRemove, content }) => {
    const [isDraggingOver, setIsDraggingOver] = useState(false);

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDraggingOver(false);

        const draggedImage = e.dataTransfer.getData("text/plain");
        if (draggedImage && onImageDrop) {
            onImageDrop(draggedImage);
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.dataTransfer.types.includes('text/plain')) {
            setIsDraggingOver(true);
        }
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        // Chỉ tắt khi thực sự rời khỏi drop zone
        if (!e.currentTarget.contains(e.relatedTarget)) {
            setIsDraggingOver(false);
        }
    };

    return (
        <div className="w-full space-y-3">
            {/* Content với drop zone */}
            <div
                className={`relative rounded-lg p-4 transition-all duration-200 min-h-[60px] flex items-center
                    ${isDraggingOver
                        ? "border-2 border-dashed border-blue-400 bg-blue-50"
                        : "border border-gray-200 hover:border-blue-300 hover:bg-blue-25"
                    }`}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
            >
                {isDraggingOver ? (
                    <div className="w-full text-center py-4">
                        <Upload className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                        <p className="text-blue-600 font-medium">Thả ảnh vào đây</p>
                        <p className="text-blue-400 text-sm">Kéo ảnh từ danh sách bên phải</p>
                    </div>
                ) : (
                    <div className="w-full">
                        <LatexRenderer text={content} />
                        {!imageUrl && (
                            <div className="mt-2 flex items-center gap-2 text-gray-400 text-sm">
                                <ImagePlus className="w-4 h-4" />
                                <span>Kéo ảnh vào đây để thêm</span>
                            </div>
                        )}
                    </div>
                )}
            </div>

            {/* Hiển thị ảnh đã thêm */}
            {imageUrl && (
                <div className="relative group w-fit bg-gray-50 rounded-lg p-2">
                    <img
                        src={imageUrl}
                        alt="Attached image"
                        className="rounded-md max-h-48 max-w-full object-contain"
                    />
                    <button
                        className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        onClick={(e) => {
                            e.stopPropagation();
                            onImageRemove?.();
                        }}
                        title="Xóa ảnh"
                    >
                        <div className="bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg">
                            <Trash2 className="w-4 h-4" />
                        </div>
                    </button>
                    <div className="mt-2 text-xs text-gray-500 text-center">
                        Ảnh đã thêm • Click vào icon để xóa
                    </div>
                </div>
            )}
        </div>
    );
};

export default ImageDropZone;
