import UserLayout from "../../../layouts/UserLayout";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchPublicExamById, saveExamForUser, fetchRelatedExamsIfNeeded } from "../../../features/exam/examDetailSlice";
import { fetchCodesByType } from "../../../features/code/codeSlice";
import { useEffect, useState, useRef } from "react";
import {
    StarIcon,
    Info,
    RefreshCcw,
    BookOpen,
    Calendar,
    Award,
    FileText,
    BarChart2,
    History,
    QrCode as QrCodeIcon,
    Pin,
    Eye,
    Play,
    Clock,
    GraduationCap,
    ListOrdered,
    StickyNote,
    ExternalLink,
    Send,
    Smile
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { setView } from "../../../features/exam/examDetailSlice";
import { formatDate } from "src/utils/formatters";
import QRCodeComponent from "src/components/QrCode";
import YouTubePlayer from "src/components/YouTubePlayer";
import { fetchCommentsByExamId, postComment, putComment, deleteComment, setCurrentPage } from "src/features/comments/ExamCommentsSlice";
import CommentSection from "src/components/comment/CommentSection";
import LoadingText from "src/components/loading/LoadingText";

const ActionButton = ({ icon: Icon, title, shortTitle, isActive = false, onClick }) => {
    return (
        <button
            onClick={onClick}
            className={`w-fit px-2 py-[7px] rounded-md text-xs font-medium transition-colors whitespace-nowrap
                ${isActive
                    ? 'bg-sky-100 text-sky-700 border border-sky-300'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}
            `}
        >
            <Icon className="w-4 h-4 inline mr-1 lg:mr-2" />
            <span className="hidden sm:inline">{title}</span>
            <span className="sm:hidden">{shortTitle}</span>
        </button>
    );
};

const ActionButtons = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { view, exam } = useSelector((state) => state.examDetail);

    const handleClickRanking = () => {
        // Cho phép xem BXH nếu:
        // 1. Đã làm bài và được phép xem đáp án, HOẶC
        // 2. Không cho phép làm bài nữa (hết thời gian)
        if ((exam?.isDone && exam?.seeCorrectAnswer) || (!exam?.acceptDoExam && exam?.seeCorrectAnswer)) {
            navigate(`/practice/exam/${exam.id}/ranking`);
        }

    }

    const handleClickPreviewExam = () => {
        // Cho phép xem đề thi nếu:
        // 1. Đã làm bài và được phép xem đáp án, HOẶC
        // 2. Không cho phép làm bài nữa (hết thời gian)
        if ((exam?.isDone && exam?.seeCorrectAnswer) || (!exam?.acceptDoExam && exam?.seeCorrectAnswer)) {
            navigate(`/practice/exam/${exam.id}/preview`);
        }
    };
    const handleClickHistory = () => {
        if (!exam?.isDone || !exam?.seeCorrectAnswer) return
        navigate(`/practice/exam/${exam.id}/history`)
    };
    return (
        <div className="flex flex-wrap gap-2">
            <ActionButton
                icon={Info}
                title="Thông tin chi tiết"
                shortTitle="Chi tiết"
                isActive={view === "detail"}
                onClick={() => dispatch(setView("detail"))}
            />
            <ActionButton
                icon={Award}
                title="Bảng xếp hạng"
                shortTitle="Xếp hạng"
                isActive={view === "ranking"}
                // onClick={() => dispatch(setView("ranking"))}
                onClick={handleClickRanking}
            />
            <ActionButton
                icon={Eye}
                title="Xem đề thi"
                shortTitle="Xem đề"
                isActive={view === "preview"}
                // onClick={() => dispatch(setView("preview"))}
                onClick={handleClickPreviewExam}
            />
            <ActionButton
                icon={History}
                title="Lịch sử làm bài"
                shortTitle="Lịch sử"
                isActive={view === "history"}
                // onClick={() => dispatch(setView("history"))}
                onClick={handleClickHistory}
            />
        </div>
    );
};

const DoExamButton = ({ onClick, disabled = false }) => {
    const { loading } = useSelector((state) => state.states);
    return (
        <LoadingText loading={loading} w="w-20" h="h-[30px]">
            <button
                onClick={onClick}
                disabled={disabled}
                className={`flex-shrink-0 flex items-center gap-2 px-3 py-[7px] rounded-md text-xs font-medium transition-all
                ${disabled
                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        : 'bg-green-600 text-white hover:bg-green-700'}
            `}
            >
                <Play size={16} />
                <span>Làm bài</span>
            </button>
        </LoadingText>

    );
};

const InfoRow = ({ icon: Icon, label, value, w = "w-48" }) => {
    const { loading } = useSelector((state) => state.states);
    return (
        <div className="flex items-center justify-between gap-2 text-sm text-gray-800">
            <div className="flex items-center gap-2">
                <Icon size={16} className="text-sky-600" />
                <span className="font-medium text-gray-800">{label}:</span>
            </div>
            <LoadingText loading={loading} w={w}>
                <span>{value}</span>
            </LoadingText>
        </div>
    );
};

const InfoExam = () => {
    const { exam, loading } = useSelector((state) => state.examDetail);
    const { codes } = useSelector((state) => state.codes);
    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            {/** Header **/}
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <LoadingText loading={loading} w="w-48">
                    <p className="font-Inter text-sm font-semibold">{exam?.name}</p>
                </LoadingText>
                <div className="flex items-center gap-2 text-gray-500">
                    <RefreshCcw size={16} className="flex-shrink-0" />
                    <LoadingText loading={loading} w="w-40" color="bg-gray-200">
                        <p className="text-xs"><span className="hidden sm:inline">Cập nhật:</span> {formatDate(exam?.updatedAt)}</p>
                    </LoadingText>
                </div>
            </div>
            <div className="flex flex-row gap-3 p-4 bg-white ">
                <div className="w-full flex flex-col sm:flex-row gap-4 items-center">
                    {/* Bên trái: ảnh hoặc QR */}
                    <div className="w-full sm:w-1/3 flex justify-center items-center">
                        {exam?.imageUrl ? (
                            <img
                                src={exam.imageUrl}
                                alt="Exam"
                                className="w-full max-w-[200px] rounded-md shadow border"
                            />
                        ) : (
                            <div className="bg-white p-2 rounded-md border">
                                <QRCodeComponent
                                    url={`https://toanthaybee.edu.vn/practice/exam/${exam?.id}`} // hoặc `https://toanthaybee.edu.vn/practice/exam/${exam.id}` hoặc `https://toanthaybee.edu.vn/practice/exam/${exam.id}`
                                    size={128}
                                />
                            </div>
                        )}
                    </div>

                    {/* Bên phải: thông tin */}
                    <div className="w-full flex flex-col sm:gap-4 gap-2">
                        <InfoRow
                            icon={BookOpen}
                            label="Loại đề"
                            value={codes?.["exam type"]?.find(c => c.code === exam?.typeOfExam)?.description || exam?.typeOfExam || "Không rõ"}
                            w="w-56"
                        />
                        <InfoRow
                            icon={GraduationCap}
                            label="Khối"
                            value={exam?.class || "Không rõ"}
                            w="w-48"
                        />
                        <InfoRow
                            icon={Calendar}
                            label="Năm"
                            value={exam?.year || "Không rõ"}
                            w="w-40"
                        />
                        <InfoRow
                            icon={Clock}
                            label="Thời gian"
                            value={exam?.testDuration ? `${exam.testDuration} phút` : "Vô thời hạn"}
                            w="w-56"
                        />
                        <InfoRow
                            icon={ListOrdered}
                            label="Chương"
                            value={codes?.["chapter"]?.find(c => c.code === exam?.chapter)?.description || exam?.chapter || "Không rõ"}
                            w="w-56"
                        />
                        <InfoRow
                            icon={BarChart2}
                            label="Tỉ lệ đạt"
                            value={exam?.passRate ? `${exam.passRate}%` : "Không có"}
                            w="w-48"
                        />
                    </div>
                </div>

                {/* Mô tả */}
                {exam?.description && (
                    <div className="flex items-start gap-2 text-sm text-gray-600">
                        <StickyNote size={16} className="text-sky-600 mt-0.5" />
                        <span className="font-medium text-gray-800">Mô tả:</span>
                        <span className="leading-relaxed">{exam.description}</span>
                    </div>
                )}
            </div>

        </div>
    )
}

const SolutionVideo = () => {
    const { exam, loading } = useSelector((state) => state.examDetail);

    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <p className="text-sm font-Inter font-semibold">Video Chữa bài</p>
                <LoadingText loading={loading} w="w-40">
                    {exam?.isDone ? (
                        <button
                            onClick={() => window.open(exam?.solutionUrl, "_blank")}
                            className="text-xs flex items-center gap-2 px-2 py-1 rounded-md transition-all text-gray-700 hover:bg-gray-200"
                        >
                            <ExternalLink size={16} />
                            <span>Mở trong tab mới</span>
                        </button>
                    ) : (
                        <button
                            className="text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 cursor-not-allowed"
                        >
                            <span>Chưa làm</span>
                        </button>
                    )}
                </LoadingText>

            </div>
            {loading && (<div className="p-4 w-full h-[436px]">
                <LoadingText loading={loading} w="w-full" h="h-full" rounded="rounded-md" />
            </div>)}

            {!loading && (
                <>
                    {exam?.isDone && exam?.solutionUrl && (
                        <div className="p-4">
                            <YouTubePlayer url={exam?.solutionUrl} />
                        </div>
                    )}
                    {exam?.isDone && !exam?.solutionUrl && (
                        <div className="p-4">
                            <p className="text-sm text-gray-600">Chưa có video hướng dẫn</p>
                        </div>
                    )}
                    {!exam?.isDone && (
                        <div className="p-4">
                            <p className="text-sm text-gray-600">Chỉ có thể xem sau khi làm bài</p>
                        </div>
                    )}
                </>
            )}


        </div>
    )
}

const RelatedExamCardSkeleton = () => {
    return (
        <div className="bg-white rounded-md border border-gray-200 p-3 sm:p-4 flex flex-col gap-2 animate-pulse">
            <div className="flex justify-between items-center">
                <LoadingText loading={true} w="w-40" />
                <div className="w-20 h-6 rounded-md bg-gray-200" />
            </div>

            <div className="flex items-center gap-2 text-xs text-gray-500">
                <Clock size={14} />
                <LoadingText loading={true} w="w-20" h="h-3" />
            </div>

            <div className="flex items-center gap-2 text-xs text-gray-500">
                <Calendar size={14} />
                <LoadingText loading={true} w="w-24" h="h-3" />
            </div>
        </div>
    );
};


const RelatedExamCard = ({ exam, onClick, compact = false }) => {
    return (
        <div
            onClick={onClick}
            className="bg-white rounded-md border border-gray-200 cursor-pointer p-3 sm:p-4 flex flex-col gap-2"
        >
            <div className="flex justify-between items-center">
                <p className="text-sm font-semibold text-gray-800 line-clamp-2">
                    {exam.name}
                </p>
                <button
                    onClick={onClick}
                    className="whitespace-nowrap text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200"
                >
                    <span>Xem đề</span>
                </button>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-500">
                <Clock size={14} />
                <span>{exam.testDuration ? `${exam.testDuration} phút` : "Vô thời hạn"}</span>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-500">
                <Calendar size={14} />
                <span>Năm: {exam.year || "Không rõ"}</span>
            </div>
        </div>
    );
};

const RightPanel = () => {
    const { relatedExams, loadingRelatedExams } = useSelector((state) => state.examDetail);
    const navigate = useNavigate();
    return (
        <div className="sm:w-1/4 flex flex-col gap-4">
            <p className="text-lg font-semibold">Bài thi khác</p>
            <div className="flex flex-col gap-2">
                {loadingRelatedExams ? (
                    <>
                        <RelatedExamCardSkeleton />
                        <RelatedExamCardSkeleton />
                        <RelatedExamCardSkeleton />
                        <RelatedExamCardSkeleton />
                        <RelatedExamCardSkeleton />
                    </>
                ) :
                    (relatedExams.map((exam) => (
                        <RelatedExamCard
                            key={exam.id}
                            exam={exam}
                            onClick={() => navigate(`/practice/exam/${exam.id}`)}
                            compact={true}
                        />
                    )))}
            </div>
        </div>
    )
}

const ExamDetail = () => {
    const { examId } = useParams();
    const { exam, loading } = useSelector((state) => state.examDetail);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const shareLink = `${window.location.origin}/practice/exam/${examId}`;

    const { comments, pagination } = useSelector((state) => state.comments);
    const { page } = pagination;

    const handleCopyLink = () => {
        navigator.clipboard.writeText(shareLink);
    };

    const handleSaveExam = () => {
        dispatch(saveExamForUser({ examId }));
    };

    const handleDoExam = () => {
        navigate(`/practice/exam/${examId}/do`);
    };



    const handleSendComment = (content) => {
        dispatch(postComment({ examId, content }));
    };

    const handleUpdateComment = (commentId, content) => {
        dispatch(putComment({ commentId, content }));
    };

    const handleDeleteComment = (commentId) => {
        dispatch(deleteComment(commentId));
    };

    const handleReplyComment = (content, parentCommentId) => {
        dispatch(postComment({ examId, content, parentCommentId }));
    };

    useEffect(() => {
        dispatch(fetchPublicExamById(examId));
        dispatch(fetchRelatedExamsIfNeeded(examId));
        dispatch(fetchCodesByType(["chapter", "exam type", "user type"]));
    }, [dispatch, examId]);

    useEffect(() => {
        dispatch(fetchCommentsByExamId({ examId, page }));
    }, [dispatch, examId, page]);

    return (
        <UserLayout>
            <div className="container flex flex-col mb-9">
                <div className="w-full flex flex-col md:flex-row gap-2 justify-between items-center">
                    <div className="items-start justify-start flex flex-wrap gap-2">
                        <FileText className="text-sky-600" />
                        <LoadingText loading={loading} w="w-48">
                            <p className="text-lg font-semibold">{exam?.name}</p>
                            <p className={`py-0.5 px-1.5 border rounded-full text-xs text-gray-500 flex-shrink-0 ${exam?.acceptDoExam ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'}`}>
                                {exam?.acceptDoExam ? 'Hoạt động' : 'Kết thúc'}
                            </p>
                            <p className={`py-0.5 px-1.5 border rounded-full text-xs text-gray-500 flex-shrink-0 ${exam?.isDone ? 'border-green-300 text-green-600' : 'border-yellow-300 text-yellow-600'}`}>
                                {exam?.isDone ? 'Đã làm' : 'Chưa làm'}
                            </p>
                        </LoadingText>
                    </div>
                    <LoadingText loading={loading} w="w-80">
                        <div className="flex md:flex-row flex-col md:w-fit w-full gap-2">
                            <button
                                // onClick={onClick}
                                className={`flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 py-[3px] px-3 rounded-md border transition-all
                                ${exam?.isSave
                                        ? "border-blue-500 bg-blue-50 text-blue-600 hover:bg-blue-100"
                                        : "border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200"
                                    }`}
                            >
                                <Pin size={16} />
                                <span>{exam?.isSave ? "Đã lưu" : "Lưu đề"}</span>
                            </button>
                            <button
                                // onClick={onClick}
                                className={`flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200`}
                            >
                                <Eye size={16} />
                                <span>Số lượt làm bài: {exam?.doneCount}</span>
                            </button>
                            <button
                                // onClick={onClick}
                                className={`flex-shrink-0 md:w-fit w-full text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200`}
                            >
                                <StarIcon size={16} />
                                <span>Star: {exam?.studentCount}</span>
                            </button>
                        </div>
                    </LoadingText>

                </div>
                <hr className="my-6 border-gray-300" />
                <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1 flex flex-col gap-4">
                        <div className="flex flex-row justify-between items-center gap-2">
                            <ActionButtons />
                            <DoExamButton
                                onClick={handleDoExam}
                                disabled={!exam?.acceptDoExam}
                            />
                        </div>
                        <InfoExam />
                        <SolutionVideo />
                        <CommentSection
                            comments={comments}
                            onSubmit={handleSendComment}
                            onUpdate={handleUpdateComment}
                            onDelete={handleDeleteComment}
                            onReply={handleReplyComment}
                        />
                    </div>
                    <RightPanel />
                </div>
            </div>
        </UserLayout>
    )
}

// Component hiển thị đề thi liên quan


export default ExamDetail;
