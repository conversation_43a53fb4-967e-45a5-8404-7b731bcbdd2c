{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\nexport const fetchExams = createAsyncThunk(\"exams/fetchExams\", async (_ref, _ref2) => {\n  let {\n    search,\n    currentPage,\n    limit,\n    sortOrder\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, examApi.getAllExamAPI, {\n    search,\n    currentPage,\n    limit,\n    sortOrder\n  }, data => {\n    // dispatch(setCurrentPage(data.currentPage));\n    // dispatch(setTotalPages(data.totalPages));\n    // dispatch(setTotalItems(data.totalItems));\n  }, true, false);\n});\nexport const fetchNewestExams = createAsyncThunk(\"exams/fetchNewestExams\", async (_, _ref3) => {\n  let {\n    dispatch\n  } = _ref3;\n  return await apiHandler(dispatch, examApi.getNewestExamAPI, null, () => {}, false, false);\n});\nexport const fetchSavedExam = createAsyncThunk(\"exams/fetchSavedExam\", async (_, _ref4) => {\n  let {\n    dispatch\n  } = _ref4;\n  return await apiHandler(dispatch, examApi.getExamsSavedAPI, null, () => {}, false, false);\n});\nexport const reExamination = createAsyncThunk(\"exams/reExamination\", async (id, _ref5) => {\n  let {\n    dispatch\n  } = _ref5;\n  return await apiHandler(dispatch, examApi.reExamination, id, () => {}, true, false, false, false);\n});\n\n// export const summitExam = createAsyncThunk(\n//     \"exams/summitExam\",\n//     async (attemptId, { dispatch }) => {\n//         return await apiHandler(dispatch, examApi.summitExamAPI, { attemptId }, () => { }, true, false);\n//     }\n// );\n\nexport const fetchRelatedExams = createAsyncThunk(\"exams/fetchRelatedExams\", async (id, _ref6) => {\n  let {\n    dispatch\n  } = _ref6;\n  return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => {}, false, false);\n});\n\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\"exams/fetchRelatedExamsIfNeeded\", async (id, _ref7) => {\n  let {\n    dispatch,\n    getState\n  } = _ref7;\n  const state = getState();\n  const {\n    relatedExams,\n    lastFetchedRelatedExams\n  } = state.exams;\n\n  // Kiểm tra xem đã có dữ liệu trong cache chưa\n  const hasData = relatedExams[id] && relatedExams[id].length > 0;\n\n  // Kiểm tra thời gian cache (5 phút = 300000ms)\n  const now = Date.now();\n  const lastFetched = lastFetchedRelatedExams[id] || 0;\n  const isCacheValid = now - lastFetched < 300000;\n\n  // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache\n  if (hasData && isCacheValid) {\n    // Cập nhật state.exams để hiển thị dữ liệu cache\n    return {\n      data: relatedExams[id]\n    };\n  }\n\n  // Nếu không có dữ liệu hoặc cache hết hạn, gọi API\n  return await dispatch(fetchRelatedExams(id)).unwrap();\n});\nexport const fetchExamById = createAsyncThunk(\"exams/fetchExamById\", async (id, _ref8) => {\n  let {\n    dispatch\n  } = _ref8;\n  return await apiHandler(dispatch, examApi.getExamByIdAPI, id, () => {}, true, false);\n});\nexport const findExams = createAsyncThunk(\"exams/findExams\", async (search, _ref9) => {\n  let {\n    dispatch\n  } = _ref9;\n  return await apiHandler(dispatch, examApi.findExamsAPI, search, () => {}, false, false, false, false);\n});\nexport const fetchPublicExamById = createAsyncThunk(\"exams/fetchPublicExamById\", async (id, _ref10) => {\n  let {\n    dispatch\n  } = _ref10;\n  return await apiHandler(dispatch, examApi.getExamPublic, id, () => {}, false, false);\n});\nexport const putExam = createAsyncThunk(\"exams/putExam\", async (_ref11, _ref12) => {\n  let {\n    examId,\n    examData\n  } = _ref11;\n  let {\n    dispatch\n  } = _ref12;\n  return await apiHandler(dispatch, examApi.putExamAPI, {\n    examId,\n    examData\n  }, () => {}, true, false);\n});\nexport const putImageExam = createAsyncThunk(\"exams/putImageExam\", async (_ref13, _ref14) => {\n  let {\n    examId,\n    examImage\n  } = _ref13;\n  let {\n    dispatch\n  } = _ref14;\n  return await apiHandler(dispatch, examApi.putImageExamAPI, {\n    examId,\n    examImage\n  }, () => {}, true, false);\n});\nexport const uploadSolutionPdf = createAsyncThunk(\"exams/uploadSolutionPdf\", async (_ref15, _ref16) => {\n  let {\n    examId,\n    pdfFile\n  } = _ref15;\n  let {\n    dispatch\n  } = _ref16;\n  return await apiHandler(dispatch, examApi.uploadSolutionPdfAPI, {\n    examId,\n    pdfFile\n  }, () => {}, true, false, true, false);\n});\nexport const uploadExamPdf = createAsyncThunk(\"exams/uploadExamPdf\", async (_ref17, _ref18) => {\n  let {\n    examId,\n    pdfFile\n  } = _ref17;\n  let {\n    dispatch\n  } = _ref18;\n  return await apiHandler(dispatch, examApi.uploadExamPdfAPI, {\n    examId,\n    pdfFile\n  }, () => {}, true, false, true, false);\n});\nexport const saveExamForUser = createAsyncThunk(\"exams/saveExamForUser\", async (_ref19, _ref20) => {\n  let {\n    examId\n  } = _ref19;\n  let {\n    dispatch\n  } = _ref20;\n  return await apiHandler(dispatch, examApi.saveExamForUserAPI, {\n    examId\n  }, () => {}, true, false, false, false);\n});\nexport const deleteExam = createAsyncThunk(\"exams/deleteExam\", async (id, _ref21) => {\n  let {\n    dispatch\n  } = _ref21;\n  return await apiHandler(dispatch, examApi.deleteExamAPI, id, () => {}, true, false);\n});\nexport const findPublicExams = createAsyncThunk(\"exams/findPublicExams\", async (search, _ref22) => {\n  let {\n    dispatch\n  } = _ref22;\n  return await apiHandler(dispatch, examApi.findPublicExamsAPI, search, () => {}, false, false, false, false);\n});\nconst examSlice = createSlice({\n  name: \"exams\",\n  initialState: {\n    exams: [],\n    exam: null,\n    examsSearch: [],\n    relatedExams: {},\n    // Lưu trữ đề thi liên quan theo examId\n    lastFetchedRelatedExams: {},\n    // Lưu thời gian gọi API cuối cùng theo examId\n    loadingExam: false,\n    loadingSearch: false,\n    loadingSubmit: false,\n    loadingUpload1: false,\n    loadingUpload2: false,\n    isSubmit: false,\n    pagination: {\n      ...initialPaginationState\n    },\n    ...initialFilterState\n  },\n  reducers: {\n    setExam: (state, action) => {\n      state.exam = action.payload;\n    },\n    ...paginationReducers,\n    ...filterReducers\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchExams.pending, state => {\n      state.exams = [];\n      state.loading = true;\n    }).addCase(fetchExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n        state.pagination = action.payload.pagination || initialPaginationState;\n      }\n      state.loading = false;\n    }).addCase(fetchExams.rejected, state => {\n      state.loading = false;\n      state.exams = [];\n    }).addCase(fetchNewestExams.pending, state => {\n      state.exams = [];\n    }).addCase(fetchNewestExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n      }\n    }).addCase(fetchSavedExam.pending, state => {\n      state.exams = [];\n      state.loadingExam = true;\n    }).addCase(fetchSavedExam.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exams = action.payload.data;\n      }\n      state.loadingExam = false;\n    }).addCase(fetchSavedExam.rejected, state => {\n      state.loadingExam = false;\n    }).addCase(fetchRelatedExams.pending, () => {\n      // Không xóa state.exams nữa để tránh làm mất dữ liệu hiện tại\n    }).addCase(fetchRelatedExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        const examId = action.meta.arg; // Lấy examId từ tham số gọi API\n        state.exams = action.payload.data;\n        state.relatedExams[examId] = action.payload.data;\n        state.lastFetchedRelatedExams[examId] = Date.now();\n      }\n    }).addCase(fetchExamById.pending, state => {\n      state.exam = null;\n    }).addCase(fetchExamById.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exam = action.payload.data;\n      }\n    }).addCase(findExams.pending, state => {\n      state.examsSearch = [];\n    }).addCase(findExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.examsSearch = action.payload.data;\n      }\n    }).addCase(fetchPublicExamById.pending, state => {\n      state.exam = null;\n    }).addCase(fetchPublicExamById.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.exam = action.payload.data;\n      }\n    }).addCase(saveExamForUser.fulfilled, (state, action) => {\n      if (action.payload) {\n        var _state$exams$;\n        const {\n          examId,\n          isSave\n        } = action.payload;\n\n        // Trường hợp 1: Khi exams chứa đối tượng có thuộc tính exam (từ API saved exams)\n        if (state.exams.length > 0 && (_state$exams$ = state.exams[0]) !== null && _state$exams$ !== void 0 && _state$exams$.exam) {\n          if (isSave === false) {\n            // Nếu isSave = false, loại bỏ exam khỏi danh sách đã lưu\n            state.exams = state.exams.filter(exam => {\n              var _exam$exam;\n              return ((_exam$exam = exam.exam) === null || _exam$exam === void 0 ? void 0 : _exam$exam.id) !== examId;\n            });\n          } else {\n            // Nếu isSave = true, cập nhật trạng thái\n            state.exams = state.exams.map(exam => {\n              var _exam$exam2;\n              if (((_exam$exam2 = exam.exam) === null || _exam$exam2 === void 0 ? void 0 : _exam$exam2.id) === examId) {\n                return {\n                  ...exam,\n                  isSave: true\n                };\n              }\n              return exam;\n            });\n          }\n          return;\n        }\n\n        // Trường hợp 2: Khi exams chứa danh sách exam thông thường\n        if (state.exams && Array.isArray(state.exams)) {\n          state.exams = state.exams.map(exam => {\n            if (exam.id === examId) {\n              return {\n                ...exam,\n                isSave: isSave\n              };\n            }\n            return exam;\n          });\n        }\n\n        // Cập nhật trạng thái cho exam hiện tại nếu có\n        if (state.exam) {\n          state.exam.isSave = isSave;\n        }\n      }\n    }).addCase(uploadSolutionPdf.pending, state => {\n      state.loadingUpload2 = true;\n    }).addCase(uploadSolutionPdf.fulfilled, (state, action) => {\n      // console.log(action.payload)\n      const pdfUrl = action.payload;\n      if (state.exam) {\n        state.exam.solutionPdfUrl = pdfUrl;\n      }\n      state.loadingUpload2 = false;\n    }).addCase(uploadSolutionPdf.rejected, state => {\n      state.loadingUpload1 = false;\n    }).addCase(uploadExamPdf.pending, state => {\n      state.loadingUpload1 = true;\n    }).addCase(uploadExamPdf.fulfilled, (state, action) => {\n      const pdfUrl = action.payload;\n      if (state.exam) {\n        state.exam.fileUrl = pdfUrl;\n      }\n      state.loadingUpload1 = false;\n    }).addCase(uploadExamPdf.rejected, state => {\n      state.loadingUpload1 = false;\n    }).addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\n      if (action.payload && action.payload.data) {\n        state.exams = action.payload.data;\n      }\n    })\n    // .addCase(summitExam.pending, (state) => {\n    //     state.loadingSubmit = true;\n    // })\n    // .addCase(summitExam.fulfilled, (state, action) => {\n    //     state.loadingSubmit = false;\n    //     state.isSubmit = true;\n    // })\n    // .addCase(summitExam.rejected, (state) => {\n    //     state.loadingSubmit = false;\n    //     state.isSubmit = false;\n    // })\n    .addCase(findPublicExams.pending, state => {\n      state.loadingSearch = true;\n    }).addCase(findPublicExams.fulfilled, (state, action) => {\n      if (action.payload) {\n        state.examsSearch = action.payload.data;\n      }\n      state.loadingSearch = false;\n    }).addCase(findPublicExams.rejected, state => {\n      state.examsSearch = [];\n      state.loadingSearch = false;\n    });\n  }\n});\nexport const {\n  setExam,\n  setCurrentPage,\n  setLimit,\n  setSortOrder,\n  setLoading,\n  setSearch\n} = examSlice.actions;\nexport default examSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialPaginationState", "paginationReducers", "initialFilterState", "filterReducers", "fetchExams", "_ref", "_ref2", "search", "currentPage", "limit", "sortOrder", "dispatch", "getAllExamAPI", "data", "fetchNewestExams", "_", "_ref3", "getNewestExamAPI", "fetchSavedExam", "_ref4", "getExamsSavedAPI", "reExamination", "id", "_ref5", "fetchRelatedExams", "_ref6", "getRelatedExamAPI", "fetchRelatedExamsIfNeeded", "_ref7", "getState", "state", "relatedExams", "lastFetchedRelatedExams", "exams", "hasData", "length", "now", "Date", "lastFetched", "isCache<PERSON><PERSON>d", "unwrap", "fetchExamById", "_ref8", "getExamByIdAPI", "findExams", "_ref9", "findExamsAPI", "fetchPublicExamById", "_ref10", "getExamPublic", "putExam", "_ref11", "_ref12", "examId", "examData", "putExamAPI", "putImageExam", "_ref13", "_ref14", "examImage", "putImageExamAPI", "uploadSolutionPdf", "_ref15", "_ref16", "pdfFile", "uploadSolutionPdfAPI", "uploadExamPdf", "_ref17", "_ref18", "uploadExamPdfAPI", "saveExamForUser", "_ref19", "_ref20", "saveExamForUserAPI", "deleteExam", "_ref21", "deleteExamAPI", "findPublicExams", "_ref22", "findPublicExamsAPI", "examSlice", "name", "initialState", "exam", "examsSearch", "loadingExam", "loadingSearch", "loadingSubmit", "loadingUpload1", "loadingUpload2", "isSubmit", "pagination", "reducers", "setExam", "action", "payload", "extraReducers", "builder", "addCase", "pending", "loading", "fulfilled", "rejected", "meta", "arg", "_state$exams$", "isSave", "filter", "_exam$exam", "map", "_exam$exam2", "Array", "isArray", "pdfUrl", "solutionPdfUrl", "fileUrl", "setCurrentPage", "setLimit", "setSortOrder", "setLoading", "setSearch", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/exam/examSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport { initialPaginationState, paginationReducers } from \"../pagination/paginationReducer\";\r\nimport { initialFilterState, filterReducers } from \"../filter/filterReducer\";\r\n\r\nexport const fetchExams = createAsyncThunk(\r\n    \"exams/fetchExams\",\r\n    async ({ search, currentPage, limit, sortOrder }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getAllExamAPI, { search, currentPage, limit, sortOrder }, (data) => {\r\n            // dispatch(setCurrentPage(data.currentPage));\r\n            // dispatch(setTotalPages(data.totalPages));\r\n            // dispatch(setTotalItems(data.totalItems));\r\n        }, true, false);\r\n    }\r\n);\r\n\r\nexport const fetchNewestExams = createAsyncThunk(\r\n    \"exams/fetchNewestExams\",\r\n    async (_, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getNewestExamAPI, null, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchSavedExam = createAsyncThunk(\r\n    \"exams/fetchSavedExam\",\r\n    async (_, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamsSavedAPI, null, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const reExamination = createAsyncThunk(\r\n    \"exams/reExamination\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.reExamination, id, () => { }, true, false, false, false);\r\n    }\r\n);\r\n\r\n// export const summitExam = createAsyncThunk(\r\n//     \"exams/summitExam\",\r\n//     async (attemptId, { dispatch }) => {\r\n//         return await apiHandler(dispatch, examApi.summitExamAPI, { attemptId }, () => { }, true, false);\r\n//     }\r\n// );\r\n\r\nexport const fetchRelatedExams = createAsyncThunk(\r\n    \"exams/fetchRelatedExams\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getRelatedExamAPI, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\n// Thêm action mới để kiểm tra và chỉ gọi API khi cần thiết\r\nexport const fetchRelatedExamsIfNeeded = createAsyncThunk(\r\n    \"exams/fetchRelatedExamsIfNeeded\",\r\n    async (id, { dispatch, getState }) => {\r\n        const state = getState();\r\n        const { relatedExams, lastFetchedRelatedExams } = state.exams;\r\n\r\n        // Kiểm tra xem đã có dữ liệu trong cache chưa\r\n        const hasData = relatedExams[id] && relatedExams[id].length > 0;\r\n\r\n        // Kiểm tra thời gian cache (5 phút = 300000ms)\r\n        const now = Date.now();\r\n        const lastFetched = lastFetchedRelatedExams[id] || 0;\r\n        const isCacheValid = now - lastFetched < 300000;\r\n\r\n        // Nếu có dữ liệu và cache còn hợp lệ, sử dụng dữ liệu cache\r\n        if (hasData && isCacheValid) {\r\n            // Cập nhật state.exams để hiển thị dữ liệu cache\r\n            return { data: relatedExams[id] };\r\n        }\r\n\r\n        // Nếu không có dữ liệu hoặc cache hết hạn, gọi API\r\n        return await dispatch(fetchRelatedExams(id)).unwrap();\r\n    }\r\n);\r\n\r\nexport const fetchExamById = createAsyncThunk(\r\n    \"exams/fetchExamById\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamByIdAPI, id, () => { }, true, false);\r\n    }\r\n);\r\n\r\nexport const findExams = createAsyncThunk(\r\n    \"exams/findExams\",\r\n    async (search, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.findExamsAPI, search, () => { }, false, false, false, false);\r\n    }\r\n);\r\n\r\nexport const fetchPublicExamById = createAsyncThunk(\r\n    \"exams/fetchPublicExamById\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.getExamPublic, id, () => { }, false, false);\r\n    }\r\n);\r\n\r\nexport const putExam = createAsyncThunk(\r\n    \"exams/putExam\",\r\n    async ({ examId, examData }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.putExamAPI, { examId, examData }, () => { }, true, false);\r\n    }\r\n);\r\n\r\nexport const putImageExam = createAsyncThunk(\r\n    \"exams/putImageExam\",\r\n    async ({ examId, examImage }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.putImageExamAPI, { examId, examImage }, () => { }, true, false);\r\n    }\r\n);\r\n\r\n\r\n\r\nexport const uploadSolutionPdf = createAsyncThunk(\r\n    \"exams/uploadSolutionPdf\",\r\n    async ({ examId, pdfFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.uploadSolutionPdfAPI, { examId, pdfFile }, () => { }, true, false, true, false);\r\n    }\r\n);\r\n\r\nexport const uploadExamPdf = createAsyncThunk(\r\n    \"exams/uploadExamPdf\",\r\n    async ({ examId, pdfFile }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.uploadExamPdfAPI, { examId, pdfFile }, () => { }, true, false, true, false);\r\n    }\r\n);\r\n\r\nexport const saveExamForUser = createAsyncThunk(\r\n    \"exams/saveExamForUser\",\r\n    async ({ examId }, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.saveExamForUserAPI, { examId }, () => { }, true, false, false, false);\r\n    }\r\n);\r\n\r\nexport const deleteExam = createAsyncThunk(\r\n    \"exams/deleteExam\",\r\n    async (id, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.deleteExamAPI, id, () => { }, true, false);\r\n    }\r\n);\r\n\r\nexport const findPublicExams = createAsyncThunk(\r\n    \"exams/findPublicExams\",\r\n    async (search, { dispatch }) => {\r\n        return await apiHandler(dispatch, examApi.findPublicExamsAPI, search, () => { }, false, false, false, false);\r\n    }\r\n);\r\n\r\nconst examSlice = createSlice({\r\n    name: \"exams\",\r\n    initialState: {\r\n        exams: [],\r\n        exam: null,\r\n        examsSearch: [],\r\n        relatedExams: {}, // Lưu trữ đề thi liên quan theo examId\r\n        lastFetchedRelatedExams: {}, // Lưu thời gian gọi API cuối cùng theo examId\r\n        loadingExam: false,\r\n        loadingSearch: false,\r\n        loadingSubmit: false,\r\n        loadingUpload1: false,\r\n        loadingUpload2: false,\r\n        isSubmit: false,\r\n        pagination: { ...initialPaginationState },\r\n        ...initialFilterState,\r\n    },\r\n    reducers: {\r\n        setExam: (state, action) => {\r\n            state.exam = action.payload;\r\n        },\r\n        ...paginationReducers,\r\n        ...filterReducers,\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchExams.pending, (state) => {\r\n                state.exams = [];\r\n                state.loading = true;\r\n            })\r\n            .addCase(fetchExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                    state.pagination = action.payload.pagination || initialPaginationState;\r\n                }\r\n                state.loading = false;\r\n            })\r\n            .addCase(fetchExams.rejected, (state) => {\r\n                state.loading = false;\r\n                state.exams = [];\r\n            })\r\n\r\n            .addCase(fetchNewestExams.pending, (state) => {\r\n                state.exams = [];\r\n            })\r\n            .addCase(fetchNewestExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(fetchSavedExam.pending, (state) => {\r\n                state.exams = [];\r\n                state.loadingExam = true;\r\n            })\r\n            .addCase(fetchSavedExam.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exams = action.payload.data;\r\n                }\r\n                state.loadingExam = false;\r\n            })\r\n            .addCase(fetchSavedExam.rejected, (state) => {\r\n                state.loadingExam = false;\r\n            })\r\n            .addCase(fetchRelatedExams.pending, () => {\r\n                // Không xóa state.exams nữa để tránh làm mất dữ liệu hiện tại\r\n            })\r\n            .addCase(fetchRelatedExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    const examId = action.meta.arg; // Lấy examId từ tham số gọi API\r\n                    state.exams = action.payload.data;\r\n                    state.relatedExams[examId] = action.payload.data;\r\n                    state.lastFetchedRelatedExams[examId] = Date.now();\r\n                }\r\n            })\r\n            .addCase(fetchExamById.pending, (state) => {\r\n                state.exam = null;\r\n            })\r\n            .addCase(fetchExamById.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exam = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(findExams.pending, (state) => {\r\n                state.examsSearch = [];\r\n            })\r\n            .addCase(findExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.examsSearch = action.payload.data;\r\n                }\r\n            })\r\n\r\n            .addCase(fetchPublicExamById.pending, (state) => {\r\n                state.exam = null;\r\n            })\r\n            .addCase(fetchPublicExamById.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.exam = action.payload.data;\r\n                }\r\n            })\r\n            .addCase(saveExamForUser.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    const { examId, isSave } = action.payload;\r\n\r\n                    // Trường hợp 1: Khi exams chứa đối tượng có thuộc tính exam (từ API saved exams)\r\n                    if (state.exams.length > 0 && state.exams[0]?.exam) {\r\n                        if (isSave === false) {\r\n                            // Nếu isSave = false, loại bỏ exam khỏi danh sách đã lưu\r\n                            state.exams = state.exams.filter(exam => exam.exam?.id !== examId);\r\n                        } else {\r\n                            // Nếu isSave = true, cập nhật trạng thái\r\n                            state.exams = state.exams.map(exam => {\r\n                                if (exam.exam?.id === examId) {\r\n                                    return { ...exam, isSave: true };\r\n                                }\r\n                                return exam;\r\n                            });\r\n                        }\r\n                        return;\r\n                    }\r\n\r\n                    // Trường hợp 2: Khi exams chứa danh sách exam thông thường\r\n                    if (state.exams && Array.isArray(state.exams)) {\r\n                        state.exams = state.exams.map(exam => {\r\n                            if (exam.id === examId) {\r\n                                return { ...exam, isSave: isSave };\r\n                            }\r\n                            return exam;\r\n                        });\r\n                    }\r\n\r\n                    // Cập nhật trạng thái cho exam hiện tại nếu có\r\n                    if (state.exam) {\r\n                        state.exam.isSave = isSave;\r\n                    }\r\n                }\r\n            })\r\n            .addCase(uploadSolutionPdf.pending, (state) => {\r\n                state.loadingUpload2 = true;\r\n            })\r\n            .addCase(uploadSolutionPdf.fulfilled, (state, action) => {\r\n                // console.log(action.payload)\r\n                const pdfUrl = action.payload\r\n                if (state.exam) {\r\n                    state.exam.solutionPdfUrl = pdfUrl;\r\n                }\r\n                state.loadingUpload2 = false;\r\n            })\r\n            .addCase(uploadSolutionPdf.rejected, (state) => {\r\n                state.loadingUpload1 = false;\r\n            })\r\n            .addCase(uploadExamPdf.pending, (state) => {\r\n                state.loadingUpload1 = true;\r\n            })\r\n            .addCase(uploadExamPdf.fulfilled, (state, action) => {\r\n                const pdfUrl = action.payload;\r\n                if (state.exam) {\r\n                    state.exam.fileUrl = pdfUrl;\r\n                }\r\n                state.loadingUpload1 = false;\r\n            })\r\n            .addCase(uploadExamPdf.rejected, (state) => {\r\n                state.loadingUpload1 = false;\r\n            })\r\n\r\n            .addCase(fetchRelatedExamsIfNeeded.fulfilled, (state, action) => {\r\n                if (action.payload && action.payload.data) {\r\n                    state.exams = action.payload.data;\r\n                }\r\n            })\r\n            // .addCase(summitExam.pending, (state) => {\r\n            //     state.loadingSubmit = true;\r\n            // })\r\n            // .addCase(summitExam.fulfilled, (state, action) => {\r\n            //     state.loadingSubmit = false;\r\n            //     state.isSubmit = true;\r\n            // })\r\n            // .addCase(summitExam.rejected, (state) => {\r\n            //     state.loadingSubmit = false;\r\n            //     state.isSubmit = false;\r\n            // })\r\n            .addCase(findPublicExams.pending, (state) => {\r\n                state.loadingSearch = true;\r\n            })\r\n            .addCase(findPublicExams.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.examsSearch = action.payload.data;\r\n                }\r\n                state.loadingSearch = false;\r\n            })\r\n            .addCase(findPublicExams.rejected, (state) => {\r\n                state.examsSearch = [];\r\n                state.loadingSearch = false;\r\n            })\r\n    }\r\n});\r\n\r\nexport const {\r\n    setExam,\r\n    setCurrentPage,\r\n    setLimit,\r\n    setSortOrder,\r\n    setLoading,\r\n    setSearch\r\n} = examSlice.actions;\r\nexport default examSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAE5E,OAAO,MAAMC,UAAU,GAAGP,gBAAgB,CACtC,kBAAkB,EAClB,OAAAQ,IAAA,EAAAC,KAAA,KAAmE;EAAA,IAA5D;IAAEC,MAAM;IAAEC,WAAW;IAAEC,KAAK;IAAEC;EAAU,CAAC,GAAAL,IAAA;EAAA,IAAE;IAAEM;EAAS,CAAC,GAAAL,KAAA;EAC1D,OAAO,MAAMP,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACc,aAAa,EAAE;IAAEL,MAAM;IAAEC,WAAW;IAAEC,KAAK;IAAEC;EAAU,CAAC,EAAGG,IAAI,IAAK;IAC1G;IACA;IACA;EAAA,CACH,EAAE,IAAI,EAAE,KAAK,CAAC;AACnB,CACJ,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGjB,gBAAgB,CAC5C,wBAAwB,EACxB,OAAOkB,CAAC,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEL;EAAS,CAAC,GAAAK,KAAA;EAClB,OAAO,MAAMjB,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACmB,gBAAgB,EAAE,IAAI,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC9F,CACJ,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGrB,gBAAgB,CAC1C,sBAAsB,EACtB,OAAOkB,CAAC,EAAAI,KAAA,KAAmB;EAAA,IAAjB;IAAER;EAAS,CAAC,GAAAQ,KAAA;EAClB,OAAO,MAAMpB,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACsB,gBAAgB,EAAE,IAAI,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC9F,CACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGxB,gBAAgB,CACzC,qBAAqB,EACrB,OAAOyB,EAAE,EAAAC,KAAA,KAAmB;EAAA,IAAjB;IAAEZ;EAAS,CAAC,GAAAY,KAAA;EACnB,OAAO,MAAMxB,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACuB,aAAa,EAAEC,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACtG,CACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAME,iBAAiB,GAAG3B,gBAAgB,CAC7C,yBAAyB,EACzB,OAAOyB,EAAE,EAAAG,KAAA,KAAmB;EAAA,IAAjB;IAAEd;EAAS,CAAC,GAAAc,KAAA;EACnB,OAAO,MAAM1B,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC4B,iBAAiB,EAAEJ,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC7F,CACJ,CAAC;;AAED;AACA,OAAO,MAAMK,yBAAyB,GAAG9B,gBAAgB,CACrD,iCAAiC,EACjC,OAAOyB,EAAE,EAAAM,KAAA,KAA6B;EAAA,IAA3B;IAAEjB,QAAQ;IAAEkB;EAAS,CAAC,GAAAD,KAAA;EAC7B,MAAME,KAAK,GAAGD,QAAQ,CAAC,CAAC;EACxB,MAAM;IAAEE,YAAY;IAAEC;EAAwB,CAAC,GAAGF,KAAK,CAACG,KAAK;;EAE7D;EACA,MAAMC,OAAO,GAAGH,YAAY,CAACT,EAAE,CAAC,IAAIS,YAAY,CAACT,EAAE,CAAC,CAACa,MAAM,GAAG,CAAC;;EAE/D;EACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;EACtB,MAAME,WAAW,GAAGN,uBAAuB,CAACV,EAAE,CAAC,IAAI,CAAC;EACpD,MAAMiB,YAAY,GAAGH,GAAG,GAAGE,WAAW,GAAG,MAAM;;EAE/C;EACA,IAAIJ,OAAO,IAAIK,YAAY,EAAE;IACzB;IACA,OAAO;MAAE1B,IAAI,EAAEkB,YAAY,CAACT,EAAE;IAAE,CAAC;EACrC;;EAEA;EACA,OAAO,MAAMX,QAAQ,CAACa,iBAAiB,CAACF,EAAE,CAAC,CAAC,CAACkB,MAAM,CAAC,CAAC;AACzD,CACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG5C,gBAAgB,CACzC,qBAAqB,EACrB,OAAOyB,EAAE,EAAAoB,KAAA,KAAmB;EAAA,IAAjB;IAAE/B;EAAS,CAAC,GAAA+B,KAAA;EACnB,OAAO,MAAM3C,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC6C,cAAc,EAAErB,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACzF,CACJ,CAAC;AAED,OAAO,MAAMsB,SAAS,GAAG/C,gBAAgB,CACrC,iBAAiB,EACjB,OAAOU,MAAM,EAAAsC,KAAA,KAAmB;EAAA,IAAjB;IAAElC;EAAS,CAAC,GAAAkC,KAAA;EACvB,OAAO,MAAM9C,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACgD,YAAY,EAAEvC,MAAM,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAC1G,CACJ,CAAC;AAED,OAAO,MAAMwC,mBAAmB,GAAGlD,gBAAgB,CAC/C,2BAA2B,EAC3B,OAAOyB,EAAE,EAAA0B,MAAA,KAAmB;EAAA,IAAjB;IAAErC;EAAS,CAAC,GAAAqC,MAAA;EACnB,OAAO,MAAMjD,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACmD,aAAa,EAAE3B,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACzF,CACJ,CAAC;AAED,OAAO,MAAM4B,OAAO,GAAGrD,gBAAgB,CACnC,eAAe,EACf,OAAAsD,MAAA,EAAAC,MAAA,KAA8C;EAAA,IAAvC;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAAH,MAAA;EAAA,IAAE;IAAExC;EAAS,CAAC,GAAAyC,MAAA;EACrC,OAAO,MAAMrD,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACyD,UAAU,EAAE;IAAEF,MAAM;IAAEC;EAAS,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACvG,CACJ,CAAC;AAED,OAAO,MAAME,YAAY,GAAG3D,gBAAgB,CACxC,oBAAoB,EACpB,OAAA4D,MAAA,EAAAC,MAAA,KAA+C;EAAA,IAAxC;IAAEL,MAAM;IAAEM;EAAU,CAAC,GAAAF,MAAA;EAAA,IAAE;IAAE9C;EAAS,CAAC,GAAA+C,MAAA;EACtC,OAAO,MAAM3D,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC8D,eAAe,EAAE;IAAEP,MAAM;IAAEM;EAAU,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AAC7G,CACJ,CAAC;AAID,OAAO,MAAME,iBAAiB,GAAGhE,gBAAgB,CAC7C,yBAAyB,EACzB,OAAAiE,MAAA,EAAAC,MAAA,KAA6C;EAAA,IAAtC;IAAEV,MAAM;IAAEW;EAAQ,CAAC,GAAAF,MAAA;EAAA,IAAE;IAAEnD;EAAS,CAAC,GAAAoD,MAAA;EACpC,OAAO,MAAMhE,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACmE,oBAAoB,EAAE;IAAEZ,MAAM;IAAEW;EAAQ,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AAC7H,CACJ,CAAC;AAED,OAAO,MAAME,aAAa,GAAGrE,gBAAgB,CACzC,qBAAqB,EACrB,OAAAsE,MAAA,EAAAC,MAAA,KAA6C;EAAA,IAAtC;IAAEf,MAAM;IAAEW;EAAQ,CAAC,GAAAG,MAAA;EAAA,IAAE;IAAExD;EAAS,CAAC,GAAAyD,MAAA;EACpC,OAAO,MAAMrE,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACuE,gBAAgB,EAAE;IAAEhB,MAAM;IAAEW;EAAQ,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AACzH,CACJ,CAAC;AAED,OAAO,MAAMM,eAAe,GAAGzE,gBAAgB,CAC3C,uBAAuB,EACvB,OAAA0E,MAAA,EAAAC,MAAA,KAAoC;EAAA,IAA7B;IAAEnB;EAAO,CAAC,GAAAkB,MAAA;EAAA,IAAE;IAAE5D;EAAS,CAAC,GAAA6D,MAAA;EAC3B,OAAO,MAAMzE,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC2E,kBAAkB,EAAE;IAAEpB;EAAO,CAAC,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACnH,CACJ,CAAC;AAED,OAAO,MAAMqB,UAAU,GAAG7E,gBAAgB,CACtC,kBAAkB,EAClB,OAAOyB,EAAE,EAAAqD,MAAA,KAAmB;EAAA,IAAjB;IAAEhE;EAAS,CAAC,GAAAgE,MAAA;EACnB,OAAO,MAAM5E,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAAC8E,aAAa,EAAEtD,EAAE,EAAE,MAAM,CAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACxF,CACJ,CAAC;AAED,OAAO,MAAMuD,eAAe,GAAGhF,gBAAgB,CAC3C,uBAAuB,EACvB,OAAOU,MAAM,EAAAuE,MAAA,KAAmB;EAAA,IAAjB;IAAEnE;EAAS,CAAC,GAAAmE,MAAA;EACvB,OAAO,MAAM/E,UAAU,CAACY,QAAQ,EAAEb,OAAO,CAACiF,kBAAkB,EAAExE,MAAM,EAAE,MAAM,CAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAChH,CACJ,CAAC;AAED,MAAMyE,SAAS,GAAGpF,WAAW,CAAC;EAC1BqF,IAAI,EAAE,OAAO;EACbC,YAAY,EAAE;IACVjD,KAAK,EAAE,EAAE;IACTkD,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,EAAE;IACfrD,YAAY,EAAE,CAAC,CAAC;IAAE;IAClBC,uBAAuB,EAAE,CAAC,CAAC;IAAE;IAC7BqD,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,KAAK;IACrBC,cAAc,EAAE,KAAK;IACrBC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;MAAE,GAAG3F;IAAuB,CAAC;IACzC,GAAGE;EACP,CAAC;EACD0F,QAAQ,EAAE;IACNC,OAAO,EAAEA,CAAC/D,KAAK,EAAEgE,MAAM,KAAK;MACxBhE,KAAK,CAACqD,IAAI,GAAGW,MAAM,CAACC,OAAO;IAC/B,CAAC;IACD,GAAG9F,kBAAkB;IACrB,GAAGE;EACP,CAAC;EACD6F,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAAC9F,UAAU,CAAC+F,OAAO,EAAGrE,KAAK,IAAK;MACpCA,KAAK,CAACG,KAAK,GAAG,EAAE;MAChBH,KAAK,CAACsE,OAAO,GAAG,IAAI;IACxB,CAAC,CAAC,CACDF,OAAO,CAAC9F,UAAU,CAACiG,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MAC9C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBjE,KAAK,CAACG,KAAK,GAAG6D,MAAM,CAACC,OAAO,CAAClF,IAAI;QACjCiB,KAAK,CAAC6D,UAAU,GAAGG,MAAM,CAACC,OAAO,CAACJ,UAAU,IAAI3F,sBAAsB;MAC1E;MACA8B,KAAK,CAACsE,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDF,OAAO,CAAC9F,UAAU,CAACkG,QAAQ,EAAGxE,KAAK,IAAK;MACrCA,KAAK,CAACsE,OAAO,GAAG,KAAK;MACrBtE,KAAK,CAACG,KAAK,GAAG,EAAE;IACpB,CAAC,CAAC,CAEDiE,OAAO,CAACpF,gBAAgB,CAACqF,OAAO,EAAGrE,KAAK,IAAK;MAC1CA,KAAK,CAACG,KAAK,GAAG,EAAE;IACpB,CAAC,CAAC,CACDiE,OAAO,CAACpF,gBAAgB,CAACuF,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MACpD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBjE,KAAK,CAACG,KAAK,GAAG6D,MAAM,CAACC,OAAO,CAAClF,IAAI;MACrC;IACJ,CAAC,CAAC,CACDqF,OAAO,CAAChF,cAAc,CAACiF,OAAO,EAAGrE,KAAK,IAAK;MACxCA,KAAK,CAACG,KAAK,GAAG,EAAE;MAChBH,KAAK,CAACuD,WAAW,GAAG,IAAI;IAC5B,CAAC,CAAC,CACDa,OAAO,CAAChF,cAAc,CAACmF,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MAClD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBjE,KAAK,CAACG,KAAK,GAAG6D,MAAM,CAACC,OAAO,CAAClF,IAAI;MACrC;MACAiB,KAAK,CAACuD,WAAW,GAAG,KAAK;IAC7B,CAAC,CAAC,CACDa,OAAO,CAAChF,cAAc,CAACoF,QAAQ,EAAGxE,KAAK,IAAK;MACzCA,KAAK,CAACuD,WAAW,GAAG,KAAK;IAC7B,CAAC,CAAC,CACDa,OAAO,CAAC1E,iBAAiB,CAAC2E,OAAO,EAAE,MAAM;MACtC;IAAA,CACH,CAAC,CACDD,OAAO,CAAC1E,iBAAiB,CAAC6E,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MACrD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChB,MAAM1C,MAAM,GAAGyC,MAAM,CAACS,IAAI,CAACC,GAAG,CAAC,CAAC;QAChC1E,KAAK,CAACG,KAAK,GAAG6D,MAAM,CAACC,OAAO,CAAClF,IAAI;QACjCiB,KAAK,CAACC,YAAY,CAACsB,MAAM,CAAC,GAAGyC,MAAM,CAACC,OAAO,CAAClF,IAAI;QAChDiB,KAAK,CAACE,uBAAuB,CAACqB,MAAM,CAAC,GAAGhB,IAAI,CAACD,GAAG,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC,CACD8D,OAAO,CAACzD,aAAa,CAAC0D,OAAO,EAAGrE,KAAK,IAAK;MACvCA,KAAK,CAACqD,IAAI,GAAG,IAAI;IACrB,CAAC,CAAC,CACDe,OAAO,CAACzD,aAAa,CAAC4D,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MACjD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBjE,KAAK,CAACqD,IAAI,GAAGW,MAAM,CAACC,OAAO,CAAClF,IAAI;MACpC;IACJ,CAAC,CAAC,CACDqF,OAAO,CAACtD,SAAS,CAACuD,OAAO,EAAGrE,KAAK,IAAK;MACnCA,KAAK,CAACsD,WAAW,GAAG,EAAE;IAC1B,CAAC,CAAC,CACDc,OAAO,CAACtD,SAAS,CAACyD,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MAC7C,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBjE,KAAK,CAACsD,WAAW,GAAGU,MAAM,CAACC,OAAO,CAAClF,IAAI;MAC3C;IACJ,CAAC,CAAC,CAEDqF,OAAO,CAACnD,mBAAmB,CAACoD,OAAO,EAAGrE,KAAK,IAAK;MAC7CA,KAAK,CAACqD,IAAI,GAAG,IAAI;IACrB,CAAC,CAAC,CACDe,OAAO,CAACnD,mBAAmB,CAACsD,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MACvD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBjE,KAAK,CAACqD,IAAI,GAAGW,MAAM,CAACC,OAAO,CAAClF,IAAI;MACpC;IACJ,CAAC,CAAC,CACDqF,OAAO,CAAC5B,eAAe,CAAC+B,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MACnD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAAA,IAAAU,aAAA;QAChB,MAAM;UAAEpD,MAAM;UAAEqD;QAAO,CAAC,GAAGZ,MAAM,CAACC,OAAO;;QAEzC;QACA,IAAIjE,KAAK,CAACG,KAAK,CAACE,MAAM,GAAG,CAAC,KAAAsE,aAAA,GAAI3E,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,cAAAwE,aAAA,eAAdA,aAAA,CAAgBtB,IAAI,EAAE;UAChD,IAAIuB,MAAM,KAAK,KAAK,EAAE;YAClB;YACA5E,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC0E,MAAM,CAACxB,IAAI;cAAA,IAAAyB,UAAA;cAAA,OAAI,EAAAA,UAAA,GAAAzB,IAAI,CAACA,IAAI,cAAAyB,UAAA,uBAATA,UAAA,CAAWtF,EAAE,MAAK+B,MAAM;YAAA,EAAC;UACtE,CAAC,MAAM;YACH;YACAvB,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC4E,GAAG,CAAC1B,IAAI,IAAI;cAAA,IAAA2B,WAAA;cAClC,IAAI,EAAAA,WAAA,GAAA3B,IAAI,CAACA,IAAI,cAAA2B,WAAA,uBAATA,WAAA,CAAWxF,EAAE,MAAK+B,MAAM,EAAE;gBAC1B,OAAO;kBAAE,GAAG8B,IAAI;kBAAEuB,MAAM,EAAE;gBAAK,CAAC;cACpC;cACA,OAAOvB,IAAI;YACf,CAAC,CAAC;UACN;UACA;QACJ;;QAEA;QACA,IAAIrD,KAAK,CAACG,KAAK,IAAI8E,KAAK,CAACC,OAAO,CAAClF,KAAK,CAACG,KAAK,CAAC,EAAE;UAC3CH,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC4E,GAAG,CAAC1B,IAAI,IAAI;YAClC,IAAIA,IAAI,CAAC7D,EAAE,KAAK+B,MAAM,EAAE;cACpB,OAAO;gBAAE,GAAG8B,IAAI;gBAAEuB,MAAM,EAAEA;cAAO,CAAC;YACtC;YACA,OAAOvB,IAAI;UACf,CAAC,CAAC;QACN;;QAEA;QACA,IAAIrD,KAAK,CAACqD,IAAI,EAAE;UACZrD,KAAK,CAACqD,IAAI,CAACuB,MAAM,GAAGA,MAAM;QAC9B;MACJ;IACJ,CAAC,CAAC,CACDR,OAAO,CAACrC,iBAAiB,CAACsC,OAAO,EAAGrE,KAAK,IAAK;MAC3CA,KAAK,CAAC2D,cAAc,GAAG,IAAI;IAC/B,CAAC,CAAC,CACDS,OAAO,CAACrC,iBAAiB,CAACwC,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MACrD;MACA,MAAMmB,MAAM,GAAGnB,MAAM,CAACC,OAAO;MAC7B,IAAIjE,KAAK,CAACqD,IAAI,EAAE;QACZrD,KAAK,CAACqD,IAAI,CAAC+B,cAAc,GAAGD,MAAM;MACtC;MACAnF,KAAK,CAAC2D,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CACDS,OAAO,CAACrC,iBAAiB,CAACyC,QAAQ,EAAGxE,KAAK,IAAK;MAC5CA,KAAK,CAAC0D,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CACDU,OAAO,CAAChC,aAAa,CAACiC,OAAO,EAAGrE,KAAK,IAAK;MACvCA,KAAK,CAAC0D,cAAc,GAAG,IAAI;IAC/B,CAAC,CAAC,CACDU,OAAO,CAAChC,aAAa,CAACmC,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MACjD,MAAMmB,MAAM,GAAGnB,MAAM,CAACC,OAAO;MAC7B,IAAIjE,KAAK,CAACqD,IAAI,EAAE;QACZrD,KAAK,CAACqD,IAAI,CAACgC,OAAO,GAAGF,MAAM;MAC/B;MACAnF,KAAK,CAAC0D,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CACDU,OAAO,CAAChC,aAAa,CAACoC,QAAQ,EAAGxE,KAAK,IAAK;MACxCA,KAAK,CAAC0D,cAAc,GAAG,KAAK;IAChC,CAAC,CAAC,CAEDU,OAAO,CAACvE,yBAAyB,CAAC0E,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MAC7D,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAAClF,IAAI,EAAE;QACvCiB,KAAK,CAACG,KAAK,GAAG6D,MAAM,CAACC,OAAO,CAAClF,IAAI;MACrC;IACJ,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAAA,CACCqF,OAAO,CAACrB,eAAe,CAACsB,OAAO,EAAGrE,KAAK,IAAK;MACzCA,KAAK,CAACwD,aAAa,GAAG,IAAI;IAC9B,CAAC,CAAC,CACDY,OAAO,CAACrB,eAAe,CAACwB,SAAS,EAAE,CAACvE,KAAK,EAAEgE,MAAM,KAAK;MACnD,IAAIA,MAAM,CAACC,OAAO,EAAE;QAChBjE,KAAK,CAACsD,WAAW,GAAGU,MAAM,CAACC,OAAO,CAAClF,IAAI;MAC3C;MACAiB,KAAK,CAACwD,aAAa,GAAG,KAAK;IAC/B,CAAC,CAAC,CACDY,OAAO,CAACrB,eAAe,CAACyB,QAAQ,EAAGxE,KAAK,IAAK;MAC1CA,KAAK,CAACsD,WAAW,GAAG,EAAE;MACtBtD,KAAK,CAACwD,aAAa,GAAG,KAAK;IAC/B,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACTO,OAAO;EACPuB,cAAc;EACdC,QAAQ;EACRC,YAAY;EACZC,UAAU;EACVC;AACJ,CAAC,GAAGxC,SAAS,CAACyC,OAAO;AACrB,eAAezC,SAAS,CAAC0C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}