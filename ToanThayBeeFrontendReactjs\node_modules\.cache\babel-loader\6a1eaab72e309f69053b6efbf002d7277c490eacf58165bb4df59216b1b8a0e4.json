{"ast": null, "code": "import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\nimport * as examApi from \"../../services/examApi\";\nimport { apiHandler } from \"../../utils/apiHandler\";\nimport * as questionApi from \"../../services/questionApi\";\nexport const fetchExamQuestionsWithoutPagination = createAsyncThunk(\"questions/fetchExamQuestionsWithExam\", async (_ref, _ref2) => {\n  let {\n    id\n  } = _ref;\n  let {\n    dispatch\n  } = _ref2;\n  return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, {\n    id,\n    pageSize: 1000,\n    sortOrder: 'DESC'\n  }, data => {}, false, false);\n});\nconst questionsExamSlice = createSlice({\n  name: \"questionsExam\",\n  initialState: {\n    questionsExam: [],\n    loading: false,\n    view: 'question',\n    showAddImagesModal: false,\n    folder: \"questionImage\",\n    selectedId: 0,\n    newQuestion: {\n      content: \"\",\n      description: \"\",\n      typeOfQuestion: null,\n      class: null,\n      chapter: null,\n      difficulty: null,\n      solution: \"\",\n      correctAnswer: null,\n      solutionUrl: \"\"\n    }\n  },\n  reducers: {\n    setQuestionsExam: (state, action) => {\n      state.questionsExam = action.payload;\n    },\n    setNewQuestion: (state, action) => {\n      state.newQuestion = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setViewRightContent: (state, action) => {\n      state.view = action.payload;\n    },\n    setSelectedId: (state, action) => {\n      state.selectedId = action.payload;\n    },\n    setQuestions: (state, action) => {\n      const question = action.payload;\n      const index = state.questionsExam.findIndex(q => q.id === question.id);\n      if (index !== -1) {\n        state.questionsExam[index] = question;\n      } else {\n        state.questionsExam.push(question);\n      }\n    },\n    reorderQuestions: (state, action) => {\n      const {\n        oldIndex,\n        newIndex\n      } = action.payload;\n      if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0 && oldIndex < state.questionsExam.length && newIndex < state.questionsExam.length) {\n        // Tạo mảng mới với thứ tự đã thay đổi\n        const newQuestions = [...state.questionsExam];\n        const [movedQuestion] = newQuestions.splice(oldIndex, 1);\n        newQuestions.splice(newIndex, 0, movedQuestion);\n\n        // Cập nhật thuộc tính order cho tất cả câu hỏi\n        const updatedQuestions = newQuestions.map((question, index) => ({\n          ...question,\n          ExamQuestions: {\n            ...question.ExamQuestions,\n            order: index\n          }\n        }));\n        state.questionsExam = updatedQuestions;\n      }\n    },\n    reorderStatements: (state, action) => {\n      const {\n        questionId,\n        oldIndex,\n        newIndex\n      } = action.payload;\n      const questionIndex = state.questionsExam.findIndex(q => q.id === questionId);\n      if (questionIndex === -1) return;\n      const question = state.questionsExam[questionIndex];\n      if (!question.statements || oldIndex === newIndex) return;\n      if (oldIndex >= 0 && newIndex >= 0 && oldIndex < question.statements.length && newIndex < question.statements.length) {\n        // Tạo mảng mới với thứ tự đã thay đổi\n        const newStatements = [...question.statements];\n        const [movedStatement] = newStatements.splice(oldIndex, 1);\n        newStatements.splice(newIndex, 0, movedStatement);\n        // console.log(\"question\", question.statements);\n        // Cập nhật thuộc tính order cho tất cả statements\n        const updatedStatements = newStatements.map((statement, index) => ({\n          ...statement,\n          order: index\n        }));\n\n        // Cập nhật question với statements mới\n        const updatedQuestion = {\n          ...question,\n          statements: updatedStatements\n        };\n        state.questionsExam[questionIndex] = updatedQuestion;\n      }\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchExamQuestionsWithoutPagination.pending, state => {\n      state.loading = true;\n      state.questionsExam = [];\n      state.selectedId = 0;\n    }).addCase(fetchExamQuestionsWithoutPagination.fulfilled, (state, action) => {\n      if (action.payload) {\n        var _action$payload$data$;\n        state.questionsExam = action.payload.data;\n        state.selectedId = ((_action$payload$data$ = action.payload.data[0]) === null || _action$payload$data$ === void 0 ? void 0 : _action$payload$data$.id) || 0;\n      }\n      state.loading = false;\n    }).addCase(fetchExamQuestionsWithoutPagination.rejected, state => {\n      state.questionsExam = [];\n      state.selectedId = 0;\n      state.loading = false;\n    });\n  }\n});\nexport const {\n  setQuestionsExam,\n  setLoading,\n  setViewRightContent,\n  setSelectedId,\n  setQuestions,\n  reorderQuestions,\n  reorderStatements,\n  setNewQuestion\n} = questionsExamSlice.actions;\nexport default questionsExamSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "examApi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questionA<PERSON>", "fetchExamQuestionsWithoutPagination", "_ref", "_ref2", "id", "dispatch", "getExamQuestionsAPI", "pageSize", "sortOrder", "data", "questionsExamSlice", "name", "initialState", "questionsExam", "loading", "view", "showAddImagesModal", "folder", "selectedId", "newQuestion", "content", "description", "typeOfQuestion", "class", "chapter", "difficulty", "solution", "<PERSON><PERSON><PERSON><PERSON>", "solutionUrl", "reducers", "setQuestionsExam", "state", "action", "payload", "setNewQuestion", "setLoading", "setViewRightContent", "setSelectedId", "setQuestions", "question", "index", "findIndex", "q", "push", "reorderQuestions", "oldIndex", "newIndex", "length", "newQuestions", "movedQuestion", "splice", "updatedQuestions", "map", "ExamQuestions", "order", "reorderStatements", "questionId", "questionIndex", "statements", "newStatements", "movedStatement", "updatedStatements", "statement", "updatedQuestion", "extraReducers", "builder", "addCase", "pending", "fulfilled", "_action$payload$data$", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/features/questionsExam/questionsExamSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from \"@reduxjs/toolkit\";\r\nimport * as examApi from \"../../services/examApi\";\r\nimport { apiHandler } from \"../../utils/apiHandler\";\r\nimport * as questionApi from \"../../services/questionApi\";\r\n\r\nexport const fetchExamQuestionsWithoutPagination = createAsyncThunk(\r\n    \"questions/fetchExamQuestionsWithExam\",\r\n    async ({ id }, { dispatch }) => {\r\n        return await apiHandler(dispatch, questionApi.getExamQuestionsAPI, { id, pageSize: 1000, sortOrder: 'DESC' }, (data) => {\r\n        }, false, false);\r\n    }\r\n);\r\n\r\n\r\nconst questionsExamSlice = createSlice({\r\n    name: \"questionsExam\",\r\n    initialState: {\r\n        questionsExam: [],\r\n        loading: false,\r\n        view: 'question',\r\n        showAddImagesModal: false,\r\n        folder: \"questionImage\",\r\n        selectedId: 0,\r\n        newQuestion: {\r\n            content: \"\",\r\n            description: \"\",\r\n            typeOfQuestion: null,\r\n            class: null,\r\n            chapter: null,\r\n            difficulty: null,\r\n            solution: \"\",\r\n            correctAnswer: null,\r\n            solutionUrl: \"\"\r\n        },\r\n    },\r\n    reducers: {\r\n        setQuestionsExam: (state, action) => {\r\n            state.questionsExam = action.payload;\r\n        },\r\n        setNewQuestion: (state, action) => {\r\n            state.newQuestion = action.payload;\r\n        },\r\n        setLoading: (state, action) => {\r\n            state.loading = action.payload;\r\n        },\r\n        setViewRightContent: (state, action) => {\r\n            state.view = action.payload;\r\n        },\r\n        setSelectedId: (state, action) => {\r\n            state.selectedId = action.payload;\r\n        },\r\n        setQuestions: (state, action) => {\r\n            const question = action.payload;\r\n            const index = state.questionsExam.findIndex(q => q.id === question.id);\r\n            if (index !== -1) {\r\n                state.questionsExam[index] = question;\r\n            } else {\r\n                state.questionsExam.push(question);\r\n            }\r\n        },\r\n        reorderQuestions: (state, action) => {\r\n            const { oldIndex, newIndex } = action.payload;\r\n\r\n            if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0 &&\r\n                oldIndex < state.questionsExam.length && newIndex < state.questionsExam.length) {\r\n\r\n                // Tạo mảng mới với thứ tự đã thay đổi\r\n                const newQuestions = [...state.questionsExam];\r\n                const [movedQuestion] = newQuestions.splice(oldIndex, 1);\r\n                newQuestions.splice(newIndex, 0, movedQuestion);\r\n\r\n                // Cập nhật thuộc tính order cho tất cả câu hỏi\r\n                const updatedQuestions = newQuestions.map((question, index) => ({\r\n                    ...question,\r\n                    ExamQuestions: {\r\n                        ...question.ExamQuestions,\r\n                        order: index\r\n                    }\r\n                }));\r\n\r\n                state.questionsExam = updatedQuestions;\r\n            }\r\n        },\r\n        reorderStatements: (state, action) => {\r\n            const { questionId, oldIndex, newIndex } = action.payload;\r\n\r\n            const questionIndex = state.questionsExam.findIndex(q => q.id === questionId);\r\n            if (questionIndex === -1) return;\r\n\r\n            const question = state.questionsExam[questionIndex];\r\n            if (!question.statements || oldIndex === newIndex) return;\r\n\r\n            if (oldIndex >= 0 && newIndex >= 0 &&\r\n                oldIndex < question.statements.length && newIndex < question.statements.length) {\r\n\r\n                // Tạo mảng mới với thứ tự đã thay đổi\r\n                const newStatements = [...question.statements];\r\n                const [movedStatement] = newStatements.splice(oldIndex, 1);\r\n                newStatements.splice(newIndex, 0, movedStatement);\r\n                // console.log(\"question\", question.statements);\r\n                // Cập nhật thuộc tính order cho tất cả statements\r\n                const updatedStatements = newStatements.map((statement, index) => ({\r\n                    ...statement,\r\n                    order: index\r\n                }));\r\n\r\n                // Cập nhật question với statements mới\r\n                const updatedQuestion = {\r\n                    ...question,\r\n                    statements: updatedStatements\r\n                };\r\n\r\n                state.questionsExam[questionIndex] = updatedQuestion;\r\n            }\r\n        }\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchExamQuestionsWithoutPagination.pending, (state) => {\r\n                state.loading = true;\r\n                state.questionsExam = [];\r\n                state.selectedId = 0;\r\n            })\r\n            .addCase(fetchExamQuestionsWithoutPagination.fulfilled, (state, action) => {\r\n                if (action.payload) {\r\n                    state.questionsExam = action.payload.data;\r\n                    state.selectedId = action.payload.data[0]?.id || 0;\r\n                }\r\n\r\n                state.loading = false;\r\n            })\r\n            .addCase(fetchExamQuestionsWithoutPagination.rejected, (state) => {\r\n                state.questionsExam = [];\r\n                state.selectedId = 0;\r\n                state.loading = false;\r\n            })\r\n    },\r\n});\r\n\r\nexport const {\r\n    setQuestionsExam,\r\n    setLoading,\r\n    setViewRightContent,\r\n    setSelectedId,\r\n    setQuestions,\r\n    reorderQuestions,\r\n    reorderStatements,\r\n    setNewQuestion,\r\n} = questionsExamSlice.actions;\r\nexport default questionsExamSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAO,KAAKC,WAAW,MAAM,4BAA4B;AAEzD,OAAO,MAAMC,mCAAmC,GAAGJ,gBAAgB,CAC/D,sCAAsC,EACtC,OAAAK,IAAA,EAAAC,KAAA,KAAgC;EAAA,IAAzB;IAAEC;EAAG,CAAC,GAAAF,IAAA;EAAA,IAAE;IAAEG;EAAS,CAAC,GAAAF,KAAA;EACvB,OAAO,MAAMJ,UAAU,CAACM,QAAQ,EAAEL,WAAW,CAACM,mBAAmB,EAAE;IAAEF,EAAE;IAAEG,QAAQ,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAO,CAAC,EAAGC,IAAI,IAAK,CACxH,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACpB,CACJ,CAAC;AAGD,MAAMC,kBAAkB,GAAGd,WAAW,CAAC;EACnCe,IAAI,EAAE,eAAe;EACrBC,YAAY,EAAE;IACVC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,UAAU;IAChBC,kBAAkB,EAAE,KAAK;IACzBC,MAAM,EAAE,eAAe;IACvBC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,IAAI;MACpBC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE;IACjB;EACJ,CAAC;EACDC,QAAQ,EAAE;IACNC,gBAAgB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MACjCD,KAAK,CAAClB,aAAa,GAAGmB,MAAM,CAACC,OAAO;IACxC,CAAC;IACDC,cAAc,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MAC/BD,KAAK,CAACZ,WAAW,GAAGa,MAAM,CAACC,OAAO;IACtC,CAAC;IACDE,UAAU,EAAEA,CAACJ,KAAK,EAAEC,MAAM,KAAK;MAC3BD,KAAK,CAACjB,OAAO,GAAGkB,MAAM,CAACC,OAAO;IAClC,CAAC;IACDG,mBAAmB,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MACpCD,KAAK,CAAChB,IAAI,GAAGiB,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDI,aAAa,EAAEA,CAACN,KAAK,EAAEC,MAAM,KAAK;MAC9BD,KAAK,CAACb,UAAU,GAAGc,MAAM,CAACC,OAAO;IACrC,CAAC;IACDK,YAAY,EAAEA,CAACP,KAAK,EAAEC,MAAM,KAAK;MAC7B,MAAMO,QAAQ,GAAGP,MAAM,CAACC,OAAO;MAC/B,MAAMO,KAAK,GAAGT,KAAK,CAAClB,aAAa,CAAC4B,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKmC,QAAQ,CAACnC,EAAE,CAAC;MACtE,IAAIoC,KAAK,KAAK,CAAC,CAAC,EAAE;QACdT,KAAK,CAAClB,aAAa,CAAC2B,KAAK,CAAC,GAAGD,QAAQ;MACzC,CAAC,MAAM;QACHR,KAAK,CAAClB,aAAa,CAAC8B,IAAI,CAACJ,QAAQ,CAAC;MACtC;IACJ,CAAC;IACDK,gBAAgB,EAAEA,CAACb,KAAK,EAAEC,MAAM,KAAK;MACjC,MAAM;QAAEa,QAAQ;QAAEC;MAAS,CAAC,GAAGd,MAAM,CAACC,OAAO;MAE7C,IAAIY,QAAQ,KAAKC,QAAQ,IAAID,QAAQ,IAAI,CAAC,IAAIC,QAAQ,IAAI,CAAC,IACvDD,QAAQ,GAAGd,KAAK,CAAClB,aAAa,CAACkC,MAAM,IAAID,QAAQ,GAAGf,KAAK,CAAClB,aAAa,CAACkC,MAAM,EAAE;QAEhF;QACA,MAAMC,YAAY,GAAG,CAAC,GAAGjB,KAAK,CAAClB,aAAa,CAAC;QAC7C,MAAM,CAACoC,aAAa,CAAC,GAAGD,YAAY,CAACE,MAAM,CAACL,QAAQ,EAAE,CAAC,CAAC;QACxDG,YAAY,CAACE,MAAM,CAACJ,QAAQ,EAAE,CAAC,EAAEG,aAAa,CAAC;;QAE/C;QACA,MAAME,gBAAgB,GAAGH,YAAY,CAACI,GAAG,CAAC,CAACb,QAAQ,EAAEC,KAAK,MAAM;UAC5D,GAAGD,QAAQ;UACXc,aAAa,EAAE;YACX,GAAGd,QAAQ,CAACc,aAAa;YACzBC,KAAK,EAAEd;UACX;QACJ,CAAC,CAAC,CAAC;QAEHT,KAAK,CAAClB,aAAa,GAAGsC,gBAAgB;MAC1C;IACJ,CAAC;IACDI,iBAAiB,EAAEA,CAACxB,KAAK,EAAEC,MAAM,KAAK;MAClC,MAAM;QAAEwB,UAAU;QAAEX,QAAQ;QAAEC;MAAS,CAAC,GAAGd,MAAM,CAACC,OAAO;MAEzD,MAAMwB,aAAa,GAAG1B,KAAK,CAAClB,aAAa,CAAC4B,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKoD,UAAU,CAAC;MAC7E,IAAIC,aAAa,KAAK,CAAC,CAAC,EAAE;MAE1B,MAAMlB,QAAQ,GAAGR,KAAK,CAAClB,aAAa,CAAC4C,aAAa,CAAC;MACnD,IAAI,CAAClB,QAAQ,CAACmB,UAAU,IAAIb,QAAQ,KAAKC,QAAQ,EAAE;MAEnD,IAAID,QAAQ,IAAI,CAAC,IAAIC,QAAQ,IAAI,CAAC,IAC9BD,QAAQ,GAAGN,QAAQ,CAACmB,UAAU,CAACX,MAAM,IAAID,QAAQ,GAAGP,QAAQ,CAACmB,UAAU,CAACX,MAAM,EAAE;QAEhF;QACA,MAAMY,aAAa,GAAG,CAAC,GAAGpB,QAAQ,CAACmB,UAAU,CAAC;QAC9C,MAAM,CAACE,cAAc,CAAC,GAAGD,aAAa,CAACT,MAAM,CAACL,QAAQ,EAAE,CAAC,CAAC;QAC1Dc,aAAa,CAACT,MAAM,CAACJ,QAAQ,EAAE,CAAC,EAAEc,cAAc,CAAC;QACjD;QACA;QACA,MAAMC,iBAAiB,GAAGF,aAAa,CAACP,GAAG,CAAC,CAACU,SAAS,EAAEtB,KAAK,MAAM;UAC/D,GAAGsB,SAAS;UACZR,KAAK,EAAEd;QACX,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMuB,eAAe,GAAG;UACpB,GAAGxB,QAAQ;UACXmB,UAAU,EAAEG;QAChB,CAAC;QAED9B,KAAK,CAAClB,aAAa,CAAC4C,aAAa,CAAC,GAAGM,eAAe;MACxD;IACJ;EACJ,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CACFC,OAAO,CAACjE,mCAAmC,CAACkE,OAAO,EAAGpC,KAAK,IAAK;MAC7DA,KAAK,CAACjB,OAAO,GAAG,IAAI;MACpBiB,KAAK,CAAClB,aAAa,GAAG,EAAE;MACxBkB,KAAK,CAACb,UAAU,GAAG,CAAC;IACxB,CAAC,CAAC,CACDgD,OAAO,CAACjE,mCAAmC,CAACmE,SAAS,EAAE,CAACrC,KAAK,EAAEC,MAAM,KAAK;MACvE,IAAIA,MAAM,CAACC,OAAO,EAAE;QAAA,IAAAoC,qBAAA;QAChBtC,KAAK,CAAClB,aAAa,GAAGmB,MAAM,CAACC,OAAO,CAACxB,IAAI;QACzCsB,KAAK,CAACb,UAAU,GAAG,EAAAmD,qBAAA,GAAArC,MAAM,CAACC,OAAO,CAACxB,IAAI,CAAC,CAAC,CAAC,cAAA4D,qBAAA,uBAAtBA,qBAAA,CAAwBjE,EAAE,KAAI,CAAC;MACtD;MAEA2B,KAAK,CAACjB,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC,CACDoD,OAAO,CAACjE,mCAAmC,CAACqE,QAAQ,EAAGvC,KAAK,IAAK;MAC9DA,KAAK,CAAClB,aAAa,GAAG,EAAE;MACxBkB,KAAK,CAACb,UAAU,GAAG,CAAC;MACpBa,KAAK,CAACjB,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC;EACV;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EACTgB,gBAAgB;EAChBK,UAAU;EACVC,mBAAmB;EACnBC,aAAa;EACbC,YAAY;EACZM,gBAAgB;EAChBW,iBAAiB;EACjBrB;AACJ,CAAC,GAAGxB,kBAAkB,CAAC6D,OAAO;AAC9B,eAAe7D,kBAAkB,CAAC8D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}